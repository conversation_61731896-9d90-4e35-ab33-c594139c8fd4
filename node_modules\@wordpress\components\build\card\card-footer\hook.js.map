{"version": 3, "names": ["_element", "require", "_context", "styles", "_interopRequireWildcard", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useCardFooter", "props", "className", "justify", "isBorderless", "isShady", "size", "otherProps", "useContextSystem", "cx", "useCx", "classes", "useMemo", "Footer", "borderRadius", "borderColor", "cardPaddings", "borderless", "shady"], "sources": ["@wordpress/components/src/card/card-footer/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport * as styles from '../styles';\nimport { useCx } from '../../utils/hooks/use-cx';\nimport type { FooterProps } from '../types';\n\nexport function useCardFooter(\n\tprops: WordPressComponentProps< FooterProps, 'div' >\n) {\n\tconst {\n\t\tclassName,\n\t\tjustify,\n\t\tisBorderless = false,\n\t\tisShady = false,\n\t\tsize = 'medium',\n\t\t...otherProps\n\t} = useContextSystem( props, 'CardFooter' );\n\n\tconst cx = useCx();\n\n\tconst classes = useMemo(\n\t\t() =>\n\t\t\tcx(\n\t\t\t\tstyles.Footer,\n\t\t\t\tstyles.borderRadius,\n\t\t\t\tstyles.borderColor,\n\t\t\t\tstyles.cardPaddings[ size ],\n\t\t\t\tisBorderless && styles.borderless,\n\t\t\t\tisShady && styles.shady,\n\t\t\t\t// This classname is added for legacy compatibility reasons.\n\t\t\t\t'components-card__footer',\n\t\t\t\tclassName\n\t\t\t),\n\t\t[ className, cx, isBorderless, isShady, size ]\n\t);\n\n\treturn {\n\t\t...otherProps,\n\t\tclassName: classes,\n\t\tjustify,\n\t};\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAiD,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAXjD;AACA;AACA;;AAYO,SAASW,aAAaA,CAC5BC,KAAoD,EACnD;EACD,MAAM;IACLC,SAAS;IACTC,OAAO;IACPC,YAAY,GAAG,KAAK;IACpBC,OAAO,GAAG,KAAK;IACfC,IAAI,GAAG,QAAQ;IACf,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEP,KAAK,EAAE,YAAa,CAAC;EAE3C,MAAMQ,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EACtB,MACCH,EAAE,CACDlC,MAAM,CAACsC,MAAM,EACbtC,MAAM,CAACuC,YAAY,EACnBvC,MAAM,CAACwC,WAAW,EAClBxC,MAAM,CAACyC,YAAY,CAAEV,IAAI,CAAE,EAC3BF,YAAY,IAAI7B,MAAM,CAAC0C,UAAU,EACjCZ,OAAO,IAAI9B,MAAM,CAAC2C,KAAK;EACvB;EACA,yBAAyB,EACzBhB,SACD,CAAC,EACF,CAAEA,SAAS,EAAEO,EAAE,EAAEL,YAAY,EAAEC,OAAO,EAAEC,IAAI,CAC7C,CAAC;EAED,OAAO;IACN,GAAGC,UAAU;IACbL,SAAS,EAAES,OAAO;IAClBR;EACD,CAAC;AACF"}