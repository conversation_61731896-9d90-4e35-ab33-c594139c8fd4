<!-- Learn how to maintain this file at https://github.com/WordPress/gutenberg/tree/HEAD/packages#maintaining-changelogs. -->

## Unreleased

## 4.49.0 (2024-05-16)

## 4.48.0 (2024-05-02)

## 4.47.0 (2024-04-19)

## 4.46.0 (2024-04-03)

## 4.45.0 (2024-03-21)

## 4.44.0 (2024-03-06)

## 4.43.0 (2024-02-21)

## 4.42.0 (2024-02-09)

## 4.41.0 (2024-01-24)

## 4.40.0 (2024-01-10)

## 4.39.0 (2023-12-13)

## 4.38.0 (2023-11-29)

## 4.37.0 (2023-11-16)

## 4.36.0 (2023-11-02)

## 4.35.0 (2023-10-18)

## 4.34.0 (2023-10-05)

## 4.33.0 (2023-09-20)

## 4.32.0 (2023-08-31)

## 4.31.0 (2023-08-16)

## 4.30.0 (2023-08-10)

## 4.29.0 (2023-07-20)

## 4.28.0 (2023-07-05)

## 4.27.0 (2023-06-23)

## 4.26.0 (2023-06-07)

## 4.25.0 (2023-05-24)

## 4.24.0 (2023-05-10)

## 4.23.0 (2023-04-26)

## 4.22.0 (2023-04-12)

## 4.21.0 (2023-03-29)

## 4.20.0 (2023-03-15)

## 4.19.0 (2023-03-01)

## 4.18.0 (2023-02-15)

## 4.17.0 (2023-02-01)

## 4.16.0 (2023-01-11)

## 4.15.0 (2023-01-02)

## 4.14.0 (2022-12-14)

### Enhancements

-   Lighten the border color in the `input-style__neutral` mixin ([#46252](https://github.com/WordPress/gutenberg/pull/46252)).

## 4.13.0 (2022-11-16)

## 4.12.0 (2022-11-02)

## 4.11.0 (2022-10-19)

## 4.10.0 (2022-10-05)

## 4.9.0 (2022-09-21)

## 4.0.0 (2021-09-09)

### Breaking Change

-   Remove the background-colors, foreground-colors, and gradient-colors mixins.

## 2.0.0 (2020-07-07)

### Breaking Changes

-   Remove the AdminColorThemes JavaScript variables.
-   Retire numerous colors and old grays, indicate others for deprecation.

## 1.2.0 (2020-01-13)

### Bug Fix

-   Import `colors` into `variables` since the latter depends on the former.
