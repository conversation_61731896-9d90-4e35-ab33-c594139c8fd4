{"version": 3, "names": ["_reactNative", "require", "_element", "_style", "_interopRequireDefault", "TooltipContext", "createContext", "<PERSON><PERSON><PERSON>", "children", "onPress", "style", "visible", "panResponder", "useRef", "PanResponder", "create", "onStartShouldSetPanResponderCapture", "current", "_react", "createElement", "Provider", "value", "View", "panHandlers", "Label", "align", "text", "xOffset", "yOffset", "animationValue", "Animated", "Value", "dimensions", "setDimensions", "useState", "useContext", "Error", "useEffect", "startAnimation", "timing", "toValue", "duration", "useNativeDriver", "delay", "easing", "Easing", "out", "quad", "start", "tooltipTransforms", "translateX", "width", "translateY", "height", "tooltipStyles", "styles", "tooltip", "shadowColor", "tooltipShadow", "color", "shadowOffset", "shadowOpacity", "shadowRadius", "elevation", "transform", "tooltipLeftAlign", "arrowStyles", "arrow", "arrowLeftAlign", "opacity", "interpolate", "inputRange", "outputRange", "onLayout", "nativeEvent", "layout", "Text", "defaultProps", "_default", "exports", "default"], "sources": ["@wordpress/components/src/focal-point-picker/tooltip/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Animated, Easing, PanResponder, Text, View } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tcreateContext,\n\tuseEffect,\n\tuseRef,\n\tuseState,\n\tuseContext,\n} from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nconst TooltipContext = createContext();\n\nfunction Tooltip( { children, onPress, style, visible } ) {\n\tconst panResponder = useRef(\n\t\tPanResponder.create( {\n\t\t\t/**\n\t\t\t * To allow dimissing the tooltip on press while also avoiding blocking\n\t\t\t * interactivity within the child context, we place this `onPress` side\n\t\t\t * effect within the `onStartShouldSetPanResponderCapture` callback.\n\t\t\t *\n\t\t\t * This is a bit unorthodox, but may be the simplest approach to achieving\n\t\t\t * this outcome. This is effectively a gesture responder that never\n\t\t\t * becomes the controlling responder. https://bit.ly/2J3ugKF\n\t\t\t */\n\t\t\tonStartShouldSetPanResponderCapture: () => {\n\t\t\t\tif ( onPress ) {\n\t\t\t\t\tonPress();\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t} )\n\t).current;\n\n\treturn (\n\t\t<TooltipContext.Provider value={ visible }>\n\t\t\t<View\n\t\t\t\t{ ...( visible ? panResponder.panHandlers : {} ) }\n\t\t\t\tstyle={ style }\n\t\t\t>\n\t\t\t\t{ children }\n\t\t\t</View>\n\t\t</TooltipContext.Provider>\n\t);\n}\n\nfunction Label( { align, text, xOffset, yOffset } ) {\n\tconst animationValue = useRef( new Animated.Value( 0 ) ).current;\n\tconst [ dimensions, setDimensions ] = useState( null );\n\tconst visible = useContext( TooltipContext );\n\n\tif ( typeof visible === 'undefined' ) {\n\t\tthrow new Error(\n\t\t\t'Tooltip.Label cannot be rendered outside of the Tooltip component'\n\t\t);\n\t}\n\n\tuseEffect( () => {\n\t\tconst startAnimation = () => {\n\t\t\tAnimated.timing( animationValue, {\n\t\t\t\ttoValue: visible ? 1 : 0,\n\t\t\t\tduration: visible ? 300 : 150,\n\t\t\t\tuseNativeDriver: true,\n\t\t\t\tdelay: visible ? 500 : 0,\n\t\t\t\teasing: Easing.out( Easing.quad ),\n\t\t\t} ).start();\n\t\t};\n\t\tstartAnimation();\n\t}, [ animationValue, visible ] );\n\n\t// Transforms rely upon onLayout to enable custom offsets additions.\n\tlet tooltipTransforms;\n\tif ( dimensions ) {\n\t\ttooltipTransforms = [\n\t\t\t{\n\t\t\t\ttranslateX:\n\t\t\t\t\t( align === 'center' ? -dimensions.width / 2 : 0 ) +\n\t\t\t\t\txOffset,\n\t\t\t},\n\t\t\t{ translateY: -dimensions.height + yOffset },\n\t\t];\n\t}\n\n\tconst tooltipStyles = [\n\t\tstyles.tooltip,\n\t\t{\n\t\t\tshadowColor: styles.tooltipShadow?.color,\n\t\t\tshadowOffset: {\n\t\t\t\twidth: 0,\n\t\t\t\theight: 2,\n\t\t\t},\n\t\t\tshadowOpacity: 0.25,\n\t\t\tshadowRadius: 2,\n\t\t\televation: 2,\n\t\t\ttransform: tooltipTransforms,\n\t\t},\n\t\talign === 'left' && styles.tooltipLeftAlign,\n\t];\n\tconst arrowStyles = [\n\t\tstyles.arrow,\n\t\talign === 'left' && styles.arrowLeftAlign,\n\t];\n\n\treturn (\n\t\t<Animated.View\n\t\t\tstyle={ {\n\t\t\t\topacity: animationValue,\n\t\t\t\ttransform: [\n\t\t\t\t\t{\n\t\t\t\t\t\ttranslateY: animationValue.interpolate( {\n\t\t\t\t\t\t\tinputRange: [ 0, 1 ],\n\t\t\t\t\t\t\toutputRange: [ visible ? 4 : -8, -8 ],\n\t\t\t\t\t\t} ),\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t} }\n\t\t>\n\t\t\t<View\n\t\t\t\tonLayout={ ( { nativeEvent } ) => {\n\t\t\t\t\tconst { height, width } = nativeEvent.layout;\n\t\t\t\t\tsetDimensions( { height, width } );\n\t\t\t\t} }\n\t\t\t\tstyle={ tooltipStyles }\n\t\t\t>\n\t\t\t\t<Text style={ styles.text }>{ text }</Text>\n\t\t\t\t<View style={ arrowStyles } />\n\t\t\t</View>\n\t\t</Animated.View>\n\t);\n}\n\nLabel.defaultProps = {\n\talign: 'center',\n\txOffset: 0,\n\tyOffset: 0,\n};\n\nTooltip.Label = Label;\n\nexport default Tooltip;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAWA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAnBA;AACA;AACA;;AAGA;AACA;AACA;;AASA;AACA;AACA;;AAGA,MAAMI,cAAc,GAAG,IAAAC,sBAAa,EAAC,CAAC;AAEtC,SAASC,OAAOA,CAAE;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,KAAK;EAAEC;AAAQ,CAAC,EAAG;EACzD,MAAMC,YAAY,GAAG,IAAAC,eAAM,EAC1BC,yBAAY,CAACC,MAAM,CAAE;IACpB;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACGC,mCAAmC,EAAEA,CAAA,KAAM;MAC1C,IAAKP,OAAO,EAAG;QACdA,OAAO,CAAC,CAAC;MACV;MACA,OAAO,KAAK;IACb;EACD,CAAE,CACH,CAAC,CAACQ,OAAO;EAET,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACd,cAAc,CAACe,QAAQ;IAACC,KAAK,EAAGV;EAAS,GACzC,IAAAO,MAAA,CAAAC,aAAA,EAACnB,YAAA,CAAAsB,IAAI;IAAA,IACGX,OAAO,GAAGC,YAAY,CAACW,WAAW,GAAG,CAAC,CAAC;IAC9Cb,KAAK,EAAGA;EAAO,GAEbF,QACG,CACkB,CAAC;AAE5B;AAEA,SAASgB,KAAKA,CAAE;EAAEC,KAAK;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAQ,CAAC,EAAG;EACnD,MAAMC,cAAc,GAAG,IAAAhB,eAAM,EAAE,IAAIiB,qBAAQ,CAACC,KAAK,CAAE,CAAE,CAAE,CAAC,CAACd,OAAO;EAChE,MAAM,CAAEe,UAAU,EAAEC,aAAa,CAAE,GAAG,IAAAC,iBAAQ,EAAE,IAAK,CAAC;EACtD,MAAMvB,OAAO,GAAG,IAAAwB,mBAAU,EAAE9B,cAAe,CAAC;EAE5C,IAAK,OAAOM,OAAO,KAAK,WAAW,EAAG;IACrC,MAAM,IAAIyB,KAAK,CACd,mEACD,CAAC;EACF;EAEA,IAAAC,kBAAS,EAAE,MAAM;IAChB,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC5BR,qBAAQ,CAACS,MAAM,CAAEV,cAAc,EAAE;QAChCW,OAAO,EAAE7B,OAAO,GAAG,CAAC,GAAG,CAAC;QACxB8B,QAAQ,EAAE9B,OAAO,GAAG,GAAG,GAAG,GAAG;QAC7B+B,eAAe,EAAE,IAAI;QACrBC,KAAK,EAAEhC,OAAO,GAAG,GAAG,GAAG,CAAC;QACxBiC,MAAM,EAAEC,mBAAM,CAACC,GAAG,CAAED,mBAAM,CAACE,IAAK;MACjC,CAAE,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC;IACDV,cAAc,CAAC,CAAC;EACjB,CAAC,EAAE,CAAET,cAAc,EAAElB,OAAO,CAAG,CAAC;;EAEhC;EACA,IAAIsC,iBAAiB;EACrB,IAAKjB,UAAU,EAAG;IACjBiB,iBAAiB,GAAG,CACnB;MACCC,UAAU,EACT,CAAEzB,KAAK,KAAK,QAAQ,GAAG,CAACO,UAAU,CAACmB,KAAK,GAAG,CAAC,GAAG,CAAC,IAChDxB;IACF,CAAC,EACD;MAAEyB,UAAU,EAAE,CAACpB,UAAU,CAACqB,MAAM,GAAGzB;IAAQ,CAAC,CAC5C;EACF;EAEA,MAAM0B,aAAa,GAAG,CACrBC,cAAM,CAACC,OAAO,EACd;IACCC,WAAW,EAAEF,cAAM,CAACG,aAAa,EAAEC,KAAK;IACxCC,YAAY,EAAE;MACbT,KAAK,EAAE,CAAC;MACRE,MAAM,EAAE;IACT,CAAC;IACDQ,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAEf;EACZ,CAAC,EACDxB,KAAK,KAAK,MAAM,IAAI8B,cAAM,CAACU,gBAAgB,CAC3C;EACD,MAAMC,WAAW,GAAG,CACnBX,cAAM,CAACY,KAAK,EACZ1C,KAAK,KAAK,MAAM,IAAI8B,cAAM,CAACa,cAAc,CACzC;EAED,OACC,IAAAlD,MAAA,CAAAC,aAAA,EAACnB,YAAA,CAAA8B,QAAQ,CAACR,IAAI;IACbZ,KAAK,EAAG;MACP2D,OAAO,EAAExC,cAAc;MACvBmC,SAAS,EAAE,CACV;QACCZ,UAAU,EAAEvB,cAAc,CAACyC,WAAW,CAAE;UACvCC,UAAU,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;UACpBC,WAAW,EAAE,CAAE7D,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC,CAAE;MACH,CAAC;IAEH;EAAG,GAEH,IAAAO,MAAA,CAAAC,aAAA,EAACnB,YAAA,CAAAsB,IAAI;IACJmD,QAAQ,EAAGA,CAAE;MAAEC;IAAY,CAAC,KAAM;MACjC,MAAM;QAAErB,MAAM;QAAEF;MAAM,CAAC,GAAGuB,WAAW,CAACC,MAAM;MAC5C1C,aAAa,CAAE;QAAEoB,MAAM;QAAEF;MAAM,CAAE,CAAC;IACnC,CAAG;IACHzC,KAAK,EAAG4C;EAAe,GAEvB,IAAApC,MAAA,CAAAC,aAAA,EAACnB,YAAA,CAAA4E,IAAI;IAAClE,KAAK,EAAG6C,cAAM,CAAC7B;EAAM,GAAGA,IAAY,CAAC,EAC3C,IAAAR,MAAA,CAAAC,aAAA,EAACnB,YAAA,CAAAsB,IAAI;IAACZ,KAAK,EAAGwD;EAAa,CAAE,CACxB,CACQ,CAAC;AAElB;AAEA1C,KAAK,CAACqD,YAAY,GAAG;EACpBpD,KAAK,EAAE,QAAQ;EACfE,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACV,CAAC;AAEDrB,OAAO,CAACiB,KAAK,GAAGA,KAAK;AAAC,IAAAsD,QAAA,GAEPvE,OAAO;AAAAwE,OAAA,CAAAC,OAAA,GAAAF,QAAA"}