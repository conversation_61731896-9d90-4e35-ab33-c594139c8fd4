{"version": 3, "names": ["_deprecated", "_interopRequireDefault", "require", "_element", "_", "UnforwardedIconButton", "label", "labelPosition", "size", "tooltip", "props", "ref", "deprecated", "since", "alternative", "version", "_react", "createElement", "default", "tooltipPosition", "iconSize", "showTooltip", "undefined", "_default", "forwardRef", "exports"], "sources": ["@wordpress/components/src/button/deprecated.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport deprecated from '@wordpress/deprecated';\nimport { forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport Button from '.';\nimport type { DeprecatedIconButtonProps } from './types';\n\nfunction UnforwardedIconButton(\n\t{\n\t\tlabel,\n\t\tlabelPosition,\n\t\tsize,\n\t\ttooltip,\n\t\t...props\n\t}: React.ComponentPropsWithoutRef< typeof Button > &\n\t\tDeprecatedIconButtonProps,\n\tref: ForwardedRef< any >\n) {\n\tdeprecated( 'wp.components.IconButton', {\n\t\tsince: '5.4',\n\t\talternative: 'wp.components.Button',\n\t\tversion: '6.2',\n\t} );\n\n\treturn (\n\t\t<Button\n\t\t\t{ ...props }\n\t\t\tref={ ref }\n\t\t\ttooltipPosition={ labelPosition }\n\t\t\ticonSize={ size }\n\t\t\tshowTooltip={ tooltip !== undefined ? !! tooltip : undefined }\n\t\t\tlabel={ tooltip || label }\n\t\t/>\n\t);\n}\n\nexport default forwardRef( UnforwardedIconButton );\n"], "mappings": ";;;;;;;;AAQA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,CAAA,GAAAH,sBAAA,CAAAC,OAAA;AAdA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAIA,SAASG,qBAAqBA,CAC7B;EACCC,KAAK;EACLC,aAAa;EACbC,IAAI;EACJC,OAAO;EACP,GAAGC;AAEqB,CAAC,EAC1BC,GAAwB,EACvB;EACD,IAAAC,mBAAU,EAAE,0BAA0B,EAAE;IACvCC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,sBAAsB;IACnCC,OAAO,EAAE;EACV,CAAE,CAAC;EAEH,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACb,CAAA,CAAAc,OAAM;IAAA,GACDR,KAAK;IACVC,GAAG,EAAGA,GAAK;IACXQ,eAAe,EAAGZ,aAAe;IACjCa,QAAQ,EAAGZ,IAAM;IACjBa,WAAW,EAAGZ,OAAO,KAAKa,SAAS,GAAG,CAAC,CAAEb,OAAO,GAAGa,SAAW;IAC9DhB,KAAK,EAAGG,OAAO,IAAIH;EAAO,CAC1B,CAAC;AAEJ;AAAC,IAAAiB,QAAA,GAEc,IAAAC,mBAAU,EAAEnB,qBAAsB,CAAC;AAAAoB,OAAA,CAAAP,OAAA,GAAAK,QAAA"}