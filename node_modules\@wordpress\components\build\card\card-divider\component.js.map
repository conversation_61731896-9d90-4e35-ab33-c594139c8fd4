{"version": 3, "names": ["_context", "require", "_divider", "_hook", "UnconnectedCardDivider", "props", "forwardedRef", "dividerProps", "useCardDivider", "_react", "createElement", "Divider", "ref", "CardDivider", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/card/card-divider/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport type { DividerProps } from '../../divider';\nimport { Divider } from '../../divider';\nimport { useCardDivider } from './hook';\n\nfunction UnconnectedCardDivider(\n\tprops: WordPressComponentProps< DividerProps, 'hr', false >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst dividerProps = useCardDivider( props );\n\n\treturn <Divider { ...dividerProps } ref={ forwardedRef } />;\n}\n\n/**\n * `CardDivider` renders an optional divider within a `Card`.\n * It is typically used to divide multiple `CardBody` components from each other.\n *\n * ```jsx\n * import { Card, CardBody, CardDivider } from `@wordpress/components`;\n *\n * <Card>\n *  <CardBody>...</CardBody>\n *  <CardDivider />\n *  <CardBody>...</CardBody>\n * </Card>\n * ```\n */\nexport const CardDivider = contextConnect(\n\tUnconnectedCardDivider,\n\t'CardDivider'\n);\n\nexport default CardDivider;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,sBAAsBA,CAC9BC,KAA2D,EAC3DC,YAAiC,EAChC;EACD,MAAMC,YAAY,GAAG,IAAAC,oBAAc,EAAEH,KAAM,CAAC;EAE5C,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,QAAA,CAAAS,OAAO;IAAA,GAAMJ,YAAY;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,WAAW,GAAG,IAAAC,uBAAc,EACxCV,sBAAsB,EACtB,aACD,CAAC;AAACW,OAAA,CAAAF,WAAA,GAAAA,WAAA;AAAA,IAAAG,QAAA,GAEaH,WAAW;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}