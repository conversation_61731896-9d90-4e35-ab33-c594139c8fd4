!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@babel/helper-module-imports"),require("@babel/types"),require("aslemammad-vite-plugin-macro"),require("babel-plugin-macros")):"function"==typeof define&&define.amd?define(["exports","@babel/helper-module-imports","@babel/types","aslemammad-vite-plugin-macro","babel-plugin-macros"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtioVite={},e.babelModuleImports,e.t,e.plugin,e.babelMacro)}(this,(function(e,t,r,a,n){"use strict";function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=o(t),l=o(r),u=o(a),c=o(n),d="default"in u?u.default:u,f=d.defineMacro,s=d.defineMacroProvider,p=d.createMacroPlugin,b=f("useProxy").withSignature("<T extends object>(proxyObject: T): void").withHandler((function(e){var t,r,a,n,o=e.path,u=e.args,d=i.addNamed(o,"useSnapshot","valtio"),f=null==(t=u[0])?void 0:t.node;if(!l.isIdentifier(f))throw new c.MacroError("no proxy object");var s=l.identifier("valtio_macro_snap_"+f.name);null==(r=o.parentPath)||r.replaceWith(l.variableDeclaration("const",[l.variableDeclarator(s,l.callExpression(d,[f]))]));var p=0;null==(a=o.parentPath)||null==(n=a.getFunctionParent())||n.traverse({Identifier:function(e){0===p&&e.node!==f&&e.node.name===f.name&&(e.node.name=s.name)},Function:{enter:function(){++p},exit:function(){--p}}})}));function m(){return s({id:"valtio/macro",exports:{"valtio/macro":{macros:[b]}}})}var v=p({}).use(m());e.default=v,e.provideValtioMacro=m,e.valtioMacro=b,Object.defineProperty(e,"__esModule",{value:!0})}));
