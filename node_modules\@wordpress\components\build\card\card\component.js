"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Card = void 0;
var _react = require("react");
var _react2 = require("@emotion/react");
var _element = require("@wordpress/element");
var _context = require("../../context");
var _elevation = require("../../elevation");
var _view = require("../../view");
var styles = _interopRequireWildcard(require("../styles"));
var _hook = require("./hook");
var _configValues = _interopRequireDefault(require("../../utils/config-values"));
var _useCx = require("../../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function UnconnectedCard(props, forwardedRef) {
  const {
    children,
    elevation,
    isBorderless,
    isRounded,
    size,
    ...otherProps
  } = (0, _hook.useCard)(props);
  const elevationBorderRadius = isRounded ? _configValues.default.cardBorderRadius : 0;
  const cx = (0, _useCx.useCx)();
  const elevationClassName = (0, _element.useMemo)(() => cx( /*#__PURE__*/(0, _react2.css)({
    borderRadius: elevationBorderRadius
  }, process.env.NODE_ENV === "production" ? "" : ";label:elevationClassName;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */")), [cx, elevationBorderRadius]);
  const contextProviderValue = (0, _element.useMemo)(() => {
    const contextProps = {
      size,
      isBorderless
    };
    return {
      CardBody: contextProps,
      CardHeader: contextProps,
      CardFooter: contextProps
    };
  }, [isBorderless, size]);
  return (0, _react.createElement)(_context.ContextSystemProvider, {
    value: contextProviderValue
  }, (0, _react.createElement)(_view.View, {
    ...otherProps,
    ref: forwardedRef
  }, (0, _react.createElement)(_view.View, {
    className: cx(styles.Content)
  }, children), (0, _react.createElement)(_elevation.Elevation, {
    className: elevationClassName,
    isInteractive: false,
    value: elevation ? 1 : 0
  }), (0, _react.createElement)(_elevation.Elevation, {
    className: elevationClassName,
    isInteractive: false,
    value: elevation
  })));
}

/**
 * `Card` provides a flexible and extensible content container.
 * `Card` also provides a convenient set of sub-components such as `CardBody`,
 * `CardHeader`, `CardFooter`, and more.
 *
 * ```jsx
 * import {
 *   Card,
 *   CardHeader,
 *   CardBody,
 *   CardFooter,
 *   __experimentalText as Text,
 *   __experimentalHeading as Heading,
 * } from `@wordpress/components`;
 *
 * function Example() {
 *   return (
 *     <Card>
 *       <CardHeader>
 *         <Heading level={ 4 }>Card Title</Heading>
 *       </CardHeader>
 *       <CardBody>
 *         <Text>Card Content</Text>
 *       </CardBody>
 *       <CardFooter>
 *         <Text>Card Footer</Text>
 *       </CardFooter>
 *     </Card>
 *   );
 * }
 * ```
 */
const Card = (0, _context.contextConnect)(UnconnectedCard, 'Card');
exports.Card = Card;
var _default = Card;
exports.default = _default;
//# sourceMappingURL=component.js.map