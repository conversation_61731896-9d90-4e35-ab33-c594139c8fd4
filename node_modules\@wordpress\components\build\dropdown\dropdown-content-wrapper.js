"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.DropdownContentWrapper = void 0;
var _react = require("react");
var _context = require("../context");
var _styles = require("./styles");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedDropdownContentWrapper(props, forwardedRef) {
  const {
    paddingSize = 'small',
    ...derivedProps
  } = (0, _context.useContextSystem)(props, 'DropdownContentWrapper');
  return (0, _react.createElement)(_styles.DropdownContentWrapperDiv, {
    ...derivedProps,
    paddingSize: paddingSize,
    ref: forwardedRef
  });
}

/**
 * A convenience wrapper for the `renderContent` when you want to apply
 * different padding. (Default is `paddingSize="small"`).
 *
 * ```jsx
 * import {
 *   Dropdown,
 *   __experimentalDropdownContentWrapper as DropdownContentWrapper,
 * } from '@wordpress/components';
 *
 * <Dropdown
 *   renderContent={ () => (
 *     <DropdownContentWrapper paddingSize="medium">
 *       My dropdown content
 *     </DropdownContentWrapper>
 * ) }
 * />
 * ```
 */
const DropdownContentWrapper = (0, _context.contextConnect)(UnconnectedDropdownContentWrapper, 'DropdownContentWrapper');
exports.DropdownContentWrapper = DropdownContentWrapper;
var _default = DropdownContentWrapper;
exports.default = _default;
//# sourceMappingURL=dropdown-content-wrapper.js.map