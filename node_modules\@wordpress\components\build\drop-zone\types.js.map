{"version": 3, "names": [], "sources": ["@wordpress/components/src/drop-zone/types.ts"], "sourcesContent": ["export type DropType = 'file' | 'html' | 'default';\n\nexport type DropZoneProps = {\n\t/**\n\t * A CSS `class` to give to the wrapper element.\n\t */\n\tclassName?: string;\n\t/**\n\t * A string to be shown within the drop zone area.\n\t *\n\t * @default `__( 'Drop files to upload' )`\n\t */\n\tlabel?: string;\n\t/**\n\t * The function is generic drop handler called if the `onFilesDrop` or `onHTMLDrop` are not called.\n\t * It receives the drop `event` object as an argument.\n\t */\n\tonDrop?: ( event: DragEvent ) => void;\n\t/**\n\t * The function is called when dropping a file into the `DropZone`.\n\t * It receives an array of dropped files as an argument.\n\t */\n\tonFilesDrop?: ( files: File[] ) => void;\n\t/**\n\t * The function is called when dropping HTML into the `DropZone`.\n\t * It receives the HTML being dropped as an argument.\n\t */\n\tonHTMLDrop?: ( html: string ) => void;\n};\n"], "mappings": ""}