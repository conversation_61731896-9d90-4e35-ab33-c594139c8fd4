"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function stopPropagation(event) {
  event.stopPropagation();
}
const IsolatedEventContainer = (0, _element.forwardRef)((props, ref) => {
  (0, _deprecated.default)('wp.components.IsolatedEventContainer', {
    since: '5.7'
  });

  // Disable reason: this stops certain events from propagating outside of the component.
  // - onMouseDown is disabled as this can cause interactions with other DOM elements.
  /* eslint-disable jsx-a11y/no-static-element-interactions */
  return (0, _react.createElement)("div", {
    ...props,
    ref: ref,
    onMouseDown: stopPropagation
  });
  /* eslint-enable jsx-a11y/no-static-element-interactions */
});
var _default = IsolatedEventContainer;
exports.default = _default;
//# sourceMappingURL=index.js.map