{"version": 3, "file": "intl-messageformat.min.js", "sources": ["../../../intl-messageformat-parser/src/parser.js", "../../lib/compiler.js", "../../lib/core.js", "../../lib/index.js"], "sourcesContent": ["export default /*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n(function() {\n  \"use strict\";\n\n  function peg$subclass(child, parent) {\n    function ctor() { this.constructor = child; }\n    ctor.prototype = parent.prototype;\n    child.prototype = new ctor();\n  }\n\n  function peg$SyntaxError(message, expected, found, location) {\n    this.message  = message;\n    this.expected = expected;\n    this.found    = found;\n    this.location = location;\n    this.name     = \"SyntaxError\";\n\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, peg$SyntaxError);\n    }\n  }\n\n  peg$subclass(peg$SyntaxError, Error);\n\n  peg$SyntaxError.buildMessage = function(expected, found) {\n    var DESCRIBE_EXPECTATION_FNS = {\n          literal: function(expectation) {\n            return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n          },\n\n          \"class\": function(expectation) {\n            var escapedParts = \"\",\n                i;\n\n            for (i = 0; i < expectation.parts.length; i++) {\n              escapedParts += expectation.parts[i] instanceof Array\n                ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n                : classEscape(expectation.parts[i]);\n            }\n\n            return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n          },\n\n          any: function(expectation) {\n            return \"any character\";\n          },\n\n          end: function(expectation) {\n            return \"end of input\";\n          },\n\n          other: function(expectation) {\n            return expectation.description;\n          }\n        };\n\n    function hex(ch) {\n      return ch.charCodeAt(0).toString(16).toUpperCase();\n    }\n\n    function literalEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\"/g,  '\\\\\"')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function classEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\\]/g, '\\\\]')\n        .replace(/\\^/g, '\\\\^')\n        .replace(/-/g,  '\\\\-')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function describeExpectation(expectation) {\n      return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n    }\n\n    function describeExpected(expected) {\n      var descriptions = new Array(expected.length),\n          i, j;\n\n      for (i = 0; i < expected.length; i++) {\n        descriptions[i] = describeExpectation(expected[i]);\n      }\n\n      descriptions.sort();\n\n      if (descriptions.length > 0) {\n        for (i = 1, j = 1; i < descriptions.length; i++) {\n          if (descriptions[i - 1] !== descriptions[i]) {\n            descriptions[j] = descriptions[i];\n            j++;\n          }\n        }\n        descriptions.length = j;\n      }\n\n      switch (descriptions.length) {\n        case 1:\n          return descriptions[0];\n\n        case 2:\n          return descriptions[0] + \" or \" + descriptions[1];\n\n        default:\n          return descriptions.slice(0, -1).join(\", \")\n            + \", or \"\n            + descriptions[descriptions.length - 1];\n      }\n    }\n\n    function describeFound(found) {\n      return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n    }\n\n    return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n  };\n\n  function peg$parse(input, options) {\n    options = options !== void 0 ? options : {};\n\n    var peg$FAILED = {},\n\n        peg$startRuleFunctions = { start: peg$parsestart },\n        peg$startRuleFunction  = peg$parsestart,\n\n        peg$c0 = function(elements) {\n                return {\n                    type    : 'messageFormatPattern',\n                    elements: elements,\n                    location: location()\n                };\n            },\n        peg$c1 = function(chunks) {\n                return chunks.reduce(function (all, chunk) {\n                    return all.concat(chunk)\n                }, []).join('')\n            },\n        peg$c2 = function(messageText) {\n                return {\n                    type : 'messageTextElement',\n                    value: messageText,\n                    location: location()\n                };\n            },\n        peg$c3 = function(chars) { return chars.join(''); },\n        peg$c4 = \"{\",\n        peg$c5 = peg$literalExpectation(\"{\", false),\n        peg$c6 = \",\",\n        peg$c7 = peg$literalExpectation(\",\", false),\n        peg$c8 = \"}\",\n        peg$c9 = peg$literalExpectation(\"}\", false),\n        peg$c10 = function(id, format) {\n                return {\n                    type  : 'argumentElement',\n                    id    : id,\n                    format: format && format[2],\n                    location: location()\n                };\n            },\n        peg$c11 = \"number\",\n        peg$c12 = peg$literalExpectation(\"number\", false),\n        peg$c13 = \"date\",\n        peg$c14 = peg$literalExpectation(\"date\", false),\n        peg$c15 = \"time\",\n        peg$c16 = peg$literalExpectation(\"time\", false),\n        peg$c17 = function(type, style) {\n                return {\n                    type : type + 'Format',\n                    style: style && style[2],\n                    location: location()\n                };\n            },\n        peg$c18 = \"plural\",\n        peg$c19 = peg$literalExpectation(\"plural\", false),\n        peg$c20 = function(pluralStyle) {\n                return {\n                    type   : pluralStyle.type,\n                    ordinal: false,\n                    offset : pluralStyle.offset || 0,\n                    options: pluralStyle.options,\n                    location: location()\n                };\n            },\n        peg$c21 = \"selectordinal\",\n        peg$c22 = peg$literalExpectation(\"selectordinal\", false),\n        peg$c23 = function(pluralStyle) {\n                return {\n                    type   : pluralStyle.type,\n                    ordinal: true,\n                    offset : pluralStyle.offset || 0,\n                    options: pluralStyle.options,\n                    location: location()\n                }\n            },\n        peg$c24 = \"select\",\n        peg$c25 = peg$literalExpectation(\"select\", false),\n        peg$c26 = function(options) {\n                return {\n                    type   : 'selectFormat',\n                    options: options,\n                    location: location()\n                };\n            },\n        peg$c27 = \"=\",\n        peg$c28 = peg$literalExpectation(\"=\", false),\n        peg$c29 = function(selector, pattern) {\n                return {\n                    type    : 'optionalFormatPattern',\n                    selector: selector,\n                    value   : pattern,\n                    location: location()\n                };\n            },\n        peg$c30 = \"offset:\",\n        peg$c31 = peg$literalExpectation(\"offset:\", false),\n        peg$c32 = function(number) {\n                return number;\n            },\n        peg$c33 = function(offset, options) {\n                return {\n                    type   : 'pluralFormat',\n                    offset : offset,\n                    options: options,\n                    location: location()\n                };\n            },\n        peg$c34 = peg$otherExpectation(\"whitespace\"),\n        peg$c35 = /^[ \\t\\n\\r]/,\n        peg$c36 = peg$classExpectation([\" \", \"\\t\", \"\\n\", \"\\r\"], false, false),\n        peg$c37 = peg$otherExpectation(\"optionalWhitespace\"),\n        peg$c38 = /^[0-9]/,\n        peg$c39 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n        peg$c40 = /^[0-9a-f]/i,\n        peg$c41 = peg$classExpectation([[\"0\", \"9\"], [\"a\", \"f\"]], false, true),\n        peg$c42 = \"0\",\n        peg$c43 = peg$literalExpectation(\"0\", false),\n        peg$c44 = /^[1-9]/,\n        peg$c45 = peg$classExpectation([[\"1\", \"9\"]], false, false),\n        peg$c46 = function(digits) {\n            return parseInt(digits, 10);\n        },\n        peg$c47 = \"'\",\n        peg$c48 = peg$literalExpectation(\"'\", false),\n        peg$c49 = /^[ \\t\\n\\r,.+={}#]/,\n        peg$c50 = peg$classExpectation([\" \", \"\\t\", \"\\n\", \"\\r\", \",\", \".\", \"+\", \"=\", \"{\", \"}\", \"#\"], false, false),\n        peg$c51 = peg$anyExpectation(),\n        peg$c52 = function(char) { return char; },\n        peg$c53 = function(sequence) { return sequence; },\n        peg$c54 = /^[^{}\\\\\\0-\\x1F\\x7F \\t\\n\\r]/,\n        peg$c55 = peg$classExpectation([\"{\", \"}\", \"\\\\\", [\"\\0\", \"\\x1F\"], \"\\x7F\", \" \", \"\\t\", \"\\n\", \"\\r\"], true, false),\n        peg$c56 = \"\\\\\\\\\",\n        peg$c57 = peg$literalExpectation(\"\\\\\\\\\", false),\n        peg$c58 = function() { return '\\\\'; },\n        peg$c59 = \"\\\\#\",\n        peg$c60 = peg$literalExpectation(\"\\\\#\", false),\n        peg$c61 = function() { return '\\\\#'; },\n        peg$c62 = \"\\\\{\",\n        peg$c63 = peg$literalExpectation(\"\\\\{\", false),\n        peg$c64 = function() { return '\\u007B'; },\n        peg$c65 = \"\\\\}\",\n        peg$c66 = peg$literalExpectation(\"\\\\}\", false),\n        peg$c67 = function() { return '\\u007D'; },\n        peg$c68 = \"\\\\u\",\n        peg$c69 = peg$literalExpectation(\"\\\\u\", false),\n        peg$c70 = function(digits) {\n                return String.fromCharCode(parseInt(digits, 16));\n            },\n\n        peg$currPos          = 0,\n        peg$savedPos         = 0,\n        peg$posDetailsCache  = [{ line: 1, column: 1 }],\n        peg$maxFailPos       = 0,\n        peg$maxFailExpected  = [],\n        peg$silentFails      = 0,\n\n        peg$result;\n\n    if (\"startRule\" in options) {\n      if (!(options.startRule in peg$startRuleFunctions)) {\n        throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n      }\n\n      peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n    }\n\n    function text() {\n      return input.substring(peg$savedPos, peg$currPos);\n    }\n\n    function location() {\n      return peg$computeLocation(peg$savedPos, peg$currPos);\n    }\n\n    function expected(description, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildStructuredError(\n        [peg$otherExpectation(description)],\n        input.substring(peg$savedPos, peg$currPos),\n        location\n      );\n    }\n\n    function error(message, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildSimpleError(message, location);\n    }\n\n    function peg$literalExpectation(text, ignoreCase) {\n      return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n    }\n\n    function peg$classExpectation(parts, inverted, ignoreCase) {\n      return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n    }\n\n    function peg$anyExpectation() {\n      return { type: \"any\" };\n    }\n\n    function peg$endExpectation() {\n      return { type: \"end\" };\n    }\n\n    function peg$otherExpectation(description) {\n      return { type: \"other\", description: description };\n    }\n\n    function peg$computePosDetails(pos) {\n      var details = peg$posDetailsCache[pos], p;\n\n      if (details) {\n        return details;\n      } else {\n        p = pos - 1;\n        while (!peg$posDetailsCache[p]) {\n          p--;\n        }\n\n        details = peg$posDetailsCache[p];\n        details = {\n          line:   details.line,\n          column: details.column\n        };\n\n        while (p < pos) {\n          if (input.charCodeAt(p) === 10) {\n            details.line++;\n            details.column = 1;\n          } else {\n            details.column++;\n          }\n\n          p++;\n        }\n\n        peg$posDetailsCache[pos] = details;\n        return details;\n      }\n    }\n\n    function peg$computeLocation(startPos, endPos) {\n      var startPosDetails = peg$computePosDetails(startPos),\n          endPosDetails   = peg$computePosDetails(endPos);\n\n      return {\n        start: {\n          offset: startPos,\n          line:   startPosDetails.line,\n          column: startPosDetails.column\n        },\n        end: {\n          offset: endPos,\n          line:   endPosDetails.line,\n          column: endPosDetails.column\n        }\n      };\n    }\n\n    function peg$fail(expected) {\n      if (peg$currPos < peg$maxFailPos) { return; }\n\n      if (peg$currPos > peg$maxFailPos) {\n        peg$maxFailPos = peg$currPos;\n        peg$maxFailExpected = [];\n      }\n\n      peg$maxFailExpected.push(expected);\n    }\n\n    function peg$buildSimpleError(message, location) {\n      return new peg$SyntaxError(message, null, null, location);\n    }\n\n    function peg$buildStructuredError(expected, found, location) {\n      return new peg$SyntaxError(\n        peg$SyntaxError.buildMessage(expected, found),\n        expected,\n        found,\n        location\n      );\n    }\n\n    function peg$parsestart() {\n      var s0;\n\n      s0 = peg$parsemessageFormatPattern();\n\n      return s0;\n    }\n\n    function peg$parsemessageFormatPattern() {\n      var s0, s1, s2;\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parsemessageFormatElement();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsemessageFormatElement();\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c0(s1);\n      }\n      s0 = s1;\n\n      return s0;\n    }\n\n    function peg$parsemessageFormatElement() {\n      var s0;\n\n      s0 = peg$parsemessageTextElement();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseargumentElement();\n      }\n\n      return s0;\n    }\n\n    function peg$parsemessageText() {\n      var s0, s1, s2, s3, s4, s5;\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$currPos;\n      s3 = peg$parse_();\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsechars();\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parse_();\n          if (s5 !== peg$FAILED) {\n            s3 = [s3, s4, s5];\n            s2 = s3;\n          } else {\n            peg$currPos = s2;\n            s2 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s2;\n          s2 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s2;\n        s2 = peg$FAILED;\n      }\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$currPos;\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parsechars();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parse_();\n              if (s5 !== peg$FAILED) {\n                s3 = [s3, s4, s5];\n                s2 = s3;\n              } else {\n                peg$currPos = s2;\n                s2 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s2;\n              s2 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s2;\n            s2 = peg$FAILED;\n          }\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1);\n      }\n      s0 = s1;\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parsews();\n        if (s1 !== peg$FAILED) {\n          s0 = input.substring(s0, peg$currPos);\n        } else {\n          s0 = s1;\n        }\n      }\n\n      return s0;\n    }\n\n    function peg$parsemessageTextElement() {\n      var s0, s1;\n\n      s0 = peg$currPos;\n      s1 = peg$parsemessageText();\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c2(s1);\n      }\n      s0 = s1;\n\n      return s0;\n    }\n\n    function peg$parseargument() {\n      var s0, s1, s2;\n\n      s0 = peg$parsenumber();\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = [];\n        s2 = peg$parsequoteEscapedChar();\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parsequoteEscapedChar();\n        }\n        if (s1 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c3(s1);\n        }\n        s0 = s1;\n      }\n\n      return s0;\n    }\n\n    function peg$parseargumentElement() {\n      var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 123) {\n        s1 = peg$c4;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c5); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseargument();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$currPos;\n              if (input.charCodeAt(peg$currPos) === 44) {\n                s6 = peg$c6;\n                peg$currPos++;\n              } else {\n                s6 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c7); }\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parse_();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parseelementFormat();\n                  if (s8 !== peg$FAILED) {\n                    s6 = [s6, s7, s8];\n                    s5 = s6;\n                  } else {\n                    peg$currPos = s5;\n                    s5 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s5;\n                  s5 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s5;\n                s5 = peg$FAILED;\n              }\n              if (s5 === peg$FAILED) {\n                s5 = null;\n              }\n              if (s5 !== peg$FAILED) {\n                s6 = peg$parse_();\n                if (s6 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 125) {\n                    s7 = peg$c8;\n                    peg$currPos++;\n                  } else {\n                    s7 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c9); }\n                  }\n                  if (s7 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c10(s3, s5);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parseelementFormat() {\n      var s0;\n\n      s0 = peg$parsesimpleFormat();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsepluralFormat();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parseselectOrdinalFormat();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parseselectFormat();\n          }\n        }\n      }\n\n      return s0;\n    }\n\n    function peg$parsesimpleFormat() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 6) === peg$c11) {\n        s1 = peg$c11;\n        peg$currPos += 6;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c12); }\n      }\n      if (s1 === peg$FAILED) {\n        if (input.substr(peg$currPos, 4) === peg$c13) {\n          s1 = peg$c13;\n          peg$currPos += 4;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c14); }\n        }\n        if (s1 === peg$FAILED) {\n          if (input.substr(peg$currPos, 4) === peg$c15) {\n            s1 = peg$c15;\n            peg$currPos += 4;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c16); }\n          }\n        }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s4 = peg$c6;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c7); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parse_();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsechars();\n              if (s6 !== peg$FAILED) {\n                s4 = [s4, s5, s6];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n          if (s3 === peg$FAILED) {\n            s3 = null;\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c17(s1, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parsepluralFormat() {\n      var s0, s1, s2, s3, s4, s5;\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 6) === peg$c18) {\n        s1 = peg$c18;\n        peg$currPos += 6;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c19); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s3 = peg$c6;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c7); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsepluralStyle();\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c20(s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parseselectOrdinalFormat() {\n      var s0, s1, s2, s3, s4, s5;\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 13) === peg$c21) {\n        s1 = peg$c21;\n        peg$currPos += 13;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c22); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s3 = peg$c6;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c7); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsepluralStyle();\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c23(s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parseselectFormat() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 6) === peg$c24) {\n        s1 = peg$c24;\n        peg$currPos += 6;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c25); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s3 = peg$c6;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c7); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = [];\n              s6 = peg$parseoptionalFormatPattern();\n              if (s6 !== peg$FAILED) {\n                while (s6 !== peg$FAILED) {\n                  s5.push(s6);\n                  s6 = peg$parseoptionalFormatPattern();\n                }\n              } else {\n                s5 = peg$FAILED;\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c26(s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parseselector() {\n      var s0, s1, s2, s3;\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 61) {\n        s2 = peg$c27;\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c28); }\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsenumber();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        s0 = input.substring(s0, peg$currPos);\n      } else {\n        s0 = s1;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsechars();\n      }\n\n      return s0;\n    }\n\n    function peg$parseoptionalFormatPattern() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselector();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 123) {\n              s4 = peg$c4;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c5); }\n            }\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsemessageFormatPattern();\n              if (s5 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 125) {\n                  s6 = peg$c8;\n                  peg$currPos++;\n                } else {\n                  s6 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c9); }\n                }\n                if (s6 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c29(s2, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parseoffset() {\n      var s0, s1, s2, s3;\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 7) === peg$c30) {\n        s1 = peg$c30;\n        peg$currPos += 7;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c31); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parsenumber();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c32(s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parsepluralStyle() {\n      var s0, s1, s2, s3, s4;\n\n      s0 = peg$currPos;\n      s1 = peg$parseoffset();\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$parseoptionalFormatPattern();\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              s4 = peg$parseoptionalFormatPattern();\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c33(s1, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      return s0;\n    }\n\n    function peg$parsews() {\n      var s0, s1;\n\n      peg$silentFails++;\n      s0 = [];\n      if (peg$c35.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c36); }\n      }\n      if (s1 !== peg$FAILED) {\n        while (s1 !== peg$FAILED) {\n          s0.push(s1);\n          if (peg$c35.test(input.charAt(peg$currPos))) {\n            s1 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c36); }\n          }\n        }\n      } else {\n        s0 = peg$FAILED;\n      }\n      peg$silentFails--;\n      if (s0 === peg$FAILED) {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c34); }\n      }\n\n      return s0;\n    }\n\n    function peg$parse_() {\n      var s0, s1, s2;\n\n      peg$silentFails++;\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parsews();\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsews();\n      }\n      if (s1 !== peg$FAILED) {\n        s0 = input.substring(s0, peg$currPos);\n      } else {\n        s0 = s1;\n      }\n      peg$silentFails--;\n      if (s0 === peg$FAILED) {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c37); }\n      }\n\n      return s0;\n    }\n\n    function peg$parsedigit() {\n      var s0;\n\n      if (peg$c38.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c39); }\n      }\n\n      return s0;\n    }\n\n    function peg$parsehexDigit() {\n      var s0;\n\n      if (peg$c40.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c41); }\n      }\n\n      return s0;\n    }\n\n    function peg$parsenumber() {\n      var s0, s1, s2, s3, s4, s5;\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 48) {\n        s1 = peg$c42;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c43); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = peg$currPos;\n        s2 = peg$currPos;\n        if (peg$c44.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c45); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsedigit();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsedigit();\n          }\n          if (s4 !== peg$FAILED) {\n            s3 = [s3, s4];\n            s2 = s3;\n          } else {\n            peg$currPos = s2;\n            s2 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s2;\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          s1 = input.substring(s1, peg$currPos);\n        } else {\n          s1 = s2;\n        }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c46(s1);\n      }\n      s0 = s1;\n\n      return s0;\n    }\n\n    function peg$parsequoteEscapedChar() {\n      var s0, s1, s2;\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      peg$silentFails++;\n      if (input.charCodeAt(peg$currPos) === 39) {\n        s2 = peg$c47;\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c48); }\n      }\n      if (s2 === peg$FAILED) {\n        if (peg$c49.test(input.charAt(peg$currPos))) {\n          s2 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c50); }\n        }\n      }\n      peg$silentFails--;\n      if (s2 === peg$FAILED) {\n        s1 = void 0;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.length > peg$currPos) {\n          s2 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c51); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c52(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 39) {\n          s1 = peg$c47;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c48); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parseescape();\n          if (s2 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c53(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      }\n\n      return s0;\n    }\n\n    function peg$parseapostrophe() {\n      var s0;\n\n      if (input.charCodeAt(peg$currPos) === 39) {\n        s0 = peg$c47;\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c48); }\n      }\n\n      return s0;\n    }\n\n    function peg$parseescape() {\n      var s0;\n\n      if (peg$c49.test(input.charAt(peg$currPos))) {\n        s0 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s0 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c50); }\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseapostrophe();\n      }\n\n      return s0;\n    }\n\n    function peg$parsechar() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 39) {\n        s1 = peg$c47;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c48); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseapostrophe();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c53(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        if (peg$c54.test(input.charAt(peg$currPos))) {\n          s0 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s0 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c55); }\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          if (input.substr(peg$currPos, 2) === peg$c56) {\n            s1 = peg$c56;\n            peg$currPos += 2;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c57); }\n          }\n          if (s1 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c58();\n          }\n          s0 = s1;\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.substr(peg$currPos, 2) === peg$c59) {\n              s1 = peg$c59;\n              peg$currPos += 2;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c60); }\n            }\n            if (s1 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c61();\n            }\n            s0 = s1;\n            if (s0 === peg$FAILED) {\n              s0 = peg$currPos;\n              if (input.substr(peg$currPos, 2) === peg$c62) {\n                s1 = peg$c62;\n                peg$currPos += 2;\n              } else {\n                s1 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c63); }\n              }\n              if (s1 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c64();\n              }\n              s0 = s1;\n              if (s0 === peg$FAILED) {\n                s0 = peg$currPos;\n                if (input.substr(peg$currPos, 2) === peg$c65) {\n                  s1 = peg$c65;\n                  peg$currPos += 2;\n                } else {\n                  s1 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c66); }\n                }\n                if (s1 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c67();\n                }\n                s0 = s1;\n                if (s0 === peg$FAILED) {\n                  s0 = peg$currPos;\n                  if (input.substr(peg$currPos, 2) === peg$c68) {\n                    s1 = peg$c68;\n                    peg$currPos += 2;\n                  } else {\n                    s1 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c69); }\n                  }\n                  if (s1 !== peg$FAILED) {\n                    s2 = peg$currPos;\n                    s3 = peg$currPos;\n                    s4 = peg$parsehexDigit();\n                    if (s4 !== peg$FAILED) {\n                      s5 = peg$parsehexDigit();\n                      if (s5 !== peg$FAILED) {\n                        s6 = peg$parsehexDigit();\n                        if (s6 !== peg$FAILED) {\n                          s7 = peg$parsehexDigit();\n                          if (s7 !== peg$FAILED) {\n                            s4 = [s4, s5, s6, s7];\n                            s3 = s4;\n                          } else {\n                            peg$currPos = s3;\n                            s3 = peg$FAILED;\n                          }\n                        } else {\n                          peg$currPos = s3;\n                          s3 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s3;\n                        s3 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s3;\n                      s3 = peg$FAILED;\n                    }\n                    if (s3 !== peg$FAILED) {\n                      s2 = input.substring(s2, peg$currPos);\n                    } else {\n                      s2 = s3;\n                    }\n                    if (s2 !== peg$FAILED) {\n                      peg$savedPos = s0;\n                      s1 = peg$c70(s2);\n                      s0 = s1;\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      return s0;\n    }\n\n    function peg$parsechars() {\n      var s0, s1, s2;\n\n      s0 = peg$currPos;\n      s1 = [];\n      s2 = peg$parsechar();\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          s2 = peg$parsechar();\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c3(s1);\n      }\n      s0 = s1;\n\n      return s0;\n    }\n\n    peg$result = peg$startRuleFunction();\n\n    if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n      return peg$result;\n    } else {\n      if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n        peg$fail(peg$endExpectation());\n      }\n\n      throw peg$buildStructuredError(\n        peg$maxFailExpected,\n        peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n        peg$maxFailPos < input.length\n          ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n          : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n      );\n    }\n  }\n\n  return {\n    SyntaxError: peg$SyntaxError,\n    parse:       peg$parse\n  };\n})()", "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar Compiler = /** @class */ (function () {\n    function Compiler(locales, formats, formatters) {\n        this.locales = [];\n        this.formats = {\n            number: {},\n            date: {},\n            time: {}\n        };\n        this.pluralNumberFormat = null;\n        this.currentPlural = null;\n        this.pluralStack = [];\n        this.locales = locales;\n        this.formats = formats;\n        this.formatters = formatters;\n    }\n    Compiler.prototype.compile = function (ast) {\n        this.pluralStack = [];\n        this.currentPlural = null;\n        this.pluralNumberFormat = null;\n        return this.compileMessage(ast);\n    };\n    Compiler.prototype.compileMessage = function (ast) {\n        var _this = this;\n        if (!(ast && ast.type === 'messageFormatPattern')) {\n            throw new Error('Message AST is not of type: \"messageFormatPattern\"');\n        }\n        var elements = ast.elements;\n        var pattern = elements\n            .filter(function (el) {\n            return el.type === 'messageTextElement' || el.type === 'argumentElement';\n        })\n            .map(function (el) {\n            return el.type === 'messageTextElement'\n                ? _this.compileMessageText(el)\n                : _this.compileArgument(el);\n        });\n        if (pattern.length !== elements.length) {\n            throw new Error('Message element does not have a valid type');\n        }\n        return pattern;\n    };\n    Compiler.prototype.compileMessageText = function (element) {\n        // When this `element` is part of plural sub-pattern and its value contains\n        // an unescaped '#', use a `PluralOffsetString` helper to properly output\n        // the number with the correct offset in the string.\n        if (this.currentPlural && /(^|[^\\\\])#/g.test(element.value)) {\n            // Create a cache a NumberFormat instance that can be reused for any\n            // PluralOffsetString instance in this message.\n            if (!this.pluralNumberFormat) {\n                this.pluralNumberFormat = new Intl.NumberFormat(this.locales);\n            }\n            return new PluralOffsetString(this.currentPlural.id, this.currentPlural.format.offset, this.pluralNumberFormat, element.value);\n        }\n        // Unescape the escaped '#'s in the message text.\n        return element.value.replace(/\\\\#/g, '#');\n    };\n    Compiler.prototype.compileArgument = function (element) {\n        var format = element.format, id = element.id;\n        var formatters = this.formatters;\n        if (!format) {\n            return new StringFormat(id);\n        }\n        var _a = this, formats = _a.formats, locales = _a.locales;\n        switch (format.type) {\n            case 'numberFormat':\n                return {\n                    id: id,\n                    format: formatters.getNumberFormat(locales, formats.number[format.style]).format\n                };\n            case 'dateFormat':\n                return {\n                    id: id,\n                    format: formatters.getDateTimeFormat(locales, formats.date[format.style]).format\n                };\n            case 'timeFormat':\n                return {\n                    id: id,\n                    format: formatters.getDateTimeFormat(locales, formats.time[format.style]).format\n                };\n            case 'pluralFormat':\n                return new PluralFormat(id, format.offset, this.compileOptions(element), formatters.getPluralRules(locales, {\n                    type: format.ordinal ? 'ordinal' : 'cardinal'\n                }));\n            case 'selectFormat':\n                return new SelectFormat(id, this.compileOptions(element));\n            default:\n                throw new Error('Message element does not have a valid format type');\n        }\n    };\n    Compiler.prototype.compileOptions = function (element) {\n        var _this = this;\n        var format = element.format;\n        var options = format.options;\n        // Save the current plural element, if any, then set it to a new value when\n        // compiling the options sub-patterns. This conforms the spec's algorithm\n        // for handling `\"#\"` syntax in message text.\n        this.pluralStack.push(this.currentPlural);\n        this.currentPlural = format.type === 'pluralFormat' ? element : null;\n        var optionsHash = options.reduce(function (all, option) {\n            // Compile the sub-pattern and save it under the options's selector.\n            all[option.selector] = _this.compileMessage(option.value);\n            return all;\n        }, {});\n        // Pop the plural stack to put back the original current plural value.\n        this.currentPlural = this.pluralStack.pop();\n        return optionsHash;\n    };\n    return Compiler;\n}());\nexport default Compiler;\n// -- Compiler Helper Classes --------------------------------------------------\nvar Formatter = /** @class */ (function () {\n    function Formatter(id) {\n        this.id = id;\n    }\n    return Formatter;\n}());\nvar StringFormat = /** @class */ (function (_super) {\n    __extends(StringFormat, _super);\n    function StringFormat() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    StringFormat.prototype.format = function (value) {\n        if (!value && typeof value !== 'number') {\n            return '';\n        }\n        return typeof value === 'string' ? value : String(value);\n    };\n    return StringFormat;\n}(Formatter));\nvar PluralFormat = /** @class */ (function () {\n    function PluralFormat(id, offset, options, pluralRules) {\n        this.id = id;\n        this.offset = offset;\n        this.options = options;\n        this.pluralRules = pluralRules;\n    }\n    PluralFormat.prototype.getOption = function (value) {\n        var options = this.options;\n        var option = options['=' + value] ||\n            options[this.pluralRules.select(value - this.offset)];\n        return option || options.other;\n    };\n    return PluralFormat;\n}());\nvar PluralOffsetString = /** @class */ (function (_super) {\n    __extends(PluralOffsetString, _super);\n    function PluralOffsetString(id, offset, numberFormat, string) {\n        var _this = _super.call(this, id) || this;\n        _this.offset = offset;\n        _this.numberFormat = numberFormat;\n        _this.string = string;\n        return _this;\n    }\n    PluralOffsetString.prototype.format = function (value) {\n        var number = this.numberFormat.format(value - this.offset);\n        return this.string\n            .replace(/(^|[^\\\\])#/g, '$1' + number)\n            .replace(/\\\\#/g, '#');\n    };\n    return PluralOffsetString;\n}(Formatter));\nexport { PluralOffsetString };\nvar SelectFormat = /** @class */ (function () {\n    function SelectFormat(id, options) {\n        this.id = id;\n        this.options = options;\n    }\n    SelectFormat.prototype.getOption = function (value) {\n        var options = this.options;\n        return options[value] || options.other;\n    };\n    return SelectFormat;\n}());\nexport { SelectFormat };\nexport function isSelectOrPluralFormat(f) {\n    return !!f.options;\n}\n//# sourceMappingURL=compiler.js.map", "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n/* jslint esnext: true */\nimport Compiler, { isSelectOrPluralFormat } from './compiler';\n// -- MessageFormat --------------------------------------------------------\nfunction resolveLocale(locales) {\n    if (typeof locales === 'string') {\n        locales = [locales];\n    }\n    try {\n        return Intl.NumberFormat.supportedLocalesOf(locales, {\n            // IE11 localeMatcher `lookup` seems to convert `en` -> `en-US`\n            // but not other browsers,\n            localeMatcher: 'best fit'\n        })[0];\n    }\n    catch (e) {\n        return IntlMessageFormat.defaultLocale;\n    }\n}\nfunction formatPatterns(pattern, values) {\n    var result = '';\n    for (var _i = 0, pattern_1 = pattern; _i < pattern_1.length; _i++) {\n        var part = pattern_1[_i];\n        // Exist early for string parts.\n        if (typeof part === 'string') {\n            result += part;\n            continue;\n        }\n        var id = part.id;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && id in values)) {\n            throw new FormatError(\"A value must be provided for: \" + id, id);\n        }\n        var value = values[id];\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if (isSelectOrPluralFormat(part)) {\n            result += formatPatterns(part.getOption(value), values);\n        }\n        else {\n            result += part.format(value);\n        }\n    }\n    return result;\n}\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return __assign({}, (c1 || {}), (c2 || {}), Object.keys(c1).reduce(function (all, k) {\n        all[k] = __assign({}, c1[k], (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, __assign({}, defaultConfig));\n}\nvar FormatError = /** @class */ (function (_super) {\n    __extends(FormatError, _super);\n    function FormatError(msg, variableId) {\n        var _this = _super.call(this, msg) || this;\n        _this.variableId = variableId;\n        return _this;\n    }\n    return FormatError;\n}(Error));\nexport function createDefaultFormatters() {\n    return {\n        getNumberFormat: function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, [void 0].concat(args)))();\n        },\n        getDateTimeFormat: function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, [void 0].concat(args)))();\n        },\n        getPluralRules: function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, [void 0].concat(args)))();\n        }\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        var _this = this;\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        this.format = function (values) {\n            try {\n                return formatPatterns(_this.pattern, values);\n            }\n            catch (e) {\n                if (e.variableId) {\n                    throw new Error(\"The intl string context variable '\" + e.variableId + \"' was not provided to the string '\" + _this.message + \"'\");\n                }\n                else {\n                    throw e;\n                }\n            }\n        };\n        if (typeof message === 'string') {\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message);\n        }\n        else {\n            this.ast = message;\n        }\n        this.message = message;\n        if (!(this.ast && this.ast.type === 'messageFormatPattern')) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        var formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        // Defined first because it's used to build the format pattern.\n        this.locale = resolveLocale(locales || []);\n        var formatters = (opts && opts.formatters) || createDefaultFormatters();\n        // Compile the `ast` to a pattern that is highly optimized for repeated\n        // `format()` invocations. **Note:** This passes the `locales` set provided\n        // to the constructor instead of just the resolved locale.\n        this.pattern = new Compiler(locales, formats, formatters).compile(this.ast);\n        // \"Bind\" `format()` method to `this` so it can be passed by reference like\n        // the other `Intl` APIs.\n    }\n    IntlMessageFormat.prototype.resolvedOptions = function () {\n        return { locale: this.locale };\n    };\n    IntlMessageFormat.prototype.getAst = function () {\n        return this.ast;\n    };\n    IntlMessageFormat.defaultLocale = 'en';\n    IntlMessageFormat.__parse = undefined;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            currency: {\n                style: 'currency'\n            },\n            percent: {\n                style: 'percent'\n            }\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit'\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric'\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric'\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric'\n            }\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric'\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric'\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short'\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short'\n            }\n        }\n    };\n    return IntlMessageFormat;\n}());\nexport { IntlMessageFormat };\nexport default IntlMessageFormat;\n//# sourceMappingURL=core.js.map", "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nimport parser from 'intl-messageformat-parser';\nimport IntlMessageFormat from './core';\nIntlMessageFormat.__parse = parser.parse;\nexport * from './core';\nexport default IntlMessageFormat;\n//# sourceMappingURL=index.js.map"], "names": ["child", "parent", "peg$SyntaxError", "Error", "ctor", "prototype", "buildMessage", "expected", "found", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "class", "i", "escapedParts", "parts", "length", "Array", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "replace", "j", "descriptions", "type", "sort", "slice", "join", "describeExpected", "SyntaxError", "parse", "input", "options", "peg$result", "location", "peg$FAILED", "peg$startRuleFunctions", "start", "peg$parsestart", "peg$startRuleFunction", "peg$c0", "elements", "peg$c1", "chunks", "reduce", "all", "chunk", "concat", "peg$c2", "messageText", "value", "peg$c3", "chars", "peg$c4", "peg$c5", "peg$literalExpectation", "peg$c6", "peg$c7", "peg$c8", "peg$c9", "peg$c10", "id", "format", "peg$c11", "peg$c12", "peg$c13", "peg$c14", "peg$c15", "peg$c16", "peg$c17", "style", "peg$c18", "peg$c19", "peg$c20", "pluralStyle", "ordinal", "offset", "peg$c21", "peg$c22", "peg$c23", "peg$c24", "peg$c25", "peg$c26", "peg$c27", "peg$c28", "peg$c29", "selector", "pattern", "peg$c30", "peg$c31", "peg$c32", "number", "peg$c33", "peg$c34", "peg$otherExpectation", "peg$c35", "peg$c36", "peg$classExpectation", "peg$c37", "peg$c38", "peg$c39", "peg$c40", "peg$c41", "peg$c42", "peg$c43", "peg$c44", "peg$c45", "peg$c46", "digits", "parseInt", "peg$c47", "peg$c48", "peg$c49", "peg$c50", "peg$c51", "peg$c52", "char", "peg$c53", "sequence", "peg$c54", "peg$c55", "peg$c56", "peg$c57", "peg$c58", "peg$c59", "peg$c60", "peg$c61", "peg$c62", "peg$c63", "peg$c64", "peg$c65", "peg$c66", "peg$c67", "peg$c68", "peg$c69", "peg$c70", "String", "fromCharCode", "peg$currPos", "peg$savedPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$silentFails", "startRule", "peg$computeLocation", "ignoreCase", "peg$computePosDetails", "pos", "p", "details", "startPos", "endPos", "startPosDetails", "endPosDetails", "peg$fail", "push", "peg$parsemessageFormatPattern", "s0", "s1", "s2", "peg$parsemessageFormatElement", "s3", "s4", "s5", "peg$parse_", "peg$parsechars", "peg$parsews", "substring", "peg$parsemessageText", "peg$parsemessageTextElement", "s6", "s7", "s8", "peg$parsenumber", "peg$parsequoteEscapedChar", "peg$parseargument", "substr", "peg$parsesimpleFormat", "peg$parsepluralStyle", "peg$parsepluralFormat", "peg$parseselectOrdinalFormat", "peg$parseoptionalFormatPattern", "peg$parseselectFormat", "peg$parseelementFormat", "peg$parseargumentElement", "peg$parseselector", "peg$parseoffset", "test", "char<PERSON>t", "peg$parsedigit", "peg$parsehexDigit", "peg$parseapostrophe", "peg$parseescape", "peg$parsechar", "message", "this", "name", "captureStackTrace", "constructor", "extendStatics", "__extends", "d", "b", "Object", "setPrototypeOf", "__proto__", "hasOwnProperty", "__", "create", "Compiler", "compile", "ast", "pluralStack", "currentPlural", "pluralNumberFormat", "compileMessage", "_this", "filter", "el", "map", "compileMessageText", "compileArgument", "element", "Intl", "NumberFormat", "locales", "PluralOffsetString", "formatters", "StringFormat", "formats", "getNumberFormat", "getDateTimeFormat", "date", "time", "PluralFormat", "compileOptions", "getPluralRules", "SelectFormat", "optionsHash", "option", "pop", "<PERSON><PERSON><PERSON>", "_super", "apply", "arguments", "getOption", "pluralRules", "select", "numberFormat", "string", "call", "__assign", "assign", "t", "n", "mergeConfigs", "defaultConfig", "configs", "keys", "k", "c1", "c2", "mergeConfig", "FormatError", "msg", "variableId", "createDefaultFormatters", "_a", "args", "_i", "bind", "DateTimeFormat", "PluralRules", "IntlMessageFormat", "resolvedOptions", "locale", "getAst", "defaultLocale", "__parse", "undefined", "currency", "percent", "short", "month", "day", "year", "medium", "long", "full", "weekday", "hour", "minute", "second", "timeZoneName", "overrideFormats", "opts", "values", "formatPatterns", "result", "pattern_1", "part", "e", "TypeError", "supportedLocalesOf", "localeMatcher", "resolveLocale", "parser"], "mappings": "kNAQwBA,EAAOC,KAAPD,EAkBTE,GAlBgBD,EAkBCE,MAhB5BC,EAAKC,UAAYJ,EAAOI,UACxBL,EAAMK,UAAY,IAAID,EAiBxBF,GAAgBI,aAAe,SAASC,EAAUC,GAChD,IAmGuBA,EAnGnBC,EAA2B,CACzBC,QAAS,SAASC,GAChB,MAAO,IAAOC,EAAcD,EAAYE,MAAQ,KAGlDC,MAAS,SAASH,GAChB,IACII,EADAC,EAAe,GAGnB,IAAKD,EAAI,EAAGA,EAAIJ,EAAYM,MAAMC,OAAQH,IACxCC,GAAgBL,EAAYM,MAAMF,aAAcI,MAC5CC,EAAYT,EAAYM,MAAMF,GAAG,IAAM,IAAMK,EAAYT,EAAYM,MAAMF,GAAG,IAC9EK,EAAYT,EAAYM,MAAMF,IAGpC,MAAO,KAAOJ,EAAYU,SAAW,IAAM,IAAML,EAAe,KAGlEM,IAAK,SAASX,GACZ,MAAO,iBAGTY,IAAK,SAASZ,GACZ,MAAO,gBAGTa,MAAO,SAASb,GACd,OAAOA,EAAYc,cAI3B,SAASC,EAAIC,GACX,OAAOA,EAAGC,WAAW,GAAGC,SAAS,IAAIC,cAGvC,SAASlB,EAAcmB,GACrB,OAAOA,EACJC,QAAQ,MAAO,QACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,eAAyB,SAASL,GAAM,MAAO,OAASD,EAAIC,KACpEK,QAAQ,wBAAyB,SAASL,GAAM,MAAO,MAASD,EAAIC,KAGzE,SAASP,EAAYW,GACnB,OAAOA,EACJC,QAAQ,MAAO,QACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,eAAyB,SAASL,GAAM,MAAO,OAASD,EAAIC,KACpEK,QAAQ,wBAAyB,SAASL,GAAM,MAAO,MAASD,EAAIC,KA6CzE,MAAO,YAtCP,SAA0BpB,GACxB,IACIQ,EAAGkB,EANoBtB,EAKvBuB,EAAe,IAAIf,MAAMZ,EAASW,QAGtC,IAAKH,EAAI,EAAGA,EAAIR,EAASW,OAAQH,IAC/BmB,EAAanB,IATYJ,EASaJ,EAASQ,GAR1CN,EAAyBE,EAAYwB,MAAMxB,IAalD,GAFAuB,EAAaE,OAEa,EAAtBF,EAAahB,OAAY,CAC3B,IAAYe,EAAPlB,EAAI,EAAUA,EAAImB,EAAahB,OAAQH,IACtCmB,EAAanB,EAAI,KAAOmB,EAAanB,KACvCmB,EAAaD,GAAKC,EAAanB,GAC/BkB,KAGJC,EAAahB,OAASe,EAGxB,OAAQC,EAAahB,QACnB,KAAK,EACH,OAAOgB,EAAa,GAEtB,KAAK,EACH,OAAOA,EAAa,GAAK,OAASA,EAAa,GAEjD,QACE,OAAOA,EAAaG,MAAM,GAAI,GAAGC,KAAK,MAClC,QACAJ,EAAaA,EAAahB,OAAS,IAQxBqB,CAAiBhC,GAAY,UAJ3BC,EAImDA,GAHzD,IAAOI,EAAcJ,GAAS,IAAO,gBAG6B,WAu4C9E,CACLgC,YAAatC,GACbuC,MAt4CF,SAAmBC,EAAOC,GACxBA,OAAsB,IAAZA,EAAqBA,EAAU,GAEzC,IA2JIC,EAwH8BrC,EAAUC,EAAOqC,EAnR/CC,EAAa,GAEbC,EAAyB,CAAEC,MAAOC,IAClCC,EAAyBD,GAEzBE,EAAS,SAASC,GACV,MAAO,CACHjB,KAAU,uBACViB,SAAUA,EACVP,SAAUA,OAGtBQ,EAAS,SAASC,GACV,OAAOA,EAAOC,OAAO,SAAUC,EAAKC,GAChC,OAAOD,EAAIE,OAAOD,IACnB,IAAInB,KAAK,KAEpBqB,EAAS,SAASC,GACV,MAAO,CACHzB,KAAO,qBACP0B,MAAOD,EACPf,SAAUA,OAGtBiB,EAAS,SAASC,GAAS,OAAOA,EAAMzB,KAAK,KAC7C0B,EAAS,IACTC,EAASC,GAAuB,KAAK,GACrCC,EAAS,IACTC,EAASF,GAAuB,KAAK,GACrCG,EAAS,IACTC,EAASJ,GAAuB,KAAK,GACrCK,EAAU,SAASC,EAAIC,GACf,MAAO,CACHtC,KAAQ,kBACRqC,GAAQA,EACRC,OAAQA,GAAUA,EAAO,GACzB5B,SAAUA,OAGtB6B,EAAU,SACVC,EAAUT,GAAuB,UAAU,GAC3CU,EAAU,OACVC,EAAUX,GAAuB,QAAQ,GACzCY,EAAU,OACVC,EAAUb,GAAuB,QAAQ,GACzCc,EAAU,SAAS7C,EAAM8C,GACjB,MAAO,CACH9C,KAAOA,EAAO,SACd8C,MAAOA,GAASA,EAAM,GACtBpC,SAAUA,OAGtBqC,EAAU,SACVC,EAAUjB,GAAuB,UAAU,GAC3CkB,EAAU,SAASC,GACX,MAAO,CACHlD,KAASkD,EAAYlD,KACrBmD,SAAS,EACTC,OAASF,EAAYE,QAAU,EAC/B5C,QAAS0C,EAAY1C,QACrBE,SAAUA,OAGtB2C,EAAU,gBACVC,EAAUvB,GAAuB,iBAAiB,GAClDwB,EAAU,SAASL,GACX,MAAO,CACHlD,KAASkD,EAAYlD,KACrBmD,SAAS,EACTC,OAASF,EAAYE,QAAU,EAC/B5C,QAAS0C,EAAY1C,QACrBE,SAAUA,OAGtB8C,EAAU,SACVC,EAAU1B,GAAuB,UAAU,GAC3C2B,EAAU,SAASlD,GACX,MAAO,CACHR,KAAS,eACTQ,QAASA,EACTE,SAAUA,OAGtBiD,EAAU,IACVC,EAAU7B,GAAuB,KAAK,GACtC8B,EAAU,SAASC,EAAUC,GACrB,MAAO,CACH/D,KAAU,wBACV8D,SAAUA,EACVpC,MAAUqC,EACVrD,SAAUA,OAGtBsD,EAAU,UACVC,EAAUlC,GAAuB,WAAW,GAC5CmC,EAAU,SAASC,GACX,OAAOA,GAEfC,EAAU,SAAShB,EAAQ5C,GACnB,MAAO,CACHR,KAAS,eACToD,OAASA,EACT5C,QAASA,EACTE,SAAUA,OAGtB2D,EAAUC,GAAqB,cAC/BC,EAAU,aACVC,EAAUC,GAAqB,CAAC,IAAK,KAAM,KAAM,OAAO,GAAO,GAC/DC,EAAUJ,GAAqB,sBAC/BK,EAAU,SACVC,EAAUH,GAAqB,CAAC,CAAC,IAAK,OAAO,GAAO,GACpDI,EAAU,aACVC,EAAUL,GAAqB,CAAC,CAAC,IAAK,KAAM,CAAC,IAAK,OAAO,GAAO,GAChEM,EAAU,IACVC,EAAUjD,GAAuB,KAAK,GACtCkD,EAAU,SACVC,GAAUT,GAAqB,CAAC,CAAC,IAAK,OAAO,GAAO,GACpDU,GAAU,SAASC,GACf,OAAOC,SAASD,EAAQ,KAE5BE,GAAU,IACVC,GAAUxD,GAAuB,KAAK,GACtCyD,GAAU,oBACVC,GAAUhB,GAAqB,CAAC,IAAK,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAM,GAAO,GAClGiB,GAyEK,CAAE1F,KAAM,OAxEb2F,GAAU,SAASC,GAAQ,OAAOA,GAClCC,GAAU,SAASC,GAAY,OAAOA,GACtCC,GAAU,6BACVC,GAAUvB,GAAqB,CAAC,IAAK,IAAK,KAAM,CAAC,KAAM,KAAS,IAAQ,IAAK,KAAM,KAAM,OAAO,GAAM,GACtGwB,GAAU,OACVC,GAAUnE,GAAuB,QAAQ,GACzCoE,GAAU,WAAa,MAAO,MAC9BC,GAAU,MACVC,GAAUtE,GAAuB,OAAO,GACxCuE,GAAU,WAAa,MAAO,OAC9BC,GAAU,MACVC,GAAUzE,GAAuB,OAAO,GACxC0E,GAAU,WAAa,MAAO,KAC9BC,GAAU,MACVC,GAAU5E,GAAuB,OAAO,GACxC6E,GAAU,WAAa,MAAO,KAC9BC,GAAU,MACVC,GAAU/E,GAAuB,OAAO,GACxCgF,GAAU,SAAS3B,GACX,OAAO4B,OAAOC,aAAa5B,SAASD,EAAQ,MAGpD8B,GAAuB,EACvBC,GAAuB,EACvBC,GAAuB,CAAC,CAAEC,KAAM,EAAGC,OAAQ,IAC3CC,GAAuB,EACvBC,GAAuB,GACvBC,GAAuB,EAI3B,GAAI,cAAejH,EAAS,CAC1B,KAAMA,EAAQkH,aAAa9G,GACzB,MAAM,IAAI5C,MAAM,mCAAqCwC,EAAQkH,UAAY,MAG3E3G,EAAwBH,EAAuBJ,EAAQkH,WAOzD,SAAShH,KACP,OAAOiH,GAAoBR,GAAcD,IAmB3C,SAASnF,GAAuBrD,EAAMkJ,GACpC,MAAO,CAAE5H,KAAM,UAAWtB,KAAMA,EAAMkJ,WAAYA,GAGpD,SAASnD,GAAqB3F,EAAOI,EAAU0I,GAC7C,MAAO,CAAE5H,KAAM,QAASlB,MAAOA,EAAOI,SAAUA,EAAU0I,WAAYA,GAWxE,SAAStD,GAAqBhF,GAC5B,MAAO,CAAEU,KAAM,QAASV,YAAaA,GAGvC,SAASuI,GAAsBC,GAC7B,IAAwCC,EAApCC,EAAUZ,GAAoBU,GAElC,GAAIE,EACF,OAAOA,EAGP,IADAD,EAAID,EAAM,GACFV,GAAoBW,IAC1BA,IASF,IALAC,EAAU,CACRX,MAFFW,EAAUZ,GAAoBW,IAEZV,KAChBC,OAAQU,EAAQV,QAGXS,EAAID,GACmB,KAAxBvH,EAAMd,WAAWsI,IACnBC,EAAQX,OACRW,EAAQV,OAAS,GAEjBU,EAAQV,SAGVS,IAIF,OADAX,GAAoBU,GAAOE,EAK/B,SAASL,GAAoBM,EAAUC,GACrC,IAAIC,EAAkBN,GAAsBI,GACxCG,EAAkBP,GAAsBK,GAE5C,MAAO,CACLrH,MAAO,CACLuC,OAAQ6E,EACRZ,KAAQc,EAAgBd,KACxBC,OAAQa,EAAgBb,QAE1BlI,IAAK,CACHgE,OAAQ8E,EACRb,KAAQe,EAAcf,KACtBC,OAAQc,EAAcd,SAK5B,SAASe,GAASjK,GACZ8I,GAAcK,KAEAA,GAAdL,KACFK,GAAiBL,GACjBM,GAAsB,IAGxBA,GAAoBc,KAAKlK,IAgB3B,SAAS0C,KAKP,OAFKyH,KAKP,SAASA,KACP,IAAIC,EAAIC,EAAIC,EAKZ,IAHAF,EAAKtB,GACLuB,EAAK,GACLC,EAAKC,KACED,IAAO/H,GACZ8H,EAAGH,KAAKI,GACRA,EAAKC,KAQP,OANIF,IAAO9H,IACTwG,GAAeqB,EACfC,EAAKzH,EAAOyH,IAEdD,EAAKC,EAKP,SAASE,KACP,IAAIH,EAOJ,OALAA,EAgFF,WACE,IAAIA,EAAIC,EAUR,OARAD,EAAKtB,IACLuB,EA5EF,WACE,IAAID,EAAIC,EAAIC,EAAIE,EAAIC,EAAIC,EAyBxB,GAtBAL,EAAK,GACLC,EAFAF,EAAKtB,IAUCwB,GAPNE,EAAKG,QACMpI,GACTkI,EAAKG,QACMrI,IACTmI,EAAKC,QACMpI,EACTiI,EAAK,CAACA,EAAIC,EAAIC,IAOhB5B,GAAcwB,EACT/H,IAGPuG,GAAcwB,EACT/H,MAEIA,EACT,KAAO+H,IAAO/H,GACZ8H,EAAGH,KAAKI,GACRA,EAAKxB,GACL0B,EAAKG,KAOCL,EANFE,IAAOjI,IACTkI,EAAKG,QACMrI,IACTmI,EAAKC,QACMpI,EACTiI,EAAK,CAACA,EAAIC,EAAIC,IAWlB5B,GAAcwB,EACT/H,QAIT8H,EAAK9H,EAiBP,OAfI8H,IAAO9H,IACTwG,GAAeqB,EACfC,EAAKvH,EAAOuH,KAEdD,EAAKC,KACM9H,IACT6H,EAAKtB,GACLuB,EAAKQ,KAEHT,EADEC,IAAO9H,EACJJ,EAAM2I,UAAUV,EAAItB,IAEpBuB,GAIFD,EAOFW,MACMxI,IACTwG,GAAeqB,EACfC,EAAKjH,EAAOiH,IAEdD,EAAKC,EAzFAW,MACMzI,IACT6H,EAkHJ,WACE,IAAIA,EAAIC,EAAQG,EAAQE,EAAIO,EAAIC,EAAIC,EA0FpC,OAxFAf,EAAKtB,GACiC,MAAlC3G,EAAMd,WAAWyH,KACnBuB,EAAK5G,EACLqF,OAEAuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAASvG,IAoD1B0G,EAlDVC,IAAO9H,EACJoI,OACMpI,IACTiI,EApCN,WACE,IAAIJ,EAAIC,EAAIC,EAGZ,IADAF,EAAKgB,QACM7I,EAAY,CAIrB,IAHA6H,EAAKtB,GACLuB,EAAK,GACLC,EAAKe,KACEf,IAAO/H,GACZ8H,EAAGH,KAAKI,GACRA,EAAKe,KAEHhB,IAAO9H,IACTwG,GAAeqB,EACfC,EAAK9G,EAAO8G,IAEdD,EAAKC,EAGP,OAAOD,EAiBEkB,MACM/I,GACJoI,OACMpI,GACTmI,EAAK5B,GACiC,KAAlC3G,EAAMd,WAAWyH,KACnBmC,EAAKrH,EACLkF,OAEAmC,EAAK1I,EACmB,IAApB8G,IAAyBY,GAASpG,KAQlC6G,EANFO,IAAO1I,IACT2I,EAAKP,QACMpI,IACT4I,EAiEd,WACE,IAAIf,EAaJ,OAXAA,EAcF,WACE,IAAIA,EAAIC,EAAQG,EAAIC,EAAIC,EAAIO,EA8E5B,OA5EAb,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAO3E,GACnCkG,EAAKlG,EACL2E,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS7F,IAEpCiG,IAAO9H,IACLJ,EAAMoJ,OAAOzC,GAAa,KAAOzE,GACnCgG,EAAKhG,EACLyE,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS3F,IAEpC+F,IAAO9H,IACLJ,EAAMoJ,OAAOzC,GAAa,KAAOvE,GACnC8F,EAAK9F,EACLuE,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAASzF,MAwCtC4F,EApCFC,IAAO9H,EACJoI,OACMpI,GACTiI,EAAK1B,GACiC,KAAlC3G,EAAMd,WAAWyH,KACnB2B,EAAK7G,EACLkF,OAEA2B,EAAKlI,EACmB,IAApB8G,IAAyBY,GAASpG,KAQlC2G,EANFC,IAAOlI,IACTmI,EAAKC,QACMpI,IACT0I,EAAKL,QACMrI,EACTkI,EAAK,CAACA,EAAIC,EAAIO,IAWlBnC,GAAc0B,EACTjI,MAEIA,IACTiI,EAAK,MAEHA,IAAOjI,GACTwG,GAAeqB,EACfC,EAAK5F,EAAQ4F,EAAIG,KAGjB1B,GAAcsB,EACT7H,KAGPuG,GAAcsB,EACT7H,IAGPuG,GAAcsB,EACT7H,GA1FFiJ,MACMjJ,IACT6H,EA8FJ,WACE,IAAIA,EAAIC,EAAQG,EAAQE,EAiDxB,OA/CAN,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAOnE,GACnC0F,EAAK1F,EACLmE,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAASrF,IAmB9BwF,EAjBNC,IAAO9H,EACJoI,OACMpI,GAC6B,KAAlCJ,EAAMd,WAAWyH,KACnB0B,EAAK5G,EACLkF,OAEA0B,EAAKjI,EACmB,IAApB8G,IAAyBY,GAASpG,IAEpC2G,IAAOjI,GACJoI,OACMpI,IACTmI,EAAKe,QACMlJ,GACTwG,GAAeqB,EACfC,EAAKxF,EAAQ6F,KAWjB5B,GAAcsB,EACT7H,KAGPuG,GAAcsB,EACT7H,IAGPuG,GAAcsB,EACT7H,GA7IAmJ,MACMnJ,IACT6H,EAiJN,WACE,IAAIA,EAAIC,EAAQG,EAAQE,EAiDxB,OA/CAN,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,MAAQ7D,GACpCoF,EAAKpF,EACL6D,IAAe,KAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS/E,IAmB9BkF,EAjBNC,IAAO9H,EACJoI,OACMpI,GAC6B,KAAlCJ,EAAMd,WAAWyH,KACnB0B,EAAK5G,EACLkF,OAEA0B,EAAKjI,EACmB,IAApB8G,IAAyBY,GAASpG,IAEpC2G,IAAOjI,GACJoI,OACMpI,IACTmI,EAAKe,QACMlJ,GACTwG,GAAeqB,EACfC,EAAKlF,EAAQuF,KAWjB5B,GAAcsB,EACT7H,KAGPuG,GAAcsB,EACT7H,IAGPuG,GAAcsB,EACT7H,GAhMEoJ,MACMpJ,IACT6H,EAoMR,WACE,IAAIA,EAAIC,EAAQG,EAAQE,EAAIO,EAU5B,GARAb,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAO1D,GACnCiF,EAAKjF,EACL0D,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS5E,IAEpCgF,IAAO9H,EAET,GADKoI,OACMpI,EAQT,GAPsC,KAAlCJ,EAAMd,WAAWyH,KACnB0B,EAAK5G,EACLkF,OAEA0B,EAAKjI,EACmB,IAApB8G,IAAyBY,GAASpG,IAEpC2G,IAAOjI,EAET,GADKoI,OACMpI,EAAY,CAGrB,GAFAmI,EAAK,IACLO,EAAKW,QACMrJ,EACT,KAAO0I,IAAO1I,GACZmI,EAAGR,KAAKe,GACRA,EAAKW,UAGPlB,EAAKnI,EAKL6H,EAHEM,IAAOnI,GACTwG,GAAeqB,EACfC,EAAK/E,EAAQoF,KAGb5B,GAAcsB,EACT7H,QAGPuG,GAAcsB,EACdA,EAAK7H,OAGPuG,GAAcsB,EACdA,EAAK7H,OAGPuG,GAAcsB,EACdA,EAAK7H,OAGPuG,GAAcsB,EACdA,EAAK7H,EAGP,OAAO6H,EA/PIyB,IAKJzB,EA/EU0B,MACMvJ,EACT0I,EAAK,CAACA,EAAIC,EAAIC,IAWlBrC,GAAc4B,EACTnI,MAEIA,IACTmI,EAAK,MAEHA,IAAOnI,IACT0I,EAAKN,QACMpI,GAC6B,MAAlCJ,EAAMd,WAAWyH,KACnBoC,EAAKpH,EACLgF,OAEAoC,EAAK3I,EACmB,IAApB8G,IAAyBY,GAASlG,IAEpCmH,IAAO3I,GACTwG,GAAeqB,EACfC,EAAKrG,EAAQwG,EAAIE,KAGjB5B,GAAcsB,EACT7H,KAOTuG,GAAcsB,EACT7H,KAWXuG,GAAcsB,EACT7H,IAGPuG,GAAcsB,EACT7H,GA1MAwJ,IAGA3B,EA6fT,SAASwB,KACP,IAAIxB,EAAQE,EAAQG,EAAIC,EAAIO,EAuD5B,OArDAb,EAAKtB,GA2BOsB,EA1BPO,OACMpI,IACT+H,EA3CJ,WACE,IAAIF,EAAIC,EAAIC,EAAIE,EAiChB,OA9BAH,EADAD,EAAKtB,GAEiC,KAAlC3G,EAAMd,WAAWyH,KACnBwB,EAAK/E,EACLuD,OAEAwB,EAAK/H,EACmB,IAApB8G,IAAyBY,GAASzE,KAgBtC4E,GAVEC,EAJAC,IAAO/H,IACTiI,EAAKY,QACM7I,EACT+H,EAAK,CAACA,EAAIE,IAOZ1B,GAAcuB,EACT9H,MAEIA,EACJJ,EAAM2I,UAAUV,EAAItB,IAEpBuB,KAEI9H,IACT6H,EAAKQ,MAGAR,EASA4B,MACMzJ,GACJoI,OACMpI,GAC6B,MAAlCJ,EAAMd,WAAWyH,KACnB2B,EAAKhH,EACLqF,OAEA2B,EAAKlI,EACmB,IAApB8G,IAAyBY,GAASvG,IAEpC+G,IAAOlI,IACTmI,EAAKP,QACM5H,GAC6B,MAAlCJ,EAAMd,WAAWyH,KACnBmC,EAAKnH,EACLgF,OAEAmC,EAAK1I,EACmB,IAApB8G,IAAyBY,GAASlG,IAEpCkH,IAAO1I,GACTwG,GAAeqB,EACV3E,EAAQ6E,EAAII,KAGjB5B,GAAcsB,EACT7H,KAOTuG,GAAcsB,EACT7H,KAWXuG,GAAcsB,EACT7H,GAyCT,SAASkJ,KACP,IAAIrB,EAAIC,EAAQG,EAAIC,EAOpB,GALAL,EAAKtB,IACLuB,EAvCF,WACE,IAAID,EAAIC,EAAQG,EA+BhB,OA7BAJ,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAOlD,GACnCyE,EAAKzE,EACLkD,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAASpE,IASlCuE,EAPFC,IAAO9H,GACJoI,OACMpI,IACTiI,EAAKY,QACM7I,GACTwG,GAAeqB,EACfC,EAAKvE,EAAQ0E,KAWjB1B,GAAcsB,EACT7H,GAUF0J,MACM1J,IACT8H,EAAK,MAEHA,IAAO9H,EAET,GADKoI,OACMpI,EAAY,CAGrB,GAFAiI,EAAK,IACLC,EAAKmB,QACMrJ,EACT,KAAOkI,IAAOlI,GACZiI,EAAGN,KAAKO,GACRA,EAAKmB,UAGPpB,EAAKjI,EAKL6H,EAHEI,IAAOjI,GACTwG,GAAeqB,EACfC,EAAKrE,EAAQqE,EAAIG,KAGjB1B,GAAcsB,EACT7H,QAGPuG,GAAcsB,EACdA,EAAK7H,OAGPuG,GAAcsB,EACdA,EAAK7H,EAGP,OAAO6H,EAGT,SAASS,KACP,IAAIT,EAAIC,EAWR,GATAhB,KACAe,EAAK,GACDjE,EAAQ+F,KAAK/J,EAAMgK,OAAOrD,MAC5BuB,EAAKlI,EAAMgK,OAAOrD,IAClBA,OAEAuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS7D,IAEpCiE,IAAO9H,EACT,KAAO8H,IAAO9H,GACZ6H,EAAGF,KAAKG,GACJlE,EAAQ+F,KAAK/J,EAAMgK,OAAOrD,MAC5BuB,EAAKlI,EAAMgK,OAAOrD,IAClBA,OAEAuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS7D,SAI1CgE,EAAK7H,EAQP,OANA8G,KACIe,IAAO7H,IACT8H,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAShE,IAGjCmE,EAGT,SAASO,KACP,IAAIP,EAAIC,EAAIC,EAMZ,IAJAjB,KACAe,EAAKtB,GACLuB,EAAK,GACLC,EAAKO,KACEP,IAAO/H,GACZ8H,EAAGH,KAAKI,GACRA,EAAKO,KAaP,OAVET,EADEC,IAAO9H,EACJJ,EAAM2I,UAAUV,EAAItB,IAEpBuB,EAEPhB,KACIe,IAAO7H,IACT8H,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS3D,IAGjC8D,EAGT,SAASgC,KACP,IAAIhC,EAUJ,OARI7D,EAAQ2F,KAAK/J,EAAMgK,OAAOrD,MAC5BsB,EAAKjI,EAAMgK,OAAOrD,IAClBA,OAEAsB,EAAK7H,EACmB,IAApB8G,IAAyBY,GAASzD,IAGjC4D,EAGT,SAASiC,KACP,IAAIjC,EAUJ,OARI3D,EAAQyF,KAAK/J,EAAMgK,OAAOrD,MAC5BsB,EAAKjI,EAAMgK,OAAOrD,IAClBA,OAEAsB,EAAK7H,EACmB,IAApB8G,IAAyBY,GAASvD,IAGjC0D,EAGT,SAASgB,KACP,IAAIhB,EAAIC,EAAIC,EAAIE,EAAIC,EAAIC,EAUxB,GARAN,EAAKtB,GACiC,KAAlC3G,EAAMd,WAAWyH,KACnBuB,EAAK1D,EACLmC,OAEAuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAASrD,IAEpCyD,IAAO9H,EAAY,CAUrB,GARA+H,EADAD,EAAKvB,GAEDjC,EAAQqF,KAAK/J,EAAMgK,OAAOrD,MAC5B0B,EAAKrI,EAAMgK,OAAOrD,IAClBA,OAEA0B,EAAKjI,EACmB,IAApB8G,IAAyBY,GAASnD,KAEpC0D,IAAOjI,EAAY,CAGrB,IAFAkI,EAAK,GACLC,EAAK0B,KACE1B,IAAOnI,GACZkI,EAAGP,KAAKQ,GACRA,EAAK0B,KAIL9B,EAFEG,IAAOlI,EACTiI,EAAK,CAACA,EAAIC,IAGV3B,GAAcwB,EACT/H,QAGPuG,GAAcwB,EACdA,EAAK/H,EAGL8H,EADEC,IAAO/H,EACJJ,EAAM2I,UAAUT,EAAIvB,IAEpBwB,EAST,OANID,IAAO9H,IACTwG,GAAeqB,EACfC,EAAKtD,GAAQsD,IAEfD,EAAKC,EAKP,SAASgB,KACP,IAAIjB,EAAIC,EAAIC,EAyEZ,OAtEAD,EADAD,EAAKtB,GAELO,KACsC,KAAlClH,EAAMd,WAAWyH,KACnBwB,EAAKpD,GACL4B,OAEAwB,EAAK/H,EACmB,IAApB8G,IAAyBY,GAAS9C,KAEpCmD,IAAO/H,IACL6E,GAAQ8E,KAAK/J,EAAMgK,OAAOrD,MAC5BwB,EAAKnI,EAAMgK,OAAOrD,IAClBA,OAEAwB,EAAK/H,EACmB,IAApB8G,IAAyBY,GAAS5C,MAG1CgC,MAkBIe,GAhBFC,EADEC,IAAO/H,OACJ,GAELuG,GAAcuB,EACT9H,MAEIA,GACLJ,EAAMxB,OAASmI,IACjBwB,EAAKnI,EAAMgK,OAAOrD,IAClBA,OAEAwB,EAAK/H,EACmB,IAApB8G,IAAyBY,GAAS3C,KAEpCgD,IAAO/H,GACTwG,GAAeqB,EACfC,EAAK9C,GAAQ+C,KAGbxB,GAAcsB,EACT7H,KAGPuG,GAAcsB,EACT7H,MAEIA,IACT6H,EAAKtB,GACiC,KAAlC3G,EAAMd,WAAWyH,KACnBuB,EAAKnD,GACL4B,OAEAuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS9C,KAOpCiD,EALAC,IAAO9H,IACT+H,EAgCN,WACE,IAAIF,EAaJ,OAXIhD,GAAQ8E,KAAK/J,EAAMgK,OAAOrD,MAC5BsB,EAAKjI,EAAMgK,OAAOrD,IAClBA,OAEAsB,EAAK7H,EACmB,IAApB8G,IAAyBY,GAAS5C,KAEpC+C,IAAO7H,IACT6H,EAAKkC,MAGAlC,EA9CEmC,MACMhK,GACTwG,GAAeqB,EACfC,EAAK5C,GAAQ6C,KAOfxB,GAAcsB,EACT7H,IAIF6H,EAGT,SAASkC,KACP,IAAIlC,EAUJ,OARsC,KAAlCjI,EAAMd,WAAWyH,KACnBsB,EAAKlD,GACL4B,OAEAsB,EAAK7H,EACmB,IAApB8G,IAAyBY,GAAS9C,KAGjCiD,EAoBT,SAASoC,KACP,IAAIpC,EAAIC,EAAIC,EAAIE,EAAIC,EAAIC,EAAIO,EAAIC,EAsJhC,OApJAd,EAAKtB,GACiC,KAAlC3G,EAAMd,WAAWyH,KACnBuB,EAAKnD,GACL4B,OAEAuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS9C,MAOpCiD,EALAC,IAAO9H,IACT+H,EAAKgC,QACM/J,GACTwG,GAAeqB,EACfC,EAAK5C,GAAQ6C,KAOfxB,GAAcsB,EACT7H,MAEIA,IACLoF,GAAQuE,KAAK/J,EAAMgK,OAAOrD,MAC5BsB,EAAKjI,EAAMgK,OAAOrD,IAClBA,OAEAsB,EAAK7H,EACmB,IAApB8G,IAAyBY,GAASrC,KAEpCwC,IAAO7H,IACT6H,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAOjB,IACnCwC,EAAKxC,GACLiB,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAASnC,KAEpCuC,IAAO9H,IACTwG,GAAeqB,EACfC,EAAKtC,OAEPqC,EAAKC,KACM9H,IACT6H,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAOd,IACnCqC,EAAKrC,GACLc,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAShC,KAEpCoC,IAAO9H,IACTwG,GAAeqB,EACfC,EAAKnC,OAEPkC,EAAKC,KACM9H,IACT6H,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAOX,IACnCkC,EAAKlC,GACLW,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS7B,KAEpCiC,IAAO9H,IACTwG,GAAeqB,EACfC,EAAKhC,OAEP+B,EAAKC,KACM9H,IACT6H,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAOR,IACnC+B,EAAK/B,GACLQ,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAAS1B,KAEpC8B,IAAO9H,IACTwG,GAAeqB,EACfC,EAAK7B,OAEP4B,EAAKC,KACM9H,IACT6H,EAAKtB,GACD3G,EAAMoJ,OAAOzC,GAAa,KAAOL,IACnC4B,EAAK5B,GACLK,IAAe,IAEfuB,EAAK9H,EACmB,IAApB8G,IAAyBY,GAASvB,KAuCpC0B,EArCAC,IAAO9H,GAETiI,EADAF,EAAKxB,IA6BHwB,GAlBME,GATRC,EAAK4B,QACM9J,IACTmI,EAAK2B,QACM9J,IACT0I,EAAKoB,QACM9J,IACT2I,EAAKmB,QACM9J,EACTkI,EAAK,CAACA,EAAIC,EAAIO,EAAIC,IAexBpC,GAAc0B,EACTjI,MAEIA,EACJJ,EAAM2I,UAAUR,EAAIxB,IAEpB0B,KAEIjI,GACTwG,GAAeqB,EACfC,EAAK1B,GAAQ2B,KAGbxB,GAAcsB,EACT7H,KAGPuG,GAAcsB,EACT7H,SASZ6H,EAGT,SAASQ,KACP,IAAIR,EAAIC,EAAIC,EAKZ,GAHAF,EAAKtB,GACLuB,EAAK,IACLC,EAAKkC,QACMjK,EACT,KAAO+H,IAAO/H,GACZ8H,EAAGH,KAAKI,GACRA,EAAKkC,UAGPnC,EAAK9H,EAQP,OANI8H,IAAO9H,IACTwG,GAAeqB,EACfC,EAAK9G,EAAO8G,IAEdD,EAAKC,EAOP,IAFAhI,EAAaM,OAEMJ,GAAcuG,KAAgB3G,EAAMxB,OACrD,OAAO0B,EAMP,MAJIA,IAAeE,GAAcuG,GAAc3G,EAAMxB,QACnDsJ,GA1qCK,CAAErI,KAAM,QAyEiB5B,EAqmC9BoJ,GArmCwCnJ,EAsmCxCkJ,GAAiBhH,EAAMxB,OAASwB,EAAMgK,OAAOhD,IAAkB,KAtmChB7G,EAumC/C6G,GAAiBhH,EAAMxB,OACnB4I,GAAoBJ,GAAgBA,GAAiB,GACrDI,GAAoBJ,GAAgBA,IAxmCnC,IAAIxJ,GACTA,GAAgBI,aAAaC,EAAUC,GACvCD,EACAC,EACAqC,MApZN,SAAS3C,GAAgB8M,EAASzM,EAAUC,EAAOqC,GACjDoK,KAAKD,QAAWA,EAChBC,KAAK1M,SAAWA,EAChB0M,KAAKzM,MAAWA,EAChByM,KAAKpK,SAAWA,EAChBoK,KAAKC,KAAW,cAEuB,mBAA5B/M,MAAMgN,mBACfhN,MAAMgN,kBAAkBF,KAAM/M,IAbhC,SAASE,IAAS6M,KAAKG,YAAcpN,ECJzC,IACQqN,EADJC,GACID,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBI,OAAOC,gBAClB,CAAEC,UAAW,cAAgBxM,OAAS,SAAUoM,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAItD,KAAKsD,EAAOA,EAAEI,eAAe1D,KAAIqD,EAAErD,GAAKsD,EAAEtD,MACpDqD,EAAGC,IAErB,SAAUD,EAAGC,GAEhB,SAASK,IAAOZ,KAAKG,YAAcG,EADnCF,EAAcE,EAAGC,GAEjBD,EAAElN,UAAkB,OAANmN,EAAaC,OAAOK,OAAON,IAAMK,EAAGxN,UAAYmN,EAAEnN,UAAW,IAAIwN,KAGnFE,GAeAA,EAAS1N,UAAU2N,QAAU,SAAUC,GAInC,OAHAhB,KAAKiB,YAAc,GACnBjB,KAAKkB,cAAgB,KACrBlB,KAAKmB,mBAAqB,KACnBnB,KAAKoB,eAAeJ,IAE/BF,EAAS1N,UAAUgO,eAAiB,SAAUJ,GAC1C,IAAIK,EAAQrB,KACZ,IAAMgB,GAAoB,yBAAbA,EAAI9L,KACb,MAAM,IAAIhC,MAAM,sDAEpB,IAAIiD,EAAW6K,EAAI7K,SACf8C,EAAU9C,EACTmL,OAAO,SAAUC,GAClB,MAAmB,uBAAZA,EAAGrM,MAA6C,oBAAZqM,EAAGrM,OAE7CsM,IAAI,SAAUD,GACf,MAAmB,uBAAZA,EAAGrM,KACJmM,EAAMI,mBAAmBF,GACzBF,EAAMK,gBAAgBH,KAEhC,GAAItI,EAAQhF,SAAWkC,EAASlC,OAC5B,MAAM,IAAIf,MAAM,8CAEpB,OAAO+F,GAEX6H,EAAS1N,UAAUqO,mBAAqB,SAAUE,GAI9C,OAAI3B,KAAKkB,eAAiB,cAAc1B,KAAKmC,EAAQ/K,QAG5CoJ,KAAKmB,qBACNnB,KAAKmB,mBAAqB,IAAIS,KAAKC,aAAa7B,KAAK8B,UAElD,IAAIC,EAAmB/B,KAAKkB,cAAc3J,GAAIyI,KAAKkB,cAAc1J,OAAOc,OAAQ0H,KAAKmB,mBAAoBQ,EAAQ/K,QAGrH+K,EAAQ/K,MAAM7B,QAAQ,OAAQ,MAEzC+L,EAAS1N,UAAUsO,gBAAkB,SAAUC,GAC3C,IAAInK,EAASmK,EAAQnK,OAAQD,EAAKoK,EAAQpK,GACtCyK,EAAahC,KAAKgC,WACtB,IAAKxK,EACD,OAAO,IAAIyK,EAAa1K,GAE5B,IAAe2K,EAANlC,KAAmBkC,QAASJ,EAA5B9B,KAAyC8B,QAClD,OAAQtK,EAAOtC,MACX,IAAK,eACD,MAAO,CACHqC,GAAIA,EACJC,OAAQwK,EAAWG,gBAAgBL,EAASI,EAAQ7I,OAAO7B,EAAOQ,QAAQR,QAElF,IAAK,aACD,MAAO,CACHD,GAAIA,EACJC,OAAQwK,EAAWI,kBAAkBN,EAASI,EAAQG,KAAK7K,EAAOQ,QAAQR,QAElF,IAAK,aACD,MAAO,CACHD,GAAIA,EACJC,OAAQwK,EAAWI,kBAAkBN,EAASI,EAAQI,KAAK9K,EAAOQ,QAAQR,QAElF,IAAK,eACD,OAAO,IAAI+K,EAAahL,EAAIC,EAAOc,OAAQ0H,KAAKwC,eAAeb,GAAUK,EAAWS,eAAeX,EAAS,CACxG5M,KAAMsC,EAAOa,QAAU,UAAY,cAE3C,IAAK,eACD,OAAO,IAAIqK,EAAanL,EAAIyI,KAAKwC,eAAeb,IACpD,QACI,MAAM,IAAIzO,MAAM,uDAG5B4N,EAAS1N,UAAUoP,eAAiB,SAAUb,GAC1C,IAAIN,EAAQrB,KACRxI,EAASmK,EAAQnK,OACjB9B,EAAU8B,EAAO9B,QAIrBsK,KAAKiB,YAAYzD,KAAKwC,KAAKkB,eAC3BlB,KAAKkB,cAAgC,iBAAhB1J,EAAOtC,KAA0ByM,EAAU,KAChE,IAAIgB,EAAcjN,EAAQY,OAAO,SAAUC,EAAKqM,GAG5C,OADArM,EAAIqM,EAAO5J,UAAYqI,EAAMD,eAAewB,EAAOhM,OAC5CL,GACR,IAGH,OADAyJ,KAAKkB,cAAgBlB,KAAKiB,YAAY4B,MAC/BF,GAEJ7B,GA1GP,SAASA,EAASgB,EAASI,EAASF,GAChChC,KAAK8B,QAAU,GACf9B,KAAKkC,QAAU,CACX7I,OAAQ,GACRgJ,KAAM,GACNC,KAAM,IAEVtC,KAAKmB,mBAAqB,KAC1BnB,KAAKkB,cAAgB,KACrBlB,KAAKiB,YAAc,GACnBjB,KAAK8B,QAAUA,EACf9B,KAAKkC,QAAUA,EACflC,KAAKgC,WAAaA,EAmGtB,SADAc,EACmBvL,GACfyI,KAAKzI,GAAKA,EAFlB,IAM4CwL,EAAxCd,GACA5B,EAAU4B,EAD8Bc,EAY1CD,GAPEb,EAAa7O,UAAUoE,OAAS,SAAUZ,GACtC,OAAKA,GAA0B,iBAAVA,EAGG,iBAAVA,EAAqBA,EAAQsF,OAAOtF,GAFvC,IAIRqL,GATP,SAASA,IACL,OAAkB,OAAXc,GAAmBA,EAAOC,MAAMhD,KAAMiD,YAAcjD,KAUnE,IAAIuC,GAOAA,EAAanP,UAAU8P,UAAY,SAAUtM,GACzC,IAAIlB,EAAUsK,KAAKtK,QAGnB,OAFaA,EAAQ,IAAMkB,IACvBlB,EAAQsK,KAAKmD,YAAYC,OAAOxM,EAAQoJ,KAAK1H,UAChC5C,EAAQnB,OAEtBgO,GAZP,SAASA,EAAahL,EAAIe,EAAQ5C,EAASyN,GACvCnD,KAAKzI,GAAKA,EACVyI,KAAK1H,OAASA,EACd0H,KAAKtK,QAAUA,EACfsK,KAAKmD,YAAcA,EAU3B,IAAkDJ,EAA9ChB,GACA1B,EAAU0B,EADoCgB,EAgBhDD,GAPEf,EAAmB3O,UAAUoE,OAAS,SAAUZ,GAC5C,IAAIyC,EAAS2G,KAAKqD,aAAa7L,OAAOZ,EAAQoJ,KAAK1H,QACnD,OAAO0H,KAAKsD,OACPvO,QAAQ,cAAe,KAAOsE,GAC9BtE,QAAQ,OAAQ,MAElBgN,GAbP,SAASA,EAAmBxK,EAAIe,EAAQ+K,EAAcC,GAClD,IAAIjC,EAAQ0B,EAAOQ,KAAKvD,KAAMzI,IAAOyI,KAIrC,OAHAqB,EAAM/I,OAASA,EACf+I,EAAMgC,aAAeA,EACrBhC,EAAMiC,OAASA,EACRjC,EAWf,IAAIqB,GAKAA,EAAatP,UAAU8P,UAAY,SAAUtM,GACzC,IAAIlB,EAAUsK,KAAKtK,QACnB,OAAOA,EAAQkB,IAAUlB,EAAQnB,OAE9BmO,GARP,SAASA,EAAanL,EAAI7B,GACtBsK,KAAKzI,GAAKA,EACVyI,KAAKtK,QAAUA,ECnLvB,IACQ0K,EADJC,GACID,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBI,OAAOC,gBAClB,CAAEC,UAAW,cAAgBxM,OAAS,SAAUoM,EAAGC,GAAKD,EAAEI,UAAYH,IACvE,SAAUD,EAAGC,GAAK,IAAK,IAAItD,KAAKsD,EAAOA,EAAEI,eAAe1D,KAAIqD,EAAErD,GAAKsD,EAAEtD,MACpDqD,EAAGC,IAErB,SAAUD,EAAGC,GAEhB,SAASK,IAAOZ,KAAKG,YAAcG,EADnCF,EAAcE,EAAGC,GAEjBD,EAAElN,UAAkB,OAANmN,EAAaC,OAAOK,OAAON,IAAMK,EAAGxN,UAAYmN,EAAEnN,UAAW,IAAIwN,KAGnF4C,EAAsC,WAStC,OARAA,EAAWhD,OAAOiD,QAAU,SAASC,GACjC,IAAK,IAAI5O,EAAGhB,EAAI,EAAG6P,EAAIV,UAAUhP,OAAQH,EAAI6P,EAAG7P,IAE5C,IAAK,IAAImJ,KADTnI,EAAImO,UAAUnP,GACO0M,OAAOpN,UAAUuN,eAAe4C,KAAKzO,EAAGmI,KACzDyG,EAAEzG,GAAKnI,EAAEmI,IAEjB,OAAOyG,IAEKV,MAAMhD,KAAMiD,YAwDhC,SAASW,EAAaC,EAAeC,GACjC,OAAKA,EAGEtD,OAAOuD,KAAKF,GAAevN,OAAO,SAAUC,EAAKyN,GAEpD,OADAzN,EAAIyN,GAdZ,SAAqBC,EAAIC,GACrB,OAAKA,EAGEV,EAAS,GAAKS,GAAM,GAAMC,GAAM,GAAK1D,OAAOuD,KAAKE,GAAI3N,OAAO,SAAUC,EAAKyN,GAE9E,OADAzN,EAAIyN,GAAKR,EAAS,GAAIS,EAAGD,GAAKE,EAAGF,IAAM,IAChCzN,GACR,KALQ0N,EAYEE,CAAYN,EAAcG,GAAIF,EAAQE,IACxCzN,GACRiN,EAAS,GAAIK,IALLA,EAOf,IAA2Cd,EAAvCqB,GAAuCrB,EAQzC7P,MAPEmN,EAAU+D,EAAarB,GAMhBqB,GALP,SAASA,EAAYC,EAAKC,GACtB,IAAIjD,EAAQ0B,EAAOQ,KAAKvD,KAAMqE,IAAQrE,KAEtC,OADAqB,EAAMiD,WAAaA,EACZjD,EAIR,SAASkD,IACZ,MAAO,CACHpC,gBAAiB,WAGb,IAFA,IAAIqC,EACAC,EAAO,GACFC,EAAK,EAAGA,EAAKzB,UAAUhP,OAAQyQ,IACpCD,EAAKC,GAAMzB,UAAUyB,GAEzB,OAAO,KAAMF,EAAK5C,KAAKC,cAAc8C,KAAK3B,MAAMwB,EAAI,MAAC,GAAQ/N,OAAOgO,MAExErC,kBAAmB,WAGf,IAFA,IAAIoC,EACAC,EAAO,GACFC,EAAK,EAAGA,EAAKzB,UAAUhP,OAAQyQ,IACpCD,EAAKC,GAAMzB,UAAUyB,GAEzB,OAAO,KAAMF,EAAK5C,KAAKgD,gBAAgBD,KAAK3B,MAAMwB,EAAI,MAAC,GAAQ/N,OAAOgO,MAE1EhC,eAAgB,WAGZ,IAFA,IAAI+B,EACAC,EAAO,GACFC,EAAK,EAAGA,EAAKzB,UAAUhP,OAAQyQ,IACpCD,EAAKC,GAAMzB,UAAUyB,GAEzB,OAAO,KAAMF,EAAK5C,KAAKiD,aAAaF,KAAK3B,MAAMwB,EAAI,MAAC,GAAQ/N,OAAOgO,WAI3EK,GA4CAA,EAAkB1R,UAAU2R,gBAAkB,WAC1C,MAAO,CAAEC,OAAQhF,KAAKgF,SAE1BF,EAAkB1R,UAAU6R,OAAS,WACjC,OAAOjF,KAAKgB,KAEhB8D,EAAkBI,cAAgB,KAClCJ,EAAkBK,aAAUC,EAI5BN,EAAkB5C,QAAU,CACxB7I,OAAQ,CACJgM,SAAU,CACNrN,MAAO,YAEXsN,QAAS,CACLtN,MAAO,YAGfqK,KAAM,CACFkD,MAAO,CACHC,MAAO,UACPC,IAAK,UACLC,KAAM,WAEVC,OAAQ,CACJH,MAAO,QACPC,IAAK,UACLC,KAAM,WAEVE,KAAM,CACFJ,MAAO,OACPC,IAAK,UACLC,KAAM,WAEVG,KAAM,CACFC,QAAS,OACTN,MAAO,OACPC,IAAK,UACLC,KAAM,YAGdpD,KAAM,CACFiD,MAAO,CACHQ,KAAM,UACNC,OAAQ,WAEZL,OAAQ,CACJI,KAAM,UACNC,OAAQ,UACRC,OAAQ,WAEZL,KAAM,CACFG,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,aAAc,SAElBL,KAAM,CACFE,KAAM,UACNC,OAAQ,UACRC,OAAQ,UACRC,aAAc,WAInBpB,GA9GP,SAASA,EAAkB/E,EAAS+B,EAASqE,EAAiBC,GAC1D,IAAI/E,EAAQrB,KAeZ,QAdgB,IAAZ8B,IAAsBA,EAAUgD,EAAkBI,eACtDlF,KAAKxI,OAAS,SAAU6O,GACpB,IACI,OAxFhB,SAASC,EAAerN,EAASoN,GAE7B,IADA,IAAIE,EAAS,GACJ7B,EAAK,EAAG8B,EAAYvN,EAASyL,EAAK8B,EAAUvS,OAAQyQ,IAAM,CAC/D,IAAI+B,EAAOD,EAAU9B,GAErB,GAAoB,iBAAT+B,EAAX,CAIA,IAAIlP,EAAKkP,EAAKlP,GAEd,KAAM8O,GAAU9O,KAAM8O,GAClB,MAAM,IAAIjC,EAAY,iCAAmC7M,EAAIA,GAEjE,IAAIX,EAAQyP,EAAO9O,GAIQkP,EDiIpB/Q,QChIH6Q,GAAUD,EAAeG,EAAKvD,UAAUtM,GAAQyP,GAGhDE,GAAUE,EAAKjP,OAAOZ,QAhBtB2P,GAAUE,EAmBlB,OAAOF,EA+DYD,CAAejF,EAAMpI,QAASoN,GAEzC,MAAOK,GACH,MAAIA,EAAEpC,WACI,IAAIpR,MAAM,qCAAuCwT,EAAEpC,WAAa,qCAAuCjD,EAAMtB,QAAU,KAGvH2G,IAIK,iBAAZ3G,EAAsB,CAC7B,IAAK+E,EAAkBK,QACnB,MAAM,IAAIwB,UAAU,+EAGxB3G,KAAKgB,IAAM8D,EAAkBK,QAAQpF,QAGrCC,KAAKgB,IAAMjB,EAGf,GADAC,KAAKD,QAAUA,GACTC,KAAKgB,KAAyB,yBAAlBhB,KAAKgB,IAAI9L,KACvB,MAAM,IAAIyR,UAAU,kDAIxB,IAAIzE,EAAU0B,EAAakB,EAAkB5C,QAASiE,GAEtDnG,KAAKgF,OApIb,SAAuBlD,GACI,iBAAZA,IACPA,EAAU,CAACA,IAEf,IACI,OAAOF,KAAKC,aAAa+E,mBAAmB9E,EAAS,CAGjD+E,cAAe,aAChB,GAEP,MAAOH,GACH,OAAO5B,EAAkBI,eAwHX4B,CAAchF,GAAW,IACvC,IAAIE,EAAcoE,GAAQA,EAAKpE,YAAeuC,IAI9CvE,KAAK/G,QAAU,IAAI6H,EAASgB,EAASI,EAASF,GAAYjB,QAAQf,KAAKgB,KClK/E8D,EAAkBK,QAAU4B,EAAOvR"}