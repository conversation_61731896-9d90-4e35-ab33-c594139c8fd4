"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ColorPicker = ColorPicker;
var _react = require("react");
var _reactNative = require("react-native");
var _colord = require("colord");
var _names = _interopRequireDefault(require("colord/plugins/names"));
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var _components = require("@wordpress/components");
var _compose = require("@wordpress/compose");
var _icons = require("@wordpress/icons");
var _style = _interopRequireDefault(require("./style.scss"));
var _hsvColorPickerNative = _interopRequireDefault(require("./hsv-color-picker.native.js"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

(0, _colord.extend)([_names.default]);
function ColorPicker({
  shouldEnableBottomSheetScroll,
  shouldEnableBottomSheetMaxHeight,
  isBottomSheetContentScrolling,
  setColor,
  activeColor,
  isGradientColor,
  onNavigationBack,
  onHandleClosingBottomSheet,
  onBottomSheetClosed,
  onHandleHardwareButtonPress,
  bottomLabelText
}) {
  const isIOS = _reactNative.Platform.OS === 'ios';
  const hitSlop = {
    top: 22,
    bottom: 22,
    left: 22,
    right: 22
  };
  const {
    h: initH,
    s: initS,
    v: initV
  } = !isGradientColor && activeColor ? (0, _colord.colord)(activeColor).toHsv() : {
    h: 0,
    s: 50,
    v: 50
  };
  const [hue, setHue] = (0, _element.useState)(initH);
  const [sat, setSaturation] = (0, _element.useState)(initS / 100);
  const [val, setValue] = (0, _element.useState)(initV / 100);
  const [savedColor] = (0, _element.useState)(activeColor);
  const {
    paddingLeft: spacing,
    height: pickerHeight,
    borderRadius
  } = _style.default.picker;
  const {
    height: pickerPointerSize
  } = _style.default.pickerPointer;
  const pickerWidth = _components.BottomSheet.getWidth() - 2 * spacing;
  const applyButtonStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.applyButton, _style.default.applyButtonDark);
  const cancelButtonStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.cancelButton, _style.default.cancelButtonDark);
  const colorTextStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.colorText, _style.default.colorTextDark);
  const selectColorTextStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.selectColorText, _style.default.selectColorTextDark);
  const footerStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.footer, _style.default.footerDark);
  const combineToHex = (h = hue, s = sat, v = val) => (0, _colord.colord)({
    h,
    s: s * 100,
    v: v * 100
  }).toHex();
  const currentColor = combineToHex();
  const updateColor = ({
    hue: h,
    saturation: s,
    value: v
  }) => {
    if (h !== undefined) setHue(h);
    if (s !== undefined) setSaturation(s);
    if (v !== undefined) setValue(v);
    setColor(combineToHex(h, s, v));
  };
  (0, _element.useEffect)(() => {
    shouldEnableBottomSheetMaxHeight(false);
    onHandleClosingBottomSheet(() => {
      if (savedColor) {
        setColor(savedColor);
      }
      if (onBottomSheetClosed) {
        onBottomSheetClosed();
      }
    });
    if (onHandleHardwareButtonPress) {
      onHandleHardwareButtonPress(onButtonPress);
    }
    // TODO: Revisit this to discover if there's a good reason for omitting
    // the hook’s dependencies and running it a single time. Ideally there
    // may be a way to refactor and obviate the disabled lint rule. If not,
    // this comment should be replaced by one that explains the reasoning.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  function onButtonPress(action) {
    onNavigationBack();
    onHandleClosingBottomSheet(null);
    shouldEnableBottomSheetMaxHeight(true);
    setColor(action === 'apply' ? currentColor : savedColor);
    if (onBottomSheetClosed) {
      onBottomSheetClosed();
    }
  }
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_hsvColorPickerNative.default, {
    huePickerHue: hue,
    currentColor: currentColor,
    onHuePickerDragMove: updateColor,
    onHuePickerPress: !isBottomSheetContentScrolling && updateColor,
    satValPickerHue: hue,
    satValPickerSaturation: sat,
    satValPickerValue: val,
    onSatValPickerDragMove: updateColor,
    onSatValPickerPress: !isBottomSheetContentScrolling && updateColor,
    onSatValPickerDragStart: () => {
      shouldEnableBottomSheetScroll(false);
    },
    onSatValPickerDragEnd: () => shouldEnableBottomSheetScroll(true),
    onHuePickerDragStart: () => shouldEnableBottomSheetScroll(false),
    onHuePickerDragEnd: () => shouldEnableBottomSheetScroll(true),
    huePickerBarWidth: pickerWidth,
    huePickerBarHeight: pickerPointerSize / 2,
    satValPickerSize: {
      width: pickerWidth,
      height: pickerHeight
    },
    satValPickerSliderSize: pickerPointerSize * 2,
    satValPickerBorderRadius: borderRadius,
    huePickerBorderRadius: borderRadius
  }), (0, _react.createElement)(_reactNative.View, {
    style: footerStyle
  }, (0, _react.createElement)(_reactNative.TouchableWithoutFeedback, {
    onPress: () => onButtonPress('cancel'),
    hitSlop: hitSlop
  }, (0, _react.createElement)(_reactNative.View, null, isIOS ? (0, _react.createElement)(_reactNative.Text, {
    style: cancelButtonStyle
  }, (0, _i18n.__)('Cancel')) : (0, _react.createElement)(_icons.Icon, {
    icon: _icons.close,
    size: 24,
    style: cancelButtonStyle
  }))), bottomLabelText ? (0, _react.createElement)(_reactNative.Text, {
    style: selectColorTextStyle
  }, bottomLabelText) : (0, _react.createElement)(_reactNative.Text, {
    style: colorTextStyle,
    selectable: true
  }, currentColor.toUpperCase()), (0, _react.createElement)(_reactNative.TouchableWithoutFeedback, {
    onPress: () => onButtonPress('apply'),
    hitSlop: hitSlop
  }, (0, _react.createElement)(_reactNative.View, null, isIOS ? (0, _react.createElement)(_reactNative.Text, {
    style: applyButtonStyle
  }, (0, _i18n.__)('Apply')) : (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check,
    size: 24,
    style: applyButtonStyle
  })))));
}
//# sourceMappingURL=index.native.js.map