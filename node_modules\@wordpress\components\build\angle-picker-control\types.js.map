{"version": 3, "names": [], "sources": ["@wordpress/components/src/angle-picker-control/types.ts"], "sourcesContent": ["export type AnglePickerControlProps = {\n\t/**\n\t * Start opting into the new margin-free styles that will become the default\n\t * in a future version.\n\t *\n\t * @default false\n\t */\n\t__nextHasNoMarginBottom?: boolean;\n\t/**\n\t * Label to use for the angle picker.\n\t *\n\t * @default __( 'Angle' )\n\t */\n\tlabel?: string;\n\t/**\n\t * A function that receives the new value of the input.\n\t */\n\tonChange: ( value: number ) => void;\n\t/**\n\t * The current value of the input. The value represents an angle in degrees\n\t * and should be a value between 0 and 360.\n\t */\n\tvalue: number | string;\n};\n\nexport type AngleCircleProps = Pick<\n\tAnglePickerControlProps,\n\t'value' | 'onChange'\n>;\n"], "mappings": ""}