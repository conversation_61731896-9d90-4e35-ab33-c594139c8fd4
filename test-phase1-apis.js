/**
 * Script de test pour les APIs de la Phase 1
 * À exécuter dans la console du navigateur sur la page d'admin WordPress
 */

console.log('🧪 Test des APIs Phase 1 - Sitemap Professionnel');
console.log('==================================================');

// Fonction utilitaire pour tester une API
async function testAPI(name, apiCall) {
    console.log(`\n🔍 Test: ${name}`);
    console.log('📡 Appel en cours...');
    
    try {
        const startTime = Date.now();
        const result = await apiCall();
        const endTime = Date.now();
        
        console.log(`✅ Succès (${endTime - startTime}ms):`, result);
        return { success: true, result, duration: endTime - startTime };
    } catch (error) {
        console.log('❌ Erreur:', error);
        return { success: false, error, duration: 0 };
    }
}

// Tests des APIs Dashboard
async function testDashboardAPIs() {
    console.log('\n📊 === TESTS DASHBOARD ===');
    
    // Test 1: Récupération des statistiques
    await testAPI('Statistiques Dashboard', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Actualisation des statistiques
    await testAPI('Actualisation Statistiques', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/dashboard/refresh', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
}

// Tests des APIs Ping
async function testPingAPIs() {
    console.log('\n📤 === TESTS PING MOTEURS ===');
    
    // Test 1: Ping des moteurs de recherche
    await testAPI('Ping Moteurs de Recherche', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/ping-engines', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Historique des pings
    await testAPI('Historique des Pings', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/ping-history?limit=5', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
}

// Tests des APIs URLs Personnalisées
async function testCustomUrlsAPIs() {
    console.log('\n🔗 === TESTS URLS PERSONNALISÉES ===');
    
    let testUrlId = null;
    
    // Test 1: Récupération des URLs existantes
    await testAPI('Récupération URLs Existantes', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/custom-urls', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Ajout d'une nouvelle URL
    const addResult = await testAPI('Ajout Nouvelle URL', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/custom-urls', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': wpApiSettings.nonce
            },
            body: JSON.stringify({
                loc: 'https://example.com/test-page-' + Date.now(),
                priority: '0.8',
                changefreq: 'weekly',
                lastmod: new Date().toISOString().split('T')[0]
            })
        });
        return await response.json();
    });
    
    // Récupérer l'ID de l'URL créée pour les tests suivants
    if (addResult.success && addResult.result && addResult.result.url) {
        testUrlId = addResult.result.url.id;
        console.log(`📝 URL de test créée avec ID: ${testUrlId}`);
    }
    
    // Test 3: Mise à jour de l'URL (si elle a été créée)
    if (testUrlId) {
        await testAPI('Mise à jour URL', async () => {
            const response = await fetch(`/wp-json/boss-seo/v1/robots-sitemap/custom-urls/${testUrlId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': wpApiSettings.nonce
                },
                body: JSON.stringify({
                    loc: 'https://example.com/test-page-updated-' + Date.now(),
                    priority: '0.9',
                    changefreq: 'daily',
                    lastmod: new Date().toISOString().split('T')[0]
                })
            });
            return await response.json();
        });
    }
    
    // Test 4: Export CSV
    await testAPI('Export CSV', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/custom-urls/export', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 5: Import CSV (test avec données simulées)
    await testAPI('Import CSV', async () => {
        const csvContent = `URL,Priorité,Fréquence,Dernière modification
https://example.com/import-test-1,0.7,monthly,2024-01-15
https://example.com/import-test-2,0.6,weekly,2024-01-14`;
        
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/custom-urls/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': wpApiSettings.nonce
            },
            body: JSON.stringify({
                csv_content: csvContent
            })
        });
        return await response.json();
    });
    
    // Test 6: Suppression de l'URL de test (si elle existe)
    if (testUrlId) {
        await testAPI('Suppression URL de Test', async () => {
            const response = await fetch(`/wp-json/boss-seo/v1/robots-sitemap/custom-urls/${testUrlId}`, {
                method: 'DELETE',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce
                }
            });
            return await response.json();
        });
    }
}

// Test de performance global
async function testPerformance() {
    console.log('\n⚡ === TEST DE PERFORMANCE ===');
    
    const startTime = Date.now();
    
    // Exécuter plusieurs appels en parallèle
    const promises = [
        fetch('/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats', {
            headers: { 'X-WP-Nonce': wpApiSettings.nonce }
        }),
        fetch('/wp-json/boss-seo/v1/robots-sitemap/custom-urls', {
            headers: { 'X-WP-Nonce': wpApiSettings.nonce }
        }),
        fetch('/wp-json/boss-seo/v1/robots-sitemap/ping-history?limit=3', {
            headers: { 'X-WP-Nonce': wpApiSettings.nonce }
        })
    ];
    
    try {
        const results = await Promise.all(promises);
        const endTime = Date.now();
        
        console.log(`✅ Appels parallèles réussis en ${endTime - startTime}ms`);
        console.log('📊 Codes de réponse:', results.map(r => r.status));
        
        return true;
    } catch (error) {
        console.log('❌ Erreur lors des appels parallèles:', error);
        return false;
    }
}

// Fonction principale pour exécuter tous les tests
async function runAllTests() {
    console.log('🚀 Démarrage des tests Phase 1...\n');
    
    const startTime = Date.now();
    
    try {
        await testDashboardAPIs();
        await testPingAPIs();
        await testCustomUrlsAPIs();
        await testPerformance();
        
        const endTime = Date.now();
        
        console.log('\n✅ === RÉSUMÉ DES TESTS ===');
        console.log(`⏱️  Durée totale: ${endTime - startTime}ms`);
        console.log('🎯 Tous les tests de la Phase 1 terminés !');
        console.log('📋 Vérifiez les logs ci-dessus pour les détails');
        
    } catch (error) {
        console.log('\n💥 Erreur lors de l\'exécution des tests:', error);
    }
}

// Fonctions utilitaires disponibles globalement
window.testPhase1 = {
    runAll: runAllTests,
    dashboard: testDashboardAPIs,
    ping: testPingAPIs,
    customUrls: testCustomUrlsAPIs,
    performance: testPerformance
};

console.log('\n🛠️ Fonctions de test disponibles:');
console.log('- testPhase1.runAll() - Exécuter tous les tests');
console.log('- testPhase1.dashboard() - Tester les APIs dashboard');
console.log('- testPhase1.ping() - Tester les APIs ping');
console.log('- testPhase1.customUrls() - Tester les APIs URLs personnalisées');
console.log('- testPhase1.performance() - Test de performance');

console.log('\n🎯 Pour commencer, tapez: testPhase1.runAll()');
