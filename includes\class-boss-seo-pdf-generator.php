<?php

/**
 * Générateur PDF professionnel pour Boss SEO.
 * 
 * Cette classe génère des rapports PDF de qualité professionnelle
 * avec mise en page avancée et graphiques.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Générateur PDF professionnel.
 *
 * Utilise mPDF pour générer des PDFs de qualité avec :
 * - Mise en page professionnelle
 * - Graphiques et tableaux
 * - En-têtes et pieds de page
 * - Table des matières
 * - Styles CSS avancés
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_PDF_Generator {

    /**
     * Instance du générateur de templates.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Report_Template_Generator    $template_generator    Générateur de templates.
     */
    private $template_generator;

    /**
     * Configuration PDF.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $config    Configuration.
     */
    private $config;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    Boss_SEO_Report_Template_Generator    $template_generator    Générateur de templates.
     */
    public function __construct( $template_generator ) {
        $this->template_generator = $template_generator;
        $this->config = array(
            'format' => 'A4',
            'orientation' => 'P', // Portrait
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 20,
            'margin_bottom' => 20,
            'margin_header' => 10,
            'margin_footer' => 10,
            'default_font' => 'Arial',
            'default_font_size' => 11,
            'enable_toc' => true,
            'enable_bookmarks' => true
        );
    }

    /**
     * Génère un PDF à partir des données de rapport.
     *
     * @since    1.2.0
     * @param    array     $report_data    Données du rapport.
     * @param    string    $type           Type de rapport.
     * @param    array     $options        Options de génération.
     * @return   string|WP_Error           Contenu PDF ou erreur.
     */
    public function generate_pdf( $report_data, $type, $options = array() ) {
        try {
            // Vérifier si mPDF est disponible
            if ( ! $this->is_mpdf_available() ) {
                return $this->generate_fallback_pdf( $report_data, $type );
            }

            // Fusionner les options
            $this->config = array_merge( $this->config, $options );

            // Initialiser mPDF
            $mpdf = $this->initialize_mpdf();

            // Générer le contenu HTML optimisé pour PDF
            $html_content = $this->generate_pdf_html( $report_data, $type );

            // Ajouter les métadonnées
            $this->set_pdf_metadata( $mpdf, $report_data );

            // Ajouter l'en-tête et le pied de page
            $this->set_header_footer( $mpdf, $report_data );

            // Générer le PDF
            $mpdf->WriteHTML( $html_content );

            // Ajouter la table des matières si activée
            if ( $this->config['enable_toc'] ) {
                $this->add_table_of_contents( $mpdf );
            }

            // Retourner le contenu PDF
            return $mpdf->Output( '', 'S' ); // 'S' pour retourner en string

        } catch ( Exception $e ) {
            return new WP_Error( 'pdf_generation_error', 'Erreur lors de la génération PDF: ' . $e->getMessage() );
        }
    }

    /**
     * Vérifie si mPDF est disponible.
     *
     * @since    1.2.0
     * @return   bool    Disponibilité de mPDF.
     */
    private function is_mpdf_available() {
        // Pour l'instant, retourner false pour utiliser le fallback
        // Dans une version future, on intégrera mPDF via Composer
        return false;
    }

    /**
     * Génère un PDF de fallback (HTML vers PDF basique).
     *
     * @since    1.2.0
     * @param    array     $report_data    Données du rapport.
     * @param    string    $type           Type de rapport.
     * @return   string                    Contenu HTML optimisé pour impression.
     */
    private function generate_fallback_pdf( $report_data, $type ) {
        // Générer un HTML optimisé pour l'impression/conversion PDF
        $html = $this->template_generator->generate_html_report( $report_data, $type );
        
        // Ajouter des styles spécifiques pour PDF
        $pdf_styles = $this->get_pdf_specific_styles();
        
        // Injecter les styles PDF
        $html = str_replace( '</head>', $pdf_styles . '</head>', $html );
        
        // Ajouter des métadonnées pour la conversion
        $metadata = $this->generate_pdf_metadata( $report_data );
        $html = str_replace( '<head>', '<head>' . $metadata, $html );
        
        return $html;
    }

    /**
     * Initialise mPDF avec la configuration.
     *
     * @since    1.2.0
     * @return   object    Instance mPDF.
     */
    private function initialize_mpdf() {
        // Configuration mPDF (à implémenter quand mPDF sera disponible)
        $config = array(
            'format' => $this->config['format'],
            'orientation' => $this->config['orientation'],
            'margin_left' => $this->config['margin_left'],
            'margin_right' => $this->config['margin_right'],
            'margin_top' => $this->config['margin_top'],
            'margin_bottom' => $this->config['margin_bottom'],
            'margin_header' => $this->config['margin_header'],
            'margin_footer' => $this->config['margin_footer'],
            'default_font' => $this->config['default_font'],
            'default_font_size' => $this->config['default_font_size']
        );

        // return new \Mpdf\Mpdf($config);
        // Pour l'instant, retourner un objet mock
        return new stdClass();
    }

    /**
     * Génère le HTML optimisé pour PDF.
     *
     * @since    1.2.0
     * @param    array     $report_data    Données du rapport.
     * @param    string    $type           Type de rapport.
     * @return   string                    HTML optimisé.
     */
    private function generate_pdf_html( $report_data, $type ) {
        // Générer le HTML de base
        $html = $this->template_generator->generate_html_report( $report_data, $type );
        
        // Optimisations pour PDF
        $html = $this->optimize_html_for_pdf( $html );
        
        return $html;
    }

    /**
     * Optimise le HTML pour la génération PDF.
     *
     * @since    1.2.0
     * @param    string    $html    HTML à optimiser.
     * @return   string             HTML optimisé.
     */
    private function optimize_html_for_pdf( $html ) {
        // Remplacer les styles responsive par des styles fixes
        $html = preg_replace( '/@media[^{]+\{[^{}]*\{[^{}]*\}[^{}]*\}/', '', $html );
        
        // Ajuster les tailles pour PDF
        $html = str_replace( 'max-width: 1200px', 'width: 100%', $html );
        
        // Optimiser les tableaux pour PDF
        $html = str_replace( 'overflow: hidden', 'page-break-inside: avoid', $html );
        
        // Ajouter des sauts de page appropriés
        $html = str_replace( '<section class="section">', '<section class="section" style="page-break-before: auto;">', $html );
        
        return $html;
    }

    /**
     * Définit les métadonnées du PDF.
     *
     * @since    1.2.0
     * @param    object    $mpdf           Instance mPDF.
     * @param    array     $report_data    Données du rapport.
     */
    private function set_pdf_metadata( $mpdf, $report_data ) {
        $metadata = $report_data['metadata'] ?? array();
        
        // Métadonnées PDF (à implémenter avec mPDF)
        /*
        $mpdf->SetTitle( $metadata['title'] ?? 'Rapport Boss SEO' );
        $mpdf->SetAuthor( 'Boss SEO Plugin' );
        $mpdf->SetSubject( $metadata['subtitle'] ?? 'Rapport SEO' );
        $mpdf->SetKeywords( 'SEO, Rapport, Boss SEO, WordPress' );
        $mpdf->SetCreator( 'Boss SEO v1.2.0' );
        */
    }

    /**
     * Définit l'en-tête et le pied de page.
     *
     * @since    1.2.0
     * @param    object    $mpdf           Instance mPDF.
     * @param    array     $report_data    Données du rapport.
     */
    private function set_header_footer( $mpdf, $report_data ) {
        $metadata = $report_data['metadata'] ?? array();
        $site_name = $metadata['site_name'] ?? get_bloginfo( 'name' );
        $report_title = $metadata['title'] ?? 'Rapport Boss SEO';
        
        // En-tête (à implémenter avec mPDF)
        /*
        $header = '<div style="text-align: center; font-size: 10pt; color: #666;">
            <strong>' . esc_html( $site_name ) . '</strong> - ' . esc_html( $report_title ) . '
        </div>';
        $mpdf->SetHTMLHeader( $header );
        
        // Pied de page
        $footer = '<div style="text-align: center; font-size: 9pt; color: #666;">
            Page {PAGENO} sur {nbpg} | Généré le ' . date( 'd/m/Y à H:i' ) . ' par Boss SEO
        </div>';
        $mpdf->SetHTMLFooter( $footer );
        */
    }

    /**
     * Ajoute une table des matières.
     *
     * @since    1.2.0
     * @param    object    $mpdf    Instance mPDF.
     */
    private function add_table_of_contents( $mpdf ) {
        // Table des matières (à implémenter avec mPDF)
        /*
        $mpdf->AddPage();
        $mpdf->WriteHTML( '<h1>Table des Matières</h1>' );
        $mpdf->TOCpagebreak();
        */
    }

    /**
     * Génère les styles CSS spécifiques pour PDF.
     *
     * @since    1.2.0
     * @return   string    Styles CSS.
     */
    private function get_pdf_specific_styles() {
        return '<style type="text/css">
            @page {
                margin: 20mm 15mm;
                @top-center {
                    content: "Rapport Boss SEO";
                    font-size: 10pt;
                    color: #666;
                }
                @bottom-center {
                    content: "Page " counter(page) " sur " counter(pages);
                    font-size: 9pt;
                    color: #666;
                }
            }
            
            body {
                font-family: Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.4;
                color: #333;
            }
            
            .report-container {
                max-width: none;
                box-shadow: none;
                margin: 0;
            }
            
            .report-header {
                background: #0073aa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                page-break-after: avoid;
            }
            
            .section {
                page-break-inside: avoid;
                margin-bottom: 20pt;
            }
            
            .section-title {
                page-break-after: avoid;
                margin-top: 15pt;
                margin-bottom: 10pt;
            }
            
            .metric-grid {
                display: block;
            }
            
            .metric-card {
                display: inline-block;
                width: 48%;
                margin: 1%;
                vertical-align: top;
                page-break-inside: avoid;
            }
            
            .data-table {
                page-break-inside: avoid;
                font-size: 10pt;
            }
            
            .data-table th {
                background: #0073aa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .recommendation-item {
                page-break-inside: avoid;
                margin-bottom: 10pt;
            }
            
            .progress-bar {
                border: 1px solid #ddd;
                height: 15pt;
            }
            
            .progress-fill {
                background: #0073aa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            /* Couleurs pour PDF */
            .metric-excellent { color: #46b450 !important; }
            .metric-good { color: #0073aa !important; }
            .metric-warning { color: #ffb900 !important; }
            .metric-danger { color: #dc3232 !important; }
            
            /* Masquer les éléments non nécessaires en PDF */
            .no-print {
                display: none !important;
            }
        </style>';
    }

    /**
     * Génère les métadonnées HTML pour PDF.
     *
     * @since    1.2.0
     * @param    array    $report_data    Données du rapport.
     * @return   string                   Métadonnées HTML.
     */
    private function generate_pdf_metadata( $report_data ) {
        $metadata = $report_data['metadata'] ?? array();
        
        return '
        <meta name="author" content="Boss SEO Plugin">
        <meta name="subject" content="' . esc_attr( $metadata['subtitle'] ?? 'Rapport SEO' ) . '">
        <meta name="keywords" content="SEO, Rapport, Boss SEO, WordPress">
        <meta name="creator" content="Boss SEO v1.2.0">
        <meta name="producer" content="Boss SEO WordPress Plugin">
        ';
    }

    /**
     * Sauvegarde le PDF dans un fichier.
     *
     * @since    1.2.0
     * @param    string    $pdf_content    Contenu PDF.
     * @param    string    $filename       Nom du fichier.
     * @param    string    $directory      Répertoire de destination.
     * @return   string|WP_Error           Chemin du fichier ou erreur.
     */
    public function save_pdf_to_file( $pdf_content, $filename, $directory ) {
        // Créer le répertoire si nécessaire
        if ( ! file_exists( $directory ) ) {
            wp_mkdir_p( $directory );
        }

        $file_path = trailingslashit( $directory ) . $filename;

        // Sauvegarder le fichier
        $result = file_put_contents( $file_path, $pdf_content );

        if ( $result === false ) {
            return new WP_Error( 'pdf_save_error', 'Impossible de sauvegarder le fichier PDF' );
        }

        return $file_path;
    }

    /**
     * Génère un nom de fichier unique pour le PDF.
     *
     * @since    1.2.0
     * @param    string    $type    Type de rapport.
     * @param    array     $metadata    Métadonnées du rapport.
     * @return   string             Nom de fichier.
     */
    public function generate_filename( $type, $metadata = array() ) {
        $site_name = sanitize_file_name( $metadata['site_name'] ?? get_bloginfo( 'name' ) );
        $date = date( 'Y-m-d_H-i-s' );
        $type = sanitize_file_name( $type );
        
        return "boss-seo-rapport-{$type}-{$site_name}-{$date}.pdf";
    }
}
