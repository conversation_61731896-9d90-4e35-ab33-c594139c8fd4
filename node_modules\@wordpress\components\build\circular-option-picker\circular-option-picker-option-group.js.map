{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "OptionGroup", "className", "options", "additionalProps", "role", "undefined", "_react", "createElement", "classnames"], "sources": ["@wordpress/components/src/circular-option-picker/circular-option-picker-option-group.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * Internal dependencies\n */\nimport type { OptionGroupProps } from './types';\n\nexport function OptionGroup( {\n\tclassName,\n\toptions,\n\t...additionalProps\n}: OptionGroupProps ) {\n\tconst role =\n\t\t'aria-label' in additionalProps || 'aria-labelledby' in additionalProps\n\t\t\t? 'group'\n\t\t\t: undefined;\n\n\treturn (\n\t\t<div\n\t\t\t{ ...additionalProps }\n\t\t\trole={ role }\n\t\t\tclassName={ classnames(\n\t\t\t\t'components-circular-option-picker__option-group',\n\t\t\t\t'components-circular-option-picker__swatches',\n\t\t\t\tclassName\n\t\t\t) }\n\t\t>\n\t\t\t{ options }\n\t\t</div>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAHA;AACA;AACA;;AAQO,SAASC,WAAWA,CAAE;EAC5BC,SAAS;EACTC,OAAO;EACP,GAAGC;AACc,CAAC,EAAG;EACrB,MAAMC,IAAI,GACT,YAAY,IAAID,eAAe,IAAI,iBAAiB,IAAIA,eAAe,GACpE,OAAO,GACPE,SAAS;EAEb,OACC,IAAAC,MAAA,CAAAC,aAAA;IAAA,GACMJ,eAAe;IACpBC,IAAI,EAAGA,IAAM;IACbH,SAAS,EAAG,IAAAO,mBAAU,EACrB,iDAAiD,EACjD,6CAA6C,EAC7CP,SACD;EAAG,GAEDC,OACE,CAAC;AAER"}