{"version": 3, "names": ["_element", "require", "_compose", "KeyboardShortcut", "target", "callback", "shortcut", "bindGlobal", "eventName", "useKeyboardShortcut", "KeyboardShortcuts", "children", "shortcuts", "useRef", "element", "Object", "entries", "map", "_react", "createElement", "key", "Children", "count", "Fragment", "ref", "_default", "exports", "default"], "sources": ["@wordpress/components/src/keyboard-shortcuts/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useRef, Children } from '@wordpress/element';\nimport { useKeyboardShortcut } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport type { KeyboardShortcutProps, KeyboardShortcutsProps } from './types';\n\nfunction KeyboardShortcut( {\n\ttarget,\n\tcallback,\n\tshortcut,\n\tbindGlobal,\n\teventName,\n}: KeyboardShortcutProps ) {\n\tuseKeyboardShortcut( shortcut, callback, {\n\t\tbindGlobal,\n\t\ttarget,\n\t\teventName,\n\t} );\n\n\treturn null;\n}\n\n/**\n * `KeyboardShortcuts` is a component which handles keyboard sequences during the lifetime of the rendering element.\n *\n * When passed children, it will capture key events which occur on or within the children. If no children are passed, events are captured on the document.\n *\n * It uses the [Mousetrap](https://craig.is/killing/mice) library to implement keyboard sequence bindings.\n *\n * ```jsx\n * import { KeyboardShortcuts } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyKeyboardShortcuts = () => {\n * \tconst [ isAllSelected, setIsAllSelected ] = useState( false );\n * \tconst selectAll = () => {\n * \t\tsetIsAllSelected( true );\n * \t};\n *\n * \treturn (\n * \t\t<div>\n * \t\t\t<KeyboardShortcuts\n * \t\t\t\tshortcuts={ {\n * \t\t\t\t\t'mod+a': selectAll,\n * \t\t\t\t} }\n * \t\t\t/>\n * \t\t\t[cmd/ctrl + A] Combination pressed? { isAllSelected ? 'Yes' : 'No' }\n * \t\t</div>\n * \t);\n * };\n * ```\n */\nfunction KeyboardShortcuts( {\n\tchildren,\n\tshortcuts,\n\tbindGlobal,\n\teventName,\n}: KeyboardShortcutsProps ) {\n\tconst target = useRef( null );\n\n\tconst element = Object.entries( shortcuts ?? {} ).map(\n\t\t( [ shortcut, callback ] ) => (\n\t\t\t<KeyboardShortcut\n\t\t\t\tkey={ shortcut }\n\t\t\t\tshortcut={ shortcut }\n\t\t\t\tcallback={ callback }\n\t\t\t\tbindGlobal={ bindGlobal }\n\t\t\t\teventName={ eventName }\n\t\t\t\ttarget={ target }\n\t\t\t/>\n\t\t)\n\t);\n\n\t// Render as non-visual if there are no children pressed. Keyboard\n\t// events will be bound to the document instead.\n\tif ( ! Children.count( children ) ) {\n\t\treturn <>{ element }</>;\n\t}\n\n\treturn (\n\t\t<div ref={ target }>\n\t\t\t{ element }\n\t\t\t{ children }\n\t\t</div>\n\t);\n}\n\nexport default KeyboardShortcuts;\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAJA;AACA;AACA;;AASA,SAASE,gBAAgBA,CAAE;EAC1BC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,UAAU;EACVC;AACsB,CAAC,EAAG;EAC1B,IAAAC,4BAAmB,EAAEH,QAAQ,EAAED,QAAQ,EAAE;IACxCE,UAAU;IACVH,MAAM;IACNI;EACD,CAAE,CAAC;EAEH,OAAO,IAAI;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAAE;EAC3BC,QAAQ;EACRC,SAAS;EACTL,UAAU;EACVC;AACuB,CAAC,EAAG;EAC3B,MAAMJ,MAAM,GAAG,IAAAS,eAAM,EAAE,IAAK,CAAC;EAE7B,MAAMC,OAAO,GAAGC,MAAM,CAACC,OAAO,CAAEJ,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI,CAAC,CAAE,CAAC,CAACK,GAAG,CACpD,CAAE,CAAEX,QAAQ,EAAED,QAAQ,CAAE,KACvB,IAAAa,MAAA,CAAAC,aAAA,EAAChB,gBAAgB;IAChBiB,GAAG,EAAGd,QAAU;IAChBA,QAAQ,EAAGA,QAAU;IACrBD,QAAQ,EAAGA,QAAU;IACrBE,UAAU,EAAGA,UAAY;IACzBC,SAAS,EAAGA,SAAW;IACvBJ,MAAM,EAAGA;EAAQ,CACjB,CAEH,CAAC;;EAED;EACA;EACA,IAAK,CAAEiB,iBAAQ,CAACC,KAAK,CAAEX,QAAS,CAAC,EAAG;IACnC,OAAO,IAAAO,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAK,QAAA,QAAIT,OAAW,CAAC;EACxB;EAEA,OACC,IAAAI,MAAA,CAAAC,aAAA;IAAKK,GAAG,EAAGpB;EAAQ,GAChBU,OAAO,EACPH,QACE,CAAC;AAER;AAAC,IAAAc,QAAA,GAEcf,iBAAiB;AAAAgB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}