{"version": 3, "names": ["_compose", "require", "_element", "_icons", "_i18n", "_styles", "_tooltip", "_interopRequireDefault", "ColorCopyButton", "props", "color", "colorType", "copiedColor", "setCopiedColor", "useState", "copyTimer", "useRef", "copyRef", "useCopyToClipboard", "toHslString", "toRgbString", "toHex", "current", "clearTimeout", "setTimeout", "undefined", "useEffect", "_react", "createElement", "default", "delay", "hideOnClick", "text", "__", "Copy<PERSON><PERSON><PERSON>", "size", "ref", "icon", "copy", "showTooltip", "exports"], "sources": ["@wordpress/components/src/color-picker/color-copy-button.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useCopyToClipboard } from '@wordpress/compose';\nimport { useState, useEffect, useRef } from '@wordpress/element';\nimport { copy } from '@wordpress/icons';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { CopyButton } from './styles';\nimport Tooltip from '../tooltip';\n\nimport type { ColorCopyButtonProps } from './types';\n\nexport const ColorCopyButton = ( props: ColorCopyButtonProps ) => {\n\tconst { color, colorType } = props;\n\tconst [ copiedColor, setCopiedColor ] = useState< string | null >( null );\n\tconst copyTimer = useRef< ReturnType< typeof setTimeout > | undefined >();\n\tconst copyRef = useCopyToClipboard< HTMLDivElement >(\n\t\t() => {\n\t\t\tswitch ( colorType ) {\n\t\t\t\tcase 'hsl': {\n\t\t\t\t\treturn color.toHslString();\n\t\t\t\t}\n\t\t\t\tcase 'rgb': {\n\t\t\t\t\treturn color.toRgbString();\n\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\tcase 'hex': {\n\t\t\t\t\treturn color.toHex();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t() => {\n\t\t\tif ( copyTimer.current ) {\n\t\t\t\tclearTimeout( copyTimer.current );\n\t\t\t}\n\t\t\tsetCopiedColor( color.toHex() );\n\t\t\tcopyTimer.current = setTimeout( () => {\n\t\t\t\tsetCopiedColor( null );\n\t\t\t\tcopyTimer.current = undefined;\n\t\t\t}, 3000 );\n\t\t}\n\t);\n\tuseEffect( () => {\n\t\t// Clear copyTimer on component unmount.\n\t\treturn () => {\n\t\t\tif ( copyTimer.current ) {\n\t\t\t\tclearTimeout( copyTimer.current );\n\t\t\t}\n\t\t};\n\t}, [] );\n\n\treturn (\n\t\t<Tooltip\n\t\t\tdelay={ 0 }\n\t\t\thideOnClick={ false }\n\t\t\ttext={\n\t\t\t\tcopiedColor === color.toHex() ? __( 'Copied!' ) : __( 'Copy' )\n\t\t\t}\n\t\t>\n\t\t\t<CopyButton\n\t\t\t\tsize=\"small\"\n\t\t\t\tref={ copyRef }\n\t\t\t\ticon={ copy }\n\t\t\t\tshowTooltip={ false }\n\t\t\t/>\n\t\t</Tooltip>\n\t);\n};\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAKA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAC,sBAAA,CAAAN,OAAA;AAZA;AACA;AACA;;AAMA;AACA;AACA;;AAMO,MAAMO,eAAe,GAAKC,KAA2B,IAAM;EACjE,MAAM;IAAEC,KAAK;IAAEC;EAAU,CAAC,GAAGF,KAAK;EAClC,MAAM,CAAEG,WAAW,EAAEC,cAAc,CAAE,GAAG,IAAAC,iBAAQ,EAAmB,IAAK,CAAC;EACzE,MAAMC,SAAS,GAAG,IAAAC,eAAM,EAAgD,CAAC;EACzE,MAAMC,OAAO,GAAG,IAAAC,2BAAkB,EACjC,MAAM;IACL,QAASP,SAAS;MACjB,KAAK,KAAK;QAAE;UACX,OAAOD,KAAK,CAACS,WAAW,CAAC,CAAC;QAC3B;MACA,KAAK,KAAK;QAAE;UACX,OAAOT,KAAK,CAACU,WAAW,CAAC,CAAC;QAC3B;MACA;MACA,KAAK,KAAK;QAAE;UACX,OAAOV,KAAK,CAACW,KAAK,CAAC,CAAC;QACrB;IACD;EACD,CAAC,EACD,MAAM;IACL,IAAKN,SAAS,CAACO,OAAO,EAAG;MACxBC,YAAY,CAAER,SAAS,CAACO,OAAQ,CAAC;IAClC;IACAT,cAAc,CAAEH,KAAK,CAACW,KAAK,CAAC,CAAE,CAAC;IAC/BN,SAAS,CAACO,OAAO,GAAGE,UAAU,CAAE,MAAM;MACrCX,cAAc,CAAE,IAAK,CAAC;MACtBE,SAAS,CAACO,OAAO,GAAGG,SAAS;IAC9B,CAAC,EAAE,IAAK,CAAC;EACV,CACD,CAAC;EACD,IAAAC,kBAAS,EAAE,MAAM;IAChB;IACA,OAAO,MAAM;MACZ,IAAKX,SAAS,CAACO,OAAO,EAAG;QACxBC,YAAY,CAAER,SAAS,CAACO,OAAQ,CAAC;MAClC;IACD,CAAC;EACF,CAAC,EAAE,EAAG,CAAC;EAEP,OACC,IAAAK,MAAA,CAAAC,aAAA,EAACtB,QAAA,CAAAuB,OAAO;IACPC,KAAK,EAAG,CAAG;IACXC,WAAW,EAAG,KAAO;IACrBC,IAAI,EACHpB,WAAW,KAAKF,KAAK,CAACW,KAAK,CAAC,CAAC,GAAG,IAAAY,QAAE,EAAE,SAAU,CAAC,GAAG,IAAAA,QAAE,EAAE,MAAO;EAC7D,GAED,IAAAN,MAAA,CAAAC,aAAA,EAACvB,OAAA,CAAA6B,UAAU;IACVC,IAAI,EAAC,OAAO;IACZC,GAAG,EAAGnB,OAAS;IACfoB,IAAI,EAAGC,WAAM;IACbC,WAAW,EAAG;EAAO,CACrB,CACO,CAAC;AAEZ,CAAC;AAACC,OAAA,CAAAhC,eAAA,GAAAA,eAAA"}