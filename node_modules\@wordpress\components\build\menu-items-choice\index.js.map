{"version": 3, "names": ["_icons", "require", "_menuItem", "_interopRequireDefault", "noop", "MenuItemsChoice", "choices", "onHover", "onSelect", "value", "_react", "createElement", "Fragment", "map", "item", "isSelected", "default", "key", "role", "disabled", "icon", "check", "info", "shortcut", "className", "onClick", "onMouseEnter", "onMouseLeave", "label", "_default", "exports"], "sources": ["@wordpress/components/src/menu-items-choice/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { check } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport MenuItem from '../menu-item';\nimport type { MenuItemsChoiceProps } from './types';\n\nconst noop = () => {};\n\n/**\n * `MenuItemsChoice` functions similarly to a set of `MenuItem`s, but allows the user to select one option from a set of multiple choices.\n *\n *\n * ```jsx\n * import { MenuGroup, MenuItemsChoice } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyMenuItemsChoice = () => {\n * \tconst [ mode, setMode ] = useState( 'visual' );\n * \tconst choices = [\n * \t\t{\n * \t\t\tvalue: 'visual',\n * \t\t\tlabel: 'Visual editor',\n * \t\t},\n * \t\t{\n * \t\t\tvalue: 'text',\n * \t\t\tlabel: 'Code editor',\n * \t\t},\n * \t];\n *\n * \treturn (\n * \t\t<MenuGroup label=\"Editor\">\n * \t\t\t<MenuItemsChoice\n * \t\t\t\tchoices={ choices }\n * \t\t\t\tvalue={ mode }\n * \t\t\t\tonSelect={ ( newMode ) => setMode( newMode ) }\n * \t\t\t/>\n * \t\t</MenuGroup>\n * \t);\n * };\n * ```\n */\nfunction MenuItemsChoice( {\n\tchoices = [],\n\tonHover = noop,\n\tonSelect,\n\tvalue,\n}: MenuItemsChoiceProps ) {\n\treturn (\n\t\t<>\n\t\t\t{ choices.map( ( item ) => {\n\t\t\t\tconst isSelected = value === item.value;\n\t\t\t\treturn (\n\t\t\t\t\t<MenuItem\n\t\t\t\t\t\tkey={ item.value }\n\t\t\t\t\t\trole=\"menuitemradio\"\n\t\t\t\t\t\tdisabled={ item.disabled }\n\t\t\t\t\t\ticon={ isSelected ? check : null }\n\t\t\t\t\t\tinfo={ item.info }\n\t\t\t\t\t\tisSelected={ isSelected }\n\t\t\t\t\t\tshortcut={ item.shortcut }\n\t\t\t\t\t\tclassName=\"components-menu-items-choice\"\n\t\t\t\t\t\tonClick={ () => {\n\t\t\t\t\t\t\tif ( ! isSelected ) {\n\t\t\t\t\t\t\t\tonSelect( item.value );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} }\n\t\t\t\t\t\tonMouseEnter={ () => onHover( item.value ) }\n\t\t\t\t\t\tonMouseLeave={ () => onHover( null ) }\n\t\t\t\t\t\taria-label={ item[ 'aria-label' ] }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ item.label }\n\t\t\t\t\t</MenuItem>\n\t\t\t\t);\n\t\t\t} ) }\n\t\t</>\n\t);\n}\n\nexport default MenuItemsChoice;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAIA,MAAMG,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAAE;EACzBC,OAAO,GAAG,EAAE;EACZC,OAAO,GAAGH,IAAI;EACdI,QAAQ;EACRC;AACqB,CAAC,EAAG;EACzB,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGN,OAAO,CAACO,GAAG,CAAIC,IAAI,IAAM;IAC1B,MAAMC,UAAU,GAAGN,KAAK,KAAKK,IAAI,CAACL,KAAK;IACvC,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACT,SAAA,CAAAc,OAAQ;MACRC,GAAG,EAAGH,IAAI,CAACL,KAAO;MAClBS,IAAI,EAAC,eAAe;MACpBC,QAAQ,EAAGL,IAAI,CAACK,QAAU;MAC1BC,IAAI,EAAGL,UAAU,GAAGM,YAAK,GAAG,IAAM;MAClCC,IAAI,EAAGR,IAAI,CAACQ,IAAM;MAClBP,UAAU,EAAGA,UAAY;MACzBQ,QAAQ,EAAGT,IAAI,CAACS,QAAU;MAC1BC,SAAS,EAAC,8BAA8B;MACxCC,OAAO,EAAGA,CAAA,KAAM;QACf,IAAK,CAAEV,UAAU,EAAG;UACnBP,QAAQ,CAAEM,IAAI,CAACL,KAAM,CAAC;QACvB;MACD,CAAG;MACHiB,YAAY,EAAGA,CAAA,KAAMnB,OAAO,CAAEO,IAAI,CAACL,KAAM,CAAG;MAC5CkB,YAAY,EAAGA,CAAA,KAAMpB,OAAO,CAAE,IAAK,CAAG;MACtC,cAAaO,IAAI,CAAE,YAAY;IAAI,GAEjCA,IAAI,CAACc,KACE,CAAC;EAEb,CAAE,CACD,CAAC;AAEL;AAAC,IAAAC,QAAA,GAEcxB,eAAe;AAAAyB,OAAA,CAAAd,OAAA,GAAAa,QAAA"}