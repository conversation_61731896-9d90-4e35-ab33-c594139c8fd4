<?php
/**
 * Script de test pour vérifier les routes API robots-sitemap
 */

echo "🔗 TEST ROUTES API ROBOTS-SITEMAP\n";
echo "==================================\n\n";

// Simuler l'environnement WordPress minimal
if (!function_exists('rest_url')) {
    function rest_url($path) { return 'https://example.com/wp-json/' . $path; }
}
if (!function_exists('register_rest_route')) {
    function register_rest_route($namespace, $route, $args) {
        echo "✓ Route enregistrée: {$namespace}{$route}\n";
        return true;
    }
}
if (!function_exists('current_user_can')) {
    function current_user_can($capability) { return true; }
}
if (!function_exists('get_option')) {
    function get_option($option, $default = false) { 
        return $default; 
    }
}
if (!function_exists('update_option')) {
    function update_option($option, $value) { 
        return true; 
    }
}
if (!function_exists('sanitize_textarea_field')) {
    function sanitize_textarea_field($str) { 
        return strip_tags($str); 
    }
}
if (!function_exists('rest_ensure_response')) {
    function rest_ensure_response($response) { 
        return $response; 
    }
}
if (!function_exists('get_site_url')) {
    function get_site_url() { 
        return 'https://example.com'; 
    }
}

function test_result($test_name, $result, $message) {
    $status = $result ? '✅ PASS' : '❌ FAIL';
    echo "{$status} {$test_name}: {$message}\n";
}

// Charger la classe du contrôleur
if (file_exists('includes/technical/class-boss-robots-sitemap-controller.php')) {
    require_once 'includes/technical/class-boss-robots-sitemap-controller.php';
    test_result('Chargement classe contrôleur', true, 'Boss_Robots_Sitemap_Controller chargée');
} else {
    test_result('Chargement classe contrôleur', false, 'Fichier class-boss-robots-sitemap-controller.php non trouvé');
    exit(1);
}

// Tester l'instanciation
try {
    $controller = new Boss_Robots_Sitemap_Controller('boss-seo', '1.2.0');
    test_result('Instanciation contrôleur', true, 'Contrôleur instancié avec succès');
} catch (Exception $e) {
    test_result('Instanciation contrôleur', false, 'Erreur: ' . $e->getMessage());
    exit(1);
}

// Tester l'enregistrement des routes
echo "\n📡 Test d'enregistrement des routes...\n";
try {
    $controller->register_rest_routes();
    test_result('Enregistrement routes REST', true, 'Toutes les routes enregistrées');
} catch (Exception $e) {
    test_result('Enregistrement routes REST', false, 'Erreur: ' . $e->getMessage());
}

// Tester les méthodes du contrôleur
echo "\n🔧 Test des méthodes du contrôleur...\n";

$methods_to_test = [
    'check_permissions' => 'Vérification des permissions',
    'get_robots_content' => 'Récupération contenu robots.txt',
    'save_robots_content' => 'Sauvegarde contenu robots.txt',
    'validate_robots_content' => 'Validation contenu robots.txt',
    'get_robots_rules' => 'Récupération règles robots.txt',
    'get_advanced_sitemap_settings' => 'Récupération paramètres sitemap',
    'save_advanced_sitemap_settings' => 'Sauvegarde paramètres sitemap',
    'regenerate_advanced_sitemap' => 'Régénération sitemap',
    'get_sitemap_generation_history' => 'Historique génération sitemap',
    'ping_search_engines' => 'Ping moteurs de recherche',
    'get_custom_urls' => 'Récupération URLs personnalisées',
    'save_custom_urls' => 'Sauvegarde URLs personnalisées',
    'get_content_types' => 'Récupération types de contenu',
    'get_taxonomies' => 'Récupération taxonomies'
];

foreach ($methods_to_test as $method => $description) {
    $method_exists = method_exists($controller, $method);
    test_result("Méthode {$method}", $method_exists, $description);
}

// Tester les permissions
echo "\n🔐 Test des permissions...\n";
try {
    $has_permissions = $controller->check_permissions();
    test_result('Vérification permissions', $has_permissions, 'Utilisateur a les permissions manage_options');
} catch (Exception $e) {
    test_result('Vérification permissions', false, 'Erreur: ' . $e->getMessage());
}

// Simuler une requête GET pour robots.txt
echo "\n📄 Test récupération robots.txt...\n";
try {
    // Simuler WP_REST_Request
    $request = new stdClass();
    $request->method = 'GET';
    
    $response = $controller->get_robots_content($request);
    $has_content = isset($response['content']);
    test_result('Récupération robots.txt', $has_content, $has_content ? 'Contenu récupéré' : 'Pas de contenu');
} catch (Exception $e) {
    test_result('Récupération robots.txt', false, 'Erreur: ' . $e->getMessage());
}

// Simuler une requête POST pour sauvegarder robots.txt
echo "\n💾 Test sauvegarde robots.txt...\n";
try {
    // Simuler WP_REST_Request avec contenu
    $request = new stdClass();
    $request->method = 'POST';
    $request->params = ['content' => "User-agent: *\nDisallow: /wp-admin/"];
    
    // Ajouter méthode get_param
    $request->get_param = function($param) use ($request) {
        return isset($request->params[$param]) ? $request->params[$param] : null;
    };
    
    $response = $controller->save_robots_content($request);
    $success = isset($response['success']) && $response['success'];
    test_result('Sauvegarde robots.txt', $success, $success ? 'Contenu sauvegardé' : 'Échec sauvegarde');
} catch (Exception $e) {
    test_result('Sauvegarde robots.txt', false, 'Erreur: ' . $e->getMessage());
}

echo "\n✅ Tests terminés !\n";
echo "==================\n\n";

echo "📋 RÉSUMÉ DES ROUTES API DISPONIBLES:\n";
echo "-------------------------------------\n";
echo "GET  /wp-json/boss-seo/v1/robots-sitemap/robots\n";
echo "POST /wp-json/boss-seo/v1/robots-sitemap/robots\n";
echo "POST /wp-json/boss-seo/v1/robots-sitemap/validate-robots\n";
echo "GET  /wp-json/boss-seo/v1/robots-sitemap/robots-rules\n";
echo "GET  /wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings\n";
echo "POST /wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings\n";
echo "POST /wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate\n";
echo "GET  /wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/history\n";
echo "POST /wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/ping\n";
echo "GET  /wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/custom-urls\n";
echo "POST /wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/custom-urls\n";
echo "GET  /wp-json/boss-seo/v1/robots-sitemap/content-types\n";
echo "GET  /wp-json/boss-seo/v1/robots-sitemap/taxonomies\n";
