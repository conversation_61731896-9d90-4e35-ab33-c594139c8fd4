"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BottomSheetRadioCell;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _compose = require("@wordpress/compose");
var _cell = _interopRequireDefault(require("./cell"));
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function BottomSheetRadioCell(props) {
  const {
    selected,
    ...cellProps
  } = props;
  const selectedIconStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.selectedIcon, _styles.default.selectedIconDark);
  return (0, _react.createElement)(_cell.default, {
    ...cellProps,
    accessibilityRole: 'radio',
    accessibilityState: {
      selected
    },
    accessibilityHint: /* translators: accessibility text (hint for selecting option) */
    (0, _i18n.__)('Double tap to select the option'),
    editable: false,
    value: '',
    showLockIcon: selected
  }, selected && (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check,
    style: selectedIconStyle
  }));
}
//# sourceMappingURL=radio-cell.native.js.map