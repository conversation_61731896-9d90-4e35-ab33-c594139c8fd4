"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _view = require("../../view");
var _context = require("../../context");
var _hook = require("./hook");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BorderBoxControlVisualizer = (props, forwardedRef) => {
  const {
    value,
    ...otherProps
  } = (0, _hook.useBorderBoxControlVisualizer)(props);
  return (0, _react.createElement)(_view.View, {
    ...otherProps,
    ref: forwardedRef
  });
};
const ConnectedBorderBoxControlVisualizer = (0, _context.contextConnect)(BorderBoxControlVisualizer, 'BorderBoxControlVisualizer');
var _default = ConnectedBorderBoxControlVisualizer;
exports.default = _default;
//# sourceMappingURL=component.js.map