!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((t=t||self).IntlMessageFormat={})}(this,function(t){"use strict";var r,e,n=(r=Yt,e=Error,o.prototype=e.prototype,r.prototype=new o,Yt.buildMessage=function(t,r){var e,a={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var r,e="";for(r=0;r<t.parts.length;r++)e+=t.parts[r]instanceof Array?u(t.parts[r][0])+"-"+u(t.parts[r][1]):u(t.parts[r]);return"["+(t.inverted?"^":"")+e+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(t){return"\\x0"+n(t)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(t){return"\\x"+n(t)})}function u(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(t){return"\\x0"+n(t)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(t){return"\\x"+n(t)})}return"Expected "+function(t){var r,e,n,o=new Array(t.length);for(r=0;r<t.length;r++)o[r]=(n=t[r],a[n.type](n));if(o.sort(),0<o.length){for(e=r=1;r<o.length;r++)o[r-1]!==o[r]&&(o[e]=o[r],e++);o.length=e}switch(o.length){case 1:return o[0];case 2:return o[0]+" or "+o[1];default:return o.slice(0,-1).join(", ")+", or "+o[o.length-1]}}(t)+" but "+((e=r)?'"'+o(e)+'"':"end of input")+" found."},{SyntaxError:Yt,parse:function(s,t){t=void 0!==t?t:{};var r,e,n,o,c={},a={start:Zt},u=Zt,i=function(t){return{type:"messageFormatPattern",elements:t,location:jt()}},l=function(t){return t.reduce(function(t,r){return t.concat(r)},[]).join("")},f=function(t){return{type:"messageTextElement",value:t,location:jt()}},p=function(t){return t.join("")},h="{",m=It("{",!1),d=",",g=It(",",!1),y="}",v=It("}",!1),b=function(t,r){return{type:"argumentElement",id:t,format:r&&r[2],location:jt()}},A="number",F=It("number",!1),w="date",x=It("date",!1),_="time",C=It("time",!1),O=function(t,r){return{type:t+"Format",style:r&&r[2],location:jt()}},P="plural",E=It("plural",!1),T=function(t){return{type:t.type,ordinal:!1,offset:t.offset||0,options:t.options,location:jt()}},M="selectordinal",S=It("selectordinal",!1),j=function(t){return{type:t.type,ordinal:!0,offset:t.offset||0,options:t.options,location:jt()}},I="select",N=It("select",!1),k=function(t){return{type:"selectFormat",options:t,location:jt()}},R="=",D=It("=",!1),L=function(t,r){return{type:"optionalFormatPattern",selector:t,value:r,location:jt()}},Z="offset:",U=It("offset:",!1),W=function(t){return t},$=function(t,r){return{type:"pluralFormat",offset:t,options:r,location:jt()}},q=kt("whitespace"),z=/^[ \t\n\r]/,B=Nt([" ","\t","\n","\r"],!1,!1),G=kt("optionalWhitespace"),H=/^[0-9]/,J=Nt([["0","9"]],!1,!1),K=/^[0-9a-f]/i,Q=Nt([["0","9"],["a","f"]],!1,!0),V="0",X=It("0",!1),Y=/^[1-9]/,tt=Nt([["1","9"]],!1,!1),rt=function(t){return parseInt(t,10)},et="'",nt=It("'",!1),ot=/^[ \t\n\r,.+={}#]/,at=Nt([" ","\t","\n","\r",",",".","+","=","{","}","#"],!1,!1),ut={type:"any"},it=function(t){return t},st=function(t){return t},ct=/^[^{}\\\0-\x1F\x7F \t\n\r]/,lt=Nt(["{","}","\\",["\0",""],""," ","\t","\n","\r"],!0,!1),ft="\\\\",pt=It("\\\\",!1),ht=function(){return"\\"},mt="\\#",dt=It("\\#",!1),gt=function(){return"\\#"},yt="\\{",vt=It("\\{",!1),bt=function(){return"{"},At="\\}",Ft=It("\\}",!1),wt=function(){return"}"},xt="\\u",_t=It("\\u",!1),Ct=function(t){return String.fromCharCode(parseInt(t,16))},Ot=0,Pt=0,Et=[{line:1,column:1}],Tt=0,Mt=[],St=0;if("startRule"in t){if(!(t.startRule in a))throw new Error("Can't start parsing from rule \""+t.startRule+'".');u=a[t.startRule]}function jt(){return Dt(Pt,Ot)}function It(t,r){return{type:"literal",text:t,ignoreCase:r}}function Nt(t,r,e){return{type:"class",parts:t,inverted:r,ignoreCase:e}}function kt(t){return{type:"other",description:t}}function Rt(t){var r,e=Et[t];if(e)return e;for(r=t-1;!Et[r];)r--;for(e={line:(e=Et[r]).line,column:e.column};r<t;)10===s.charCodeAt(r)?(e.line++,e.column=1):e.column++,r++;return Et[t]=e}function Dt(t,r){var e=Rt(t),n=Rt(r);return{start:{offset:t,line:e.line,column:e.column},end:{offset:r,line:n.line,column:n.column}}}function Lt(t){Ot<Tt||(Tt<Ot&&(Tt=Ot,Mt=[]),Mt.push(t))}function Zt(){return Ut()}function Ut(){var t,r,e;for(t=Ot,r=[],e=Wt();e!==c;)r.push(e),e=Wt();return r!==c&&(Pt=t,r=i(r)),t=r}function Wt(){var t;return(t=function(){var t,r;return t=Ot,(r=function(){var t,r,e,n,o,a;if(r=[],e=t=Ot,(e=(n=Bt())!==c?(o=Xt())!==c&&(a=Bt())!==c?n=[n,o,a]:(Ot=e,c):(Ot=e,c))!==c)for(;e!==c;)r.push(e),e=Ot,n=Bt(),e=n!==c&&(o=Xt())!==c&&(a=Bt())!==c?n=[n,o,a]:(Ot=e,c);else r=c;return r!==c&&(Pt=t,r=l(r)),(t=r)===c&&(t=Ot,r=zt(),t=r!==c?s.substring(t,Ot):r),t}())!==c&&(Pt=t,r=f(r)),t=r}())===c&&(t=function(){var t,r,e,n,o,a,u;return t=Ot,123===s.charCodeAt(Ot)?(r=h,Ot++):(r=c,0===St&&Lt(m)),t=r!==c?Bt()!==c&&(e=function(){var t,r,e;if((t=Jt())===c){for(t=Ot,r=[],e=Kt();e!==c;)r.push(e),e=Kt();r!==c&&(Pt=t,r=p(r)),t=r}return t}())!==c&&Bt()!==c?(n=Ot,44===s.charCodeAt(Ot)?(o=d,Ot++):(o=c,0===St&&Lt(g)),(n=o!==c&&(a=Bt())!==c&&(u=function(){var t;return(t=function(){var t,r,e,n,o,a;return t=Ot,s.substr(Ot,6)===A?(r=A,Ot+=6):(r=c,0===St&&Lt(F)),r===c&&(s.substr(Ot,4)===w?(r=w,Ot+=4):(r=c,0===St&&Lt(x)),r===c&&(s.substr(Ot,4)===_?(r=_,Ot+=4):(r=c,0===St&&Lt(C)))),t=r!==c?Bt()!==c?(e=Ot,44===s.charCodeAt(Ot)?(n=d,Ot++):(n=c,0===St&&Lt(g)),(e=n!==c&&(o=Bt())!==c&&(a=Xt())!==c?n=[n,o,a]:(Ot=e,c))===c&&(e=null),e!==c?(Pt=t,r=O(r,e)):(Ot=t,c)):(Ot=t,c):(Ot=t,c)}())===c&&(t=function(){var t,r,e,n;return t=Ot,s.substr(Ot,6)===P?(r=P,Ot+=6):(r=c,0===St&&Lt(E)),t=r!==c?Bt()!==c?(44===s.charCodeAt(Ot)?(e=d,Ot++):(e=c,0===St&&Lt(g)),e!==c&&Bt()!==c&&(n=qt())!==c?(Pt=t,r=T(n)):(Ot=t,c)):(Ot=t,c):(Ot=t,c)}())===c&&(t=function(){var t,r,e,n;return t=Ot,s.substr(Ot,13)===M?(r=M,Ot+=13):(r=c,0===St&&Lt(S)),t=r!==c?Bt()!==c?(44===s.charCodeAt(Ot)?(e=d,Ot++):(e=c,0===St&&Lt(g)),e!==c&&Bt()!==c&&(n=qt())!==c?(Pt=t,r=j(n)):(Ot=t,c)):(Ot=t,c):(Ot=t,c)}())===c&&(t=function(){var t,r,e,n,o;if(t=Ot,s.substr(Ot,6)===I?(r=I,Ot+=6):(r=c,0===St&&Lt(N)),r!==c)if(Bt()!==c)if(44===s.charCodeAt(Ot)?(e=d,Ot++):(e=c,0===St&&Lt(g)),e!==c)if(Bt()!==c){if(n=[],(o=$t())!==c)for(;o!==c;)n.push(o),o=$t();else n=c;t=n!==c?(Pt=t,r=k(n)):(Ot=t,c)}else Ot=t,t=c;else Ot=t,t=c;else Ot=t,t=c;else Ot=t,t=c;return t}()),t}())!==c?o=[o,a,u]:(Ot=n,c))===c&&(n=null),n!==c&&(o=Bt())!==c?(125===s.charCodeAt(Ot)?(a=y,Ot++):(a=c,0===St&&Lt(v)),a!==c?(Pt=t,r=b(e,n)):(Ot=t,c)):(Ot=t,c)):(Ot=t,c):(Ot=t,c)}()),t}function $t(){var t,r,e,n,o;return t=Ot,t=Bt()!==c&&(r=function(){var t,r,e,n;return r=t=Ot,61===s.charCodeAt(Ot)?(e=R,Ot++):(e=c,0===St&&Lt(D)),(t=(r=e!==c&&(n=Jt())!==c?e=[e,n]:(Ot=r,c))!==c?s.substring(t,Ot):r)===c&&(t=Xt()),t}())!==c&&Bt()!==c?(123===s.charCodeAt(Ot)?(e=h,Ot++):(e=c,0===St&&Lt(m)),e!==c&&(n=Ut())!==c?(125===s.charCodeAt(Ot)?(o=y,Ot++):(o=c,0===St&&Lt(v)),o!==c?(Pt=t,L(r,n)):(Ot=t,c)):(Ot=t,c)):(Ot=t,c)}function qt(){var t,r,e,n;if(t=Ot,(r=function(){var t,r,e;return t=Ot,s.substr(Ot,7)===Z?(r=Z,Ot+=7):(r=c,0===St&&Lt(U)),t=r!==c&&Bt()!==c&&(e=Jt())!==c?(Pt=t,r=W(e)):(Ot=t,c)}())===c&&(r=null),r!==c)if(Bt()!==c){if(e=[],(n=$t())!==c)for(;n!==c;)e.push(n),n=$t();else e=c;t=e!==c?(Pt=t,r=$(r,e)):(Ot=t,c)}else Ot=t,t=c;else Ot=t,t=c;return t}function zt(){var t,r;if(St++,t=[],z.test(s.charAt(Ot))?(r=s.charAt(Ot),Ot++):(r=c,0===St&&Lt(B)),r!==c)for(;r!==c;)t.push(r),z.test(s.charAt(Ot))?(r=s.charAt(Ot),Ot++):(r=c,0===St&&Lt(B));else t=c;return St--,t===c&&(r=c,0===St&&Lt(q)),t}function Bt(){var t,r,e;for(St++,t=Ot,r=[],e=zt();e!==c;)r.push(e),e=zt();return t=r!==c?s.substring(t,Ot):r,St--,t===c&&(r=c,0===St&&Lt(G)),t}function Gt(){var t;return H.test(s.charAt(Ot))?(t=s.charAt(Ot),Ot++):(t=c,0===St&&Lt(J)),t}function Ht(){var t;return K.test(s.charAt(Ot))?(t=s.charAt(Ot),Ot++):(t=c,0===St&&Lt(Q)),t}function Jt(){var t,r,e,n,o,a;if(t=Ot,48===s.charCodeAt(Ot)?(r=V,Ot++):(r=c,0===St&&Lt(X)),r===c){if(e=r=Ot,Y.test(s.charAt(Ot))?(n=s.charAt(Ot),Ot++):(n=c,0===St&&Lt(tt)),n!==c){for(o=[],a=Gt();a!==c;)o.push(a),a=Gt();e=o!==c?n=[n,o]:(Ot=e,c)}else Ot=e,e=c;r=e!==c?s.substring(r,Ot):e}return r!==c&&(Pt=t,r=rt(r)),t=r}function Kt(){var t,r,e;return r=t=Ot,St++,39===s.charCodeAt(Ot)?(e=et,Ot++):(e=c,0===St&&Lt(nt)),e===c&&(ot.test(s.charAt(Ot))?(e=s.charAt(Ot),Ot++):(e=c,0===St&&Lt(at))),St--,(t=(r=e===c?void 0:(Ot=r,c))!==c?(s.length>Ot?(e=s.charAt(Ot),Ot++):(e=c,0===St&&Lt(ut)),e!==c?(Pt=t,r=it(e)):(Ot=t,c)):(Ot=t,c))===c&&(t=Ot,39===s.charCodeAt(Ot)?(r=et,Ot++):(r=c,0===St&&Lt(nt)),t=r!==c&&(e=function(){var t;return ot.test(s.charAt(Ot))?(t=s.charAt(Ot),Ot++):(t=c,0===St&&Lt(at)),t===c&&(t=Qt()),t}())!==c?(Pt=t,r=st(e)):(Ot=t,c)),t}function Qt(){var t;return 39===s.charCodeAt(Ot)?(t=et,Ot++):(t=c,0===St&&Lt(nt)),t}function Vt(){var t,r,e,n,o,a,u,i;return t=Ot,39===s.charCodeAt(Ot)?(r=et,Ot++):(r=c,0===St&&Lt(nt)),(t=r!==c&&(e=Qt())!==c?(Pt=t,r=st(e)):(Ot=t,c))===c&&(ct.test(s.charAt(Ot))?(t=s.charAt(Ot),Ot++):(t=c,0===St&&Lt(lt)),t===c&&(t=Ot,s.substr(Ot,2)===ft?(r=ft,Ot+=2):(r=c,0===St&&Lt(pt)),r!==c&&(Pt=t,r=ht()),(t=r)===c&&(t=Ot,s.substr(Ot,2)===mt?(r=mt,Ot+=2):(r=c,0===St&&Lt(dt)),r!==c&&(Pt=t,r=gt()),(t=r)===c&&(t=Ot,s.substr(Ot,2)===yt?(r=yt,Ot+=2):(r=c,0===St&&Lt(vt)),r!==c&&(Pt=t,r=bt()),(t=r)===c&&(t=Ot,s.substr(Ot,2)===At?(r=At,Ot+=2):(r=c,0===St&&Lt(Ft)),r!==c&&(Pt=t,r=wt()),(t=r)===c&&(t=Ot,s.substr(Ot,2)===xt?(r=xt,Ot+=2):(r=c,0===St&&Lt(_t)),t=r!==c?(n=e=Ot,(e=(n=(o=Ht())!==c&&(a=Ht())!==c&&(u=Ht())!==c&&(i=Ht())!==c?o=[o,a,u,i]:(Ot=n,c))!==c?s.substring(e,Ot):n)!==c?(Pt=t,r=Ct(e)):(Ot=t,c)):(Ot=t,c))))))),t}function Xt(){var t,r,e;if(t=Ot,r=[],(e=Vt())!==c)for(;e!==c;)r.push(e),e=Vt();else r=c;return r!==c&&(Pt=t,r=p(r)),t=r}if((r=u())!==c&&Ot===s.length)return r;throw r!==c&&Ot<s.length&&Lt({type:"end"}),e=Mt,n=Tt<s.length?s.charAt(Tt):null,o=Tt<s.length?Dt(Tt,Tt+1):Dt(Tt,Tt),new Yt(Yt.buildMessage(e,n),e,n,o)}});function Yt(t,r,e,n){this.message=t,this.expected=r,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,Yt)}function o(){this.constructor=r}var a,u=(a=function(t,r){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(t,r)},function(t,r){function e(){this.constructor=t}a(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}),i=(s.prototype.compile=function(t){return this.pluralStack=[],this.currentPlural=null,this.pluralNumberFormat=null,this.compileMessage(t)},s.prototype.compileMessage=function(t){var r=this;if(!t||"messageFormatPattern"!==t.type)throw new Error('Message AST is not of type: "messageFormatPattern"');var e=t.elements,n=e.filter(function(t){return"messageTextElement"===t.type||"argumentElement"===t.type}).map(function(t){return"messageTextElement"===t.type?r.compileMessageText(t):r.compileArgument(t)});if(n.length!==e.length)throw new Error("Message element does not have a valid type");return n},s.prototype.compileMessageText=function(t){return this.currentPlural&&/(^|[^\\])#/g.test(t.value)?(this.pluralNumberFormat||(this.pluralNumberFormat=new Intl.NumberFormat(this.locales)),new g(this.currentPlural.id,this.currentPlural.format.offset,this.pluralNumberFormat,t.value)):t.value.replace(/\\#/g,"#")},s.prototype.compileArgument=function(t){var r=t.format,e=t.id,n=this.formatters;if(!r)return new f(e);var o=this.formats,a=this.locales;switch(r.type){case"numberFormat":return{id:e,format:n.getNumberFormat(a,o.number[r.style]).format};case"dateFormat":return{id:e,format:n.getDateTimeFormat(a,o.date[r.style]).format};case"timeFormat":return{id:e,format:n.getDateTimeFormat(a,o.time[r.style]).format};case"pluralFormat":return new h(e,r.offset,this.compileOptions(t),n.getPluralRules(a,{type:r.ordinal?"ordinal":"cardinal"}));case"selectFormat":return new v(e,this.compileOptions(t));default:throw new Error("Message element does not have a valid format type")}},s.prototype.compileOptions=function(t){var e=this,r=t.format,n=r.options;this.pluralStack.push(this.currentPlural),this.currentPlural="pluralFormat"===r.type?t:null;var o=n.reduce(function(t,r){return t[r.selector]=e.compileMessage(r.value),t},{});return this.currentPlural=this.pluralStack.pop(),o},s);function s(t,r,e){this.locales=[],this.formats={number:{},date:{},time:{}},this.pluralNumberFormat=null,this.currentPlural=null,this.pluralStack=[],this.locales=t,this.formats=r,this.formatters=e}function c(t){this.id=t}var l,f=(u(p,l=c),p.prototype.format=function(t){return t||"number"==typeof t?"string"==typeof t?t:String(t):""},p);function p(){return null!==l&&l.apply(this,arguments)||this}var h=(m.prototype.getOption=function(t){var r=this.options;return r["="+t]||r[this.pluralRules.select(t-this.offset)]||r.other},m);function m(t,r,e,n){this.id=t,this.offset=r,this.options=e,this.pluralRules=n}var d,g=(u(y,d=c),y.prototype.format=function(t){var r=this.numberFormat.format(t-this.offset);return this.string.replace(/(^|[^\\])#/g,"$1"+r).replace(/\\#/g,"#")},y);function y(t,r,e,n){var o=d.call(this,t)||this;return o.offset=r,o.numberFormat=e,o.string=n,o}var v=(b.prototype.getOption=function(t){var r=this.options;return r[t]||r.other},b);function b(t,r){this.id=t,this.options=r}var A,F=(A=function(t,r){return(A=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(t,r)},function(t,r){function e(){this.constructor=t}A(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}),w=function(){return(w=Object.assign||function(t){for(var r,e=1,n=arguments.length;e<n;e++)for(var o in r=arguments[e])Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o]);return t}).apply(this,arguments)};function x(e,n){return n?Object.keys(e).reduce(function(t,r){return t[r]=function(e,n){return n?w({},e||{},n||{},Object.keys(e).reduce(function(t,r){return t[r]=w({},e[r],n[r]||{}),t},{})):e}(e[r],n[r]),t},w({},e)):e}var _,C=(_=Error,F(O,_),O);function O(t,r){var e=_.call(this,t)||this;return e.variableId=r,e}function P(){return{getNumberFormat:function(){for(var t,r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return new((t=Intl.NumberFormat).bind.apply(t,[void 0].concat(r)))},getDateTimeFormat:function(){for(var t,r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return new((t=Intl.DateTimeFormat).bind.apply(t,[void 0].concat(r)))},getPluralRules:function(){for(var t,r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return new((t=Intl.PluralRules).bind.apply(t,[void 0].concat(r)))}}}var E=(T.prototype.resolvedOptions=function(){return{locale:this.locale}},T.prototype.getAst=function(){return this.ast},T.defaultLocale="en",T.__parse=void 0,T.formats={number:{currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},T);function T(t,r,e,n){var o=this;if(void 0===r&&(r=T.defaultLocale),this.format=function(t){try{return function t(r,e){for(var n="",o=0,a=r;o<a.length;o++){var u=a[o];if("string"!=typeof u){var i=u.id;if(!(e&&i in e))throw new C("A value must be provided for: "+i,i);var s=e[i];u.options?n+=t(u.getOption(s),e):n+=u.format(s)}else n+=u}return n}(o.pattern,t)}catch(t){throw t.variableId?new Error("The intl string context variable '"+t.variableId+"' was not provided to the string '"+o.message+"'"):t}},"string"==typeof t){if(!T.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");this.ast=T.__parse(t)}else this.ast=t;if(this.message=t,!this.ast||"messageFormatPattern"!==this.ast.type)throw new TypeError("A message must be provided as a String or AST.");var a=x(T.formats,e);this.locale=function(t){"string"==typeof t&&(t=[t]);try{return Intl.NumberFormat.supportedLocalesOf(t,{localeMatcher:"best fit"})[0]}catch(t){return E.defaultLocale}}(r||[]);var u=n&&n.formatters||P();this.pattern=new i(r,a,u).compile(this.ast)}E.__parse=n.parse,t.IntlMessageFormat=E,t.createDefaultFormatters=P,t.default=E,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=intl-messageformat.min.js.map
