{"version": 3, "names": ["_context", "require", "_view", "_hook", "_interopRequireDefault", "UnconnectedGrid", "props", "forwardedRef", "gridProps", "useGrid", "_react", "createElement", "View", "ref", "Grid", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/grid/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnect } from '../context';\nimport { View } from '../view';\nimport useGrid from './hook';\nimport type { GridProps } from './types';\n\nfunction UnconnectedGrid(\n\tprops: WordPressComponentProps< GridProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst gridProps = useGrid( props );\n\n\treturn <View { ...gridProps } ref={ forwardedRef } />;\n}\n\n/**\n * `Grid` is a primitive layout component that can arrange content in a grid configuration.\n *\n * ```jsx\n * import {\n * \t__experimentalGrid as Grid,\n * \t__experimentalText as Text\n * } from `@wordpress/components`;\n *\n * function Example() {\n * \treturn (\n * \t\t<Grid columns={ 3 }>\n * \t\t\t<Text>Code</Text>\n * \t\t\t<Text>is</Text>\n * \t\t\t<Text>Poetry</Text>\n * \t\t</Grid>\n * \t);\n * }\n * ```\n */\nexport const Grid = contextConnect( UnconnectedGrid, 'Grid' );\n\nexport default Grid;\n"], "mappings": ";;;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAC,sBAAA,CAAAH,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASI,eAAeA,CACvBC,KAAkD,EAClDC,YAAiC,EAChC;EACD,MAAMC,SAAS,GAAG,IAAAC,aAAO,EAAEH,KAAM,CAAC;EAElC,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACT,KAAA,CAAAU,IAAI;IAAA,GAAMJ,SAAS;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,IAAI,GAAG,IAAAC,uBAAc,EAAEV,eAAe,EAAE,MAAO,CAAC;AAACW,OAAA,CAAAF,IAAA,GAAAA,IAAA;AAAA,IAAAG,QAAA,GAE/CH,IAAI;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}