"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = Cell;
var _react = require("react");
var _v = require("../composite/v2");
var _tooltip = _interopRequireDefault(require("../tooltip"));
var _visuallyHidden = require("../visually-hidden");
var _utils = require("./utils");
var _alignmentMatrixControlStyles = require("./styles/alignment-matrix-control-styles");
/**
 * Internal dependencies
 */

/**
 * Internal dependencies
 */

function Cell({
  id,
  isActive = false,
  value,
  ...props
}) {
  const tooltipText = _utils.ALIGNMENT_LABEL[value];
  return (0, _react.createElement)(_tooltip.default, {
    text: tooltipText
  }, (0, _react.createElement)(_v.CompositeItem, {
    id: id,
    render: (0, _react.createElement)(_alignmentMatrixControlStyles.Cell, {
      ...props,
      role: "gridcell"
    })
  }, (0, _react.createElement)(_visuallyHidden.VisuallyHidden, null, value), (0, _react.createElement)(_alignmentMatrixControlStyles.Point, {
    isActive: isActive,
    role: "presentation"
  })));
}
//# sourceMappingURL=cell.js.map