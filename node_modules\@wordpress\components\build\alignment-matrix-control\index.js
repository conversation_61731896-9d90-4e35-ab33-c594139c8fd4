"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AlignmentMatrixControl = AlignmentMatrixControl;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _i18n = require("@wordpress/i18n");
var _compose = require("@wordpress/compose");
var _cell = _interopRequireDefault(require("./cell"));
var _v = require("../composite/v2");
var _alignmentMatrixControlStyles = require("./styles/alignment-matrix-control-styles");
var _icon = _interopRequireDefault(require("./icon"));
var _utils = require("./utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 *
 * AlignmentMatrixControl components enable adjustments to horizontal and vertical alignments for UI.
 *
 * ```jsx
 * import { __experimentalAlignmentMatrixControl as AlignmentMatrixControl } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const Example = () => {
 * 	const [ alignment, setAlignment ] = useState( 'center center' );
 *
 * 	return (
 * 		<AlignmentMatrixControl
 * 			value={ alignment }
 * 			onChange={ setAlignment }
 * 		/>
 * 	);
 * };
 * ```
 */
function AlignmentMatrixControl({
  className,
  id,
  label = (0, _i18n.__)('Alignment Matrix Control'),
  defaultValue = 'center center',
  value,
  onChange,
  width = 92,
  ...props
}) {
  const baseId = (0, _compose.useInstanceId)(AlignmentMatrixControl, 'alignment-matrix-control', id);
  const compositeStore = (0, _v.useCompositeStore)({
    defaultActiveId: (0, _utils.getItemId)(baseId, defaultValue),
    activeId: (0, _utils.getItemId)(baseId, value),
    setActiveId: nextActiveId => {
      const nextValue = (0, _utils.getItemValue)(baseId, nextActiveId);
      if (nextValue) onChange?.(nextValue);
    },
    rtl: (0, _i18n.isRTL)()
  });
  const activeId = compositeStore.useState('activeId');
  const classes = (0, _classnames.default)('component-alignment-matrix-control', className);
  return (0, _react.createElement)(_v.Composite, {
    store: compositeStore,
    render: (0, _react.createElement)(_alignmentMatrixControlStyles.Root, {
      ...props,
      "aria-label": label,
      className: classes,
      id: baseId,
      role: "grid",
      size: width
    })
  }, _utils.GRID.map((cells, index) => (0, _react.createElement)(_v.CompositeRow, {
    render: (0, _react.createElement)(_alignmentMatrixControlStyles.Row, {
      role: "row"
    }),
    key: index
  }, cells.map(cell => {
    const cellId = (0, _utils.getItemId)(baseId, cell);
    const isActive = cellId === activeId;
    return (0, _react.createElement)(_cell.default, {
      id: cellId,
      isActive: isActive,
      key: cell,
      value: cell
    });
  }))));
}
AlignmentMatrixControl.Icon = _icon.default;
var _default = AlignmentMatrixControl;
exports.default = _default;
//# sourceMappingURL=index.js.map