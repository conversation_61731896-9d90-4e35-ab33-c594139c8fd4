{"version": 3, "names": ["_deprecated", "_interopRequireDefault", "require", "_element", "_context", "_surface", "styles", "_interopRequireWildcard", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useDeprecatedProps", "elevation", "isElevated", "otherProps", "propsToReturn", "computedElevation", "_computedElevation", "deprecated", "since", "alternative", "useCard", "props", "className", "isBorderless", "isRounded", "size", "useContextSystem", "cx", "useCx", "classes", "useMemo", "Card", "boxShadowless", "rounded", "surfaceProps", "useSurface"], "sources": ["@wordpress/components/src/card/card/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport deprecated from '@wordpress/deprecated';\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useSurface } from '../../surface';\nimport * as styles from '../styles';\nimport { useCx } from '../../utils/hooks/use-cx';\nimport type { Props } from '../types';\n\ntype CardProps = WordPressComponentProps< Props, 'div' >;\n\nfunction useDeprecatedProps( {\n\televation,\n\tisElevated,\n\t...otherProps\n}: CardProps ) {\n\tconst propsToReturn: Omit< CardProps, 'isElevated' > = {\n\t\t...otherProps,\n\t};\n\tlet computedElevation = elevation;\n\n\tif ( isElevated ) {\n\t\tdeprecated( 'Card isElevated prop', {\n\t\t\tsince: '5.9',\n\t\t\talternative: 'elevation',\n\t\t} );\n\t\tcomputedElevation ??= 2;\n\t}\n\n\t// The `elevation` prop should only be passed when it's not `undefined`,\n\t// otherwise it will override the value that gets derived from `useContextSystem`.\n\tif ( typeof computedElevation !== 'undefined' ) {\n\t\tpropsToReturn.elevation = computedElevation;\n\t}\n\n\treturn propsToReturn;\n}\n\nexport function useCard( props: CardProps ) {\n\tconst {\n\t\tclassName,\n\t\televation = 0,\n\t\tisBorderless = false,\n\t\tisRounded = true,\n\t\tsize = 'medium',\n\t\t...otherProps\n\t} = useContextSystem( useDeprecatedProps( props ), 'Card' );\n\n\tconst cx = useCx();\n\n\tconst classes = useMemo( () => {\n\t\treturn cx(\n\t\t\tstyles.Card,\n\t\t\tisBorderless && styles.boxShadowless,\n\t\t\tisRounded && styles.rounded,\n\t\t\tclassName\n\t\t);\n\t}, [ className, cx, isBorderless, isRounded ] );\n\n\tconst surfaceProps = useSurface( { ...otherProps, className: classes } );\n\n\treturn {\n\t\t...surfaceProps,\n\t\televation,\n\t\tisBorderless,\n\t\tisRounded,\n\t\tsize,\n\t};\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAMA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAC,uBAAA,CAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AAAiD,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAbjD;AACA;AACA;;AAgBA,SAASW,kBAAkBA,CAAE;EAC5BC,SAAS;EACTC,UAAU;EACV,GAAGC;AACO,CAAC,EAAG;EACd,MAAMC,aAA8C,GAAG;IACtD,GAAGD;EACJ,CAAC;EACD,IAAIE,iBAAiB,GAAGJ,SAAS;EAEjC,IAAKC,UAAU,EAAG;IAAA,IAAAI,kBAAA;IACjB,IAAAC,mBAAU,EAAE,sBAAsB,EAAE;MACnCC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE;IACd,CAAE,CAAC;IACH,CAAAH,kBAAA,GAAAD,iBAAiB,cAAAC,kBAAA,cAAAA,kBAAA,GAAjBD,iBAAiB,GAAK,CAAC;EACxB;;EAEA;EACA;EACA,IAAK,OAAOA,iBAAiB,KAAK,WAAW,EAAG;IAC/CD,aAAa,CAACH,SAAS,GAAGI,iBAAiB;EAC5C;EAEA,OAAOD,aAAa;AACrB;AAEO,SAASM,OAAOA,CAAEC,KAAgB,EAAG;EAC3C,MAAM;IACLC,SAAS;IACTX,SAAS,GAAG,CAAC;IACbY,YAAY,GAAG,KAAK;IACpBC,SAAS,GAAG,IAAI;IAChBC,IAAI,GAAG,QAAQ;IACf,GAAGZ;EACJ,CAAC,GAAG,IAAAa,yBAAgB,EAAEhB,kBAAkB,CAAEW,KAAM,CAAC,EAAE,MAAO,CAAC;EAE3D,MAAMM,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,OAAOH,EAAE,CACR1C,MAAM,CAAC8C,IAAI,EACXR,YAAY,IAAItC,MAAM,CAAC+C,aAAa,EACpCR,SAAS,IAAIvC,MAAM,CAACgD,OAAO,EAC3BX,SACD,CAAC;EACF,CAAC,EAAE,CAAEA,SAAS,EAAEK,EAAE,EAAEJ,YAAY,EAAEC,SAAS,CAAG,CAAC;EAE/C,MAAMU,YAAY,GAAG,IAAAC,mBAAU,EAAE;IAAE,GAAGtB,UAAU;IAAES,SAAS,EAAEO;EAAQ,CAAE,CAAC;EAExE,OAAO;IACN,GAAGK,YAAY;IACfvB,SAAS;IACTY,YAAY;IACZC,SAAS;IACTC;EACD,CAAC;AACF"}