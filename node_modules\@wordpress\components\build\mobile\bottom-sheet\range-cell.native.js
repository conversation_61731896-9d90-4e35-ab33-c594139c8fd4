"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _slider = _interopRequireDefault(require("@react-native-community/slider"));
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _cell = _interopRequireDefault(require("./cell"));
var _lockIcon = _interopRequireDefault(require("./lock-icon"));
var _rangeCell = _interopRequireDefault(require("./range-cell.scss"));
var _rangeTextInput = _interopRequireDefault(require("./range-text-input"));
var _utils = require("../utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const isIOS = _reactNative.Platform.OS === 'ios';
class BottomSheetRangeCell extends _element.Component {
  constructor(props) {
    super(props);
    this.onSliderChange = this.onSliderChange.bind(this);
    this.onCompleteSliderChange = this.onCompleteSliderChange.bind(this);
    this.onTextInputChange = this.onTextInputChange.bind(this);
    this.a11yIncrementValue = this.a11yIncrementValue.bind(this);
    this.a11yDecrementValue = this.a11yDecrementValue.bind(this);
    this.a11yUpdateValue = this.a11yUpdateValue.bind(this);
    const {
      value,
      defaultValue,
      minimumValue
    } = props;
    const initialValue = Number(value || defaultValue || minimumValue);
    this.state = {
      inputValue: initialValue,
      sliderValue: initialValue
    };
  }
  componentWillUnmount() {
    clearTimeout(this.timeoutAnnounceValue);
  }
  onSliderChange(initialValue) {
    const {
      decimalNum,
      onChange
    } = this.props;
    initialValue = (0, _utils.toFixed)(initialValue, decimalNum);
    this.setState({
      inputValue: initialValue
    });
    onChange(initialValue);
  }
  onTextInputChange(nextValue) {
    const {
      onChange,
      onComplete
    } = this.props;
    this.setState({
      sliderValue: nextValue
    });
    onChange(nextValue);
    if (onComplete) {
      onComplete(nextValue);
    }
  }
  onCompleteSliderChange(nextValue) {
    const {
      decimalNum,
      onComplete
    } = this.props;
    nextValue = (0, _utils.toFixed)(nextValue, decimalNum);
    if (onComplete) {
      onComplete(nextValue);
    }
  }

  /*
   * Only used with screenreaders like VoiceOver and TalkBack. Increments the
   * value of this setting programmatically.
   */
  a11yIncrementValue() {
    const {
      step = 5,
      maximumValue,
      decimalNum
    } = this.props;
    const {
      inputValue
    } = this.state;
    const newValue = (0, _utils.toFixed)(inputValue + step, decimalNum);
    if (newValue <= maximumValue || maximumValue === undefined) {
      this.a11yUpdateValue(newValue);
    }
  }

  /*
   * Only used with screenreaders like VoiceOver and TalkBack. Decrements the
   * value of this setting programmatically.
   */
  a11yDecrementValue() {
    const {
      step = 5,
      minimumValue,
      decimalNum
    } = this.props;
    const {
      sliderValue
    } = this.state;
    const newValue = (0, _utils.toFixed)(sliderValue - step, decimalNum);
    if (newValue >= minimumValue) {
      this.a11yUpdateValue(newValue);
    }
  }
  a11yUpdateValue(newValue) {
    const {
      onChange,
      onComplete
    } = this.props;
    this.setState({
      sliderValue: newValue,
      inputValue: newValue
    });
    onChange(newValue);
    if (onComplete) {
      onComplete(newValue);
    }
    this.announceValue(newValue);
  }

  /*
   * Only used with screenreaders like VoiceOver and TalkBack.
   */
  announceValue(value) {
    const {
      label,
      unitLabel = ''
    } = this.props;
    if (isIOS) {
      // On Android it triggers the accessibilityLabel with the value change, but
      // on iOS we need to do this manually.
      clearTimeout(this.timeoutAnnounceValue);
      this.timeoutAnnounceValue = setTimeout(() => {
        _reactNative.AccessibilityInfo.announceForAccessibility(`${value} ${unitLabel},  ${label}`);
      }, 300);
    }
  }
  render() {
    const {
      value,
      defaultValue,
      minimumValue = 0,
      maximumValue = 10,
      disabled,
      step = 1,
      preferredColorScheme,
      minimumTrackTintColor = preferredColorScheme === 'light' ? '#00669b' : '#5198d9',
      maximumTrackTintColor = isIOS ? '#e9eff3' : '#909090',
      thumbTintColor = !isIOS && '#00669b',
      preview,
      cellContainerStyle,
      shouldDisplayTextInput = true,
      unitLabel = '',
      settingLabel = 'Value',
      openUnitPicker,
      children,
      decimalNum,
      ...cellProps
    } = this.props;
    const {
      inputValue,
      sliderValue
    } = this.state;
    const getAccessibilityHint = () => {
      return openUnitPicker ? (0, _i18n.__)('double-tap to change unit') : '';
    };
    const getAccessibilityLabel = () => {
      return (0, _i18n.sprintf)( /* translators: accessibility text. Inform about current value. %1$s: Control label %2$s: setting label (example: width), %3$s: Current value. %4$s: value measurement unit (example: pixels) */
      (0, _i18n.__)('%1$s. %2$s is %3$s %4$s.'), cellProps.label, settingLabel, (0, _utils.toFixed)(value, decimalNum), unitLabel);
    };
    const containerStyle = [_rangeCell.default.container, isIOS ? _rangeCell.default.containerIOS : _rangeCell.default.containerAndroid];
    return (0, _react.createElement)(_reactNative.View, {
      accessible: true,
      accessibilityRole: "adjustable",
      accessibilityActions: [{
        name: 'increment'
      }, {
        name: 'decrement'
      }, {
        name: 'activate'
      }],
      onAccessibilityAction: event => {
        switch (event.nativeEvent.actionName) {
          case 'increment':
            this.a11yIncrementValue();
            break;
          case 'decrement':
            this.a11yDecrementValue();
            break;
          case 'activate':
            if (openUnitPicker) {
              openUnitPicker();
            }
            break;
        }
      },
      accessibilityLabel: getAccessibilityLabel(),
      accessibilityHint: getAccessibilityHint()
    }, (0, _react.createElement)(_reactNative.View, {
      importantForAccessibility: "no-hide-descendants"
    }, (0, _react.createElement)(_cell.default, {
      ...cellProps,
      cellContainerStyle: [_rangeCell.default.cellContainerStyles, cellContainerStyle],
      cellRowContainerStyle: containerStyle,
      leftAlign: true,
      editable: false,
      activeOpacity: 1,
      accessible: false,
      valueStyle: _rangeCell.default.valueStyle,
      disabled: disabled,
      showLockIcon: false
    }, (0, _react.createElement)(_reactNative.View, {
      style: containerStyle
    }, preview, (0, _react.createElement)(_slider.default, {
      testID: `Slider ${cellProps.label}`,
      value: sliderValue,
      defaultValue: defaultValue,
      disabled: disabled && !isIOS,
      step: step,
      minimumValue: minimumValue,
      maximumValue: maximumValue,
      minimumTrackTintColor: minimumTrackTintColor,
      maximumTrackTintColor: maximumTrackTintColor,
      thumbTintColor: thumbTintColor,
      onValueChange: this.onSliderChange,
      onSlidingComplete: this.onCompleteSliderChange,
      ref: slider => {
        this.sliderRef = slider;
      },
      style: isIOS ? _rangeCell.default.sliderIOS : _rangeCell.default.sliderAndroid
    }), shouldDisplayTextInput && (0, _react.createElement)(_rangeTextInput.default, {
      label: cellProps.label,
      onChange: this.onTextInputChange,
      defaultValue: `${inputValue}`,
      value: inputValue,
      min: minimumValue,
      max: maximumValue,
      step: step,
      decimalNum: decimalNum
    }, children), disabled && (0, _react.createElement)(_lockIcon.default, null)))));
  }
}
var _default = (0, _compose.withPreferredColorScheme)(BottomSheetRangeCell);
exports.default = _default;
//# sourceMappingURL=range-cell.native.js.map