{"version": 3, "names": ["_react2", "require", "_flex", "_text", "_utils", "_space", "_EMOTION_STRINGIFIED_CSS_ERROR__", "_ref2", "process", "env", "NODE_ENV", "name", "styles", "map", "toString", "rootFocusedStyles", "isFocused", "Root", "_base", "default", "Flex", "target", "label", "exports", "containerDisabledStyles", "disabled", "backgroundColor", "COLORS", "ui", "backgroundDisabled", "background", "css", "_ref", "containerWidthStyles", "__unstableInputWidth", "labelPosition", "flex", "width", "Container", "disabledStyles", "color", "textDisabled", "fontSizeStyles", "inputSize", "size", "sizes", "small", "compact", "fontSize", "fontSizeMobile", "getSizeConfig", "__next40pxDefaultSize", "height", "lineHeight", "minHeight", "paddingLeft", "space", "paddingRight", "sizeStyles", "props", "customPaddings", "paddingInlineStart", "paddingInlineEnd", "dragStyles", "isDragging", "dragCursor", "defaultArrowStyles", "activeDragCursorStyles", "Input", "gray", "BaseLabel", "Text", "baseLabelTypography", "Label", "_react", "createElement", "as", "LabelWrapper", "FlexItem", "backdropFocusedStyles", "borderColor", "borderFocus", "border", "boxShadow", "outline", "outlineOffset", "CONFIG", "controlBoxShadowFocus", "borderDisabled", "borderStyle", "borderWidth", "BackdropUI", "rtl", "Prefix", "Suffix"], "sources": ["@wordpress/components/src/input-control/styles/input-control-styles.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { SerializedStyles } from '@emotion/react';\nimport { css } from '@emotion/react';\nimport styled from '@emotion/styled';\nimport type { CSSProperties, ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { Flex, FlexItem } from '../../flex';\nimport { Text } from '../../text';\nimport { baseLabelTypography, COLORS, CONFIG, rtl } from '../../utils';\nimport type { LabelPosition, Size } from '../types';\nimport { space } from '../../utils/space';\n\ntype ContainerProps = {\n\tdisabled?: boolean;\n\thideLabel?: boolean;\n\t__unstableInputWidth?: CSSProperties[ 'width' ];\n\tlabelPosition?: LabelPosition;\n};\n\ntype RootProps = {\n\tisFocused?: boolean;\n\tlabelPosition?: LabelPosition;\n};\n\nconst rootFocusedStyles = ( { isFocused }: RootProps ) => {\n\tif ( ! isFocused ) return '';\n\n\treturn css( { zIndex: 1 } );\n};\n\nexport const Root = styled( Flex )< RootProps >`\n\tbox-sizing: border-box;\n\tposition: relative;\n\tborder-radius: 2px;\n\tpadding-top: 0;\n\t${ rootFocusedStyles }\n`;\n\nconst containerDisabledStyles = ( { disabled }: ContainerProps ) => {\n\tconst backgroundColor = disabled\n\t\t? COLORS.ui.backgroundDisabled\n\t\t: COLORS.ui.background;\n\n\treturn css( { backgroundColor } );\n};\n\nconst containerWidthStyles = ( {\n\t__unstableInputWidth,\n\tlabelPosition,\n}: ContainerProps ) => {\n\tif ( ! __unstableInputWidth ) return css( { width: '100%' } );\n\n\tif ( labelPosition === 'side' ) return '';\n\n\tif ( labelPosition === 'edge' ) {\n\t\treturn css( {\n\t\t\tflex: `0 0 ${ __unstableInputWidth }`,\n\t\t} );\n\t}\n\n\treturn css( { width: __unstableInputWidth } );\n};\n\nexport const Container = styled.div< ContainerProps >`\n\talign-items: center;\n\tbox-sizing: border-box;\n\tborder-radius: inherit;\n\tdisplay: flex;\n\tflex: 1;\n\tposition: relative;\n\n\t${ containerDisabledStyles }\n\t${ containerWidthStyles }\n`;\n\ntype InputProps = {\n\t__next40pxDefaultSize?: boolean;\n\tdisabled?: boolean;\n\tinputSize?: Size;\n\tisDragging?: boolean;\n\tdragCursor?: CSSProperties[ 'cursor' ];\n\tpaddingInlineStart?: CSSProperties[ 'paddingInlineStart' ];\n\tpaddingInlineEnd?: CSSProperties[ 'paddingInlineEnd' ];\n};\n\nconst disabledStyles = ( { disabled }: InputProps ) => {\n\tif ( ! disabled ) return '';\n\n\treturn css( {\n\t\tcolor: COLORS.ui.textDisabled,\n\t} );\n};\n\nexport const fontSizeStyles = ( { inputSize: size }: InputProps ) => {\n\tconst sizes = {\n\t\tdefault: '13px',\n\t\tsmall: '11px',\n\t\tcompact: '13px',\n\t\t'__unstable-large': '13px',\n\t};\n\n\tconst fontSize = sizes[ size as Size ] || sizes.default;\n\tconst fontSizeMobile = '16px';\n\n\tif ( ! fontSize ) return '';\n\n\treturn css`\n\t\tfont-size: ${ fontSizeMobile };\n\n\t\t@media ( min-width: 600px ) {\n\t\t\tfont-size: ${ fontSize };\n\t\t}\n\t`;\n};\n\nexport const getSizeConfig = ( {\n\tinputSize: size,\n\t__next40pxDefaultSize,\n}: InputProps ) => {\n\t// Paddings may be overridden by the custom paddings props.\n\tconst sizes = {\n\t\tdefault: {\n\t\t\theight: 40,\n\t\t\tlineHeight: 1,\n\t\t\tminHeight: 40,\n\t\t\tpaddingLeft: space( 4 ),\n\t\t\tpaddingRight: space( 4 ),\n\t\t},\n\t\tsmall: {\n\t\t\theight: 24,\n\t\t\tlineHeight: 1,\n\t\t\tminHeight: 24,\n\t\t\tpaddingLeft: space( 2 ),\n\t\t\tpaddingRight: space( 2 ),\n\t\t},\n\t\tcompact: {\n\t\t\theight: 32,\n\t\t\tlineHeight: 1,\n\t\t\tminHeight: 32,\n\t\t\tpaddingLeft: space( 2 ),\n\t\t\tpaddingRight: space( 2 ),\n\t\t},\n\t\t'__unstable-large': {\n\t\t\theight: 40,\n\t\t\tlineHeight: 1,\n\t\t\tminHeight: 40,\n\t\t\tpaddingLeft: space( 4 ),\n\t\t\tpaddingRight: space( 4 ),\n\t\t},\n\t};\n\n\tif ( ! __next40pxDefaultSize ) {\n\t\tsizes.default = sizes.compact;\n\t}\n\n\treturn sizes[ size as Size ] || sizes.default;\n};\n\nconst sizeStyles = ( props: InputProps ) => {\n\treturn css( getSizeConfig( props ) );\n};\n\nconst customPaddings = ( {\n\tpaddingInlineStart,\n\tpaddingInlineEnd,\n}: InputProps ) => {\n\treturn css( { paddingInlineStart, paddingInlineEnd } );\n};\n\nconst dragStyles = ( { isDragging, dragCursor }: InputProps ) => {\n\tlet defaultArrowStyles: SerializedStyles | undefined;\n\tlet activeDragCursorStyles: SerializedStyles | undefined;\n\n\tif ( isDragging ) {\n\t\tdefaultArrowStyles = css`\n\t\t\tcursor: ${ dragCursor };\n\t\t\tuser-select: none;\n\n\t\t\t&::-webkit-outer-spin-button,\n\t\t\t&::-webkit-inner-spin-button {\n\t\t\t\t-webkit-appearance: none !important;\n\t\t\t\tmargin: 0 !important;\n\t\t\t}\n\t\t`;\n\t}\n\n\tif ( isDragging && dragCursor ) {\n\t\tactiveDragCursorStyles = css`\n\t\t\t&:active {\n\t\t\t\tcursor: ${ dragCursor };\n\t\t\t}\n\t\t`;\n\t}\n\n\treturn css`\n\t\t${ defaultArrowStyles }\n\t\t${ activeDragCursorStyles }\n\t`;\n};\n\n// TODO: Resolve need to use &&& to increase specificity\n// https://github.com/WordPress/gutenberg/issues/18483\n\nexport const Input = styled.input< InputProps >`\n\t&&& {\n\t\tbackground-color: transparent;\n\t\tbox-sizing: border-box;\n\t\tborder: none;\n\t\tbox-shadow: none !important;\n\t\tcolor: ${ COLORS.gray[ 900 ] };\n\t\tdisplay: block;\n\t\tfont-family: inherit;\n\t\tmargin: 0;\n\t\toutline: none;\n\t\twidth: 100%;\n\n\t\t${ dragStyles }\n\t\t${ disabledStyles }\n\t\t${ fontSizeStyles }\n\t\t${ sizeStyles }\n\t\t${ customPaddings }\n\n\t\t&::-webkit-input-placeholder {\n\t\t\tline-height: normal;\n\t\t}\n\t}\n`;\n\nconst BaseLabel = styled( Text )< { labelPosition?: LabelPosition } >`\n\t&&& {\n\t\t${ baseLabelTypography };\n\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\tpadding-top: 0;\n\t\tpadding-bottom: 0;\n\t\tmax-width: 100%;\n\t\tz-index: 1;\n\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n`;\n\nexport const Label = (\n\tprops: WordPressComponentProps<\n\t\t{ labelPosition?: LabelPosition; children: ReactNode },\n\t\t'label',\n\t\tfalse\n\t>\n) => <BaseLabel { ...props } as=\"label\" />;\n\nexport const LabelWrapper = styled( FlexItem )`\n\tmax-width: calc( 100% - 10px );\n`;\n\ntype BackdropProps = {\n\tdisabled?: boolean;\n\tisFocused?: boolean;\n};\n\nconst backdropFocusedStyles = ( {\n\tdisabled,\n\tisFocused,\n}: BackdropProps ): SerializedStyles => {\n\tlet borderColor = isFocused ? COLORS.ui.borderFocus : COLORS.ui.border;\n\n\tlet boxShadow;\n\tlet outline;\n\tlet outlineOffset;\n\n\tif ( isFocused ) {\n\t\tboxShadow = CONFIG.controlBoxShadowFocus;\n\t\t// Windows High Contrast mode will show this outline, but not the box-shadow.\n\t\toutline = `2px solid transparent`;\n\t\toutlineOffset = `-2px`;\n\t}\n\n\tif ( disabled ) {\n\t\tborderColor = COLORS.ui.borderDisabled;\n\t}\n\n\treturn css( {\n\t\tboxShadow,\n\t\tborderColor,\n\t\tborderStyle: 'solid',\n\t\tborderWidth: 1,\n\t\toutline,\n\t\toutlineOffset,\n\t} );\n};\n\nexport const BackdropUI = styled.div< BackdropProps >`\n\t&&& {\n\t\tbox-sizing: border-box;\n\t\tborder-radius: inherit;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\tpointer-events: none;\n\t\tposition: absolute;\n\t\tright: 0;\n\t\ttop: 0;\n\n\t\t${ backdropFocusedStyles }\n\t\t${ rtl( { paddingLeft: 2 } ) }\n\t}\n`;\n\nexport const Prefix = styled.span`\n\tbox-sizing: border-box;\n\tdisplay: block;\n`;\n\nexport const Suffix = styled.span`\n\talign-items: center;\n\talign-self: stretch;\n\tbox-sizing: border-box;\n\tdisplay: flex;\n`;\n"], "mappings": ";;;;;;;;;AAIA,IAAAA,OAAA,GAAAC,OAAA;AAQA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAJ,OAAA;AAA0C,SAAAK,iCAAA;AAAA,IAAAC,KAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA;AAc1C,MAAMS,iBAAiB,GAAGA,CAAE;EAAEC;AAAqB,CAAC,KAAM;EACzD,IAAK,CAAEA,SAAS,EAAG,OAAO,EAAE;EAE5B,OAAAT,KAAA;AACD,CAAC;AAEM,MAAMU,IAAI,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,UAAI,EAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,6EAK9BP,iBAAiB,SAAAP,OAAA,CAAAC,GAAA,CAAAC,QAAA,wlUACpB;AAACa,OAAA,CAAAN,IAAA,GAAAA,IAAA;AAEF,MAAMO,uBAAuB,GAAGA,CAAE;EAAEC;AAAyB,CAAC,KAAM;EACnE,MAAMC,eAAe,GAAGD,QAAQ,GAC7BE,aAAM,CAACC,EAAE,CAACC,kBAAkB,GAC5BF,aAAM,CAACC,EAAE,CAACE,UAAU;EAEvB,oBAAO,IAAAC,WAAG,EAAE;IAAEL;EAAgB,CAAC,EAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA,4DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,klUAAC,CAAC;AAClC,CAAC;AAAC,IAAAsB,IAAA,GAAAxB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA;AAEF,MAAM2B,oBAAoB,GAAGA,CAAE;EAC9BC,oBAAoB;EACpBC;AACe,CAAC,KAAM;EACtB,IAAK,CAAED,oBAAoB,EAAG,OAAAF,IAAA;EAE9B,IAAKG,aAAa,KAAK,MAAM,EAAG,OAAO,EAAE;EAEzC,IAAKA,aAAa,KAAK,MAAM,EAAG;IAC/B,oBAAO,IAAAJ,WAAG,EAAE;MACXK,IAAI,EAAG,OAAOF,oBAAsB;IACrC,CAAC,EAAA1B,OAAA,CAAAC,GAAA,CAAAC,QAAA,yDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,klUAAC,CAAC;EACJ;EAEA,oBAAO,IAAAqB,WAAG,EAAE;IAAEM,KAAK,EAAEH;EAAqB,CAAC,EAAA1B,OAAA,CAAAC,GAAA,CAAAC,QAAA,yDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,klUAAC,CAAC;AAC9C,CAAC;AAEM,MAAM4B,SAAS,OAAApB,KAAA,CAAAC,OAAA,SAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,4GAQlBE,uBAAuB,OACvBS,oBAAoB,SAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA,wlUACvB;AAACa,OAAA,CAAAe,SAAA,GAAAA,SAAA;AAYF,MAAMC,cAAc,GAAGA,CAAE;EAAEd;AAAqB,CAAC,KAAM;EACtD,IAAK,CAAEA,QAAQ,EAAG,OAAO,EAAE;EAE3B,oBAAO,IAAAM,WAAG,EAAE;IACXS,KAAK,EAAEb,aAAM,CAACC,EAAE,CAACa;EAClB,CAAC,EAAAjC,OAAA,CAAAC,GAAA,CAAAC,QAAA,mDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,klUAAC,CAAC;AACJ,CAAC;AAEM,MAAMgC,cAAc,GAAGA,CAAE;EAAEC,SAAS,EAAEC;AAAiB,CAAC,KAAM;EACpE,MAAMC,KAAK,GAAG;IACb1B,OAAO,EAAE,MAAM;IACf2B,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,MAAM;IACf,kBAAkB,EAAE;EACrB,CAAC;EAED,MAAMC,QAAQ,GAAGH,KAAK,CAAED,IAAI,CAAU,IAAIC,KAAK,CAAC1B,OAAO;EACvD,MAAM8B,cAAc,GAAG,MAAM;EAE7B,IAAK,CAAED,QAAQ,EAAG,OAAO,EAAE;EAE3B,wBAAOjB,WAAG,gBACKkB,cAAc,6CAGbD,QAAQ,UAAAxC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAGzB,CAAC;AAACa,OAAA,CAAAmB,cAAA,GAAAA,cAAA;AAEK,MAAMQ,aAAa,GAAGA,CAAE;EAC9BP,SAAS,EAAEC,IAAI;EACfO;AACW,CAAC,KAAM;EAClB;EACA,MAAMN,KAAK,GAAG;IACb1B,OAAO,EAAE;MACRiC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,IAAAC,YAAK,EAAE,CAAE,CAAC;MACvBC,YAAY,EAAE,IAAAD,YAAK,EAAE,CAAE;IACxB,CAAC;IACDV,KAAK,EAAE;MACNM,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,IAAAC,YAAK,EAAE,CAAE,CAAC;MACvBC,YAAY,EAAE,IAAAD,YAAK,EAAE,CAAE;IACxB,CAAC;IACDT,OAAO,EAAE;MACRK,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,IAAAC,YAAK,EAAE,CAAE,CAAC;MACvBC,YAAY,EAAE,IAAAD,YAAK,EAAE,CAAE;IACxB,CAAC;IACD,kBAAkB,EAAE;MACnBJ,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,IAAAC,YAAK,EAAE,CAAE,CAAC;MACvBC,YAAY,EAAE,IAAAD,YAAK,EAAE,CAAE;IACxB;EACD,CAAC;EAED,IAAK,CAAEL,qBAAqB,EAAG;IAC9BN,KAAK,CAAC1B,OAAO,GAAG0B,KAAK,CAACE,OAAO;EAC9B;EAEA,OAAOF,KAAK,CAAED,IAAI,CAAU,IAAIC,KAAK,CAAC1B,OAAO;AAC9C,CAAC;AAACI,OAAA,CAAA2B,aAAA,GAAAA,aAAA;AAEF,MAAMQ,UAAU,GAAKC,KAAiB,IAAM;EAC3C,oBAAO,IAAA5B,WAAG,EAAEmB,aAAa,CAAES,KAAM,CAAC,EAAAnD,OAAA,CAAAC,GAAA,CAAAC,QAAA,+CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,klUAAC,CAAC;AACrC,CAAC;AAED,MAAMkD,cAAc,GAAGA,CAAE;EACxBC,kBAAkB;EAClBC;AACW,CAAC,KAAM;EAClB,oBAAO,IAAA/B,WAAG,EAAE;IAAE8B,kBAAkB;IAAEC;EAAiB,CAAC,EAAAtD,OAAA,CAAAC,GAAA,CAAAC,QAAA,mDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,klUAAC,CAAC;AACvD,CAAC;AAED,MAAMqD,UAAU,GAAGA,CAAE;EAAEC,UAAU;EAAEC;AAAuB,CAAC,KAAM;EAChE,IAAIC,kBAAgD;EACpD,IAAIC,sBAAoD;EAExD,IAAKH,UAAU,EAAG;IACjBE,kBAAkB,oBAAGnC,WAAG,aACZkC,UAAU,0IAAAzD,OAAA,CAAAC,GAAA,CAAAC,QAAA,wDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,ulUAQrB;EACF;EAEA,IAAKsD,UAAU,IAAIC,UAAU,EAAG;IAC/BE,sBAAsB,oBAAGpC,WAAG,sBAEfkC,UAAU,UAAAzD,OAAA,CAAAC,GAAA,CAAAC,QAAA,4DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,ulUAEtB;EACF;EAEA,wBAAOqB,WAAG,EACNmC,kBAAkB,OAClBC,sBAAsB,SAAA3D,OAAA,CAAAC,GAAA,CAAAC,QAAA,gDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAE3B,CAAC;;AAED;AACA;;AAEO,MAAM0D,KAAK,OAAAlD,KAAA,CAAAC,OAAA,WAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,0GAMNK,aAAM,CAAC0C,IAAI,CAAE,GAAG,CAAE,0EAOzBN,UAAU,OACVxB,cAAc,OACdG,cAAc,OACdgB,UAAU,OACVE,cAAc,2DAAApD,OAAA,CAAAC,GAAA,CAAAC,QAAA,wlUAMlB;AAACa,OAAA,CAAA6C,KAAA,GAAAA,KAAA;AAEF,MAAME,SAAS,GAAG,kBAAApD,KAAA,CAAAC,OAAA,EAAQoD,UAAI,EAAA/D,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,SAE3BkD,0BAAmB,gKAAAhE,OAAA,CAAAC,GAAA,CAAAC,QAAA,wlUAavB;AAEM,MAAM+D,KAAK,GACjBd,KAIC,IACG,IAAAe,MAAA,CAAAC,aAAA,EAACL,SAAS;EAAA,GAAMX,KAAK;EAAGiB,EAAE,EAAC;AAAO,CAAE,CAAC;AAACrD,OAAA,CAAAkD,KAAA,GAAAA,KAAA;AAEpC,MAAMI,YAAY,GAAG,kBAAA3D,KAAA,CAAAC,OAAA,EAAQ2D,cAAQ,EAAAtE,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,EAE7C;AAACiB,OAAA,CAAAsD,YAAA,GAAAA,YAAA;AAOF,MAAME,qBAAqB,GAAGA,CAAE;EAC/BtD,QAAQ;EACRT;AACc,CAAC,KAAwB;EACvC,IAAIgE,WAAW,GAAGhE,SAAS,GAAGW,aAAM,CAACC,EAAE,CAACqD,WAAW,GAAGtD,aAAM,CAACC,EAAE,CAACsD,MAAM;EAEtE,IAAIC,SAAS;EACb,IAAIC,OAAO;EACX,IAAIC,aAAa;EAEjB,IAAKrE,SAAS,EAAG;IAChBmE,SAAS,GAAGG,aAAM,CAACC,qBAAqB;IACxC;IACAH,OAAO,GAAI,uBAAsB;IACjCC,aAAa,GAAI,MAAK;EACvB;EAEA,IAAK5D,QAAQ,EAAG;IACfuD,WAAW,GAAGrD,aAAM,CAACC,EAAE,CAAC4D,cAAc;EACvC;EAEA,oBAAO,IAAAzD,WAAG,EAAE;IACXoD,SAAS;IACTH,WAAW;IACXS,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,CAAC;IACdN,OAAO;IACPC;EACD,CAAC,EAAA7E,OAAA,CAAAC,GAAA,CAAAC,QAAA,0DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,klUAAC,CAAC;AACJ,CAAC;AAEM,MAAMiF,UAAU,OAAAzE,KAAA,CAAAC,OAAA,SAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,8IAalByD,qBAAqB,OACrB,IAAAa,UAAG,EAAE;EAAErC,WAAW,EAAE;AAAE,CAAE,CAAC,UAAA/C,OAAA,CAAAC,GAAA,CAAAC,QAAA,wlUAE7B;AAACa,OAAA,CAAAoE,UAAA,GAAAA,UAAA;AAEK,MAAME,MAAM,OAAA3E,KAAA,CAAAC,OAAA,UAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,EAGlB;AAACiB,OAAA,CAAAsE,MAAA,GAAAA,MAAA;AAEK,MAAMC,MAAM,OAAA5E,KAAA,CAAAC,OAAA,UAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAW,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,EAKlB;AAACiB,OAAA,CAAAuE,MAAA,GAAAA,MAAA"}