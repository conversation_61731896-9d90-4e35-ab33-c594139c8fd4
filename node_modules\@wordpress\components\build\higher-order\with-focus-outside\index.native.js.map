{"version": 3, "names": ["_reactNative", "require", "_element", "_compose", "_default", "createHigherOrderComponent", "WrappedComponent", "props", "handleFocusOutside", "setHandleFocusOutside", "useState", "bindFocusOutsideHandler", "useCallback", "node", "bind", "undefined", "_react", "createElement", "View", "useFocusOutside", "ref", "exports", "default"], "sources": ["@wordpress/components/src/higher-order/with-focus-outside/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View } from 'react-native';\n/**\n * WordPress dependencies\n */\nimport { useCallback, useState } from '@wordpress/element';\nimport {\n\tcreateHigherOrderComponent,\n\t__experimentalUseFocusOutside as useFocusOutside,\n} from '@wordpress/compose';\n\nexport default createHigherOrderComponent(\n\t( WrappedComponent ) => ( props ) => {\n\t\tconst [ handleFocusOutside, setHandleFocusOutside ] = useState();\n\t\tconst bindFocusOutsideHandler = useCallback(\n\t\t\t( node ) =>\n\t\t\t\tsetHandleFocusOutside( () =>\n\t\t\t\t\tnode?.handleFocusOutside\n\t\t\t\t\t\t? node.handleFocusOutside.bind( node )\n\t\t\t\t\t\t: undefined\n\t\t\t\t),\n\t\t\t[]\n\t\t);\n\n\t\treturn (\n\t\t\t<View { ...useFocusOutside( handleFocusOutside ) }>\n\t\t\t\t<WrappedComponent\n\t\t\t\t\tref={ bindFocusOutsideHandler }\n\t\t\t\t\t{ ...props }\n\t\t\t\t/>\n\t\t\t</View>\n\t\t);\n\t},\n\t'withFocusOutside'\n);\n"], "mappings": ";;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAIA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AARA;AACA;AACA;AAEA;AACA;AACA;AAFA,IAAAG,QAAA,GASe,IAAAC,mCAA0B,EACtCC,gBAAgB,IAAQC,KAAK,IAAM;EACpC,MAAM,CAAEC,kBAAkB,EAAEC,qBAAqB,CAAE,GAAG,IAAAC,iBAAQ,EAAC,CAAC;EAChE,MAAMC,uBAAuB,GAAG,IAAAC,oBAAW,EACxCC,IAAI,IACLJ,qBAAqB,CAAE,MACtBI,IAAI,EAAEL,kBAAkB,GACrBK,IAAI,CAACL,kBAAkB,CAACM,IAAI,CAAED,IAAK,CAAC,GACpCE,SACJ,CAAC,EACF,EACD,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACjB,YAAA,CAAAkB,IAAI;IAAA,GAAM,IAAAC,sCAAe,EAAEX,kBAAmB;EAAC,GAC/C,IAAAQ,MAAA,CAAAC,aAAA,EAACX,gBAAgB;IAChBc,GAAG,EAAGT,uBAAyB;IAAA,GAC1BJ;EAAK,CACV,CACI,CAAC;AAET,CAAC,EACD,kBACD,CAAC;AAAAc,OAAA,CAAAC,OAAA,GAAAlB,QAAA"}