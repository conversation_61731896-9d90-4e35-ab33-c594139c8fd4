"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useDeprecatedProps = useDeprecatedProps;
var _colord = require("colord");
var _memize = _interopRequireDefault(require("memize"));
var _element = require("@wordpress/element");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function isLegacyProps(props) {
  return typeof props.onChangeComplete !== 'undefined' || typeof props.disableAlpha !== 'undefined' || typeof props.color?.hex === 'string';
}
function getColorFromLegacyProps(color) {
  if (color === undefined) return;
  if (typeof color === 'string') return color;
  if (color.hex) return color.hex;
  return undefined;
}
const transformColorStringToLegacyColor = (0, _memize.default)(color => {
  const colordColor = (0, _colord.colord)(color);
  const hex = colordColor.toHex();
  const rgb = colordColor.toRgb();
  const hsv = colordColor.toHsv();
  const hsl = colordColor.toHsl();
  return {
    hex,
    rgb,
    hsv,
    hsl,
    source: 'hex',
    oldHue: hsl.h
  };
});
function useDeprecatedProps(props) {
  const {
    onChangeComplete
  } = props;
  const legacyChangeHandler = (0, _element.useCallback)(color => {
    onChangeComplete(transformColorStringToLegacyColor(color));
  }, [onChangeComplete]);
  if (isLegacyProps(props)) {
    return {
      color: getColorFromLegacyProps(props.color),
      enableAlpha: !props.disableAlpha,
      onChange: legacyChangeHandler
    };
  }
  return {
    ...props,
    color: props.color,
    enableAlpha: props.enableAlpha,
    onChange: props.onChange
  };
}
//# sourceMappingURL=use-deprecated-props.js.map