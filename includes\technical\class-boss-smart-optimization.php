<?php
/**
 * Classe pour l'optimisation intelligente des sitemaps
 *
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 * @since      1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Boss_Smart_Optimization
 *
 * Gère l'optimisation intelligente des priorités et fréquences des sitemaps
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */
class Boss_Smart_Optimization {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Algorithmes de priorités disponibles.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $priority_algorithms    Les algorithmes disponibles.
     */
    private $priority_algorithms;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        $this->priority_algorithms = array(
            'recency' => array(
                'name' => __( 'Récence du contenu', 'boss-seo' ),
                'weight' => 0.3,
                'enabled' => true
            ),
            'popularity' => array(
                'name' => __( 'Popularité (vues)', 'boss-seo' ),
                'weight' => 0.25,
                'enabled' => true
            ),
            'engagement' => array(
                'name' => __( 'Engagement (commentaires)', 'boss-seo' ),
                'weight' => 0.2,
                'enabled' => true
            ),
            'update_frequency' => array(
                'name' => __( 'Fréquence de mise à jour', 'boss-seo' ),
                'weight' => 0.15,
                'enabled' => true
            ),
            'content_quality' => array(
                'name' => __( 'Qualité du contenu', 'boss-seo' ),
                'weight' => 0.1,
                'enabled' => true
            )
        );
    }

    /**
     * Calcule les priorités intelligentes pour tous les contenus.
     *
     * @since    1.2.0
     * @return   array    Les priorités calculées.
     */
    public function calculate_smart_priorities() {
        try {
            $priorities = array();
            
            // Récupérer tous les types de contenu publics
            $post_types = get_post_types( array( 'public' => true ), 'names' );
            
            foreach ( $post_types as $post_type ) {
                if ( $post_type === 'attachment' ) {
                    continue;
                }
                
                $priorities[$post_type] = $this->calculate_post_type_priorities( $post_type );
            }
            
            // Sauvegarder les priorités calculées
            update_option( 'boss_seo_smart_priorities', array(
                'priorities' => $priorities,
                'calculated_at' => current_time( 'mysql' ),
                'algorithm_version' => '1.0'
            ) );
            
            return $priorities;
            
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur calcul priorités intelligentes - ' . $e->getMessage() );
            return array();
        }
    }

    /**
     * Calcule les priorités pour un type de contenu spécifique.
     *
     * @since    1.2.0
     * @param    string    $post_type    Le type de contenu.
     * @return   array                   Les priorités calculées.
     */
    private function calculate_post_type_priorities( $post_type ) {
        $posts = get_posts( array(
            'post_type' => $post_type,
            'post_status' => 'publish',
            'numberposts' => -1,
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => 'boss_seo_exclude_sitemap',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key' => 'boss_seo_exclude_sitemap',
                    'value' => '1',
                    'compare' => '!='
                )
            )
        ) );
        
        $priorities = array();
        
        foreach ( $posts as $post ) {
            $priority = $this->calculate_single_post_priority( $post );
            $changefreq = $this->calculate_optimal_changefreq( $post );
            
            $priorities[$post->ID] = array(
                'post_id' => $post->ID,
                'title' => $post->post_title,
                'url' => get_permalink( $post->ID ),
                'priority' => $priority,
                'changefreq' => $changefreq,
                'calculated_at' => current_time( 'mysql' ),
                'factors' => $this->get_priority_factors( $post )
            );
        }
        
        return $priorities;
    }

    /**
     * Calcule la priorité pour un post spécifique.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   float               La priorité calculée.
     */
    private function calculate_single_post_priority( $post ) {
        $base_priority = 0.5;
        $factors = array();
        
        // 1. Facteur de récence
        if ( $this->priority_algorithms['recency']['enabled'] ) {
            $recency_factor = $this->calculate_recency_factor( $post );
            $factors['recency'] = $recency_factor;
            $base_priority += $recency_factor * $this->priority_algorithms['recency']['weight'];
        }
        
        // 2. Facteur de popularité
        if ( $this->priority_algorithms['popularity']['enabled'] ) {
            $popularity_factor = $this->calculate_popularity_factor( $post );
            $factors['popularity'] = $popularity_factor;
            $base_priority += $popularity_factor * $this->priority_algorithms['popularity']['weight'];
        }
        
        // 3. Facteur d'engagement
        if ( $this->priority_algorithms['engagement']['enabled'] ) {
            $engagement_factor = $this->calculate_engagement_factor( $post );
            $factors['engagement'] = $engagement_factor;
            $base_priority += $engagement_factor * $this->priority_algorithms['engagement']['weight'];
        }
        
        // 4. Facteur de fréquence de mise à jour
        if ( $this->priority_algorithms['update_frequency']['enabled'] ) {
            $update_factor = $this->calculate_update_frequency_factor( $post );
            $factors['update_frequency'] = $update_factor;
            $base_priority += $update_factor * $this->priority_algorithms['update_frequency']['weight'];
        }
        
        // 5. Facteur de qualité du contenu
        if ( $this->priority_algorithms['content_quality']['enabled'] ) {
            $quality_factor = $this->calculate_content_quality_factor( $post );
            $factors['content_quality'] = $quality_factor;
            $base_priority += $quality_factor * $this->priority_algorithms['content_quality']['weight'];
        }
        
        // Normaliser entre 0.1 et 1.0
        $priority = max( 0.1, min( 1.0, $base_priority ) );
        
        return round( $priority, 1 );
    }

    /**
     * Calcule le facteur de récence.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   float               Le facteur de récence.
     */
    private function calculate_recency_factor( $post ) {
        $post_date = strtotime( $post->post_date );
        $current_time = current_time( 'timestamp' );
        $days_old = ( $current_time - $post_date ) / DAY_IN_SECONDS;
        
        if ( $days_old <= 7 ) {
            return 0.4; // Très récent
        } elseif ( $days_old <= 30 ) {
            return 0.3; // Récent
        } elseif ( $days_old <= 90 ) {
            return 0.2; // Moyennement récent
        } elseif ( $days_old <= 365 ) {
            return 0.1; // Ancien
        } else {
            return 0.0; // Très ancien
        }
    }

    /**
     * Calcule le facteur de popularité.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   float               Le facteur de popularité.
     */
    private function calculate_popularity_factor( $post ) {
        // Utiliser les vues si disponibles (plugin de statistiques)
        $views = get_post_meta( $post->ID, 'post_views_count', true );
        
        if ( ! $views ) {
            // Estimation basée sur les commentaires et l'âge
            $comments_count = wp_count_comments( $post->ID );
            $approved_comments = $comments_count->approved;
            
            // Estimation simple : 1 commentaire = ~50 vues
            $views = $approved_comments * 50;
        }
        
        $views = intval( $views );
        
        if ( $views >= 10000 ) {
            return 0.4; // Très populaire
        } elseif ( $views >= 5000 ) {
            return 0.3; // Populaire
        } elseif ( $views >= 1000 ) {
            return 0.2; // Moyennement populaire
        } elseif ( $views >= 100 ) {
            return 0.1; // Peu populaire
        } else {
            return 0.0; // Pas populaire
        }
    }

    /**
     * Calcule le facteur d'engagement.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   float               Le facteur d'engagement.
     */
    private function calculate_engagement_factor( $post ) {
        $comments_count = wp_count_comments( $post->ID );
        $approved_comments = $comments_count->approved;
        
        if ( $approved_comments >= 50 ) {
            return 0.3; // Très engageant
        } elseif ( $approved_comments >= 20 ) {
            return 0.2; // Engageant
        } elseif ( $approved_comments >= 5 ) {
            return 0.1; // Moyennement engageant
        } else {
            return 0.0; // Peu engageant
        }
    }

    /**
     * Calcule le facteur de fréquence de mise à jour.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   float               Le facteur de fréquence.
     */
    private function calculate_update_frequency_factor( $post ) {
        $post_date = strtotime( $post->post_date );
        $modified_date = strtotime( $post->post_modified );
        
        $days_since_update = ( current_time( 'timestamp' ) - $modified_date ) / DAY_IN_SECONDS;
        $total_age = ( current_time( 'timestamp' ) - $post_date ) / DAY_IN_SECONDS;
        
        if ( $total_age < 1 ) {
            return 0.2; // Nouveau contenu
        }
        
        $update_ratio = ( $modified_date - $post_date ) / ( current_time( 'timestamp' ) - $post_date );
        
        if ( $days_since_update <= 7 ) {
            return 0.3; // Récemment mis à jour
        } elseif ( $days_since_update <= 30 ) {
            return 0.2; // Mis à jour récemment
        } elseif ( $update_ratio > 0.1 ) {
            return 0.1; // Mis à jour occasionnellement
        } else {
            return 0.0; // Rarement mis à jour
        }
    }

    /**
     * Calcule le facteur de qualité du contenu.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   float               Le facteur de qualité.
     */
    private function calculate_content_quality_factor( $post ) {
        $content_length = strlen( strip_tags( $post->post_content ) );
        $word_count = str_word_count( strip_tags( $post->post_content ) );
        
        // Vérifier la présence d'images
        $has_images = strpos( $post->post_content, '<img' ) !== false;
        $has_featured_image = has_post_thumbnail( $post->ID );
        
        $quality_score = 0;
        
        // Longueur du contenu
        if ( $word_count >= 2000 ) {
            $quality_score += 0.15; // Contenu long et détaillé
        } elseif ( $word_count >= 1000 ) {
            $quality_score += 0.1; // Contenu de bonne longueur
        } elseif ( $word_count >= 300 ) {
            $quality_score += 0.05; // Contenu correct
        }
        
        // Présence d'images
        if ( $has_featured_image ) {
            $quality_score += 0.05;
        }
        if ( $has_images ) {
            $quality_score += 0.05;
        }
        
        return min( 0.2, $quality_score );
    }

    /**
     * Calcule la fréquence de changement optimale.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   string              La fréquence optimale.
     */
    private function calculate_optimal_changefreq( $post ) {
        $post_date = strtotime( $post->post_date );
        $modified_date = strtotime( $post->post_modified );
        $days_since_update = ( current_time( 'timestamp' ) - $modified_date ) / DAY_IN_SECONDS;
        
        // Analyser le type de contenu
        if ( $post->post_type === 'page' ) {
            return 'monthly'; // Les pages changent moins souvent
        }
        
        // Analyser la fréquence de mise à jour
        if ( $days_since_update <= 1 ) {
            return 'daily';
        } elseif ( $days_since_update <= 7 ) {
            return 'weekly';
        } elseif ( $days_since_update <= 30 ) {
            return 'monthly';
        } else {
            return 'yearly';
        }
    }

    /**
     * Récupère les facteurs de priorité pour un post.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Le post.
     * @return   array               Les facteurs.
     */
    private function get_priority_factors( $post ) {
        return array(
            'recency' => $this->calculate_recency_factor( $post ),
            'popularity' => $this->calculate_popularity_factor( $post ),
            'engagement' => $this->calculate_engagement_factor( $post ),
            'update_frequency' => $this->calculate_update_frequency_factor( $post ),
            'content_quality' => $this->calculate_content_quality_factor( $post )
        );
    }

    /**
     * Récupère les priorités intelligentes sauvegardées.
     *
     * @since    1.2.0
     * @return   array    Les priorités sauvegardées.
     */
    public function get_smart_priorities() {
        $saved_priorities = get_option( 'boss_seo_smart_priorities', array() );
        
        // Vérifier si les priorités sont récentes (moins de 24h)
        if ( isset( $saved_priorities['calculated_at'] ) ) {
            $calculated_time = strtotime( $saved_priorities['calculated_at'] );
            $current_time = current_time( 'timestamp' );
            
            if ( ( $current_time - $calculated_time ) > DAY_IN_SECONDS ) {
                // Recalculer si trop ancien
                return $this->calculate_smart_priorities();
            }
        } else {
            // Calculer si jamais fait
            return $this->calculate_smart_priorities();
        }
        
        return $saved_priorities['priorities'] ?? array();
    }

    /**
     * Configure les algorithmes de priorités.
     *
     * @since    1.2.0
     * @param    array    $config    La configuration des algorithmes.
     * @return   bool                Succès de la configuration.
     */
    public function configure_algorithms( $config ) {
        try {
            foreach ( $config as $algorithm => $settings ) {
                if ( isset( $this->priority_algorithms[$algorithm] ) ) {
                    $this->priority_algorithms[$algorithm] = array_merge(
                        $this->priority_algorithms[$algorithm],
                        $settings
                    );
                }
            }
            
            // Sauvegarder la configuration
            update_option( 'boss_seo_priority_algorithms', $this->priority_algorithms );
            
            // Invalider les priorités existantes pour forcer le recalcul
            delete_option( 'boss_seo_smart_priorities' );
            
            return true;
            
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur configuration algorithmes - ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Récupère la configuration des algorithmes.
     *
     * @since    1.2.0
     * @return   array    La configuration des algorithmes.
     */
    public function get_algorithms_config() {
        $saved_config = get_option( 'boss_seo_priority_algorithms', array() );
        
        if ( ! empty( $saved_config ) ) {
            $this->priority_algorithms = $saved_config;
        }
        
        return $this->priority_algorithms;
    }
}
?>
