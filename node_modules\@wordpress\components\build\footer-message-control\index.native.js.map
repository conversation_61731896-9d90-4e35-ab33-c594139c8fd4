{"version": 3, "names": ["_element", "require", "_footerMessageCell", "_interopRequireDefault", "FooterMessageControl", "props", "_react", "createElement", "default", "_default", "memo", "exports"], "sources": ["@wordpress/components/src/footer-message-control/index.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { memo } from '@wordpress/element';\n/**\n * Internal dependencies\n */\nimport FooterMessageCell from '../mobile/bottom-sheet/footer-message-cell';\n\nfunction FooterMessageControl( { ...props } ) {\n\treturn <FooterMessageCell { ...props } />;\n}\n\nexport default memo( FooterMessageControl );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAIA,IAAAC,kBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAPA;AACA;AACA;;AAEA;AACA;AACA;;AAGA,SAASG,oBAAoBA,CAAE;EAAE,GAAGC;AAAM,CAAC,EAAG;EAC7C,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACL,kBAAA,CAAAM,OAAiB;IAAA,GAAMH;EAAK,CAAI,CAAC;AAC1C;AAAC,IAAAI,QAAA,GAEc,IAAAC,aAAI,EAAEN,oBAAqB,CAAC;AAAAO,OAAA,CAAAH,OAAA,GAAAC,QAAA"}