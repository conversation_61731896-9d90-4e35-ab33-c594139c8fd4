{"version": 3, "names": ["_react", "require", "_alignmentMatrixControlStyles", "_EMOTION_STRINGIFIED_CSS_ERROR__", "rootSize", "padding", "size", "css", "gridTemplateRows", "maxHeight", "max<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "rootPointerEvents", "disablePointerEvents", "pointerEvents", "undefined", "Wrapper", "_base", "default", "target", "label", "name", "styles", "map", "toString", "exports", "Root", "rootBase", "pointActive", "isActive", "boxShadow", "Point", "pointBase", "Cell", "CellBase"], "sources": ["@wordpress/components/src/alignment-matrix-control/styles/alignment-matrix-control-icon-styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport {\n\trootBase,\n\tpointBase,\n\tCell as CellBase,\n} from './alignment-matrix-control-styles';\nimport type {\n\tAlignmentMatrixControlIconProps,\n\tAlignmentMatrixControlCellProps,\n} from '../types';\n\nconst rootSize = () => {\n\tconst padding = 1.5;\n\tconst size = 24;\n\n\treturn css( {\n\t\tgridTemplateRows: `repeat( 3, calc( ${ size - padding * 2 }px / 3))`,\n\t\tpadding,\n\t\tmaxHeight: size,\n\t\tmaxWidth: size,\n\t} );\n};\n\nconst rootPointerEvents = ( {\n\tdisablePointerEvents,\n}: Pick< AlignmentMatrixControlIconProps, 'disablePointerEvents' > ) => {\n\treturn css( {\n\t\tpointerEvents: disablePointerEvents ? 'none' : undefined,\n\t} );\n};\n\nexport const Wrapper = styled.div`\n\tbox-sizing: border-box;\n\tpadding: 2px;\n`;\n\nexport const Root = styled.div`\n\ttransform-origin: top left;\n\theight: 100%;\n\twidth: 100%;\n\n\t${ rootBase };\n\t${ rootSize };\n\t${ rootPointerEvents };\n`;\n\nconst pointActive = ( {\n\tisActive,\n}: Pick< AlignmentMatrixControlCellProps, 'isActive' > ) => {\n\tconst boxShadow = isActive ? `0 0 0 1px currentColor` : null;\n\n\treturn css`\n\t\tbox-shadow: ${ boxShadow };\n\t\tcolor: currentColor;\n\n\t\t*:hover > & {\n\t\t\tcolor: currentColor;\n\t\t}\n\t`;\n};\n\nexport const Point = styled.span`\n\theight: 2px;\n\twidth: 2px;\n\t${ pointBase };\n\t${ pointActive };\n`;\n\nexport const Cell = CellBase;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,6BAAA,GAAAD,OAAA;AAI2C,SAAAE,iCAAA;AAM3C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACtB,MAAMC,OAAO,GAAG,GAAG;EACnB,MAAMC,IAAI,GAAG,EAAE;EAEf,oBAAO,IAAAC,UAAG,EAAE;IACXC,gBAAgB,EAAG,oBAAoBF,IAAI,GAAGD,OAAO,GAAG,CAAG,UAAS;IACpEA,OAAO;IACPI,SAAS,EAAEH,IAAI;IACfI,QAAQ,EAAEJ;EACX,CAAC,EAAAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,6CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,k9EAAC,CAAC;AACJ,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAAE;EAC3BC;AACgE,CAAC,KAAM;EACvE,oBAAO,IAAAR,UAAG,EAAE;IACXS,aAAa,EAAED,oBAAoB,GAAG,MAAM,GAAGE;EAChD,CAAC,EAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,sDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,k9EAAC,CAAC;AACJ,CAAC;AAEM,MAAMK,OAAO,OAAAC,KAAA,CAAAC,OAAA,SAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAQ,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAU,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAvB;AAAA,EAGnB;AAACwB,OAAA,CAAAT,OAAA,GAAAA,OAAA;AAEK,MAAMU,IAAI,OAAAT,KAAA,CAAAC,OAAA,SAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAQ,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,wDAKbO,sCAAQ,OACRzB,QAAQ,OACRU,iBAAiB,SAAAH,OAAA,CAAAC,GAAA,CAAAC,QAAA,w9EACpB;AAACc,OAAA,CAAAC,IAAA,GAAAA,IAAA;AAEF,MAAME,WAAW,GAAGA,CAAE;EACrBC;AACoD,CAAC,KAAM;EAC3D,MAAMC,SAAS,GAAGD,QAAQ,GAAI,wBAAuB,GAAG,IAAI;EAE5D,wBAAOxB,UAAG,iBACMyB,SAAS,0DAAArB,OAAA,CAAAC,GAAA,CAAAC,QAAA,iDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAO1B,CAAC;AAEM,MAAMoB,KAAK,OAAAd,KAAA,CAAAC,OAAA,UAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAQ,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,4BAGdY,uCAAS,OACTJ,WAAW,SAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA,w9EACd;AAACc,OAAA,CAAAM,KAAA,GAAAA,KAAA;AAEK,MAAME,IAAI,GAAGC,kCAAQ;AAACT,OAAA,CAAAQ,IAAA,GAAAA,IAAA"}