"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.parseAudioUrl = void 0;
var _url = require("@wordpress/url");
/**
 * WordPress dependencies
 */

const parseAudioUrl = src => {
  const decodedURI = (0, _url.safeDecodeURI)(src);
  const fileName = decodedURI.split('#').shift().split('?').shift().split('/').pop();
  const parts = fileName.split('.');
  const extension = parts.length === 2 ? parts.pop().toUpperCase() + ' ' : '';
  const title = parts.join('.');
  return {
    title,
    extension
  };
};
exports.parseAudioUrl = parseAudioUrl;
//# sourceMappingURL=audio-url-parser.native.js.map