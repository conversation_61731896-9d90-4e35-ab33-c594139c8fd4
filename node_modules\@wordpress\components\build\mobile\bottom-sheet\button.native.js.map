{"version": 3, "names": ["_reactNative", "require", "_styles", "_interopRequireDefault", "BottomSheetButton", "onPress", "disabled", "text", "color", "_react", "createElement", "TouchableOpacity", "accessible", "View", "style", "flexDirection", "justifyContent", "Text", "styles", "buttonText", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/button.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { TouchableOpacity, View, Text } from 'react-native';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\n\nconst BottomSheetButton = ( { onPress, disabled, text, color } ) => (\n\t<TouchableOpacity\n\t\taccessible={ true }\n\t\tonPress={ onPress }\n\t\tdisabled={ disabled }\n\t>\n\t\t<View style={ { flexDirection: 'row', justifyContent: 'center' } }>\n\t\t\t<Text style={ { ...styles.buttonText, color } }>{ text }</Text>\n\t\t</View>\n\t</TouchableOpacity>\n);\n\nexport default BottomSheetButton;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAGA,MAAMG,iBAAiB,GAAGA,CAAE;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAC7D,IAAAC,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAW,gBAAgB;EAChBC,UAAU,EAAG,IAAM;EACnBP,OAAO,EAAGA,OAAS;EACnBC,QAAQ,EAAGA;AAAU,GAErB,IAAAG,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAa,IAAI;EAACC,KAAK,EAAG;IAAEC,aAAa,EAAE,KAAK;IAAEC,cAAc,EAAE;EAAS;AAAG,GACjE,IAAAP,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAiB,IAAI;EAACH,KAAK,EAAG;IAAE,GAAGI,eAAM,CAACC,UAAU;IAAEX;EAAM;AAAG,GAAGD,IAAY,CACzD,CACW,CAClB;AAAC,IAAAa,QAAA,GAEahB,iBAAiB;AAAAiB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}