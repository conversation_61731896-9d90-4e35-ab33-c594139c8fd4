"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.HStack = void 0;
var _react = require("react");
var _context = require("../context");
var _view = require("../view");
var _hook = require("./hook");
/**
 * Internal dependencies
 */

function UnconnectedHStack(props, forwardedRef) {
  const hStackProps = (0, _hook.useHStack)(props);
  return (0, _react.createElement)(_view.View, {
    ...hStackProps,
    ref: forwardedRef
  });
}

/**
 * `HStack` (Horizontal Stack) arranges child elements in a horizontal line.
 *
 * `HStack` can render anything inside.
 *
 * ```jsx
 * import {
 * 	__experimentalHStack as HStack,
 * 	__experimentalText as Text,
 * } from `@wordpress/components`;
 *
 * function Example() {
 * 	return (
 * 		<HStack>
 * 			<Text>Code</Text>
 * 			<Text>is</Text>
 * 			<Text>Poetry</Text>
 * 		</HStack>
 * 	);
 * }
 * ```
 */
const HStack = (0, _context.contextConnect)(UnconnectedHStack, 'HStack');
exports.HStack = HStack;
var _default = HStack;
exports.default = _default;
//# sourceMappingURL=component.js.map