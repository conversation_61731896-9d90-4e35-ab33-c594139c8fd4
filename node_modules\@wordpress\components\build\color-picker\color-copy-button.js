"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ColorCopyButton = void 0;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _styles = require("./styles");
var _tooltip = _interopRequireDefault(require("../tooltip"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const ColorCopyButton = props => {
  const {
    color,
    colorType
  } = props;
  const [copiedColor, setCopiedColor] = (0, _element.useState)(null);
  const copyTimer = (0, _element.useRef)();
  const copyRef = (0, _compose.useCopyToClipboard)(() => {
    switch (colorType) {
      case 'hsl':
        {
          return color.toHslString();
        }
      case 'rgb':
        {
          return color.toRgbString();
        }
      default:
      case 'hex':
        {
          return color.toHex();
        }
    }
  }, () => {
    if (copyTimer.current) {
      clearTimeout(copyTimer.current);
    }
    setCopiedColor(color.toHex());
    copyTimer.current = setTimeout(() => {
      setCopiedColor(null);
      copyTimer.current = undefined;
    }, 3000);
  });
  (0, _element.useEffect)(() => {
    // Clear copyTimer on component unmount.
    return () => {
      if (copyTimer.current) {
        clearTimeout(copyTimer.current);
      }
    };
  }, []);
  return (0, _react.createElement)(_tooltip.default, {
    delay: 0,
    hideOnClick: false,
    text: copiedColor === color.toHex() ? (0, _i18n.__)('Copied!') : (0, _i18n.__)('Copy')
  }, (0, _react.createElement)(_styles.CopyButton, {
    size: "small",
    ref: copyRef,
    icon: _icons.copy,
    showTooltip: false
  }));
};
exports.ColorCopyButton = ColorCopyButton;
//# sourceMappingURL=color-copy-button.js.map