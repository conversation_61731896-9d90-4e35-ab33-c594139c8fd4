"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ColorPicker = void 0;
var _react = require("react");
var _colord = require("colord");
var _names = _interopRequireDefault(require("colord/plugins/names"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _i18n = require("@wordpress/i18n");
var _context = require("../context");
var _styles = require("./styles");
var _colorCopyButton = require("./color-copy-button");
var _colorInput = require("./color-input");
var _picker = require("./picker");
var _hooks = require("../utils/hooks");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

(0, _colord.extend)([_names.default]);
const options = [{
  label: 'RGB',
  value: 'rgb'
}, {
  label: 'HSL',
  value: 'hsl'
}, {
  label: 'Hex',
  value: 'hex'
}];
const UnconnectedColorPicker = (props, forwardedRef) => {
  const {
    enableAlpha = false,
    color: colorProp,
    onChange,
    defaultValue = '#fff',
    copyFormat,
    // Context
    onPickerDragStart,
    onPickerDragEnd,
    ...divProps
  } = (0, _context.useContextSystem)(props, 'ColorPicker');
  const [containerEl, setContainerEl] = (0, _element.useState)(null);
  const containerRef = node => {
    setContainerEl(node);
  };

  // Use a safe default value for the color and remove the possibility of `undefined`.
  const [color, setColor] = (0, _hooks.useControlledValue)({
    onChange,
    value: colorProp,
    defaultValue
  });
  const safeColordColor = (0, _element.useMemo)(() => {
    return (0, _colord.colord)(color || '');
  }, [color]);
  const debouncedSetColor = (0, _compose.useDebounce)(setColor);
  const handleChange = (0, _element.useCallback)(nextValue => {
    debouncedSetColor(nextValue.toHex());
  }, [debouncedSetColor]);
  const [colorType, setColorType] = (0, _element.useState)(copyFormat || 'hex');
  return (0, _react.createElement)(_styles.ColorfulWrapper, {
    ref: (0, _compose.useMergeRefs)([containerRef, forwardedRef]),
    ...divProps
  }, (0, _react.createElement)(_picker.Picker, {
    containerEl: containerEl,
    onChange: handleChange,
    color: safeColordColor,
    enableAlpha: enableAlpha,
    onDragStart: onPickerDragStart,
    onDragEnd: onPickerDragEnd
  }), (0, _react.createElement)(_styles.AuxiliaryColorArtefactWrapper, null, (0, _react.createElement)(_styles.AuxiliaryColorArtefactHStackHeader, {
    justify: "space-between"
  }, (0, _react.createElement)(_styles.SelectControl, {
    __nextHasNoMarginBottom: true,
    options: options,
    value: colorType,
    onChange: nextColorType => setColorType(nextColorType),
    label: (0, _i18n.__)('Color format'),
    hideLabelFromVision: true
  }), (0, _react.createElement)(_colorCopyButton.ColorCopyButton, {
    color: safeColordColor,
    colorType: copyFormat || colorType
  })), (0, _react.createElement)(_styles.ColorInputWrapper, {
    direction: "column",
    gap: 2
  }, (0, _react.createElement)(_colorInput.ColorInput, {
    colorType: colorType,
    color: safeColordColor,
    onChange: handleChange,
    enableAlpha: enableAlpha
  }))));
};
const ColorPicker = (0, _context.contextConnect)(UnconnectedColorPicker, 'ColorPicker');
exports.ColorPicker = ColorPicker;
var _default = ColorPicker;
exports.default = _default;
//# sourceMappingURL=component.js.map