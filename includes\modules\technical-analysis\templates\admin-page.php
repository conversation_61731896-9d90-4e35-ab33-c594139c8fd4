<?php
/**
 * Template de la page d'administration du module d'analyse technique.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/modules/technical-analysis/templates
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( function_exists( 'console_log' ) ) {
    console_log( 'Boss SEO Technical Analysis: Chargement du template admin-page.php' );
}
?>

<div class="wrap boss-technical-analysis-wrap">
    <h1 class="wp-heading-inline">
        <span class="dashicons dashicons-analytics"></span>
        <?php echo esc_html( get_admin_page_title() ); ?>
        <span class="boss-version-badge">v2.0 Pro</span>
    </h1>

    <hr class="wp-header-end">

    <!-- Messages de statut -->
    <div id="boss-technical-messages" class="boss-messages-container"></div>

    <!-- Configuration requise -->
    <?php
    $external_services = get_option( 'boss_optimizer_external_services', array() );
    $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';

    if ( empty( $api_key ) ) {
        $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
    }

    if ( empty( $api_key ) ) :
    ?>
        <div class="notice notice-warning boss-config-notice">
            <h3><span class="dashicons dashicons-admin-settings"></span> Configuration recommandée</h3>
            <p>
                <strong><?php _e( 'API PageSpeed non configurée', 'boss-seo' ); ?></strong><br>
                <?php _e( 'Pour des analyses complètes avec données réelles de Google PageSpeed Insights, configurez votre clé API.', 'boss-seo' ); ?>
            </p>
            <p>
                <a href="<?php echo admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ); ?>" class="button button-primary">
                    <span class="dashicons dashicons-admin-settings"></span>
                    <?php _e( 'Configurer l\'API PageSpeed', 'boss-seo' ); ?>
                </a>
                <a href="https://developers.google.com/speed/docs/insights/v5/get-started" target="_blank" class="button button-secondary">
                    <span class="dashicons dashicons-external"></span>
                    <?php _e( 'Obtenir une clé API', 'boss-seo' ); ?>
                </a>
            </p>
        </div>
    <?php endif; ?>

    <!-- Interface principale à onglets -->
    <div class="boss-technical-analysis-container">

        <!-- Navigation des onglets -->
        <nav class="boss-tabs-nav">
            <ul class="boss-tabs-list">
                <li class="boss-tab-item active" data-tab="global-analysis">
                    <span class="dashicons dashicons-chart-area"></span>
                    <span class="tab-label"><?php _e( 'Analyse Globale', 'boss-seo' ); ?></span>
                </li>
                <li class="boss-tab-item" data-tab="pagespeed-test">
                    <span class="dashicons dashicons-performance"></span>
                    <span class="tab-label"><?php _e( 'Test PageSpeed', 'boss-seo' ); ?></span>
                </li>
                <li class="boss-tab-item" data-tab="ai-suggestions">
                    <span class="dashicons dashicons-lightbulb"></span>
                    <span class="tab-label"><?php _e( 'Suggestions IA', 'boss-seo' ); ?></span>
                </li>
                <li class="boss-tab-item" data-tab="technical-details">
                    <span class="dashicons dashicons-admin-tools"></span>
                    <span class="tab-label"><?php _e( 'Détails Techniques', 'boss-seo' ); ?></span>
                </li>
            </ul>
        </nav>

        <!-- Contenu des onglets -->
        <div class="boss-tabs-content">

            <!-- Onglet 1: Analyse Globale -->
            <div id="global-analysis" class="boss-tab-content active">
                <div class="boss-tab-header">
                    <h2><span class="dashicons dashicons-chart-area"></span> Analyse Globale des Pages</h2>
                    <p class="description">Vue d'ensemble des performances et de l'état technique de votre site.</p>
                </div>

                <div class="boss-analysis-controls">
                    <div class="boss-control-group">
                        <label for="global-page-selector"><?php _e( 'Sélectionner une page:', 'boss-seo' ); ?></label>
                        <select id="global-page-selector" class="boss-select">
                            <option value=""><?php _e( 'Chargement des pages...', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                    <div class="boss-control-group">
                        <button id="global-analyze-btn" class="boss-btn boss-btn-primary" disabled>
                            <span class="dashicons dashicons-search"></span>
                            <?php _e( 'Analyser', 'boss-seo' ); ?>
                        </button>
                    </div>
                </div>

                <div id="global-results" class="boss-results-container" style="display: none;">
                    <!-- Les résultats seront injectés ici par JavaScript -->
                </div>
            </div>

            <!-- Onglet 2: Test PageSpeed -->
            <div id="pagespeed-test" class="boss-tab-content">
                <div class="boss-tab-header">
                    <h2><span class="dashicons dashicons-performance"></span> Test Google PageSpeed Insights</h2>
                    <p class="description">Analyse détaillée des performances avec l'API officielle de Google.</p>
                </div>

                <div class="boss-analysis-controls">
                    <div class="boss-control-group">
                        <label for="pagespeed-page-selector"><?php _e( 'Sélectionner une page:', 'boss-seo' ); ?></label>
                        <select id="pagespeed-page-selector" class="boss-select">
                            <option value=""><?php _e( 'Chargement des pages...', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                    <div class="boss-control-group">
                        <label for="pagespeed-strategy"><?php _e( 'Stratégie:', 'boss-seo' ); ?></label>
                        <select id="pagespeed-strategy" class="boss-select">
                            <option value="mobile"><?php _e( 'Mobile', 'boss-seo' ); ?></option>
                            <option value="desktop"><?php _e( 'Desktop', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                    <div class="boss-control-group">
                        <button id="pagespeed-analyze-btn" class="boss-btn boss-btn-primary" disabled>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e( 'Tester PageSpeed', 'boss-seo' ); ?>
                        </button>
                    </div>
                </div>

                <div id="pagespeed-results" class="boss-results-container" style="display: none;">
                    <!-- Les résultats PageSpeed seront injectés ici -->
                </div>
            </div>

            <!-- Onglet 3: Suggestions IA -->
            <div id="ai-suggestions" class="boss-tab-content">
                <div class="boss-tab-header">
                    <h2><span class="dashicons dashicons-lightbulb"></span> Suggestions IA Intelligentes</h2>
                    <p class="description">Recommandations personnalisées générées par l'intelligence artificielle.</p>
                </div>

                <div class="boss-analysis-controls">
                    <div class="boss-control-group">
                        <label for="ai-page-selector"><?php _e( 'Sélectionner une page:', 'boss-seo' ); ?></label>
                        <select id="ai-page-selector" class="boss-select">
                            <option value=""><?php _e( 'Chargement des pages...', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                    <div class="boss-control-group">
                        <button id="ai-analyze-btn" class="boss-btn boss-btn-primary" disabled>
                            <span class="dashicons dashicons-lightbulb"></span>
                            <?php _e( 'Générer Suggestions', 'boss-seo' ); ?>
                        </button>
                    </div>
                </div>

                <div id="ai-results" class="boss-results-container" style="display: none;">
                    <!-- Les suggestions IA seront injectées ici -->
                </div>
            </div>

            <!-- Onglet 4: Détails Techniques -->
            <div id="technical-details" class="boss-tab-content">
                <div class="boss-tab-header">
                    <h2><span class="dashicons dashicons-admin-tools"></span> Détails Techniques Avancés</h2>
                    <p class="description">Analyse technique approfondie : SSL, meta tags, structure HTML, etc.</p>
                </div>

                <div class="boss-analysis-controls">
                    <div class="boss-control-group">
                        <label for="technical-page-selector"><?php _e( 'Sélectionner une page:', 'boss-seo' ); ?></label>
                        <select id="technical-page-selector" class="boss-select">
                            <option value=""><?php _e( 'Chargement des pages...', 'boss-seo' ); ?></option>
                        </select>
                    </div>
                    <div class="boss-control-group">
                        <button id="technical-analyze-btn" class="boss-btn boss-btn-primary" disabled>
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e( 'Analyser Technique', 'boss-seo' ); ?>
                        </button>
                    </div>
                </div>

                <div id="technical-results" class="boss-results-container" style="display: none;">
                    <!-- Les détails techniques seront injectés ici -->
                </div>
            </div>

        </div>
    </div>

    <!-- Loader global -->
    <div id="boss-global-loader" class="boss-loader-overlay" style="display: none;">
        <div class="boss-loader">
            <div class="boss-spinner"></div>
            <p class="boss-loader-text"><?php _e( 'Analyse en cours...', 'boss-seo' ); ?></p>
        </div>
    </div>

</div>

<script>
// Log de débogage pour le chargement du template
console.log('Boss SEO Technical Analysis: Template admin-page.php chargé');
console.log('Boss SEO Technical Analysis: Configuration disponible:', typeof bossTechnicalAnalysis !== 'undefined');

// Vérifier que les éléments DOM sont présents
document.addEventListener('DOMContentLoaded', function() {
    console.log('Boss SEO Technical Analysis: DOM ready');
    console.log('Boss SEO Technical Analysis: Onglets trouvés:', document.querySelectorAll('.boss-tab-item').length);
    console.log('Boss SEO Technical Analysis: Contenus d\'onglets trouvés:', document.querySelectorAll('.boss-tab-content').length);
});
</script>
