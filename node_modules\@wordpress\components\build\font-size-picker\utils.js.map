{"version": 3, "names": ["_unitControl", "require", "isSimpleCssValue", "value", "sizeRegex", "test", "String", "getCommonSizeUnit", "fontSizes", "firstFontSize", "otherFontSizes", "firstUnit", "parseQuantityAndUnitFromRawValue", "size", "areAllSizesSameUnit", "every", "fontSize", "unit"], "sources": ["@wordpress/components/src/font-size-picker/utils.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport type { FontSizePickerProps, FontSize } from './types';\nimport { parseQuantityAndUnitFromRawValue } from '../unit-control';\n\n/**\n * Some themes use css vars for their font sizes, so until we\n * have the way of calculating them don't display them.\n *\n * @param value The value that is checked.\n * @return Whether the value is a simple css value.\n */\nexport function isSimpleCssValue(\n\tvalue: NonNullable< FontSizePickerProps[ 'value' ] >\n) {\n\tconst sizeRegex =\n\t\t/^[\\d\\.]+(px|em|rem|vw|vh|%|svw|lvw|dvw|svh|lvh|dvh|vi|svi|lvi|dvi|vb|svb|lvb|dvb|vmin|svmin|lvmin|dvmin|vmax|svmax|lvmax|dvmax)?$/i;\n\treturn sizeRegex.test( String( value ) );\n}\n\n/**\n * If all of the given font sizes have the same unit (e.g. 'px'), return that\n * unit. Otherwise return null.\n *\n * @param fontSizes List of font sizes.\n * @return The common unit, or null.\n */\nexport function getCommonSizeUnit( fontSizes: FontSize[] ) {\n\tconst [ firstFontSize, ...otherFontSizes ] = fontSizes;\n\tif ( ! firstFontSize ) {\n\t\treturn null;\n\t}\n\tconst [ , firstUnit ] = parseQuantityAndUnitFromRawValue(\n\t\tfirstFontSize.size\n\t);\n\tconst areAllSizesSameUnit = otherFontSizes.every( ( fontSize ) => {\n\t\tconst [ , unit ] = parseQuantityAndUnitFromRawValue( fontSize.size );\n\t\treturn unit === firstUnit;\n\t} );\n\treturn areAllSizesSameUnit ? firstUnit : null;\n}\n"], "mappings": ";;;;;;;AASA,IAAAA,YAAA,GAAAC,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,gBAAgBA,CAC/BC,KAAoD,EACnD;EACD,MAAMC,SAAS,GACd,oIAAoI;EACrI,OAAOA,SAAS,CAACC,IAAI,CAAEC,MAAM,CAAEH,KAAM,CAAE,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,iBAAiBA,CAAEC,SAAqB,EAAG;EAC1D,MAAM,CAAEC,aAAa,EAAE,GAAGC,cAAc,CAAE,GAAGF,SAAS;EACtD,IAAK,CAAEC,aAAa,EAAG;IACtB,OAAO,IAAI;EACZ;EACA,MAAM,GAAIE,SAAS,CAAE,GAAG,IAAAC,6CAAgC,EACvDH,aAAa,CAACI,IACf,CAAC;EACD,MAAMC,mBAAmB,GAAGJ,cAAc,CAACK,KAAK,CAAIC,QAAQ,IAAM;IACjE,MAAM,GAAIC,IAAI,CAAE,GAAG,IAAAL,6CAAgC,EAAEI,QAAQ,CAACH,IAAK,CAAC;IACpE,OAAOI,IAAI,KAAKN,SAAS;EAC1B,CAAE,CAAC;EACH,OAAOG,mBAAmB,GAAGH,SAAS,GAAG,IAAI;AAC9C"}