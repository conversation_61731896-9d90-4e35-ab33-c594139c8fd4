{"version": 3, "names": ["_i18n", "require", "_icons", "_borderControlStylePicker", "_interopRequireDefault", "_button", "_colorIndicator", "_colorPalette", "_dropdown", "_hStack", "_vStack", "_context", "_hook", "_baseControlStyles", "_dropdownContentWrapper", "_utils", "getAriaLabelColorValue", "colorValue", "replace", "getColorObject", "colors", "isMultiplePaletteArray", "matchedColor", "some", "origin", "color", "find", "getToggleAriaLabel", "colorObject", "style", "isStyleEnabled", "ariaLabelValue", "sprintf", "name", "__", "BorderControlDropdown", "props", "forwardedRef", "__experimentalIsRenderedInSidebar", "border", "disableCustomColors", "enableAlpha", "enableStyle", "indicatorClassName", "indicatorWrapperClassName", "isStyleSettable", "onReset", "onColorChange", "onStyleChange", "popoverContentClassName", "popoverControlsClassName", "resetButtonClassName", "showDropdownHeader", "size", "__unstablePopoverProps", "otherProps", "useBorderControlDropdown", "toggleAriaLabel", "showResetButton", "dropdownPosition", "undefined", "renderToggle", "onToggle", "_react", "createElement", "default", "onClick", "variant", "tooltipPosition", "label", "showTooltip", "__next40pxDefaultSize", "className", "renderContent", "onClose", "Fragment", "paddingSize", "VStack", "spacing", "HStack", "StyledLabel", "icon", "closeSmall", "value", "onChange", "clearable", "popoverProps", "ref", "ConnectedBorderControlDropdown", "contextConnect", "_default", "exports"], "sources": ["@wordpress/components/src/border-control/border-control-dropdown/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { __, sprintf } from '@wordpress/i18n';\nimport { closeSmall } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport BorderControlStylePicker from '../border-control-style-picker';\nimport Button from '../../button';\nimport ColorIndicator from '../../color-indicator';\nimport ColorPalette from '../../color-palette';\nimport Dropdown from '../../dropdown';\nimport { HStack } from '../../h-stack';\nimport { VStack } from '../../v-stack';\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useBorderControlDropdown } from './hook';\nimport { StyledLabel } from '../../base-control/styles/base-control-styles';\nimport DropdownContentWrapper from '../../dropdown/dropdown-content-wrapper';\n\nimport type { ColorObject } from '../../color-palette/types';\nimport { isMultiplePaletteArray } from '../../color-palette/utils';\nimport type { DropdownProps as DropdownComponentProps } from '../../dropdown/types';\nimport type { ColorProps, DropdownProps } from '../types';\n\nconst getAriaLabelColorValue = ( colorValue: string ) => {\n\t// Leave hex values as-is. Remove the `var()` wrapper from CSS vars.\n\treturn colorValue.replace( /^var\\((.+)\\)$/, '$1' );\n};\n\nconst getColorObject = (\n\tcolorValue: CSSProperties[ 'borderColor' ],\n\tcolors: ColorProps[ 'colors' ] | undefined\n) => {\n\tif ( ! colorValue || ! colors ) {\n\t\treturn;\n\t}\n\n\tif ( isMultiplePaletteArray( colors ) ) {\n\t\t// Multiple origins\n\t\tlet matchedColor;\n\n\t\tcolors.some( ( origin ) =>\n\t\t\torigin.colors.some( ( color ) => {\n\t\t\t\tif ( color.color === colorValue ) {\n\t\t\t\t\tmatchedColor = color;\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\t\t\t} )\n\t\t);\n\n\t\treturn matchedColor;\n\t}\n\n\t// Single origin\n\treturn colors.find( ( color ) => color.color === colorValue );\n};\n\nconst getToggleAriaLabel = (\n\tcolorValue: CSSProperties[ 'borderColor' ],\n\tcolorObject: ColorObject | undefined,\n\tstyle: CSSProperties[ 'borderStyle' ],\n\tisStyleEnabled: boolean\n) => {\n\tif ( isStyleEnabled ) {\n\t\tif ( colorObject ) {\n\t\t\tconst ariaLabelValue = getAriaLabelColorValue( colorObject.color );\n\t\t\treturn style\n\t\t\t\t? sprintf(\n\t\t\t\t\t\t// translators: %1$s: The name of the color e.g. \"vivid red\". %2$s: The color's hex code e.g.: \"#f00:\". %3$s: The current border style selection e.g. \"solid\".\n\t\t\t\t\t\t'Border color and style picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\". The currently selected style is \"%3$s\".',\n\t\t\t\t\t\tcolorObject.name,\n\t\t\t\t\t\tariaLabelValue,\n\t\t\t\t\t\tstyle\n\t\t\t\t  )\n\t\t\t\t: sprintf(\n\t\t\t\t\t\t// translators: %1$s: The name of the color e.g. \"vivid red\". %2$s: The color's hex code e.g.: \"#f00:\".\n\t\t\t\t\t\t'Border color and style picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\".',\n\t\t\t\t\t\tcolorObject.name,\n\t\t\t\t\t\tariaLabelValue\n\t\t\t\t  );\n\t\t}\n\n\t\tif ( colorValue ) {\n\t\t\tconst ariaLabelValue = getAriaLabelColorValue( colorValue );\n\t\t\treturn style\n\t\t\t\t? sprintf(\n\t\t\t\t\t\t// translators: %1$s: The color's hex code e.g.: \"#f00:\". %2$s: The current border style selection e.g. \"solid\".\n\t\t\t\t\t\t'Border color and style picker. The currently selected color has a value of \"%1$s\". The currently selected style is \"%2$s\".',\n\t\t\t\t\t\tariaLabelValue,\n\t\t\t\t\t\tstyle\n\t\t\t\t  )\n\t\t\t\t: sprintf(\n\t\t\t\t\t\t// translators: %1$s: The color's hex code e.g: \"#f00\".\n\t\t\t\t\t\t'Border color and style picker. The currently selected color has a value of \"%1$s\".',\n\t\t\t\t\t\tariaLabelValue\n\t\t\t\t  );\n\t\t}\n\n\t\treturn __( 'Border color and style picker.' );\n\t}\n\n\tif ( colorObject ) {\n\t\treturn sprintf(\n\t\t\t// translators: %1$s: The name of the color e.g. \"vivid red\". %2$s: The color's hex code e.g: \"#f00\".\n\t\t\t'Border color picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\".',\n\t\t\tcolorObject.name,\n\t\t\tgetAriaLabelColorValue( colorObject.color )\n\t\t);\n\t}\n\n\tif ( colorValue ) {\n\t\treturn sprintf(\n\t\t\t// translators: %1$s: The color's hex code e.g: \"#f00\".\n\t\t\t'Border color picker. The currently selected color has a value of \"%1$s\".',\n\t\t\tgetAriaLabelColorValue( colorValue )\n\t\t);\n\t}\n\n\treturn __( 'Border color picker.' );\n};\n\nconst BorderControlDropdown = (\n\tprops: WordPressComponentProps< DropdownProps, 'div' >,\n\tforwardedRef: React.ForwardedRef< any >\n) => {\n\tconst {\n\t\t__experimentalIsRenderedInSidebar,\n\t\tborder,\n\t\tcolors,\n\t\tdisableCustomColors,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\tindicatorClassName,\n\t\tindicatorWrapperClassName,\n\t\tisStyleSettable,\n\t\tonReset,\n\t\tonColorChange,\n\t\tonStyleChange,\n\t\tpopoverContentClassName,\n\t\tpopoverControlsClassName,\n\t\tresetButtonClassName,\n\t\tshowDropdownHeader,\n\t\tsize,\n\t\t__unstablePopoverProps,\n\t\t...otherProps\n\t} = useBorderControlDropdown( props );\n\n\tconst { color, style } = border || {};\n\tconst colorObject = getColorObject( color, colors );\n\n\tconst toggleAriaLabel = getToggleAriaLabel(\n\t\tcolor,\n\t\tcolorObject,\n\t\tstyle,\n\t\tenableStyle\n\t);\n\n\tconst showResetButton = color || ( style && style !== 'none' );\n\tconst dropdownPosition = __experimentalIsRenderedInSidebar\n\t\t? 'bottom left'\n\t\t: undefined;\n\n\tconst renderToggle: DropdownComponentProps[ 'renderToggle' ] = ( {\n\t\tonToggle,\n\t} ) => (\n\t\t<Button\n\t\t\tonClick={ onToggle }\n\t\t\tvariant=\"tertiary\"\n\t\t\taria-label={ toggleAriaLabel }\n\t\t\ttooltipPosition={ dropdownPosition }\n\t\t\tlabel={ __( 'Border color and style picker' ) }\n\t\t\tshowTooltip={ true }\n\t\t\t__next40pxDefaultSize={ size === '__unstable-large' ? true : false }\n\t\t>\n\t\t\t<span className={ indicatorWrapperClassName }>\n\t\t\t\t<ColorIndicator\n\t\t\t\t\tclassName={ indicatorClassName }\n\t\t\t\t\tcolorValue={ color }\n\t\t\t\t/>\n\t\t\t</span>\n\t\t</Button>\n\t);\n\n\tconst renderContent: DropdownComponentProps[ 'renderContent' ] = ( {\n\t\tonClose,\n\t} ) => (\n\t\t<>\n\t\t\t<DropdownContentWrapper paddingSize=\"medium\">\n\t\t\t\t<VStack className={ popoverControlsClassName } spacing={ 6 }>\n\t\t\t\t\t{ showDropdownHeader ? (\n\t\t\t\t\t\t<HStack>\n\t\t\t\t\t\t\t<StyledLabel>{ __( 'Border color' ) }</StyledLabel>\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\tlabel={ __( 'Close border color' ) }\n\t\t\t\t\t\t\t\ticon={ closeSmall }\n\t\t\t\t\t\t\t\tonClick={ onClose }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</HStack>\n\t\t\t\t\t) : undefined }\n\t\t\t\t\t<ColorPalette\n\t\t\t\t\t\tclassName={ popoverContentClassName }\n\t\t\t\t\t\tvalue={ color }\n\t\t\t\t\t\tonChange={ onColorChange }\n\t\t\t\t\t\t{ ...{ colors, disableCustomColors } }\n\t\t\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclearable={ false }\n\t\t\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\t\t/>\n\t\t\t\t\t{ enableStyle && isStyleSettable && (\n\t\t\t\t\t\t<BorderControlStylePicker\n\t\t\t\t\t\t\tlabel={ __( 'Style' ) }\n\t\t\t\t\t\t\tvalue={ style }\n\t\t\t\t\t\t\tonChange={ onStyleChange }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t</VStack>\n\t\t\t</DropdownContentWrapper>\n\t\t\t{ showResetButton && (\n\t\t\t\t<DropdownContentWrapper paddingSize=\"none\">\n\t\t\t\t\t<Button\n\t\t\t\t\t\tclassName={ resetButtonClassName }\n\t\t\t\t\t\tvariant=\"tertiary\"\n\t\t\t\t\t\tonClick={ () => {\n\t\t\t\t\t\t\tonReset();\n\t\t\t\t\t\t\tonClose();\n\t\t\t\t\t\t} }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ __( 'Reset' ) }\n\t\t\t\t\t</Button>\n\t\t\t\t</DropdownContentWrapper>\n\t\t\t) }\n\t\t</>\n\t);\n\n\treturn (\n\t\t<Dropdown\n\t\t\trenderToggle={ renderToggle }\n\t\t\trenderContent={ renderContent }\n\t\t\tpopoverProps={ {\n\t\t\t\t...__unstablePopoverProps,\n\t\t\t} }\n\t\t\t{ ...otherProps }\n\t\t\tref={ forwardedRef }\n\t\t/>\n\t);\n};\n\nconst ConnectedBorderControlDropdown = contextConnect(\n\tBorderControlDropdown,\n\t'BorderControlDropdown'\n);\n\nexport default ConnectedBorderControlDropdown;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAKA,IAAAE,yBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,OAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,eAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,aAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,SAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,OAAA,GAAAT,OAAA;AAEA,IAAAU,QAAA,GAAAV,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AACA,IAAAY,kBAAA,GAAAZ,OAAA;AACA,IAAAa,uBAAA,GAAAV,sBAAA,CAAAH,OAAA;AAGA,IAAAc,MAAA,GAAAd,OAAA;AA5BA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAmBA,MAAMe,sBAAsB,GAAKC,UAAkB,IAAM;EACxD;EACA,OAAOA,UAAU,CAACC,OAAO,CAAE,eAAe,EAAE,IAAK,CAAC;AACnD,CAAC;AAED,MAAMC,cAAc,GAAGA,CACtBF,UAA0C,EAC1CG,MAA0C,KACtC;EACJ,IAAK,CAAEH,UAAU,IAAI,CAAEG,MAAM,EAAG;IAC/B;EACD;EAEA,IAAK,IAAAC,6BAAsB,EAAED,MAAO,CAAC,EAAG;IACvC;IACA,IAAIE,YAAY;IAEhBF,MAAM,CAACG,IAAI,CAAIC,MAAM,IACpBA,MAAM,CAACJ,MAAM,CAACG,IAAI,CAAIE,KAAK,IAAM;MAChC,IAAKA,KAAK,CAACA,KAAK,KAAKR,UAAU,EAAG;QACjCK,YAAY,GAAGG,KAAK;QACpB,OAAO,IAAI;MACZ;MAEA,OAAO,KAAK;IACb,CAAE,CACH,CAAC;IAED,OAAOH,YAAY;EACpB;;EAEA;EACA,OAAOF,MAAM,CAACM,IAAI,CAAID,KAAK,IAAMA,KAAK,CAACA,KAAK,KAAKR,UAAW,CAAC;AAC9D,CAAC;AAED,MAAMU,kBAAkB,GAAGA,CAC1BV,UAA0C,EAC1CW,WAAoC,EACpCC,KAAqC,EACrCC,cAAuB,KACnB;EACJ,IAAKA,cAAc,EAAG;IACrB,IAAKF,WAAW,EAAG;MAClB,MAAMG,cAAc,GAAGf,sBAAsB,CAAEY,WAAW,CAACH,KAAM,CAAC;MAClE,OAAOI,KAAK,GACT,IAAAG,aAAO;MACP;MACA,iJAAiJ,EACjJJ,WAAW,CAACK,IAAI,EAChBF,cAAc,EACdF,KACA,CAAC,GACD,IAAAG,aAAO;MACP;MACA,yGAAyG,EACzGJ,WAAW,CAACK,IAAI,EAChBF,cACA,CAAC;IACL;IAEA,IAAKd,UAAU,EAAG;MACjB,MAAMc,cAAc,GAAGf,sBAAsB,CAAEC,UAAW,CAAC;MAC3D,OAAOY,KAAK,GACT,IAAAG,aAAO;MACP;MACA,4HAA4H,EAC5HD,cAAc,EACdF,KACA,CAAC,GACD,IAAAG,aAAO;MACP;MACA,oFAAoF,EACpFD,cACA,CAAC;IACL;IAEA,OAAO,IAAAG,QAAE,EAAE,gCAAiC,CAAC;EAC9C;EAEA,IAAKN,WAAW,EAAG;IAClB,OAAO,IAAAI,aAAO;IACb;IACA,+FAA+F,EAC/FJ,WAAW,CAACK,IAAI,EAChBjB,sBAAsB,CAAEY,WAAW,CAACH,KAAM,CAC3C,CAAC;EACF;EAEA,IAAKR,UAAU,EAAG;IACjB,OAAO,IAAAe,aAAO;IACb;IACA,0EAA0E,EAC1EhB,sBAAsB,CAAEC,UAAW,CACpC,CAAC;EACF;EAEA,OAAO,IAAAiB,QAAE,EAAE,sBAAuB,CAAC;AACpC,CAAC;AAED,MAAMC,qBAAqB,GAAGA,CAC7BC,KAAsD,EACtDC,YAAuC,KACnC;EACJ,MAAM;IACLC,iCAAiC;IACjCC,MAAM;IACNnB,MAAM;IACNoB,mBAAmB;IACnBC,WAAW;IACXC,WAAW;IACXC,kBAAkB;IAClBC,yBAAyB;IACzBC,eAAe;IACfC,OAAO;IACPC,aAAa;IACbC,aAAa;IACbC,uBAAuB;IACvBC,wBAAwB;IACxBC,oBAAoB;IACpBC,kBAAkB;IAClBC,IAAI;IACJC,sBAAsB;IACtB,GAAGC;EACJ,CAAC,GAAG,IAAAC,8BAAwB,EAAEpB,KAAM,CAAC;EAErC,MAAM;IAAEX,KAAK;IAAEI;EAAM,CAAC,GAAGU,MAAM,IAAI,CAAC,CAAC;EACrC,MAAMX,WAAW,GAAGT,cAAc,CAAEM,KAAK,EAAEL,MAAO,CAAC;EAEnD,MAAMqC,eAAe,GAAG9B,kBAAkB,CACzCF,KAAK,EACLG,WAAW,EACXC,KAAK,EACLa,WACD,CAAC;EAED,MAAMgB,eAAe,GAAGjC,KAAK,IAAMI,KAAK,IAAIA,KAAK,KAAK,MAAQ;EAC9D,MAAM8B,gBAAgB,GAAGrB,iCAAiC,GACvD,aAAa,GACbsB,SAAS;EAEZ,MAAMC,YAAsD,GAAGA,CAAE;IAChEC;EACD,CAAC,KACA,IAAAC,MAAA,CAAAC,aAAA,EAAC3D,OAAA,CAAA4D,OAAM;IACNC,OAAO,EAAGJ,QAAU;IACpBK,OAAO,EAAC,UAAU;IAClB,cAAaV,eAAiB;IAC9BW,eAAe,EAAGT,gBAAkB;IACpCU,KAAK,EAAG,IAAAnC,QAAE,EAAE,+BAAgC,CAAG;IAC/CoC,WAAW,EAAG,IAAM;IACpBC,qBAAqB,EAAGlB,IAAI,KAAK,kBAAkB,GAAG,IAAI,GAAG;EAAO,GAEpE,IAAAU,MAAA,CAAAC,aAAA;IAAMQ,SAAS,EAAG5B;EAA2B,GAC5C,IAAAmB,MAAA,CAAAC,aAAA,EAAC1D,eAAA,CAAA2D,OAAc;IACdO,SAAS,EAAG7B,kBAAoB;IAChC1B,UAAU,EAAGQ;EAAO,CACpB,CACI,CACC,CACR;EAED,MAAMgD,aAAwD,GAAGA,CAAE;IAClEC;EACD,CAAC,KACA,IAAAX,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAY,QAAA,QACC,IAAAZ,MAAA,CAAAC,aAAA,EAAClD,uBAAA,CAAAmD,OAAsB;IAACW,WAAW,EAAC;EAAQ,GAC3C,IAAAb,MAAA,CAAAC,aAAA,EAACtD,OAAA,CAAAmE,MAAM;IAACL,SAAS,EAAGtB,wBAA0B;IAAC4B,OAAO,EAAG;EAAG,GACzD1B,kBAAkB,GACnB,IAAAW,MAAA,CAAAC,aAAA,EAACvD,OAAA,CAAAsE,MAAM,QACN,IAAAhB,MAAA,CAAAC,aAAA,EAACnD,kBAAA,CAAAmE,WAAW,QAAG,IAAA9C,QAAE,EAAE,cAAe,CAAgB,CAAC,EACnD,IAAA6B,MAAA,CAAAC,aAAA,EAAC3D,OAAA,CAAA4D,OAAM;IACNZ,IAAI,EAAC,OAAO;IACZgB,KAAK,EAAG,IAAAnC,QAAE,EAAE,oBAAqB,CAAG;IACpC+C,IAAI,EAAGC,iBAAY;IACnBhB,OAAO,EAAGQ;EAAS,CACnB,CACM,CAAC,GACNd,SAAS,EACb,IAAAG,MAAA,CAAAC,aAAA,EAACzD,aAAA,CAAA0D,OAAY;IACZO,SAAS,EAAGvB,uBAAyB;IACrCkC,KAAK,EAAG1D,KAAO;IACf2D,QAAQ,EAAGrC,aAAe;IACnB3B,MAAM;IAAEoB,mBAAmB;IAClCF,iCAAiC,EAChCA,iCACA;IACD+C,SAAS,EAAG,KAAO;IACnB5C,WAAW,EAAGA;EAAa,CAC3B,CAAC,EACAC,WAAW,IAAIG,eAAe,IAC/B,IAAAkB,MAAA,CAAAC,aAAA,EAAC7D,yBAAA,CAAA8D,OAAwB;IACxBI,KAAK,EAAG,IAAAnC,QAAE,EAAE,OAAQ,CAAG;IACvBiD,KAAK,EAAGtD,KAAO;IACfuD,QAAQ,EAAGpC;EAAe,CAC1B,CAEK,CACe,CAAC,EACvBU,eAAe,IAChB,IAAAK,MAAA,CAAAC,aAAA,EAAClD,uBAAA,CAAAmD,OAAsB;IAACW,WAAW,EAAC;EAAM,GACzC,IAAAb,MAAA,CAAAC,aAAA,EAAC3D,OAAA,CAAA4D,OAAM;IACNO,SAAS,EAAGrB,oBAAsB;IAClCgB,OAAO,EAAC,UAAU;IAClBD,OAAO,EAAGA,CAAA,KAAM;MACfpB,OAAO,CAAC,CAAC;MACT4B,OAAO,CAAC,CAAC;IACV;EAAG,GAED,IAAAxC,QAAE,EAAE,OAAQ,CACP,CACe,CAExB,CACF;EAED,OACC,IAAA6B,MAAA,CAAAC,aAAA,EAACxD,SAAA,CAAAyD,OAAQ;IACRJ,YAAY,EAAGA,YAAc;IAC7BY,aAAa,EAAGA,aAAe;IAC/Ba,YAAY,EAAG;MACd,GAAGhC;IACJ,CAAG;IAAA,GACEC,UAAU;IACfgC,GAAG,EAAGlD;EAAc,CACpB,CAAC;AAEJ,CAAC;AAED,MAAMmD,8BAA8B,GAAG,IAAAC,uBAAc,EACpDtD,qBAAqB,EACrB,uBACD,CAAC;AAAC,IAAAuD,QAAA,GAEaF,8BAA8B;AAAAG,OAAA,CAAA1B,OAAA,GAAAyB,QAAA"}