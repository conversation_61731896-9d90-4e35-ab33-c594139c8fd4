/**
 * Script de test pour les APIs de la Phase 2 - Monitoring & Analytics
 * À exécuter dans la console du navigateur sur la page d'admin WordPress
 */

console.log('🧪 Test des APIs Phase 2 - Monitoring & Analytics');
console.log('===================================================');

// Fonction utilitaire pour tester une API
async function testAPI(name, apiCall) {
    console.log(`\n🔍 Test: ${name}`);
    console.log('📡 Appel en cours...');
    
    try {
        const startTime = Date.now();
        const result = await apiCall();
        const endTime = Date.now();
        
        console.log(`✅ Succès (${endTime - startTime}ms):`, result);
        return { success: true, result, duration: endTime - startTime };
    } catch (error) {
        console.log('❌ Erreur:', error);
        return { success: false, error, duration: 0 };
    }
}

// Tests des APIs Monitoring
async function testMonitoringAPIs() {
    console.log('\n📈 === TESTS MONITORING ===');
    
    // Test 1: Statistiques d'indexation détaillées
    await testAPI('Statistiques d\'Indexation Détaillées', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/monitoring/indexation-stats', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Actualisation des données de monitoring
    await testAPI('Actualisation Monitoring', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/monitoring/refresh', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 3: Récupération des erreurs
    await testAPI('Récupération des Erreurs', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/monitoring/errors', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 4: Détection d'erreurs
    await testAPI('Détection d\'Erreurs', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/monitoring/detect-errors', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
}

// Tests des APIs Sitemaps Spécialisés
async function testSpecializedAPIs() {
    console.log('\n🎯 === TESTS SITEMAPS SPÉCIALISÉS ===');
    
    // Test 1: Statistiques des sitemaps spécialisés
    await testAPI('Statistiques Sitemaps Spécialisés', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/specialized/stats', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Actualisation des stats spécialisés
    await testAPI('Actualisation Stats Spécialisés', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/specialized/refresh', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 3: Régénération sitemap d'images
    await testAPI('Régénération Sitemap Images', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/specialized/regenerate/image', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 4: Régénération sitemap de vidéos
    await testAPI('Régénération Sitemap Vidéos', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/specialized/regenerate/video', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 5: Régénération sitemap news
    await testAPI('Régénération Sitemap News', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/specialized/regenerate/news', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 6: Régénération sitemap stories
    await testAPI('Régénération Sitemap Stories', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/specialized/regenerate/stories', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
}

// Test d'analyse des données
async function testDataAnalysis() {
    console.log('\n📊 === TESTS ANALYSE DES DONNÉES ===');
    
    // Test 1: Analyse complète des statistiques
    const statsResult = await testAPI('Analyse Statistiques Complètes', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/monitoring/indexation-stats', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    if (statsResult.success && statsResult.result.stats) {
        const stats = statsResult.result.stats;
        
        console.log('\n📋 === ANALYSE DES RÉSULTATS ===');
        
        // Analyse globale
        if (stats.global) {
            console.log('🌍 Statistiques Globales:');
            console.log(`  📊 URLs soumises: ${stats.global.submitted || 0}`);
            console.log(`  ✅ URLs indexées: ${stats.global.indexed || 0}`);
            console.log(`  ⏳ URLs en attente: ${stats.global.pending || 0}`);
            console.log(`  ❌ URLs avec erreurs: ${stats.global.errors || 0}`);
            console.log(`  📈 Taux d'indexation: ${stats.global.rate || 0}%`);
        }
        
        // Analyse par type de contenu
        if (stats.by_content_type && stats.by_content_type.length > 0) {
            console.log('\n📄 Analyse par Type de Contenu:');
            stats.by_content_type.forEach(type => {
                console.log(`  ${type.label}: ${type.indexed}/${type.total} (${type.rate}%)`);
            });
        }
        
        // Analyse des erreurs
        if (stats.errors_detail && stats.errors_detail.length > 0) {
            console.log('\n🚨 Erreurs Détectées:');
            const errorsByType = {};
            stats.errors_detail.forEach(error => {
                errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
            });
            
            Object.entries(errorsByType).forEach(([type, count]) => {
                console.log(`  ${type}: ${count} erreur(s)`);
            });
        }
        
        // Analyse de performance
        if (stats.performance && stats.performance.length > 0) {
            console.log('\n⚡ Performance des Sitemaps:');
            stats.performance.forEach(perf => {
                console.log(`  ${perf.name}: ${perf.load_time}ms (${perf.size_formatted || 'N/A'})`);
            });
        }
        
        // Benchmark
        if (stats.benchmark) {
            console.log('\n🏆 Benchmark:');
            if (stats.benchmark.your_site) {
                console.log(`  Votre site: ${stats.benchmark.your_site.rate}%`);
            }
            if (stats.benchmark.industry_avg) {
                console.log(`  Moyenne secteur: ${stats.benchmark.industry_avg.rate}%`);
            }
            if (stats.benchmark.best_practice) {
                console.log(`  Meilleures pratiques: ${stats.benchmark.best_practice.rate}%`);
            }
        }
    }
}

// Test de stress (appels multiples)
async function testStress() {
    console.log('\n💪 === TEST DE STRESS ===');
    
    const startTime = Date.now();
    const promises = [];
    
    // Lancer 10 appels simultanés
    for (let i = 0; i < 10; i++) {
        promises.push(
            fetch('/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats', {
                headers: { 'X-WP-Nonce': wpApiSettings.nonce }
            })
        );
    }
    
    try {
        const results = await Promise.all(promises);
        const endTime = Date.now();
        
        const successCount = results.filter(r => r.ok).length;
        const avgTime = (endTime - startTime) / 10;
        
        console.log(`✅ Test de stress réussi:`);
        console.log(`  📊 ${successCount}/10 appels réussis`);
        console.log(`  ⏱️  Temps moyen: ${avgTime.toFixed(2)}ms`);
        console.log(`  🚀 Temps total: ${endTime - startTime}ms`);
        
        return true;
    } catch (error) {
        console.log('❌ Erreur lors du test de stress:', error);
        return false;
    }
}

// Test de validation des données
async function testDataValidation() {
    console.log('\n🔍 === TEST DE VALIDATION DES DONNÉES ===');
    
    // Récupérer les données et valider leur structure
    const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/monitoring/indexation-stats', {
        headers: { 'X-WP-Nonce': wpApiSettings.nonce }
    });
    
    if (response.ok) {
        const data = await response.json();
        
        console.log('📋 Validation de la structure des données:');
        
        // Vérifier la structure attendue
        const requiredFields = ['success', 'stats'];
        const missingFields = requiredFields.filter(field => !(field in data));
        
        if (missingFields.length === 0) {
            console.log('✅ Structure de base valide');
        } else {
            console.log('❌ Champs manquants:', missingFields);
        }
        
        // Vérifier les sous-structures
        if (data.stats) {
            const statsFields = ['global', 'by_content_type', 'evolution', 'errors_detail', 'performance', 'benchmark'];
            const missingStatsFields = statsFields.filter(field => !(field in data.stats));
            
            if (missingStatsFields.length === 0) {
                console.log('✅ Structure des statistiques valide');
            } else {
                console.log('⚠️ Champs stats manquants:', missingStatsFields);
            }
        }
        
        return data;
    } else {
        console.log('❌ Erreur lors de la récupération des données');
        return null;
    }
}

// Fonction principale pour exécuter tous les tests
async function runAllPhase2Tests() {
    console.log('🚀 Démarrage des tests Phase 2...\n');
    
    const startTime = Date.now();
    
    try {
        await testMonitoringAPIs();
        await testSpecializedAPIs();
        await testDataAnalysis();
        await testDataValidation();
        await testStress();
        
        const endTime = Date.now();
        
        console.log('\n✅ === RÉSUMÉ DES TESTS PHASE 2 ===');
        console.log(`⏱️  Durée totale: ${endTime - startTime}ms`);
        console.log('🎯 Tous les tests de la Phase 2 terminés !');
        console.log('📋 Vérifiez les logs ci-dessus pour les détails');
        console.log('📈 Les fonctionnalités de monitoring et analytics sont opérationnelles');
        
    } catch (error) {
        console.log('\n💥 Erreur lors de l\'exécution des tests:', error);
    }
}

// Fonctions utilitaires disponibles globalement
window.testPhase2 = {
    runAll: runAllPhase2Tests,
    monitoring: testMonitoringAPIs,
    specialized: testSpecializedAPIs,
    analysis: testDataAnalysis,
    validation: testDataValidation,
    stress: testStress
};

console.log('\n🛠️ Fonctions de test Phase 2 disponibles:');
console.log('- testPhase2.runAll() - Exécuter tous les tests Phase 2');
console.log('- testPhase2.monitoring() - Tester les APIs monitoring');
console.log('- testPhase2.specialized() - Tester les APIs sitemaps spécialisés');
console.log('- testPhase2.analysis() - Analyser les données');
console.log('- testPhase2.validation() - Valider la structure des données');
console.log('- testPhase2.stress() - Test de stress');

console.log('\n🎯 Pour commencer, tapez: testPhase2.runAll()');
