{"version": 3, "names": ["_react", "require", "_inputBase", "_interopRequireDefault", "_inputControlStyles", "backCompatMinWidth", "props", "__nextUnconstrainedWidth", "css", "InputControlContainer", "process", "env", "NODE_ENV", "InputBaseWithBackCompatMinWidth", "_base", "default", "InputBase", "target", "label", "exports"], "sources": ["@wordpress/components/src/custom-select-control/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport InputBase from '../input-control/input-base';\nimport { Container as InputControlContainer } from '../input-control/styles/input-control-styles';\n\ntype BackCompatMinWidthProps = {\n\t__nextUnconstrainedWidth: boolean;\n};\n\nconst backCompatMinWidth = ( props: BackCompatMinWidthProps ) =>\n\t! props.__nextUnconstrainedWidth\n\t\t? css`\n\t\t\t\t${ InputControlContainer } {\n\t\t\t\t\tmin-width: 130px;\n\t\t\t\t}\n\t\t  `\n\t\t: '';\n\nexport const InputBaseWithBackCompatMinWidth = styled( InputBase )`\n\t${ backCompatMinWidth }\n`;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAQA,MAAMI,kBAAkB,GAAKC,KAA8B,IAC1D,CAAEA,KAAK,CAACC,wBAAwB,oBAC7BC,UAAG,EACAC,6BAAqB,0BAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,wDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,0xCAIxB,EAAE;AAEC,MAAMC,+BAA+B,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,kBAAS,EAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAC9Db,kBAAkB,SAAAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxCACrB;AAACO,OAAA,CAAAN,+BAAA,GAAAA,+BAAA"}