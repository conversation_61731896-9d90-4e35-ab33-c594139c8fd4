"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _native = require("@react-navigation/native");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _compose = require("@wordpress/compose");
var _components = require("@wordpress/components");
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BottomSheetTextControl = ({
  initialValue,
  onChange,
  placeholder,
  label,
  icon,
  footerNote,
  cellPlaceholder,
  disabled
}) => {
  const [showSubSheet, setShowSubSheet] = (0, _element.useState)(false);
  const navigation = (0, _native.useNavigation)();
  const goBack = () => {
    setShowSubSheet(false);
    navigation.goBack();
  };
  const openSubSheet = () => {
    navigation.navigate(_components.BottomSheet.SubSheet.screenName);
    setShowSubSheet(true);
  };
  const horizontalBorderStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.horizontalBorder, _styles.default.horizontalBorderDark);
  const textEditorStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.textEditor, _styles.default.textEditorDark);
  return (0, _react.createElement)(_components.BottomSheet.SubSheet, {
    navigationButton: (0, _react.createElement)(_components.BottomSheet.Cell, {
      icon: icon,
      label: label,
      onPress: openSubSheet,
      value: initialValue || '',
      placeholder: cellPlaceholder || placeholder || '',
      disabled: disabled
    }, disabled ? null : (0, _react.createElement)(_icons.Icon, {
      icon: _icons.chevronRight
    })),
    showSheet: showSubSheet
  }, (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_components.BottomSheet.NavBar, null, (0, _react.createElement)(_components.BottomSheet.NavBar.BackButton, {
    onPress: goBack
  }), (0, _react.createElement)(_components.BottomSheet.NavBar.Heading, null, label)), (0, _react.createElement)(_components.PanelBody, {
    style: horizontalBorderStyle
  }, (0, _react.createElement)(_reactNative.TextInput, {
    label: label,
    onChangeText: text => onChange(text),
    defaultValue: initialValue,
    multiline: true,
    placeholder: placeholder,
    placeholderTextColor: '#87a6bc',
    style: textEditorStyle,
    textAlignVertical: 'top'
  }))), footerNote && (0, _react.createElement)(_components.PanelBody, {
    style: _styles.default.textFooternote
  }, (0, _react.createElement)(_components.FooterMessageControl, {
    label: footerNote,
    textAlign: "left"
  })));
};
var _default = BottomSheetTextControl;
exports.default = _default;
//# sourceMappingURL=index.native.js.map