"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _icons = require("@wordpress/icons");
var _colorIndicator = _interopRequireDefault(require("../color-indicator"));
var _icon = _interopRequireDefault(require("../icon"));
var _utils = require("./utils");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function DuotoneSwatch({
  values
}) {
  return values ? (0, _react.createElement)(_colorIndicator.default, {
    colorValue: (0, _utils.getGradientFromCSSColors)(values, '135deg')
  }) : (0, _react.createElement)(_icon.default, {
    icon: _icons.swatch
  });
}
var _default = DuotoneSwatch;
exports.default = _default;
//# sourceMappingURL=duotone-swatch.js.map