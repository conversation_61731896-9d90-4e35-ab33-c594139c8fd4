{"version": 3, "names": ["_dateFns", "require", "inputToDate", "input", "Date", "toDate"], "sources": ["@wordpress/components/src/date-time/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { toDate } from 'date-fns';\n\n/**\n * Like date-fn's toDate, but tries to guess the format when a string is\n * given.\n *\n * @param input Value to turn into a date.\n */\nexport function inputToDate( input: Date | string | number ): Date {\n\tif ( typeof input === 'string' ) {\n\t\treturn new Date( input );\n\t}\n\treturn toDate( input );\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CAAEC,KAA6B,EAAS;EAClE,IAAK,OAAOA,KAAK,KAAK,QAAQ,EAAG;IAChC,OAAO,IAAIC,IAAI,CAAED,KAAM,CAAC;EACzB;EACA,OAAO,IAAAE,eAAM,EAAEF,KAAM,CAAC;AACvB"}