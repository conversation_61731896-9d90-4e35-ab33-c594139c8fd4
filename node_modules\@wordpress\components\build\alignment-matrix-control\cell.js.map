{"version": 3, "names": ["_v", "require", "_tooltip", "_interopRequireDefault", "_visually<PERSON><PERSON>den", "_utils", "_alignmentMatrixControlStyles", "Cell", "id", "isActive", "value", "props", "tooltipText", "ALIGNMENT_LABEL", "_react", "createElement", "default", "text", "CompositeItem", "render", "role", "VisuallyHidden", "Point"], "sources": ["@wordpress/components/src/alignment-matrix-control/cell.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport { CompositeItem } from '../composite/v2';\nimport Tooltip from '../tooltip';\nimport { VisuallyHidden } from '../visually-hidden';\n\n/**\n * Internal dependencies\n */\nimport { ALIGNMENT_LABEL } from './utils';\nimport {\n\tCell as CellView,\n\tPoint,\n} from './styles/alignment-matrix-control-styles';\nimport type { AlignmentMatrixControlCellProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\nexport default function Cell( {\n\tid,\n\tisActive = false,\n\tvalue,\n\t...props\n}: WordPressComponentProps< AlignmentMatrixControlCellProps, 'span', false > ) {\n\tconst tooltipText = ALIGNMENT_LABEL[ value ];\n\n\treturn (\n\t\t<Tooltip text={ tooltipText }>\n\t\t\t<CompositeItem\n\t\t\t\tid={ id }\n\t\t\t\trender={ <CellView { ...props } role=\"gridcell\" /> }\n\t\t\t>\n\t\t\t\t{ /* VoiceOver needs a text content to be rendered within grid cell,\n\t\t\totherwise it'll announce the content as \"blank\". So we use a visually\n\t\t\thidden element instead of aria-label. */ }\n\t\t\t\t<VisuallyHidden>{ value }</VisuallyHidden>\n\t\t\t\t<Point isActive={ isActive } role=\"presentation\" />\n\t\t\t</CompositeItem>\n\t\t</Tooltip>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,EAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AAKA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,6BAAA,GAAAL,OAAA;AAXA;AACA;AACA;;AAKA;AACA;AACA;;AASe,SAASM,IAAIA,CAAE;EAC7BC,EAAE;EACFC,QAAQ,GAAG,KAAK;EAChBC,KAAK;EACL,GAAGC;AACuE,CAAC,EAAG;EAC9E,MAAMC,WAAW,GAAGC,sBAAe,CAAEH,KAAK,CAAE;EAE5C,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACb,QAAA,CAAAc,OAAO;IAACC,IAAI,EAAGL;EAAa,GAC5B,IAAAE,MAAA,CAAAC,aAAA,EAACf,EAAA,CAAAkB,aAAa;IACbV,EAAE,EAAGA,EAAI;IACTW,MAAM,EAAG,IAAAL,MAAA,CAAAC,aAAA,EAACT,6BAAA,CAAAC,IAAQ;MAAA,GAAMI,KAAK;MAAGS,IAAI,EAAC;IAAU,CAAE;EAAG,GAKpD,IAAAN,MAAA,CAAAC,aAAA,EAACX,eAAA,CAAAiB,cAAc,QAAGX,KAAuB,CAAC,EAC1C,IAAAI,MAAA,CAAAC,aAAA,EAACT,6BAAA,CAAAgB,KAAK;IAACb,QAAQ,EAAGA,QAAU;IAACW,IAAI,EAAC;EAAc,CAAE,CACpC,CACP,CAAC;AAEZ"}