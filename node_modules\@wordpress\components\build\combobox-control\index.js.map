{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_i18n", "_element", "_compose", "_a11y", "_icons", "_styles", "_tokenInput", "_suggestionsList", "_baseControl", "_button", "_flex", "_withFocusOutside", "_hooks", "_strings", "_useDeprecatedProps", "noop", "DetectOutside", "withFocusOutside", "Component", "handleFocusOutside", "event", "props", "onFocusOutside", "render", "children", "getIndexOfMatchingSuggestion", "selectedSuggestion", "matchingSuggestions", "indexOf", "ComboboxControl", "_currentOption$label", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "value", "valueProp", "label", "options", "onChange", "onChangeProp", "onFilterValueChange", "hideLabelFromVision", "help", "allowReset", "className", "messages", "selected", "__", "__experimentalRenderItem", "useDeprecated36pxDefaultSizeProp", "setValue", "useControlledValue", "currentOption", "find", "option", "current<PERSON><PERSON><PERSON>", "instanceId", "useInstanceId", "setSelectedSuggestion", "useState", "isExpanded", "setIsExpanded", "inputHasFocus", "setInputHasFocus", "inputValue", "setInputValue", "inputContainer", "useRef", "useMemo", "startsWithMatch", "containsMatch", "match", "normalizeTextString", "for<PERSON>ach", "index", "push", "concat", "onSuggestionSelected", "newSelectedSuggestion", "speak", "handleArrowNavigation", "offset", "nextIndex", "length", "onKeyDown", "preventDefault", "defaultPrevented", "nativeEvent", "isComposing", "keyCode", "code", "onBlur", "onFocus", "onInputChange", "text", "handleOnReset", "current", "focus", "useEffect", "hasMatchingSuggestions", "hasSelectedMatchingSuggestions", "message", "sprintf", "_n", "_react", "createElement", "default", "classnames", "id", "tabIndex", "InputWrapperFlex", "FlexBlock", "ref", "selectedSuggestionIndex", "FlexItem", "icon", "closeSmall", "disabled", "onClick", "displayTransform", "suggestion", "suggestions", "selectedIndex", "onHover", "onSelect", "scrollIntoView", "_default", "exports"], "sources": ["@wordpress/components/src/combobox-control/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { __, _n, sprintf } from '@wordpress/i18n';\nimport {\n\tComponent,\n\tuseState,\n\tuseMemo,\n\tuseRef,\n\tuseEffect,\n} from '@wordpress/element';\nimport { useInstanceId } from '@wordpress/compose';\nimport { speak } from '@wordpress/a11y';\nimport { closeSmall } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport { InputWrapperFlex } from './styles';\nimport TokenInput from '../form-token-field/token-input';\nimport SuggestionsList from '../form-token-field/suggestions-list';\nimport BaseControl from '../base-control';\nimport Button from '../button';\nimport { FlexBlock, FlexItem } from '../flex';\nimport withFocusOutside from '../higher-order/with-focus-outside';\nimport { useControlledValue } from '../utils/hooks';\nimport { normalizeTextString } from '../utils/strings';\nimport type { ComboboxControlOption, ComboboxControlProps } from './types';\nimport type { TokenInputProps } from '../form-token-field/types';\nimport { useDeprecated36pxDefaultSizeProp } from '../utils/use-deprecated-props';\n\nconst noop = () => {};\n\ninterface DetectOutsideComponentProps {\n\tonFocusOutside: ( event: React.FocusEvent ) => void;\n\tchildren?: React.ReactNode;\n}\n\nconst DetectOutside = withFocusOutside(\n\tclass extends Component< DetectOutsideComponentProps > {\n\t\thandleFocusOutside( event: React.FocusEvent ) {\n\t\t\tthis.props.onFocusOutside( event );\n\t\t}\n\n\t\trender() {\n\t\t\treturn this.props.children;\n\t\t}\n\t}\n);\n\nconst getIndexOfMatchingSuggestion = (\n\tselectedSuggestion: ComboboxControlOption | null,\n\tmatchingSuggestions: ComboboxControlOption[]\n) =>\n\tselectedSuggestion === null\n\t\t? -1\n\t\t: matchingSuggestions.indexOf( selectedSuggestion );\n\n/**\n * `ComboboxControl` is an enhanced version of a [`SelectControl`](../select-control/README.md) with the addition of\n * being able to search for options using a search input.\n *\n * ```jsx\n * import { ComboboxControl } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const options = [\n * \t{\n * \t\tvalue: 'small',\n * \t\tlabel: 'Small',\n * \t},\n * \t{\n * \t\tvalue: 'normal',\n * \t\tlabel: 'Normal',\n * \t},\n * \t{\n * \t\tvalue: 'large',\n * \t\tlabel: 'Large',\n * \t},\n * ];\n *\n * function MyComboboxControl() {\n * \tconst [ fontSize, setFontSize ] = useState();\n * \tconst [ filteredOptions, setFilteredOptions ] = useState( options );\n * \treturn (\n * \t\t<ComboboxControl\n * \t\t\tlabel=\"Font Size\"\n * \t\t\tvalue={ fontSize }\n * \t\t\tonChange={ setFontSize }\n * \t\t\toptions={ filteredOptions }\n * \t\t\tonFilterValueChange={ ( inputValue ) =>\n * \t\t\t\tsetFilteredOptions(\n * \t\t\t\t\toptions.filter( ( option ) =>\n * \t\t\t\t\t\toption.label\n * \t\t\t\t\t\t\t.toLowerCase()\n * \t\t\t\t\t\t\t.startsWith( inputValue.toLowerCase() )\n * \t\t\t\t\t)\n * \t\t\t\t)\n * \t\t\t}\n * \t\t/>\n * \t);\n * }\n * ```\n */\nfunction ComboboxControl( props: ComboboxControlProps ) {\n\tconst {\n\t\t__nextHasNoMarginBottom = false,\n\t\t__next40pxDefaultSize = false,\n\t\tvalue: valueProp,\n\t\tlabel,\n\t\toptions,\n\t\tonChange: onChangeProp,\n\t\tonFilterValueChange = noop,\n\t\thideLabelFromVision,\n\t\thelp,\n\t\tallowReset = true,\n\t\tclassName,\n\t\tmessages = {\n\t\t\tselected: __( 'Item selected.' ),\n\t\t},\n\t\t__experimentalRenderItem,\n\t} = useDeprecated36pxDefaultSizeProp< ComboboxControlProps >(\n\t\tprops,\n\t\t'wp.components.ComboboxControl'\n\t);\n\n\tconst [ value, setValue ] = useControlledValue( {\n\t\tvalue: valueProp,\n\t\tonChange: onChangeProp,\n\t} );\n\n\tconst currentOption = options.find( ( option ) => option.value === value );\n\tconst currentLabel = currentOption?.label ?? '';\n\t// Use a custom prefix when generating the `instanceId` to avoid having\n\t// duplicate input IDs when rendering this component and `FormTokenField`\n\t// in the same page (see https://github.com/WordPress/gutenberg/issues/42112).\n\tconst instanceId = useInstanceId( ComboboxControl, 'combobox-control' );\n\tconst [ selectedSuggestion, setSelectedSuggestion ] = useState(\n\t\tcurrentOption || null\n\t);\n\tconst [ isExpanded, setIsExpanded ] = useState( false );\n\tconst [ inputHasFocus, setInputHasFocus ] = useState( false );\n\tconst [ inputValue, setInputValue ] = useState( '' );\n\tconst inputContainer = useRef< HTMLInputElement >( null );\n\n\tconst matchingSuggestions = useMemo( () => {\n\t\tconst startsWithMatch: ComboboxControlOption[] = [];\n\t\tconst containsMatch: ComboboxControlOption[] = [];\n\t\tconst match = normalizeTextString( inputValue );\n\t\toptions.forEach( ( option ) => {\n\t\t\tconst index = normalizeTextString( option.label ).indexOf( match );\n\t\t\tif ( index === 0 ) {\n\t\t\t\tstartsWithMatch.push( option );\n\t\t\t} else if ( index > 0 ) {\n\t\t\t\tcontainsMatch.push( option );\n\t\t\t}\n\t\t} );\n\n\t\treturn startsWithMatch.concat( containsMatch );\n\t}, [ inputValue, options ] );\n\n\tconst onSuggestionSelected = (\n\t\tnewSelectedSuggestion: ComboboxControlOption\n\t) => {\n\t\tsetValue( newSelectedSuggestion.value );\n\t\tspeak( messages.selected, 'assertive' );\n\t\tsetSelectedSuggestion( newSelectedSuggestion );\n\t\tsetInputValue( '' );\n\t\tsetIsExpanded( false );\n\t};\n\n\tconst handleArrowNavigation = ( offset = 1 ) => {\n\t\tconst index = getIndexOfMatchingSuggestion(\n\t\t\tselectedSuggestion,\n\t\t\tmatchingSuggestions\n\t\t);\n\t\tlet nextIndex = index + offset;\n\t\tif ( nextIndex < 0 ) {\n\t\t\tnextIndex = matchingSuggestions.length - 1;\n\t\t} else if ( nextIndex >= matchingSuggestions.length ) {\n\t\t\tnextIndex = 0;\n\t\t}\n\t\tsetSelectedSuggestion( matchingSuggestions[ nextIndex ] );\n\t\tsetIsExpanded( true );\n\t};\n\n\tconst onKeyDown: React.KeyboardEventHandler< HTMLDivElement > = (\n\t\tevent\n\t) => {\n\t\tlet preventDefault = false;\n\n\t\tif (\n\t\t\tevent.defaultPrevented ||\n\t\t\t// Ignore keydowns from IMEs\n\t\t\tevent.nativeEvent.isComposing ||\n\t\t\t// Workaround for Mac Safari where the final Enter/Backspace of an IME composition\n\t\t\t// is `isComposing=false`, even though it's technically still part of the composition.\n\t\t\t// These can only be detected by keyCode.\n\t\t\tevent.keyCode === 229\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tswitch ( event.code ) {\n\t\t\tcase 'Enter':\n\t\t\t\tif ( selectedSuggestion ) {\n\t\t\t\t\tonSuggestionSelected( selectedSuggestion );\n\t\t\t\t\tpreventDefault = true;\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'ArrowUp':\n\t\t\t\thandleArrowNavigation( -1 );\n\t\t\t\tpreventDefault = true;\n\t\t\t\tbreak;\n\t\t\tcase 'ArrowDown':\n\t\t\t\thandleArrowNavigation( 1 );\n\t\t\t\tpreventDefault = true;\n\t\t\t\tbreak;\n\t\t\tcase 'Escape':\n\t\t\t\tsetIsExpanded( false );\n\t\t\t\tsetSelectedSuggestion( null );\n\t\t\t\tpreventDefault = true;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\n\t\tif ( preventDefault ) {\n\t\t\tevent.preventDefault();\n\t\t}\n\t};\n\n\tconst onBlur = () => {\n\t\tsetInputHasFocus( false );\n\t};\n\n\tconst onFocus = () => {\n\t\tsetInputHasFocus( true );\n\t\tsetIsExpanded( true );\n\t\tonFilterValueChange( '' );\n\t\tsetInputValue( '' );\n\t};\n\n\tconst onFocusOutside = () => {\n\t\tsetIsExpanded( false );\n\t};\n\n\tconst onInputChange: TokenInputProps[ 'onChange' ] = ( event ) => {\n\t\tconst text = event.value;\n\t\tsetInputValue( text );\n\t\tonFilterValueChange( text );\n\t\tif ( inputHasFocus ) {\n\t\t\tsetIsExpanded( true );\n\t\t}\n\t};\n\n\tconst handleOnReset = () => {\n\t\tsetValue( null );\n\t\tinputContainer.current?.focus();\n\t};\n\n\t// Update current selections when the filter input changes.\n\tuseEffect( () => {\n\t\tconst hasMatchingSuggestions = matchingSuggestions.length > 0;\n\t\tconst hasSelectedMatchingSuggestions =\n\t\t\tgetIndexOfMatchingSuggestion(\n\t\t\t\tselectedSuggestion,\n\t\t\t\tmatchingSuggestions\n\t\t\t) > 0;\n\n\t\tif ( hasMatchingSuggestions && ! hasSelectedMatchingSuggestions ) {\n\t\t\t// If the current selection isn't present in the list of suggestions, then automatically select the first item from the list of suggestions.\n\t\t\tsetSelectedSuggestion( matchingSuggestions[ 0 ] );\n\t\t}\n\t}, [ matchingSuggestions, selectedSuggestion ] );\n\n\t// Announcements.\n\tuseEffect( () => {\n\t\tconst hasMatchingSuggestions = matchingSuggestions.length > 0;\n\t\tif ( isExpanded ) {\n\t\t\tconst message = hasMatchingSuggestions\n\t\t\t\t? sprintf(\n\t\t\t\t\t\t/* translators: %d: number of results. */\n\t\t\t\t\t\t_n(\n\t\t\t\t\t\t\t'%d result found, use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\t'%d results found, use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\tmatchingSuggestions.length\n\t\t\t\t\t\t),\n\t\t\t\t\t\tmatchingSuggestions.length\n\t\t\t\t  )\n\t\t\t\t: __( 'No results.' );\n\n\t\t\tspeak( message, 'polite' );\n\t\t}\n\t}, [ matchingSuggestions, isExpanded ] );\n\n\t// Disable reason: There is no appropriate role which describes the\n\t// input container intended accessible usability.\n\t// TODO: Refactor click detection to use blur to stop propagation.\n\t/* eslint-disable jsx-a11y/no-static-element-interactions */\n\treturn (\n\t\t<DetectOutside onFocusOutside={ onFocusOutside }>\n\t\t\t<BaseControl\n\t\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t\tclassName={ classnames(\n\t\t\t\t\tclassName,\n\t\t\t\t\t'components-combobox-control'\n\t\t\t\t) }\n\t\t\t\tlabel={ label }\n\t\t\t\tid={ `components-form-token-input-${ instanceId }` }\n\t\t\t\thideLabelFromVision={ hideLabelFromVision }\n\t\t\t\thelp={ help }\n\t\t\t>\n\t\t\t\t<div\n\t\t\t\t\tclassName=\"components-combobox-control__suggestions-container\"\n\t\t\t\t\ttabIndex={ -1 }\n\t\t\t\t\tonKeyDown={ onKeyDown }\n\t\t\t\t>\n\t\t\t\t\t<InputWrapperFlex\n\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t>\n\t\t\t\t\t\t<FlexBlock>\n\t\t\t\t\t\t\t<TokenInput\n\t\t\t\t\t\t\t\tclassName=\"components-combobox-control__input\"\n\t\t\t\t\t\t\t\tinstanceId={ instanceId }\n\t\t\t\t\t\t\t\tref={ inputContainer }\n\t\t\t\t\t\t\t\tvalue={ isExpanded ? inputValue : currentLabel }\n\t\t\t\t\t\t\t\tonFocus={ onFocus }\n\t\t\t\t\t\t\t\tonBlur={ onBlur }\n\t\t\t\t\t\t\t\tisExpanded={ isExpanded }\n\t\t\t\t\t\t\t\tselectedSuggestionIndex={ getIndexOfMatchingSuggestion(\n\t\t\t\t\t\t\t\t\tselectedSuggestion,\n\t\t\t\t\t\t\t\t\tmatchingSuggestions\n\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\tonChange={ onInputChange }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</FlexBlock>\n\t\t\t\t\t\t{ allowReset && (\n\t\t\t\t\t\t\t<FlexItem>\n\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\tclassName=\"components-combobox-control__reset\"\n\t\t\t\t\t\t\t\t\ticon={ closeSmall }\n\t\t\t\t\t\t\t\t\tdisabled={ ! value }\n\t\t\t\t\t\t\t\t\tonClick={ handleOnReset }\n\t\t\t\t\t\t\t\t\tlabel={ __( 'Reset' ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</FlexItem>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</InputWrapperFlex>\n\t\t\t\t\t{ isExpanded && (\n\t\t\t\t\t\t<SuggestionsList\n\t\t\t\t\t\t\tinstanceId={ instanceId }\n\t\t\t\t\t\t\t// The empty string for `value` here is not actually used, but is\n\t\t\t\t\t\t\t// just a quick way to satisfy the TypeScript requirements of SuggestionsList.\n\t\t\t\t\t\t\t// See: https://github.com/WordPress/gutenberg/pull/47581/files#r1091089330\n\t\t\t\t\t\t\tmatch={ { label: inputValue, value: '' } }\n\t\t\t\t\t\t\tdisplayTransform={ ( suggestion ) =>\n\t\t\t\t\t\t\t\tsuggestion.label\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tsuggestions={ matchingSuggestions }\n\t\t\t\t\t\t\tselectedIndex={ getIndexOfMatchingSuggestion(\n\t\t\t\t\t\t\t\tselectedSuggestion,\n\t\t\t\t\t\t\t\tmatchingSuggestions\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\tonHover={ setSelectedSuggestion }\n\t\t\t\t\t\t\tonSelect={ onSuggestionSelected }\n\t\t\t\t\t\t\tscrollIntoView\n\t\t\t\t\t\t\t__experimentalRenderItem={\n\t\t\t\t\t\t\t\t__experimentalRenderItem\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t</div>\n\t\t\t</BaseControl>\n\t\t</DetectOutside>\n\t);\n\t/* eslint-enable jsx-a11y/no-static-element-interactions */\n}\n\nexport default ComboboxControl;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAOA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAKA,IAAAM,OAAA,GAAAN,OAAA;AACA,IAAAO,WAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,gBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,YAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,OAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AACA,IAAAY,iBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,MAAA,GAAAb,OAAA;AACA,IAAAc,QAAA,GAAAd,OAAA;AAGA,IAAAe,mBAAA,GAAAf,OAAA;AAlCA;AACA;AACA;;AAGA;AACA;AACA;;AAaA;AACA;AACA;;AAcA,MAAMgB,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAOrB,MAAMC,aAAa,GAAG,IAAAC,yBAAgB,EACrC,cAAcC,kBAAS,CAAgC;EACtDC,kBAAkBA,CAAEC,KAAuB,EAAG;IAC7C,IAAI,CAACC,KAAK,CAACC,cAAc,CAAEF,KAAM,CAAC;EACnC;EAEAG,MAAMA,CAAA,EAAG;IACR,OAAO,IAAI,CAACF,KAAK,CAACG,QAAQ;EAC3B;AACD,CACD,CAAC;AAED,MAAMC,4BAA4B,GAAGA,CACpCC,kBAAgD,EAChDC,mBAA4C,KAE5CD,kBAAkB,KAAK,IAAI,GACxB,CAAC,CAAC,GACFC,mBAAmB,CAACC,OAAO,CAAEF,kBAAmB,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAAER,KAA2B,EAAG;EAAA,IAAAS,oBAAA;EACvD,MAAM;IACLC,uBAAuB,GAAG,KAAK;IAC/BC,qBAAqB,GAAG,KAAK;IAC7BC,KAAK,EAAEC,SAAS;IAChBC,KAAK;IACLC,OAAO;IACPC,QAAQ,EAAEC,YAAY;IACtBC,mBAAmB,GAAGxB,IAAI;IAC1ByB,mBAAmB;IACnBC,IAAI;IACJC,UAAU,GAAG,IAAI;IACjBC,SAAS;IACTC,QAAQ,GAAG;MACVC,QAAQ,EAAE,IAAAC,QAAE,EAAE,gBAAiB;IAChC,CAAC;IACDC;EACD,CAAC,GAAG,IAAAC,oDAAgC,EACnC3B,KAAK,EACL,+BACD,CAAC;EAED,MAAM,CAAEY,KAAK,EAAEgB,QAAQ,CAAE,GAAG,IAAAC,yBAAkB,EAAE;IAC/CjB,KAAK,EAAEC,SAAS;IAChBG,QAAQ,EAAEC;EACX,CAAE,CAAC;EAEH,MAAMa,aAAa,GAAGf,OAAO,CAACgB,IAAI,CAAIC,MAAM,IAAMA,MAAM,CAACpB,KAAK,KAAKA,KAAM,CAAC;EAC1E,MAAMqB,YAAY,IAAAxB,oBAAA,GAAGqB,aAAa,EAAEhB,KAAK,cAAAL,oBAAA,cAAAA,oBAAA,GAAI,EAAE;EAC/C;EACA;EACA;EACA,MAAMyB,UAAU,GAAG,IAAAC,sBAAa,EAAE3B,eAAe,EAAE,kBAAmB,CAAC;EACvE,MAAM,CAAEH,kBAAkB,EAAE+B,qBAAqB,CAAE,GAAG,IAAAC,iBAAQ,EAC7DP,aAAa,IAAI,IAClB,CAAC;EACD,MAAM,CAAEQ,UAAU,EAAEC,aAAa,CAAE,GAAG,IAAAF,iBAAQ,EAAE,KAAM,CAAC;EACvD,MAAM,CAAEG,aAAa,EAAEC,gBAAgB,CAAE,GAAG,IAAAJ,iBAAQ,EAAE,KAAM,CAAC;EAC7D,MAAM,CAAEK,UAAU,EAAEC,aAAa,CAAE,GAAG,IAAAN,iBAAQ,EAAE,EAAG,CAAC;EACpD,MAAMO,cAAc,GAAG,IAAAC,eAAM,EAAsB,IAAK,CAAC;EAEzD,MAAMvC,mBAAmB,GAAG,IAAAwC,gBAAO,EAAE,MAAM;IAC1C,MAAMC,eAAwC,GAAG,EAAE;IACnD,MAAMC,aAAsC,GAAG,EAAE;IACjD,MAAMC,KAAK,GAAG,IAAAC,4BAAmB,EAAER,UAAW,CAAC;IAC/C3B,OAAO,CAACoC,OAAO,CAAInB,MAAM,IAAM;MAC9B,MAAMoB,KAAK,GAAG,IAAAF,4BAAmB,EAAElB,MAAM,CAAClB,KAAM,CAAC,CAACP,OAAO,CAAE0C,KAAM,CAAC;MAClE,IAAKG,KAAK,KAAK,CAAC,EAAG;QAClBL,eAAe,CAACM,IAAI,CAAErB,MAAO,CAAC;MAC/B,CAAC,MAAM,IAAKoB,KAAK,GAAG,CAAC,EAAG;QACvBJ,aAAa,CAACK,IAAI,CAAErB,MAAO,CAAC;MAC7B;IACD,CAAE,CAAC;IAEH,OAAOe,eAAe,CAACO,MAAM,CAAEN,aAAc,CAAC;EAC/C,CAAC,EAAE,CAAEN,UAAU,EAAE3B,OAAO,CAAG,CAAC;EAE5B,MAAMwC,oBAAoB,GACzBC,qBAA4C,IACxC;IACJ5B,QAAQ,CAAE4B,qBAAqB,CAAC5C,KAAM,CAAC;IACvC,IAAA6C,WAAK,EAAElC,QAAQ,CAACC,QAAQ,EAAE,WAAY,CAAC;IACvCY,qBAAqB,CAAEoB,qBAAsB,CAAC;IAC9Cb,aAAa,CAAE,EAAG,CAAC;IACnBJ,aAAa,CAAE,KAAM,CAAC;EACvB,CAAC;EAED,MAAMmB,qBAAqB,GAAGA,CAAEC,MAAM,GAAG,CAAC,KAAM;IAC/C,MAAMP,KAAK,GAAGhD,4BAA4B,CACzCC,kBAAkB,EAClBC,mBACD,CAAC;IACD,IAAIsD,SAAS,GAAGR,KAAK,GAAGO,MAAM;IAC9B,IAAKC,SAAS,GAAG,CAAC,EAAG;MACpBA,SAAS,GAAGtD,mBAAmB,CAACuD,MAAM,GAAG,CAAC;IAC3C,CAAC,MAAM,IAAKD,SAAS,IAAItD,mBAAmB,CAACuD,MAAM,EAAG;MACrDD,SAAS,GAAG,CAAC;IACd;IACAxB,qBAAqB,CAAE9B,mBAAmB,CAAEsD,SAAS,CAAG,CAAC;IACzDrB,aAAa,CAAE,IAAK,CAAC;EACtB,CAAC;EAED,MAAMuB,SAAuD,GAC5D/D,KAAK,IACD;IACJ,IAAIgE,cAAc,GAAG,KAAK;IAE1B,IACChE,KAAK,CAACiE,gBAAgB;IACtB;IACAjE,KAAK,CAACkE,WAAW,CAACC,WAAW;IAC7B;IACA;IACA;IACAnE,KAAK,CAACoE,OAAO,KAAK,GAAG,EACpB;MACD;IACD;IAEA,QAASpE,KAAK,CAACqE,IAAI;MAClB,KAAK,OAAO;QACX,IAAK/D,kBAAkB,EAAG;UACzBkD,oBAAoB,CAAElD,kBAAmB,CAAC;UAC1C0D,cAAc,GAAG,IAAI;QACtB;QACA;MACD,KAAK,SAAS;QACbL,qBAAqB,CAAE,CAAC,CAAE,CAAC;QAC3BK,cAAc,GAAG,IAAI;QACrB;MACD,KAAK,WAAW;QACfL,qBAAqB,CAAE,CAAE,CAAC;QAC1BK,cAAc,GAAG,IAAI;QACrB;MACD,KAAK,QAAQ;QACZxB,aAAa,CAAE,KAAM,CAAC;QACtBH,qBAAqB,CAAE,IAAK,CAAC;QAC7B2B,cAAc,GAAG,IAAI;QACrB;MACD;QACC;IACF;IAEA,IAAKA,cAAc,EAAG;MACrBhE,KAAK,CAACgE,cAAc,CAAC,CAAC;IACvB;EACD,CAAC;EAED,MAAMM,MAAM,GAAGA,CAAA,KAAM;IACpB5B,gBAAgB,CAAE,KAAM,CAAC;EAC1B,CAAC;EAED,MAAM6B,OAAO,GAAGA,CAAA,KAAM;IACrB7B,gBAAgB,CAAE,IAAK,CAAC;IACxBF,aAAa,CAAE,IAAK,CAAC;IACrBrB,mBAAmB,CAAE,EAAG,CAAC;IACzByB,aAAa,CAAE,EAAG,CAAC;EACpB,CAAC;EAED,MAAM1C,cAAc,GAAGA,CAAA,KAAM;IAC5BsC,aAAa,CAAE,KAAM,CAAC;EACvB,CAAC;EAED,MAAMgC,aAA4C,GAAKxE,KAAK,IAAM;IACjE,MAAMyE,IAAI,GAAGzE,KAAK,CAACa,KAAK;IACxB+B,aAAa,CAAE6B,IAAK,CAAC;IACrBtD,mBAAmB,CAAEsD,IAAK,CAAC;IAC3B,IAAKhC,aAAa,EAAG;MACpBD,aAAa,CAAE,IAAK,CAAC;IACtB;EACD,CAAC;EAED,MAAMkC,aAAa,GAAGA,CAAA,KAAM;IAC3B7C,QAAQ,CAAE,IAAK,CAAC;IAChBgB,cAAc,CAAC8B,OAAO,EAAEC,KAAK,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,IAAAC,kBAAS,EAAE,MAAM;IAChB,MAAMC,sBAAsB,GAAGvE,mBAAmB,CAACuD,MAAM,GAAG,CAAC;IAC7D,MAAMiB,8BAA8B,GACnC1E,4BAA4B,CAC3BC,kBAAkB,EAClBC,mBACD,CAAC,GAAG,CAAC;IAEN,IAAKuE,sBAAsB,IAAI,CAAEC,8BAA8B,EAAG;MACjE;MACA1C,qBAAqB,CAAE9B,mBAAmB,CAAE,CAAC,CAAG,CAAC;IAClD;EACD,CAAC,EAAE,CAAEA,mBAAmB,EAAED,kBAAkB,CAAG,CAAC;;EAEhD;EACA,IAAAuE,kBAAS,EAAE,MAAM;IAChB,MAAMC,sBAAsB,GAAGvE,mBAAmB,CAACuD,MAAM,GAAG,CAAC;IAC7D,IAAKvB,UAAU,EAAG;MACjB,MAAMyC,OAAO,GAAGF,sBAAsB,GACnC,IAAAG,aAAO,GACP;MACA,IAAAC,QAAE,EACD,0DAA0D,EAC1D,2DAA2D,EAC3D3E,mBAAmB,CAACuD,MACrB,CAAC,EACDvD,mBAAmB,CAACuD,MACpB,CAAC,GACD,IAAApC,QAAE,EAAE,aAAc,CAAC;MAEtB,IAAAgC,WAAK,EAAEsB,OAAO,EAAE,QAAS,CAAC;IAC3B;EACD,CAAC,EAAE,CAAEzE,mBAAmB,EAAEgC,UAAU,CAAG,CAAC;;EAExC;EACA;EACA;EACA;EACA,OACC,IAAA4C,MAAA,CAAAC,aAAA,EAACxF,aAAa;IAACM,cAAc,EAAGA;EAAgB,GAC/C,IAAAiF,MAAA,CAAAC,aAAA,EAAChG,YAAA,CAAAiG,OAAW;IACX1E,uBAAuB,EAAGA,uBAAyB;IACnDY,SAAS,EAAG,IAAA+D,mBAAU,EACrB/D,SAAS,EACT,6BACD,CAAG;IACHR,KAAK,EAAGA,KAAO;IACfwE,EAAE,EAAI,+BAA+BpD,UAAY,EAAG;IACpDf,mBAAmB,EAAGA,mBAAqB;IAC3CC,IAAI,EAAGA;EAAM,GAEb,IAAA8D,MAAA,CAAAC,aAAA;IACC7D,SAAS,EAAC,oDAAoD;IAC9DiE,QAAQ,EAAG,CAAC,CAAG;IACfzB,SAAS,EAAGA;EAAW,GAEvB,IAAAoB,MAAA,CAAAC,aAAA,EAACnG,OAAA,CAAAwG,gBAAgB;IAChB7E,qBAAqB,EAAGA;EAAuB,GAE/C,IAAAuE,MAAA,CAAAC,aAAA,EAAC9F,KAAA,CAAAoG,SAAS,QACT,IAAAP,MAAA,CAAAC,aAAA,EAAClG,WAAA,CAAAmG,OAAU;IACV9D,SAAS,EAAC,oCAAoC;IAC9CY,UAAU,EAAGA,UAAY;IACzBwD,GAAG,EAAG9C,cAAgB;IACtBhC,KAAK,EAAG0B,UAAU,GAAGI,UAAU,GAAGT,YAAc;IAChDqC,OAAO,EAAGA,OAAS;IACnBD,MAAM,EAAGA,MAAQ;IACjB/B,UAAU,EAAGA,UAAY;IACzBqD,uBAAuB,EAAGvF,4BAA4B,CACrDC,kBAAkB,EAClBC,mBACD,CAAG;IACHU,QAAQ,EAAGuD;EAAe,CAC1B,CACS,CAAC,EACVlD,UAAU,IACX,IAAA6D,MAAA,CAAAC,aAAA,EAAC9F,KAAA,CAAAuG,QAAQ,QACR,IAAAV,MAAA,CAAAC,aAAA,EAAC/F,OAAA,CAAAgG,OAAM;IACN9D,SAAS,EAAC,oCAAoC;IAC9CuE,IAAI,EAAGC,iBAAY;IACnBC,QAAQ,EAAG,CAAEnF,KAAO;IACpBoF,OAAO,EAAGvB,aAAe;IACzB3D,KAAK,EAAG,IAAAW,QAAE,EAAE,OAAQ;EAAG,CACvB,CACQ,CAEM,CAAC,EACjBa,UAAU,IACX,IAAA4C,MAAA,CAAAC,aAAA,EAACjG,gBAAA,CAAAkG,OAAe;IACflD,UAAU,EAAGA;IACb;IACA;IACA;IAAA;IACAe,KAAK,EAAG;MAAEnC,KAAK,EAAE4B,UAAU;MAAE9B,KAAK,EAAE;IAAG,CAAG;IAC1CqF,gBAAgB,EAAKC,UAAU,IAC9BA,UAAU,CAACpF,KACX;IACDqF,WAAW,EAAG7F,mBAAqB;IACnC8F,aAAa,EAAGhG,4BAA4B,CAC3CC,kBAAkB,EAClBC,mBACD,CAAG;IACH+F,OAAO,EAAGjE,qBAAuB;IACjCkE,QAAQ,EAAG/C,oBAAsB;IACjCgD,cAAc;IACd7E,wBAAwB,EACvBA;EACA,CACD,CAEE,CACO,CACC,CAAC;EAEjB;AACD;AAAC,IAAA8E,QAAA,GAEchG,eAAe;AAAAiG,OAAA,CAAArB,OAAA,GAAAoB,QAAA"}