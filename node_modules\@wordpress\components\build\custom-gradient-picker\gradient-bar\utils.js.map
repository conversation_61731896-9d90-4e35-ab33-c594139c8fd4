{"version": 3, "names": ["_constants", "require", "clampPercent", "value", "Math", "max", "min", "isOverlapping", "initialIndex", "newPosition", "minDistance", "MINIMUM_DISTANCE_BETWEEN_POINTS", "initialPosition", "position", "minPosition", "maxPosition", "some", "index", "abs", "addControlPoint", "points", "color", "nextIndex", "findIndex", "point", "newPoint", "newPoints", "slice", "splice", "removeControlPoint", "filter", "_point", "pointIndex", "updateControlPoint", "newValue", "updateControlPointPosition", "updateControlPointColor", "newColor", "updateControlPointColorByPosition", "getHorizontalRelativeGradientPosition", "mouseXCoordinate", "containerElement", "x", "width", "getBoundingClientRect", "absolutePositionValue", "round"], "sources": ["@wordpress/components/src/custom-gradient-picker/gradient-bar/utils.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport { MINIMUM_DISTANCE_BETWEEN_POINTS } from './constants';\nimport type { ControlPoint } from '../types';\n\n/**\n * Clamps a number between 0 and 100.\n *\n * @param value Value to clamp.\n *\n * @return Value clamped between 0 and 100.\n */\nexport function clampPercent( value: number ) {\n\treturn Math.max( 0, Math.min( 100, value ) );\n}\n\n/**\n * Check if a control point is overlapping with another.\n *\n * @param value        Array of control points.\n * @param initialIndex Index of the position to test.\n * @param newPosition  New position of the control point.\n * @param minDistance  Distance considered to be overlapping.\n *\n * @return True if the point is overlapping.\n */\nexport function isOverlapping(\n\tvalue: ControlPoint[],\n\tinitialIndex: number,\n\tnewPosition: number,\n\tminDistance: number = MINIMUM_DISTANCE_BETWEEN_POINTS\n) {\n\tconst initialPosition = value[ initialIndex ].position;\n\tconst minPosition = Math.min( initialPosition, newPosition );\n\tconst maxPosition = Math.max( initialPosition, newPosition );\n\n\treturn value.some( ( { position }, index ) => {\n\t\treturn (\n\t\t\tindex !== initialIndex &&\n\t\t\t( Math.abs( position - newPosition ) < minDistance ||\n\t\t\t\t( minPosition < position && position < maxPosition ) )\n\t\t);\n\t} );\n}\n\n/**\n * Adds a control point from an array and returns the new array.\n *\n * @param points   Array of control points.\n * @param position Position to insert the new point.\n * @param color    Color to update the control point at index.\n *\n * @return New array of control points.\n */\nexport function addControlPoint(\n\tpoints: ControlPoint[],\n\tposition: number,\n\tcolor: ControlPoint[ 'color' ]\n) {\n\tconst nextIndex = points.findIndex(\n\t\t( point ) => point.position > position\n\t);\n\tconst newPoint = { color, position };\n\tconst newPoints = points.slice();\n\tnewPoints.splice( nextIndex - 1, 0, newPoint );\n\treturn newPoints;\n}\n\n/**\n * Removes a control point from an array and returns the new array.\n *\n * @param points Array of control points.\n * @param index  Index to remove.\n *\n * @return New array of control points.\n */\nexport function removeControlPoint( points: ControlPoint[], index: number ) {\n\treturn points.filter( ( _point, pointIndex ) => {\n\t\treturn pointIndex !== index;\n\t} );\n}\n/**\n * Updates a control point from an array and returns the new array.\n *\n * @param points   Array of control points.\n * @param index    Index to update.\n * @param newPoint New control point to replace the index.\n *\n * @return New array of control points.\n */\nexport function updateControlPoint(\n\tpoints: ControlPoint[],\n\tindex: number,\n\tnewPoint: ControlPoint\n) {\n\tconst newValue = points.slice();\n\tnewValue[ index ] = newPoint;\n\treturn newValue;\n}\n\n/**\n * Updates the position of a control point from an array and returns the new array.\n *\n * @param points      Array of control points.\n * @param index       Index to update.\n * @param newPosition Position to move the control point at index.\n *\n * @return New array of control points.\n */\nexport function updateControlPointPosition(\n\tpoints: ControlPoint[],\n\tindex: number,\n\tnewPosition: ControlPoint[ 'position' ]\n) {\n\tif ( isOverlapping( points, index, newPosition ) ) {\n\t\treturn points;\n\t}\n\tconst newPoint = {\n\t\t...points[ index ],\n\t\tposition: newPosition,\n\t};\n\treturn updateControlPoint( points, index, newPoint );\n}\n\n/**\n * Updates the position of a control point from an array and returns the new array.\n *\n * @param points   Array of control points.\n * @param index    Index to update.\n * @param newColor Color to update the control point at index.\n *\n * @return New array of control points.\n */\nexport function updateControlPointColor(\n\tpoints: ControlPoint[],\n\tindex: number,\n\tnewColor: ControlPoint[ 'color' ]\n) {\n\tconst newPoint = {\n\t\t...points[ index ],\n\t\tcolor: newColor,\n\t};\n\treturn updateControlPoint( points, index, newPoint );\n}\n\n/**\n * Updates the position of a control point from an array and returns the new array.\n *\n * @param points   Array of control points.\n * @param position Position of the color stop.\n * @param newColor Color to update the control point at index.\n *\n * @return New array of control points.\n */\nexport function updateControlPointColorByPosition(\n\tpoints: ControlPoint[],\n\tposition: ControlPoint[ 'position' ],\n\tnewColor: ControlPoint[ 'color' ]\n) {\n\tconst index = points.findIndex( ( point ) => point.position === position );\n\treturn updateControlPointColor( points, index, newColor );\n}\n\n/**\n * Gets the horizontal coordinate when dragging a control point with the mouse.\n *\n * @param mouseXcoordinate Horizontal coordinate of the mouse position.\n * @param containerElement Container for the gradient picker.\n *\n * @return Whole number percentage from the left.\n */\nexport function getHorizontalRelativeGradientPosition(\n\tmouseXcoordinate: number,\n\tcontainerElement: HTMLDivElement\n): number;\nexport function getHorizontalRelativeGradientPosition(\n\tmouseXcoordinate: number,\n\tcontainerElement: null\n): undefined;\nexport function getHorizontalRelativeGradientPosition(\n\tmouseXCoordinate: number,\n\tcontainerElement: HTMLDivElement | null\n) {\n\tif ( ! containerElement ) {\n\t\treturn;\n\t}\n\tconst { x, width } = containerElement.getBoundingClientRect();\n\tconst absolutePositionValue = mouseXCoordinate - x;\n\treturn Math.round(\n\t\tclampPercent( ( absolutePositionValue * 100 ) / width )\n\t);\n}\n"], "mappings": ";;;;;;;;;;;;;;AAGA,IAAAA,UAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,YAAYA,CAAEC,KAAa,EAAG;EAC7C,OAAOC,IAAI,CAACC,GAAG,CAAE,CAAC,EAAED,IAAI,CAACE,GAAG,CAAE,GAAG,EAAEH,KAAM,CAAE,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,aAAaA,CAC5BJ,KAAqB,EACrBK,YAAoB,EACpBC,WAAmB,EACnBC,WAAmB,GAAGC,0CAA+B,EACpD;EACD,MAAMC,eAAe,GAAGT,KAAK,CAAEK,YAAY,CAAE,CAACK,QAAQ;EACtD,MAAMC,WAAW,GAAGV,IAAI,CAACE,GAAG,CAAEM,eAAe,EAAEH,WAAY,CAAC;EAC5D,MAAMM,WAAW,GAAGX,IAAI,CAACC,GAAG,CAAEO,eAAe,EAAEH,WAAY,CAAC;EAE5D,OAAON,KAAK,CAACa,IAAI,CAAE,CAAE;IAAEH;EAAS,CAAC,EAAEI,KAAK,KAAM;IAC7C,OACCA,KAAK,KAAKT,YAAY,KACpBJ,IAAI,CAACc,GAAG,CAAEL,QAAQ,GAAGJ,WAAY,CAAC,GAAGC,WAAW,IAC/CI,WAAW,GAAGD,QAAQ,IAAIA,QAAQ,GAAGE,WAAa,CAAE;EAEzD,CAAE,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,eAAeA,CAC9BC,MAAsB,EACtBP,QAAgB,EAChBQ,KAA8B,EAC7B;EACD,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS,CAC/BC,KAAK,IAAMA,KAAK,CAACX,QAAQ,GAAGA,QAC/B,CAAC;EACD,MAAMY,QAAQ,GAAG;IAAEJ,KAAK;IAAER;EAAS,CAAC;EACpC,MAAMa,SAAS,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC;EAChCD,SAAS,CAACE,MAAM,CAAEN,SAAS,GAAG,CAAC,EAAE,CAAC,EAAEG,QAAS,CAAC;EAC9C,OAAOC,SAAS;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,kBAAkBA,CAAET,MAAsB,EAAEH,KAAa,EAAG;EAC3E,OAAOG,MAAM,CAACU,MAAM,CAAE,CAAEC,MAAM,EAAEC,UAAU,KAAM;IAC/C,OAAOA,UAAU,KAAKf,KAAK;EAC5B,CAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgB,kBAAkBA,CACjCb,MAAsB,EACtBH,KAAa,EACbQ,QAAsB,EACrB;EACD,MAAMS,QAAQ,GAAGd,MAAM,CAACO,KAAK,CAAC,CAAC;EAC/BO,QAAQ,CAAEjB,KAAK,CAAE,GAAGQ,QAAQ;EAC5B,OAAOS,QAAQ;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,0BAA0BA,CACzCf,MAAsB,EACtBH,KAAa,EACbR,WAAuC,EACtC;EACD,IAAKF,aAAa,CAAEa,MAAM,EAAEH,KAAK,EAAER,WAAY,CAAC,EAAG;IAClD,OAAOW,MAAM;EACd;EACA,MAAMK,QAAQ,GAAG;IAChB,GAAGL,MAAM,CAAEH,KAAK,CAAE;IAClBJ,QAAQ,EAAEJ;EACX,CAAC;EACD,OAAOwB,kBAAkB,CAAEb,MAAM,EAAEH,KAAK,EAAEQ,QAAS,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASW,uBAAuBA,CACtChB,MAAsB,EACtBH,KAAa,EACboB,QAAiC,EAChC;EACD,MAAMZ,QAAQ,GAAG;IAChB,GAAGL,MAAM,CAAEH,KAAK,CAAE;IAClBI,KAAK,EAAEgB;EACR,CAAC;EACD,OAAOJ,kBAAkB,CAAEb,MAAM,EAAEH,KAAK,EAAEQ,QAAS,CAAC;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASa,iCAAiCA,CAChDlB,MAAsB,EACtBP,QAAoC,EACpCwB,QAAiC,EAChC;EACD,MAAMpB,KAAK,GAAGG,MAAM,CAACG,SAAS,CAAIC,KAAK,IAAMA,KAAK,CAACX,QAAQ,KAAKA,QAAS,CAAC;EAC1E,OAAOuB,uBAAuB,CAAEhB,MAAM,EAAEH,KAAK,EAAEoB,QAAS,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASO,SAASE,qCAAqCA,CACpDC,gBAAwB,EACxBC,gBAAuC,EACtC;EACD,IAAK,CAAEA,gBAAgB,EAAG;IACzB;EACD;EACA,MAAM;IAAEC,CAAC;IAAEC;EAAM,CAAC,GAAGF,gBAAgB,CAACG,qBAAqB,CAAC,CAAC;EAC7D,MAAMC,qBAAqB,GAAGL,gBAAgB,GAAGE,CAAC;EAClD,OAAOtC,IAAI,CAAC0C,KAAK,CAChB5C,YAAY,CAAI2C,qBAAqB,GAAG,GAAG,GAAKF,KAAM,CACvD,CAAC;AACF"}