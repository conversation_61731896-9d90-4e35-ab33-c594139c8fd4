<?php
/**
 * Gestionnaire de publication de contenu
 * 
 * @package Boss_SEO
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class Boss_SEO_Content_Publisher {

    /**
     * Initialise les hooks
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
        add_action('wp_head', array($this, 'add_seo_meta_tags'));
    }

    /**
     * Enregistre les routes de l'API REST
     */
    public function register_routes() {
        register_rest_route('boss-seo/v1', '/content/publish', array(
            'methods' => 'POST',
            'callback' => array($this, 'publish_content'),
            'permission_callback' => array($this, 'check_permissions'),
            'args' => array(
                'title' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'content' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'wp_kses_post'
                ),
                'excerpt' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field'
                ),
                'status' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'draft',
                    'enum' => array('draft', 'publish', 'pending', 'private')
                ),
                'category' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'tags' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'featured' => array(
                    'required' => false,
                    'type' => 'boolean',
                    'default' => false
                ),
                'comments' => array(
                    'required' => false,
                    'type' => 'boolean',
                    'default' => true
                ),
                'pingbacks' => array(
                    'required' => false,
                    'type' => 'boolean',
                    'default' => false
                )
            )
        ));

        // Route pour sauvegarder en brouillon
        register_rest_route('boss-seo/v1', '/content/draft', array(
            'methods' => 'POST',
            'callback' => array($this, 'save_draft'),
            'permission_callback' => array($this, 'check_draft_permissions'),
            'args' => array(
                'title' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'content' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'wp_kses_post'
                ),
                'excerpt' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_textarea_field'
                )
            )
        ));

        // Route pour importer des images
        register_rest_route('boss-seo/v1', '/images/import', array(
            'methods' => 'POST',
            'callback' => array($this, 'import_images'),
            'permission_callback' => array($this, 'check_draft_permissions'),
            'args' => array(
                'images' => array(
                    'required' => true,
                    'type' => 'array'
                )
            )
        ));
    }

    /**
     * Vérifie les permissions pour publier
     */
    public function check_permissions() {
        return current_user_can('publish_posts');
    }

    /**
     * Vérifie les permissions pour sauvegarder en brouillon
     */
    public function check_draft_permissions() {
        return current_user_can('edit_posts');
    }

    /**
     * Sauvegarde en brouillon
     */
    public function save_draft($request) {
        try {
            $params = $request->get_params();

            // Forcer le statut en brouillon
            $params['status'] = 'draft';

            // Utiliser la même méthode que pour publier
            return $this->publish_content($request);

        } catch (Exception $e) {
            return new WP_Error(
                'draft_save_error',
                'Erreur lors de la sauvegarde du brouillon: ' . $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Publie le contenu
     */
    public function publish_content($request) {
        try {
            $params = $request->get_params();
            
            // Préparer les données du post
            $post_data = array(
                'post_title' => $params['title'],
                'post_content' => $params['content'],
                'post_excerpt' => isset($params['excerpt']) ? $params['excerpt'] : '',
                'post_status' => $params['status'],
                'post_type' => 'post',
                'post_author' => get_current_user_id(),
                'comment_status' => $params['comments'] ? 'open' : 'closed',
                'ping_status' => $params['pingbacks'] ? 'open' : 'closed'
            );

            // Créer le post
            $post_id = wp_insert_post($post_data);

            if (is_wp_error($post_id)) {
                return new WP_Error(
                    'post_creation_failed',
                    'Erreur lors de la création du post: ' . $post_id->get_error_message(),
                    array('status' => 500)
                );
            }

            // Assigner la catégorie
            if (!empty($params['category']) && is_numeric($params['category'])) {
                wp_set_post_categories($post_id, array(intval($params['category'])));
            }

            // Assigner les tags
            if (!empty($params['tags'])) {
                $tags = array_map('trim', explode(',', $params['tags']));
                wp_set_post_tags($post_id, $tags);
            }

            // Définir comme article en vedette
            if ($params['featured']) {
                set_post_thumbnail($post_id, 0); // Vous pouvez gérer l'image en vedette ici
            }

            // Sauvegarder les métadonnées SEO
            if (isset($params['meta'])) {
                $meta = $params['meta'];

                if (isset($meta['focus_keyword'])) {
                    update_post_meta($post_id, '_boss_seo_focus_keyword', sanitize_text_field($meta['focus_keyword']));
                }
            }

            // Sauvegarder la meta description séparément (pour compatibilité SEO)
            if (!empty($params['excerpt'])) {
                update_post_meta($post_id, '_boss_seo_meta_description', sanitize_textarea_field($params['excerpt']));

                // Aussi compatible avec Yoast SEO si installé
                update_post_meta($post_id, '_yoast_wpseo_metadesc', sanitize_textarea_field($params['excerpt']));

                // Compatible avec RankMath si installé
                update_post_meta($post_id, 'rank_math_description', sanitize_textarea_field($params['excerpt']));
            }

            // Continuer avec les autres métadonnées
            if (isset($params['meta'])) {
                $meta = $params['meta'];
                
                if (isset($meta['secondary_keywords'])) {
                    // Convertir l'array en string si nécessaire
                    $secondary_keywords = is_array($meta['secondary_keywords'])
                        ? implode(', ', $meta['secondary_keywords'])
                        : $meta['secondary_keywords'];
                    update_post_meta($post_id, '_boss_seo_secondary_keywords', sanitize_text_field($secondary_keywords));
                }
                
                if (isset($meta['content_type'])) {
                    update_post_meta($post_id, '_boss_seo_content_type', sanitize_text_field($meta['content_type']));
                }
                
                if (isset($meta['tone'])) {
                    update_post_meta($post_id, '_boss_seo_tone', sanitize_text_field($meta['tone']));
                }
                
                if (isset($meta['audience'])) {
                    update_post_meta($post_id, '_boss_seo_audience', sanitize_text_field($meta['audience']));
                }
                
                if (isset($meta['template_used'])) {
                    update_post_meta($post_id, '_boss_seo_template_used', sanitize_text_field($meta['template_used']));
                }
                
                if (isset($meta['seo_score'])) {
                    update_post_meta($post_id, '_boss_seo_score', $meta['seo_score']);
                }
                
                if (isset($meta['selected_images'])) {
                    update_post_meta($post_id, '_boss_seo_selected_images', $meta['selected_images']);
                }
            }

            // Sauvegarder les données du template
            if (isset($params['template'])) {
                update_post_meta($post_id, '_boss_seo_template_data', $params['template']);
            }

            // Préparer la réponse
            $post = get_post($post_id);
            $edit_url = admin_url('post.php?post=' . $post_id . '&action=edit');
            $view_url = get_permalink($post_id);

            return new WP_REST_Response(array(
                'success' => true,
                'message' => 'Contenu publié avec succès',
                'post_id' => $post_id,
                'edit_url' => $edit_url,
                'view_url' => $view_url,
                'post_status' => $post->post_status,
                'post_title' => $post->post_title,
                'is_simulation' => false
            ), 200);

        } catch (Exception $e) {
            return new WP_Error(
                'publication_error',
                'Erreur lors de la publication: ' . $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Traite les images attachées au contenu
     */
    private function process_images($post_id, $images) {
        if (empty($images) || !is_array($images)) {
            return;
        }

        $attached_images = array();

        foreach ($images as $image) {
            if (isset($image['id']) && is_numeric($image['id'])) {
                // Attacher l'image au post
                wp_update_post(array(
                    'ID' => $image['id'],
                    'post_parent' => $post_id
                ));
                
                $attached_images[] = $image['id'];
            }
        }

        // Sauvegarder la liste des images attachées
        if (!empty($attached_images)) {
            update_post_meta($post_id, '_boss_seo_attached_images', $attached_images);
            
            // Définir la première image comme image en vedette si demandé
            if (isset($images[0]['id'])) {
                set_post_thumbnail($post_id, $images[0]['id']);
            }
        }
    }

    /**
     * Importe des images depuis des URLs externes
     */
    public function import_images($request) {
        try {
            $params = $request->get_params();
            $images = $params['images'];
            $imported_images = array();

            if (!function_exists('media_handle_sideload')) {
                require_once(ABSPATH . 'wp-admin/includes/media.php');
                require_once(ABSPATH . 'wp-admin/includes/file.php');
                require_once(ABSPATH . 'wp-admin/includes/image.php');
            }

            foreach ($images as $image) {
                if (!isset($image['url']) || empty($image['url'])) {
                    continue;
                }

                // Télécharger l'image
                $imported_image = $this->download_and_import_image($image);

                if ($imported_image && !is_wp_error($imported_image)) {
                    $imported_images[] = $imported_image;
                }
            }

            return new WP_REST_Response(array(
                'success' => true,
                'message' => count($imported_images) . ' image(s) importée(s) avec succès',
                'images' => $imported_images
            ), 200);

        } catch (Exception $e) {
            return new WP_Error(
                'import_error',
                'Erreur lors de l\'importation des images: ' . $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Télécharge et importe une image dans la médiathèque
     */
    private function download_and_import_image($image) {
        try {
            $image_url = $image['url'];
            $alt_text = isset($image['alt']) ? $image['alt'] : '';

            // Créer un nom de fichier unique
            $filename = basename(parse_url($image_url, PHP_URL_PATH));
            if (empty($filename) || !preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $filename)) {
                $filename = 'boss-seo-image-' . time() . '.jpg';
            }

            // Télécharger l'image
            $tmp = download_url($image_url);

            if (is_wp_error($tmp)) {
                error_log('Erreur téléchargement image: ' . $tmp->get_error_message());
                return false;
            }

            // Préparer le fichier pour l'upload
            $file_array = array(
                'name' => $filename,
                'tmp_name' => $tmp
            );

            // Importer dans la médiathèque
            $attachment_id = media_handle_sideload($file_array, 0);

            // Nettoyer le fichier temporaire
            @unlink($tmp);

            if (is_wp_error($attachment_id)) {
                error_log('Erreur import médiathèque: ' . $attachment_id->get_error_message());
                return false;
            }

            // Définir le texte alternatif
            if (!empty($alt_text)) {
                update_post_meta($attachment_id, '_wp_attachment_image_alt', $alt_text);
            }

            // Retourner les informations de l'image importée
            return array(
                'id' => $attachment_id,
                'url' => wp_get_attachment_url($attachment_id),
                'thumbnail' => wp_get_attachment_image_url($attachment_id, 'thumbnail'),
                'medium' => wp_get_attachment_image_url($attachment_id, 'medium'),
                'large' => wp_get_attachment_image_url($attachment_id, 'large'),
                'alt' => $alt_text,
                'original_url' => $image_url
            );

        } catch (Exception $e) {
            error_log('Erreur lors du téléchargement de l\'image: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Ajoute les meta tags SEO dans le head
     */
    public function add_seo_meta_tags() {
        // Seulement sur les posts individuels
        if (!is_single()) {
            return;
        }

        global $post;

        // Meta description
        $meta_description = get_post_meta($post->ID, '_boss_seo_meta_description', true);
        if (!empty($meta_description)) {
            echo '<meta name="description" content="' . esc_attr($meta_description) . '">' . "\n";
        }

        // Meta keywords
        $focus_keyword = get_post_meta($post->ID, '_boss_seo_focus_keyword', true);
        $secondary_keywords = get_post_meta($post->ID, '_boss_seo_secondary_keywords', true);

        $all_keywords = array();
        if (!empty($focus_keyword)) {
            $all_keywords[] = $focus_keyword;
        }
        if (!empty($secondary_keywords)) {
            // Si c'est déjà une string avec des virgules, la diviser
            if (is_string($secondary_keywords) && strpos($secondary_keywords, ',') !== false) {
                $secondary_array = array_map('trim', explode(',', $secondary_keywords));
                $all_keywords = array_merge($all_keywords, $secondary_array);
            } else if (is_array($secondary_keywords)) {
                $all_keywords = array_merge($all_keywords, $secondary_keywords);
            } else {
                $all_keywords[] = $secondary_keywords;
            }
        }

        if (!empty($all_keywords)) {
            $keywords_string = implode(', ', array_unique($all_keywords));
            echo '<meta name="keywords" content="' . esc_attr($keywords_string) . '">' . "\n";
        }

        // Meta robots (pour le SEO)
        echo '<meta name="robots" content="index, follow">' . "\n";

        // Open Graph tags
        echo '<meta property="og:title" content="' . esc_attr(get_the_title()) . '">' . "\n";
        if (!empty($meta_description)) {
            echo '<meta property="og:description" content="' . esc_attr($meta_description) . '">' . "\n";
        }
        echo '<meta property="og:type" content="article">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(get_permalink()) . '">' . "\n";

        // Image Open Graph
        if (has_post_thumbnail()) {
            $thumbnail_url = get_the_post_thumbnail_url($post->ID, 'large');
            echo '<meta property="og:image" content="' . esc_url($thumbnail_url) . '">' . "\n";
        }

        // Twitter Card
        echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr(get_the_title()) . '">' . "\n";
        if (!empty($meta_description)) {
            echo '<meta name="twitter:description" content="' . esc_attr($meta_description) . '">' . "\n";
        }
    }


}
