import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Notice,
  Spinner,
  TextControl,
  Flex,
  FlexItem,
  FlexBlock,
  __experimentalSpacer as Spacer,
  __experimentalHeading as Heading,
  __experimentalText as Text
} from '@wordpress/components';
import { check, warning, info } from '@wordpress/icons';

/**
 * Composant pour l'analyse des Schema Markup
 */
const SchemaAnalysisSection = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisUrl, setAnalysisUrl] = useState('');
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);

  /**
   * Lance l'analyse Schema Markup
   */
  const handleAnalyzeSchema = async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch('/wp-json/boss-seo/v1/technical-analysis/schema', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': window.bossOptimizer?.nonce || ''
        },
        body: JSON.stringify({
          url: analysisUrl || window.location.origin
        })
      });

      const data = await response.json();

      if (data.success) {
        setResults(data);
      } else {
        setError(data.message || __('Erreur lors de l\'analyse Schema', 'boss-seo'));
      }
    } catch (err) {
      setError(__('Erreur de connexion lors de l\'analyse', 'boss-seo'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  /**
   * Retourne l'icône et la couleur selon la sévérité
   */
  const getSeverityDisplay = (severity) => {
    switch (severity) {
      case 'error':
        return { icon: warning, color: '#d63638' };
      case 'warning':
        return { icon: info, color: '#dba617' };
      default:
        return { icon: check, color: '#00a32a' };
    }
  };

  /**
   * Retourne la couleur du score
   */
  const getScoreColor = (score) => {
    if (score >= 80) return '#00a32a';
    if (score >= 60) return '#dba617';
    return '#d63638';
  };

  return (
    <Card>
      <CardHeader>
        <Heading level={3}>
          {__('Analyse Schema Markup', 'boss-seo')}
        </Heading>
      </CardHeader>
      <CardBody>
        <Text>
          {__('Analysez les données structurées (Schema.org) de votre site pour améliorer l\'affichage dans les résultats de recherche.', 'boss-seo')}
        </Text>

        <Spacer marginY={4} />

        <Flex>
          <FlexBlock>
            <TextControl
              label={__('URL à analyser', 'boss-seo')}
              value={analysisUrl}
              onChange={setAnalysisUrl}
              placeholder={__('Laissez vide pour analyser la page d\'accueil', 'boss-seo')}
              help={__('Entrez une URL spécifique ou laissez vide pour analyser la page d\'accueil', 'boss-seo')}
            />
          </FlexBlock>
          <FlexItem>
            <Button
              isPrimary
              onClick={handleAnalyzeSchema}
              disabled={isAnalyzing}
              style={{ marginTop: '28px' }}
            >
              {isAnalyzing ? (
                <>
                  <Spinner />
                  {__('Analyse...', 'boss-seo')}
                </>
              ) : (
                __('Analyser Schema', 'boss-seo')
              )}
            </Button>
          </FlexItem>
        </Flex>

        {error && (
          <Notice status="error" isDismissible={false}>
            {error}
          </Notice>
        )}

        {results && (
          <>
            <Spacer marginY={6} />
            
            {/* Score global */}
            <Card>
              <CardBody>
                <Flex align="center" justify="space-between">
                  <FlexItem>
                    <Heading level={4}>
                      {__('Score Schema Markup', 'boss-seo')}
                    </Heading>
                    <Text>
                      {sprintf(__('%d schemas trouvés', 'boss-seo'), results.schemas_found)}
                    </Text>
                  </FlexItem>
                  <FlexItem>
                    <div
                      style={{
                        fontSize: '2em',
                        fontWeight: 'bold',
                        color: getScoreColor(results.score)
                      }}
                    >
                      {results.score}/100
                    </div>
                  </FlexItem>
                </Flex>
              </CardBody>
            </Card>

            <Spacer marginY={4} />

            {/* Schemas trouvés */}
            {results.schemas && results.schemas.length > 0 && (
              <Card>
                <CardHeader>
                  <Heading level={4}>
                    {__('Schemas détectés', 'boss-seo')}
                  </Heading>
                </CardHeader>
                <CardBody>
                  <div className="boss-grid boss-grid-cols-1 boss-gap-4">
                    {results.schemas.map((schema, index) => (
                      <div
                        key={index}
                        className="boss-p-4 boss-border boss-border-gray-200 boss-rounded-lg"
                      >
                        <Flex align="center" justify="space-between">
                          <FlexItem>
                            <Text weight="600">
                              {schema.schema_type}
                            </Text>
                            <Text variant="muted" size="small">
                              {schema.type === 'json-ld' ? 'JSON-LD' : 'Microdata'} - Position {schema.position}
                            </Text>
                          </FlexItem>
                          <FlexItem>
                            <div
                              style={{
                                width: '12px',
                                height: '12px',
                                borderRadius: '50%',
                                backgroundColor: '#00a32a'
                              }}
                            />
                          </FlexItem>
                        </Flex>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}

            <Spacer marginY={4} />

            {/* Issues */}
            {results.issues && results.issues.length > 0 && (
              <Card>
                <CardHeader>
                  <Heading level={4}>
                    {__('Problèmes détectés', 'boss-seo')}
                  </Heading>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    {results.issues.map((issue, index) => {
                      const { icon, color } = getSeverityDisplay(issue.severity);
                      return (
                        <div
                          key={index}
                          className="boss-p-4 boss-border boss-border-gray-200 boss-rounded-lg"
                        >
                          <Flex align="flex-start" gap={3}>
                            <FlexItem>
                              <div style={{ color }}>
                                {React.createElement(icon, { size: 20 })}
                              </div>
                            </FlexItem>
                            <FlexBlock>
                              <Text weight="600">
                                {issue.message}
                              </Text>
                              {issue.description && (
                                <Text variant="muted" size="small">
                                  {issue.description}
                                </Text>
                              )}
                              {issue.schema_type && (
                                <Text size="small">
                                  <strong>{__('Schema:', 'boss-seo')}</strong> {issue.schema_type}
                                </Text>
                              )}
                              {issue.property && (
                                <Text size="small">
                                  <strong>{__('Propriété:', 'boss-seo')}</strong> {issue.property}
                                </Text>
                              )}
                            </FlexBlock>
                          </Flex>
                        </div>
                      );
                    })}
                  </div>
                </CardBody>
              </Card>
            )}

            <Spacer marginY={4} />

            {/* Recommandations */}
            {results.recommendations && results.recommendations.length > 0 && (
              <Card>
                <CardHeader>
                  <Heading level={4}>
                    {__('Recommandations', 'boss-seo')}
                  </Heading>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    {results.recommendations.map((recommendation, index) => {
                      const priorityColor = {
                        high: '#d63638',
                        medium: '#dba617',
                        low: '#00a32a'
                      }[recommendation.priority] || '#666';

                      return (
                        <div
                          key={index}
                          className="boss-p-4 boss-border boss-border-gray-200 boss-rounded-lg"
                        >
                          <Flex align="flex-start" gap={3}>
                            <FlexItem>
                              <div
                                style={{
                                  width: '4px',
                                  height: '60px',
                                  backgroundColor: priorityColor,
                                  borderRadius: '2px'
                                }}
                              />
                            </FlexItem>
                            <FlexBlock>
                              <Text weight="600">
                                {recommendation.title}
                              </Text>
                              <Text variant="muted" size="small">
                                {recommendation.description}
                              </Text>
                              <Text size="small">
                                <strong>{__('Action:', 'boss-seo')}</strong> {recommendation.action}
                              </Text>
                              <Text size="small" style={{ color: priorityColor }}>
                                <strong>{__('Priorité:', 'boss-seo')}</strong> {
                                  recommendation.priority === 'high' ? __('Haute', 'boss-seo') :
                                  recommendation.priority === 'medium' ? __('Moyenne', 'boss-seo') :
                                  __('Basse', 'boss-seo')
                                }
                              </Text>
                            </FlexBlock>
                          </Flex>
                        </div>
                      );
                    })}
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Pas de problèmes */}
            {(!results.issues || results.issues.length === 0) && results.schemas_found > 0 && (
              <Notice status="success" isDismissible={false}>
                {__('Excellent ! Aucun problème détecté dans vos schemas.', 'boss-seo')}
              </Notice>
            )}

            {/* Aucun schema trouvé */}
            {results.schemas_found === 0 && (
              <Notice status="warning" isDismissible={false}>
                {__('Aucun schema markup détecté. Considérez l\'ajout de données structurées pour améliorer votre SEO.', 'boss-seo')}
              </Notice>
            )}
          </>
        )}
      </CardBody>
    </Card>
  );
};

export default SchemaAnalysisSection;
