<?php

/**
 * Système de stockage pour les rapports Boss SEO.
 * 
 * Cette classe gère le stockage, la récupération et la gestion
 * des rapports générés.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Système de stockage des rapports.
 *
 * Gère :
 * - Stockage des rapports générés
 * - Métadonnées des rapports
 * - Historique et nettoyage
 * - Accès sécurisé aux fichiers
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Reports_Storage {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * Répertoire de stockage des rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $reports_dir    Répertoire des rapports.
     */
    private $reports_dir;

    /**
     * URL de base pour accéder aux rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $reports_url    URL des rapports.
     */
    private $reports_url;

    /**
     * Table des métadonnées des rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $table_name    Nom de la table.
     */
    private $table_name;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    string    $plugin_name    Le nom du plugin.
     */
    public function __construct( $plugin_name ) {
        $this->plugin_name = $plugin_name;
        
        // Définir les chemins
        $upload_dir = wp_upload_dir();
        $this->reports_dir = $upload_dir['basedir'] . '/boss-seo-reports';
        $this->reports_url = $upload_dir['baseurl'] . '/boss-seo-reports';
        
        // Nom de la table
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'boss_seo_reports';
        
        // Créer le répertoire et la table si nécessaire
        $this->init_storage();
    }

    /**
     * Initialise le système de stockage.
     *
     * @since    1.2.0
     */
    private function init_storage() {
        // Créer le répertoire de stockage
        if ( ! file_exists( $this->reports_dir ) ) {
            wp_mkdir_p( $this->reports_dir );
            
            // Créer un fichier .htaccess pour la sécurité
            $htaccess_content = "Options -Indexes\n";
            $htaccess_content .= "deny from all\n";
            file_put_contents( $this->reports_dir . '/.htaccess', $htaccess_content );
            
            // Créer un index.php vide
            file_put_contents( $this->reports_dir . '/index.php', '<?php // Silence is golden' );
        }
        
        // Créer la table des métadonnées
        $this->create_reports_table();
    }

    /**
     * Crée la table des rapports.
     *
     * @since    1.2.0
     */
    private function create_reports_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            type varchar(50) NOT NULL,
            format varchar(10) NOT NULL,
            file_path varchar(500) NOT NULL,
            file_size bigint(20) DEFAULT 0,
            generated_at datetime DEFAULT CURRENT_TIMESTAMP,
            generated_by bigint(20) DEFAULT 0,
            period_start date NULL,
            period_end date NULL,
            scheduled tinyint(1) DEFAULT 0,
            frequency varchar(20) NULL,
            recipients text NULL,
            status varchar(20) DEFAULT 'completed',
            data_hash varchar(64) NULL,
            PRIMARY KEY (id),
            KEY type (type),
            KEY generated_at (generated_at),
            KEY generated_by (generated_by)
        ) $charset_collate;";
        
        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Stocke un rapport généré.
     *
     * @since    1.2.0
     * @param    array     $report_data    Données du rapport.
     * @param    string    $content        Contenu du rapport.
     * @param    string    $format         Format du rapport (pdf, csv, html).
     * @return   int|false                 ID du rapport ou false en cas d'erreur.
     */
    public function store_report( $report_data, $content, $format = 'pdf' ) {
        try {
            // Générer un nom de fichier unique
            $filename = $this->generate_filename( $report_data['type'], $format );
            $file_path = $this->reports_dir . '/' . $filename;
            
            // Écrire le contenu dans le fichier
            $bytes_written = file_put_contents( $file_path, $content );
            
            if ( $bytes_written === false ) {
                throw new Exception( 'Impossible d\'écrire le fichier rapport' );
            }
            
            // Insérer les métadonnées en base
            global $wpdb;
            
            $result = $wpdb->insert(
                $this->table_name,
                array(
                    'title' => sanitize_text_field( $report_data['title'] ),
                    'type' => sanitize_text_field( $report_data['type'] ),
                    'format' => sanitize_text_field( $format ),
                    'file_path' => $filename,
                    'file_size' => $bytes_written,
                    'generated_by' => get_current_user_id(),
                    'period_start' => $report_data['period_start'] ?? null,
                    'period_end' => $report_data['period_end'] ?? null,
                    'scheduled' => $report_data['scheduled'] ?? 0,
                    'frequency' => $report_data['frequency'] ?? null,
                    'recipients' => $report_data['recipients'] ?? null,
                    'data_hash' => md5( $content )
                ),
                array(
                    '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s', '%d', '%s', '%s', '%s'
                )
            );
            
            if ( $result === false ) {
                // Supprimer le fichier en cas d'erreur de base
                unlink( $file_path );
                throw new Exception( 'Erreur lors de l\'insertion en base de données' );
            }
            
            return $wpdb->insert_id;
            
        } catch ( Exception $e ) {
            error_log( 'Boss SEO Reports Storage - Erreur stockage: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Récupère la liste des rapports.
     *
     * @since    1.2.0
     * @param    array    $args    Arguments de filtrage.
     * @return   array             Liste des rapports.
     */
    public function get_reports( $args = array() ) {
        global $wpdb;
        
        $defaults = array(
            'type' => '',
            'limit' => 20,
            'offset' => 0,
            'order_by' => 'generated_at',
            'order' => 'DESC',
            'date_from' => '',
            'date_to' => '',
            'search' => ''
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        $where_conditions = array( '1=1' );
        $where_values = array();
        
        // Filtrer par type
        if ( ! empty( $args['type'] ) ) {
            $where_conditions[] = 'type = %s';
            $where_values[] = $args['type'];
        }
        
        // Filtrer par date
        if ( ! empty( $args['date_from'] ) ) {
            $where_conditions[] = 'generated_at >= %s';
            $where_values[] = $args['date_from'] . ' 00:00:00';
        }
        
        if ( ! empty( $args['date_to'] ) ) {
            $where_conditions[] = 'generated_at <= %s';
            $where_values[] = $args['date_to'] . ' 23:59:59';
        }
        
        // Recherche dans le titre
        if ( ! empty( $args['search'] ) ) {
            $where_conditions[] = 'title LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like( $args['search'] ) . '%';
        }
        
        $where_clause = implode( ' AND ', $where_conditions );
        
        // Construire la requête
        $sql = "SELECT * FROM {$this->table_name} WHERE {$where_clause}";
        
        // Ajouter l'ordre
        $sql .= $wpdb->prepare( " ORDER BY {$args['order_by']} {$args['order']}" );
        
        // Ajouter la limite
        $sql .= $wpdb->prepare( " LIMIT %d OFFSET %d", $args['limit'], $args['offset'] );
        
        // Préparer et exécuter la requête
        if ( ! empty( $where_values ) ) {
            $sql = $wpdb->prepare( $sql, $where_values );
        }
        
        $reports = $wpdb->get_results( $sql, ARRAY_A );
        
        // Ajouter les URLs de téléchargement
        foreach ( $reports as &$report ) {
            $report['download_url'] = $this->get_download_url( $report['id'] );
            $report['file_exists'] = file_exists( $this->reports_dir . '/' . $report['file_path'] );
        }
        
        return $reports;
    }

    /**
     * Génère un nom de fichier unique.
     *
     * @since    1.2.0
     * @param    string    $type      Type de rapport.
     * @param    string    $format    Format du fichier.
     * @return   string               Nom de fichier.
     */
    private function generate_filename( $type, $format ) {
        $timestamp = date( 'Y-m-d_H-i-s' );
        $random = wp_rand( 1000, 9999 );
        return "boss-seo-{$type}-{$timestamp}-{$random}.{$format}";
    }

    /**
     * Génère une URL de téléchargement sécurisée.
     *
     * @since    1.2.0
     * @param    int       $report_id    ID du rapport.
     * @return   string                  URL de téléchargement.
     */
    public function get_download_url( $report_id ) {
        $nonce = wp_create_nonce( 'boss_seo_download_report_' . $report_id );
        return admin_url( "admin-ajax.php?action=boss_seo_download_report&report_id={$report_id}&_wpnonce={$nonce}" );
    }

    /**
     * Récupère un rapport par son ID.
     *
     * @since    1.2.0
     * @param    int       $report_id    ID du rapport.
     * @return   array|null              Données du rapport ou null.
     */
    public function get_report( $report_id ) {
        global $wpdb;

        $report = $wpdb->get_row(
            $wpdb->prepare( "SELECT * FROM {$this->table_name} WHERE id = %d", $report_id ),
            ARRAY_A
        );

        if ( $report ) {
            $report['download_url'] = $this->get_download_url( $report['id'] );
            $report['file_exists'] = file_exists( $this->reports_dir . '/' . $report['file_path'] );
        }

        return $report;
    }

    /**
     * Supprime un rapport.
     *
     * @since    1.2.0
     * @param    int       $report_id    ID du rapport.
     * @return   bool                    Succès de la suppression.
     */
    public function delete_report( $report_id ) {
        global $wpdb;

        // Récupérer les informations du rapport
        $report = $this->get_report( $report_id );

        if ( ! $report ) {
            return false;
        }

        // Supprimer le fichier
        $file_path = $this->reports_dir . '/' . $report['file_path'];
        if ( file_exists( $file_path ) ) {
            unlink( $file_path );
        }

        // Supprimer l'entrée de la base
        $result = $wpdb->delete(
            $this->table_name,
            array( 'id' => $report_id ),
            array( '%d' )
        );

        return $result !== false;
    }

    /**
     * Nettoie les anciens rapports.
     *
     * @since    1.2.0
     * @param    int       $days_old    Nombre de jours (défaut: 30).
     * @return   int                    Nombre de rapports supprimés.
     */
    public function cleanup_old_reports( $days_old = 30 ) {
        global $wpdb;

        $cutoff_date = date( 'Y-m-d H:i:s', strtotime( "-{$days_old} days" ) );

        // Récupérer les rapports à supprimer
        $old_reports = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT id, file_path FROM {$this->table_name} WHERE generated_at < %s AND scheduled = 0",
                $cutoff_date
            ),
            ARRAY_A
        );

        $deleted_count = 0;

        foreach ( $old_reports as $report ) {
            if ( $this->delete_report( $report['id'] ) ) {
                $deleted_count++;
            }
        }

        return $deleted_count;
    }

    /**
     * Récupère les statistiques de stockage.
     *
     * @since    1.2.0
     * @return   array    Statistiques.
     */
    public function get_storage_stats() {
        global $wpdb;

        $stats = array(
            'total_reports' => 0,
            'total_size' => 0,
            'by_type' => array(),
            'by_format' => array(),
            'oldest_report' => null,
            'newest_report' => null
        );

        // Statistiques générales
        $general_stats = $wpdb->get_row(
            "SELECT
                COUNT(*) as total_reports,
                SUM(file_size) as total_size,
                MIN(generated_at) as oldest_report,
                MAX(generated_at) as newest_report
             FROM {$this->table_name}",
            ARRAY_A
        );

        if ( $general_stats ) {
            $stats = array_merge( $stats, $general_stats );
        }

        // Statistiques par type
        $type_stats = $wpdb->get_results(
            "SELECT type, COUNT(*) as count, SUM(file_size) as size
             FROM {$this->table_name}
             GROUP BY type",
            ARRAY_A
        );

        foreach ( $type_stats as $type_stat ) {
            $stats['by_type'][ $type_stat['type'] ] = array(
                'count' => $type_stat['count'],
                'size' => $type_stat['size']
            );
        }

        // Statistiques par format
        $format_stats = $wpdb->get_results(
            "SELECT format, COUNT(*) as count, SUM(file_size) as size
             FROM {$this->table_name}
             GROUP BY format",
            ARRAY_A
        );

        foreach ( $format_stats as $format_stat ) {
            $stats['by_format'][ $format_stat['format'] ] = array(
                'count' => $format_stat['count'],
                'size' => $format_stat['size']
            );
        }

        return $stats;
    }

    /**
     * Vérifie l'intégrité des fichiers stockés.
     *
     * @since    1.2.0
     * @return   array    Résultats de la vérification.
     */
    public function check_integrity() {
        global $wpdb;

        $reports = $wpdb->get_results( "SELECT id, file_path FROM {$this->table_name}", ARRAY_A );

        $results = array(
            'total_checked' => count( $reports ),
            'missing_files' => array(),
            'orphaned_files' => array()
        );

        // Vérifier les fichiers manquants
        foreach ( $reports as $report ) {
            $file_path = $this->reports_dir . '/' . $report['file_path'];
            if ( ! file_exists( $file_path ) ) {
                $results['missing_files'][] = $report;
            }
        }

        // Vérifier les fichiers orphelins
        if ( is_dir( $this->reports_dir ) ) {
            $files = scandir( $this->reports_dir );
            $db_files = array_column( $reports, 'file_path' );

            foreach ( $files as $file ) {
                if ( $file === '.' || $file === '..' || $file === '.htaccess' || $file === 'index.php' ) {
                    continue;
                }

                if ( ! in_array( $file, $db_files ) ) {
                    $results['orphaned_files'][] = $file;
                }
            }
        }

        return $results;
    }
}
