"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";








var _F6HPKLO2cjs = require('./F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('./KBNYGXWI.cjs');



var _AV6KTKLEcjs = require('./AV6KTKLE.cjs');

// src/disclosure/disclosure-store.ts
function createDisclosureStore(props = {}) {
  const store = _F6HPKLO2cjs.mergeStore.call(void 0, 
    props.store,
    _F6HPKLO2cjs.omit.call(void 0, props.disclosure, ["contentElement", "disclosureElement"])
  );
  _F6HPKLO2cjs.throwOnConflictingProps.call(void 0, props, store);
  const syncState = store == null ? void 0 : store.getState();
  const open = _KBNYGXWIcjs.defaultValue.call(void 0, 
    props.open,
    syncState == null ? void 0 : syncState.open,
    props.defaultOpen,
    false
  );
  const animated = _KBNYGXWIcjs.defaultValue.call(void 0, props.animated, syncState == null ? void 0 : syncState.animated, false);
  const initialState = {
    open,
    animated,
    animating: !!animated && open,
    mounted: open,
    contentElement: _KBNYGXWIcjs.defaultValue.call(void 0, syncState == null ? void 0 : syncState.contentElement, null),
    disclosureElement: _KBNYGXWIcjs.defaultValue.call(void 0, syncState == null ? void 0 : syncState.disclosureElement, null)
  };
  const disclosure = _F6HPKLO2cjs.createStore.call(void 0, initialState, store);
  _F6HPKLO2cjs.setup.call(void 0, 
    disclosure,
    () => _F6HPKLO2cjs.sync.call(void 0, disclosure, ["animated", "animating"], (state) => {
      if (state.animated)
        return;
      disclosure.setState("animating", false);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    disclosure,
    () => _F6HPKLO2cjs.subscribe.call(void 0, disclosure, ["open"], () => {
      if (!disclosure.getState().animated)
        return;
      disclosure.setState("animating", true);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    disclosure,
    () => _F6HPKLO2cjs.sync.call(void 0, disclosure, ["open", "animating"], (state) => {
      disclosure.setState("mounted", state.open || state.animating);
    })
  );
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, disclosure), {
    setOpen: (value) => disclosure.setState("open", value),
    show: () => disclosure.setState("open", true),
    hide: () => disclosure.setState("open", false),
    toggle: () => disclosure.setState("open", (open2) => !open2),
    stopAnimation: () => disclosure.setState("animating", false),
    setContentElement: (value) => disclosure.setState("contentElement", value),
    setDisclosureElement: (value) => disclosure.setState("disclosureElement", value)
  });
}



exports.createDisclosureStore = createDisclosureStore;
