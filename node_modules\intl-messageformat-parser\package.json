{"name": "intl-messageformat-parser", "version": "1.8.1", "description": "Parses ICU Message strings into an AST via JavaScript.", "main": "index.js", "module": "src/parser.js", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist lib", "build": "./build.js", "benchmark": "./test/benchmark.js", "prepublish": "npm run build", "test": "mocha --opts ../../mocha.opts tests/index.ts"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Song <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/formatjs/formatjs.git"}, "keywords": ["i18n", "intl", "internationalization", "localization", "globalization", "messageformat", "parser", "plural", "icu"], "author": "<PERSON> <<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "homepage": "https://github.com/formatjs/formatjs", "gitHead": "60ddf374ed659ffe2f3807c3c7df411c80492761"}