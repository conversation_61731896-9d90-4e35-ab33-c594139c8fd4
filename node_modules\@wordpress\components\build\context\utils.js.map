{"version": 3, "names": ["_constants", "require", "getNamespace", "componentName", "COMPONENT_NAMESPACE", "getConnectedNamespace", "CONNECTED_NAMESPACE"], "sources": ["@wordpress/components/src/context/utils.js"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport { CONNECTED_NAMESPACE, COMPONENT_NAMESPACE } from './constants';\n\n/**\n * Creates a dedicated context namespace HTML attribute for components.\n * ns is short for \"namespace\"\n *\n * @example\n * ```jsx\n * <div {...ns('Container')} />\n * ```\n *\n * @param {string} componentName The name for the component.\n * @return {Record<string, any>} A props object with the namespaced HTML attribute.\n */\nexport function getNamespace( componentName ) {\n\treturn { [ COMPONENT_NAMESPACE ]: componentName };\n}\n\n/**\n * Creates a dedicated connected context namespace HTML attribute for components.\n * ns is short for \"namespace\"\n *\n * @example\n * ```jsx\n * <div {...cns()} />\n * ```\n *\n * @return {Record<string, any>} A props object with the namespaced HTML attribute.\n */\nexport function getConnectedNamespace() {\n\treturn { [ CONNECTED_NAMESPACE ]: true };\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,UAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,YAAYA,CAAEC,aAAa,EAAG;EAC7C,OAAO;IAAE,CAAEC,8BAAmB,GAAID;EAAc,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,qBAAqBA,CAAA,EAAG;EACvC,OAAO;IAAE,CAAEC,8BAAmB,GAAI;EAAK,CAAC;AACzC"}