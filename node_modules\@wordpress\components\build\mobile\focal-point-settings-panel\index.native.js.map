{"version": 3, "names": ["_reactNative", "require", "_native", "_i18n", "_element", "_components", "_navBar", "_interopRequireDefault", "_styles", "FocalPointSettingsPanelMemo", "memo", "focalPoint", "onFocalPointChange", "shouldEnableBottomSheetScroll", "url", "navigation", "useNavigation", "onButtonPress", "action", "goBack", "draftFocalPoint", "setDraftFocalPoint", "useState", "setPosition", "coordinates", "prevState", "_react", "createElement", "SafeAreaView", "style", "styles", "safearea", "default", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onPress", "Heading", "__", "App<PERSON><PERSON><PERSON><PERSON>", "FocalPointPicker", "onChange", "useCallback", "FocalPointSettingsPanel", "props", "route", "useRoute", "useContext", "BottomSheetContext", "params", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/focal-point-settings-panel/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { SafeAreaView } from 'react-native';\nimport { useRoute, useNavigation } from '@react-navigation/native';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { memo, useContext, useState, useCallback } from '@wordpress/element';\nimport { BottomSheetContext, FocalPointPicker } from '@wordpress/components';\n\n/**\n * Internal dependencies\n */\nimport NavBar from '../bottom-sheet/nav-bar';\nimport styles from './styles.scss';\n\nconst FocalPointSettingsPanelMemo = memo(\n\t( {\n\t\tfocalPoint,\n\t\tonFocalPointChange,\n\t\tshouldEnableBottomSheetScroll,\n\t\turl,\n\t} ) => {\n\t\tconst navigation = useNavigation();\n\n\t\tfunction onButtonPress( action ) {\n\t\t\tnavigation.goBack();\n\t\t\tif ( action === 'apply' ) {\n\t\t\t\tonFocalPointChange( draftFocalPoint );\n\t\t\t}\n\t\t}\n\n\t\tconst [ draftFocalPoint, setDraftFocalPoint ] = useState( focalPoint );\n\t\tfunction setPosition( coordinates ) {\n\t\t\tsetDraftFocalPoint( ( prevState ) => ( {\n\t\t\t\t...prevState,\n\t\t\t\t...coordinates,\n\t\t\t} ) );\n\t\t}\n\n\t\treturn (\n\t\t\t<SafeAreaView style={ styles.safearea }>\n\t\t\t\t<NavBar>\n\t\t\t\t\t<NavBar.DismissButton\n\t\t\t\t\t\tonPress={ () => onButtonPress( 'cancel' ) }\n\t\t\t\t\t/>\n\t\t\t\t\t<NavBar.Heading>\n\t\t\t\t\t\t{ __( 'Edit focal point' ) }\n\t\t\t\t\t</NavBar.Heading>\n\t\t\t\t\t<NavBar.ApplyButton\n\t\t\t\t\t\tonPress={ () => onButtonPress( 'apply' ) }\n\t\t\t\t\t/>\n\t\t\t\t</NavBar>\n\t\t\t\t<FocalPointPicker\n\t\t\t\t\tfocalPoint={ draftFocalPoint }\n\t\t\t\t\tonChange={ useCallback( setPosition, [] ) }\n\t\t\t\t\tshouldEnableBottomSheetScroll={\n\t\t\t\t\t\tshouldEnableBottomSheetScroll\n\t\t\t\t\t}\n\t\t\t\t\turl={ url }\n\t\t\t\t/>\n\t\t\t</SafeAreaView>\n\t\t);\n\t}\n);\n\nfunction FocalPointSettingsPanel( props ) {\n\tconst route = useRoute();\n\tconst { shouldEnableBottomSheetScroll } = useContext( BottomSheetContext );\n\n\treturn (\n\t\t<FocalPointSettingsPanelMemo\n\t\t\tshouldEnableBottomSheetScroll={ shouldEnableBottomSheetScroll }\n\t\t\t{ ...props }\n\t\t\t{ ...route.params }\n\t\t/>\n\t);\n}\n\nexport default FocalPointSettingsPanel;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAKA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAKA,IAAAK,OAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,OAAA,GAAAD,sBAAA,CAAAN,OAAA;AAjBA;AACA;AACA;;AAIA;AACA;AACA;;AAKA;AACA;AACA;;AAIA,MAAMQ,2BAA2B,GAAG,IAAAC,aAAI,EACvC,CAAE;EACDC,UAAU;EACVC,kBAAkB;EAClBC,6BAA6B;EAC7BC;AACD,CAAC,KAAM;EACN,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAElC,SAASC,aAAaA,CAAEC,MAAM,EAAG;IAChCH,UAAU,CAACI,MAAM,CAAC,CAAC;IACnB,IAAKD,MAAM,KAAK,OAAO,EAAG;MACzBN,kBAAkB,CAAEQ,eAAgB,CAAC;IACtC;EACD;EAEA,MAAM,CAAEA,eAAe,EAAEC,kBAAkB,CAAE,GAAG,IAAAC,iBAAQ,EAAEX,UAAW,CAAC;EACtE,SAASY,WAAWA,CAAEC,WAAW,EAAG;IACnCH,kBAAkB,CAAII,SAAS,KAAQ;MACtC,GAAGA,SAAS;MACZ,GAAGD;IACJ,CAAC,CAAG,CAAC;EACN;EAEA,OACC,IAAAE,MAAA,CAAAC,aAAA,EAAC3B,YAAA,CAAA4B,YAAY;IAACC,KAAK,EAAGC,eAAM,CAACC;EAAU,GACtC,IAAAL,MAAA,CAAAC,aAAA,EAACrB,OAAA,CAAA0B,OAAM,QACN,IAAAN,MAAA,CAAAC,aAAA,EAACrB,OAAA,CAAA0B,OAAM,CAACC,aAAa;IACpBC,OAAO,EAAGA,CAAA,KAAMjB,aAAa,CAAE,QAAS;EAAG,CAC3C,CAAC,EACF,IAAAS,MAAA,CAAAC,aAAA,EAACrB,OAAA,CAAA0B,OAAM,CAACG,OAAO,QACZ,IAAAC,QAAE,EAAE,kBAAmB,CACV,CAAC,EACjB,IAAAV,MAAA,CAAAC,aAAA,EAACrB,OAAA,CAAA0B,OAAM,CAACK,WAAW;IAClBH,OAAO,EAAGA,CAAA,KAAMjB,aAAa,CAAE,OAAQ;EAAG,CAC1C,CACM,CAAC,EACT,IAAAS,MAAA,CAAAC,aAAA,EAACtB,WAAA,CAAAiC,gBAAgB;IAChB3B,UAAU,EAAGS,eAAiB;IAC9BmB,QAAQ,EAAG,IAAAC,oBAAW,EAAEjB,WAAW,EAAE,EAAG,CAAG;IAC3CV,6BAA6B,EAC5BA,6BACA;IACDC,GAAG,EAAGA;EAAK,CACX,CACY,CAAC;AAEjB,CACD,CAAC;AAED,SAAS2B,uBAAuBA,CAAEC,KAAK,EAAG;EACzC,MAAMC,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,MAAM;IAAE/B;EAA8B,CAAC,GAAG,IAAAgC,mBAAU,EAAEC,8BAAmB,CAAC;EAE1E,OACC,IAAApB,MAAA,CAAAC,aAAA,EAAClB,2BAA2B;IAC3BI,6BAA6B,EAAGA,6BAA+B;IAAA,GAC1D6B,KAAK;IAAA,GACLC,KAAK,CAACI;EAAM,CACjB,CAAC;AAEJ;AAAC,IAAAC,QAAA,GAEcP,uBAAuB;AAAAQ,OAAA,CAAAjB,OAAA,GAAAgB,QAAA"}