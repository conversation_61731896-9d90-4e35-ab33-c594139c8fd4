"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useCard = useCard;
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _element = require("@wordpress/element");
var _context = require("../../context");
var _surface = require("../../surface");
var styles = _interopRequireWildcard(require("../styles"));
var _useCx = require("../../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

function useDeprecatedProps({
  elevation,
  isElevated,
  ...otherProps
}) {
  const propsToReturn = {
    ...otherProps
  };
  let computedElevation = elevation;
  if (isElevated) {
    var _computedElevation;
    (0, _deprecated.default)('Card isElevated prop', {
      since: '5.9',
      alternative: 'elevation'
    });
    (_computedElevation = computedElevation) !== null && _computedElevation !== void 0 ? _computedElevation : computedElevation = 2;
  }

  // The `elevation` prop should only be passed when it's not `undefined`,
  // otherwise it will override the value that gets derived from `useContextSystem`.
  if (typeof computedElevation !== 'undefined') {
    propsToReturn.elevation = computedElevation;
  }
  return propsToReturn;
}
function useCard(props) {
  const {
    className,
    elevation = 0,
    isBorderless = false,
    isRounded = true,
    size = 'medium',
    ...otherProps
  } = (0, _context.useContextSystem)(useDeprecatedProps(props), 'Card');
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => {
    return cx(styles.Card, isBorderless && styles.boxShadowless, isRounded && styles.rounded, className);
  }, [className, cx, isBorderless, isRounded]);
  const surfaceProps = (0, _surface.useSurface)({
    ...otherProps,
    className: classes
  });
  return {
    ...surfaceProps,
    elevation,
    isBorderless,
    isRounded,
    size
  };
}
//# sourceMappingURL=hook.js.map