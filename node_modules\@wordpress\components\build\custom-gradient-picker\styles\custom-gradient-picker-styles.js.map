{"version": 3, "names": ["_flex", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "SelectWrapper", "_base", "default", "FlexBlock", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports", "AccessoryWrapper"], "sources": ["@wordpress/components/src/custom-gradient-picker/styles/custom-gradient-picker-styles.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n/**\n * Internal dependencies\n */\nimport { FlexBlock } from '../../flex';\n\nexport const SelectWrapper = styled( FlexBlock )`\n\tflex-grow: 5;\n`;\n\nexport const AccessoryWrapper = styled( FlexBlock )`\n\tflex-grow: 5;\n`;\n"], "mappings": ";;;;;;;;AAOA,IAAAA,KAAA,GAAAC,OAAA;AAAuC,SAAAC,iCAAA;AAEhC,MAAMC,aAAa,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,eAAS,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAE/C;AAACc,OAAA,CAAAb,aAAA,GAAAA,aAAA;AAEK,MAAMc,gBAAgB,GAAG,kBAAAb,KAAA,CAAAC,OAAA,EAAQC,eAAS,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAElD;AAACc,OAAA,CAAAC,gBAAA,GAAAA,gBAAA"}