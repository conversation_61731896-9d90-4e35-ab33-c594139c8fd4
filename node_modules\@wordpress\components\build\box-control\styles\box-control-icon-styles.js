"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Viewbox = exports.TopStroke = exports.Root = exports.RightStroke = exports.LeftStroke = exports.BottomStroke = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react = require("@emotion/react");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const Root = (0, _base.default)("span", process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z8"
} : {
  target: "e1j5nr4z8",
  label: "Root"
})(process.env.NODE_ENV === "production" ? {
  name: "1w884gc",
  styles: "box-sizing:border-box;display:block;width:24px;height:24px;position:relative;padding:4px"
} : {
  name: "1w884gc",
  styles: "box-sizing:border-box;display:block;width:24px;height:24px;position:relative;padding:4px",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQU0rQiIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2JveC1jb250cm9sL3N0eWxlcy9ib3gtY29udHJvbC1pY29uLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IGNzcyB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcblxuZXhwb3J0IGNvbnN0IFJvb3QgPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHdpZHRoOiAyNHB4O1xuXHRoZWlnaHQ6IDI0cHg7XG5cdHBvc2l0aW9uOiByZWxhdGl2ZTtcblx0cGFkZGluZzogNHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IFZpZXdib3ggPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHBvc2l0aW9uOiByZWxhdGl2ZTtcblx0d2lkdGg6IDEwMCU7XG5cdGhlaWdodDogMTAwJTtcbmA7XG5cbmNvbnN0IHN0cm9rZUZvY3VzID0gKCB7IGlzRm9jdXNlZCB9OiB7IGlzRm9jdXNlZDogYm9vbGVhbiB9ICkgPT4ge1xuXHRyZXR1cm4gY3NzKCB7XG5cdFx0YmFja2dyb3VuZENvbG9yOiAnY3VycmVudENvbG9yJyxcblx0XHRvcGFjaXR5OiBpc0ZvY3VzZWQgPyAxIDogMC4zLFxuXHR9ICk7XG59O1xuXG5jb25zdCBTdHJva2UgPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHBvaW50ZXItZXZlbnRzOiBub25lO1xuXHRwb3NpdGlvbjogYWJzb2x1dGU7XG5cdCR7IHN0cm9rZUZvY3VzIH07XG5gO1xuXG5jb25zdCBWZXJ0aWNhbFN0cm9rZSA9IHN0eWxlZCggU3Ryb2tlIClgXG5cdGJvdHRvbTogM3B4O1xuXHR0b3A6IDNweDtcblx0d2lkdGg6IDJweDtcbmA7XG5cbmNvbnN0IEhvcml6b250YWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRoZWlnaHQ6IDJweDtcblx0bGVmdDogM3B4O1xuXHRyaWdodDogM3B4O1xuYDtcblxuZXhwb3J0IGNvbnN0IFRvcFN0cm9rZSA9IHN0eWxlZCggSG9yaXpvbnRhbFN0cm9rZSApYFxuXHR0b3A6IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgUmlnaHRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdHJpZ2h0OiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IEJvdHRvbVN0cm9rZSA9IHN0eWxlZCggSG9yaXpvbnRhbFN0cm9rZSApYFxuXHRib3R0b206IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgTGVmdFN0cm9rZSA9IHN0eWxlZCggVmVydGljYWxTdHJva2UgKWBcblx0bGVmdDogMDtcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.Root = Root;
const Viewbox = (0, _base.default)("span", process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z7"
} : {
  target: "e1j5nr4z7",
  label: "Viewbox"
})(process.env.NODE_ENV === "production" ? {
  name: "i6vjox",
  styles: "box-sizing:border-box;display:block;position:relative;width:100%;height:100%"
} : {
  name: "i6vjox",
  styles: "box-sizing:border-box;display:block;position:relative;width:100%;height:100%",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQWVrQyIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2JveC1jb250cm9sL3N0eWxlcy9ib3gtY29udHJvbC1pY29uLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IGNzcyB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcblxuZXhwb3J0IGNvbnN0IFJvb3QgPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHdpZHRoOiAyNHB4O1xuXHRoZWlnaHQ6IDI0cHg7XG5cdHBvc2l0aW9uOiByZWxhdGl2ZTtcblx0cGFkZGluZzogNHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IFZpZXdib3ggPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHBvc2l0aW9uOiByZWxhdGl2ZTtcblx0d2lkdGg6IDEwMCU7XG5cdGhlaWdodDogMTAwJTtcbmA7XG5cbmNvbnN0IHN0cm9rZUZvY3VzID0gKCB7IGlzRm9jdXNlZCB9OiB7IGlzRm9jdXNlZDogYm9vbGVhbiB9ICkgPT4ge1xuXHRyZXR1cm4gY3NzKCB7XG5cdFx0YmFja2dyb3VuZENvbG9yOiAnY3VycmVudENvbG9yJyxcblx0XHRvcGFjaXR5OiBpc0ZvY3VzZWQgPyAxIDogMC4zLFxuXHR9ICk7XG59O1xuXG5jb25zdCBTdHJva2UgPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHBvaW50ZXItZXZlbnRzOiBub25lO1xuXHRwb3NpdGlvbjogYWJzb2x1dGU7XG5cdCR7IHN0cm9rZUZvY3VzIH07XG5gO1xuXG5jb25zdCBWZXJ0aWNhbFN0cm9rZSA9IHN0eWxlZCggU3Ryb2tlIClgXG5cdGJvdHRvbTogM3B4O1xuXHR0b3A6IDNweDtcblx0d2lkdGg6IDJweDtcbmA7XG5cbmNvbnN0IEhvcml6b250YWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRoZWlnaHQ6IDJweDtcblx0bGVmdDogM3B4O1xuXHRyaWdodDogM3B4O1xuYDtcblxuZXhwb3J0IGNvbnN0IFRvcFN0cm9rZSA9IHN0eWxlZCggSG9yaXpvbnRhbFN0cm9rZSApYFxuXHR0b3A6IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgUmlnaHRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdHJpZ2h0OiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IEJvdHRvbVN0cm9rZSA9IHN0eWxlZCggSG9yaXpvbnRhbFN0cm9rZSApYFxuXHRib3R0b206IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgTGVmdFN0cm9rZSA9IHN0eWxlZCggVmVydGljYWxTdHJva2UgKWBcblx0bGVmdDogMDtcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.Viewbox = Viewbox;
const strokeFocus = ({
  isFocused
}) => {
  return /*#__PURE__*/(0, _react.css)({
    backgroundColor: 'currentColor',
    opacity: isFocused ? 1 : 0.3
  }, process.env.NODE_ENV === "production" ? "" : ";label:strokeFocus;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQXdCUSIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2JveC1jb250cm9sL3N0eWxlcy9ib3gtY29udHJvbC1pY29uLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IGNzcyB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcblxuZXhwb3J0IGNvbnN0IFJvb3QgPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHdpZHRoOiAyNHB4O1xuXHRoZWlnaHQ6IDI0cHg7XG5cdHBvc2l0aW9uOiByZWxhdGl2ZTtcblx0cGFkZGluZzogNHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IFZpZXdib3ggPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHBvc2l0aW9uOiByZWxhdGl2ZTtcblx0d2lkdGg6IDEwMCU7XG5cdGhlaWdodDogMTAwJTtcbmA7XG5cbmNvbnN0IHN0cm9rZUZvY3VzID0gKCB7IGlzRm9jdXNlZCB9OiB7IGlzRm9jdXNlZDogYm9vbGVhbiB9ICkgPT4ge1xuXHRyZXR1cm4gY3NzKCB7XG5cdFx0YmFja2dyb3VuZENvbG9yOiAnY3VycmVudENvbG9yJyxcblx0XHRvcGFjaXR5OiBpc0ZvY3VzZWQgPyAxIDogMC4zLFxuXHR9ICk7XG59O1xuXG5jb25zdCBTdHJva2UgPSBzdHlsZWQuc3BhbmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdHBvaW50ZXItZXZlbnRzOiBub25lO1xuXHRwb3NpdGlvbjogYWJzb2x1dGU7XG5cdCR7IHN0cm9rZUZvY3VzIH07XG5gO1xuXG5jb25zdCBWZXJ0aWNhbFN0cm9rZSA9IHN0eWxlZCggU3Ryb2tlIClgXG5cdGJvdHRvbTogM3B4O1xuXHR0b3A6IDNweDtcblx0d2lkdGg6IDJweDtcbmA7XG5cbmNvbnN0IEhvcml6b250YWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRoZWlnaHQ6IDJweDtcblx0bGVmdDogM3B4O1xuXHRyaWdodDogM3B4O1xuYDtcblxuZXhwb3J0IGNvbnN0IFRvcFN0cm9rZSA9IHN0eWxlZCggSG9yaXpvbnRhbFN0cm9rZSApYFxuXHR0b3A6IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgUmlnaHRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdHJpZ2h0OiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IEJvdHRvbVN0cm9rZSA9IHN0eWxlZCggSG9yaXpvbnRhbFN0cm9rZSApYFxuXHRib3R0b206IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgTGVmdFN0cm9rZSA9IHN0eWxlZCggVmVydGljYWxTdHJva2UgKWBcblx0bGVmdDogMDtcbmA7XG4iXX0= */");
};
const Stroke = (0, _base.default)("span", process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z6"
} : {
  target: "e1j5nr4z6",
  label: "Stroke"
})("box-sizing:border-box;display:block;pointer-events:none;position:absolute;", strokeFocus, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQThCMEIiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtaWNvbi1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMjRweDtcblx0aGVpZ2h0OiAyNHB4O1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHBhZGRpbmc6IDRweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBWaWV3Ym94ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDEwMCU7XG5gO1xuXG5jb25zdCBzdHJva2VGb2N1cyA9ICggeyBpc0ZvY3VzZWQgfTogeyBpc0ZvY3VzZWQ6IGJvb2xlYW4gfSApID0+IHtcblx0cmV0dXJuIGNzcygge1xuXHRcdGJhY2tncm91bmRDb2xvcjogJ2N1cnJlbnRDb2xvcicsXG5cdFx0b3BhY2l0eTogaXNGb2N1c2VkID8gMSA6IDAuMyxcblx0fSApO1xufTtcblxuY29uc3QgU3Ryb2tlID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHQkeyBzdHJva2VGb2N1cyB9O1xuYDtcblxuY29uc3QgVmVydGljYWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRib3R0b206IDNweDtcblx0dG9wOiAzcHg7XG5cdHdpZHRoOiAycHg7XG5gO1xuXG5jb25zdCBIb3Jpem9udGFsU3Ryb2tlID0gc3R5bGVkKCBTdHJva2UgKWBcblx0aGVpZ2h0OiAycHg7XG5cdGxlZnQ6IDNweDtcblx0cmlnaHQ6IDNweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBUb3BTdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0dG9wOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJpZ2h0U3Ryb2tlID0gc3R5bGVkKCBWZXJ0aWNhbFN0cm9rZSApYFxuXHRyaWdodDogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBCb3R0b21TdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0Ym90dG9tOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IExlZnRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdGxlZnQ6IDA7XG5gO1xuIl19 */"));
const VerticalStroke = ( /*#__PURE__*/0, _base.default)(Stroke, process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z5"
} : {
  target: "e1j5nr4z5",
  label: "VerticalStroke"
})(process.env.NODE_ENV === "production" ? {
  name: "1k2w39q",
  styles: "bottom:3px;top:3px;width:2px"
} : {
  name: "1k2w39q",
  styles: "bottom:3px;top:3px;width:2px",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQXNDdUMiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtaWNvbi1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMjRweDtcblx0aGVpZ2h0OiAyNHB4O1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHBhZGRpbmc6IDRweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBWaWV3Ym94ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDEwMCU7XG5gO1xuXG5jb25zdCBzdHJva2VGb2N1cyA9ICggeyBpc0ZvY3VzZWQgfTogeyBpc0ZvY3VzZWQ6IGJvb2xlYW4gfSApID0+IHtcblx0cmV0dXJuIGNzcygge1xuXHRcdGJhY2tncm91bmRDb2xvcjogJ2N1cnJlbnRDb2xvcicsXG5cdFx0b3BhY2l0eTogaXNGb2N1c2VkID8gMSA6IDAuMyxcblx0fSApO1xufTtcblxuY29uc3QgU3Ryb2tlID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHQkeyBzdHJva2VGb2N1cyB9O1xuYDtcblxuY29uc3QgVmVydGljYWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRib3R0b206IDNweDtcblx0dG9wOiAzcHg7XG5cdHdpZHRoOiAycHg7XG5gO1xuXG5jb25zdCBIb3Jpem9udGFsU3Ryb2tlID0gc3R5bGVkKCBTdHJva2UgKWBcblx0aGVpZ2h0OiAycHg7XG5cdGxlZnQ6IDNweDtcblx0cmlnaHQ6IDNweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBUb3BTdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0dG9wOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJpZ2h0U3Ryb2tlID0gc3R5bGVkKCBWZXJ0aWNhbFN0cm9rZSApYFxuXHRyaWdodDogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBCb3R0b21TdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0Ym90dG9tOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IExlZnRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdGxlZnQ6IDA7XG5gO1xuIl19 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
const HorizontalStroke = ( /*#__PURE__*/0, _base.default)(Stroke, process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z4"
} : {
  target: "e1j5nr4z4",
  label: "HorizontalStroke"
})(process.env.NODE_ENV === "production" ? {
  name: "1q9b07k",
  styles: "height:2px;left:3px;right:3px"
} : {
  name: "1q9b07k",
  styles: "height:2px;left:3px;right:3px",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQTRDeUMiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtaWNvbi1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMjRweDtcblx0aGVpZ2h0OiAyNHB4O1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHBhZGRpbmc6IDRweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBWaWV3Ym94ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDEwMCU7XG5gO1xuXG5jb25zdCBzdHJva2VGb2N1cyA9ICggeyBpc0ZvY3VzZWQgfTogeyBpc0ZvY3VzZWQ6IGJvb2xlYW4gfSApID0+IHtcblx0cmV0dXJuIGNzcygge1xuXHRcdGJhY2tncm91bmRDb2xvcjogJ2N1cnJlbnRDb2xvcicsXG5cdFx0b3BhY2l0eTogaXNGb2N1c2VkID8gMSA6IDAuMyxcblx0fSApO1xufTtcblxuY29uc3QgU3Ryb2tlID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHQkeyBzdHJva2VGb2N1cyB9O1xuYDtcblxuY29uc3QgVmVydGljYWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRib3R0b206IDNweDtcblx0dG9wOiAzcHg7XG5cdHdpZHRoOiAycHg7XG5gO1xuXG5jb25zdCBIb3Jpem9udGFsU3Ryb2tlID0gc3R5bGVkKCBTdHJva2UgKWBcblx0aGVpZ2h0OiAycHg7XG5cdGxlZnQ6IDNweDtcblx0cmlnaHQ6IDNweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBUb3BTdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0dG9wOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJpZ2h0U3Ryb2tlID0gc3R5bGVkKCBWZXJ0aWNhbFN0cm9rZSApYFxuXHRyaWdodDogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBCb3R0b21TdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0Ym90dG9tOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IExlZnRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdGxlZnQ6IDA7XG5gO1xuIl19 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
const TopStroke = ( /*#__PURE__*/0, _base.default)(HorizontalStroke, process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z3"
} : {
  target: "e1j5nr4z3",
  label: "TopStroke"
})(process.env.NODE_ENV === "production" ? {
  name: "abcix4",
  styles: "top:0"
} : {
  name: "abcix4",
  styles: "top:0",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQWtEbUQiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtaWNvbi1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMjRweDtcblx0aGVpZ2h0OiAyNHB4O1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHBhZGRpbmc6IDRweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBWaWV3Ym94ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDEwMCU7XG5gO1xuXG5jb25zdCBzdHJva2VGb2N1cyA9ICggeyBpc0ZvY3VzZWQgfTogeyBpc0ZvY3VzZWQ6IGJvb2xlYW4gfSApID0+IHtcblx0cmV0dXJuIGNzcygge1xuXHRcdGJhY2tncm91bmRDb2xvcjogJ2N1cnJlbnRDb2xvcicsXG5cdFx0b3BhY2l0eTogaXNGb2N1c2VkID8gMSA6IDAuMyxcblx0fSApO1xufTtcblxuY29uc3QgU3Ryb2tlID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHQkeyBzdHJva2VGb2N1cyB9O1xuYDtcblxuY29uc3QgVmVydGljYWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRib3R0b206IDNweDtcblx0dG9wOiAzcHg7XG5cdHdpZHRoOiAycHg7XG5gO1xuXG5jb25zdCBIb3Jpem9udGFsU3Ryb2tlID0gc3R5bGVkKCBTdHJva2UgKWBcblx0aGVpZ2h0OiAycHg7XG5cdGxlZnQ6IDNweDtcblx0cmlnaHQ6IDNweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBUb3BTdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0dG9wOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJpZ2h0U3Ryb2tlID0gc3R5bGVkKCBWZXJ0aWNhbFN0cm9rZSApYFxuXHRyaWdodDogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBCb3R0b21TdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0Ym90dG9tOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IExlZnRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdGxlZnQ6IDA7XG5gO1xuIl19 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.TopStroke = TopStroke;
const RightStroke = ( /*#__PURE__*/0, _base.default)(VerticalStroke, process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z2"
} : {
  target: "e1j5nr4z2",
  label: "RightStroke"
})(process.env.NODE_ENV === "production" ? {
  name: "1wf8jf",
  styles: "right:0"
} : {
  name: "1wf8jf",
  styles: "right:0",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQXNEbUQiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtaWNvbi1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMjRweDtcblx0aGVpZ2h0OiAyNHB4O1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHBhZGRpbmc6IDRweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBWaWV3Ym94ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDEwMCU7XG5gO1xuXG5jb25zdCBzdHJva2VGb2N1cyA9ICggeyBpc0ZvY3VzZWQgfTogeyBpc0ZvY3VzZWQ6IGJvb2xlYW4gfSApID0+IHtcblx0cmV0dXJuIGNzcygge1xuXHRcdGJhY2tncm91bmRDb2xvcjogJ2N1cnJlbnRDb2xvcicsXG5cdFx0b3BhY2l0eTogaXNGb2N1c2VkID8gMSA6IDAuMyxcblx0fSApO1xufTtcblxuY29uc3QgU3Ryb2tlID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHQkeyBzdHJva2VGb2N1cyB9O1xuYDtcblxuY29uc3QgVmVydGljYWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRib3R0b206IDNweDtcblx0dG9wOiAzcHg7XG5cdHdpZHRoOiAycHg7XG5gO1xuXG5jb25zdCBIb3Jpem9udGFsU3Ryb2tlID0gc3R5bGVkKCBTdHJva2UgKWBcblx0aGVpZ2h0OiAycHg7XG5cdGxlZnQ6IDNweDtcblx0cmlnaHQ6IDNweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBUb3BTdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0dG9wOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJpZ2h0U3Ryb2tlID0gc3R5bGVkKCBWZXJ0aWNhbFN0cm9rZSApYFxuXHRyaWdodDogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBCb3R0b21TdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0Ym90dG9tOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IExlZnRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdGxlZnQ6IDA7XG5gO1xuIl19 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.RightStroke = RightStroke;
const BottomStroke = ( /*#__PURE__*/0, _base.default)(HorizontalStroke, process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z1"
} : {
  target: "e1j5nr4z1",
  label: "BottomStroke"
})(process.env.NODE_ENV === "production" ? {
  name: "8tapst",
  styles: "bottom:0"
} : {
  name: "8tapst",
  styles: "bottom:0",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQTBEc0QiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtaWNvbi1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMjRweDtcblx0aGVpZ2h0OiAyNHB4O1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHBhZGRpbmc6IDRweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBWaWV3Ym94ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDEwMCU7XG5gO1xuXG5jb25zdCBzdHJva2VGb2N1cyA9ICggeyBpc0ZvY3VzZWQgfTogeyBpc0ZvY3VzZWQ6IGJvb2xlYW4gfSApID0+IHtcblx0cmV0dXJuIGNzcygge1xuXHRcdGJhY2tncm91bmRDb2xvcjogJ2N1cnJlbnRDb2xvcicsXG5cdFx0b3BhY2l0eTogaXNGb2N1c2VkID8gMSA6IDAuMyxcblx0fSApO1xufTtcblxuY29uc3QgU3Ryb2tlID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHQkeyBzdHJva2VGb2N1cyB9O1xuYDtcblxuY29uc3QgVmVydGljYWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRib3R0b206IDNweDtcblx0dG9wOiAzcHg7XG5cdHdpZHRoOiAycHg7XG5gO1xuXG5jb25zdCBIb3Jpem9udGFsU3Ryb2tlID0gc3R5bGVkKCBTdHJva2UgKWBcblx0aGVpZ2h0OiAycHg7XG5cdGxlZnQ6IDNweDtcblx0cmlnaHQ6IDNweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBUb3BTdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0dG9wOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJpZ2h0U3Ryb2tlID0gc3R5bGVkKCBWZXJ0aWNhbFN0cm9rZSApYFxuXHRyaWdodDogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBCb3R0b21TdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0Ym90dG9tOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IExlZnRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdGxlZnQ6IDA7XG5gO1xuIl19 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.BottomStroke = BottomStroke;
const LeftStroke = ( /*#__PURE__*/0, _base.default)(VerticalStroke, process.env.NODE_ENV === "production" ? {
  target: "e1j5nr4z0"
} : {
  target: "e1j5nr4z0",
  label: "LeftStroke"
})(process.env.NODE_ENV === "production" ? {
  name: "1ode3cm",
  styles: "left:0"
} : {
  name: "1ode3cm",
  styles: "left:0",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLWljb24tc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQThEa0QiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtaWNvbi1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHR3aWR0aDogMjRweDtcblx0aGVpZ2h0OiAyNHB4O1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHBhZGRpbmc6IDRweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBWaWV3Ym94ID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IDEwMCU7XG5gO1xuXG5jb25zdCBzdHJva2VGb2N1cyA9ICggeyBpc0ZvY3VzZWQgfTogeyBpc0ZvY3VzZWQ6IGJvb2xlYW4gfSApID0+IHtcblx0cmV0dXJuIGNzcygge1xuXHRcdGJhY2tncm91bmRDb2xvcjogJ2N1cnJlbnRDb2xvcicsXG5cdFx0b3BhY2l0eTogaXNGb2N1c2VkID8gMSA6IDAuMyxcblx0fSApO1xufTtcblxuY29uc3QgU3Ryb2tlID0gc3R5bGVkLnNwYW5gXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHQkeyBzdHJva2VGb2N1cyB9O1xuYDtcblxuY29uc3QgVmVydGljYWxTdHJva2UgPSBzdHlsZWQoIFN0cm9rZSApYFxuXHRib3R0b206IDNweDtcblx0dG9wOiAzcHg7XG5cdHdpZHRoOiAycHg7XG5gO1xuXG5jb25zdCBIb3Jpem9udGFsU3Ryb2tlID0gc3R5bGVkKCBTdHJva2UgKWBcblx0aGVpZ2h0OiAycHg7XG5cdGxlZnQ6IDNweDtcblx0cmlnaHQ6IDNweDtcbmA7XG5cbmV4cG9ydCBjb25zdCBUb3BTdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0dG9wOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJpZ2h0U3Ryb2tlID0gc3R5bGVkKCBWZXJ0aWNhbFN0cm9rZSApYFxuXHRyaWdodDogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBCb3R0b21TdHJva2UgPSBzdHlsZWQoIEhvcml6b250YWxTdHJva2UgKWBcblx0Ym90dG9tOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IExlZnRTdHJva2UgPSBzdHlsZWQoIFZlcnRpY2FsU3Ryb2tlIClgXG5cdGxlZnQ6IDA7XG5gO1xuIl19 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.LeftStroke = LeftStroke;
//# sourceMappingURL=box-control-icon-styles.js.map