{"version": 3, "names": ["_react", "require", "_utils", "_EMOTION_STRINGIFIED_CSS_ERROR__", "borderBoxControl", "css", "process", "env", "NODE_ENV", "exports", "linkedBorderControl", "rtl", "marginRight", "wrapper", "name", "styles", "map", "toString", "borderBoxControlLinkedButton", "size", "right", "borderBoxStyleWithFallback", "border", "color", "COLORS", "gray", "style", "width", "CONFIG", "borderWidth", "<PERSON><PERSON><PERSON><PERSON>", "hasVisibleBorder", "borderStyle", "borderBoxControlVisualizer", "borders", "top", "bottom", "borderLeft", "left", "borderRight", "borderBoxControlSplitControls", "undefined", "centeredBorderControl", "rightBorderControl", "marginLeft"], "sources": ["@wordpress/components/src/border-box-control/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { COLORS, CONFIG, rtl } from '../utils';\n\nimport type { Border } from '../border-control/types';\nimport type { Borders } from './types';\n\nexport const borderBoxControl = css``;\n\nexport const linkedBorderControl = () => css`\n\tflex: 1;\n\t${ rtl( { marginRight: '24px' } )() }\n`;\n\nexport const wrapper = css`\n\tposition: relative;\n`;\n\nexport const borderBoxControlLinkedButton = (\n\tsize?: 'default' | '__unstable-large'\n) => {\n\treturn css`\n\t\tposition: absolute;\n\t\ttop: ${ size === '__unstable-large' ? '8px' : '3px' };\n\t\t${ rtl( { right: 0 } )() }\n\t\tline-height: 0;\n\t`;\n};\n\nconst borderBoxStyleWithFallback = ( border?: Border ) => {\n\tconst {\n\t\tcolor = COLORS.gray[ 200 ],\n\t\tstyle = 'solid',\n\t\twidth = CONFIG.borderWidth,\n\t} = border || {};\n\n\tconst clampedWidth =\n\t\twidth !== CONFIG.borderWidth ? `clamp(1px, ${ width }, 10px)` : width;\n\tconst hasVisibleBorder = ( !! width && width !== '0' ) || !! color;\n\tconst borderStyle = hasVisibleBorder ? style || 'solid' : style;\n\n\treturn `${ color } ${ borderStyle } ${ clampedWidth }`;\n};\n\nexport const borderBoxControlVisualizer = (\n\tborders?: Borders,\n\tsize?: 'default' | '__unstable-large'\n) => {\n\treturn css`\n\t\tposition: absolute;\n\t\ttop: ${ size === '__unstable-large' ? '20px' : '15px' };\n\t\tright: ${ size === '__unstable-large' ? '39px' : '29px' };\n\t\tbottom: ${ size === '__unstable-large' ? '20px' : '15px' };\n\t\tleft: ${ size === '__unstable-large' ? '39px' : '29px' };\n\t\tborder-top: ${ borderBoxStyleWithFallback( borders?.top ) };\n\t\tborder-bottom: ${ borderBoxStyleWithFallback( borders?.bottom ) };\n\t\t${ rtl( {\n\t\t\tborderLeft: borderBoxStyleWithFallback( borders?.left ),\n\t\t} )() }\n\t\t${ rtl( {\n\t\t\tborderRight: borderBoxStyleWithFallback( borders?.right ),\n\t\t} )() }\n\t`;\n};\n\nexport const borderBoxControlSplitControls = (\n\tsize?: 'default' | '__unstable-large'\n) => css`\n\tposition: relative;\n\tflex: 1;\n\twidth: ${ size === '__unstable-large' ? undefined : '80%' };\n`;\n\nexport const centeredBorderControl = css`\n\tgrid-column: span 2;\n\tmargin: 0 auto;\n`;\n\nexport const rightBorderControl = () => css`\n\t${ rtl( { marginLeft: 'auto' } )() }\n`;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AAA+C,SAAAE,iCAAA;AAKxC,MAAMC,gBAAgB,oBAAGC,UAAG,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,qDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,u6GAAE;AAACC,OAAA,CAAAL,gBAAA,GAAAA,gBAAA;AAE/B,MAAMM,mBAAmB,GAAGA,CAAA,sBAAML,UAAG,aAExC,IAAAM,UAAG,EAAE;EAAEC,WAAW,EAAE;AAAO,CAAE,CAAC,CAAC,CAAC,SAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,yDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,u6GACnC;AAACC,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAEK,MAAMG,OAAO,GAAAP,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAM,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAd;AAAA,CAEnB;AAACM,OAAA,CAAAI,OAAA,GAAAA,OAAA;AAEK,MAAMK,4BAA4B,GACxCC,IAAqC,IACjC;EACJ,wBAAOd,UAAG,4BAEDc,IAAI,KAAK,kBAAkB,GAAG,KAAK,GAAG,KAAK,OAChD,IAAAR,UAAG,EAAE;IAAES,KAAK,EAAE;EAAE,CAAE,CAAC,CAAC,CAAC,uBAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA,kEAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAG1B,CAAC;AAACC,OAAA,CAAAS,4BAAA,GAAAA,4BAAA;AAEF,MAAMG,0BAA0B,GAAKC,MAAe,IAAM;EACzD,MAAM;IACLC,KAAK,GAAGC,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE;IAC1BC,KAAK,GAAG,OAAO;IACfC,KAAK,GAAGC,aAAM,CAACC;EAChB,CAAC,GAAGP,MAAM,IAAI,CAAC,CAAC;EAEhB,MAAMQ,YAAY,GACjBH,KAAK,KAAKC,aAAM,CAACC,WAAW,GAAI,cAAcF,KAAO,SAAQ,GAAGA,KAAK;EACtE,MAAMI,gBAAgB,GAAK,CAAC,CAAEJ,KAAK,IAAIA,KAAK,KAAK,GAAG,IAAM,CAAC,CAAEJ,KAAK;EAClE,MAAMS,WAAW,GAAGD,gBAAgB,GAAGL,KAAK,IAAI,OAAO,GAAGA,KAAK;EAE/D,OAAQ,GAAGH,KAAO,IAAIS,WAAa,IAAIF,YAAc,EAAC;AACvD,CAAC;AAEM,MAAMG,0BAA0B,GAAGA,CACzCC,OAAiB,EACjBf,IAAqC,KACjC;EACJ,wBAAOd,UAAG,4BAEDc,IAAI,KAAK,kBAAkB,GAAG,MAAM,GAAG,MAAM,aAC3CA,IAAI,KAAK,kBAAkB,GAAG,MAAM,GAAG,MAAM,cAC5CA,IAAI,KAAK,kBAAkB,GAAG,MAAM,GAAG,MAAM,YAC/CA,IAAI,KAAK,kBAAkB,GAAG,MAAM,GAAG,MAAM,kBACvCE,0BAA0B,CAAEa,OAAO,EAAEC,GAAI,CAAC,qBACvCd,0BAA0B,CAAEa,OAAO,EAAEE,MAAO,CAAC,OAC5D,IAAAzB,UAAG,EAAE;IACP0B,UAAU,EAAEhB,0BAA0B,CAAEa,OAAO,EAAEI,IAAK;EACvD,CAAE,CAAC,CAAC,CAAC,OACF,IAAA3B,UAAG,EAAE;IACP4B,WAAW,EAAElB,0BAA0B,CAAEa,OAAO,EAAEd,KAAM;EACzD,CAAE,CAAC,CAAC,CAAC,SAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA,gEAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAEP,CAAC;AAACC,OAAA,CAAAwB,0BAAA,GAAAA,0BAAA;AAEK,MAAMO,6BAA6B,GACzCrB,IAAqC,qBACjCd,UAAG,qCAGGc,IAAI,KAAK,kBAAkB,GAAGsB,SAAS,GAAG,KAAK,SAAAnC,OAAA,CAAAC,GAAA,CAAAC,QAAA,mEAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,u6GACzD;AAACC,OAAA,CAAA+B,6BAAA,GAAAA,6BAAA;AAEK,MAAME,qBAAqB,GAAApC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAM,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAd;AAAA,CAGjC;AAACM,OAAA,CAAAiC,qBAAA,GAAAA,qBAAA;AAEK,MAAMC,kBAAkB,GAAGA,CAAA,sBAAMtC,UAAG,EACvC,IAAAM,UAAG,EAAE;EAAEiC,UAAU,EAAE;AAAO,CAAE,CAAC,CAAC,CAAC,SAAAtC,OAAA,CAAAC,GAAA,CAAAC,QAAA,wDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,26GAClC;AAACC,OAAA,CAAAkC,kBAAA,GAAAA,kBAAA"}