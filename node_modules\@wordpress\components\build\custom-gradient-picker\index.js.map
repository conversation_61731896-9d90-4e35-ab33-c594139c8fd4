{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_deprecated", "_i18n", "_anglePickerControl", "_gradientBar", "_flex", "_selectControl", "_vStack", "_utils", "_serializer", "_constants", "_customGradientPickerStyles", "GradientAnglePicker", "gradientAST", "hasGradient", "onChange", "_gradientAST$orientat", "angle", "orientation", "value", "DEFAULT_LINEAR_GRADIENT_ANGLE", "onAngleChange", "newAngle", "serializeGradient", "type", "_react", "createElement", "default", "__nextHasNoMarginBottom", "GradientTypePicker", "onSetLinearGradient", "undefined", "HORIZONTAL_GRADIENT_ORIENTATION", "onSetRadialGradient", "restGradientAST", "handleOnChange", "next", "className", "label", "__", "labelPosition", "options", "GRADIENT_OPTIONS", "size", "CustomGradientPicker", "__nextHasNoMargin", "__experimentalIsRenderedInSidebar", "getGradientAstWithDefault", "background", "getLinearGradientRepresentation", "controlPoints", "colorStops", "map", "colorStop", "color", "getStopCssColor", "position", "parseInt", "length", "deprecated", "since", "version", "hint", "VStack", "spacing", "classnames", "newControlPoints", "getGradientAstWithControlPoints", "Flex", "gap", "SelectWrapper", "AccessoryWrapper", "_default", "exports"], "sources": ["@wordpress/components/src/custom-gradient-picker/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type gradientParser from 'gradient-parser';\n\n/**\n * WordPress dependencies\n */\nimport deprecated from '@wordpress/deprecated';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport AnglePickerControl from '../angle-picker-control';\nimport CustomGradientBar from './gradient-bar';\nimport { Flex } from '../flex';\nimport SelectControl from '../select-control';\nimport { VStack } from '../v-stack';\nimport {\n\tgetGradientAstWithDefault,\n\tgetLinearGradientRepresentation,\n\tgetGradientAstWithControlPoints,\n\tgetStopCssColor,\n} from './utils';\nimport { serializeGradient } from './serializer';\nimport {\n\tDEFAULT_LINEAR_GRADIENT_ANGLE,\n\tHORIZONTAL_GRADIENT_ORIENTATION,\n\tGRADIENT_OPTIONS,\n} from './constants';\nimport {\n\tAccessoryWrapper,\n\tSelectWrapper,\n} from './styles/custom-gradient-picker-styles';\nimport type {\n\tCustomGradientPickerProps,\n\tGradientAnglePickerProps,\n\tGradientTypePickerProps,\n} from './types';\n\nconst GradientAnglePicker = ( {\n\tgradientAST,\n\thasGradient,\n\tonChange,\n}: GradientAnglePickerProps ) => {\n\tconst angle =\n\t\tgradientAST?.orientation?.value ?? DEFAULT_LINEAR_GRADIENT_ANGLE;\n\tconst onAngleChange = ( newAngle: number ) => {\n\t\tonChange(\n\t\t\tserializeGradient( {\n\t\t\t\t...gradientAST,\n\t\t\t\torientation: {\n\t\t\t\t\ttype: 'angular',\n\t\t\t\t\tvalue: `${ newAngle }`,\n\t\t\t\t},\n\t\t\t} )\n\t\t);\n\t};\n\treturn (\n\t\t<AnglePickerControl\n\t\t\t__nextHasNoMarginBottom\n\t\t\tonChange={ onAngleChange }\n\t\t\tvalue={ hasGradient ? angle : '' }\n\t\t/>\n\t);\n};\n\nconst GradientTypePicker = ( {\n\tgradientAST,\n\thasGradient,\n\tonChange,\n}: GradientTypePickerProps ) => {\n\tconst { type } = gradientAST;\n\n\tconst onSetLinearGradient = () => {\n\t\tonChange(\n\t\t\tserializeGradient( {\n\t\t\t\t...gradientAST,\n\t\t\t\torientation: gradientAST.orientation\n\t\t\t\t\t? undefined\n\t\t\t\t\t: HORIZONTAL_GRADIENT_ORIENTATION,\n\t\t\t\ttype: 'linear-gradient',\n\t\t\t} as gradientParser.LinearGradientNode )\n\t\t);\n\t};\n\n\tconst onSetRadialGradient = () => {\n\t\tconst { orientation, ...restGradientAST } = gradientAST;\n\t\tonChange(\n\t\t\tserializeGradient( {\n\t\t\t\t...restGradientAST,\n\t\t\t\ttype: 'radial-gradient',\n\t\t\t} )\n\t\t);\n\t};\n\n\tconst handleOnChange = ( next: string ) => {\n\t\tif ( next === 'linear-gradient' ) {\n\t\t\tonSetLinearGradient();\n\t\t}\n\t\tif ( next === 'radial-gradient' ) {\n\t\t\tonSetRadialGradient();\n\t\t}\n\t};\n\n\treturn (\n\t\t<SelectControl\n\t\t\t__nextHasNoMarginBottom\n\t\t\tclassName=\"components-custom-gradient-picker__type-picker\"\n\t\t\tlabel={ __( 'Type' ) }\n\t\t\tlabelPosition=\"top\"\n\t\t\tonChange={ handleOnChange }\n\t\t\toptions={ GRADIENT_OPTIONS }\n\t\t\tsize=\"__unstable-large\"\n\t\t\tvalue={ hasGradient ? type : undefined }\n\t\t/>\n\t);\n};\n\n/**\n * CustomGradientPicker is a React component that renders a UI for specifying\n * linear or radial gradients. Radial gradients are displayed in the picker as\n * a slice of the gradient from the center to the outside.\n *\n * ```jsx\n * import { CustomGradientPicker } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyCustomGradientPicker = () => {\n *   const [ gradient, setGradient ] = useState();\n *\n *   return (\n *     <CustomGradientPicker\n *\t\t\tvalue={ gradient }\n *\t\t\tonChange={ setGradient }\n *     />\n *   );\n * };\n * ```\n */\nexport function CustomGradientPicker( {\n\t/** Start opting into the new margin-free styles that will become the default in a future version. */\n\t__nextHasNoMargin = false,\n\tvalue,\n\tonChange,\n\t__experimentalIsRenderedInSidebar = false,\n}: CustomGradientPickerProps ) {\n\tconst { gradientAST, hasGradient } = getGradientAstWithDefault( value );\n\n\t// On radial gradients the bar should display a linear gradient.\n\t// On radial gradients the bar represents a slice of the gradient from the center until the outside.\n\t// On liner gradients the bar represents the color stops from left to right independently of the angle.\n\tconst background = getLinearGradientRepresentation( gradientAST );\n\n\t// Control points color option may be hex from presets, custom colors will be rgb.\n\t// The position should always be a percentage.\n\tconst controlPoints = gradientAST.colorStops.map( ( colorStop ) => {\n\t\treturn {\n\t\t\tcolor: getStopCssColor( colorStop ),\n\t\t\t// Although it's already been checked by `hasUnsupportedLength` in `getGradientAstWithDefault`,\n\t\t\t// TypeScript doesn't know that `colorStop.length` is not undefined here.\n\t\t\t// @ts-expect-error\n\t\t\tposition: parseInt( colorStop.length.value ),\n\t\t};\n\t} );\n\n\tif ( ! __nextHasNoMargin ) {\n\t\tdeprecated(\n\t\t\t'Outer margin styles for wp.components.CustomGradientPicker',\n\t\t\t{\n\t\t\t\tsince: '6.1',\n\t\t\t\tversion: '6.4',\n\t\t\t\thint: 'Set the `__nextHasNoMargin` prop to true to start opting into the new styles, which will become the default in a future version',\n\t\t\t}\n\t\t);\n\t}\n\n\treturn (\n\t\t<VStack\n\t\t\tspacing={ 4 }\n\t\t\tclassName={ classnames( 'components-custom-gradient-picker', {\n\t\t\t\t'is-next-has-no-margin': __nextHasNoMargin,\n\t\t\t} ) }\n\t\t>\n\t\t\t<CustomGradientBar\n\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t}\n\t\t\t\tbackground={ background }\n\t\t\t\thasGradient={ hasGradient }\n\t\t\t\tvalue={ controlPoints }\n\t\t\t\tonChange={ ( newControlPoints ) => {\n\t\t\t\t\tonChange(\n\t\t\t\t\t\tserializeGradient(\n\t\t\t\t\t\t\tgetGradientAstWithControlPoints(\n\t\t\t\t\t\t\t\tgradientAST,\n\t\t\t\t\t\t\t\tnewControlPoints\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t\t\t} }\n\t\t\t/>\n\t\t\t<Flex\n\t\t\t\tgap={ 3 }\n\t\t\t\tclassName=\"components-custom-gradient-picker__ui-line\"\n\t\t\t>\n\t\t\t\t<SelectWrapper>\n\t\t\t\t\t<GradientTypePicker\n\t\t\t\t\t\tgradientAST={ gradientAST }\n\t\t\t\t\t\thasGradient={ hasGradient }\n\t\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\t/>\n\t\t\t\t</SelectWrapper>\n\t\t\t\t<AccessoryWrapper>\n\t\t\t\t\t{ gradientAST.type === 'linear-gradient' && (\n\t\t\t\t\t\t<GradientAnglePicker\n\t\t\t\t\t\t\tgradientAST={ gradientAST }\n\t\t\t\t\t\t\thasGradient={ hasGradient }\n\t\t\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t</AccessoryWrapper>\n\t\t\t</Flex>\n\t\t</VStack>\n\t);\n}\n\nexport default CustomGradientPicker;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAKA,IAAAG,mBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,YAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,cAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AACA,IAAAQ,MAAA,GAAAR,OAAA;AAMA,IAAAS,WAAA,GAAAT,OAAA;AACA,IAAAU,UAAA,GAAAV,OAAA;AAKA,IAAAW,2BAAA,GAAAX,OAAA;AAhCA;AACA;AACA;;AAIA;AACA;AACA;;AAIA;AACA;AACA;;AA4BA,MAAMY,mBAAmB,GAAGA,CAAE;EAC7BC,WAAW;EACXC,WAAW;EACXC;AACyB,CAAC,KAAM;EAAA,IAAAC,qBAAA;EAChC,MAAMC,KAAK,IAAAD,qBAAA,GACVH,WAAW,EAAEK,WAAW,EAAEC,KAAK,cAAAH,qBAAA,cAAAA,qBAAA,GAAII,wCAA6B;EACjE,MAAMC,aAAa,GAAKC,QAAgB,IAAM;IAC7CP,QAAQ,CACP,IAAAQ,6BAAiB,EAAE;MAClB,GAAGV,WAAW;MACdK,WAAW,EAAE;QACZM,IAAI,EAAE,SAAS;QACfL,KAAK,EAAG,GAAGG,QAAU;MACtB;IACD,CAAE,CACH,CAAC;EACF,CAAC;EACD,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACvB,mBAAA,CAAAwB,OAAkB;IAClBC,uBAAuB;IACvBb,QAAQ,EAAGM,aAAe;IAC1BF,KAAK,EAAGL,WAAW,GAAGG,KAAK,GAAG;EAAI,CAClC,CAAC;AAEJ,CAAC;AAED,MAAMY,kBAAkB,GAAGA,CAAE;EAC5BhB,WAAW;EACXC,WAAW;EACXC;AACwB,CAAC,KAAM;EAC/B,MAAM;IAAES;EAAK,CAAC,GAAGX,WAAW;EAE5B,MAAMiB,mBAAmB,GAAGA,CAAA,KAAM;IACjCf,QAAQ,CACP,IAAAQ,6BAAiB,EAAE;MAClB,GAAGV,WAAW;MACdK,WAAW,EAAEL,WAAW,CAACK,WAAW,GACjCa,SAAS,GACTC,0CAA+B;MAClCR,IAAI,EAAE;IACP,CAAuC,CACxC,CAAC;EACF,CAAC;EAED,MAAMS,mBAAmB,GAAGA,CAAA,KAAM;IACjC,MAAM;MAAEf,WAAW;MAAE,GAAGgB;IAAgB,CAAC,GAAGrB,WAAW;IACvDE,QAAQ,CACP,IAAAQ,6BAAiB,EAAE;MAClB,GAAGW,eAAe;MAClBV,IAAI,EAAE;IACP,CAAE,CACH,CAAC;EACF,CAAC;EAED,MAAMW,cAAc,GAAKC,IAAY,IAAM;IAC1C,IAAKA,IAAI,KAAK,iBAAiB,EAAG;MACjCN,mBAAmB,CAAC,CAAC;IACtB;IACA,IAAKM,IAAI,KAAK,iBAAiB,EAAG;MACjCH,mBAAmB,CAAC,CAAC;IACtB;EACD,CAAC;EAED,OACC,IAAAR,MAAA,CAAAC,aAAA,EAACpB,cAAA,CAAAqB,OAAa;IACbC,uBAAuB;IACvBS,SAAS,EAAC,gDAAgD;IAC1DC,KAAK,EAAG,IAAAC,QAAE,EAAE,MAAO,CAAG;IACtBC,aAAa,EAAC,KAAK;IACnBzB,QAAQ,EAAGoB,cAAgB;IAC3BM,OAAO,EAAGC,2BAAkB;IAC5BC,IAAI,EAAC,kBAAkB;IACvBxB,KAAK,EAAGL,WAAW,GAAGU,IAAI,GAAGO;EAAW,CACxC,CAAC;AAEJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASa,oBAAoBA,CAAE;EACrC;EACAC,iBAAiB,GAAG,KAAK;EACzB1B,KAAK;EACLJ,QAAQ;EACR+B,iCAAiC,GAAG;AACV,CAAC,EAAG;EAC9B,MAAM;IAAEjC,WAAW;IAAEC;EAAY,CAAC,GAAG,IAAAiC,gCAAyB,EAAE5B,KAAM,CAAC;;EAEvE;EACA;EACA;EACA,MAAM6B,UAAU,GAAG,IAAAC,sCAA+B,EAAEpC,WAAY,CAAC;;EAEjE;EACA;EACA,MAAMqC,aAAa,GAAGrC,WAAW,CAACsC,UAAU,CAACC,GAAG,CAAIC,SAAS,IAAM;IAClE,OAAO;MACNC,KAAK,EAAE,IAAAC,sBAAe,EAAEF,SAAU,CAAC;MACnC;MACA;MACA;MACAG,QAAQ,EAAEC,QAAQ,CAAEJ,SAAS,CAACK,MAAM,CAACvC,KAAM;IAC5C,CAAC;EACF,CAAE,CAAC;EAEH,IAAK,CAAE0B,iBAAiB,EAAG;IAC1B,IAAAc,mBAAU,EACT,4DAA4D,EAC5D;MACCC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE;IACP,CACD,CAAC;EACF;EAEA,OACC,IAAArC,MAAA,CAAAC,aAAA,EAACnB,OAAA,CAAAwD,MAAM;IACNC,OAAO,EAAG,CAAG;IACb3B,SAAS,EAAG,IAAA4B,mBAAU,EAAE,mCAAmC,EAAE;MAC5D,uBAAuB,EAAEpB;IAC1B,CAAE;EAAG,GAEL,IAAApB,MAAA,CAAAC,aAAA,EAACtB,YAAA,CAAAuB,OAAiB;IACjBmB,iCAAiC,EAChCA,iCACA;IACDE,UAAU,EAAGA,UAAY;IACzBlC,WAAW,EAAGA,WAAa;IAC3BK,KAAK,EAAG+B,aAAe;IACvBnC,QAAQ,EAAKmD,gBAAgB,IAAM;MAClCnD,QAAQ,CACP,IAAAQ,6BAAiB,EAChB,IAAA4C,sCAA+B,EAC9BtD,WAAW,EACXqD,gBACD,CACD,CACD,CAAC;IACF;EAAG,CACH,CAAC,EACF,IAAAzC,MAAA,CAAAC,aAAA,EAACrB,KAAA,CAAA+D,IAAI;IACJC,GAAG,EAAG,CAAG;IACThC,SAAS,EAAC;EAA4C,GAEtD,IAAAZ,MAAA,CAAAC,aAAA,EAACf,2BAAA,CAAA2D,aAAa,QACb,IAAA7C,MAAA,CAAAC,aAAA,EAACG,kBAAkB;IAClBhB,WAAW,EAAGA,WAAa;IAC3BC,WAAW,EAAGA,WAAa;IAC3BC,QAAQ,EAAGA;EAAU,CACrB,CACa,CAAC,EAChB,IAAAU,MAAA,CAAAC,aAAA,EAACf,2BAAA,CAAA4D,gBAAgB,QACd1D,WAAW,CAACW,IAAI,KAAK,iBAAiB,IACvC,IAAAC,MAAA,CAAAC,aAAA,EAACd,mBAAmB;IACnBC,WAAW,EAAGA,WAAa;IAC3BC,WAAW,EAAGA,WAAa;IAC3BC,QAAQ,EAAGA;EAAU,CACrB,CAEe,CACb,CACC,CAAC;AAEX;AAAC,IAAAyD,QAAA,GAEc5B,oBAAoB;AAAA6B,OAAA,CAAA9C,OAAA,GAAA6C,QAAA"}