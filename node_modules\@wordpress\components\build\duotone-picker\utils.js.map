{"version": 3, "names": ["_colord", "require", "_names", "_interopRequireDefault", "extend", "namesPlugin", "getDefaultColors", "palette", "length", "map", "color", "brightness", "colord", "reduce", "min", "max", "current", "getGradientFromCSSColors", "colors", "angle", "l", "stops", "c", "i", "join", "getColorStopsFromColors", "position", "getColorsFromColorStops", "colorStops"], "sources": ["@wordpress/components/src/duotone-picker/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { colord, extend } from 'colord';\nimport namesPlugin from 'colord/plugins/names';\n\n/**\n * Internal dependencies\n */\nimport type { DuotonePickerProps } from './types';\n\nextend( [ namesPlugin ] );\n\n/**\n * Object representation for a color.\n *\n * @typedef {Object} RGBColor\n * @property {number} r Red component of the color in the range [0,1].\n * @property {number} g Green component of the color in the range [0,1].\n * @property {number} b Blue component of the color in the range [0,1].\n */\n\n/**\n * Calculate the brightest and darkest values from a color palette.\n *\n * @param palette Color palette for the theme.\n *\n * @return Tuple of the darkest color and brightest color.\n */\nexport function getDefaultColors(\n\tpalette: DuotonePickerProps[ 'colorPalette' ]\n) {\n\t// A default dark and light color are required.\n\tif ( ! palette || palette.length < 2 ) return [ '#000', '#fff' ];\n\n\treturn palette\n\t\t.map( ( { color } ) => ( {\n\t\t\tcolor,\n\t\t\tbrightness: colord( color ).brightness(),\n\t\t} ) )\n\t\t.reduce(\n\t\t\t( [ min, max ], current ) => {\n\t\t\t\treturn [\n\t\t\t\t\tcurrent.brightness <= min.brightness ? current : min,\n\t\t\t\t\tcurrent.brightness >= max.brightness ? current : max,\n\t\t\t\t];\n\t\t\t},\n\t\t\t[\n\t\t\t\t{ brightness: 1, color: '' },\n\t\t\t\t{ brightness: 0, color: '' },\n\t\t\t]\n\t\t)\n\t\t.map( ( { color } ) => color );\n}\n\n/**\n * Generate a duotone gradient from a list of colors.\n *\n * @param colors CSS color strings.\n * @param angle  CSS gradient angle.\n *\n * @return  CSS gradient string for the duotone swatch.\n */\nexport function getGradientFromCSSColors(\n\tcolors: string[] = [],\n\tangle = '90deg'\n) {\n\tconst l = 100 / colors.length;\n\n\tconst stops = colors\n\t\t.map( ( c, i ) => `${ c } ${ i * l }%, ${ c } ${ ( i + 1 ) * l }%` )\n\t\t.join( ', ' );\n\n\treturn `linear-gradient( ${ angle }, ${ stops } )`;\n}\n\n/**\n * Convert a color array to an array of color stops.\n *\n * @param colors CSS colors array\n *\n * @return Color stop information.\n */\nexport function getColorStopsFromColors( colors: string[] ) {\n\treturn colors.map( ( color, i ) => ( {\n\t\tposition: ( i * 100 ) / ( colors.length - 1 ),\n\t\tcolor,\n\t} ) );\n}\n\n/**\n * Convert a color stop array to an array colors.\n *\n * @param colorStops Color stop information.\n *\n * @return CSS colors array.\n */\nexport function getColorsFromColorStops(\n\tcolorStops: { position: number; color: string }[] = []\n) {\n\treturn colorStops.map( ( { color } ) => color );\n}\n"], "mappings": ";;;;;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAJA;AACA;AACA;;AASA,IAAAG,cAAM,EAAE,CAAEC,cAAW,CAAG,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,gBAAgBA,CAC/BC,OAA6C,EAC5C;EACD;EACA,IAAK,CAAEA,OAAO,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAG,OAAO,CAAE,MAAM,EAAE,MAAM,CAAE;EAEhE,OAAOD,OAAO,CACZE,GAAG,CAAE,CAAE;IAAEC;EAAM,CAAC,MAAQ;IACxBA,KAAK;IACLC,UAAU,EAAE,IAAAC,cAAM,EAAEF,KAAM,CAAC,CAACC,UAAU,CAAC;EACxC,CAAC,CAAG,CAAC,CACJE,MAAM,CACN,CAAE,CAAEC,GAAG,EAAEC,GAAG,CAAE,EAAEC,OAAO,KAAM;IAC5B,OAAO,CACNA,OAAO,CAACL,UAAU,IAAIG,GAAG,CAACH,UAAU,GAAGK,OAAO,GAAGF,GAAG,EACpDE,OAAO,CAACL,UAAU,IAAII,GAAG,CAACJ,UAAU,GAAGK,OAAO,GAAGD,GAAG,CACpD;EACF,CAAC,EACD,CACC;IAAEJ,UAAU,EAAE,CAAC;IAAED,KAAK,EAAE;EAAG,CAAC,EAC5B;IAAEC,UAAU,EAAE,CAAC;IAAED,KAAK,EAAE;EAAG,CAAC,CAE9B,CAAC,CACAD,GAAG,CAAE,CAAE;IAAEC;EAAM,CAAC,KAAMA,KAAM,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASO,wBAAwBA,CACvCC,MAAgB,GAAG,EAAE,EACrBC,KAAK,GAAG,OAAO,EACd;EACD,MAAMC,CAAC,GAAG,GAAG,GAAGF,MAAM,CAACV,MAAM;EAE7B,MAAMa,KAAK,GAAGH,MAAM,CAClBT,GAAG,CAAE,CAAEa,CAAC,EAAEC,CAAC,KAAO,GAAGD,CAAG,IAAIC,CAAC,GAAGH,CAAG,MAAME,CAAG,IAAI,CAAEC,CAAC,GAAG,CAAC,IAAKH,CAAG,GAAG,CAAC,CACnEI,IAAI,CAAE,IAAK,CAAC;EAEd,OAAQ,oBAAoBL,KAAO,KAAKE,KAAO,IAAG;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,uBAAuBA,CAAEP,MAAgB,EAAG;EAC3D,OAAOA,MAAM,CAACT,GAAG,CAAE,CAAEC,KAAK,EAAEa,CAAC,MAAQ;IACpCG,QAAQ,EAAIH,CAAC,GAAG,GAAG,IAAOL,MAAM,CAACV,MAAM,GAAG,CAAC,CAAE;IAC7CE;EACD,CAAC,CAAG,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASiB,uBAAuBA,CACtCC,UAAiD,GAAG,EAAE,EACrD;EACD,OAAOA,UAAU,CAACnB,GAAG,CAAE,CAAE;IAAEC;EAAM,CAAC,KAAMA,KAAM,CAAC;AAChD"}