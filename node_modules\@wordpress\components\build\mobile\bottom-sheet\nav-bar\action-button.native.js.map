{"version": 3, "names": ["_reactNative", "require", "_styles", "_interopRequireDefault", "ActionButton", "onPress", "accessibilityLabel", "accessibilityHint", "children", "_react", "createElement", "TouchableWithoutFeedback", "accessibilityRole", "View", "style", "styles", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/nav-bar/action-button.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, TouchableWithoutFeedback } from 'react-native';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\n\n// Action button component is used by both Back and Apply Button componenets.\nfunction ActionButton( {\n\tonPress,\n\taccessibilityLabel,\n\taccessibilityHint,\n\tchildren,\n} ) {\n\treturn (\n\t\t<TouchableWithoutFeedback\n\t\t\tonPress={ onPress }\n\t\t\taccessibilityRole={ 'button' }\n\t\t\taccessibilityLabel={ accessibilityLabel }\n\t\t\taccessibilityHint={ accessibilityHint }\n\t\t>\n\t\t\t<View style={ styles[ 'action-button' ] }>{ children }</View>\n\t\t</TouchableWithoutFeedback>\n\t);\n}\n\nexport default ActionButton;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA,SAASG,YAAYA,CAAE;EACtBC,OAAO;EACPC,kBAAkB;EAClBC,iBAAiB;EACjBC;AACD,CAAC,EAAG;EACH,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAW,wBAAwB;IACxBN,OAAO,EAAGA,OAAS;IACnBO,iBAAiB,EAAG,QAAU;IAC9BN,kBAAkB,EAAGA,kBAAoB;IACzCC,iBAAiB,EAAGA;EAAmB,GAEvC,IAAAE,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAa,IAAI;IAACC,KAAK,EAAGC,eAAM,CAAE,eAAe;EAAI,GAAGP,QAAgB,CACnC,CAAC;AAE7B;AAAC,IAAAQ,QAAA,GAEcZ,YAAY;AAAAa,OAAA,CAAAC,OAAA,GAAAF,QAAA"}