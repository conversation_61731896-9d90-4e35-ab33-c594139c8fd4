"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _native = require("@react-navigation/native");
var _element = require("@wordpress/element");
var _components = require("@wordpress/components");
var _colorPicker = require("../../color-picker");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const PickerScreen = () => {
  const route = (0, _native.useRoute)();
  const navigation = (0, _native.useNavigation)();
  const {
    onShouldEnableInnerHandling,
    shouldEnableBottomSheetMaxHeight,
    onHandleClosingBottomSheet,
    isBottomSheetContentScrolling,
    shouldEnableBottomSheetScroll,
    onHandleHardwareButtonPress
  } = (0, _element.useContext)(_components.BottomSheetContext);
  const {
    setColor,
    currentValue,
    isGradientColor
  } = route.params;
  return (0, _element.useMemo)(() => {
    return (0, _react.createElement)(_colorPicker.ColorPicker, {
      onShouldEnableInnerHandling: onShouldEnableInnerHandling,
      shouldEnableBottomSheetMaxHeight: shouldEnableBottomSheetMaxHeight,
      setColor: setColor,
      activeColor: currentValue,
      isGradientColor: isGradientColor,
      onNavigationBack: navigation.goBack,
      onHandleClosingBottomSheet: onHandleClosingBottomSheet,
      isBottomSheetContentScrolling: isBottomSheetContentScrolling,
      shouldEnableBottomSheetScroll: shouldEnableBottomSheetScroll,
      onHandleHardwareButtonPress: onHandleHardwareButtonPress
    });
    // Disable reason: deferring this refactor to the native team.
    // see https://github.com/WordPress/gutenberg/pull/41166
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setColor, currentValue, isGradientColor, onShouldEnableInnerHandling, shouldEnableBottomSheetMaxHeight, onHandleClosingBottomSheet, isBottomSheetContentScrolling, shouldEnableBottomSheetScroll, onHandleHardwareButtonPress]);
};
var _default = PickerScreen;
exports.default = _default;
//# sourceMappingURL=picker-screen.native.js.map