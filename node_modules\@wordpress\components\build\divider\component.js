"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Divider = void 0;
var _react = require("react");
var Ariakit = _interopRequireWildcard(require("@ariakit/react"));
var _context = require("../context");
var _styles = require("./styles");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */
// eslint-disable-next-line no-restricted-imports

function UnconnectedDivider(props, forwardedRef) {
  const contextProps = (0, _context.useContextSystem)(props, 'Divider');
  return (0, _react.createElement)(Ariakit.Separator, {
    render: (0, _react.createElement)(_styles.DividerView, null),
    ...contextProps,
    ref: forwardedRef
  });
}

/**
 * `Divider` is a layout component that separates groups of related content.
 *
 * ```js
 * import {
 * 		__experimentalDivider as Divider,
 * 		__experimentalText as Text,
 * 		__experimentalVStack as VStack,
 * } from `@wordpress/components`;
 *
 * function Example() {
 * 	return (
 * 		<VStack spacing={4}>
 * 			<Text>Some text here</Text>
 * 			<Divider />
 * 			<Text>Some more text here</Text>
 * 		</VStack>
 * 	);
 * }
 * ```
 */
const Divider = (0, _context.contextConnect)(UnconnectedDivider, 'Divider');
exports.Divider = Divider;
var _default = Divider;
exports.default = _default;
//# sourceMappingURL=component.js.map