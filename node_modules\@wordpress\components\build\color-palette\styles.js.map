{"version": 3, "names": ["_heading", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "ColorHeading", "_base", "default", "Heading", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports"], "sources": ["@wordpress/components/src/color-palette/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport { Heading } from '../heading';\n\nexport const ColorHeading = styled( Heading )`\n\ttext-transform: uppercase;\n\tline-height: 24px;\n\tfont-weight: 500;\n\t&&& {\n\t\tfont-size: 11px;\n\t\tmargin-bottom: 0;\n\t}\n`;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AAAqC,SAAAC,iCAAA;AAE9B,MAAMC,YAAY,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,gBAAO,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAQ5C;AAACc,OAAA,CAAAb,YAAA,GAAAA,YAAA"}