{"version": 3, "names": ["_reactNative", "require", "_native", "_element", "_icons", "_i18n", "_components", "_getPxFromCssUnit", "_interopRequireDefault", "_unitControl", "_interopRequireWildcard", "_style", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "DEFAULT_FONT_SIZE", "FontSizePicker", "fontSizes", "disableCustomFontSizes", "onChange", "value", "selected<PERSON><PERSON><PERSON>", "_fontSizes$find", "showSubSheet", "setShowSubSheet", "useState", "navigation", "useNavigation", "height", "width", "useWindowDimensions", "cssUnitOptions", "fontSize", "selectedPxValue", "getPxFromCssUnit", "onChangeValue", "goBack", "selectedOption", "find", "option", "sizePx", "name", "openSubSheet", "navigate", "BottomSheet", "SubSheet", "screenName", "label", "__", "units", "useCustomUnits", "availableUnits", "accessibilityLabel", "sprintf", "_react", "createElement", "navigationButton", "Cell", "separatorType", "onPress", "accessibilityRole", "accessibilityHint", "Icon", "icon", "chevronRight", "showSheet", "Fragment", "NavBar", "BackButton", "Heading", "View", "style", "styles", "customActionButton", "undefined", "leftAlign", "check", "map", "item", "index", "parseFloat", "subLabel", "min", "max", "step", "nextSize", "_default", "exports"], "sources": ["@wordpress/components/src/font-size-picker/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, useWindowDimensions } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { useNavigation } from '@react-navigation/native';\nimport { useState } from '@wordpress/element';\nimport { Icon, chevronRight, check } from '@wordpress/icons';\nimport { __, sprintf } from '@wordpress/i18n';\nimport { BottomSheet } from '@wordpress/components';\n\n/**\n * Internal dependencies\n */\nimport { default as getPxFromCssUnit } from '../mobile/utils/get-px-from-css-unit';\nimport { default as UnitControl, useCustomUnits } from '../unit-control';\nimport styles from './style.scss';\n\nconst DEFAULT_FONT_SIZE = 16;\n\nfunction FontSizePicker( {\n\tfontSizes = [],\n\tdisableCustomFontSizes = false,\n\tonChange,\n\tvalue: selectedValue,\n} ) {\n\tconst [ showSubSheet, setShowSubSheet ] = useState( false );\n\tconst navigation = useNavigation();\n\n\tconst { height, width } = useWindowDimensions();\n\tconst cssUnitOptions = { height, width, fontSize: DEFAULT_FONT_SIZE };\n\t// We need to always convert to px units because the selected value\n\t// could be coming from the web where it could be stored as a different unit.\n\tconst selectedPxValue = getPxFromCssUnit( selectedValue, cssUnitOptions );\n\n\tconst onChangeValue = ( value ) => {\n\t\treturn () => {\n\t\t\tgoBack();\n\t\t\tonChange( value );\n\t\t};\n\t};\n\n\tconst selectedOption = fontSizes.find(\n\t\t( option ) => option.sizePx === selectedPxValue\n\t) ?? { name: 'Custom' };\n\n\tconst goBack = () => {\n\t\tsetShowSubSheet( false );\n\t\tnavigation.goBack();\n\t};\n\n\tconst openSubSheet = () => {\n\t\tnavigation.navigate( BottomSheet.SubSheet.screenName );\n\t\tsetShowSubSheet( true );\n\t};\n\tconst label = __( 'Font Size' );\n\n\tconst units = useCustomUnits( {\n\t\tavailableUnits: [ 'px', 'em', 'rem' ],\n\t} );\n\n\tconst accessibilityLabel = sprintf(\n\t\t// translators: %1$s: Font size name e.g. Small\n\t\t__( 'Font Size, %1$s' ),\n\t\tselectedOption.name\n\t);\n\n\treturn (\n\t\t<BottomSheet.SubSheet\n\t\t\tnavigationButton={\n\t\t\t\t<BottomSheet.Cell\n\t\t\t\t\tlabel={ label }\n\t\t\t\t\tseparatorType=\"none\"\n\t\t\t\t\tvalue={\n\t\t\t\t\t\tselectedValue\n\t\t\t\t\t\t\t? sprintf(\n\t\t\t\t\t\t\t\t\t// translators: %1$s: Select control font size name e.g. Small, %2$s: Select control font size e.g. 12px\n\t\t\t\t\t\t\t\t\t__( '%1$s (%2$s)' ),\n\t\t\t\t\t\t\t\t\tselectedOption.name,\n\t\t\t\t\t\t\t\t\tselectedPxValue\n\t\t\t\t\t\t\t  )\n\t\t\t\t\t\t\t: __( 'Default' )\n\t\t\t\t\t}\n\t\t\t\t\tonPress={ openSubSheet }\n\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\taccessibilityLabel={ accessibilityLabel }\n\t\t\t\t\taccessibilityHint={ sprintf(\n\t\t\t\t\t\t// translators: %s: Select control button label e.g. Small\n\t\t\t\t\t\t__( 'Navigates to select %s' ),\n\t\t\t\t\t\tselectedOption.name\n\t\t\t\t\t) }\n\t\t\t\t>\n\t\t\t\t\t<Icon icon={ chevronRight }></Icon>\n\t\t\t\t</BottomSheet.Cell>\n\t\t\t}\n\t\t\tshowSheet={ showSubSheet }\n\t\t>\n\t\t\t<>\n\t\t\t\t<BottomSheet.NavBar>\n\t\t\t\t\t<BottomSheet.NavBar.BackButton onPress={ goBack } />\n\t\t\t\t\t<BottomSheet.NavBar.Heading>\n\t\t\t\t\t\t{ label }\n\t\t\t\t\t</BottomSheet.NavBar.Heading>\n\t\t\t\t</BottomSheet.NavBar>\n\t\t\t\t<View style={ styles[ 'components-font-size-picker' ] }>\n\t\t\t\t\t<BottomSheet.Cell\n\t\t\t\t\t\tcustomActionButton\n\t\t\t\t\t\tseparatorType=\"none\"\n\t\t\t\t\t\tlabel={ __( 'Default' ) }\n\t\t\t\t\t\tonPress={ onChangeValue( undefined ) }\n\t\t\t\t\t\tleftAlign={ true }\n\t\t\t\t\t\tkey={ 'default' }\n\t\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\t\taccessibilityLabel={ __( 'Selected: Default' ) }\n\t\t\t\t\t\taccessibilityHint={ __(\n\t\t\t\t\t\t\t'Double tap to select default font size'\n\t\t\t\t\t\t) }\n\t\t\t\t\t>\n\t\t\t\t\t\t<View>\n\t\t\t\t\t\t\t{ selectedValue === undefined && (\n\t\t\t\t\t\t\t\t<Icon icon={ check }></Icon>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</View>\n\t\t\t\t\t</BottomSheet.Cell>\n\t\t\t\t\t{ fontSizes.map( ( item, index ) => {\n\t\t\t\t\t\t// Only display a choice that we can currenly select.\n\t\t\t\t\t\tif ( ! parseFloat( item.sizePx ) ) {\n\t\t\t\t\t\t\treturn null;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t<BottomSheet.Cell\n\t\t\t\t\t\t\t\tcustomActionButton\n\t\t\t\t\t\t\t\tseparatorType=\"none\"\n\t\t\t\t\t\t\t\tlabel={ item.name }\n\t\t\t\t\t\t\t\tsubLabel={ item.sizePx }\n\t\t\t\t\t\t\t\tonPress={ onChangeValue( item.sizePx ) }\n\t\t\t\t\t\t\t\tleftAlign={ true }\n\t\t\t\t\t\t\t\tkey={ index }\n\t\t\t\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\t\t\t\taccessibilityLabel={\n\t\t\t\t\t\t\t\t\titem.sizePx === selectedValue\n\t\t\t\t\t\t\t\t\t\t? sprintf(\n\t\t\t\t\t\t\t\t\t\t\t\t// translators: %s: Select font size option value e.g: \"Selected: Large\".\n\t\t\t\t\t\t\t\t\t\t\t\t__( 'Selected: %s' ),\n\t\t\t\t\t\t\t\t\t\t\t\titem.name\n\t\t\t\t\t\t\t\t\t\t  )\n\t\t\t\t\t\t\t\t\t\t: item.name\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\taccessibilityHint={ __(\n\t\t\t\t\t\t\t\t\t'Double tap to select font size'\n\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<View>\n\t\t\t\t\t\t\t\t\t{ item.sizePx === selectedPxValue && (\n\t\t\t\t\t\t\t\t\t\t<Icon icon={ check }></Icon>\n\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t</View>\n\t\t\t\t\t\t\t</BottomSheet.Cell>\n\t\t\t\t\t\t);\n\t\t\t\t\t} ) }\n\t\t\t\t\t{ ! disableCustomFontSizes && (\n\t\t\t\t\t\t<UnitControl\n\t\t\t\t\t\t\tlabel={ __( 'Custom' ) }\n\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\tmax={ 200 }\n\t\t\t\t\t\t\tstep={ 1 }\n\t\t\t\t\t\t\tvalue={ selectedValue }\n\t\t\t\t\t\t\tonChange={ ( nextSize ) => {\n\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t0 === parseFloat( nextSize ) ||\n\t\t\t\t\t\t\t\t\t! nextSize\n\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\tonChange( undefined );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tonChange( nextSize );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\tunits={ units }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t</View>\n\t\t\t</>\n\t\t</BottomSheet.SubSheet>\n\t);\n}\n\nexport default FontSizePicker;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AAKA,IAAAM,iBAAA,GAAAC,sBAAA,CAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAC,uBAAA,CAAAT,OAAA;AACA,IAAAU,MAAA,GAAAH,sBAAA,CAAAP,OAAA;AAAkC,SAAAW,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAnBlC;AACA;AACA;;AAGA;AACA;AACA;;AAOA;AACA;AACA;;AAKA,MAAMW,iBAAiB,GAAG,EAAE;AAE5B,SAASC,cAAcA,CAAE;EACxBC,SAAS,GAAG,EAAE;EACdC,sBAAsB,GAAG,KAAK;EAC9BC,QAAQ;EACRC,KAAK,EAAEC;AACR,CAAC,EAAG;EAAA,IAAAC,eAAA;EACH,MAAM,CAAEC,YAAY,EAAEC,eAAe,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAC3D,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAElC,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EAC/C,MAAMC,cAAc,GAAG;IAAEH,MAAM;IAAEC,KAAK;IAAEG,QAAQ,EAAEjB;EAAkB,CAAC;EACrE;EACA;EACA,MAAMkB,eAAe,GAAG,IAAAC,yBAAgB,EAAEb,aAAa,EAAEU,cAAe,CAAC;EAEzE,MAAMI,aAAa,GAAKf,KAAK,IAAM;IAClC,OAAO,MAAM;MACZgB,MAAM,CAAC,CAAC;MACRjB,QAAQ,CAAEC,KAAM,CAAC;IAClB,CAAC;EACF,CAAC;EAED,MAAMiB,cAAc,IAAAf,eAAA,GAAGL,SAAS,CAACqB,IAAI,CAClCC,MAAM,IAAMA,MAAM,CAACC,MAAM,KAAKP,eACjC,CAAC,cAAAX,eAAA,cAAAA,eAAA,GAAI;IAAEmB,IAAI,EAAE;EAAS,CAAC;EAEvB,MAAML,MAAM,GAAGA,CAAA,KAAM;IACpBZ,eAAe,CAAE,KAAM,CAAC;IACxBE,UAAU,CAACU,MAAM,CAAC,CAAC;EACpB,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IAC1BhB,UAAU,CAACiB,QAAQ,CAAEC,uBAAW,CAACC,QAAQ,CAACC,UAAW,CAAC;IACtDtB,eAAe,CAAE,IAAK,CAAC;EACxB,CAAC;EACD,MAAMuB,KAAK,GAAG,IAAAC,QAAE,EAAE,WAAY,CAAC;EAE/B,MAAMC,KAAK,GAAG,IAAAC,2BAAc,EAAE;IAC7BC,cAAc,EAAE,CAAE,IAAI,EAAE,IAAI,EAAE,KAAK;EACpC,CAAE,CAAC;EAEH,MAAMC,kBAAkB,GAAG,IAAAC,aAAO;EACjC;EACA,IAAAL,QAAE,EAAE,iBAAkB,CAAC,EACvBX,cAAc,CAACI,IAChB,CAAC;EAED,OACC,IAAAa,MAAA,CAAAC,aAAA,EAACpE,WAAA,CAAAyD,WAAW,CAACC,QAAQ;IACpBW,gBAAgB,EACf,IAAAF,MAAA,CAAAC,aAAA,EAACpE,WAAA,CAAAyD,WAAW,CAACa,IAAI;MAChBV,KAAK,EAAGA,KAAO;MACfW,aAAa,EAAC,MAAM;MACpBtC,KAAK,EACJC,aAAa,GACV,IAAAgC,aAAO;MACP;MACA,IAAAL,QAAE,EAAE,aAAc,CAAC,EACnBX,cAAc,CAACI,IAAI,EACnBR,eACA,CAAC,GACD,IAAAe,QAAE,EAAE,SAAU,CACjB;MACDW,OAAO,EAAGjB,YAAc;MACxBkB,iBAAiB,EAAG,QAAU;MAC9BR,kBAAkB,EAAGA,kBAAoB;MACzCS,iBAAiB,EAAG,IAAAR,aAAO;MAC1B;MACA,IAAAL,QAAE,EAAE,wBAAyB,CAAC,EAC9BX,cAAc,CAACI,IAChB;IAAG,GAEH,IAAAa,MAAA,CAAAC,aAAA,EAACtE,MAAA,CAAA6E,IAAI;MAACC,IAAI,EAAGC;IAAc,CAAO,CACjB,CAClB;IACDC,SAAS,EAAG1C;EAAc,GAE1B,IAAA+B,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAY,QAAA,QACC,IAAAZ,MAAA,CAAAC,aAAA,EAACpE,WAAA,CAAAyD,WAAW,CAACuB,MAAM,QAClB,IAAAb,MAAA,CAAAC,aAAA,EAACpE,WAAA,CAAAyD,WAAW,CAACuB,MAAM,CAACC,UAAU;IAACT,OAAO,EAAGvB;EAAQ,CAAE,CAAC,EACpD,IAAAkB,MAAA,CAAAC,aAAA,EAACpE,WAAA,CAAAyD,WAAW,CAACuB,MAAM,CAACE,OAAO,QACxBtB,KACyB,CACT,CAAC,EACrB,IAAAO,MAAA,CAAAC,aAAA,EAAC1E,YAAA,CAAAyF,IAAI;IAACC,KAAK,EAAGC,cAAM,CAAE,6BAA6B;EAAI,GACtD,IAAAlB,MAAA,CAAAC,aAAA,EAACpE,WAAA,CAAAyD,WAAW,CAACa,IAAI;IAChBgB,kBAAkB;IAClBf,aAAa,EAAC,MAAM;IACpBX,KAAK,EAAG,IAAAC,QAAE,EAAE,SAAU,CAAG;IACzBW,OAAO,EAAGxB,aAAa,CAAEuC,SAAU,CAAG;IACtCC,SAAS,EAAG,IAAM;IAClBlE,GAAG,EAAG,SAAW;IACjBmD,iBAAiB,EAAG,QAAU;IAC9BR,kBAAkB,EAAG,IAAAJ,QAAE,EAAE,mBAAoB,CAAG;IAChDa,iBAAiB,EAAG,IAAAb,QAAE,EACrB,wCACD;EAAG,GAEH,IAAAM,MAAA,CAAAC,aAAA,EAAC1E,YAAA,CAAAyF,IAAI,QACFjD,aAAa,KAAKqD,SAAS,IAC5B,IAAApB,MAAA,CAAAC,aAAA,EAACtE,MAAA,CAAA6E,IAAI;IAACC,IAAI,EAAGa;EAAO,CAAO,CAEvB,CACW,CAAC,EACjB3D,SAAS,CAAC4D,GAAG,CAAE,CAAEC,IAAI,EAAEC,KAAK,KAAM;IACnC;IACA,IAAK,CAAEC,UAAU,CAAEF,IAAI,CAACtC,MAAO,CAAC,EAAG;MAClC,OAAO,IAAI;IACZ;IACA,OACC,IAAAc,MAAA,CAAAC,aAAA,EAACpE,WAAA,CAAAyD,WAAW,CAACa,IAAI;MAChBgB,kBAAkB;MAClBf,aAAa,EAAC,MAAM;MACpBX,KAAK,EAAG+B,IAAI,CAACrC,IAAM;MACnBwC,QAAQ,EAAGH,IAAI,CAACtC,MAAQ;MACxBmB,OAAO,EAAGxB,aAAa,CAAE2C,IAAI,CAACtC,MAAO,CAAG;MACxCmC,SAAS,EAAG,IAAM;MAClBlE,GAAG,EAAGsE,KAAO;MACbnB,iBAAiB,EAAG,QAAU;MAC9BR,kBAAkB,EACjB0B,IAAI,CAACtC,MAAM,KAAKnB,aAAa,GAC1B,IAAAgC,aAAO;MACP;MACA,IAAAL,QAAE,EAAE,cAAe,CAAC,EACpB8B,IAAI,CAACrC,IACL,CAAC,GACDqC,IAAI,CAACrC,IACR;MACDoB,iBAAiB,EAAG,IAAAb,QAAE,EACrB,gCACD;IAAG,GAEH,IAAAM,MAAA,CAAAC,aAAA,EAAC1E,YAAA,CAAAyF,IAAI,QACFQ,IAAI,CAACtC,MAAM,KAAKP,eAAe,IAChC,IAAAqB,MAAA,CAAAC,aAAA,EAACtE,MAAA,CAAA6E,IAAI;MAACC,IAAI,EAAGa;IAAO,CAAO,CAEvB,CACW,CAAC;EAErB,CAAE,CAAC,EACD,CAAE1D,sBAAsB,IACzB,IAAAoC,MAAA,CAAAC,aAAA,EAACjE,YAAA,CAAAU,OAAW;IACX+C,KAAK,EAAG,IAAAC,QAAE,EAAE,QAAS,CAAG;IACxBkC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,IAAI,EAAG,CAAG;IACVhE,KAAK,EAAGC,aAAe;IACvBF,QAAQ,EAAKkE,QAAQ,IAAM;MAC1B,IACC,CAAC,KAAKL,UAAU,CAAEK,QAAS,CAAC,IAC5B,CAAEA,QAAQ,EACT;QACDlE,QAAQ,CAAEuD,SAAU,CAAC;MACtB,CAAC,MAAM;QACNvD,QAAQ,CAAEkE,QAAS,CAAC;MACrB;IACD,CAAG;IACHpC,KAAK,EAAGA;EAAO,CACf,CAEG,CACL,CACmB,CAAC;AAEzB;AAAC,IAAAqC,QAAA,GAEctE,cAAc;AAAAuE,OAAA,CAAAvF,OAAA,GAAAsF,QAAA"}