{"version": 3, "names": [], "sources": ["@wordpress/components/src/autocomplete/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactElement } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport type { RichTextValue } from '@wordpress/rich-text';\n\n/**\n * Internal dependencies\n */\nimport type { useAutocomplete } from '.';\n\n// Insert the `value` into the text.\nexport type InsertOption = {\n\taction: 'insert-at-caret';\n\tvalue: React.ReactNode;\n};\n\n// Replace the current block with the block specified in the `value` property\nexport type ReplaceOption = { action: 'replace'; value: RichTextValue };\n\nexport type OptionCompletion = React.ReactNode | InsertOption | ReplaceOption;\n\ntype OptionLabel = string | ReactElement | Array< string | ReactElement >;\nexport type KeyedOption = {\n\tkey: string;\n\tvalue: any;\n\tlabel: OptionLabel;\n\tkeywords: Array< string >;\n\tisDisabled: boolean;\n};\n\nexport type WPCompleter< TCompleterOption = any > = {\n\t/**\n\t * The name of the completer. Useful for identifying a specific completer to\n\t * be overridden via extensibility hooks.\n\t */\n\tname: string;\n\t/**\n\t * The string prefix that should trigger the completer. For example,\n\t * <PERSON><PERSON><PERSON>'s block completer is triggered when the '/' character is\n\t * entered.\n\t */\n\ttriggerPrefix: string;\n\t/**\n\t * The raw options for completion. May be an array, a function that returns\n\t * an array, or a function that returns a promise for an array.\n\t * Options may be of any type or shape. The completer declares how those\n\t * options are rendered and what their completions should be when selected.\n\t */\n\toptions:\n\t\t| ( (\n\t\t\t\tquery: string\n\t\t  ) =>\n\t\t\t\t| PromiseLike< readonly TCompleterOption[] >\n\t\t\t\t| readonly TCompleterOption[] )\n\t\t| readonly TCompleterOption[];\n\t/**\n\t * A function that returns the keywords for the specified option.\n\t */\n\tgetOptionKeywords?: ( option: TCompleterOption ) => Array< string >;\n\t/**\n\t * A function that returns whether or not the specified option is disabled.\n\t * Disabled options cannot be selected.\n\t */\n\tisOptionDisabled?: ( option: TCompleterOption ) => boolean;\n\t/**\n\t * A function that returns the label for a given option. A label may be a\n\t * string or a mixed array of strings, elements, and components.\n\t */\n\tgetOptionLabel: ( option: TCompleterOption ) => OptionLabel;\n\t/**\n\t * A function that takes a Range before and a Range after the autocomplete\n\t * trigger and query text and returns a boolean indicating whether the\n\t * completer should be considered for that context.\n\t */\n\tallowContext?: ( before: string, after: string ) => boolean;\n\t/**\n\t * A function that takes an option and returns how the option should\n\t * be completed. By default, the result is a value to be inserted in the\n\t * text.\n\t * However, a completer may explicitly declare how a completion should be\n\t * treated by returning an object with `action` and `value` properties. The\n\t * `action` declares what should be done with the `value`.\n\t */\n\tgetOptionCompletion?: (\n\t\toption: TCompleterOption,\n\t\tquery: string\n\t) => OptionCompletion;\n\t/**\n\t * A function that returns an array of items to be displayed in the\n\t * Autocomplete UI. These items have uniform shape and have been filtered by\n\t * `AutocompleterUIProps.filterValue`.\n\t */\n\tuseItems?: ( filterValue: string ) => readonly [ Array< KeyedOption > ];\n\t/**\n\t * Whether or not changes to the `filterValue` should be debounced.\n\t */\n\tisDebounced?: boolean;\n\t/**\n\t * A CSS class name to be applied to the completion menu.\n\t */\n\tclassName?: string;\n};\n\ntype ContentRef = React.RefObject< HTMLElement >;\n\nexport type AutocompleterUIProps = {\n\t/**\n\t * The value to filter the options by.\n\t */\n\tfilterValue: string;\n\t/**\n\t * An id unique to each instance of the component, used in the IDs of the\n\t * buttons generated for individual options.\n\t */\n\tinstanceId: number;\n\t/**\n\t * The id of to be applied to the listbox of options.\n\t */\n\tlistBoxId: string | undefined;\n\t/**\n\t * The class to apply to the wrapper element.\n\t */\n\tclassName?: string;\n\t/**\n\t * The index of the currently selected option.\n\t */\n\tselectedIndex: number;\n\t/**\n\t * A function to be called when the filterValue changes.\n\t */\n\tonChangeOptions: ( items: Array< KeyedOption > ) => void;\n\t/**\n\t * A function to be called when an option is selected.\n\t */\n\tonSelect: ( option: KeyedOption ) => void;\n\t/**\n\t * A function to be called when the completer is reset\n\t * (e.g. when the user hits the escape key).\n\t */\n\tonReset?: () => void;\n\t/**\n\t * A function that defines the behavior of the completer when it is reset\n\t */\n\treset: ( event: Event ) => void;\n\t// This is optional because it's still needed for mobile/native.\n\t/**\n\t * The rich text value object the autocompleter is being applied to.\n\t */\n\tvalue?: RichTextValue;\n\t/**\n\t * A ref containing the editable element that will serve as the anchor for\n\t * `Autocomplete`'s `Popover`.\n\t */\n\tcontentRef: ContentRef;\n};\n\nexport type CancelablePromise< T = void > = Promise< T > & {\n\tcanceled?: boolean;\n};\n\nexport type UseAutocompleteProps = {\n\t/**\n\t * The rich text value object the autocompleter is being applied to.\n\t */\n\trecord: RichTextValue & {\n\t\tstart: NonNullable< RichTextValue[ 'start' ] >;\n\t\tend: NonNullable< RichTextValue[ 'end' ] >;\n\t};\n\t/**\n\t * A function to be called when an option is selected to insert into the\n\t * existing text.\n\t */\n\tonChange: ( value: RichTextValue ) => void;\n\t/**\n\t * A function to be called when an option is selected to replace the\n\t * existing text.\n\t */\n\tonReplace: ( values: RichTextValue[] ) => void;\n\t/**\n\t * An array of all of the completers to apply to the current element.\n\t */\n\tcompleters: Array< WPCompleter >;\n\t/**\n\t * A ref containing the editable element that will serve as the anchor for\n\t * `Autocomplete`'s `Popover`.\n\t */\n\tcontentRef: ContentRef;\n};\n\nexport type AutocompleteProps = UseAutocompleteProps & {\n\t/**\n\t * A function that returns nodes to be rendered within the Autocomplete.\n\t */\n\tchildren: (\n\t\tprops: Omit< ReturnType< typeof useAutocomplete >, 'popover' >\n\t) => React.ReactNode;\n\t/**\n\t * Whether or not the Autocomplte componenet is selected, and if its\n\t * `Popover`\n\t * should be displayed.\n\t */\n\tisSelected: boolean;\n};\n"], "mappings": ""}