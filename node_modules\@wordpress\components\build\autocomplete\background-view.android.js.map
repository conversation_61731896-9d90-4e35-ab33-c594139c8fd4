{"version": 3, "names": ["_reactNative", "require", "_compose", "_style", "_interopRequireDefault", "<PERSON><PERSON><PERSON><PERSON>", "children", "backgroundStyles", "usePreferredColorSchemeStyle", "styles", "_react", "createElement", "View", "style", "_default", "exports", "default"], "sources": ["@wordpress/components/src/autocomplete/background-view.android.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nconst BackgroundView = ( { children } ) => {\n\tconst backgroundStyles = usePreferredColorSchemeStyle(\n\t\tstyles[ 'components-autocomplete__background' ],\n\t\tstyles[ 'components-autocomplete__background-dark' ]\n\t);\n\n\treturn <View style={ backgroundStyles }>{ children }</View>;\n};\n\nexport default BackgroundView;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAbA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAGA,MAAMI,cAAc,GAAGA,CAAE;EAAEC;AAAS,CAAC,KAAM;EAC1C,MAAMC,gBAAgB,GAAG,IAAAC,qCAA4B,EACpDC,cAAM,CAAE,qCAAqC,CAAE,EAC/CA,cAAM,CAAE,0CAA0C,CACnD,CAAC;EAED,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAY,IAAI;IAACC,KAAK,EAAGN;EAAkB,GAAGD,QAAgB,CAAC;AAC5D,CAAC;AAAC,IAAAQ,QAAA,GAEaT,cAAc;AAAAU,OAAA,CAAAC,OAAA,GAAAF,QAAA"}