"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OptionGroup = OptionGroup;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
/**
 * External dependencies
 */

function OptionGroup({
  className,
  options,
  ...additionalProps
}) {
  const role = 'aria-label' in additionalProps || 'aria-labelledby' in additionalProps ? 'group' : undefined;
  return (0, _react.createElement)("div", {
    ...additionalProps,
    role: role,
    className: (0, _classnames.default)('components-circular-option-picker__option-group', 'components-circular-option-picker__swatches', className)
  }, options);
}
//# sourceMappingURL=circular-option-picker-option-group.js.map