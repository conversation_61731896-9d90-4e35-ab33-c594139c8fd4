import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  SelectControl,
  TextareaControl,
  Notice,
  Spinner
} from '@wordpress/components';

const RedirectionImportExport = ({ 
  onImport, 
  onExport, 
  isImporting, 
  isExporting 
}) => {
  // États
  const [importFormat, setImportFormat] = useState('csv');
  const [exportFormat, setExportFormat] = useState('csv');
  const [importData, setImportData] = useState('');
  const [importErrors, setImportErrors] = useState([]);
  const [importSuccess, setImportSuccess] = useState(false);
  const [exportData, setExportData] = useState('');
  
  // Fonction pour gérer l'importation
  const handleImport = () => {
    // Réinitialiser les erreurs et le succès
    setImportErrors([]);
    setImportSuccess(false);
    
    // Vérifier si des données sont présentes
    if (!importData.trim()) {
      setImportErrors([__('Veuillez entrer des données à importer', 'boss-seo')]);
      return;
    }
    
    // Simuler la validation des données
    const lines = importData.trim().split('\n');
    const errors = [];
    
    if (importFormat === 'csv') {
      // Valider le format CSV
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const parts = line.split(',');
        
        if (parts.length < 2) {
          errors.push(__(`Ligne ${i + 1}: Format invalide, attendu "source,destination,type"`, 'boss-seo'));
        }
      }
    } else if (importFormat === 'htaccess') {
      // Valider le format .htaccess
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (line.trim() && !line.includes('Redirect') && !line.includes('RewriteRule')) {
          errors.push(__(`Ligne ${i + 1}: Format .htaccess invalide`, 'boss-seo'));
        }
      }
    }
    
    if (errors.length > 0) {
      setImportErrors(errors);
      return;
    }
    
    // Si tout est valide, procéder à l'importation
    onImport(importData, importFormat);
    
    // Afficher le message de succès après l'importation
    setTimeout(() => {
      setImportSuccess(true);
      setImportData('');
      
      // Masquer le message après 3 secondes
      setTimeout(() => {
        setImportSuccess(false);
      }, 3000);
    }, 1000);
  };
  
  // Fonction pour gérer l'exportation
  const handleExport = () => {
    // Réinitialiser les données d'exportation
    setExportData('');
    
    // Appeler la fonction d'exportation
    onExport(exportFormat).then(data => {
      setExportData(data);
    });
  };
  
  // Fonction pour télécharger les données exportées
  const handleDownload = () => {
    if (!exportData) return;
    
    const blob = new Blob([exportData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `redirections.${exportFormat}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
      {/* Importation */}
      <Card>
        <CardHeader className="boss-border-b boss-border-gray-200">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Importer des redirections', 'boss-seo')}
          </h2>
        </CardHeader>
        <CardBody>
          <div className="boss-space-y-4">
            <SelectControl
              label={__('Format d\'importation', 'boss-seo')}
              value={importFormat}
              options={[
                { label: 'CSV', value: 'csv' },
                { label: '.htaccess', value: 'htaccess' },
                { label: 'JSON', value: 'json' }
              ]}
              onChange={setImportFormat}
            />
            
            <TextareaControl
              label={__('Données à importer', 'boss-seo')}
              help={
                importFormat === 'csv' 
                  ? __('Format: source,destination,type (301, 302, etc.)', 'boss-seo')
                  : importFormat === 'htaccess'
                    ? __('Format: Redirect 301 /source /destination', 'boss-seo')
                    : __('Format JSON', 'boss-seo')
              }
              value={importData}
              onChange={setImportData}
              rows={8}
            />
            
            {importErrors.length > 0 && (
              <Notice status="error" isDismissible={false}>
                <ul className="boss-list-disc boss-pl-4">
                  {importErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </Notice>
            )}
            
            {importSuccess && (
              <Notice status="success" isDismissible={false}>
                {__('Importation réussie !', 'boss-seo')}
              </Notice>
            )}
          </div>
        </CardBody>
        <CardFooter className="boss-border-t boss-border-gray-200">
          <Button
            isPrimary
            onClick={handleImport}
            isBusy={isImporting}
            disabled={isImporting || !importData.trim()}
          >
            {isImporting ? __('Importation en cours...', 'boss-seo') : __('Importer', 'boss-seo')}
          </Button>
        </CardFooter>
      </Card>
      
      {/* Exportation */}
      <Card>
        <CardHeader className="boss-border-b boss-border-gray-200">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Exporter des redirections', 'boss-seo')}
          </h2>
        </CardHeader>
        <CardBody>
          <div className="boss-space-y-4">
            <SelectControl
              label={__('Format d\'exportation', 'boss-seo')}
              value={exportFormat}
              options={[
                { label: 'CSV', value: 'csv' },
                { label: '.htaccess', value: 'htaccess' },
                { label: 'JSON', value: 'json' }
              ]}
              onChange={setExportFormat}
            />
            
            {isExporting ? (
              <div className="boss-flex boss-justify-center boss-items-center boss-p-6">
                <Spinner />
              </div>
            ) : exportData ? (
              <div>
                <TextareaControl
                  label={__('Données exportées', 'boss-seo')}
                  value={exportData}
                  readOnly
                  rows={8}
                />
                <div className="boss-mt-4">
                  <Button
                    isPrimary
                    onClick={handleDownload}
                  >
                    {__('Télécharger', 'boss-seo')}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="boss-text-center boss-py-6 boss-text-boss-gray">
                {__('Cliquez sur "Exporter" pour générer les données.', 'boss-seo')}
              </div>
            )}
          </div>
        </CardBody>
        <CardFooter className="boss-border-t boss-border-gray-200">
          <Button
            isPrimary
            onClick={handleExport}
            isBusy={isExporting}
            disabled={isExporting}
          >
            {isExporting ? __('Exportation en cours...', 'boss-seo') : __('Exporter', 'boss-seo')}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default RedirectionImportExport;
