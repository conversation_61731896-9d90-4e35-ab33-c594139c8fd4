{"version": 3, "names": ["_removeAccents", "_interopRequireDefault", "require", "_compose", "_element", "_strings", "filterOptions", "search", "options", "maxResults", "filtered", "i", "length", "option", "keywords", "label", "isMatch", "some", "keyword", "test", "removeAccents", "push", "getDefaultUseItems", "autocompleter", "filterValue", "items", "setItems", "useState", "useLayoutEffect", "isDebounced", "loadOptions", "debounce", "promise", "Promise", "resolve", "then", "optionsData", "canceled", "keyedOptions", "map", "optionData", "optionIndex", "key", "name", "value", "getOptionLabel", "getOptionKeywords", "isDisabled", "isOptionDisabled", "RegExp", "escapeRegExp", "cancel"], "sources": ["@wordpress/components/src/autocomplete/get-default-use-items.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport removeAccents from 'remove-accents';\n\n/**\n * WordPress dependencies\n */\nimport { debounce } from '@wordpress/compose';\nimport { useLayoutEffect, useState } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport { escapeRegExp } from '../utils/strings';\nimport type { CancelablePromise, KeyedOption, WPCompleter } from './types';\n\nfunction filterOptions(\n\tsearch: RegExp,\n\toptions: Array< KeyedOption > = [],\n\tmaxResults = 10\n) {\n\tconst filtered = [];\n\tfor ( let i = 0; i < options.length; i++ ) {\n\t\tconst option = options[ i ];\n\n\t\t// Merge label into keywords.\n\t\tlet { keywords = [] } = option;\n\t\tif ( 'string' === typeof option.label ) {\n\t\t\tkeywords = [ ...keywords, option.label ];\n\t\t}\n\n\t\tconst isMatch = keywords.some( ( keyword ) =>\n\t\t\tsearch.test( removeAccents( keyword ) )\n\t\t);\n\t\tif ( ! isMatch ) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tfiltered.push( option );\n\n\t\t// Abort early if max reached.\n\t\tif ( filtered.length === maxResults ) {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\treturn filtered;\n}\n\nexport default function getDefaultUseItems( autocompleter: WPCompleter ) {\n\treturn ( filterValue: string ) => {\n\t\tconst [ items, setItems ] = useState< Array< KeyedOption > >( [] );\n\t\t/*\n\t\t * We support both synchronous and asynchronous retrieval of completer options\n\t\t * but internally treat all as async so we maintain a single, consistent code path.\n\t\t *\n\t\t * Because networks can be slow, and the internet is wonderfully unpredictable,\n\t\t * we don't want two promises updating the state at once. This ensures that only\n\t\t * the most recent promise will act on `optionsData`. This doesn't use the state\n\t\t * because `setState` is batched, and so there's no guarantee that setting\n\t\t * `activePromise` in the state would result in it actually being in `this.state`\n\t\t * before the promise resolves and we check to see if this is the active promise or not.\n\t\t */\n\t\tuseLayoutEffect( () => {\n\t\t\tconst { options, isDebounced } = autocompleter;\n\t\t\tconst loadOptions = debounce(\n\t\t\t\t() => {\n\t\t\t\t\tconst promise: CancelablePromise = Promise.resolve(\n\t\t\t\t\t\ttypeof options === 'function'\n\t\t\t\t\t\t\t? options( filterValue )\n\t\t\t\t\t\t\t: options\n\t\t\t\t\t).then( ( optionsData ) => {\n\t\t\t\t\t\tif ( promise.canceled ) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst keyedOptions = optionsData.map(\n\t\t\t\t\t\t\t( optionData, optionIndex ) => ( {\n\t\t\t\t\t\t\t\tkey: `${ autocompleter.name }-${ optionIndex }`,\n\t\t\t\t\t\t\t\tvalue: optionData,\n\t\t\t\t\t\t\t\tlabel: autocompleter.getOptionLabel(\n\t\t\t\t\t\t\t\t\toptionData\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\tkeywords: autocompleter.getOptionKeywords\n\t\t\t\t\t\t\t\t\t? autocompleter.getOptionKeywords(\n\t\t\t\t\t\t\t\t\t\t\toptionData\n\t\t\t\t\t\t\t\t\t  )\n\t\t\t\t\t\t\t\t\t: [],\n\t\t\t\t\t\t\t\tisDisabled: autocompleter.isOptionDisabled\n\t\t\t\t\t\t\t\t\t? autocompleter.isOptionDisabled(\n\t\t\t\t\t\t\t\t\t\t\toptionData\n\t\t\t\t\t\t\t\t\t  )\n\t\t\t\t\t\t\t\t\t: false,\n\t\t\t\t\t\t\t} )\n\t\t\t\t\t\t);\n\n\t\t\t\t\t\t// Create a regular expression to filter the options.\n\t\t\t\t\t\tconst search = new RegExp(\n\t\t\t\t\t\t\t'(?:\\\\b|\\\\s|^)' + escapeRegExp( filterValue ),\n\t\t\t\t\t\t\t'i'\n\t\t\t\t\t\t);\n\t\t\t\t\t\tsetItems( filterOptions( search, keyedOptions ) );\n\t\t\t\t\t} );\n\n\t\t\t\t\treturn promise;\n\t\t\t\t},\n\t\t\t\tisDebounced ? 250 : 0\n\t\t\t);\n\n\t\t\tconst promise = loadOptions();\n\n\t\t\treturn () => {\n\t\t\t\tloadOptions.cancel();\n\t\t\t\tif ( promise ) {\n\t\t\t\t\tpromise.canceled = true;\n\t\t\t\t}\n\t\t\t};\n\t\t}, [ filterValue ] );\n\n\t\treturn [ items ] as const;\n\t};\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,QAAA,GAAAH,OAAA;AAdA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAIA,SAASI,aAAaA,CACrBC,MAAc,EACdC,OAA6B,GAAG,EAAE,EAClCC,UAAU,GAAG,EAAE,EACd;EACD,MAAMC,QAAQ,GAAG,EAAE;EACnB,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAG;IAC1C,MAAME,MAAM,GAAGL,OAAO,CAAEG,CAAC,CAAE;;IAE3B;IACA,IAAI;MAAEG,QAAQ,GAAG;IAAG,CAAC,GAAGD,MAAM;IAC9B,IAAK,QAAQ,KAAK,OAAOA,MAAM,CAACE,KAAK,EAAG;MACvCD,QAAQ,GAAG,CAAE,GAAGA,QAAQ,EAAED,MAAM,CAACE,KAAK,CAAE;IACzC;IAEA,MAAMC,OAAO,GAAGF,QAAQ,CAACG,IAAI,CAAIC,OAAO,IACvCX,MAAM,CAACY,IAAI,CAAE,IAAAC,sBAAa,EAAEF,OAAQ,CAAE,CACvC,CAAC;IACD,IAAK,CAAEF,OAAO,EAAG;MAChB;IACD;IAEAN,QAAQ,CAACW,IAAI,CAAER,MAAO,CAAC;;IAEvB;IACA,IAAKH,QAAQ,CAACE,MAAM,KAAKH,UAAU,EAAG;MACrC;IACD;EACD;EAEA,OAAOC,QAAQ;AAChB;AAEe,SAASY,kBAAkBA,CAAEC,aAA0B,EAAG;EACxE,OAASC,WAAmB,IAAM;IACjC,MAAM,CAAEC,KAAK,EAAEC,QAAQ,CAAE,GAAG,IAAAC,iBAAQ,EAA0B,EAAG,CAAC;IAClE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,IAAAC,wBAAe,EAAE,MAAM;MACtB,MAAM;QAAEpB,OAAO;QAAEqB;MAAY,CAAC,GAAGN,aAAa;MAC9C,MAAMO,WAAW,GAAG,IAAAC,iBAAQ,EAC3B,MAAM;QACL,MAAMC,OAA0B,GAAGC,OAAO,CAACC,OAAO,CACjD,OAAO1B,OAAO,KAAK,UAAU,GAC1BA,OAAO,CAAEgB,WAAY,CAAC,GACtBhB,OACJ,CAAC,CAAC2B,IAAI,CAAIC,WAAW,IAAM;UAC1B,IAAKJ,OAAO,CAACK,QAAQ,EAAG;YACvB;UACD;UACA,MAAMC,YAAY,GAAGF,WAAW,CAACG,GAAG,CACnC,CAAEC,UAAU,EAAEC,WAAW,MAAQ;YAChCC,GAAG,EAAG,GAAGnB,aAAa,CAACoB,IAAM,IAAIF,WAAa,EAAC;YAC/CG,KAAK,EAAEJ,UAAU;YACjBzB,KAAK,EAAEQ,aAAa,CAACsB,cAAc,CAClCL,UACD,CAAC;YACD1B,QAAQ,EAAES,aAAa,CAACuB,iBAAiB,GACtCvB,aAAa,CAACuB,iBAAiB,CAC/BN,UACA,CAAC,GACD,EAAE;YACLO,UAAU,EAAExB,aAAa,CAACyB,gBAAgB,GACvCzB,aAAa,CAACyB,gBAAgB,CAC9BR,UACA,CAAC,GACD;UACJ,CAAC,CACF,CAAC;;UAED;UACA,MAAMjC,MAAM,GAAG,IAAI0C,MAAM,CACxB,eAAe,GAAG,IAAAC,qBAAY,EAAE1B,WAAY,CAAC,EAC7C,GACD,CAAC;UACDE,QAAQ,CAAEpB,aAAa,CAAEC,MAAM,EAAE+B,YAAa,CAAE,CAAC;QAClD,CAAE,CAAC;QAEH,OAAON,OAAO;MACf,CAAC,EACDH,WAAW,GAAG,GAAG,GAAG,CACrB,CAAC;MAED,MAAMG,OAAO,GAAGF,WAAW,CAAC,CAAC;MAE7B,OAAO,MAAM;QACZA,WAAW,CAACqB,MAAM,CAAC,CAAC;QACpB,IAAKnB,OAAO,EAAG;UACdA,OAAO,CAACK,QAAQ,GAAG,IAAI;QACxB;MACD,CAAC;IACF,CAAC,EAAE,CAAEb,WAAW,CAAG,CAAC;IAEpB,OAAO,CAAEC,KAAK,CAAE;EACjB,CAAC;AACF"}