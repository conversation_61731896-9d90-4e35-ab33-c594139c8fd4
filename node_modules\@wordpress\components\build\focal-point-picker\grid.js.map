{"version": 3, "names": ["_focalPointPickerStyle", "require", "FocalPointPickerGrid", "bounds", "props", "_react", "createElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "style", "width", "height", "GridLineX", "top", "GridLineY", "left"], "sources": ["@wordpress/components/src/focal-point-picker/grid.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport {\n\tGridView,\n\tGridLineX,\n\tGridLineY,\n} from './styles/focal-point-picker-style';\nimport type { FocalPointPickerGridProps } from './types';\nimport type { WordPressComponentProps } from '../context/wordpress-component';\n\nexport default function FocalPointPickerGrid( {\n\tbounds,\n\t...props\n}: WordPressComponentProps< FocalPointPickerGridProps, 'div' > ) {\n\treturn (\n\t\t<GridView\n\t\t\t{ ...props }\n\t\t\tclassName=\"components-focal-point-picker__grid\"\n\t\t\tstyle={ {\n\t\t\t\twidth: bounds.width,\n\t\t\t\theight: bounds.height,\n\t\t\t} }\n\t\t>\n\t\t\t<GridLineX style={ { top: '33%' } } />\n\t\t\t<GridLineX style={ { top: '66%' } } />\n\t\t\t<GridLineY style={ { left: '33%' } } />\n\t\t\t<GridLineY style={ { left: '66%' } } />\n\t\t</GridView>\n\t);\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,sBAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AASe,SAASC,oBAAoBA,CAAE;EAC7CC,MAAM;EACN,GAAGC;AACyD,CAAC,EAAG;EAChE,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACN,sBAAA,CAAAO,QAAQ;IAAA,GACHH,KAAK;IACVI,SAAS,EAAC,qCAAqC;IAC/CC,KAAK,EAAG;MACPC,KAAK,EAAEP,MAAM,CAACO,KAAK;MACnBC,MAAM,EAAER,MAAM,CAACQ;IAChB;EAAG,GAEH,IAAAN,MAAA,CAAAC,aAAA,EAACN,sBAAA,CAAAY,SAAS;IAACH,KAAK,EAAG;MAAEI,GAAG,EAAE;IAAM;EAAG,CAAE,CAAC,EACtC,IAAAR,MAAA,CAAAC,aAAA,EAACN,sBAAA,CAAAY,SAAS;IAACH,KAAK,EAAG;MAAEI,GAAG,EAAE;IAAM;EAAG,CAAE,CAAC,EACtC,IAAAR,MAAA,CAAAC,aAAA,EAACN,sBAAA,CAAAc,SAAS;IAACL,KAAK,EAAG;MAAEM,IAAI,EAAE;IAAM;EAAG,CAAE,CAAC,EACvC,IAAAV,MAAA,CAAAC,aAAA,EAACN,sBAAA,CAAAc,SAAS;IAACL,KAAK,EAAG;MAAEM,IAAI,EAAE;IAAM;EAAG,CAAE,CAC7B,CAAC;AAEb"}