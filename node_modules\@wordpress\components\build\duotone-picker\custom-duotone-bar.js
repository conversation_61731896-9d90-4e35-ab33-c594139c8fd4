"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = CustomDuotoneBar;
var _react = require("react");
var _gradientBar = _interopRequireDefault(require("../custom-gradient-picker/gradient-bar"));
var _utils = require("./utils");
/**
 * Internal dependencies
 */

const PLACEHOLDER_VALUES = ['#333', '#CCC'];
function CustomDuotoneBar({
  value,
  onChange
}) {
  const hasGradient = !!value;
  const values = hasGradient ? value : PLACEHOLDER_VALUES;
  const background = (0, _utils.getGradientFromCSSColors)(values);
  const controlPoints = (0, _utils.getColorStopsFromColors)(values);
  return (0, _react.createElement)(_gradientBar.default, {
    disableInserter: true,
    background: background,
    hasGradient: hasGradient,
    value: controlPoints,
    onChange: newColorStops => {
      const newValue = (0, _utils.getColorsFromColorStops)(newColorStops);
      onChange(newValue);
    }
  });
}
//# sourceMappingURL=custom-duotone-bar.js.map