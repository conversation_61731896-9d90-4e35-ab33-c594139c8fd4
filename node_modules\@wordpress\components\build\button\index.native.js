"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Button = Button;
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _reactNativeGestureHandler = require("react-native-gesture-handler");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _tooltip = _interopRequireDefault(require("../tooltip"));
var _icon = _interopRequireDefault(require("../icon"));
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const isAndroid = _reactNative.Platform.OS === 'android';
const marginBottom = isAndroid ? -0.5 : 0;
const marginLeft = -3;
const styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1,
    padding: 3,
    justifyContent: 'center',
    alignItems: 'center'
  },
  buttonInactive: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  fixedRatio: {
    aspectRatio: 1
  },
  buttonActive: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6
  },
  subscriptInactive: {
    color: '#7b9ab1',
    // $toolbar-button.
    fontWeight: 'bold',
    fontSize: 13,
    alignSelf: 'flex-end',
    marginLeft,
    marginBottom
  },
  subscriptInactiveDark: {
    color: '#a7aaad' // $gray_20.
  },

  subscriptActive: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 13,
    alignSelf: 'flex-end',
    marginLeft,
    marginBottom
  }
});
function Button(props) {
  const {
    children,
    onClick,
    onLongPress,
    disabled,
    hint,
    fixedRatio = true,
    isPressed,
    'aria-disabled': ariaDisabled,
    'data-subscript': subscript,
    testID,
    icon,
    iconSize,
    showTooltip,
    label,
    shortcut,
    tooltipPosition,
    isActiveStyle,
    customContainerStyles,
    hitSlop
  } = props;
  const preferredColorScheme = (0, _compose.usePreferredColorScheme)();
  const isDisabled = ariaDisabled || disabled;
  const containerStyle = [styles.container, customContainerStyles && {
    ...customContainerStyles
  }];
  const buttonActiveColorStyles = (0, _compose.usePreferredColorSchemeStyle)(_style.default['components-button-light--active'], _style.default['components-button-dark--active']);
  const buttonViewStyle = {
    opacity: isDisabled ? 0.3 : 1,
    ...(fixedRatio && styles.fixedRatio),
    ...(isPressed ? styles.buttonActive : styles.buttonInactive),
    ...(isPressed ? buttonActiveColorStyles : {}),
    ...(isPressed && isActiveStyle?.borderRadius && {
      borderRadius: isActiveStyle.borderRadius
    }),
    ...(isActiveStyle?.backgroundColor && {
      backgroundColor: isActiveStyle.backgroundColor
    })
  };
  const states = [];
  if (isPressed) {
    states.push('selected');
  }
  if (isDisabled) {
    states.push('disabled');
  }
  const subscriptInactive = (0, _compose.usePreferredColorSchemeStyle)(styles.subscriptInactive, styles.subscriptInactiveDark);
  const newChildren = _element.Children.map(children, child => {
    return child ? (0, _element.cloneElement)(child, {
      colorScheme: preferredColorScheme,
      isPressed
    }) : child;
  });

  // Should show the tooltip if...
  const shouldShowTooltip = !isDisabled && (
  // An explicit tooltip is passed or...
  showTooltip && label ||
  // There's a shortcut or...
  shortcut ||
  // There's a label and...
  !!label && (
  // The children are empty and...
  !children || Array.isArray(children) && !children.length) &&
  // The tooltip is not explicitly disabled.
  false !== showTooltip);
  const newIcon = icon ? (0, _element.cloneElement)((0, _react.createElement)(_icon.default, {
    icon: icon,
    size: iconSize
  }), {
    isPressed
  }) : null;
  const longPressHandler = (0, _element.useCallback)(({
    nativeEvent
  }) => {
    if (nativeEvent.state === _reactNativeGestureHandler.State.ACTIVE && onLongPress) {
      onLongPress();
    }
  }, [onLongPress]);
  const element = (0, _react.createElement)(_reactNative.TouchableOpacity, {
    activeOpacity: 0.7,
    accessible: true,
    accessibilityLabel: label,
    accessibilityStates: states,
    accessibilityRole: 'button',
    accessibilityHint: hint,
    onPress: onClick,
    style: containerStyle,
    disabled: isDisabled,
    testID: testID,
    hitSlop: hitSlop
  }, (0, _react.createElement)(_reactNativeGestureHandler.LongPressGestureHandler, {
    minDurationMs: 500,
    maxDist: 150,
    onHandlerStateChange: longPressHandler
  }, (0, _react.createElement)(_reactNative.View, {
    style: buttonViewStyle
  }, (0, _react.createElement)(_reactNative.View, {
    style: {
      flexDirection: 'row'
    }
  }, newIcon, newChildren, subscript && (0, _react.createElement)(_reactNative.Text, {
    style: isPressed ? styles.subscriptActive : subscriptInactive
  }, subscript)))));
  if (!shouldShowTooltip) {
    return element;
  }
  return (0, _react.createElement)(_tooltip.default, {
    text: label,
    shortcut: shortcut,
    position: tooltipPosition,
    visible: showTooltip === true
  }, element);
}
var _default = Button;
exports.default = _default;
//# sourceMappingURL=index.native.js.map