{"version": 3, "names": ["_icons", "require", "_compose", "_styles", "_interopRequireDefault", "LockIcon", "iconStyle", "usePreferredColorSchemeStyle", "styles", "icon", "_react", "createElement", "Icon", "lock", "color", "style"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/lock-icon/index.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { Icon, lock } from '@wordpress/icons';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\n\nexport default function LockIcon() {\n\tconst iconStyle = usePreferredColorSchemeStyle(\n\t\tstyles.icon,\n\t\tstyles[ 'icon--dark' ]\n\t);\n\n\treturn <Icon icon={ lock } color={ iconStyle.color } style={ iconStyle } />;\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AAGe,SAASI,QAAQA,CAAA,EAAG;EAClC,MAAMC,SAAS,GAAG,IAAAC,qCAA4B,EAC7CC,eAAM,CAACC,IAAI,EACXD,eAAM,CAAE,YAAY,CACrB,CAAC;EAED,OAAO,IAAAE,MAAA,CAAAC,aAAA,EAACX,MAAA,CAAAY,IAAI;IAACH,IAAI,EAAGI,WAAM;IAACC,KAAK,EAAGR,SAAS,CAACQ,KAAO;IAACC,KAAK,EAAGT;EAAW,CAAE,CAAC;AAC5E"}