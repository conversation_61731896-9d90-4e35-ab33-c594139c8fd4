{"version": 3, "names": ["_reactColorful", "require", "_colord", "_element", "useOnPickerDrag", "containerEl", "onDragStart", "onDragEnd", "isDragging", "useRef", "leftWhileDragging", "useEffect", "interactiveElements", "querySelector", "filter", "el", "length", "doc", "ownerDocument", "onPointerUp", "event", "current", "onPointerDown", "onPointerLeave", "onPointerEnter", "noPointerButtonsArePressed", "buttons", "for<PERSON>ach", "addEventListener", "removeEventListener", "Picker", "color", "enableAlpha", "onChange", "Component", "RgbaStringColorPicker", "RgbStringColorPicker", "rgbColor", "useMemo", "toRgbString", "_react", "createElement", "nextColor", "colord", "exports"], "sources": ["@wordpress/components/src/color-picker/picker.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { RgbStringColorPicker, RgbaStringColorPicker } from 'react-colorful';\nimport { colord } from 'colord';\n\n/**\n * WordPress dependencies\n */\nimport { useMemo, useEffect, useRef } from '@wordpress/element';\n/**\n * Internal dependencies\n */\nimport type { PickerProps } from './types';\n\n/**\n * Track the start and the end of drag pointer events related to controlling\n * the picker's saturation / hue / alpha, and fire the corresponding callbacks.\n * This is particularly useful to implement synergies like the one with the\n * `Popover` component, where a pointer events \"trap\" is rendered while\n * the user is dragging the pointer to avoid potential interference with iframe\n * elements.\n *\n * @param props\n * @param props.containerEl\n * @param props.onDragStart\n * @param props.onDragEnd\n */\nconst useOnPickerDrag = ( {\n\tcontainerEl,\n\tonDragStart,\n\tonDragEnd,\n}: Pick< PickerProps, 'containerEl' | 'onDragStart' | 'onDragEnd' > ) => {\n\tconst isDragging = useRef( false );\n\tconst leftWhileDragging = useRef( false );\n\tuseEffect( () => {\n\t\tif ( ! containerEl || ( ! onDragStart && ! onDragEnd ) ) {\n\t\t\treturn;\n\t\t}\n\t\tconst interactiveElements = [\n\t\t\tcontainerEl.querySelector( '.react-colorful__saturation' ),\n\t\t\tcontainerEl.querySelector( '.react-colorful__hue' ),\n\t\t\tcontainerEl.querySelector( '.react-colorful__alpha' ),\n\t\t].filter( ( el ) => !! el ) as Element[];\n\n\t\tif ( interactiveElements.length === 0 ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst doc = containerEl.ownerDocument;\n\n\t\tconst onPointerUp: EventListener = ( event ) => {\n\t\t\tisDragging.current = false;\n\t\t\tleftWhileDragging.current = false;\n\t\t\tonDragEnd?.( event as MouseEvent );\n\t\t};\n\n\t\tconst onPointerDown: EventListener = ( event ) => {\n\t\t\tisDragging.current = true;\n\t\t\tonDragStart?.( event as MouseEvent );\n\t\t};\n\n\t\tconst onPointerLeave: EventListener = () => {\n\t\t\tleftWhileDragging.current = isDragging.current;\n\t\t};\n\n\t\t// Try to detect if the user released the pointer while away from the\n\t\t// current window. If the check is successfull, the dragEnd callback will\n\t\t// called as soon as the pointer re-enters the window (better late than never)\n\t\tconst onPointerEnter: EventListener = ( event ) => {\n\t\t\tconst noPointerButtonsArePressed =\n\t\t\t\t( event as PointerEvent ).buttons === 0;\n\n\t\t\tif ( leftWhileDragging.current && noPointerButtonsArePressed ) {\n\t\t\t\tonPointerUp( event );\n\t\t\t}\n\t\t};\n\n\t\t// The pointerdown event is added on the interactive elements,\n\t\t// while the remaining events are added on the document object since\n\t\t// the pointer wouldn't necessarily be hovering the initial interactive\n\t\t// element at that point.\n\t\tinteractiveElements.forEach( ( el ) =>\n\t\t\tel.addEventListener( 'pointerdown', onPointerDown )\n\t\t);\n\t\tdoc.addEventListener( 'pointerup', onPointerUp );\n\t\tdoc.addEventListener( 'pointerenter', onPointerEnter );\n\t\tdoc.addEventListener( 'pointerleave', onPointerLeave );\n\n\t\treturn () => {\n\t\t\tinteractiveElements.forEach( ( el ) =>\n\t\t\t\tel.removeEventListener( 'pointerdown', onPointerDown )\n\t\t\t);\n\t\t\tdoc.removeEventListener( 'pointerup', onPointerUp );\n\t\t\tdoc.removeEventListener( 'pointerenter', onPointerEnter );\n\t\t\tdoc.removeEventListener( 'pointerleave', onPointerUp );\n\t\t};\n\t}, [ onDragStart, onDragEnd, containerEl ] );\n};\n\nexport const Picker = ( {\n\tcolor,\n\tenableAlpha,\n\tonChange,\n\tonDragStart,\n\tonDragEnd,\n\tcontainerEl,\n}: PickerProps ) => {\n\tconst Component = enableAlpha\n\t\t? RgbaStringColorPicker\n\t\t: RgbStringColorPicker;\n\tconst rgbColor = useMemo( () => color.toRgbString(), [ color ] );\n\n\tuseOnPickerDrag( { containerEl, onDragStart, onDragEnd } );\n\n\treturn (\n\t\t<Component\n\t\t\tcolor={ rgbColor }\n\t\t\tonChange={ ( nextColor ) => {\n\t\t\t\tonChange( colord( nextColor ) );\n\t\t\t} }\n\t\t/>\n\t);\n};\n"], "mappings": ";;;;;;;AAGA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAF,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,eAAe,GAAGA,CAAE;EACzBC,WAAW;EACXC,WAAW;EACXC;AACiE,CAAC,KAAM;EACxE,MAAMC,UAAU,GAAG,IAAAC,eAAM,EAAE,KAAM,CAAC;EAClC,MAAMC,iBAAiB,GAAG,IAAAD,eAAM,EAAE,KAAM,CAAC;EACzC,IAAAE,kBAAS,EAAE,MAAM;IAChB,IAAK,CAAEN,WAAW,IAAM,CAAEC,WAAW,IAAI,CAAEC,SAAW,EAAG;MACxD;IACD;IACA,MAAMK,mBAAmB,GAAG,CAC3BP,WAAW,CAACQ,aAAa,CAAE,6BAA8B,CAAC,EAC1DR,WAAW,CAACQ,aAAa,CAAE,sBAAuB,CAAC,EACnDR,WAAW,CAACQ,aAAa,CAAE,wBAAyB,CAAC,CACrD,CAACC,MAAM,CAAIC,EAAE,IAAM,CAAC,CAAEA,EAAG,CAAc;IAExC,IAAKH,mBAAmB,CAACI,MAAM,KAAK,CAAC,EAAG;MACvC;IACD;IAEA,MAAMC,GAAG,GAAGZ,WAAW,CAACa,aAAa;IAErC,MAAMC,WAA0B,GAAKC,KAAK,IAAM;MAC/CZ,UAAU,CAACa,OAAO,GAAG,KAAK;MAC1BX,iBAAiB,CAACW,OAAO,GAAG,KAAK;MACjCd,SAAS,GAAIa,KAAoB,CAAC;IACnC,CAAC;IAED,MAAME,aAA4B,GAAKF,KAAK,IAAM;MACjDZ,UAAU,CAACa,OAAO,GAAG,IAAI;MACzBf,WAAW,GAAIc,KAAoB,CAAC;IACrC,CAAC;IAED,MAAMG,cAA6B,GAAGA,CAAA,KAAM;MAC3Cb,iBAAiB,CAACW,OAAO,GAAGb,UAAU,CAACa,OAAO;IAC/C,CAAC;;IAED;IACA;IACA;IACA,MAAMG,cAA6B,GAAKJ,KAAK,IAAM;MAClD,MAAMK,0BAA0B,GAC7BL,KAAK,CAAmBM,OAAO,KAAK,CAAC;MAExC,IAAKhB,iBAAiB,CAACW,OAAO,IAAII,0BAA0B,EAAG;QAC9DN,WAAW,CAAEC,KAAM,CAAC;MACrB;IACD,CAAC;;IAED;IACA;IACA;IACA;IACAR,mBAAmB,CAACe,OAAO,CAAIZ,EAAE,IAChCA,EAAE,CAACa,gBAAgB,CAAE,aAAa,EAAEN,aAAc,CACnD,CAAC;IACDL,GAAG,CAACW,gBAAgB,CAAE,WAAW,EAAET,WAAY,CAAC;IAChDF,GAAG,CAACW,gBAAgB,CAAE,cAAc,EAAEJ,cAAe,CAAC;IACtDP,GAAG,CAACW,gBAAgB,CAAE,cAAc,EAAEL,cAAe,CAAC;IAEtD,OAAO,MAAM;MACZX,mBAAmB,CAACe,OAAO,CAAIZ,EAAE,IAChCA,EAAE,CAACc,mBAAmB,CAAE,aAAa,EAAEP,aAAc,CACtD,CAAC;MACDL,GAAG,CAACY,mBAAmB,CAAE,WAAW,EAAEV,WAAY,CAAC;MACnDF,GAAG,CAACY,mBAAmB,CAAE,cAAc,EAAEL,cAAe,CAAC;MACzDP,GAAG,CAACY,mBAAmB,CAAE,cAAc,EAAEV,WAAY,CAAC;IACvD,CAAC;EACF,CAAC,EAAE,CAAEb,WAAW,EAAEC,SAAS,EAAEF,WAAW,CAAG,CAAC;AAC7C,CAAC;AAEM,MAAMyB,MAAM,GAAGA,CAAE;EACvBC,KAAK;EACLC,WAAW;EACXC,QAAQ;EACR3B,WAAW;EACXC,SAAS;EACTF;AACY,CAAC,KAAM;EACnB,MAAM6B,SAAS,GAAGF,WAAW,GAC1BG,oCAAqB,GACrBC,mCAAoB;EACvB,MAAMC,QAAQ,GAAG,IAAAC,gBAAO,EAAE,MAAMP,KAAK,CAACQ,WAAW,CAAC,CAAC,EAAE,CAAER,KAAK,CAAG,CAAC;EAEhE3B,eAAe,CAAE;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAU,CAAE,CAAC;EAE1D,OACC,IAAAiC,MAAA,CAAAC,aAAA,EAACP,SAAS;IACTH,KAAK,EAAGM,QAAU;IAClBJ,QAAQ,EAAKS,SAAS,IAAM;MAC3BT,QAAQ,CAAE,IAAAU,cAAM,EAAED,SAAU,CAAE,CAAC;IAChC;EAAG,CACH,CAAC;AAEJ,CAAC;AAACE,OAAA,CAAAd,MAAA,GAAAA,MAAA"}