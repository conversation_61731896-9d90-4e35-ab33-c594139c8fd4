"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.initialStateReducer = exports.initialInputControlState = void 0;
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const initialStateReducer = state => state;
exports.initialStateReducer = initialStateReducer;
const initialInputControlState = {
  error: null,
  initialValue: '',
  isDirty: false,
  isDragEnabled: false,
  isDragging: false,
  isPressEnterToChange: false,
  value: ''
};
exports.initialInputControlState = initialInputControlState;
//# sourceMappingURL=state.js.map