{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_i18n", "_compose", "_a11y", "_isShallowEqual", "_token", "_tokenInput", "_styles", "_suggestionsList", "_flex", "_baseControlStyles", "_spacer", "_useDeprecatedProps", "identity", "value", "FormTokenField", "props", "autoCapitalize", "autoComplete", "max<PERSON><PERSON><PERSON>", "placeholder", "label", "__", "className", "suggestions", "maxSuggestions", "displayTransform", "saveTransform", "token", "trim", "onChange", "onInputChange", "onFocus", "undefined", "isBorderless", "disabled", "tokenizeOnSpace", "messages", "added", "removed", "remove", "__experimentalInvalid", "__experimentalRenderItem", "__experimentalExpandOnFocus", "__experimentalValidateInput", "__experimentalShowHowTo", "__next40pxDefaultSize", "__experimentalAutoSelectFirstMatch", "__nextHasNoMarginBottom", "tokenizeOnBlur", "useDeprecated36pxDefaultSizeProp", "instanceId", "useInstanceId", "incompleteTokenValue", "setIncompleteTokenValue", "useState", "inputOffsetFromEnd", "setInputOffsetFromEnd", "isActive", "setIsActive", "isExpanded", "setIsExpanded", "selectedSuggestionIndex", "setSelectedSuggestionIndex", "selectedSuggestionScroll", "setSelectedSuggestionScroll", "prevSuggestions", "usePrevious", "prevValue", "input", "useRef", "tokensAndInput", "debouncedSpeak", "useDebounce", "speak", "useEffect", "hasFocus", "focus", "suggestionsDidUpdate", "isShallowEqual", "updateSuggestions", "current", "ownerDocument", "activeElement", "onFocusHandler", "event", "target", "onBlur", "inputHasValidValue", "addNewToken", "hasFocusWithin", "relatedTarget", "onKeyDown", "preventDefault", "defaultPrevented", "nativeEvent", "isComposing", "keyCode", "key", "handleDeleteKey", "deleteTokenBeforeInput", "addCurrentToken", "handleLeftArrowKey", "handleUpArrowKey", "handleRightArrowKey", "handleDownArrowKey", "deleteTokenAfterInput", "handleEscapeKey", "onKeyPress", "handleCommaKey", "onContainerTouched", "onTokenClickRemove", "deleteToken", "onSuggestionHovered", "suggestion", "index", "getMatchingSuggestions", "indexOf", "onSuggestionSelected", "onInputChangeHandler", "text", "separator", "items", "split", "tokenValue", "length", "addNewTokens", "slice", "_deleteToken", "isInputEmpty", "moveInputBeforePreviousToken", "moveInputAfterNextToken", "HTMLInputElement", "moveInputToIndex", "Math", "max", "prevInputOffsetFromEnd", "min", "getIndexOfInput", "selectedSuggestion", "getSelectedSuggestion", "tokens", "tokensToAdd", "Set", "map", "filter", "Boolean", "valueContainsToken", "newValue", "splice", "newTokens", "item", "getTokenValue", "searchValue", "_suggestions", "_value", "_maxSuggestions", "_saveTransform", "match", "startsWithMatch", "containsMatch", "normalizedValue", "includes", "toLocaleLowerCase", "for<PERSON>ach", "push", "concat", "some", "resetSelectedSuggestion", "inputHasMinimumChars", "matchingSuggestions", "hasMatchingSuggestions", "shouldExpandIfFocuses", "message", "sprintf", "_n", "renderTokensAndInput", "components", "renderToken", "renderInput", "status", "termPosition", "termsCount", "_react", "createElement", "FlexItem", "default", "title", "onClickRemove", "onMouseEnter", "onMouseLeave", "inputProps", "ref", "classes", "classnames", "tokenFieldProps", "tabIndex", "Object", "assign", "StyledLabel", "htmlFor", "onMouseDown", "onTouchStart", "TokensAndInputWrapperFlex", "justify", "align", "gap", "wrap", "hasTokens", "selectedIndex", "scrollIntoView", "onHover", "onSelect", "Spacer", "marginBottom", "StyledHelp", "id", "_default", "exports"], "sources": ["@wordpress/components/src/form-token-field/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { KeyboardEvent, MouseEvent, TouchEvent, FocusEvent } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useEffect, useRef, useState } from '@wordpress/element';\nimport { __, _n, sprintf } from '@wordpress/i18n';\nimport { useDebounce, useInstanceId, usePrevious } from '@wordpress/compose';\nimport { speak } from '@wordpress/a11y';\nimport isShallowEqual from '@wordpress/is-shallow-equal';\n\n/**\n * Internal dependencies\n */\nimport Token from './token';\nimport TokenInput from './token-input';\nimport { TokensAndInputWrapperFlex } from './styles';\nimport SuggestionsList from './suggestions-list';\nimport type { FormTokenFieldProps, TokenItem } from './types';\nimport { FlexItem } from '../flex';\nimport {\n\tStyledHelp,\n\tStyledLabel,\n} from '../base-control/styles/base-control-styles';\nimport { Spacer } from '../spacer';\nimport { useDeprecated36pxDefaultSizeProp } from '../utils/use-deprecated-props';\n\nconst identity = ( value: string ) => value;\n\n/**\n * A `FormTokenField` is a field similar to the tags and categories fields in the interim editor chrome,\n * or the \"to\" field in Mail on OS X. Tokens can be entered by typing them or selecting them from a list of suggested tokens.\n *\n * Up to one hundred suggestions that match what the user has typed so far will be shown from which the user can pick from (auto-complete).\n * Tokens are separated by the \",\" character. Suggestions can be selected with the up or down arrows and added with the tab or enter key.\n *\n * The `value` property is handled in a manner similar to controlled form components.\n * See [Forms](http://facebook.github.io/react/docs/forms.html) in the React Documentation for more information.\n */\nexport function FormTokenField( props: FormTokenFieldProps ) {\n\tconst {\n\t\tautoCapitalize,\n\t\tautoComplete,\n\t\tmaxLength,\n\t\tplaceholder,\n\t\tlabel = __( 'Add item' ),\n\t\tclassName,\n\t\tsuggestions = [],\n\t\tmaxSuggestions = 100,\n\t\tvalue = [],\n\t\tdisplayTransform = identity,\n\t\tsaveTransform = ( token ) => token.trim(),\n\t\tonChange = () => {},\n\t\tonInputChange = () => {},\n\t\tonFocus = undefined,\n\t\tisBorderless = false,\n\t\tdisabled = false,\n\t\ttokenizeOnSpace = false,\n\t\tmessages = {\n\t\t\tadded: __( 'Item added.' ),\n\t\t\tremoved: __( 'Item removed.' ),\n\t\t\tremove: __( 'Remove item' ),\n\t\t\t__experimentalInvalid: __( 'Invalid item' ),\n\t\t},\n\t\t__experimentalRenderItem,\n\t\t__experimentalExpandOnFocus = false,\n\t\t__experimentalValidateInput = () => true,\n\t\t__experimentalShowHowTo = true,\n\t\t__next40pxDefaultSize = false,\n\t\t__experimentalAutoSelectFirstMatch = false,\n\t\t__nextHasNoMarginBottom = false,\n\t\ttokenizeOnBlur = false,\n\t} = useDeprecated36pxDefaultSizeProp< FormTokenFieldProps >(\n\t\tprops,\n\t\t'wp.components.FormTokenField'\n\t);\n\n\tconst instanceId = useInstanceId( FormTokenField );\n\n\t// We reset to these initial values again in the onBlur\n\tconst [ incompleteTokenValue, setIncompleteTokenValue ] = useState( '' );\n\tconst [ inputOffsetFromEnd, setInputOffsetFromEnd ] = useState( 0 );\n\tconst [ isActive, setIsActive ] = useState( false );\n\tconst [ isExpanded, setIsExpanded ] = useState( false );\n\tconst [ selectedSuggestionIndex, setSelectedSuggestionIndex ] =\n\t\tuseState( -1 );\n\tconst [ selectedSuggestionScroll, setSelectedSuggestionScroll ] =\n\t\tuseState( false );\n\n\tconst prevSuggestions = usePrevious< string[] >( suggestions );\n\tconst prevValue = usePrevious< ( string | TokenItem )[] >( value );\n\n\tconst input = useRef< HTMLInputElement >( null );\n\tconst tokensAndInput = useRef< HTMLInputElement >( null );\n\n\tconst debouncedSpeak = useDebounce( speak, 500 );\n\n\tuseEffect( () => {\n\t\t// Make sure to focus the input when the isActive state is true.\n\t\tif ( isActive && ! hasFocus() ) {\n\t\t\tfocus();\n\t\t}\n\t}, [ isActive ] );\n\n\tuseEffect( () => {\n\t\tconst suggestionsDidUpdate = ! isShallowEqual(\n\t\t\tsuggestions,\n\t\t\tprevSuggestions || []\n\t\t);\n\n\t\tif ( suggestionsDidUpdate || value !== prevValue ) {\n\t\t\tupdateSuggestions( suggestionsDidUpdate );\n\t\t}\n\n\t\t// TODO: updateSuggestions() should first be refactored so its actual deps are clearer.\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [ suggestions, prevSuggestions, value, prevValue ] );\n\n\tuseEffect( () => {\n\t\tupdateSuggestions();\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [ incompleteTokenValue ] );\n\n\tuseEffect( () => {\n\t\tupdateSuggestions();\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [ __experimentalAutoSelectFirstMatch ] );\n\n\tif ( disabled && isActive ) {\n\t\tsetIsActive( false );\n\t\tsetIncompleteTokenValue( '' );\n\t}\n\n\tfunction focus() {\n\t\tinput.current?.focus();\n\t}\n\n\tfunction hasFocus() {\n\t\treturn input.current === input.current?.ownerDocument.activeElement;\n\t}\n\n\tfunction onFocusHandler( event: FocusEvent ) {\n\t\t// If focus is on the input or on the container, set the isActive state to true.\n\t\tif ( hasFocus() || event.target === tokensAndInput.current ) {\n\t\t\tsetIsActive( true );\n\t\t\tsetIsExpanded( __experimentalExpandOnFocus || isExpanded );\n\t\t} else {\n\t\t\t/*\n\t\t\t * Otherwise, focus is on one of the token \"remove\" buttons and we\n\t\t\t * set the isActive state to false to prevent the input to be\n\t\t\t * re-focused, see componentDidUpdate().\n\t\t\t */\n\t\t\tsetIsActive( false );\n\t\t}\n\n\t\tif ( 'function' === typeof onFocus ) {\n\t\t\tonFocus( event );\n\t\t}\n\t}\n\n\tfunction onBlur( event: FocusEvent ) {\n\t\tif (\n\t\t\tinputHasValidValue() &&\n\t\t\t__experimentalValidateInput( incompleteTokenValue )\n\t\t) {\n\t\t\tsetIsActive( false );\n\t\t\tif ( tokenizeOnBlur && inputHasValidValue() ) {\n\t\t\t\taddNewToken( incompleteTokenValue );\n\t\t\t}\n\t\t} else {\n\t\t\t// Reset to initial state\n\t\t\tsetIncompleteTokenValue( '' );\n\t\t\tsetInputOffsetFromEnd( 0 );\n\t\t\tsetIsActive( false );\n\n\t\t\tif ( __experimentalExpandOnFocus ) {\n\t\t\t\t// If `__experimentalExpandOnFocus` is true, don't close the suggestions list when\n\t\t\t\t// the user clicks on it (`tokensAndInput` will be the element that caused the blur).\n\t\t\t\tconst hasFocusWithin =\n\t\t\t\t\tevent.relatedTarget === tokensAndInput.current;\n\t\t\t\tsetIsExpanded( hasFocusWithin );\n\t\t\t} else {\n\t\t\t\t// Else collapse the suggestion list. This will result in the suggestion list closing\n\t\t\t\t// after a suggestion has been submitted since that causes a blur.\n\t\t\t\tsetIsExpanded( false );\n\t\t\t}\n\n\t\t\tsetSelectedSuggestionIndex( -1 );\n\t\t\tsetSelectedSuggestionScroll( false );\n\t\t}\n\t}\n\n\tfunction onKeyDown( event: KeyboardEvent ) {\n\t\tlet preventDefault = false;\n\n\t\tif (\n\t\t\tevent.defaultPrevented ||\n\t\t\t// Ignore keydowns from IMEs\n\t\t\tevent.nativeEvent.isComposing ||\n\t\t\t// Workaround for Mac Safari where the final Enter/Backspace of an IME composition\n\t\t\t// is `isComposing=false`, even though it's technically still part of the composition.\n\t\t\t// These can only be detected by keyCode.\n\t\t\tevent.keyCode === 229\n\t\t) {\n\t\t\treturn;\n\t\t}\n\t\tswitch ( event.key ) {\n\t\t\tcase 'Backspace':\n\t\t\t\tpreventDefault = handleDeleteKey( deleteTokenBeforeInput );\n\t\t\t\tbreak;\n\t\t\tcase 'Enter':\n\t\t\t\tpreventDefault = addCurrentToken();\n\t\t\t\tbreak;\n\t\t\tcase 'ArrowLeft':\n\t\t\t\tpreventDefault = handleLeftArrowKey();\n\t\t\t\tbreak;\n\t\t\tcase 'ArrowUp':\n\t\t\t\tpreventDefault = handleUpArrowKey();\n\t\t\t\tbreak;\n\t\t\tcase 'ArrowRight':\n\t\t\t\tpreventDefault = handleRightArrowKey();\n\t\t\t\tbreak;\n\t\t\tcase 'ArrowDown':\n\t\t\t\tpreventDefault = handleDownArrowKey();\n\t\t\t\tbreak;\n\t\t\tcase 'Delete':\n\t\t\t\tpreventDefault = handleDeleteKey( deleteTokenAfterInput );\n\t\t\t\tbreak;\n\t\t\tcase 'Space':\n\t\t\t\tif ( tokenizeOnSpace ) {\n\t\t\t\t\tpreventDefault = addCurrentToken();\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'Escape':\n\t\t\t\tpreventDefault = handleEscapeKey( event );\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\n\t\tif ( preventDefault ) {\n\t\t\tevent.preventDefault();\n\t\t}\n\t}\n\n\tfunction onKeyPress( event: KeyboardEvent ) {\n\t\tlet preventDefault = false;\n\n\t\tswitch ( event.key ) {\n\t\t\tcase ',':\n\t\t\t\tpreventDefault = handleCommaKey();\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\n\t\tif ( preventDefault ) {\n\t\t\tevent.preventDefault();\n\t\t}\n\t}\n\n\tfunction onContainerTouched( event: MouseEvent | TouchEvent ) {\n\t\t// Prevent clicking/touching the tokensAndInput container from blurring\n\t\t// the input and adding the current token.\n\t\tif ( event.target === tokensAndInput.current && isActive ) {\n\t\t\tevent.preventDefault();\n\t\t}\n\t}\n\n\tfunction onTokenClickRemove( event: { value: string } ) {\n\t\tdeleteToken( event.value );\n\t\tfocus();\n\t}\n\n\tfunction onSuggestionHovered( suggestion: string ) {\n\t\tconst index = getMatchingSuggestions().indexOf( suggestion );\n\n\t\tif ( index >= 0 ) {\n\t\t\tsetSelectedSuggestionIndex( index );\n\t\t\tsetSelectedSuggestionScroll( false );\n\t\t}\n\t}\n\n\tfunction onSuggestionSelected( suggestion: string ) {\n\t\taddNewToken( suggestion );\n\t}\n\n\tfunction onInputChangeHandler( event: { value: string } ) {\n\t\tconst text = event.value;\n\t\tconst separator = tokenizeOnSpace ? /[ ,\\t]+/ : /[,\\t]+/;\n\t\tconst items = text.split( separator );\n\t\tconst tokenValue = items[ items.length - 1 ] || '';\n\n\t\tif ( items.length > 1 ) {\n\t\t\taddNewTokens( items.slice( 0, -1 ) );\n\t\t}\n\t\tsetIncompleteTokenValue( tokenValue );\n\t\tonInputChange( tokenValue );\n\t}\n\n\tfunction handleDeleteKey( _deleteToken: () => void ) {\n\t\tlet preventDefault = false;\n\t\tif ( hasFocus() && isInputEmpty() ) {\n\t\t\t_deleteToken();\n\t\t\tpreventDefault = true;\n\t\t}\n\n\t\treturn preventDefault;\n\t}\n\n\tfunction handleLeftArrowKey() {\n\t\tlet preventDefault = false;\n\t\tif ( isInputEmpty() ) {\n\t\t\tmoveInputBeforePreviousToken();\n\t\t\tpreventDefault = true;\n\t\t}\n\n\t\treturn preventDefault;\n\t}\n\n\tfunction handleRightArrowKey() {\n\t\tlet preventDefault = false;\n\t\tif ( isInputEmpty() ) {\n\t\t\tmoveInputAfterNextToken();\n\t\t\tpreventDefault = true;\n\t\t}\n\n\t\treturn preventDefault;\n\t}\n\n\tfunction handleUpArrowKey() {\n\t\tsetSelectedSuggestionIndex( ( index ) => {\n\t\t\treturn (\n\t\t\t\t( index === 0\n\t\t\t\t\t? getMatchingSuggestions(\n\t\t\t\t\t\t\tincompleteTokenValue,\n\t\t\t\t\t\t\tsuggestions,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\tmaxSuggestions,\n\t\t\t\t\t\t\tsaveTransform\n\t\t\t\t\t  ).length\n\t\t\t\t\t: index ) - 1\n\t\t\t);\n\t\t} );\n\t\tsetSelectedSuggestionScroll( true );\n\n\t\treturn true; // PreventDefault.\n\t}\n\n\tfunction handleDownArrowKey() {\n\t\tsetSelectedSuggestionIndex( ( index ) => {\n\t\t\treturn (\n\t\t\t\t( index + 1 ) %\n\t\t\t\tgetMatchingSuggestions(\n\t\t\t\t\tincompleteTokenValue,\n\t\t\t\t\tsuggestions,\n\t\t\t\t\tvalue,\n\t\t\t\t\tmaxSuggestions,\n\t\t\t\t\tsaveTransform\n\t\t\t\t).length\n\t\t\t);\n\t\t} );\n\n\t\tsetSelectedSuggestionScroll( true );\n\t\treturn true; // PreventDefault.\n\t}\n\n\tfunction handleEscapeKey( event: KeyboardEvent ) {\n\t\tif ( event.target instanceof HTMLInputElement ) {\n\t\t\tsetIncompleteTokenValue( event.target.value );\n\t\t\tsetIsExpanded( false );\n\t\t\tsetSelectedSuggestionIndex( -1 );\n\t\t\tsetSelectedSuggestionScroll( false );\n\t\t}\n\n\t\treturn true; // PreventDefault.\n\t}\n\n\tfunction handleCommaKey() {\n\t\tif ( inputHasValidValue() ) {\n\t\t\taddNewToken( incompleteTokenValue );\n\t\t}\n\n\t\treturn true; // PreventDefault.\n\t}\n\n\tfunction moveInputToIndex( index: number ) {\n\t\tsetInputOffsetFromEnd( value.length - Math.max( index, -1 ) - 1 );\n\t}\n\n\tfunction moveInputBeforePreviousToken() {\n\t\tsetInputOffsetFromEnd( ( prevInputOffsetFromEnd ) => {\n\t\t\treturn Math.min( prevInputOffsetFromEnd + 1, value.length );\n\t\t} );\n\t}\n\n\tfunction moveInputAfterNextToken() {\n\t\tsetInputOffsetFromEnd( ( prevInputOffsetFromEnd ) => {\n\t\t\treturn Math.max( prevInputOffsetFromEnd - 1, 0 );\n\t\t} );\n\t}\n\n\tfunction deleteTokenBeforeInput() {\n\t\tconst index = getIndexOfInput() - 1;\n\n\t\tif ( index > -1 ) {\n\t\t\tdeleteToken( value[ index ] );\n\t\t}\n\t}\n\n\tfunction deleteTokenAfterInput() {\n\t\tconst index = getIndexOfInput();\n\n\t\tif ( index < value.length ) {\n\t\t\tdeleteToken( value[ index ] );\n\t\t\t// Update input offset since it's the offset from the last token.\n\t\t\tmoveInputToIndex( index );\n\t\t}\n\t}\n\n\tfunction addCurrentToken() {\n\t\tlet preventDefault = false;\n\t\tconst selectedSuggestion = getSelectedSuggestion();\n\n\t\tif ( selectedSuggestion ) {\n\t\t\taddNewToken( selectedSuggestion );\n\t\t\tpreventDefault = true;\n\t\t} else if ( inputHasValidValue() ) {\n\t\t\taddNewToken( incompleteTokenValue );\n\t\t\tpreventDefault = true;\n\t\t}\n\n\t\treturn preventDefault;\n\t}\n\n\tfunction addNewTokens( tokens: string[] ) {\n\t\tconst tokensToAdd = [\n\t\t\t...new Set(\n\t\t\t\ttokens\n\t\t\t\t\t.map( saveTransform )\n\t\t\t\t\t.filter( Boolean )\n\t\t\t\t\t.filter( ( token ) => ! valueContainsToken( token ) )\n\t\t\t),\n\t\t];\n\n\t\tif ( tokensToAdd.length > 0 ) {\n\t\t\tconst newValue = [ ...value ];\n\t\t\tnewValue.splice( getIndexOfInput(), 0, ...tokensToAdd );\n\t\t\tonChange( newValue );\n\t\t}\n\t}\n\n\tfunction addNewToken( token: string ) {\n\t\tif ( ! __experimentalValidateInput( token ) ) {\n\t\t\tspeak( messages.__experimentalInvalid, 'assertive' );\n\t\t\treturn;\n\t\t}\n\t\taddNewTokens( [ token ] );\n\t\tspeak( messages.added, 'assertive' );\n\n\t\tsetIncompleteTokenValue( '' );\n\t\tsetSelectedSuggestionIndex( -1 );\n\t\tsetSelectedSuggestionScroll( false );\n\t\tsetIsExpanded( ! __experimentalExpandOnFocus );\n\n\t\tif ( isActive && ! tokenizeOnBlur ) {\n\t\t\tfocus();\n\t\t}\n\t}\n\n\tfunction deleteToken( token: string | TokenItem ) {\n\t\tconst newTokens = value.filter( ( item ) => {\n\t\t\treturn getTokenValue( item ) !== getTokenValue( token );\n\t\t} );\n\t\tonChange( newTokens );\n\t\tspeak( messages.removed, 'assertive' );\n\t}\n\n\tfunction getTokenValue( token: { value: string } | string ) {\n\t\tif ( 'object' === typeof token ) {\n\t\t\treturn token.value;\n\t\t}\n\n\t\treturn token;\n\t}\n\n\tfunction getMatchingSuggestions(\n\t\tsearchValue = incompleteTokenValue,\n\t\t_suggestions = suggestions,\n\t\t_value = value,\n\t\t_maxSuggestions = maxSuggestions,\n\t\t_saveTransform = saveTransform\n\t) {\n\t\tlet match = _saveTransform( searchValue );\n\t\tconst startsWithMatch: string[] = [];\n\t\tconst containsMatch: string[] = [];\n\t\tconst normalizedValue = _value.map( ( item ) => {\n\t\t\tif ( typeof item === 'string' ) {\n\t\t\t\treturn item;\n\t\t\t}\n\t\t\treturn item.value;\n\t\t} );\n\n\t\tif ( match.length === 0 ) {\n\t\t\t_suggestions = _suggestions.filter(\n\t\t\t\t( suggestion ) => ! normalizedValue.includes( suggestion )\n\t\t\t);\n\t\t} else {\n\t\t\tmatch = match.toLocaleLowerCase();\n\n\t\t\t_suggestions.forEach( ( suggestion ) => {\n\t\t\t\tconst index = suggestion.toLocaleLowerCase().indexOf( match );\n\t\t\t\tif ( normalizedValue.indexOf( suggestion ) === -1 ) {\n\t\t\t\t\tif ( index === 0 ) {\n\t\t\t\t\t\tstartsWithMatch.push( suggestion );\n\t\t\t\t\t} else if ( index > 0 ) {\n\t\t\t\t\t\tcontainsMatch.push( suggestion );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t_suggestions = startsWithMatch.concat( containsMatch );\n\t\t}\n\n\t\treturn _suggestions.slice( 0, _maxSuggestions );\n\t}\n\n\tfunction getSelectedSuggestion() {\n\t\tif ( selectedSuggestionIndex !== -1 ) {\n\t\t\treturn getMatchingSuggestions()[ selectedSuggestionIndex ];\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tfunction valueContainsToken( token: string ) {\n\t\treturn value.some( ( item ) => {\n\t\t\treturn getTokenValue( token ) === getTokenValue( item );\n\t\t} );\n\t}\n\n\tfunction getIndexOfInput() {\n\t\treturn value.length - inputOffsetFromEnd;\n\t}\n\n\tfunction isInputEmpty() {\n\t\treturn incompleteTokenValue.length === 0;\n\t}\n\n\tfunction inputHasValidValue() {\n\t\treturn saveTransform( incompleteTokenValue ).length > 0;\n\t}\n\n\tfunction updateSuggestions( resetSelectedSuggestion = true ) {\n\t\tconst inputHasMinimumChars = incompleteTokenValue.trim().length > 1;\n\t\tconst matchingSuggestions =\n\t\t\tgetMatchingSuggestions( incompleteTokenValue );\n\t\tconst hasMatchingSuggestions = matchingSuggestions.length > 0;\n\n\t\tconst shouldExpandIfFocuses = hasFocus() && __experimentalExpandOnFocus;\n\t\tsetIsExpanded(\n\t\t\tshouldExpandIfFocuses ||\n\t\t\t\t( inputHasMinimumChars && hasMatchingSuggestions )\n\t\t);\n\n\t\tif ( resetSelectedSuggestion ) {\n\t\t\tif (\n\t\t\t\t__experimentalAutoSelectFirstMatch &&\n\t\t\t\tinputHasMinimumChars &&\n\t\t\t\thasMatchingSuggestions\n\t\t\t) {\n\t\t\t\tsetSelectedSuggestionIndex( 0 );\n\t\t\t\tsetSelectedSuggestionScroll( true );\n\t\t\t} else {\n\t\t\t\tsetSelectedSuggestionIndex( -1 );\n\t\t\t\tsetSelectedSuggestionScroll( false );\n\t\t\t}\n\t\t}\n\n\t\tif ( inputHasMinimumChars ) {\n\t\t\tconst message = hasMatchingSuggestions\n\t\t\t\t? sprintf(\n\t\t\t\t\t\t/* translators: %d: number of results. */\n\t\t\t\t\t\t_n(\n\t\t\t\t\t\t\t'%d result found, use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\t'%d results found, use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\tmatchingSuggestions.length\n\t\t\t\t\t\t),\n\t\t\t\t\t\tmatchingSuggestions.length\n\t\t\t\t  )\n\t\t\t\t: __( 'No results.' );\n\n\t\t\tdebouncedSpeak( message, 'assertive' );\n\t\t}\n\t}\n\n\tfunction renderTokensAndInput() {\n\t\tconst components = value.map( renderToken );\n\t\tcomponents.splice( getIndexOfInput(), 0, renderInput() );\n\n\t\treturn components;\n\t}\n\n\tfunction renderToken(\n\t\ttoken: string | TokenItem,\n\t\tindex: number,\n\t\ttokens: ( string | TokenItem )[]\n\t) {\n\t\tconst _value = getTokenValue( token );\n\t\tconst status = typeof token !== 'string' ? token.status : undefined;\n\t\tconst termPosition = index + 1;\n\t\tconst termsCount = tokens.length;\n\n\t\treturn (\n\t\t\t<FlexItem key={ 'token-' + _value }>\n\t\t\t\t<Token\n\t\t\t\t\tvalue={ _value }\n\t\t\t\t\tstatus={ status }\n\t\t\t\t\ttitle={\n\t\t\t\t\t\ttypeof token !== 'string' ? token.title : undefined\n\t\t\t\t\t}\n\t\t\t\t\tdisplayTransform={ displayTransform }\n\t\t\t\t\tonClickRemove={ onTokenClickRemove }\n\t\t\t\t\tisBorderless={\n\t\t\t\t\t\t( typeof token !== 'string' && token.isBorderless ) ||\n\t\t\t\t\t\tisBorderless\n\t\t\t\t\t}\n\t\t\t\t\tonMouseEnter={\n\t\t\t\t\t\ttypeof token !== 'string'\n\t\t\t\t\t\t\t? token.onMouseEnter\n\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t}\n\t\t\t\t\tonMouseLeave={\n\t\t\t\t\t\ttypeof token !== 'string'\n\t\t\t\t\t\t\t? token.onMouseLeave\n\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t}\n\t\t\t\t\tdisabled={ 'error' !== status && disabled }\n\t\t\t\t\tmessages={ messages }\n\t\t\t\t\ttermsCount={ termsCount }\n\t\t\t\t\ttermPosition={ termPosition }\n\t\t\t\t/>\n\t\t\t</FlexItem>\n\t\t);\n\t}\n\n\tfunction renderInput() {\n\t\tconst inputProps = {\n\t\t\tinstanceId,\n\t\t\tautoCapitalize,\n\t\t\tautoComplete,\n\t\t\tplaceholder: value.length === 0 ? placeholder : '',\n\t\t\tkey: 'input',\n\t\t\tdisabled,\n\t\t\tvalue: incompleteTokenValue,\n\t\t\tonBlur,\n\t\t\tisExpanded,\n\t\t\tselectedSuggestionIndex,\n\t\t};\n\n\t\treturn (\n\t\t\t<TokenInput\n\t\t\t\t{ ...inputProps }\n\t\t\t\tonChange={\n\t\t\t\t\t! ( maxLength && value.length >= maxLength )\n\t\t\t\t\t\t? onInputChangeHandler\n\t\t\t\t\t\t: undefined\n\t\t\t\t}\n\t\t\t\tref={ input }\n\t\t\t/>\n\t\t);\n\t}\n\n\tconst classes = classnames(\n\t\tclassName,\n\t\t'components-form-token-field__input-container',\n\t\t{\n\t\t\t'is-active': isActive,\n\t\t\t'is-disabled': disabled,\n\t\t}\n\t);\n\n\tlet tokenFieldProps = {\n\t\tclassName: 'components-form-token-field',\n\t\ttabIndex: -1,\n\t};\n\tconst matchingSuggestions = getMatchingSuggestions();\n\n\tif ( ! disabled ) {\n\t\ttokenFieldProps = Object.assign( {}, tokenFieldProps, {\n\t\t\tonKeyDown,\n\t\t\tonKeyPress,\n\t\t\tonFocus: onFocusHandler,\n\t\t} );\n\t}\n\n\t// Disable reason: There is no appropriate role which describes the\n\t// input container intended accessible usability.\n\t// TODO: Refactor click detection to use blur to stop propagation.\n\t/* eslint-disable jsx-a11y/no-static-element-interactions */\n\treturn (\n\t\t<div { ...tokenFieldProps }>\n\t\t\t<StyledLabel\n\t\t\t\thtmlFor={ `components-form-token-input-${ instanceId }` }\n\t\t\t\tclassName=\"components-form-token-field__label\"\n\t\t\t>\n\t\t\t\t{ label }\n\t\t\t</StyledLabel>\n\t\t\t<div\n\t\t\t\tref={ tokensAndInput }\n\t\t\t\tclassName={ classes }\n\t\t\t\ttabIndex={ -1 }\n\t\t\t\tonMouseDown={ onContainerTouched }\n\t\t\t\tonTouchStart={ onContainerTouched }\n\t\t\t>\n\t\t\t\t<TokensAndInputWrapperFlex\n\t\t\t\t\tjustify=\"flex-start\"\n\t\t\t\t\talign=\"center\"\n\t\t\t\t\tgap={ 1 }\n\t\t\t\t\twrap={ true }\n\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\thasTokens={ !! value.length }\n\t\t\t\t>\n\t\t\t\t\t{ renderTokensAndInput() }\n\t\t\t\t</TokensAndInputWrapperFlex>\n\t\t\t\t{ isExpanded && (\n\t\t\t\t\t<SuggestionsList\n\t\t\t\t\t\tinstanceId={ instanceId }\n\t\t\t\t\t\tmatch={ saveTransform( incompleteTokenValue ) }\n\t\t\t\t\t\tdisplayTransform={ displayTransform }\n\t\t\t\t\t\tsuggestions={ matchingSuggestions }\n\t\t\t\t\t\tselectedIndex={ selectedSuggestionIndex }\n\t\t\t\t\t\tscrollIntoView={ selectedSuggestionScroll }\n\t\t\t\t\t\tonHover={ onSuggestionHovered }\n\t\t\t\t\t\tonSelect={ onSuggestionSelected }\n\t\t\t\t\t\t__experimentalRenderItem={ __experimentalRenderItem }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t</div>\n\t\t\t{ ! __nextHasNoMarginBottom && <Spacer marginBottom={ 2 } /> }\n\t\t\t{ __experimentalShowHowTo && (\n\t\t\t\t<StyledHelp\n\t\t\t\t\tid={ `components-form-token-suggestions-howto-${ instanceId }` }\n\t\t\t\t\tclassName=\"components-form-token-field__help\"\n\t\t\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t\t>\n\t\t\t\t\t{ tokenizeOnSpace\n\t\t\t\t\t\t? __(\n\t\t\t\t\t\t\t\t'Separate with commas, spaces, or the Enter key.'\n\t\t\t\t\t\t  )\n\t\t\t\t\t\t: __( 'Separate with commas or the Enter key.' ) }\n\t\t\t\t</StyledHelp>\n\t\t\t) }\n\t\t</div>\n\t);\n\t/* eslint-enable jsx-a11y/no-static-element-interactions */\n}\n\nexport default FormTokenField;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,eAAA,GAAAN,sBAAA,CAAAC,OAAA;AAKA,IAAAM,MAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,WAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,gBAAA,GAAAV,sBAAA,CAAAC,OAAA;AAEA,IAAAU,KAAA,GAAAV,OAAA;AACA,IAAAW,kBAAA,GAAAX,OAAA;AAIA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,mBAAA,GAAAb,OAAA;AA7BA;AACA;AACA;;AAIA;AACA;AACA;;AAOA;AACA;AACA;;AAcA,MAAMc,QAAQ,GAAKC,KAAa,IAAMA,KAAK;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,cAAcA,CAAEC,KAA0B,EAAG;EAC5D,MAAM;IACLC,cAAc;IACdC,YAAY;IACZC,SAAS;IACTC,WAAW;IACXC,KAAK,GAAG,IAAAC,QAAE,EAAE,UAAW,CAAC;IACxBC,SAAS;IACTC,WAAW,GAAG,EAAE;IAChBC,cAAc,GAAG,GAAG;IACpBX,KAAK,GAAG,EAAE;IACVY,gBAAgB,GAAGb,QAAQ;IAC3Bc,aAAa,GAAKC,KAAK,IAAMA,KAAK,CAACC,IAAI,CAAC,CAAC;IACzCC,QAAQ,GAAGA,CAAA,KAAM,CAAC,CAAC;IACnBC,aAAa,GAAGA,CAAA,KAAM,CAAC,CAAC;IACxBC,OAAO,GAAGC,SAAS;IACnBC,YAAY,GAAG,KAAK;IACpBC,QAAQ,GAAG,KAAK;IAChBC,eAAe,GAAG,KAAK;IACvBC,QAAQ,GAAG;MACVC,KAAK,EAAE,IAAAhB,QAAE,EAAE,aAAc,CAAC;MAC1BiB,OAAO,EAAE,IAAAjB,QAAE,EAAE,eAAgB,CAAC;MAC9BkB,MAAM,EAAE,IAAAlB,QAAE,EAAE,aAAc,CAAC;MAC3BmB,qBAAqB,EAAE,IAAAnB,QAAE,EAAE,cAAe;IAC3C,CAAC;IACDoB,wBAAwB;IACxBC,2BAA2B,GAAG,KAAK;IACnCC,2BAA2B,GAAGA,CAAA,KAAM,IAAI;IACxCC,uBAAuB,GAAG,IAAI;IAC9BC,qBAAqB,GAAG,KAAK;IAC7BC,kCAAkC,GAAG,KAAK;IAC1CC,uBAAuB,GAAG,KAAK;IAC/BC,cAAc,GAAG;EAClB,CAAC,GAAG,IAAAC,oDAAgC,EACnClC,KAAK,EACL,8BACD,CAAC;EAED,MAAMmC,UAAU,GAAG,IAAAC,sBAAa,EAAErC,cAAe,CAAC;;EAElD;EACA,MAAM,CAAEsC,oBAAoB,EAAEC,uBAAuB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,EAAG,CAAC;EACxE,MAAM,CAAEC,kBAAkB,EAAEC,qBAAqB,CAAE,GAAG,IAAAF,iBAAQ,EAAE,CAAE,CAAC;EACnE,MAAM,CAAEG,QAAQ,EAAEC,WAAW,CAAE,GAAG,IAAAJ,iBAAQ,EAAE,KAAM,CAAC;EACnD,MAAM,CAAEK,UAAU,EAAEC,aAAa,CAAE,GAAG,IAAAN,iBAAQ,EAAE,KAAM,CAAC;EACvD,MAAM,CAAEO,uBAAuB,EAAEC,0BAA0B,CAAE,GAC5D,IAAAR,iBAAQ,EAAE,CAAC,CAAE,CAAC;EACf,MAAM,CAAES,wBAAwB,EAAEC,2BAA2B,CAAE,GAC9D,IAAAV,iBAAQ,EAAE,KAAM,CAAC;EAElB,MAAMW,eAAe,GAAG,IAAAC,oBAAW,EAAc3C,WAAY,CAAC;EAC9D,MAAM4C,SAAS,GAAG,IAAAD,oBAAW,EAA8BrD,KAAM,CAAC;EAElE,MAAMuD,KAAK,GAAG,IAAAC,eAAM,EAAsB,IAAK,CAAC;EAChD,MAAMC,cAAc,GAAG,IAAAD,eAAM,EAAsB,IAAK,CAAC;EAEzD,MAAME,cAAc,GAAG,IAAAC,oBAAW,EAAEC,WAAK,EAAE,GAAI,CAAC;EAEhD,IAAAC,kBAAS,EAAE,MAAM;IAChB;IACA,IAAKjB,QAAQ,IAAI,CAAEkB,QAAQ,CAAC,CAAC,EAAG;MAC/BC,KAAK,CAAC,CAAC;IACR;EACD,CAAC,EAAE,CAAEnB,QAAQ,CAAG,CAAC;EAEjB,IAAAiB,kBAAS,EAAE,MAAM;IAChB,MAAMG,oBAAoB,GAAG,CAAE,IAAAC,uBAAc,EAC5CvD,WAAW,EACX0C,eAAe,IAAI,EACpB,CAAC;IAED,IAAKY,oBAAoB,IAAIhE,KAAK,KAAKsD,SAAS,EAAG;MAClDY,iBAAiB,CAAEF,oBAAqB,CAAC;IAC1C;;IAEA;IACA;EACD,CAAC,EAAE,CAAEtD,WAAW,EAAE0C,eAAe,EAAEpD,KAAK,EAAEsD,SAAS,CAAG,CAAC;EAEvD,IAAAO,kBAAS,EAAE,MAAM;IAChBK,iBAAiB,CAAC,CAAC;IACnB;EACD,CAAC,EAAE,CAAE3B,oBAAoB,CAAG,CAAC;EAE7B,IAAAsB,kBAAS,EAAE,MAAM;IAChBK,iBAAiB,CAAC,CAAC;IACnB;EACD,CAAC,EAAE,CAAEjC,kCAAkC,CAAG,CAAC;EAE3C,IAAKZ,QAAQ,IAAIuB,QAAQ,EAAG;IAC3BC,WAAW,CAAE,KAAM,CAAC;IACpBL,uBAAuB,CAAE,EAAG,CAAC;EAC9B;EAEA,SAASuB,KAAKA,CAAA,EAAG;IAChBR,KAAK,CAACY,OAAO,EAAEJ,KAAK,CAAC,CAAC;EACvB;EAEA,SAASD,QAAQA,CAAA,EAAG;IACnB,OAAOP,KAAK,CAACY,OAAO,KAAKZ,KAAK,CAACY,OAAO,EAAEC,aAAa,CAACC,aAAa;EACpE;EAEA,SAASC,cAAcA,CAAEC,KAAiB,EAAG;IAC5C;IACA,IAAKT,QAAQ,CAAC,CAAC,IAAIS,KAAK,CAACC,MAAM,KAAKf,cAAc,CAACU,OAAO,EAAG;MAC5DtB,WAAW,CAAE,IAAK,CAAC;MACnBE,aAAa,CAAElB,2BAA2B,IAAIiB,UAAW,CAAC;IAC3D,CAAC,MAAM;MACN;AACH;AACA;AACA;AACA;MACGD,WAAW,CAAE,KAAM,CAAC;IACrB;IAEA,IAAK,UAAU,KAAK,OAAO3B,OAAO,EAAG;MACpCA,OAAO,CAAEqD,KAAM,CAAC;IACjB;EACD;EAEA,SAASE,MAAMA,CAAEF,KAAiB,EAAG;IACpC,IACCG,kBAAkB,CAAC,CAAC,IACpB5C,2BAA2B,CAAES,oBAAqB,CAAC,EAClD;MACDM,WAAW,CAAE,KAAM,CAAC;MACpB,IAAKV,cAAc,IAAIuC,kBAAkB,CAAC,CAAC,EAAG;QAC7CC,WAAW,CAAEpC,oBAAqB,CAAC;MACpC;IACD,CAAC,MAAM;MACN;MACAC,uBAAuB,CAAE,EAAG,CAAC;MAC7BG,qBAAqB,CAAE,CAAE,CAAC;MAC1BE,WAAW,CAAE,KAAM,CAAC;MAEpB,IAAKhB,2BAA2B,EAAG;QAClC;QACA;QACA,MAAM+C,cAAc,GACnBL,KAAK,CAACM,aAAa,KAAKpB,cAAc,CAACU,OAAO;QAC/CpB,aAAa,CAAE6B,cAAe,CAAC;MAChC,CAAC,MAAM;QACN;QACA;QACA7B,aAAa,CAAE,KAAM,CAAC;MACvB;MAEAE,0BAA0B,CAAE,CAAC,CAAE,CAAC;MAChCE,2BAA2B,CAAE,KAAM,CAAC;IACrC;EACD;EAEA,SAAS2B,SAASA,CAAEP,KAAoB,EAAG;IAC1C,IAAIQ,cAAc,GAAG,KAAK;IAE1B,IACCR,KAAK,CAACS,gBAAgB;IACtB;IACAT,KAAK,CAACU,WAAW,CAACC,WAAW;IAC7B;IACA;IACA;IACAX,KAAK,CAACY,OAAO,KAAK,GAAG,EACpB;MACD;IACD;IACA,QAASZ,KAAK,CAACa,GAAG;MACjB,KAAK,WAAW;QACfL,cAAc,GAAGM,eAAe,CAAEC,sBAAuB,CAAC;QAC1D;MACD,KAAK,OAAO;QACXP,cAAc,GAAGQ,eAAe,CAAC,CAAC;QAClC;MACD,KAAK,WAAW;QACfR,cAAc,GAAGS,kBAAkB,CAAC,CAAC;QACrC;MACD,KAAK,SAAS;QACbT,cAAc,GAAGU,gBAAgB,CAAC,CAAC;QACnC;MACD,KAAK,YAAY;QAChBV,cAAc,GAAGW,mBAAmB,CAAC,CAAC;QACtC;MACD,KAAK,WAAW;QACfX,cAAc,GAAGY,kBAAkB,CAAC,CAAC;QACrC;MACD,KAAK,QAAQ;QACZZ,cAAc,GAAGM,eAAe,CAAEO,qBAAsB,CAAC;QACzD;MACD,KAAK,OAAO;QACX,IAAKtE,eAAe,EAAG;UACtByD,cAAc,GAAGQ,eAAe,CAAC,CAAC;QACnC;QACA;MACD,KAAK,QAAQ;QACZR,cAAc,GAAGc,eAAe,CAAEtB,KAAM,CAAC;QACzC;MACD;QACC;IACF;IAEA,IAAKQ,cAAc,EAAG;MACrBR,KAAK,CAACQ,cAAc,CAAC,CAAC;IACvB;EACD;EAEA,SAASe,UAAUA,CAAEvB,KAAoB,EAAG;IAC3C,IAAIQ,cAAc,GAAG,KAAK;IAE1B,QAASR,KAAK,CAACa,GAAG;MACjB,KAAK,GAAG;QACPL,cAAc,GAAGgB,cAAc,CAAC,CAAC;QACjC;MACD;QACC;IACF;IAEA,IAAKhB,cAAc,EAAG;MACrBR,KAAK,CAACQ,cAAc,CAAC,CAAC;IACvB;EACD;EAEA,SAASiB,kBAAkBA,CAAEzB,KAA8B,EAAG;IAC7D;IACA;IACA,IAAKA,KAAK,CAACC,MAAM,KAAKf,cAAc,CAACU,OAAO,IAAIvB,QAAQ,EAAG;MAC1D2B,KAAK,CAACQ,cAAc,CAAC,CAAC;IACvB;EACD;EAEA,SAASkB,kBAAkBA,CAAE1B,KAAwB,EAAG;IACvD2B,WAAW,CAAE3B,KAAK,CAACvE,KAAM,CAAC;IAC1B+D,KAAK,CAAC,CAAC;EACR;EAEA,SAASoC,mBAAmBA,CAAEC,UAAkB,EAAG;IAClD,MAAMC,KAAK,GAAGC,sBAAsB,CAAC,CAAC,CAACC,OAAO,CAAEH,UAAW,CAAC;IAE5D,IAAKC,KAAK,IAAI,CAAC,EAAG;MACjBpD,0BAA0B,CAAEoD,KAAM,CAAC;MACnClD,2BAA2B,CAAE,KAAM,CAAC;IACrC;EACD;EAEA,SAASqD,oBAAoBA,CAAEJ,UAAkB,EAAG;IACnDzB,WAAW,CAAEyB,UAAW,CAAC;EAC1B;EAEA,SAASK,oBAAoBA,CAAElC,KAAwB,EAAG;IACzD,MAAMmC,IAAI,GAAGnC,KAAK,CAACvE,KAAK;IACxB,MAAM2G,SAAS,GAAGrF,eAAe,GAAG,SAAS,GAAG,QAAQ;IACxD,MAAMsF,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAEF,SAAU,CAAC;IACrC,MAAMG,UAAU,GAAGF,KAAK,CAAEA,KAAK,CAACG,MAAM,GAAG,CAAC,CAAE,IAAI,EAAE;IAElD,IAAKH,KAAK,CAACG,MAAM,GAAG,CAAC,EAAG;MACvBC,YAAY,CAAEJ,KAAK,CAACK,KAAK,CAAE,CAAC,EAAE,CAAC,CAAE,CAAE,CAAC;IACrC;IACAzE,uBAAuB,CAAEsE,UAAW,CAAC;IACrC7F,aAAa,CAAE6F,UAAW,CAAC;EAC5B;EAEA,SAASzB,eAAeA,CAAE6B,YAAwB,EAAG;IACpD,IAAInC,cAAc,GAAG,KAAK;IAC1B,IAAKjB,QAAQ,CAAC,CAAC,IAAIqD,YAAY,CAAC,CAAC,EAAG;MACnCD,YAAY,CAAC,CAAC;MACdnC,cAAc,GAAG,IAAI;IACtB;IAEA,OAAOA,cAAc;EACtB;EAEA,SAASS,kBAAkBA,CAAA,EAAG;IAC7B,IAAIT,cAAc,GAAG,KAAK;IAC1B,IAAKoC,YAAY,CAAC,CAAC,EAAG;MACrBC,4BAA4B,CAAC,CAAC;MAC9BrC,cAAc,GAAG,IAAI;IACtB;IAEA,OAAOA,cAAc;EACtB;EAEA,SAASW,mBAAmBA,CAAA,EAAG;IAC9B,IAAIX,cAAc,GAAG,KAAK;IAC1B,IAAKoC,YAAY,CAAC,CAAC,EAAG;MACrBE,uBAAuB,CAAC,CAAC;MACzBtC,cAAc,GAAG,IAAI;IACtB;IAEA,OAAOA,cAAc;EACtB;EAEA,SAASU,gBAAgBA,CAAA,EAAG;IAC3BxC,0BAA0B,CAAIoD,KAAK,IAAM;MACxC,OACC,CAAEA,KAAK,KAAK,CAAC,GACVC,sBAAsB,CACtB/D,oBAAoB,EACpB7B,WAAW,EACXV,KAAK,EACLW,cAAc,EACdE,aACA,CAAC,CAACkG,MAAM,GACRV,KAAK,IAAK,CAAC;IAEhB,CAAE,CAAC;IACHlD,2BAA2B,CAAE,IAAK,CAAC;IAEnC,OAAO,IAAI,CAAC,CAAC;EACd;;EAEA,SAASwC,kBAAkBA,CAAA,EAAG;IAC7B1C,0BAA0B,CAAIoD,KAAK,IAAM;MACxC,OACC,CAAEA,KAAK,GAAG,CAAC,IACXC,sBAAsB,CACrB/D,oBAAoB,EACpB7B,WAAW,EACXV,KAAK,EACLW,cAAc,EACdE,aACD,CAAC,CAACkG,MAAM;IAEV,CAAE,CAAC;IAEH5D,2BAA2B,CAAE,IAAK,CAAC;IACnC,OAAO,IAAI,CAAC,CAAC;EACd;;EAEA,SAAS0C,eAAeA,CAAEtB,KAAoB,EAAG;IAChD,IAAKA,KAAK,CAACC,MAAM,YAAY8C,gBAAgB,EAAG;MAC/C9E,uBAAuB,CAAE+B,KAAK,CAACC,MAAM,CAACxE,KAAM,CAAC;MAC7C+C,aAAa,CAAE,KAAM,CAAC;MACtBE,0BAA0B,CAAE,CAAC,CAAE,CAAC;MAChCE,2BAA2B,CAAE,KAAM,CAAC;IACrC;IAEA,OAAO,IAAI,CAAC,CAAC;EACd;;EAEA,SAAS4C,cAAcA,CAAA,EAAG;IACzB,IAAKrB,kBAAkB,CAAC,CAAC,EAAG;MAC3BC,WAAW,CAAEpC,oBAAqB,CAAC;IACpC;IAEA,OAAO,IAAI,CAAC,CAAC;EACd;;EAEA,SAASgF,gBAAgBA,CAAElB,KAAa,EAAG;IAC1C1D,qBAAqB,CAAE3C,KAAK,CAAC+G,MAAM,GAAGS,IAAI,CAACC,GAAG,CAAEpB,KAAK,EAAE,CAAC,CAAE,CAAC,GAAG,CAAE,CAAC;EAClE;EAEA,SAASe,4BAA4BA,CAAA,EAAG;IACvCzE,qBAAqB,CAAI+E,sBAAsB,IAAM;MACpD,OAAOF,IAAI,CAACG,GAAG,CAAED,sBAAsB,GAAG,CAAC,EAAE1H,KAAK,CAAC+G,MAAO,CAAC;IAC5D,CAAE,CAAC;EACJ;EAEA,SAASM,uBAAuBA,CAAA,EAAG;IAClC1E,qBAAqB,CAAI+E,sBAAsB,IAAM;MACpD,OAAOF,IAAI,CAACC,GAAG,CAAEC,sBAAsB,GAAG,CAAC,EAAE,CAAE,CAAC;IACjD,CAAE,CAAC;EACJ;EAEA,SAASpC,sBAAsBA,CAAA,EAAG;IACjC,MAAMe,KAAK,GAAGuB,eAAe,CAAC,CAAC,GAAG,CAAC;IAEnC,IAAKvB,KAAK,GAAG,CAAC,CAAC,EAAG;MACjBH,WAAW,CAAElG,KAAK,CAAEqG,KAAK,CAAG,CAAC;IAC9B;EACD;EAEA,SAAST,qBAAqBA,CAAA,EAAG;IAChC,MAAMS,KAAK,GAAGuB,eAAe,CAAC,CAAC;IAE/B,IAAKvB,KAAK,GAAGrG,KAAK,CAAC+G,MAAM,EAAG;MAC3Bb,WAAW,CAAElG,KAAK,CAAEqG,KAAK,CAAG,CAAC;MAC7B;MACAkB,gBAAgB,CAAElB,KAAM,CAAC;IAC1B;EACD;EAEA,SAASd,eAAeA,CAAA,EAAG;IAC1B,IAAIR,cAAc,GAAG,KAAK;IAC1B,MAAM8C,kBAAkB,GAAGC,qBAAqB,CAAC,CAAC;IAElD,IAAKD,kBAAkB,EAAG;MACzBlD,WAAW,CAAEkD,kBAAmB,CAAC;MACjC9C,cAAc,GAAG,IAAI;IACtB,CAAC,MAAM,IAAKL,kBAAkB,CAAC,CAAC,EAAG;MAClCC,WAAW,CAAEpC,oBAAqB,CAAC;MACnCwC,cAAc,GAAG,IAAI;IACtB;IAEA,OAAOA,cAAc;EACtB;EAEA,SAASiC,YAAYA,CAAEe,MAAgB,EAAG;IACzC,MAAMC,WAAW,GAAG,CACnB,GAAG,IAAIC,GAAG,CACTF,MAAM,CACJG,GAAG,CAAErH,aAAc,CAAC,CACpBsH,MAAM,CAAEC,OAAQ,CAAC,CACjBD,MAAM,CAAIrH,KAAK,IAAM,CAAEuH,kBAAkB,CAAEvH,KAAM,CAAE,CACtD,CAAC,CACD;IAED,IAAKkH,WAAW,CAACjB,MAAM,GAAG,CAAC,EAAG;MAC7B,MAAMuB,QAAQ,GAAG,CAAE,GAAGtI,KAAK,CAAE;MAC7BsI,QAAQ,CAACC,MAAM,CAAEX,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGI,WAAY,CAAC;MACvDhH,QAAQ,CAAEsH,QAAS,CAAC;IACrB;EACD;EAEA,SAAS3D,WAAWA,CAAE7D,KAAa,EAAG;IACrC,IAAK,CAAEgB,2BAA2B,CAAEhB,KAAM,CAAC,EAAG;MAC7C,IAAA8C,WAAK,EAAErC,QAAQ,CAACI,qBAAqB,EAAE,WAAY,CAAC;MACpD;IACD;IACAqF,YAAY,CAAE,CAAElG,KAAK,CAAG,CAAC;IACzB,IAAA8C,WAAK,EAAErC,QAAQ,CAACC,KAAK,EAAE,WAAY,CAAC;IAEpCgB,uBAAuB,CAAE,EAAG,CAAC;IAC7BS,0BAA0B,CAAE,CAAC,CAAE,CAAC;IAChCE,2BAA2B,CAAE,KAAM,CAAC;IACpCJ,aAAa,CAAE,CAAElB,2BAA4B,CAAC;IAE9C,IAAKe,QAAQ,IAAI,CAAET,cAAc,EAAG;MACnC4B,KAAK,CAAC,CAAC;IACR;EACD;EAEA,SAASmC,WAAWA,CAAEpF,KAAyB,EAAG;IACjD,MAAM0H,SAAS,GAAGxI,KAAK,CAACmI,MAAM,CAAIM,IAAI,IAAM;MAC3C,OAAOC,aAAa,CAAED,IAAK,CAAC,KAAKC,aAAa,CAAE5H,KAAM,CAAC;IACxD,CAAE,CAAC;IACHE,QAAQ,CAAEwH,SAAU,CAAC;IACrB,IAAA5E,WAAK,EAAErC,QAAQ,CAACE,OAAO,EAAE,WAAY,CAAC;EACvC;EAEA,SAASiH,aAAaA,CAAE5H,KAAiC,EAAG;IAC3D,IAAK,QAAQ,KAAK,OAAOA,KAAK,EAAG;MAChC,OAAOA,KAAK,CAACd,KAAK;IACnB;IAEA,OAAOc,KAAK;EACb;EAEA,SAASwF,sBAAsBA,CAC9BqC,WAAW,GAAGpG,oBAAoB,EAClCqG,YAAY,GAAGlI,WAAW,EAC1BmI,MAAM,GAAG7I,KAAK,EACd8I,eAAe,GAAGnI,cAAc,EAChCoI,cAAc,GAAGlI,aAAa,EAC7B;IACD,IAAImI,KAAK,GAAGD,cAAc,CAAEJ,WAAY,CAAC;IACzC,MAAMM,eAAyB,GAAG,EAAE;IACpC,MAAMC,aAAuB,GAAG,EAAE;IAClC,MAAMC,eAAe,GAAGN,MAAM,CAACX,GAAG,CAAIO,IAAI,IAAM;MAC/C,IAAK,OAAOA,IAAI,KAAK,QAAQ,EAAG;QAC/B,OAAOA,IAAI;MACZ;MACA,OAAOA,IAAI,CAACzI,KAAK;IAClB,CAAE,CAAC;IAEH,IAAKgJ,KAAK,CAACjC,MAAM,KAAK,CAAC,EAAG;MACzB6B,YAAY,GAAGA,YAAY,CAACT,MAAM,CAC/B/B,UAAU,IAAM,CAAE+C,eAAe,CAACC,QAAQ,CAAEhD,UAAW,CAC1D,CAAC;IACF,CAAC,MAAM;MACN4C,KAAK,GAAGA,KAAK,CAACK,iBAAiB,CAAC,CAAC;MAEjCT,YAAY,CAACU,OAAO,CAAIlD,UAAU,IAAM;QACvC,MAAMC,KAAK,GAAGD,UAAU,CAACiD,iBAAiB,CAAC,CAAC,CAAC9C,OAAO,CAAEyC,KAAM,CAAC;QAC7D,IAAKG,eAAe,CAAC5C,OAAO,CAAEH,UAAW,CAAC,KAAK,CAAC,CAAC,EAAG;UACnD,IAAKC,KAAK,KAAK,CAAC,EAAG;YAClB4C,eAAe,CAACM,IAAI,CAAEnD,UAAW,CAAC;UACnC,CAAC,MAAM,IAAKC,KAAK,GAAG,CAAC,EAAG;YACvB6C,aAAa,CAACK,IAAI,CAAEnD,UAAW,CAAC;UACjC;QACD;MACD,CAAE,CAAC;MAEHwC,YAAY,GAAGK,eAAe,CAACO,MAAM,CAAEN,aAAc,CAAC;IACvD;IAEA,OAAON,YAAY,CAAC3B,KAAK,CAAE,CAAC,EAAE6B,eAAgB,CAAC;EAChD;EAEA,SAAShB,qBAAqBA,CAAA,EAAG;IAChC,IAAK9E,uBAAuB,KAAK,CAAC,CAAC,EAAG;MACrC,OAAOsD,sBAAsB,CAAC,CAAC,CAAEtD,uBAAuB,CAAE;IAC3D;IAEA,OAAO7B,SAAS;EACjB;EAEA,SAASkH,kBAAkBA,CAAEvH,KAAa,EAAG;IAC5C,OAAOd,KAAK,CAACyJ,IAAI,CAAIhB,IAAI,IAAM;MAC9B,OAAOC,aAAa,CAAE5H,KAAM,CAAC,KAAK4H,aAAa,CAAED,IAAK,CAAC;IACxD,CAAE,CAAC;EACJ;EAEA,SAASb,eAAeA,CAAA,EAAG;IAC1B,OAAO5H,KAAK,CAAC+G,MAAM,GAAGrE,kBAAkB;EACzC;EAEA,SAASyE,YAAYA,CAAA,EAAG;IACvB,OAAO5E,oBAAoB,CAACwE,MAAM,KAAK,CAAC;EACzC;EAEA,SAASrC,kBAAkBA,CAAA,EAAG;IAC7B,OAAO7D,aAAa,CAAE0B,oBAAqB,CAAC,CAACwE,MAAM,GAAG,CAAC;EACxD;EAEA,SAAS7C,iBAAiBA,CAAEwF,uBAAuB,GAAG,IAAI,EAAG;IAC5D,MAAMC,oBAAoB,GAAGpH,oBAAoB,CAACxB,IAAI,CAAC,CAAC,CAACgG,MAAM,GAAG,CAAC;IACnE,MAAM6C,mBAAmB,GACxBtD,sBAAsB,CAAE/D,oBAAqB,CAAC;IAC/C,MAAMsH,sBAAsB,GAAGD,mBAAmB,CAAC7C,MAAM,GAAG,CAAC;IAE7D,MAAM+C,qBAAqB,GAAGhG,QAAQ,CAAC,CAAC,IAAIjC,2BAA2B;IACvEkB,aAAa,CACZ+G,qBAAqB,IAClBH,oBAAoB,IAAIE,sBAC5B,CAAC;IAED,IAAKH,uBAAuB,EAAG;MAC9B,IACCzH,kCAAkC,IAClC0H,oBAAoB,IACpBE,sBAAsB,EACrB;QACD5G,0BAA0B,CAAE,CAAE,CAAC;QAC/BE,2BAA2B,CAAE,IAAK,CAAC;MACpC,CAAC,MAAM;QACNF,0BAA0B,CAAE,CAAC,CAAE,CAAC;QAChCE,2BAA2B,CAAE,KAAM,CAAC;MACrC;IACD;IAEA,IAAKwG,oBAAoB,EAAG;MAC3B,MAAMI,OAAO,GAAGF,sBAAsB,GACnC,IAAAG,aAAO,GACP;MACA,IAAAC,QAAE,EACD,0DAA0D,EAC1D,2DAA2D,EAC3DL,mBAAmB,CAAC7C,MACrB,CAAC,EACD6C,mBAAmB,CAAC7C,MACpB,CAAC,GACD,IAAAvG,QAAE,EAAE,aAAc,CAAC;MAEtBkD,cAAc,CAAEqG,OAAO,EAAE,WAAY,CAAC;IACvC;EACD;EAEA,SAASG,oBAAoBA,CAAA,EAAG;IAC/B,MAAMC,UAAU,GAAGnK,KAAK,CAACkI,GAAG,CAAEkC,WAAY,CAAC;IAC3CD,UAAU,CAAC5B,MAAM,CAAEX,eAAe,CAAC,CAAC,EAAE,CAAC,EAAEyC,WAAW,CAAC,CAAE,CAAC;IAExD,OAAOF,UAAU;EAClB;EAEA,SAASC,WAAWA,CACnBtJ,KAAyB,EACzBuF,KAAa,EACb0B,MAAgC,EAC/B;IACD,MAAMc,MAAM,GAAGH,aAAa,CAAE5H,KAAM,CAAC;IACrC,MAAMwJ,MAAM,GAAG,OAAOxJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACwJ,MAAM,GAAGnJ,SAAS;IACnE,MAAMoJ,YAAY,GAAGlE,KAAK,GAAG,CAAC;IAC9B,MAAMmE,UAAU,GAAGzC,MAAM,CAAChB,MAAM;IAEhC,OACC,IAAA0D,MAAA,CAAAC,aAAA,EAAC/K,KAAA,CAAAgL,QAAQ;MAACvF,GAAG,EAAG,QAAQ,GAAGyD;IAAQ,GAClC,IAAA4B,MAAA,CAAAC,aAAA,EAACnL,MAAA,CAAAqL,OAAK;MACL5K,KAAK,EAAG6I,MAAQ;MAChByB,MAAM,EAAGA,MAAQ;MACjBO,KAAK,EACJ,OAAO/J,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC+J,KAAK,GAAG1J,SAC1C;MACDP,gBAAgB,EAAGA,gBAAkB;MACrCkK,aAAa,EAAG7E,kBAAoB;MACpC7E,YAAY,EACT,OAAON,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACM,YAAY,IACjDA,YACA;MACD2J,YAAY,EACX,OAAOjK,KAAK,KAAK,QAAQ,GACtBA,KAAK,CAACiK,YAAY,GAClB5J,SACH;MACD6J,YAAY,EACX,OAAOlK,KAAK,KAAK,QAAQ,GACtBA,KAAK,CAACkK,YAAY,GAClB7J,SACH;MACDE,QAAQ,EAAG,OAAO,KAAKiJ,MAAM,IAAIjJ,QAAU;MAC3CE,QAAQ,EAAGA,QAAU;MACrBiJ,UAAU,EAAGA,UAAY;MACzBD,YAAY,EAAGA;IAAc,CAC7B,CACQ,CAAC;EAEb;EAEA,SAASF,WAAWA,CAAA,EAAG;IACtB,MAAMY,UAAU,GAAG;MAClB5I,UAAU;MACVlC,cAAc;MACdC,YAAY;MACZE,WAAW,EAAEN,KAAK,CAAC+G,MAAM,KAAK,CAAC,GAAGzG,WAAW,GAAG,EAAE;MAClD8E,GAAG,EAAE,OAAO;MACZ/D,QAAQ;MACRrB,KAAK,EAAEuC,oBAAoB;MAC3BkC,MAAM;MACN3B,UAAU;MACVE;IACD,CAAC;IAED,OACC,IAAAyH,MAAA,CAAAC,aAAA,EAAClL,WAAA,CAAAoL,OAAU;MAAA,GACLK,UAAU;MACfjK,QAAQ,EACP,EAAIX,SAAS,IAAIL,KAAK,CAAC+G,MAAM,IAAI1G,SAAS,CAAE,GACzCoG,oBAAoB,GACpBtF,SACH;MACD+J,GAAG,EAAG3H;IAAO,CACb,CAAC;EAEJ;EAEA,MAAM4H,OAAO,GAAG,IAAAC,mBAAU,EACzB3K,SAAS,EACT,8CAA8C,EAC9C;IACC,WAAW,EAAEmC,QAAQ;IACrB,aAAa,EAAEvB;EAChB,CACD,CAAC;EAED,IAAIgK,eAAe,GAAG;IACrB5K,SAAS,EAAE,6BAA6B;IACxC6K,QAAQ,EAAE,CAAC;EACZ,CAAC;EACD,MAAM1B,mBAAmB,GAAGtD,sBAAsB,CAAC,CAAC;EAEpD,IAAK,CAAEjF,QAAQ,EAAG;IACjBgK,eAAe,GAAGE,MAAM,CAACC,MAAM,CAAE,CAAC,CAAC,EAAEH,eAAe,EAAE;MACrDvG,SAAS;MACTgB,UAAU;MACV5E,OAAO,EAAEoD;IACV,CAAE,CAAC;EACJ;;EAEA;EACA;EACA;EACA;EACA,OACC,IAAAmG,MAAA,CAAAC,aAAA;IAAA,GAAUW;EAAe,GACxB,IAAAZ,MAAA,CAAAC,aAAA,EAAC9K,kBAAA,CAAA6L,WAAW;IACXC,OAAO,EAAI,+BAA+BrJ,UAAY,EAAG;IACzD5B,SAAS,EAAC;EAAoC,GAE5CF,KACU,CAAC,EACd,IAAAkK,MAAA,CAAAC,aAAA;IACCQ,GAAG,EAAGzH,cAAgB;IACtBhD,SAAS,EAAG0K,OAAS;IACrBG,QAAQ,EAAG,CAAC,CAAG;IACfK,WAAW,EAAG3F,kBAAoB;IAClC4F,YAAY,EAAG5F;EAAoB,GAEnC,IAAAyE,MAAA,CAAAC,aAAA,EAACjL,OAAA,CAAAoM,yBAAyB;IACzBC,OAAO,EAAC,YAAY;IACpBC,KAAK,EAAC,QAAQ;IACdC,GAAG,EAAG,CAAG;IACTC,IAAI,EAAG,IAAM;IACbjK,qBAAqB,EAAGA,qBAAuB;IAC/CkK,SAAS,EAAG,CAAC,CAAElM,KAAK,CAAC+G;EAAQ,GAE3BmD,oBAAoB,CAAC,CACG,CAAC,EAC1BpH,UAAU,IACX,IAAA2H,MAAA,CAAAC,aAAA,EAAChL,gBAAA,CAAAkL,OAAe;IACfvI,UAAU,EAAGA,UAAY;IACzB2G,KAAK,EAAGnI,aAAa,CAAE0B,oBAAqB,CAAG;IAC/C3B,gBAAgB,EAAGA,gBAAkB;IACrCF,WAAW,EAAGkJ,mBAAqB;IACnCuC,aAAa,EAAGnJ,uBAAyB;IACzCoJ,cAAc,EAAGlJ,wBAA0B;IAC3CmJ,OAAO,EAAGlG,mBAAqB;IAC/BmG,QAAQ,EAAG9F,oBAAsB;IACjC5E,wBAAwB,EAAGA;EAA0B,CACrD,CAEE,CAAC,EACJ,CAAEM,uBAAuB,IAAI,IAAAuI,MAAA,CAAAC,aAAA,EAAC7K,OAAA,CAAA0M,MAAM;IAACC,YAAY,EAAG;EAAG,CAAE,CAAC,EAC1DzK,uBAAuB,IACxB,IAAA0I,MAAA,CAAAC,aAAA,EAAC9K,kBAAA,CAAA6M,UAAU;IACVC,EAAE,EAAI,2CAA2CrK,UAAY,EAAG;IAChE5B,SAAS,EAAC,mCAAmC;IAC7CyB,uBAAuB,EAAGA;EAAyB,GAEjDZ,eAAe,GACd,IAAAd,QAAE,EACF,iDACA,CAAC,GACD,IAAAA,QAAE,EAAE,wCAAyC,CACrC,CAET,CAAC;EAEP;AACD;AAAC,IAAAmM,QAAA,GAEc1M,cAAc;AAAA2M,OAAA,CAAAhC,OAAA,GAAA+B,QAAA"}