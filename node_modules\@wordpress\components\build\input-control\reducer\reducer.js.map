{"version": 3, "names": ["_element", "require", "_state", "actions", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "mergeInitialState", "initialState", "initialInputControlState", "value", "initialValue", "inputControlStateReducer", "composedStateReducers", "state", "action", "nextState", "type", "CONTROL", "payload", "isDirty", "_event", "undefined", "PRESS_UP", "PRESS_DOWN", "DRAG_START", "isDragging", "DRAG_END", "CHANGE", "error", "isPressEnterToChange", "COMMIT", "RESET", "INVALIDATE", "event", "useInputControlStateReducer", "stateReducer", "initialStateReducer", "onChangeHandler", "dispatch", "useReducer", "createChangeEvent", "nextValue", "createKeyEvent", "createDragEvent", "change", "invalidate", "reset", "commit", "dragStart", "drag", "DRAG", "dragEnd", "pressUp", "pressDown", "pressEnter", "PRESS_ENTER", "currentState", "useRef", "refProps", "useLayoutEffect", "current", "_state$value", "_initialState$value"], "sources": ["@wordpress/components/src/input-control/reducer/reducer.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { SyntheticEvent, ChangeEvent, PointerEvent } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useReducer, useLayoutEffect, useRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { InputState, StateReducer } from './state';\nimport { initialInputControlState, initialStateReducer } from './state';\nimport * as actions from './actions';\nimport type { InputChangeCallback } from '../types';\n\n/**\n * Prepares initialState for the reducer.\n *\n * @param initialState The initial state.\n * @return Prepared initialState for the reducer\n */\nfunction mergeInitialState(\n\tinitialState: Partial< InputState > = initialInputControlState\n): InputState {\n\tconst { value } = initialState;\n\n\treturn {\n\t\t...initialInputControlState,\n\t\t...initialState,\n\t\tinitialValue: value,\n\t} as InputState;\n}\n\n/**\n * Creates the base reducer which may be coupled to a specializing reducer.\n * As its final step, for all actions other than CONTROL, the base reducer\n * passes the state and action on through the specializing reducer. The\n * exception for CONTROL actions is because they represent controlled updates\n * from props and no case has yet presented for their specialization.\n *\n * @param composedStateReducers A reducer to specialize state changes.\n * @return The reducer.\n */\nfunction inputControlStateReducer(\n\tcomposedStateReducers: StateReducer\n): StateReducer< actions.ControlAction > {\n\treturn ( state, action ) => {\n\t\tconst nextState = { ...state };\n\n\t\tswitch ( action.type ) {\n\t\t\t/*\n\t\t\t * Controlled updates\n\t\t\t */\n\t\t\tcase actions.CONTROL:\n\t\t\t\tnextState.value = action.payload.value;\n\t\t\t\tnextState.isDirty = false;\n\t\t\t\tnextState._event = undefined;\n\t\t\t\t// Returns immediately to avoid invoking additional reducers.\n\t\t\t\treturn nextState;\n\n\t\t\t/**\n\t\t\t * Keyboard events\n\t\t\t */\n\t\t\tcase actions.PRESS_UP:\n\t\t\t\tnextState.isDirty = false;\n\t\t\t\tbreak;\n\n\t\t\tcase actions.PRESS_DOWN:\n\t\t\t\tnextState.isDirty = false;\n\t\t\t\tbreak;\n\n\t\t\t/**\n\t\t\t * Drag events\n\t\t\t */\n\t\t\tcase actions.DRAG_START:\n\t\t\t\tnextState.isDragging = true;\n\t\t\t\tbreak;\n\n\t\t\tcase actions.DRAG_END:\n\t\t\t\tnextState.isDragging = false;\n\t\t\t\tbreak;\n\n\t\t\t/**\n\t\t\t * Input events\n\t\t\t */\n\t\t\tcase actions.CHANGE:\n\t\t\t\tnextState.error = null;\n\t\t\t\tnextState.value = action.payload.value;\n\n\t\t\t\tif ( state.isPressEnterToChange ) {\n\t\t\t\t\tnextState.isDirty = true;\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase actions.COMMIT:\n\t\t\t\tnextState.value = action.payload.value;\n\t\t\t\tnextState.isDirty = false;\n\t\t\t\tbreak;\n\n\t\t\tcase actions.RESET:\n\t\t\t\tnextState.error = null;\n\t\t\t\tnextState.isDirty = false;\n\t\t\t\tnextState.value = action.payload.value || state.initialValue;\n\t\t\t\tbreak;\n\n\t\t\t/**\n\t\t\t * Validation\n\t\t\t */\n\t\t\tcase actions.INVALIDATE:\n\t\t\t\tnextState.error = action.payload.error;\n\t\t\t\tbreak;\n\t\t}\n\n\t\tnextState._event = action.payload.event;\n\n\t\t/**\n\t\t * Send the nextState + action to the composedReducers via\n\t\t * this \"bridge\" mechanism. This allows external stateReducers\n\t\t * to hook into actions, and modify state if needed.\n\t\t */\n\t\treturn composedStateReducers( nextState, action );\n\t};\n}\n\n/**\n * A custom hook that connects and external stateReducer with an internal\n * reducer. This hook manages the internal state of InputControl.\n * However, by connecting an external stateReducer function, other\n * components can react to actions as well as modify state before it is\n * applied.\n *\n * This technique uses the \"stateReducer\" design pattern:\n * https://kentcdodds.com/blog/the-state-reducer-pattern/\n *\n * @param stateReducer    An external state reducer.\n * @param initialState    The initial state for the reducer.\n * @param onChangeHandler A handler for the onChange event.\n * @return State, dispatch, and a collection of actions.\n */\nexport function useInputControlStateReducer(\n\tstateReducer: StateReducer = initialStateReducer,\n\tinitialState: Partial< InputState > = initialInputControlState,\n\tonChangeHandler: InputChangeCallback\n) {\n\tconst [ state, dispatch ] = useReducer(\n\t\tinputControlStateReducer( stateReducer ),\n\t\tmergeInitialState( initialState )\n\t);\n\n\tconst createChangeEvent =\n\t\t( type: actions.ChangeEventAction[ 'type' ] ) =>\n\t\t(\n\t\t\tnextValue: actions.ChangeEventAction[ 'payload' ][ 'value' ],\n\t\t\tevent: actions.ChangeEventAction[ 'payload' ][ 'event' ]\n\t\t) => {\n\t\t\tdispatch( {\n\t\t\t\ttype,\n\t\t\t\tpayload: { value: nextValue, event },\n\t\t\t} as actions.InputAction );\n\t\t};\n\n\tconst createKeyEvent =\n\t\t( type: actions.KeyEventAction[ 'type' ] ) =>\n\t\t( event: actions.KeyEventAction[ 'payload' ][ 'event' ] ) => {\n\t\t\tdispatch( { type, payload: { event } } );\n\t\t};\n\n\tconst createDragEvent =\n\t\t( type: actions.DragEventAction[ 'type' ] ) =>\n\t\t( payload: actions.DragEventAction[ 'payload' ] ) => {\n\t\t\tdispatch( { type, payload } );\n\t\t};\n\n\t/**\n\t * Actions for the reducer\n\t */\n\tconst change = createChangeEvent( actions.CHANGE );\n\tconst invalidate = ( error: unknown, event: SyntheticEvent ) =>\n\t\tdispatch( { type: actions.INVALIDATE, payload: { error, event } } );\n\tconst reset = createChangeEvent( actions.RESET );\n\tconst commit = createChangeEvent( actions.COMMIT );\n\n\tconst dragStart = createDragEvent( actions.DRAG_START );\n\tconst drag = createDragEvent( actions.DRAG );\n\tconst dragEnd = createDragEvent( actions.DRAG_END );\n\n\tconst pressUp = createKeyEvent( actions.PRESS_UP );\n\tconst pressDown = createKeyEvent( actions.PRESS_DOWN );\n\tconst pressEnter = createKeyEvent( actions.PRESS_ENTER );\n\n\tconst currentState = useRef( state );\n\tconst refProps = useRef( { value: initialState.value, onChangeHandler } );\n\n\t// Freshens refs to props and state so that subsequent effects have access\n\t// to their latest values without their changes causing effect runs.\n\tuseLayoutEffect( () => {\n\t\tcurrentState.current = state;\n\t\trefProps.current = { value: initialState.value, onChangeHandler };\n\t} );\n\n\t// Propagates the latest state through onChange.\n\tuseLayoutEffect( () => {\n\t\tif (\n\t\t\tcurrentState.current._event !== undefined &&\n\t\t\tstate.value !== refProps.current.value &&\n\t\t\t! state.isDirty\n\t\t) {\n\t\t\trefProps.current.onChangeHandler( state.value ?? '', {\n\t\t\t\tevent: currentState.current._event as\n\t\t\t\t\t| ChangeEvent< HTMLInputElement >\n\t\t\t\t\t| PointerEvent< HTMLInputElement >,\n\t\t\t} );\n\t\t}\n\t}, [ state.value, state.isDirty ] );\n\n\t// Updates the state from props.\n\tuseLayoutEffect( () => {\n\t\tif (\n\t\t\tinitialState.value !== currentState.current.value &&\n\t\t\t! currentState.current.isDirty\n\t\t) {\n\t\t\tdispatch( {\n\t\t\t\ttype: actions.CONTROL,\n\t\t\t\tpayload: { value: initialState.value ?? '' },\n\t\t\t} );\n\t\t}\n\t}, [ initialState.value ] );\n\n\treturn {\n\t\tchange,\n\t\tcommit,\n\t\tdispatch,\n\t\tdrag,\n\t\tdragEnd,\n\t\tdragStart,\n\t\tinvalidate,\n\t\tpressDown,\n\t\tpressEnter,\n\t\tpressUp,\n\t\treset,\n\t\tstate,\n\t} as const;\n}\n"], "mappings": ";;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AAMA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,uBAAA,CAAAH,OAAA;AAAqC,SAAAI,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAfrC;AACA;AACA;;AAGA;AACA;AACA;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,iBAAiBA,CACzBC,YAAmC,GAAGC,+BAAwB,EACjD;EACb,MAAM;IAAEC;EAAM,CAAC,GAAGF,YAAY;EAE9B,OAAO;IACN,GAAGC,+BAAwB;IAC3B,GAAGD,YAAY;IACfG,YAAY,EAAED;EACf,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAChCC,qBAAmC,EACK;EACxC,OAAO,CAAEC,KAAK,EAAEC,MAAM,KAAM;IAC3B,MAAMC,SAAS,GAAG;MAAE,GAAGF;IAAM,CAAC;IAE9B,QAASC,MAAM,CAACE,IAAI;MACnB;AACH;AACA;MACG,KAAKlC,OAAO,CAACmC,OAAO;QACnBF,SAAS,CAACN,KAAK,GAAGK,MAAM,CAACI,OAAO,CAACT,KAAK;QACtCM,SAAS,CAACI,OAAO,GAAG,KAAK;QACzBJ,SAAS,CAACK,MAAM,GAAGC,SAAS;QAC5B;QACA,OAAON,SAAS;;MAEjB;AACH;AACA;MACG,KAAKjC,OAAO,CAACwC,QAAQ;QACpBP,SAAS,CAACI,OAAO,GAAG,KAAK;QACzB;MAED,KAAKrC,OAAO,CAACyC,UAAU;QACtBR,SAAS,CAACI,OAAO,GAAG,KAAK;QACzB;;MAED;AACH;AACA;MACG,KAAKrC,OAAO,CAAC0C,UAAU;QACtBT,SAAS,CAACU,UAAU,GAAG,IAAI;QAC3B;MAED,KAAK3C,OAAO,CAAC4C,QAAQ;QACpBX,SAAS,CAACU,UAAU,GAAG,KAAK;QAC5B;;MAED;AACH;AACA;MACG,KAAK3C,OAAO,CAAC6C,MAAM;QAClBZ,SAAS,CAACa,KAAK,GAAG,IAAI;QACtBb,SAAS,CAACN,KAAK,GAAGK,MAAM,CAACI,OAAO,CAACT,KAAK;QAEtC,IAAKI,KAAK,CAACgB,oBAAoB,EAAG;UACjCd,SAAS,CAACI,OAAO,GAAG,IAAI;QACzB;QAEA;MAED,KAAKrC,OAAO,CAACgD,MAAM;QAClBf,SAAS,CAACN,KAAK,GAAGK,MAAM,CAACI,OAAO,CAACT,KAAK;QACtCM,SAAS,CAACI,OAAO,GAAG,KAAK;QACzB;MAED,KAAKrC,OAAO,CAACiD,KAAK;QACjBhB,SAAS,CAACa,KAAK,GAAG,IAAI;QACtBb,SAAS,CAACI,OAAO,GAAG,KAAK;QACzBJ,SAAS,CAACN,KAAK,GAAGK,MAAM,CAACI,OAAO,CAACT,KAAK,IAAII,KAAK,CAACH,YAAY;QAC5D;;MAED;AACH;AACA;MACG,KAAK5B,OAAO,CAACkD,UAAU;QACtBjB,SAAS,CAACa,KAAK,GAAGd,MAAM,CAACI,OAAO,CAACU,KAAK;QACtC;IACF;IAEAb,SAAS,CAACK,MAAM,GAAGN,MAAM,CAACI,OAAO,CAACe,KAAK;;IAEvC;AACF;AACA;AACA;AACA;IACE,OAAOrB,qBAAqB,CAAEG,SAAS,EAAED,MAAO,CAAC;EAClD,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASoB,2BAA2BA,CAC1CC,YAA0B,GAAGC,0BAAmB,EAChD7B,YAAmC,GAAGC,+BAAwB,EAC9D6B,eAAoC,EACnC;EACD,MAAM,CAAExB,KAAK,EAAEyB,QAAQ,CAAE,GAAG,IAAAC,mBAAU,EACrC5B,wBAAwB,CAAEwB,YAAa,CAAC,EACxC7B,iBAAiB,CAAEC,YAAa,CACjC,CAAC;EAED,MAAMiC,iBAAiB,GACpBxB,IAAyC,IAC3C,CACCyB,SAA4D,EAC5DR,KAAwD,KACpD;IACJK,QAAQ,CAAE;MACTtB,IAAI;MACJE,OAAO,EAAE;QAAET,KAAK,EAAEgC,SAAS;QAAER;MAAM;IACpC,CAAyB,CAAC;EAC3B,CAAC;EAEF,MAAMS,cAAc,GACjB1B,IAAsC,IACtCiB,KAAqD,IAAM;IAC5DK,QAAQ,CAAE;MAAEtB,IAAI;MAAEE,OAAO,EAAE;QAAEe;MAAM;IAAE,CAAE,CAAC;EACzC,CAAC;EAEF,MAAMU,eAAe,GAClB3B,IAAuC,IACvCE,OAA6C,IAAM;IACpDoB,QAAQ,CAAE;MAAEtB,IAAI;MAAEE;IAAQ,CAAE,CAAC;EAC9B,CAAC;;EAEF;AACD;AACA;EACC,MAAM0B,MAAM,GAAGJ,iBAAiB,CAAE1D,OAAO,CAAC6C,MAAO,CAAC;EAClD,MAAMkB,UAAU,GAAGA,CAAEjB,KAAc,EAAEK,KAAqB,KACzDK,QAAQ,CAAE;IAAEtB,IAAI,EAAElC,OAAO,CAACkD,UAAU;IAAEd,OAAO,EAAE;MAAEU,KAAK;MAAEK;IAAM;EAAE,CAAE,CAAC;EACpE,MAAMa,KAAK,GAAGN,iBAAiB,CAAE1D,OAAO,CAACiD,KAAM,CAAC;EAChD,MAAMgB,MAAM,GAAGP,iBAAiB,CAAE1D,OAAO,CAACgD,MAAO,CAAC;EAElD,MAAMkB,SAAS,GAAGL,eAAe,CAAE7D,OAAO,CAAC0C,UAAW,CAAC;EACvD,MAAMyB,IAAI,GAAGN,eAAe,CAAE7D,OAAO,CAACoE,IAAK,CAAC;EAC5C,MAAMC,OAAO,GAAGR,eAAe,CAAE7D,OAAO,CAAC4C,QAAS,CAAC;EAEnD,MAAM0B,OAAO,GAAGV,cAAc,CAAE5D,OAAO,CAACwC,QAAS,CAAC;EAClD,MAAM+B,SAAS,GAAGX,cAAc,CAAE5D,OAAO,CAACyC,UAAW,CAAC;EACtD,MAAM+B,UAAU,GAAGZ,cAAc,CAAE5D,OAAO,CAACyE,WAAY,CAAC;EAExD,MAAMC,YAAY,GAAG,IAAAC,eAAM,EAAE5C,KAAM,CAAC;EACpC,MAAM6C,QAAQ,GAAG,IAAAD,eAAM,EAAE;IAAEhD,KAAK,EAAEF,YAAY,CAACE,KAAK;IAAE4B;EAAgB,CAAE,CAAC;;EAEzE;EACA;EACA,IAAAsB,wBAAe,EAAE,MAAM;IACtBH,YAAY,CAACI,OAAO,GAAG/C,KAAK;IAC5B6C,QAAQ,CAACE,OAAO,GAAG;MAAEnD,KAAK,EAAEF,YAAY,CAACE,KAAK;MAAE4B;IAAgB,CAAC;EAClE,CAAE,CAAC;;EAEH;EACA,IAAAsB,wBAAe,EAAE,MAAM;IACtB,IACCH,YAAY,CAACI,OAAO,CAACxC,MAAM,KAAKC,SAAS,IACzCR,KAAK,CAACJ,KAAK,KAAKiD,QAAQ,CAACE,OAAO,CAACnD,KAAK,IACtC,CAAEI,KAAK,CAACM,OAAO,EACd;MAAA,IAAA0C,YAAA;MACDH,QAAQ,CAACE,OAAO,CAACvB,eAAe,EAAAwB,YAAA,GAAEhD,KAAK,CAACJ,KAAK,cAAAoD,YAAA,cAAAA,YAAA,GAAI,EAAE,EAAE;QACpD5B,KAAK,EAAEuB,YAAY,CAACI,OAAO,CAACxC;MAG7B,CAAE,CAAC;IACJ;EACD,CAAC,EAAE,CAAEP,KAAK,CAACJ,KAAK,EAAEI,KAAK,CAACM,OAAO,CAAG,CAAC;;EAEnC;EACA,IAAAwC,wBAAe,EAAE,MAAM;IACtB,IACCpD,YAAY,CAACE,KAAK,KAAK+C,YAAY,CAACI,OAAO,CAACnD,KAAK,IACjD,CAAE+C,YAAY,CAACI,OAAO,CAACzC,OAAO,EAC7B;MAAA,IAAA2C,mBAAA;MACDxB,QAAQ,CAAE;QACTtB,IAAI,EAAElC,OAAO,CAACmC,OAAO;QACrBC,OAAO,EAAE;UAAET,KAAK,GAAAqD,mBAAA,GAAEvD,YAAY,CAACE,KAAK,cAAAqD,mBAAA,cAAAA,mBAAA,GAAI;QAAG;MAC5C,CAAE,CAAC;IACJ;EACD,CAAC,EAAE,CAAEvD,YAAY,CAACE,KAAK,CAAG,CAAC;EAE3B,OAAO;IACNmC,MAAM;IACNG,MAAM;IACNT,QAAQ;IACRW,IAAI;IACJE,OAAO;IACPH,SAAS;IACTH,UAAU;IACVQ,SAAS;IACTC,UAAU;IACVF,OAAO;IACPN,KAAK;IACLjC;EACD,CAAC;AACF"}