"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StyledIcon = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _icons = require("@wordpress/icons");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const StyledIcon = ( /*#__PURE__*/0, _base.default)(_icons.Icon, process.env.NODE_ENV === "production" ? {
  target: "esh4a730"
} : {
  target: "esh4a730",
  label: "StyledIcon"
})(process.env.NODE_ENV === "production" ? {
  name: "rvs7bx",
  styles: "width:1em;height:1em;margin:0;vertical-align:middle;fill:currentColor"
} : {
  name: "rvs7bx",
  styles: "width:1em;height:1em;margin:0;vertical-align:middle;fill:currentColor",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZXh0ZXJuYWwtbGluay9zdHlsZXMvZXh0ZXJuYWwtbGluay1zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBVXdDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZXh0ZXJuYWwtbGluay9zdHlsZXMvZXh0ZXJuYWwtbGluay1zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbi8qKlxuICogV29yZFByZXNzIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBJY29uIH0gZnJvbSAnQHdvcmRwcmVzcy9pY29ucyc7XG5cbmV4cG9ydCBjb25zdCBTdHlsZWRJY29uID0gc3R5bGVkKCBJY29uIClgXG5cdHdpZHRoOiAxZW07XG5cdGhlaWdodDogMWVtO1xuXHRtYXJnaW46IDA7XG5cdHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XG5cdGZpbGw6IGN1cnJlbnRDb2xvcjtcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.StyledIcon = StyledIcon;
//# sourceMappingURL=external-link-styles.js.map