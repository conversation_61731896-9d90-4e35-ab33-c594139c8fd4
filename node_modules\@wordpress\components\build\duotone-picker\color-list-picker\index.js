"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _button = _interopRequireDefault(require("../../button"));
var _colorPalette = _interopRequireDefault(require("../../color-palette"));
var _colorIndicator = _interopRequireDefault(require("../../color-indicator"));
var _icon = _interopRequireDefault(require("../../icon"));
var _hStack = require("../../h-stack");
var _compose = require("@wordpress/compose");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function ColorOption({
  label,
  value,
  colors,
  disableCustomColors,
  enableAlpha,
  onChange
}) {
  const [isOpen, setIsOpen] = (0, _element.useState)(false);
  const idRoot = (0, _compose.useInstanceId)(ColorOption, 'color-list-picker-option');
  const labelId = `${idRoot}__label`;
  const contentId = `${idRoot}__content`;
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_button.default, {
    className: "components-color-list-picker__swatch-button",
    onClick: () => setIsOpen(prev => !prev),
    "aria-expanded": isOpen,
    "aria-controls": contentId
  }, (0, _react.createElement)(_hStack.HStack, {
    justify: "flex-start",
    spacing: 2
  }, value ? (0, _react.createElement)(_colorIndicator.default, {
    colorValue: value,
    className: "components-color-list-picker__swatch-color"
  }) : (0, _react.createElement)(_icon.default, {
    icon: _icons.swatch
  }), (0, _react.createElement)("span", {
    id: labelId
  }, label))), (0, _react.createElement)("div", {
    role: "group",
    id: contentId,
    "aria-labelledby": labelId,
    "aria-hidden": !isOpen
  }, isOpen && (0, _react.createElement)(_colorPalette.default, {
    "aria-label": (0, _i18n.__)('Color options'),
    className: "components-color-list-picker__color-picker",
    colors: colors,
    value: value,
    clearable: false,
    onChange: onChange,
    disableCustomColors: disableCustomColors,
    enableAlpha: enableAlpha
  })));
}
function ColorListPicker({
  colors,
  labels,
  value = [],
  disableCustomColors,
  enableAlpha,
  onChange
}) {
  return (0, _react.createElement)("div", {
    className: "components-color-list-picker"
  }, labels.map((label, index) => (0, _react.createElement)(ColorOption, {
    key: index,
    label: label,
    value: value[index],
    colors: colors,
    disableCustomColors: disableCustomColors,
    enableAlpha: enableAlpha,
    onChange: newColor => {
      const newColors = value.slice();
      newColors[index] = newColor;
      onChange(newColors);
    }
  })));
}
var _default = ColorListPicker;
exports.default = _default;
//# sourceMappingURL=index.js.map