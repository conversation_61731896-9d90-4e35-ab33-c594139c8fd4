{"version": 3, "names": [], "sources": ["@wordpress/components/src/dropdown-menu/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { HTMLAttributes, ReactNode } from 'react';\n/**\n * Internal dependencies\n */\nimport type { ButtonAsButtonProps } from '../button/types';\nimport type { WordPressComponentProps } from '../context';\nimport type { DropdownProps } from '../dropdown/types';\nimport type { Props as IconProps } from '../icon';\nimport type { NavigableMenuProps } from '../navigable-container/types';\n\nexport type DropdownOption = {\n\t/**\n\t * The icon to be shown for the option.\n\t */\n\ticon?: IconProps[ 'icon' ];\n\t/**\n\t * A human-readable title to display for the option.\n\t */\n\ttitle: string;\n\t/**\n\t * Whether or not the option is disabled.\n\t *\n\t * @default false\n\t */\n\tisDisabled?: boolean;\n\t/**\n\t * A callback function to invoke when the option is selected.\n\t */\n\tonClick?: ( event?: React.MouseEvent ) => void;\n\t/**\n\t * Whether or not the control is currently active.\n\t */\n\tisActive?: boolean;\n\t/**\n\t * Text to use for the internal `Button` component's tooltip.\n\t */\n\tlabel?: string;\n\t/**\n\t * The role to apply to the option's HTML element\n\t */\n\trole?: HTMLAttributes< HTMLElement >[ 'role' ];\n};\n\ntype DropdownCallbackProps = {\n\tisOpen: boolean;\n\tonToggle: () => void;\n\tonClose: () => void;\n};\n\n// Manually including `as` prop because `WordPressComponentProps` polymorphism\n// creates a union that is too large for TypeScript to handle.\ntype ToggleProps = Partial<\n\tOmit<\n\t\tWordPressComponentProps< ButtonAsButtonProps, 'button', false >,\n\t\t'label' | 'text'\n\t>\n> & {\n\tas?: React.ElementType | keyof JSX.IntrinsicElements;\n\t'data-toolbar-item'?: boolean;\n};\n\nexport type DropdownMenuProps = {\n\t/**\n\t * The icon to be shown in the collapsed menu button.\n\t *\n\t * @default \"menu\"\n\t */\n\ticon?: IconProps[ 'icon' ] | null;\n\t/**\n\t * A human-readable label to present as accessibility text on the focused\n\t * collapsed menu button.\n\t */\n\tlabel: string;\n\t/**\n\t * A class name to apply to the dropdown menu's toggle element wrapper.\n\t */\n\tclassName?: string;\n\t/**\n\t * Properties of `popoverProps` object will be passed as props to the nested\n\t * `Popover` component.\n\t * Use this object to modify props available for the `Popover` component that\n\t * are not already exposed in the `DropdownMenu` component, e.g.: the\n\t * direction in which the popover should open relative to its parent node\n\t * set with `position` prop.\n\t */\n\tpopoverProps?: DropdownProps[ 'popoverProps' ];\n\t/**\n\t * Properties of `toggleProps` object will be passed as props to the nested\n\t * `Button` component in the `renderToggle` implementation of the `Dropdown`\n\t * component used internally.\n\t * Use this object to modify props available for the `Button` component that\n\t * are not already exposed in the `DropdownMenu` component, e.g.: the tooltip\n\t * text displayed on hover set with `tooltip` prop.\n\t */\n\ttoggleProps?: ToggleProps;\n\t/**\n\t * Properties of `menuProps` object will be passed as props to the nested\n\t * `NavigableMenu` component in the `renderContent` implementation of the\n\t * `Dropdown` component used internally.\n\t * Use this object to modify props available for the `NavigableMenu`\n\t * component that are not already exposed in the `DropdownMenu` component,\n\t * e.g.: the orientation of the menu set with `orientation` prop.\n\t */\n\tmenuProps?: Omit< Partial< NavigableMenuProps >, 'children' >;\n\t/**\n\t * In some contexts, the arrow down key used to open the dropdown menu might\n\t * need to be disabled—for example when that key is used to perform another\n\t * action.\n\t *\n\t * @default false\n\t */\n\tdisableOpenOnArrowDown?: boolean;\n\t/**\n\t * Text to display on the nested `Button` component in the `renderToggle`\n\t * implementation of the `Dropdown` component used internally.\n\t */\n\ttext?: string;\n\t/**\n\t * Whether or not `no-icons` should be added to the menu's `className`.\n\t */\n\tnoIcons?: boolean;\n\t/**\n\t * A [function render prop](https://reactjs.org/docs/render-props.html#using-props-other-than-render)\n\t * which should return an element or elements valid for use in a DropdownMenu:\n\t * `MenuItem`, `MenuItemsChoice`, or `MenuGroup`. Its first argument is a\n\t * props object including the same values as given to a `Dropdown`'s\n\t * `renderContent` (`isOpen`, `onToggle`, `onClose`).\n\t *\n\t * A valid DropdownMenu must specify a `controls` or `children` prop, or both.\n\t */\n\tchildren?: ( callbackProps: DropdownCallbackProps ) => ReactNode;\n\t/**\n\t * An array or nested array of objects describing the options to be shown in\n\t * the expanded menu. Each object should include an `icon` Dashicon slug\n\t * string, a human-readable `title` string, `isDisabled` boolean flag, and\n\t * an `onClick` function callback to invoke when the option is selected.\n\t *\n\t * A valid DropdownMenu must specify a `controls` or `children` prop, or both.\n\t */\n\tcontrols?: DropdownOption[] | DropdownOption[][];\n\t/**\n\t * The controlled open state of the dropdown menu.\n\t * Must be used in conjunction with `onToggle`.\n\t */\n\topen?: boolean;\n\t/**\n\t * The open state of the dropdown menu when initially rendered.\n\t * Use when you do not need to control its open state. It will be overridden\n\t * by the `open` prop if it is specified on the component's first render.\n\t */\n\tdefaultOpen?: boolean;\n\t/**\n\t * A callback invoked when the state of the dropdown menu changes\n\t * from open to closed and vice versa.\n\t */\n\tonToggle?: ( willOpen: boolean ) => void;\n};\n\nexport type DropdownMenuInternalContext = {\n\t/**\n\t * This variant can be used to change the appearance of the component in\n\t * specific contexts, ie. when rendered inside the `Toolbar` component.\n\t */\n\tvariant?: 'toolbar';\n};\n"], "mappings": ""}