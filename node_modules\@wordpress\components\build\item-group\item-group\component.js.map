{"version": 3, "names": ["_context", "require", "_hook", "_context2", "_view", "UnconnectedItemGroup", "props", "forwardedRef", "isBordered", "isSeparated", "size", "sizeProp", "otherProps", "useItemGroup", "contextSize", "useItemGroupContext", "spacedAround", "contextValue", "_react", "createElement", "ItemGroupContext", "Provider", "value", "View", "ref", "ItemGroup", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/item-group/item-group/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useItemGroup } from './hook';\nimport { ItemGroupContext, useItemGroupContext } from '../context';\nimport { View } from '../../view';\nimport type { ItemGroupProps } from '../types';\n\nfunction UnconnectedItemGroup(\n\tprops: WordPressComponentProps< ItemGroupProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst {\n\t\tisBordered,\n\t\tisSeparated,\n\t\tsize: sizeProp,\n\t\t...otherProps\n\t} = useItemGroup( props );\n\n\tconst { size: contextSize } = useItemGroupContext();\n\n\tconst spacedAround = ! isBordered && ! isSeparated;\n\tconst size = sizeProp || contextSize;\n\n\tconst contextValue = {\n\t\tspacedAround,\n\t\tsize,\n\t};\n\n\treturn (\n\t\t<ItemGroupContext.Provider value={ contextValue }>\n\t\t\t<View { ...otherProps } ref={ forwardedRef } />\n\t\t</ItemGroupContext.Provider>\n\t);\n}\n\n/**\n * `ItemGroup` displays a list of `Item`s grouped and styled together.\n *\n * ```jsx\n * import {\n *   __experimentalItemGroup as ItemGroup,\n *   __experimentalItem as Item,\n * } from '@wordpress/components';\n *\n * function Example() {\n *   return (\n *     <ItemGroup>\n *       <Item>Code</Item>\n *       <Item>is</Item>\n *       <Item>Poetry</Item>\n *     </ItemGroup>\n *   );\n * }\n * ```\n */\nexport const ItemGroup = contextConnect( UnconnectedItemGroup, 'ItemGroup' );\n\nexport default ItemGroup;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAQA,SAASI,oBAAoBA,CAC5BC,KAAuD,EACvDC,YAAiC,EAChC;EACD,MAAM;IACLC,UAAU;IACVC,WAAW;IACXC,IAAI,EAAEC,QAAQ;IACd,GAAGC;EACJ,CAAC,GAAG,IAAAC,kBAAY,EAAEP,KAAM,CAAC;EAEzB,MAAM;IAAEI,IAAI,EAAEI;EAAY,CAAC,GAAG,IAAAC,6BAAmB,EAAC,CAAC;EAEnD,MAAMC,YAAY,GAAG,CAAER,UAAU,IAAI,CAAEC,WAAW;EAClD,MAAMC,IAAI,GAAGC,QAAQ,IAAIG,WAAW;EAEpC,MAAMG,YAAY,GAAG;IACpBD,YAAY;IACZN;EACD,CAAC;EAED,OACC,IAAAQ,MAAA,CAAAC,aAAA,EAAChB,SAAA,CAAAiB,gBAAgB,CAACC,QAAQ;IAACC,KAAK,EAAGL;EAAc,GAChD,IAAAC,MAAA,CAAAC,aAAA,EAACf,KAAA,CAAAmB,IAAI;IAAA,GAAMX,UAAU;IAAGY,GAAG,EAAGjB;EAAc,CAAE,CACpB,CAAC;AAE9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMkB,SAAS,GAAG,IAAAC,uBAAc,EAAErB,oBAAoB,EAAE,WAAY,CAAC;AAACsB,OAAA,CAAAF,SAAA,GAAAA,SAAA;AAAA,IAAAG,QAAA,GAE9DH,SAAS;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}