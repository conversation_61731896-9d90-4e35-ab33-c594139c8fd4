!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("react"),require("proxy-compare"),require("use-sync-external-store/shim"),require("valtio/vanilla")):"function"==typeof define&&define.amd?define(["exports","react","proxy-compare","use-sync-external-store/shim","valtio/vanilla"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).valtio={},e.<PERSON><PERSON>,e.proxyCompare,e.useSyncExternalStoreExports,e.valtioVanilla)}(this,(function(e,r,n,t,u){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=o(t).default.useSyncExternalStore;Object.defineProperty(e,"getVersion",{enumerable:!0,get:function(){return u.getVersion}}),Object.defineProperty(e,"proxy",{enumerable:!0,get:function(){return u.proxy}}),Object.defineProperty(e,"ref",{enumerable:!0,get:function(){return u.ref}}),Object.defineProperty(e,"snapshot",{enumerable:!0,get:function(){return u.snapshot}}),Object.defineProperty(e,"subscribe",{enumerable:!0,get:function(){return u.subscribe}}),Object.defineProperty(e,"unstable_buildProxyFunction",{enumerable:!0,get:function(){return u.unstable_buildProxyFunction}}),e.useSnapshot=function(e,t){var o=null==t?void 0:t.sync,a=r.useRef(),c=r.useRef(),s=!0,f=i(r.useCallback((function(r){var n=u.subscribe(e,r,o);return r(),n}),[e,o]),(function(){var r=u.snapshot(e);try{if(!s&&a.current&&c.current&&!n.isChanged(a.current,r,c.current,new WeakMap))return a.current}catch(e){}return r}),(function(){return u.snapshot(e)}));s=!1;var l=new WeakMap;r.useEffect((function(){a.current=f,c.current=l}));var p=r.useMemo((function(){return new WeakMap}),[]);return n.createProxy(f,l,p)},Object.defineProperty(e,"__esModule",{value:!0})}));
