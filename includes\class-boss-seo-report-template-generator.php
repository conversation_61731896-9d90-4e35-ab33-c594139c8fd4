<?php

/**
 * Générateur de templates pour les rapports Boss SEO.
 * 
 * Cette classe génère des templates HTML/PDF professionnels
 * pour les différents types de rapports.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Générateur de templates de rapports.
 *
 * Génère des templates professionnels pour :
 * - Rapports HTML avec CSS intégré
 * - Rapports PDF (via HTML)
 * - Exports CSV structurés
 * - Graphiques et visualisations
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Report_Template_Generator {

    /**
     * Configuration des templates.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $config    Configuration.
     */
    private $config;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     */
    public function __construct() {
        $this->config = array(
            'brand_color' => '#0073aa',
            'success_color' => '#46b450',
            'warning_color' => '#ffb900',
            'danger_color' => '#dc3232',
            'font_family' => 'Arial, sans-serif',
            'logo_url' => '', // À configurer
            'company_name' => get_bloginfo( 'name' )
        );
    }

    /**
     * Génère un rapport HTML complet.
     *
     * @since    1.2.0
     * @param    array     $report_data    Données du rapport.
     * @param    string    $type           Type de rapport.
     * @return   string                    HTML généré.
     */
    public function generate_html_report( $report_data, $type ) {
        $html = $this->get_html_header( $report_data['metadata'] );
        
        switch ( $type ) {
            case 'performance':
                $html .= $this->generate_performance_html( $report_data );
                break;
                
            case 'content':
                $html .= $this->generate_content_html( $report_data );
                break;
                
            case 'technical':
                $html .= $this->generate_technical_html( $report_data );
                break;
                
            default:
                $html .= $this->generate_generic_html( $report_data );
        }
        
        $html .= $this->get_html_footer();
        
        return $html;
    }

    /**
     * Génère l'en-tête HTML.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées du rapport.
     * @return   string               HTML de l'en-tête.
     */
    private function get_html_header( $metadata ) {
        $title = $metadata['title'] ?? 'Rapport Boss SEO';
        $generated_at = $metadata['generated_at'] ?? current_time( 'mysql' );
        $site_name = $metadata['site_name'] ?? get_bloginfo( 'name' );
        
        return '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . esc_html( $title ) . '</title>
    <style>
        ' . $this->get_css_styles() . '
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <div class="header-content">
                <div class="logo-section">
                    <h1 class="site-name">' . esc_html( $site_name ) . '</h1>
                    <p class="report-subtitle">Rapport SEO Professionnel</p>
                </div>
                <div class="meta-section">
                    <p class="generated-date">Généré le ' . date( 'd/m/Y à H:i', strtotime( $generated_at ) ) . '</p>
                    <p class="report-type">' . esc_html( $title ) . '</p>
                </div>
            </div>
        </header>
        
        <main class="report-content">';
    }

    /**
     * Génère le pied de page HTML.
     *
     * @since    1.2.0
     * @return   string    HTML du pied de page.
     */
    private function get_html_footer() {
        return '
        </main>
        
        <footer class="report-footer">
            <div class="footer-content">
                <p>Rapport généré par <strong>Boss SEO</strong> - Plugin WordPress professionnel</p>
                <p>Pour plus d\'informations : <a href="https://boss-seo.com">boss-seo.com</a></p>
            </div>
        </footer>
    </div>
</body>
</html>';
    }

    /**
     * Génère les styles CSS.
     *
     * @since    1.2.0
     * @return   string    CSS complet.
     */
    private function get_css_styles() {
        return '
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: ' . $this->config['font_family'] . ';
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .report-header {
            background: linear-gradient(135deg, ' . $this->config['brand_color'] . ', #005a87);
            color: white;
            padding: 30px;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .site-name {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .meta-section {
            text-align: right;
        }
        
        .generated-date {
            font-size: 1.1em;
            margin-bottom: 5px;
        }
        
        .report-type {
            font-size: 1.3em;
            font-weight: bold;
        }
        
        .report-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 30px;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid ' . $this->config['brand_color'] . ';
        }
        
        .section-title {
            font-size: 1.8em;
            color: ' . $this->config['brand_color'] . ';
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .metric-label {
            font-size: 1.1em;
            color: #666;
        }
        
        .metric-excellent { color: ' . $this->config['success_color'] . '; }
        .metric-good { color: ' . $this->config['brand_color'] . '; }
        .metric-warning { color: ' . $this->config['warning_color'] . '; }
        .metric-danger { color: ' . $this->config['danger_color'] . '; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, ' . $this->config['brand_color'] . ', ' . $this->config['success_color'] . ');
            transition: width 0.3s ease;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .data-table th {
            background: ' . $this->config['brand_color'] . ';
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }
        
        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .insight-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .insight-title {
            font-weight: bold;
            color: ' . $this->config['brand_color'] . ';
            margin-bottom: 10px;
        }
        
        .recommendation-list {
            list-style: none;
            padding: 0;
        }
        
        .recommendation-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid ' . $this->config['warning_color'] . ';
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .recommendation-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .priority-high { border-left-color: ' . $this->config['danger_color'] . '; }
        .priority-medium { border-left-color: ' . $this->config['warning_color'] . '; }
        .priority-low { border-left-color: ' . $this->config['success_color'] . '; }
        
        .report-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #666;
        }
        
        .footer-content a {
            color: ' . $this->config['brand_color'] . ';
            text-decoration: none;
        }
        
        @media print {
            .report-container {
                box-shadow: none;
            }
            
            .report-header {
                background: ' . $this->config['brand_color'] . ' !important;
            }
        }
        ';
    }

    /**
     * Génère le HTML pour un rapport de performance.
     *
     * @since    1.2.0
     * @param    array    $report_data    Données du rapport.
     * @return   string                   HTML généré.
     */
    private function generate_performance_html( $report_data ) {
        $html = '';
        
        // Résumé exécutif
        if ( isset( $report_data['executive_summary'] ) ) {
            $html .= $this->generate_executive_summary_section( $report_data['executive_summary'] );
        }
        
        // Statistiques globales
        if ( isset( $report_data['global_statistics'] ) ) {
            $html .= $this->generate_global_statistics_section( $report_data['global_statistics'] );
        }
        
        // Analyse des pages
        if ( isset( $report_data['pages_analysis'] ) ) {
            $html .= $this->generate_pages_analysis_section( $report_data['pages_analysis'] );
        }
        
        // Performance technique
        if ( isset( $report_data['technical_performance'] ) ) {
            $html .= $this->generate_technical_performance_section( $report_data['technical_performance'] );
        }
        
        // Recommandations
        if ( isset( $report_data['recommendations'] ) ) {
            $html .= $this->generate_recommendations_section( $report_data['recommendations'] );
        }
        
        // Plan d'action
        if ( isset( $report_data['action_plan'] ) ) {
            $html .= $this->generate_action_plan_section( $report_data['action_plan'] );
        }
        
        return $html;
    }

    /**
     * Génère la section résumé exécutif.
     *
     * @since    1.2.0
     * @param    array    $summary    Données du résumé.
     * @return   string               HTML de la section.
     */
    private function generate_executive_summary_section( $summary ) {
        $performance_level = $summary['performance_level'];
        $level_class = 'metric-' . $performance_level['level'];
        
        $html = '<section class="section">
            <h2 class="section-title">📊 Résumé Exécutif</h2>
            
            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-value ' . $level_class . '">' . $summary['average_score'] . '</div>
                    <div class="metric-label">Score SEO Moyen</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ' . $summary['average_score'] . '%"></div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value metric-good">' . $summary['total_content'] . '</div>
                    <div class="metric-label">Total du Contenu</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value metric-excellent">' . $summary['optimized_content'] . '</div>
                    <div class="metric-label">Contenu Optimisé</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value ' . ( $summary['optimization_rate'] > 50 ? 'metric-good' : 'metric-warning' ) . '">' . $summary['optimization_rate'] . '%</div>
                    <div class="metric-label">Taux d\'Optimisation</div>
                </div>
            </div>
            
            <div class="insight-box">
                <div class="insight-title">🎯 Niveau de Performance : ' . $performance_level['label'] . '</div>
                <p>' . $performance_level['description'] . '</p>
            </div>';
        
        // Insights clés
        if ( ! empty( $summary['key_insights'] ) ) {
            $html .= '<div class="insight-box">
                <div class="insight-title">💡 Insights Clés</div>
                <ul>';
            foreach ( $summary['key_insights'] as $insight ) {
                $html .= '<li>' . esc_html( $insight ) . '</li>';
            }
            $html .= '</ul></div>';
        }
        
        $html .= '</section>';

        return $html;
    }

    /**
     * Génère la section statistiques globales.
     *
     * @since    1.2.0
     * @param    array    $stats    Statistiques globales.
     * @return   string             HTML de la section.
     */
    private function generate_global_statistics_section( $stats ) {
        $html = '<section class="section">
            <h2 class="section-title">📈 Statistiques Globales</h2>';

        // Répartition par type de contenu
        if ( isset( $stats['contentTypes'] ) ) {
            $html .= '<h3>Répartition du Contenu</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Type de Contenu</th>
                            <th>Nombre</th>
                            <th>Pourcentage</th>
                        </tr>
                    </thead>
                    <tbody>';

            $total = array_sum( $stats['contentTypes'] );
            foreach ( $stats['contentTypes'] as $type => $count ) {
                $percentage = $total > 0 ? round( ( $count / $total ) * 100, 1 ) : 0;
                $html .= '<tr>
                    <td>' . ucfirst( $type ) . '</td>
                    <td>' . $count . '</td>
                    <td>' . $percentage . '%</td>
                </tr>';
            }

            $html .= '</tbody></table>';
        }

        // Analyse des erreurs
        if ( isset( $stats['error_analysis'] ) ) {
            $html .= '<h3>Erreurs Communes</h3>
                <div class="metric-grid">';

            foreach ( $stats['error_analysis'] as $error ) {
                $severity_class = 'metric-' . ( $error['severity'] ?? 'warning' );
                $html .= '<div class="metric-card">
                    <div class="metric-value ' . $severity_class . '">' . $error['count'] . '</div>
                    <div class="metric-label">' . esc_html( $error['type'] ) . '</div>
                </div>';
            }

            $html .= '</div>';
        }

        $html .= '</section>';

        return $html;
    }

    /**
     * Génère la section analyse des pages.
     *
     * @since    1.2.0
     * @param    array    $pages_analysis    Analyse des pages.
     * @return   string                      HTML de la section.
     */
    private function generate_pages_analysis_section( $pages_analysis ) {
        $html = '<section class="section">
            <h2 class="section-title">📄 Analyse des Pages</h2>';

        // Top performers
        if ( isset( $pages_analysis['top_performers']['pages'] ) && ! empty( $pages_analysis['top_performers']['pages'] ) ) {
            $html .= '<h3>🏆 Pages les Plus Performantes</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Titre</th>
                            <th>Type</th>
                            <th>Score SEO</th>
                            <th>URL</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ( $pages_analysis['top_performers']['pages'] as $page ) {
                $score_class = $page['score'] >= 80 ? 'metric-excellent' : 'metric-good';
                $html .= '<tr>
                    <td>' . esc_html( $page['title'] ) . '</td>
                    <td>' . ucfirst( $page['type'] ) . '</td>
                    <td><span class="' . $score_class . '">' . $page['score'] . '</span></td>
                    <td><a href="' . esc_url( $page['url'] ) . '" target="_blank">Voir</a></td>
                </tr>';
            }

            $html .= '</tbody></table>';
        }

        // Underperformers
        if ( isset( $pages_analysis['underperformers']['pages'] ) && ! empty( $pages_analysis['underperformers']['pages'] ) ) {
            $html .= '<h3>⚠️ Pages à Améliorer</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Titre</th>
                            <th>Type</th>
                            <th>Score SEO</th>
                            <th>Potentiel</th>
                        </tr>
                    </thead>
                    <tbody>';

            foreach ( $pages_analysis['underperformers']['pages'] as $page ) {
                $score_class = $page['score'] < 40 ? 'metric-danger' : 'metric-warning';
                $potential = min( $page['score'] + 30, 90 );
                $html .= '<tr>
                    <td>' . esc_html( $page['title'] ) . '</td>
                    <td>' . ucfirst( $page['type'] ) . '</td>
                    <td><span class="' . $score_class . '">' . $page['score'] . '</span></td>
                    <td><span class="metric-good">+' . ( $potential - $page['score'] ) . '</span></td>
                </tr>';
            }

            $html .= '</tbody></table>';
        }

        $html .= '</section>';

        return $html;
    }

    /**
     * Génère la section performance technique.
     *
     * @since    1.2.0
     * @param    array    $technical    Performance technique.
     * @return   string                 HTML de la section.
     */
    private function generate_technical_performance_section( $technical ) {
        $html = '<section class="section">
            <h2 class="section-title">⚙️ Performance Technique</h2>';

        if ( $technical['pagespeed_available'] ) {
            // Scores PageSpeed
            if ( isset( $technical['pagespeed_scores'] ) ) {
                $html .= '<h3>Scores Google PageSpeed</h3>
                    <div class="metric-grid">';

                $scores = $technical['pagespeed_scores'];
                foreach ( $scores as $metric => $score ) {
                    $score_class = $score >= 80 ? 'metric-excellent' : ( $score >= 60 ? 'metric-good' : 'metric-warning' );
                    $html .= '<div class="metric-card">
                        <div class="metric-value ' . $score_class . '">' . $score . '</div>
                        <div class="metric-label">' . ucfirst( str_replace( '_', ' ', $metric ) ) . '</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ' . $score . '%"></div>
                        </div>
                    </div>';
                }

                $html .= '</div>';
            }

            // Core Web Vitals
            if ( isset( $technical['core_web_vitals'] ) ) {
                $html .= '<h3>Core Web Vitals</h3>
                    <div class="metric-grid">';

                $vitals = $technical['core_web_vitals'];
                foreach ( $vitals as $vital => $data ) {
                    $status_class = $data['status'] === 'good' ? 'metric-excellent' : 'metric-warning';
                    $html .= '<div class="metric-card">
                        <div class="metric-value ' . $status_class . '">' . $data['value'] . '</div>
                        <div class="metric-label">' . strtoupper( $vital ) . '</div>
                    </div>';
                }

                $html .= '</div>';
            }
        } else {
            $html .= '<div class="insight-box">
                <div class="insight-title">⚠️ API Google PageSpeed non configurée</div>
                <p>Configurez l\'API Google PageSpeed dans les paramètres pour obtenir des données de performance détaillées.</p>
            </div>';
        }

        $html .= '</section>';

        return $html;
    }

    /**
     * Génère la section recommandations.
     *
     * @since    1.2.0
     * @param    array    $recommendations    Recommandations.
     * @return   string                       HTML de la section.
     */
    private function generate_recommendations_section( $recommendations ) {
        $html = '<section class="section">
            <h2 class="section-title">💡 Recommandations</h2>
            <ul class="recommendation-list">';

        foreach ( $recommendations as $recommendation ) {
            $priority_class = 'priority-' . ( $recommendation['priority'] ?? 'medium' );
            $html .= '<li class="recommendation-item ' . $priority_class . '">
                <div class="recommendation-title">' . esc_html( $recommendation['title'] ) . '</div>
                <p>' . esc_html( $recommendation['description'] ) . '</p>';

            if ( isset( $recommendation['actions'] ) && ! empty( $recommendation['actions'] ) ) {
                $html .= '<ul>';
                foreach ( $recommendation['actions'] as $action ) {
                    $html .= '<li>' . esc_html( $action ) . '</li>';
                }
                $html .= '</ul>';
            }

            $html .= '</li>';
        }

        $html .= '</ul></section>';

        return $html;
    }

    /**
     * Génère la section plan d'action.
     *
     * @since    1.2.0
     * @param    array    $action_plan    Plan d'action.
     * @return   string                   HTML de la section.
     */
    private function generate_action_plan_section( $action_plan ) {
        $html = '<section class="section">
            <h2 class="section-title">🎯 Plan d\'Action</h2>';

        // Actions immédiates
        if ( isset( $action_plan['immediate_actions'] ) && ! empty( $action_plan['immediate_actions'] ) ) {
            $html .= '<h3>🚨 Actions Immédiates (0-7 jours)</h3>
                <ul class="recommendation-list">';

            foreach ( $action_plan['immediate_actions'] as $action ) {
                $html .= '<li class="recommendation-item priority-high">
                    <div class="recommendation-title">' . esc_html( $action['task'] ) . '</div>
                    <p>' . esc_html( $action['description'] ) . '</p>
                    <p><strong>Temps estimé :</strong> ' . esc_html( $action['estimated_time'] ) . '</p>
                </li>';
            }

            $html .= '</ul>';
        }

        // Objectifs à court terme
        if ( isset( $action_plan['short_term_goals'] ) && ! empty( $action_plan['short_term_goals'] ) ) {
            $html .= '<h3>📅 Objectifs à Court Terme (1-4 semaines)</h3>
                <ul class="recommendation-list">';

            foreach ( $action_plan['short_term_goals'] as $goal ) {
                $html .= '<li class="recommendation-item priority-medium">
                    <div class="recommendation-title">' . esc_html( $goal['goal'] ) . '</div>
                    <p><strong>Cible :</strong> ' . esc_html( $goal['target'] ) . '</p>
                    <p><strong>Délai :</strong> ' . esc_html( $goal['timeline'] ) . '</p>';

                if ( isset( $goal['key_actions'] ) ) {
                    $html .= '<ul>';
                    foreach ( $goal['key_actions'] as $action ) {
                        $html .= '<li>' . esc_html( $action ) . '</li>';
                    }
                    $html .= '</ul>';
                }

                $html .= '</li>';
            }

            $html .= '</ul>';
        }

        $html .= '</section>';

        return $html;
    }

    /**
     * Génère un rapport de contenu HTML.
     *
     * @since    1.2.0
     * @param    array    $report_data    Données du rapport.
     * @return   string                   HTML généré.
     */
    private function generate_content_html( $report_data ) {
        // À implémenter pour les rapports de contenu
        return '<section class="section">
            <h2 class="section-title">📝 Rapport de Contenu</h2>
            <p>Rapport de contenu en cours de développement...</p>
        </section>';
    }

    /**
     * Génère un rapport technique HTML.
     *
     * @since    1.2.0
     * @param    array    $report_data    Données du rapport.
     * @return   string                   HTML généré.
     */
    private function generate_technical_html( $report_data ) {
        // À implémenter pour les rapports techniques
        return '<section class="section">
            <h2 class="section-title">⚙️ Rapport Technique</h2>
            <p>Rapport technique en cours de développement...</p>
        </section>';
    }

    /**
     * Génère un rapport générique HTML.
     *
     * @since    1.2.0
     * @param    array    $report_data    Données du rapport.
     * @return   string                   HTML généré.
     */
    private function generate_generic_html( $report_data ) {
        return '<section class="section">
            <h2 class="section-title">📊 Rapport SEO</h2>
            <p>Rapport généré avec succès.</p>
            <pre>' . esc_html( print_r( $report_data, true ) ) . '</pre>
        </section>';
    }
}
