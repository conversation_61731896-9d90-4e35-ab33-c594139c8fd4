"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.CardHeader = void 0;
var _react = require("react");
var _context = require("../../context");
var _flex = require("../../flex");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedCardHeader(props, forwardedRef) {
  const headerProps = (0, _hook.useCardHeader)(props);
  return (0, _react.createElement)(_flex.Flex, {
    ...headerProps,
    ref: forwardedRef
  });
}

/**
 * `CardHeader` renders an optional header within a `Card`.
 *
 * ```jsx
 * import { Card, CardBody, CardHeader } from `@wordpress/components`;
 *
 * <Card>
 * 	<CardHeader>...</CardHeader>
 * 	<CardBody>...</CardBody>
 * </Card>
 * ```
 */
const CardHeader = (0, _context.contextConnect)(UnconnectedCardHeader, 'CardHeader');
exports.CardHeader = CardHeader;
var _default = CardHeader;
exports.default = _default;
//# sourceMappingURL=component.js.map