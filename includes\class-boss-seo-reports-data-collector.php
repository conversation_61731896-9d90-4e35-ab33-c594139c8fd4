<?php

/**
 * Collecteur de données pour les rapports Boss SEO.
 * 
 * Cette classe collecte les données depuis les modules existants
 * sans les modifier (lecture seule).
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Collecteur de données pour les rapports.
 *
 * Collecte les données depuis :
 * - Boss Optimizer (analyse SEO)
 * - Audit IA (statistiques globales)
 * - Analyse Technique (PageSpeed)
 * - Base WordPress (posts, pages, etc.)
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Reports_Data_Collector {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * Instance de l'analyse Boss Optimizer.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_Analysis    $optimizer_analysis    Instance d'analyse.
     */
    private $optimizer_analysis;

    /**
     * Instance de l'API Audit IA.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Audit_API    $audit_ia_api    Instance Audit IA.
     */
    private $audit_ia_api;

    /**
     * Cache des données collectées.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $cache    Cache des données.
     */
    private $cache = array();

    /**
     * Durée du cache en secondes (1 heure).
     *
     * @since    1.2.0
     * @access   private
     * @var      int    $cache_duration    Durée du cache.
     */
    private $cache_duration = 3600;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    string                    $plugin_name         Le nom du plugin.
     * @param    Boss_Optimizer_Analysis   $optimizer_analysis  Instance d'analyse.
     * @param    Boss_SEO_Audit_API        $audit_ia_api        Instance Audit IA.
     */
    public function __construct( $plugin_name, $optimizer_analysis, $audit_ia_api ) {
        $this->plugin_name = $plugin_name;
        $this->optimizer_analysis = $optimizer_analysis;
        $this->audit_ia_api = $audit_ia_api;
    }

    /**
     * Collecte les données pour le rapport de performance SEO.
     *
     * @since    1.2.0
     * @return   array    Données de performance.
     */
    public function get_performance_data() {
        $cache_key = 'performance_data';
        
        if ( $cached = $this->get_cached_data( $cache_key ) ) {
            return $cached;
        }

        try {
            // Collecter les statistiques globales depuis l'Audit IA
            $global_stats = $this->get_global_statistics();
            
            // Collecter les données de performance technique
            $technical_data = $this->get_technical_performance_data();
            
            // Collecter les top/bottom pages
            $pages_performance = $this->get_pages_performance();

            $performance_data = array(
                'global_stats' => $global_stats,
                'technical_performance' => $technical_data,
                'pages_performance' => $pages_performance,
                'generated_at' => current_time( 'mysql' ),
                'period' => array(
                    'start' => date( 'Y-m-d', strtotime( '-30 days' ) ),
                    'end' => date( 'Y-m-d' )
                )
            );

            $this->set_cached_data( $cache_key, $performance_data );
            return $performance_data;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Reports - Erreur collecte performance: ' . $e->getMessage() );
            return $this->get_fallback_performance_data();
        }
    }

    /**
     * Collecte les données pour le rapport d'audit de contenu.
     *
     * @since    1.2.0
     * @return   array    Données d'audit de contenu.
     */
    public function get_content_audit_data() {
        $cache_key = 'content_audit_data';
        
        if ( $cached = $this->get_cached_data( $cache_key ) ) {
            return $cached;
        }

        try {
            // Analyser tous les posts et pages
            $content_analysis = $this->analyze_all_content();
            
            // Statistiques des méta-données
            $metadata_stats = $this->get_metadata_statistics();
            
            // Analyse de la qualité du contenu
            $content_quality = $this->get_content_quality_analysis();

            $content_data = array(
                'content_analysis' => $content_analysis,
                'metadata_stats' => $metadata_stats,
                'content_quality' => $content_quality,
                'generated_at' => current_time( 'mysql' )
            );

            $this->set_cached_data( $cache_key, $content_data );
            return $content_data;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Reports - Erreur collecte contenu: ' . $e->getMessage() );
            return $this->get_fallback_content_data();
        }
    }

    /**
     * Collecte les données pour le rapport technique.
     *
     * @since    1.2.0
     * @return   array    Données techniques.
     */
    public function get_technical_audit_data() {
        $cache_key = 'technical_audit_data';
        
        if ( $cached = $this->get_cached_data( $cache_key ) ) {
            return $cached;
        }

        try {
            // Données PageSpeed depuis l'analyse technique
            $pagespeed_data = $this->get_pagespeed_insights_data();
            
            // Erreurs techniques depuis l'Audit IA
            $technical_errors = $this->get_technical_errors();
            
            // Informations WordPress
            $wp_info = $this->get_wordpress_info();

            $technical_data = array(
                'pagespeed' => $pagespeed_data,
                'technical_errors' => $technical_errors,
                'wordpress_info' => $wp_info,
                'generated_at' => current_time( 'mysql' )
            );

            $this->set_cached_data( $cache_key, $technical_data );
            return $technical_data;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Reports - Erreur collecte technique: ' . $e->getMessage() );
            return $this->get_fallback_technical_data();
        }
    }

    /**
     * Collecte les statistiques globales depuis l'Audit IA.
     *
     * @since    1.2.0
     * @return   array    Statistiques globales.
     */
    private function get_global_statistics() {
        // Utiliser l'API Audit IA existante (lecture seule)
        $request = new WP_REST_Request( 'GET', '/boss-seo/v1/audit/global-stats' );
        $response = $this->audit_ia_api->get_global_stats( $request );
        
        if ( is_wp_error( $response ) ) {
            return $this->get_fallback_global_stats();
        }
        
        return $response->get_data();
    }

    /**
     * Analyse tous les contenus (posts, pages, produits).
     *
     * @since    1.2.0
     * @return   array    Analyse du contenu.
     */
    private function analyze_all_content() {
        $post_types = array( 'post', 'page' );
        
        // Ajouter les produits WooCommerce si disponible
        if ( class_exists( 'WooCommerce' ) ) {
            $post_types[] = 'product';
        }

        $analysis = array();
        
        foreach ( $post_types as $post_type ) {
            $posts = get_posts( array(
                'post_type' => $post_type,
                'post_status' => 'publish',
                'numberposts' => -1,
                'fields' => 'ids'
            ) );

            $type_analysis = array(
                'total' => count( $posts ),
                'optimized' => 0,
                'needs_improvement' => 0,
                'poor' => 0,
                'average_score' => 0,
                'issues' => array()
            );

            $total_score = 0;
            
            foreach ( $posts as $post_id ) {
                // Utiliser Boss Optimizer pour analyser chaque post (lecture seule)
                $post_analysis = $this->optimizer_analysis->analyze_post( $post_id );
                
                if ( $post_analysis && isset( $post_analysis['score'] ) ) {
                    $score = $post_analysis['score'];
                    $total_score += $score;
                    
                    if ( $score >= 80 ) {
                        $type_analysis['optimized']++;
                    } elseif ( $score >= 60 ) {
                        $type_analysis['needs_improvement']++;
                    } else {
                        $type_analysis['poor']++;
                    }
                    
                    // Collecter les problèmes fréquents
                    if ( isset( $post_analysis['issues'] ) ) {
                        foreach ( $post_analysis['issues'] as $issue ) {
                            $issue_key = $issue['type'] ?? 'unknown';
                            if ( ! isset( $type_analysis['issues'][ $issue_key ] ) ) {
                                $type_analysis['issues'][ $issue_key ] = 0;
                            }
                            $type_analysis['issues'][ $issue_key ]++;
                        }
                    }
                }
            }
            
            if ( $type_analysis['total'] > 0 ) {
                $type_analysis['average_score'] = round( $total_score / $type_analysis['total'], 1 );
            }
            
            $analysis[ $post_type ] = $type_analysis;
        }

        return $analysis;
    }

    /**
     * Récupère les données depuis le cache.
     *
     * @since    1.2.0
     * @param    string    $key    Clé du cache.
     * @return   mixed             Données en cache ou false.
     */
    private function get_cached_data( $key ) {
        $cache_key = 'boss_seo_reports_' . $key;
        return get_transient( $cache_key );
    }

    /**
     * Stocke les données en cache.
     *
     * @since    1.2.0
     * @param    string    $key     Clé du cache.
     * @param    mixed     $data    Données à cacher.
     */
    private function set_cached_data( $key, $data ) {
        $cache_key = 'boss_seo_reports_' . $key;
        set_transient( $cache_key, $data, $this->cache_duration );
    }

    /**
     * Récupère les données de performance technique.
     *
     * @since    1.2.0
     * @return   array    Données de performance technique.
     */
    private function get_technical_performance_data() {
        // Essayer de récupérer les données PageSpeed depuis l'analyse technique
        $homepage_url = home_url();

        // Simuler l'appel à l'analyse technique (à adapter selon l'implémentation)
        $pagespeed_data = array(
            'performance_score' => 0,
            'seo_score' => 0,
            'accessibility_score' => 0,
            'best_practices_score' => 0,
            'core_web_vitals' => array(
                'lcp' => 'N/A',
                'fid' => 'N/A',
                'cls' => 'N/A'
            )
        );

        // Essayer de récupérer les vraies données si l'API PageSpeed est configurée
        $external_services_settings = get_option( 'boss_seo_external_services', array() );
        if ( ! empty( $external_services_settings['google_pagespeed_api_key'] ) ) {
            // Les données PageSpeed seront récupérées par l'analyse technique existante
            $pagespeed_data['available'] = true;
        } else {
            $pagespeed_data['available'] = false;
            $pagespeed_data['message'] = 'API Google PageSpeed non configurée';
        }

        return $pagespeed_data;
    }

    /**
     * Récupère les performances des pages.
     *
     * @since    1.2.0
     * @return   array    Performances des pages.
     */
    private function get_pages_performance() {
        $posts = get_posts( array(
            'post_type' => array( 'post', 'page' ),
            'post_status' => 'publish',
            'numberposts' => 20,
            'orderby' => 'date',
            'order' => 'DESC'
        ) );

        $top_pages = array();
        $bottom_pages = array();

        foreach ( $posts as $post ) {
            $analysis = $this->optimizer_analysis->analyze_post( $post->ID );
            $score = $analysis['score'] ?? 0;

            $page_data = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'url' => get_permalink( $post->ID ),
                'score' => $score,
                'type' => $post->post_type
            );

            if ( $score >= 80 ) {
                $top_pages[] = $page_data;
            } elseif ( $score < 50 ) {
                $bottom_pages[] = $page_data;
            }
        }

        // Trier et limiter
        usort( $top_pages, function( $a, $b ) { return $b['score'] - $a['score']; } );
        usort( $bottom_pages, function( $a, $b ) { return $a['score'] - $b['score']; } );

        return array(
            'top_pages' => array_slice( $top_pages, 0, 10 ),
            'bottom_pages' => array_slice( $bottom_pages, 0, 10 )
        );
    }

    /**
     * Récupère les statistiques des méta-données.
     *
     * @since    1.2.0
     * @return   array    Statistiques des méta-données.
     */
    private function get_metadata_statistics() {
        $post_types = array( 'post', 'page' );
        if ( class_exists( 'WooCommerce' ) ) {
            $post_types[] = 'product';
        }

        $stats = array(
            'missing_titles' => 0,
            'missing_descriptions' => 0,
            'missing_keywords' => 0,
            'total_content' => 0
        );

        foreach ( $post_types as $post_type ) {
            $posts = get_posts( array(
                'post_type' => $post_type,
                'post_status' => 'publish',
                'numberposts' => -1,
                'fields' => 'ids'
            ) );

            $stats['total_content'] += count( $posts );

            foreach ( $posts as $post_id ) {
                $seo_title = get_post_meta( $post_id, '_boss_seo_title', true );
                $seo_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
                $seo_keywords = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );

                if ( empty( $seo_title ) ) {
                    $stats['missing_titles']++;
                }
                if ( empty( $seo_description ) ) {
                    $stats['missing_descriptions']++;
                }
                if ( empty( $seo_keywords ) ) {
                    $stats['missing_keywords']++;
                }
            }
        }

        return $stats;
    }

    /**
     * Récupère l'analyse de la qualité du contenu.
     *
     * @since    1.2.0
     * @return   array    Analyse de la qualité.
     */
    private function get_content_quality_analysis() {
        $post_types = array( 'post', 'page' );
        $quality_stats = array(
            'average_word_count' => 0,
            'short_content' => 0,
            'good_content' => 0,
            'excellent_content' => 0,
            'total_analyzed' => 0
        );

        foreach ( $post_types as $post_type ) {
            $posts = get_posts( array(
                'post_type' => $post_type,
                'post_status' => 'publish',
                'numberposts' => 100, // Limiter pour les performances
                'fields' => 'ids'
            ) );

            $total_words = 0;

            foreach ( $posts as $post_id ) {
                $post = get_post( $post_id );
                $word_count = str_word_count( strip_tags( $post->post_content ) );
                $total_words += $word_count;
                $quality_stats['total_analyzed']++;

                if ( $word_count < 300 ) {
                    $quality_stats['short_content']++;
                } elseif ( $word_count >= 1000 ) {
                    $quality_stats['excellent_content']++;
                } else {
                    $quality_stats['good_content']++;
                }
            }

            if ( $quality_stats['total_analyzed'] > 0 ) {
                $quality_stats['average_word_count'] = round( $total_words / $quality_stats['total_analyzed'] );
            }
        }

        return $quality_stats;
    }

    /**
     * Récupère les données PageSpeed Insights.
     *
     * @since    1.2.0
     * @return   array    Données PageSpeed.
     */
    private function get_pagespeed_insights_data() {
        // Utiliser les services externes existants si configurés
        $external_services_settings = get_option( 'boss_seo_external_services', array() );

        if ( empty( $external_services_settings['google_pagespeed_api_key'] ) ) {
            return array(
                'available' => false,
                'message' => 'API Google PageSpeed non configurée'
            );
        }

        // Données de base (à enrichir avec l'API réelle)
        return array(
            'available' => true,
            'homepage_url' => home_url(),
            'last_check' => current_time( 'mysql' ),
            'mobile' => array(
                'performance' => 0,
                'seo' => 0,
                'accessibility' => 0,
                'best_practices' => 0
            ),
            'desktop' => array(
                'performance' => 0,
                'seo' => 0,
                'accessibility' => 0,
                'best_practices' => 0
            )
        );
    }

    /**
     * Récupère les erreurs techniques.
     *
     * @since    1.2.0
     * @return   array    Erreurs techniques.
     */
    private function get_technical_errors() {
        // Utiliser l'API Audit IA pour récupérer les erreurs techniques
        try {
            $request = new WP_REST_Request( 'GET', '/boss-seo/v1/audit/global-stats' );
            $response = $this->audit_ia_api->get_global_stats( $request );

            if ( ! is_wp_error( $response ) ) {
                $data = $response->get_data();
                return $data['commonErrors'] ?? array();
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO Reports - Erreur récupération erreurs techniques: ' . $e->getMessage() );
        }

        return array();
    }

    /**
     * Récupère les informations WordPress.
     *
     * @since    1.2.0
     * @return   array    Informations WordPress.
     */
    private function get_wordpress_info() {
        global $wp_version;

        return array(
            'wp_version' => $wp_version,
            'php_version' => PHP_VERSION,
            'theme' => get_template(),
            'active_plugins' => count( get_option( 'active_plugins', array() ) ),
            'total_posts' => wp_count_posts()->publish,
            'total_pages' => wp_count_posts( 'page' )->publish,
            'total_media' => wp_count_attachments()
        );
    }

    /**
     * Données de fallback pour la performance.
     *
     * @since    1.2.0
     * @return   array    Données de fallback.
     */
    private function get_fallback_performance_data() {
        return array(
            'global_stats' => $this->get_fallback_global_stats(),
            'technical_performance' => array( 'available' => false ),
            'pages_performance' => array( 'top_pages' => array(), 'bottom_pages' => array() ),
            'generated_at' => current_time( 'mysql' ),
            'error' => true
        );
    }

    /**
     * Données de fallback pour le contenu.
     *
     * @since    1.2.0
     * @return   array    Données de fallback.
     */
    private function get_fallback_content_data() {
        return array(
            'content_analysis' => array(),
            'metadata_stats' => array( 'total_content' => 0 ),
            'content_quality' => array( 'total_analyzed' => 0 ),
            'generated_at' => current_time( 'mysql' ),
            'error' => true
        );
    }

    /**
     * Données de fallback pour le technique.
     *
     * @since    1.2.0
     * @return   array    Données de fallback.
     */
    private function get_fallback_technical_data() {
        return array(
            'pagespeed' => array( 'available' => false ),
            'technical_errors' => array(),
            'wordpress_info' => $this->get_wordpress_info(),
            'generated_at' => current_time( 'mysql' ),
            'error' => true
        );
    }

    /**
     * Statistiques globales de fallback.
     *
     * @since    1.2.0
     * @return   array    Statistiques de fallback.
     */
    private function get_fallback_global_stats() {
        return array(
            'totalContent' => wp_count_posts()->publish + wp_count_posts( 'page' )->publish,
            'optimizedContent' => 0,
            'averageScore' => 0,
            'contentTypes' => array(
                'posts' => wp_count_posts()->publish,
                'pages' => wp_count_posts( 'page' )->publish
            ),
            'commonErrors' => array(),
            'criticalIssues' => 0
        );
    }

    /**
     * Vide le cache des rapports.
     *
     * @since    1.2.0
     */
    public function clear_cache() {
        global $wpdb;

        $wpdb->query(
            "DELETE FROM {$wpdb->options}
             WHERE option_name LIKE '_transient_boss_seo_reports_%'
             OR option_name LIKE '_transient_timeout_boss_seo_reports_%'"
        );
    }
}
