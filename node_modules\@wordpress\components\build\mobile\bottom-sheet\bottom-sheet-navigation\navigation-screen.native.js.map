{"version": 3, "names": ["_native", "require", "_reactNative", "_components", "_element", "_bottomSheetNavigationContext", "_styles", "_interopRequireDefault", "BottomSheetNavigationScreen", "children", "fullScreen", "isScrollable", "isNested", "name", "navigation", "useNavigation", "maxHeight", "useRef", "isFocused", "useIsFocused", "onHandleHardwareButtonPress", "shouldEnableBottomSheetMaxHeight", "setIsFullScreen", "listProps", "safeAreaBottomInset", "useContext", "BottomSheetContext", "height", "windowHeight", "useWindowDimensions", "setHeight", "BottomSheetNavigationContext", "useFocusEffect", "useCallback", "canGoBack", "goBack", "current", "onLayout", "nativeEvent", "layout", "useMemo", "_react", "createElement", "View", "testID", "ScrollView", "TouchableHighlight", "accessible", "style", "styles", "scrollableContent", "paddingBottom", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/bottom-sheet-navigation/navigation-screen.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tuseIsFocused,\n\tuseNavigation,\n\tuseFocusEffect,\n} from '@react-navigation/native';\nimport {\n\tScrollView,\n\tTouchableHighlight,\n\tuseWindowDimensions,\n\tView,\n} from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { BottomSheetContext } from '@wordpress/components';\nimport { useRef, useCallback, useContext, useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport { BottomSheetNavigationContext } from './bottom-sheet-navigation-context';\nimport styles from './styles.scss';\n\nconst BottomSheetNavigationScreen = ( {\n\tchildren,\n\tfullScreen,\n\tisScrollable,\n\tisNested,\n\tname,\n} ) => {\n\tconst navigation = useNavigation();\n\tconst maxHeight = useRef( 0 );\n\tconst isFocused = useIsFocused();\n\tconst {\n\t\tonHandleHardwareButtonPress,\n\t\tshouldEnableBottomSheetMaxHeight,\n\t\tsetIsFullScreen,\n\t\tlistProps,\n\t\tsafeAreaBottomInset,\n\t} = useContext( BottomSheetContext );\n\tconst { height: windowHeight } = useWindowDimensions();\n\n\tconst { setHeight } = useContext( BottomSheetNavigationContext );\n\n\tuseFocusEffect(\n\t\tuseCallback( () => {\n\t\t\tonHandleHardwareButtonPress( () => {\n\t\t\t\tif ( navigation.canGoBack() ) {\n\t\t\t\t\tshouldEnableBottomSheetMaxHeight( true );\n\t\t\t\t\tnavigation.goBack();\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tonHandleHardwareButtonPress( null );\n\t\t\t\treturn false;\n\t\t\t} );\n\t\t\t/**\n\t\t\t * TODO: onHandleHardwareButtonPress stores a single value, which means\n\t\t\t * future invocations from sibling screens can replace the callback for\n\t\t\t * the currently active screen. Currently, the empty dependency array\n\t\t\t * passed to useCallback here is what prevents erroneous callback\n\t\t\t * replacements, but leveraging memoization to achieve this is brittle and\n\t\t\t * explicitly discouraged in the React documentation.\n\t\t\t * https://reactjs.org/docs/hooks-reference.html#usememo\n\t\t\t *\n\t\t\t * Ideally, we refactor onHandleHardwareButtonPress to manage multiple\n\t\t\t * callbacks triggered based upon which screen is currently active.\n\t\t\t *\n\t\t\t * Related: https://github.com/WordPress/gutenberg/pull/36328#discussion_r768897546\n\t\t\t */\n\t\t\t// see https://github.com/WordPress/gutenberg/pull/41166\n\t\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t\t}, [] )\n\t);\n\n\tuseFocusEffect(\n\t\tuseCallback( () => {\n\t\t\tif ( fullScreen ) {\n\t\t\t\tsetHeight( windowHeight );\n\t\t\t\tsetIsFullScreen( true );\n\t\t\t} else if ( maxHeight.current !== 0 ) {\n\t\t\t\tsetIsFullScreen( false );\n\t\t\t\tsetHeight( maxHeight.current );\n\t\t\t}\n\t\t\treturn () => {};\n\t\t}, [ fullScreen, setHeight, setIsFullScreen, windowHeight ] )\n\t);\n\n\tconst onLayout = ( { nativeEvent } ) => {\n\t\tif ( fullScreen ) {\n\t\t\treturn;\n\t\t}\n\t\tconst { height } = nativeEvent.layout;\n\t\tif ( maxHeight.current !== height && isFocused ) {\n\t\t\tmaxHeight.current = height;\n\t\t\tsetHeight( height );\n\t\t}\n\t};\n\n\treturn useMemo( () => {\n\t\treturn isScrollable || isNested ? (\n\t\t\t<View\n\t\t\t\tonLayout={ onLayout }\n\t\t\t\ttestID={ `navigation-screen-${ name }` }\n\t\t\t>\n\t\t\t\t{ children }\n\t\t\t</View>\n\t\t) : (\n\t\t\t<ScrollView { ...listProps }>\n\t\t\t\t<TouchableHighlight accessible={ false }>\n\t\t\t\t\t<View\n\t\t\t\t\t\tonLayout={ onLayout }\n\t\t\t\t\t\ttestID={ `navigation-screen-${ name }` }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ children }\n\t\t\t\t\t\t{ ! isNested && (\n\t\t\t\t\t\t\t<View\n\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\theight:\n\t\t\t\t\t\t\t\t\t\tsafeAreaBottomInset ||\n\t\t\t\t\t\t\t\t\t\tstyles.scrollableContent.paddingBottom,\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</View>\n\t\t\t\t</TouchableHighlight>\n\t\t\t</ScrollView>\n\t\t);\n\t\t// Disable reason: deferring this refactor to the native team.\n\t\t// see https://github.com/WordPress/gutenberg/pull/41166\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [\n\t\tchildren,\n\t\tisFocused,\n\t\tsafeAreaBottomInset,\n\t\tlistProps,\n\t\tname,\n\t\tisScrollable,\n\t\tisNested,\n\t\tonLayout,\n\t] );\n};\n\nexport default BottomSheetNavigationScreen;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,6BAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAC,sBAAA,CAAAN,OAAA;AAzBA;AACA;AACA;;AAaA;AACA;AACA;;AAIA;AACA;AACA;;AAIA,MAAMO,2BAA2B,GAAGA,CAAE;EACrCC,QAAQ;EACRC,UAAU;EACVC,YAAY;EACZC,QAAQ;EACRC;AACD,CAAC,KAAM;EACN,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAClC,MAAMC,SAAS,GAAG,IAAAC,eAAM,EAAE,CAAE,CAAC;EAC7B,MAAMC,SAAS,GAAG,IAAAC,oBAAY,EAAC,CAAC;EAChC,MAAM;IACLC,2BAA2B;IAC3BC,gCAAgC;IAChCC,eAAe;IACfC,SAAS;IACTC;EACD,CAAC,GAAG,IAAAC,mBAAU,EAAEC,8BAAmB,CAAC;EACpC,MAAM;IAAEC,MAAM,EAAEC;EAAa,CAAC,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EAEtD,MAAM;IAAEC;EAAU,CAAC,GAAG,IAAAL,mBAAU,EAAEM,0DAA6B,CAAC;EAEhE,IAAAC,sBAAc,EACb,IAAAC,oBAAW,EAAE,MAAM;IAClBb,2BAA2B,CAAE,MAAM;MAClC,IAAKN,UAAU,CAACoB,SAAS,CAAC,CAAC,EAAG;QAC7Bb,gCAAgC,CAAE,IAAK,CAAC;QACxCP,UAAU,CAACqB,MAAM,CAAC,CAAC;QACnB,OAAO,IAAI;MACZ;MACAf,2BAA2B,CAAE,IAAK,CAAC;MACnC,OAAO,KAAK;IACb,CAAE,CAAC;IACH;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACG;IACA;EACD,CAAC,EAAE,EAAG,CACP,CAAC;EAED,IAAAY,sBAAc,EACb,IAAAC,oBAAW,EAAE,MAAM;IAClB,IAAKvB,UAAU,EAAG;MACjBoB,SAAS,CAAEF,YAAa,CAAC;MACzBN,eAAe,CAAE,IAAK,CAAC;IACxB,CAAC,MAAM,IAAKN,SAAS,CAACoB,OAAO,KAAK,CAAC,EAAG;MACrCd,eAAe,CAAE,KAAM,CAAC;MACxBQ,SAAS,CAAEd,SAAS,CAACoB,OAAQ,CAAC;IAC/B;IACA,OAAO,MAAM,CAAC,CAAC;EAChB,CAAC,EAAE,CAAE1B,UAAU,EAAEoB,SAAS,EAAER,eAAe,EAAEM,YAAY,CAAG,CAC7D,CAAC;EAED,MAAMS,QAAQ,GAAGA,CAAE;IAAEC;EAAY,CAAC,KAAM;IACvC,IAAK5B,UAAU,EAAG;MACjB;IACD;IACA,MAAM;MAAEiB;IAAO,CAAC,GAAGW,WAAW,CAACC,MAAM;IACrC,IAAKvB,SAAS,CAACoB,OAAO,KAAKT,MAAM,IAAIT,SAAS,EAAG;MAChDF,SAAS,CAACoB,OAAO,GAAGT,MAAM;MAC1BG,SAAS,CAAEH,MAAO,CAAC;IACpB;EACD,CAAC;EAED,OAAO,IAAAa,gBAAO,EAAE,MAAM;IACrB,OAAO7B,YAAY,IAAIC,QAAQ,GAC9B,IAAA6B,MAAA,CAAAC,aAAA,EAACxC,YAAA,CAAAyC,IAAI;MACJN,QAAQ,EAAGA,QAAU;MACrBO,MAAM,EAAI,qBAAqB/B,IAAM;IAAG,GAEtCJ,QACG,CAAC,GAEP,IAAAgC,MAAA,CAAAC,aAAA,EAACxC,YAAA,CAAA2C,UAAU;MAAA,GAAMtB;IAAS,GACzB,IAAAkB,MAAA,CAAAC,aAAA,EAACxC,YAAA,CAAA4C,kBAAkB;MAACC,UAAU,EAAG;IAAO,GACvC,IAAAN,MAAA,CAAAC,aAAA,EAACxC,YAAA,CAAAyC,IAAI;MACJN,QAAQ,EAAGA,QAAU;MACrBO,MAAM,EAAI,qBAAqB/B,IAAM;IAAG,GAEtCJ,QAAQ,EACR,CAAEG,QAAQ,IACX,IAAA6B,MAAA,CAAAC,aAAA,EAACxC,YAAA,CAAAyC,IAAI;MACJK,KAAK,EAAG;QACPrB,MAAM,EACLH,mBAAmB,IACnByB,eAAM,CAACC,iBAAiB,CAACC;MAC3B;IAAG,CACH,CAEG,CACa,CACT,CACZ;IACD;IACA;IACA;EACD,CAAC,EAAE,CACF1C,QAAQ,EACRS,SAAS,EACTM,mBAAmB,EACnBD,SAAS,EACTV,IAAI,EACJF,YAAY,EACZC,QAAQ,EACRyB,QAAQ,CACP,CAAC;AACJ,CAAC;AAAC,IAAAe,QAAA,GAEa5C,2BAA2B;AAAA6C,OAAA,CAAAC,OAAA,GAAAF,QAAA"}