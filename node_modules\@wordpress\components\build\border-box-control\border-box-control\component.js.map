{"version": 3, "names": ["_i18n", "require", "_element", "_compose", "_borderBoxControlLinkedButton", "_interopRequireDefault", "_borderBoxControlSplitControls", "_borderControl", "_baseControlStyles", "_view", "_visually<PERSON><PERSON>den", "_context", "_hook", "BorderLabel", "props", "label", "hideLabelFromVision", "_react", "createElement", "VisuallyHidden", "as", "StyledLabel", "UnconnectedBorderBoxControl", "forwardedRef", "className", "colors", "disableCustomColors", "disableUnits", "enableAlpha", "enableStyle", "hasMixedBorders", "isLinked", "linkedControlClassName", "linkedValue", "onLinkedChange", "onSplitChange", "popoverPlacement", "popoverOffset", "size", "splitValue", "toggleLinked", "wrapperClassName", "__experimentalIsRenderedInSidebar", "otherProps", "useBorderBoxControl", "popoverAnchor", "setPopoverAnchor", "useState", "popoverProps", "useMemo", "placement", "offset", "anchor", "shift", "undefined", "mergedRef", "useMergeRefs", "View", "ref", "BorderControl", "onChange", "placeholder", "__", "__unstablePopoverProps", "shouldSanitizeBorder", "value", "<PERSON><PERSON><PERSON><PERSON>", "width", "default", "onClick", "BorderBoxControl", "contextConnect", "exports", "_default"], "sources": ["@wordpress/components/src/border-box-control/border-box-control/component.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { useMemo, useState } from '@wordpress/element';\nimport { useMergeRefs } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport BorderBoxControlLinkedButton from '../border-box-control-linked-button';\nimport BorderBoxControlSplitControls from '../border-box-control-split-controls';\nimport { BorderControl } from '../../border-control';\nimport { StyledLabel } from '../../base-control/styles/base-control-styles';\nimport { View } from '../../view';\nimport { VisuallyHidden } from '../../visually-hidden';\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useBorderBoxControl } from './hook';\n\nimport type { BorderBoxControlProps } from '../types';\nimport type {\n\tLabelProps,\n\tBorderControlProps,\n} from '../../border-control/types';\n\nconst BorderLabel = ( props: LabelProps ) => {\n\tconst { label, hideLabelFromVision } = props;\n\n\tif ( ! label ) {\n\t\treturn null;\n\t}\n\n\treturn hideLabelFromVision ? (\n\t\t<VisuallyHidden as=\"label\">{ label }</VisuallyHidden>\n\t) : (\n\t\t<StyledLabel>{ label }</StyledLabel>\n\t);\n};\n\nconst UnconnectedBorderBoxControl = (\n\tprops: WordPressComponentProps< BorderBoxControlProps, 'div', false >,\n\tforwardedRef: React.ForwardedRef< any >\n) => {\n\tconst {\n\t\tclassName,\n\t\tcolors,\n\t\tdisableCustomColors,\n\t\tdisableUnits,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\thasMixedBorders,\n\t\thideLabelFromVision,\n\t\tisLinked,\n\t\tlabel,\n\t\tlinkedControlClassName,\n\t\tlinkedValue,\n\t\tonLinkedChange,\n\t\tonSplitChange,\n\t\tpopoverPlacement,\n\t\tpopoverOffset,\n\t\tsize,\n\t\tsplitValue,\n\t\ttoggleLinked,\n\t\twrapperClassName,\n\t\t__experimentalIsRenderedInSidebar,\n\t\t...otherProps\n\t} = useBorderBoxControl( props );\n\n\t// Use internal state instead of a ref to make sure that the component\n\t// re-renders when the popover's anchor updates.\n\tconst [ popoverAnchor, setPopoverAnchor ] = useState< Element | null >(\n\t\tnull\n\t);\n\n\t// Memoize popoverProps to avoid returning a new object every time.\n\tconst popoverProps: BorderControlProps[ '__unstablePopoverProps' ] =\n\t\tuseMemo(\n\t\t\t() =>\n\t\t\t\tpopoverPlacement\n\t\t\t\t\t? {\n\t\t\t\t\t\t\tplacement: popoverPlacement,\n\t\t\t\t\t\t\toffset: popoverOffset,\n\t\t\t\t\t\t\tanchor: popoverAnchor,\n\t\t\t\t\t\t\tshift: true,\n\t\t\t\t\t  }\n\t\t\t\t\t: undefined,\n\t\t\t[ popoverPlacement, popoverOffset, popoverAnchor ]\n\t\t);\n\n\tconst mergedRef = useMergeRefs( [ setPopoverAnchor, forwardedRef ] );\n\treturn (\n\t\t<View className={ className } { ...otherProps } ref={ mergedRef }>\n\t\t\t<BorderLabel\n\t\t\t\tlabel={ label }\n\t\t\t\thideLabelFromVision={ hideLabelFromVision }\n\t\t\t/>\n\t\t\t<View className={ wrapperClassName }>\n\t\t\t\t{ isLinked ? (\n\t\t\t\t\t<BorderControl\n\t\t\t\t\t\tclassName={ linkedControlClassName }\n\t\t\t\t\t\tcolors={ colors }\n\t\t\t\t\t\tdisableUnits={ disableUnits }\n\t\t\t\t\t\tdisableCustomColors={ disableCustomColors }\n\t\t\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\t\t\tenableStyle={ enableStyle }\n\t\t\t\t\t\tonChange={ onLinkedChange }\n\t\t\t\t\t\tplaceholder={\n\t\t\t\t\t\t\thasMixedBorders ? __( 'Mixed' ) : undefined\n\t\t\t\t\t\t}\n\t\t\t\t\t\t__unstablePopoverProps={ popoverProps }\n\t\t\t\t\t\tshouldSanitizeBorder={ false } // This component will handle that.\n\t\t\t\t\t\tvalue={ linkedValue }\n\t\t\t\t\t\twithSlider={ true }\n\t\t\t\t\t\twidth={\n\t\t\t\t\t\t\tsize === '__unstable-large' ? '116px' : '110px'\n\t\t\t\t\t\t}\n\t\t\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t\t}\n\t\t\t\t\t\tsize={ size }\n\t\t\t\t\t/>\n\t\t\t\t) : (\n\t\t\t\t\t<BorderBoxControlSplitControls\n\t\t\t\t\t\tcolors={ colors }\n\t\t\t\t\t\tdisableCustomColors={ disableCustomColors }\n\t\t\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\t\t\tenableStyle={ enableStyle }\n\t\t\t\t\t\tonChange={ onSplitChange }\n\t\t\t\t\t\tpopoverPlacement={ popoverPlacement }\n\t\t\t\t\t\tpopoverOffset={ popoverOffset }\n\t\t\t\t\t\tvalue={ splitValue }\n\t\t\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t\t}\n\t\t\t\t\t\tsize={ size }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t\t<BorderBoxControlLinkedButton\n\t\t\t\t\tonClick={ toggleLinked }\n\t\t\t\t\tisLinked={ isLinked }\n\t\t\t\t\tsize={ size }\n\t\t\t\t/>\n\t\t\t</View>\n\t\t</View>\n\t);\n};\n\n/**\n * The `BorderBoxControl` effectively has two view states. The first, a \"linked\"\n * view, allows configuration of a flat border via a single `BorderControl`.\n * The second, a \"split\" view, contains a `BorderControl` for each side\n * as well as a visualizer for the currently selected borders. Each view also\n * contains a button to toggle between the two.\n *\n * When switching from the \"split\" view to \"linked\", if the individual side\n * borders are not consistent, the \"linked\" view will display any border\n * properties selections that are consistent while showing a mixed state for\n * those that aren't. For example, if all borders had the same color and style\n * but different widths, then the border dropdown in the \"linked\" view's\n * `BorderControl` would show that consistent color and style but the \"linked\"\n * view's width input would show \"Mixed\" placeholder text.\n *\n * ```jsx\n * import { __experimentalBorderBoxControl as BorderBoxControl } from '@wordpress/components';\n * import { __ } from '@wordpress/i18n';\n *\n * const colors = [\n * \t{ name: 'Blue 20', color: '#72aee6' },\n * \t// ...\n * ];\n *\n * const MyBorderBoxControl = () => {\n * \tconst defaultBorder = {\n * \t\tcolor: '#72aee6',\n * \t\tstyle: 'dashed',\n * \t\twidth: '1px',\n * \t};\n * \tconst [ borders, setBorders ] = useState( {\n * \t\ttop: defaultBorder,\n * \t\tright: defaultBorder,\n * \t\tbottom: defaultBorder,\n * \t\tleft: defaultBorder,\n * \t} );\n * \tconst onChange = ( newBorders ) => setBorders( newBorders );\n *\n * \treturn (\n * \t\t<BorderBoxControl\n * \t\t\tcolors={ colors }\n * \t\t\tlabel={ __( 'Borders' ) }\n * \t\t\tonChange={ onChange }\n * \t\t\tvalue={ borders }\n * \t\t/>\n * \t);\n * };\n * ```\n */\nexport const BorderBoxControl = contextConnect(\n\tUnconnectedBorderBoxControl,\n\t'BorderBoxControl'\n);\n\nexport default BorderBoxControl;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,6BAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,8BAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,cAAA,GAAAN,OAAA;AACA,IAAAO,kBAAA,GAAAP,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,eAAA,GAAAT,OAAA;AAEA,IAAAU,QAAA,GAAAV,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AAlBA;AACA;AACA;;AAKA;AACA;AACA;;AAiBA,MAAMY,WAAW,GAAKC,KAAiB,IAAM;EAC5C,MAAM;IAAEC,KAAK;IAAEC;EAAoB,CAAC,GAAGF,KAAK;EAE5C,IAAK,CAAEC,KAAK,EAAG;IACd,OAAO,IAAI;EACZ;EAEA,OAAOC,mBAAmB,GACzB,IAAAC,MAAA,CAAAC,aAAA,EAACR,eAAA,CAAAS,cAAc;IAACC,EAAE,EAAC;EAAO,GAAGL,KAAuB,CAAC,GAErD,IAAAE,MAAA,CAAAC,aAAA,EAACV,kBAAA,CAAAa,WAAW,QAAGN,KAAoB,CACnC;AACF,CAAC;AAED,MAAMO,2BAA2B,GAAGA,CACnCR,KAAqE,EACrES,YAAuC,KACnC;EACJ,MAAM;IACLC,SAAS;IACTC,MAAM;IACNC,mBAAmB;IACnBC,YAAY;IACZC,WAAW;IACXC,WAAW;IACXC,eAAe;IACfd,mBAAmB;IACnBe,QAAQ;IACRhB,KAAK;IACLiB,sBAAsB;IACtBC,WAAW;IACXC,cAAc;IACdC,aAAa;IACbC,gBAAgB;IAChBC,aAAa;IACbC,IAAI;IACJC,UAAU;IACVC,YAAY;IACZC,gBAAgB;IAChBC,iCAAiC;IACjC,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAmB,EAAE9B,KAAM,CAAC;;EAEhC;EACA;EACA,MAAM,CAAE+B,aAAa,EAAEC,gBAAgB,CAAE,GAAG,IAAAC,iBAAQ,EACnD,IACD,CAAC;;EAED;EACA,MAAMC,YAA4D,GACjE,IAAAC,gBAAO,EACN,MACCb,gBAAgB,GACb;IACAc,SAAS,EAAEd,gBAAgB;IAC3Be,MAAM,EAAEd,aAAa;IACrBe,MAAM,EAAEP,aAAa;IACrBQ,KAAK,EAAE;EACP,CAAC,GACDC,SAAS,EACb,CAAElB,gBAAgB,EAAEC,aAAa,EAAEQ,aAAa,CACjD,CAAC;EAEF,MAAMU,SAAS,GAAG,IAAAC,qBAAY,EAAE,CAAEV,gBAAgB,EAAEvB,YAAY,CAAG,CAAC;EACpE,OACC,IAAAN,MAAA,CAAAC,aAAA,EAACT,KAAA,CAAAgD,IAAI;IAACjC,SAAS,EAAGA,SAAW;IAAA,GAAMmB,UAAU;IAAGe,GAAG,EAAGH;EAAW,GAChE,IAAAtC,MAAA,CAAAC,aAAA,EAACL,WAAW;IACXE,KAAK,EAAGA,KAAO;IACfC,mBAAmB,EAAGA;EAAqB,CAC3C,CAAC,EACF,IAAAC,MAAA,CAAAC,aAAA,EAACT,KAAA,CAAAgD,IAAI;IAACjC,SAAS,EAAGiB;EAAkB,GACjCV,QAAQ,GACT,IAAAd,MAAA,CAAAC,aAAA,EAACX,cAAA,CAAAoD,aAAa;IACbnC,SAAS,EAAGQ,sBAAwB;IACpCP,MAAM,EAAGA,MAAQ;IACjBE,YAAY,EAAGA,YAAc;IAC7BD,mBAAmB,EAAGA,mBAAqB;IAC3CE,WAAW,EAAGA,WAAa;IAC3BC,WAAW,EAAGA,WAAa;IAC3B+B,QAAQ,EAAG1B,cAAgB;IAC3B2B,WAAW,EACV/B,eAAe,GAAG,IAAAgC,QAAE,EAAE,OAAQ,CAAC,GAAGR,SAClC;IACDS,sBAAsB,EAAGf,YAAc;IACvCgB,oBAAoB,EAAG,KAAO,CAAC;IAAA;IAC/BC,KAAK,EAAGhC,WAAa;IACrBiC,UAAU,EAAG,IAAM;IACnBC,KAAK,EACJ7B,IAAI,KAAK,kBAAkB,GAAG,OAAO,GAAG,OACxC;IACDI,iCAAiC,EAChCA,iCACA;IACDJ,IAAI,EAAGA;EAAM,CACb,CAAC,GAEF,IAAArB,MAAA,CAAAC,aAAA,EAACZ,8BAAA,CAAA8D,OAA6B;IAC7B3C,MAAM,EAAGA,MAAQ;IACjBC,mBAAmB,EAAGA,mBAAqB;IAC3CE,WAAW,EAAGA,WAAa;IAC3BC,WAAW,EAAGA,WAAa;IAC3B+B,QAAQ,EAAGzB,aAAe;IAC1BC,gBAAgB,EAAGA,gBAAkB;IACrCC,aAAa,EAAGA,aAAe;IAC/B4B,KAAK,EAAG1B,UAAY;IACpBG,iCAAiC,EAChCA,iCACA;IACDJ,IAAI,EAAGA;EAAM,CACb,CACD,EACD,IAAArB,MAAA,CAAAC,aAAA,EAACd,6BAAA,CAAAgE,OAA4B;IAC5BC,OAAO,EAAG7B,YAAc;IACxBT,QAAQ,EAAGA,QAAU;IACrBO,IAAI,EAAGA;EAAM,CACb,CACI,CACD,CAAC;AAET,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMgC,gBAAgB,GAAG,IAAAC,uBAAc,EAC7CjD,2BAA2B,EAC3B,kBACD,CAAC;AAACkD,OAAA,CAAAF,gBAAA,GAAAA,gBAAA;AAAA,IAAAG,QAAA,GAEaH,gBAAgB;AAAAE,OAAA,CAAAJ,OAAA,GAAAK,QAAA"}