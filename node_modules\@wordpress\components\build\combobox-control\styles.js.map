{"version": 3, "names": ["_react", "require", "_flex", "_space", "deprecatedDefaultSize", "__next40pxDefaultSize", "css", "space", "process", "env", "NODE_ENV", "InputWrapperFlex", "_base", "default", "Flex", "target", "label", "exports"], "sources": ["@wordpress/components/src/combobox-control/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { Flex } from '../flex';\nimport { space } from '../utils/space';\nimport type { ComboboxControlProps } from './types';\n\nconst deprecatedDefaultSize = ( {\n\t__next40pxDefaultSize,\n}: Pick< ComboboxControlProps, '__next40pxDefaultSize' > ) =>\n\t! __next40pxDefaultSize &&\n\tcss`\n\t\theight: 28px; // 30px - 2px vertical borders on parent container\n\t\tpadding-left: ${ space( 1 ) };\n\t\tpadding-right: ${ space( 1 ) };\n\t`;\n\nexport const InputWrapperFlex = styled( Flex )`\n\theight: 38px; // 40px - 2px vertical borders on parent container\n\tpadding-left: ${ space( 2 ) };\n\tpadding-right: ${ space( 2 ) };\n\n\t${ deprecatedDefaultSize }\n`;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAKA,MAAMG,qBAAqB,GAAGA,CAAE;EAC/BC;AACsD,CAAC,KACvD,CAAEA,qBAAqB,qBACvBC,UAAG,+BAEe,IAAAC,YAAK,EAAE,CAAE,CAAC,qBACT,IAAAA,YAAK,EAAE,CAAE,CAAC,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,2DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,m6CAC5B;AAEK,MAAMC,gBAAgB,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,UAAI,EAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,8BAE5B,IAAAT,YAAK,EAAE,CAAE,CAAC,qBACT,IAAAA,YAAK,EAAE,CAAE,CAAC,OAEzBH,qBAAqB,SAAAI,OAAA,CAAAC,GAAA,CAAAC,QAAA,w6CACxB;AAACO,OAAA,CAAAN,gBAAA,GAAAA,gBAAA"}