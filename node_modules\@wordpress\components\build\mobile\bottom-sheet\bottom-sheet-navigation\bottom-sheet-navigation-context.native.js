"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BottomSheetNavigationProvider = exports.BottomSheetNavigationContext = exports.BottomSheetNavigationConsumer = void 0;
var _element = require("@wordpress/element");
/**
 * WordPress dependencies
 */

// Navigation context in BottomSheet is necessary for controlling the
// height of navigation container.
const BottomSheetNavigationContext = (0, _element.createContext)({
  currentHeight: {
    value: 0
  },
  setHeight: () => {}
});
exports.BottomSheetNavigationContext = BottomSheetNavigationContext;
const {
  Provider: BottomSheetNavigationProvider,
  Consumer: BottomSheetNavigationConsumer
} = BottomSheetNavigationContext;
exports.BottomSheetNavigationConsumer = BottomSheetNavigationConsumer;
exports.BottomSheetNavigationProvider = BottomSheetNavigationProvider;
//# sourceMappingURL=bottom-sheet-navigation-context.native.js.map