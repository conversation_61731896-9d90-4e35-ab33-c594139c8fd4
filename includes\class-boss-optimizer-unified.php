<?php
/**
 * Service d'optimisation unifié pour Boss SEO
 * Unifie la logique entre le module Boss Optimizer et les metaboxes
 *
 * @package Boss_SEO
 * @subpackage Boss_SEO/includes
 * @since 1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour unifier l'optimisation SEO
 */
class Boss_Optimizer_Unified {

    /**
     * Instance du service IA
     *
     * @var Boss_Optimizer_AI
     */
    private $ai;

    /**
     * Instance des paramètres
     *
     * @var Boss_Optimizer_Settings
     */
    private $settings;

    /**
     * Instance des prompts
     *
     * @var Boss_Optimizer_Prompts
     */
    private $prompts;

    /**
     * Constructeur
     */
    public function __construct() {
        // Charger les dépendances
        $this->load_dependencies();
        
        // Initialiser les services
        $this->settings = new Boss_Optimizer_Settings( 'boss-seo' );
        $this->ai = new Boss_Optimizer_AI( 'boss-seo', $this->settings );
    }

    /**
     * Charge les dépendances nécessaires
     */
    private function load_dependencies() {
        if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
            require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-ai.php';
        }
        if ( ! class_exists( 'Boss_Optimizer_Settings' ) ) {
            require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-settings.php';
        }
        if ( ! class_exists( 'Boss_Optimizer_Prompts' ) ) {
            require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-prompts.php';
        }
    }

    /**
     * Optimise un contenu de manière unifiée
     *
     * @param int $post_id ID du post
     * @param array $options Options d'optimisation
     * @return array Résultat de l'optimisation
     */
    public function optimize_content( $post_id, $options = array() ) {
        try {
            // Récupérer le post
            $post = get_post( $post_id );
            if ( ! $post ) {
                return array(
                    'success' => false,
                    'message' => __( 'Post non trouvé', 'boss-seo' )
                );
            }

            // Préparer le contenu pour l'analyse
            $content = $this->prepare_content_for_analysis( $post );
            
            // Options par défaut
            $default_options = array(
                'use_ai' => true,
                'auto_save' => true,
                'generate_title' => true,
                'generate_description' => true,
                'generate_keywords' => true,
                'focus_keyword' => '',
            );
            
            $options = wp_parse_args( $options, $default_options );

            // Résultat de l'optimisation
            $result = array(
                'success' => true,
                'post_id' => $post_id,
                'optimizations' => array(),
                'auto_saved' => false,
                'message' => __( 'Optimisation réussie', 'boss-seo' )
            );

            // Optimisation avec IA si activée
            if ( $options['use_ai'] && $this->ai->is_available() ) {
                $ai_result = $this->optimize_with_ai( $content, $options );
                if ( $ai_result['success'] ) {
                    $result['optimizations'] = $ai_result['data'];
                } else {
                    // Fallback vers optimisation basique
                    $result['optimizations'] = $this->optimize_basic( $content, $options );
                    $result['message'] = __( 'Optimisation basique appliquée (IA non disponible)', 'boss-seo' );
                }
            } else {
                // Optimisation basique
                $result['optimizations'] = $this->optimize_basic( $content, $options );
                $result['message'] = __( 'Optimisation basique appliquée', 'boss-seo' );
            }

            // Sauvegarde automatique si demandée
            if ( $options['auto_save'] && ! empty( $result['optimizations'] ) ) {
                $save_result = $this->save_optimizations( $post_id, $result['optimizations'] );
                $result['auto_saved'] = $save_result;
            }

            return $result;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Unified Optimizer Error: ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'optimisation: ', 'boss-seo' ) . $e->getMessage()
            );
        }
    }

    /**
     * Optimise avec l'IA en utilisant les prompts directs
     *
     * @param string $content Contenu à optimiser
     * @param array $options Options d'optimisation
     * @return array Résultat de l'optimisation IA
     */
    private function optimize_with_ai( $content, $options ) {
        try {
            $optimizations = array();

            // Optimisation complète avec un seul prompt JSON
            if ( $options['generate_title'] && $options['generate_description'] && $options['generate_keywords'] ) {
                $prompt = Boss_Optimizer_Prompts::get_complete_optimization_prompt( 
                    $content, 
                    $options['focus_keyword'] 
                );
                
                $ai_response = $this->ai->generate_content( $prompt, array(
                    'temperature' => 0.3,
                    'max_tokens' => 500
                ) );

                if ( $ai_response['success'] ) {
                    $parsed_data = Boss_Optimizer_Prompts::parse_json_response( $ai_response['content'] );
                    
                    if ( $parsed_data ) {
                        $optimizations = array(
                            'boss_seo_title' => $parsed_data['title'] ?? '',
                            'boss_seo_meta_description' => $parsed_data['meta_description'] ?? '',
                            'boss_seo_focus_keyword' => $parsed_data['focus_keyword'] ?? '',
                            'boss_seo_secondary_keywords' => $parsed_data['secondary_keywords'] ?? '',
                        );
                    }
                }
            } else {
                // Optimisation par éléments individuels
                if ( $options['generate_title'] ) {
                    $optimizations['boss_seo_title'] = $this->generate_title_with_ai( $content, $options['focus_keyword'] );
                }
                
                if ( $options['generate_description'] ) {
                    $optimizations['boss_seo_meta_description'] = $this->generate_description_with_ai( $content, $options['focus_keyword'] );
                }
                
                if ( $options['generate_keywords'] ) {
                    $keywords = $this->generate_keywords_with_ai( $content );
                    if ( ! empty( $keywords ) ) {
                        if ( empty( $options['focus_keyword'] ) && ! empty( $keywords[0] ) ) {
                            $optimizations['boss_seo_focus_keyword'] = $keywords[0];
                            $secondary = array_slice( $keywords, 1, 3 );
                        } else {
                            $secondary = array_slice( $keywords, 0, 3 );
                        }
                        $optimizations['boss_seo_secondary_keywords'] = implode( ',', $secondary );
                    }
                }
            }

            // Nettoyer et valider les optimisations
            $optimizations = $this->validate_optimizations( $optimizations );

            return array(
                'success' => true,
                'data' => $optimizations
            );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO AI Optimization Error: ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Génère un titre avec l'IA
     *
     * @param string $content Contenu
     * @param string $keyword Mot-clé principal
     * @return string Titre généré
     */
    private function generate_title_with_ai( $content, $keyword = '' ) {
        $prompt = Boss_Optimizer_Prompts::get_title_prompt( $content, $keyword );
        
        $ai_response = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.3,
            'max_tokens' => 100
        ) );

        if ( $ai_response['success'] ) {
            $title = trim( $ai_response['content'] );
            // Supprimer les guillemets si présents
            $title = preg_replace( '/^["\']|["\']$/', '', $title );
            return substr( $title, 0, 60 );
        }

        return '';
    }

    /**
     * Génère une description avec l'IA
     *
     * @param string $content Contenu
     * @param string $keyword Mot-clé principal
     * @return string Description générée
     */
    private function generate_description_with_ai( $content, $keyword = '' ) {
        $prompt = Boss_Optimizer_Prompts::get_meta_description_prompt( $content, $keyword );
        
        $ai_response = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.3,
            'max_tokens' => 200
        ) );

        if ( $ai_response['success'] ) {
            $description = trim( $ai_response['content'] );
            // Supprimer les guillemets si présents
            $description = preg_replace( '/^["\']|["\']$/', '', $description );
            return substr( $description, 0, 160 );
        }

        return '';
    }

    /**
     * Génère des mots-clés avec l'IA
     *
     * @param string $content Contenu
     * @return array Mots-clés générés
     */
    private function generate_keywords_with_ai( $content ) {
        // Extraire le sujet du contenu
        $topic = $this->extract_topic_from_content( $content );
        
        $prompt = Boss_Optimizer_Prompts::get_keywords_prompt( $topic, 5 );
        
        $ai_response = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.3,
            'max_tokens' => 100
        ) );

        if ( $ai_response['success'] ) {
            return Boss_Optimizer_Prompts::parse_keywords_response( $ai_response['content'] );
        }

        return array();
    }

    /**
     * Optimisation basique sans IA
     *
     * @param string $content Contenu
     * @param array $options Options
     * @return array Optimisations basiques
     */
    private function optimize_basic( $content, $options ) {
        $optimizations = array();

        if ( $options['generate_title'] ) {
            $optimizations['boss_seo_title'] = $this->generate_basic_title( $content );
        }

        if ( $options['generate_description'] ) {
            $optimizations['boss_seo_meta_description'] = $this->generate_basic_description( $content );
        }

        if ( $options['generate_keywords'] ) {
            $keywords = $this->extract_basic_keywords( $content );
            if ( ! empty( $keywords ) ) {
                $optimizations['boss_seo_focus_keyword'] = $keywords[0];
                $optimizations['boss_seo_secondary_keywords'] = implode( ',', array_slice( $keywords, 1, 3 ) );
            }
        }

        return $optimizations;
    }

    /**
     * Prépare le contenu pour l'analyse
     *
     * @param WP_Post $post Post WordPress
     * @return string Contenu préparé
     */
    private function prepare_content_for_analysis( $post ) {
        $content = $post->post_title . ' ' . $post->post_content;
        
        // Nettoyer le contenu
        $content = wp_strip_all_tags( $content );
        $content = preg_replace( '/\s+/', ' ', $content );
        
        return trim( $content );
    }

    /**
     * Extrait le sujet principal du contenu
     *
     * @param string $content Contenu
     * @return string Sujet extrait
     */
    private function extract_topic_from_content( $content ) {
        // Prendre les premiers mots significatifs
        $words = explode( ' ', $content );
        $topic_words = array_slice( $words, 0, 10 );
        
        return implode( ' ', $topic_words );
    }

    /**
     * Valide et nettoie les optimisations
     *
     * @param array $optimizations Optimisations à valider
     * @return array Optimisations validées
     */
    private function validate_optimizations( $optimizations ) {
        $validated = array();

        if ( isset( $optimizations['boss_seo_title'] ) ) {
            $title = sanitize_text_field( $optimizations['boss_seo_title'] );
            if ( strlen( $title ) > 0 && strlen( $title ) <= 60 ) {
                $validated['boss_seo_title'] = $title;
            }
        }

        if ( isset( $optimizations['boss_seo_meta_description'] ) ) {
            $description = sanitize_textarea_field( $optimizations['boss_seo_meta_description'] );
            if ( strlen( $description ) > 0 && strlen( $description ) <= 160 ) {
                $validated['boss_seo_meta_description'] = $description;
            }
        }

        if ( isset( $optimizations['boss_seo_focus_keyword'] ) ) {
            $keyword = sanitize_text_field( $optimizations['boss_seo_focus_keyword'] );
            if ( strlen( $keyword ) > 0 ) {
                $validated['boss_seo_focus_keyword'] = $keyword;
            }
        }

        if ( isset( $optimizations['boss_seo_secondary_keywords'] ) ) {
            $keywords = sanitize_text_field( $optimizations['boss_seo_secondary_keywords'] );
            if ( strlen( $keywords ) > 0 ) {
                $validated['boss_seo_secondary_keywords'] = $keywords;
            }
        }

        return $validated;
    }

    /**
     * Sauvegarde les optimisations
     *
     * @param int $post_id ID du post
     * @param array $optimizations Optimisations à sauvegarder
     * @return bool Succès de la sauvegarde
     */
    private function save_optimizations( $post_id, $optimizations ) {
        try {
            foreach ( $optimizations as $key => $value ) {
                update_post_meta( $post_id, '_' . $key, $value );
            }

            // Marquer comme optimisé
            update_post_meta( $post_id, '_boss_seo_optimized_date', current_time( 'mysql' ) );
            update_post_meta( $post_id, '_boss_seo_optimized', 1 );

            return true;
        } catch ( Exception $e ) {
            error_log( 'Boss SEO Save Error: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Génère un titre basique
     *
     * @param string $content Contenu
     * @return string Titre basique
     */
    private function generate_basic_title( $content ) {
        $words = explode( ' ', $content );
        $title_words = array_slice( $words, 0, 8 );
        $title = implode( ' ', $title_words );
        
        return substr( $title, 0, 60 );
    }

    /**
     * Génère une description basique
     *
     * @param string $content Contenu
     * @return string Description basique
     */
    private function generate_basic_description( $content ) {
        $words = explode( ' ', $content );
        $desc_words = array_slice( $words, 0, 25 );
        $description = implode( ' ', $desc_words );
        
        return substr( $description, 0, 160 );
    }

    /**
     * Extrait des mots-clés basiques
     *
     * @param string $content Contenu
     * @return array Mots-clés basiques
     */
    private function extract_basic_keywords( $content ) {
        // Logique simple d'extraction de mots-clés
        $words = str_word_count( strtolower( $content ), 1 );
        $word_counts = array_count_values( $words );
        
        // Filtrer les mots trop courts
        $word_counts = array_filter( $word_counts, function( $count, $word ) {
            return strlen( $word ) > 3 && $count > 1;
        }, ARRAY_FILTER_USE_BOTH );
        
        arsort( $word_counts );
        
        return array_slice( array_keys( $word_counts ), 0, 5 );
    }
}
