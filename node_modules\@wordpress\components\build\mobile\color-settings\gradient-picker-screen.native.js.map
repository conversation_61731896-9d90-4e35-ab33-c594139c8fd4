{"version": 3, "names": ["_reactNative", "require", "_native", "_i18n", "_customGradientPicker", "_interopRequireDefault", "_navBar", "GradientPickerScreen", "navigation", "useNavigation", "route", "useRoute", "setColor", "currentValue", "isGradientColor", "params", "_react", "createElement", "View", "default", "BackButton", "onPress", "goBack", "Heading", "__", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/color-settings/gradient-picker-screen.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View } from 'react-native';\nimport { useNavigation, useRoute } from '@react-navigation/native';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport CustomGradientPicker from '../../custom-gradient-picker';\nimport NavBar from '../bottom-sheet/nav-bar';\n\nconst GradientPickerScreen = () => {\n\tconst navigation = useNavigation();\n\tconst route = useRoute();\n\tconst { setColor, currentValue, isGradientColor } = route.params;\n\treturn (\n\t\t<View>\n\t\t\t<NavBar>\n\t\t\t\t<NavBar.BackButton onPress={ navigation.goBack } />\n\t\t\t\t<NavBar.Heading>{ __( 'Customize Gradient' ) }</NavBar.Heading>\n\t\t\t</NavBar>\n\t\t\t<CustomGradientPicker\n\t\t\t\tsetColor={ setColor }\n\t\t\t\tcurrentValue={ currentValue }\n\t\t\t\tisGradientColor={ isGradientColor }\n\t\t\t/>\n\t\t</View>\n\t);\n};\n\nexport default GradientPickerScreen;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAKA,IAAAE,KAAA,GAAAF,OAAA;AAKA,IAAAG,qBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAD,sBAAA,CAAAJ,OAAA;AAfA;AACA;AACA;;AAIA;AACA;AACA;;AAGA;AACA;AACA;;AAIA,MAAMM,oBAAoB,GAAGA,CAAA,KAAM;EAClC,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAClC,MAAMC,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGJ,KAAK,CAACK,MAAM;EAChE,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACjB,YAAA,CAAAkB,IAAI,QACJ,IAAAF,MAAA,CAAAC,aAAA,EAACX,OAAA,CAAAa,OAAM,QACN,IAAAH,MAAA,CAAAC,aAAA,EAACX,OAAA,CAAAa,OAAM,CAACC,UAAU;IAACC,OAAO,EAAGb,UAAU,CAACc;EAAQ,CAAE,CAAC,EACnD,IAAAN,MAAA,CAAAC,aAAA,EAACX,OAAA,CAAAa,OAAM,CAACI,OAAO,QAAG,IAAAC,QAAE,EAAE,oBAAqB,CAAmB,CACvD,CAAC,EACT,IAAAR,MAAA,CAAAC,aAAA,EAACb,qBAAA,CAAAe,OAAoB;IACpBP,QAAQ,EAAGA,QAAU;IACrBC,YAAY,EAAGA,YAAc;IAC7BC,eAAe,EAAGA;EAAiB,CACnC,CACI,CAAC;AAET,CAAC;AAAC,IAAAW,QAAA,GAEalB,oBAAoB;AAAAmB,OAAA,CAAAP,OAAA,GAAAM,QAAA"}