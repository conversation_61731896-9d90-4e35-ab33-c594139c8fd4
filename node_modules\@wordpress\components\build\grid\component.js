"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Grid = void 0;
var _react = require("react");
var _context = require("../context");
var _view = require("../view");
var _hook = _interopRequireDefault(require("./hook"));
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedGrid(props, forwardedRef) {
  const gridProps = (0, _hook.default)(props);
  return (0, _react.createElement)(_view.View, {
    ...gridProps,
    ref: forwardedRef
  });
}

/**
 * `Grid` is a primitive layout component that can arrange content in a grid configuration.
 *
 * ```jsx
 * import {
 * 	__experimentalGrid as Grid,
 * 	__experimentalText as Text
 * } from `@wordpress/components`;
 *
 * function Example() {
 * 	return (
 * 		<Grid columns={ 3 }>
 * 			<Text>Code</Text>
 * 			<Text>is</Text>
 * 			<Text>Poetry</Text>
 * 		</Grid>
 * 	);
 * }
 * ```
 */
const Grid = (0, _context.contextConnect)(UnconnectedGrid, 'Grid');
exports.Grid = Grid;
var _default = Grid;
exports.default = _default;
//# sourceMappingURL=component.js.map