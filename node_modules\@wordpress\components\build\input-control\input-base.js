"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.InputBase = InputBase;
exports.default = void 0;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _element = require("@wordpress/element");
var _backdrop = _interopRequireDefault(require("./backdrop"));
var _label = _interopRequireDefault(require("./label"));
var _inputControlStyles = require("./styles/input-control-styles");
var _context = require("../context");
var _useDeprecatedProps = require("../utils/use-deprecated-props");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function useUniqueId(idProp) {
  const instanceId = (0, _compose.useInstanceId)(InputBase);
  const id = `input-base-control-${instanceId}`;
  return idProp || id;
}

// Adapter to map props for the new ui/flex component.
function getUIFlexProps(labelPosition) {
  const props = {};
  switch (labelPosition) {
    case 'top':
      props.direction = 'column';
      props.expanded = false;
      props.gap = 0;
      break;
    case 'bottom':
      props.direction = 'column-reverse';
      props.expanded = false;
      props.gap = 0;
      break;
    case 'edge':
      props.justify = 'space-between';
      break;
  }
  return props;
}
function InputBase(props, ref) {
  const {
    __next40pxDefaultSize,
    __unstableInputWidth,
    children,
    className,
    disabled = false,
    hideLabelFromVision = false,
    labelPosition,
    id: idProp,
    isFocused = false,
    label,
    prefix,
    size = 'default',
    suffix,
    ...restProps
  } = (0, _useDeprecatedProps.useDeprecated36pxDefaultSizeProp)(props, 'wp.components.InputBase', '6.4');
  const id = useUniqueId(idProp);
  const hideLabel = hideLabelFromVision || !label;
  const {
    paddingLeft,
    paddingRight
  } = (0, _inputControlStyles.getSizeConfig)({
    inputSize: size,
    __next40pxDefaultSize
  });
  const prefixSuffixContextValue = (0, _element.useMemo)(() => {
    return {
      InputControlPrefixWrapper: {
        paddingLeft
      },
      InputControlSuffixWrapper: {
        paddingRight
      }
    };
  }, [paddingLeft, paddingRight]);
  return (
    // @ts-expect-error The `direction` prop from Flex (FlexDirection) conflicts with legacy SVGAttributes `direction` (string) that come from React intrinsic prop definitions.
    (0, _react.createElement)(_inputControlStyles.Root, {
      ...restProps,
      ...getUIFlexProps(labelPosition),
      className: className,
      gap: 2,
      isFocused: isFocused,
      labelPosition: labelPosition,
      ref: ref
    }, (0, _react.createElement)(_label.default, {
      className: "components-input-control__label",
      hideLabelFromVision: hideLabelFromVision,
      labelPosition: labelPosition,
      htmlFor: id
    }, label), (0, _react.createElement)(_inputControlStyles.Container, {
      __unstableInputWidth: __unstableInputWidth,
      className: "components-input-control__container",
      disabled: disabled,
      hideLabel: hideLabel,
      labelPosition: labelPosition
    }, (0, _react.createElement)(_context.ContextSystemProvider, {
      value: prefixSuffixContextValue
    }, prefix && (0, _react.createElement)(_inputControlStyles.Prefix, {
      className: "components-input-control__prefix"
    }, prefix), children, suffix && (0, _react.createElement)(_inputControlStyles.Suffix, {
      className: "components-input-control__suffix"
    }, suffix)), (0, _react.createElement)(_backdrop.default, {
      disabled: disabled,
      isFocused: isFocused
    })))
  );
}
var _default = (0, _element.forwardRef)(InputBase);
exports.default = _default;
//# sourceMappingURL=input-base.js.map