{"version": 3, "names": ["_context", "require", "_flex", "_hook", "UnconnectedCardHeader", "props", "forwardedRef", "headerProps", "useCardHeader", "_react", "createElement", "Flex", "ref", "<PERSON><PERSON><PERSON><PERSON>", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/card/card-header/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { Flex } from '../../flex';\nimport { useCardHeader } from './hook';\nimport type { HeaderProps } from '../types';\n\nfunction UnconnectedCardHeader(\n\tprops: WordPressComponentProps< HeaderProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst headerProps = useCardHeader( props );\n\n\treturn <Flex { ...headerProps } ref={ forwardedRef } />;\n}\n\n/**\n * `CardHeader` renders an optional header within a `Card`.\n *\n * ```jsx\n * import { Card, CardBody, CardHeader } from `@wordpress/components`;\n *\n * <Card>\n * \t<CardHeader>...</CardHeader>\n * \t<CardBody>...</CardBody>\n * </Card>\n * ```\n */\nexport const CardHeader = contextConnect( UnconnectedCardHeader, 'CardHeader' );\n\nexport default CardHeader;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,qBAAqBA,CAC7BC,KAAoD,EACpDC,YAAiC,EAChC;EACD,MAAMC,WAAW,GAAG,IAAAC,mBAAa,EAAEH,KAAM,CAAC;EAE1C,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,WAAW;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,UAAU,GAAG,IAAAC,uBAAc,EAAEV,qBAAqB,EAAE,YAAa,CAAC;AAACW,OAAA,CAAAF,UAAA,GAAAA,UAAA;AAAA,IAAAG,QAAA,GAEjEH,UAAU;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}