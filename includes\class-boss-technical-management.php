<?php
/**
 * Classe principale pour la gestion technique du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe principale pour la gestion technique du plugin Boss SEO.
 *
 * Cette classe gère les fonctionnalités de gestion technique du plugin Boss SEO :
 * - Redirections
 * - Robots.txt et Sitemap
 * - Optimisation des médias
 * - En-têtes HTTP
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Technical_Management {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe de gestion des redirections.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Redirections    $redirections    Instance de la classe de gestion des redirections.
     */
    protected $redirections;

    /**
     * Instance de la classe de gestion des robots.txt et sitemaps.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Robots_Sitemap    $robots_sitemap    Instance de la classe de gestion des robots.txt et sitemaps.
     */
    protected $robots_sitemap;

    /**
     * Instance de la classe d'optimisation des médias.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Media_Optimization    $media_optimization    Instance de la classe d'optimisation des médias.
     */
    protected $media_optimization;

    /**
     * Instance de la classe de gestion des en-têtes HTTP.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Http_Headers    $http_headers    Instance de la classe de gestion des en-têtes HTTP.
     */
    protected $http_headers;

    /**
     * Instance de la classe de gestion des liens cassés.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Broken_Links    $broken_links    Instance de la classe de gestion des liens cassés.
     */
    protected $broken_links;

    /**
     * Instance de la classe de gestion des sitemaps avancés.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Advanced_Sitemaps    $advanced_sitemaps    Instance de la classe de gestion des sitemaps avancés.
     */
    protected $advanced_sitemaps;

    /**
     * Instance du contrôleur REST API pour robots.txt et sitemaps.
     *
     * @since    1.2.0
     * @access   protected
     * @var      Boss_Robots_Sitemap_Controller    $robots_sitemap_controller    Instance du contrôleur REST API.
     */
    protected $robots_sitemap_controller;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->load_dependencies();
        $this->init_components();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.1.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * La classe de gestion des redirections.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-redirections.php';

        /**
         * La classe de gestion des robots.txt et sitemaps.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-robots-sitemap.php';

        /**
         * La classe d'optimisation des médias.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-media-optimization.php';

        /**
         * La classe de gestion des en-têtes HTTP.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-http-headers.php';

        /**
         * La classe de gestion des liens cassés.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-broken-links.php';

        /**
         * La classe de gestion des sitemaps avancés.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-advanced-sitemaps.php';

        /**
         * Le contrôleur REST API pour robots.txt et sitemaps.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-robots-sitemap-controller.php';

        /**
         * Les nouvelles classes pour les fonctionnalités avancées des sitemaps.
         */
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-sitemap-dashboard.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-sitemap-ping.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-custom-urls.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-sitemap-monitoring.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-specialized-sitemaps.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-smart-optimization.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-search-console.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-sitemap-cache.php';
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/technical/class-boss-pdf-reports.php';
    }

    /**
     * Initialise les composants du module.
     *
     * @since    1.1.0
     * @access   private
     */
    private function init_components() {
        $this->redirections = new Boss_Redirections( $this->plugin_name, $this->version );
        $this->robots_sitemap = new Boss_Robots_Sitemap( $this->plugin_name, $this->version );
        $this->media_optimization = new Boss_Media_Optimization( $this->plugin_name, $this->version );
        $this->http_headers = new Boss_Http_Headers( $this->plugin_name, $this->version );
        $this->broken_links = new Boss_Broken_Links( $this->plugin_name, $this->version );
        $this->advanced_sitemaps = new Boss_Advanced_Sitemaps( $this->plugin_name, $this->version );
        $this->robots_sitemap_controller = new Boss_Robots_Sitemap_Controller( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks WordPress nécessaires.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Enregistrer les hooks pour les redirections
        $this->redirections->register_hooks();

        // Enregistrer les hooks pour les robots.txt et sitemaps
        $this->robots_sitemap->register_hooks();

        // Enregistrer les hooks pour l'optimisation des médias
        $this->media_optimization->register_hooks();

        // Enregistrer les hooks pour les en-têtes HTTP
        $this->http_headers->register_hooks();

        // Enregistrer les hooks pour les liens cassés
        $this->broken_links->register_hooks();

        // Enregistrer les hooks pour les sitemaps avancés
        $this->advanced_sitemaps->register_hooks();
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_rest_routes() {
        // Enregistrer les routes REST API pour les redirections
        $this->redirections->register_rest_routes();

        // Enregistrer les routes REST API pour les robots.txt et sitemaps
        $this->robots_sitemap->register_rest_routes();

        // Enregistrer les routes REST API pour l'optimisation des médias
        $this->media_optimization->register_rest_routes();

        // Enregistrer les routes REST API pour les en-têtes HTTP
        $this->http_headers->register_rest_routes();

        // Enregistrer les routes REST API pour les liens cassés
        $this->broken_links->register_rest_routes();

        // Enregistrer les routes REST API du contrôleur robots-sitemap
        // Note: Ce contrôleur gère toutes les routes pour éviter les conflits
        $this->robots_sitemap_controller->register_rest_routes();

        // Note: Les routes de $this->advanced_sitemaps sont désactivées pour éviter
        // les conflits avec $this->robots_sitemap_controller qui gère les mêmes routes
    }
}
