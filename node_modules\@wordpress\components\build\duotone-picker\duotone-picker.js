"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _es = _interopRequireDefault(require("fast-deep-equal/es6"));
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var _colorListPicker = _interopRequireDefault(require("./color-list-picker"));
var _circularOptionPicker = _interopRequireDefault(require("../circular-option-picker"));
var _vStack = require("../v-stack");
var _customDuotoneBar = _interopRequireDefault(require("./custom-duotone-bar"));
var _utils = require("./utils");
var _spacer = require("../spacer");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * ```jsx
 * import { DuotonePicker, DuotoneSwatch } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const DUOTONE_PALETTE = [
 * 	{ colors: [ '#8c00b7', '#fcff41' ], name: 'Purple and yellow', slug: 'purple-yellow' },
 * 	{ colors: [ '#000097', '#ff4747' ], name: 'Blue and red', slug: 'blue-red' },
 * ];
 *
 * const COLOR_PALETTE = [
 * 	{ color: '#ff4747', name: 'Red', slug: 'red' },
 * 	{ color: '#fcff41', name: 'Yellow', slug: 'yellow' },
 * 	{ color: '#000097', name: 'Blue', slug: 'blue' },
 * 	{ color: '#8c00b7', name: 'Purple', slug: 'purple' },
 * ];
 *
 * const Example = () => {
 * 	const [ duotone, setDuotone ] = useState( [ '#000000', '#ffffff' ] );
 * 	return (
 * 		<>
 * 			<DuotonePicker
 * 				duotonePalette={ DUOTONE_PALETTE }
 * 				colorPalette={ COLOR_PALETTE }
 * 				value={ duotone }
 * 				onChange={ setDuotone }
 * 			/>
 * 			<DuotoneSwatch values={ duotone } />
 * 		</>
 * 	);
 * };
 * ```
 */
function DuotonePicker({
  asButtons,
  loop,
  clearable = true,
  unsetable = true,
  colorPalette,
  duotonePalette,
  disableCustomColors,
  disableCustomDuotone,
  value,
  onChange,
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledby,
  ...otherProps
}) {
  const [defaultDark, defaultLight] = (0, _element.useMemo)(() => (0, _utils.getDefaultColors)(colorPalette), [colorPalette]);
  const isUnset = value === 'unset';
  const unsetOptionLabel = (0, _i18n.__)('Unset');
  const unsetOption = (0, _react.createElement)(_circularOptionPicker.default.Option, {
    key: "unset",
    value: "unset",
    isSelected: isUnset,
    tooltipText: unsetOptionLabel,
    "aria-label": unsetOptionLabel,
    className: "components-duotone-picker__color-indicator",
    onClick: () => {
      onChange(isUnset ? undefined : 'unset');
    }
  });
  const duotoneOptions = duotonePalette.map(({
    colors,
    slug,
    name
  }) => {
    const style = {
      background: (0, _utils.getGradientFromCSSColors)(colors, '135deg'),
      color: 'transparent'
    };
    const tooltipText = name !== null && name !== void 0 ? name : (0, _i18n.sprintf)(
    // translators: %s: duotone code e.g: "dark-grayscale" or "7f7f7f-ffffff".
    (0, _i18n.__)('Duotone code: %s'), slug);
    const label = name ? (0, _i18n.sprintf)(
    // translators: %s: The name of the option e.g: "Dark grayscale".
    (0, _i18n.__)('Duotone: %s'), name) : tooltipText;
    const isSelected = (0, _es.default)(colors, value);
    return (0, _react.createElement)(_circularOptionPicker.default.Option, {
      key: slug,
      value: colors,
      isSelected: isSelected,
      "aria-label": label,
      tooltipText: tooltipText,
      style: style,
      onClick: () => {
        onChange(isSelected ? undefined : colors);
      }
    });
  });
  let metaProps;
  if (asButtons) {
    metaProps = {
      asButtons: true
    };
  } else {
    const _metaProps = {
      asButtons: false,
      loop
    };
    if (ariaLabel) {
      metaProps = {
        ..._metaProps,
        'aria-label': ariaLabel
      };
    } else if (ariaLabelledby) {
      metaProps = {
        ..._metaProps,
        'aria-labelledby': ariaLabelledby
      };
    } else {
      metaProps = {
        ..._metaProps,
        'aria-label': (0, _i18n.__)('Custom color picker.')
      };
    }
  }
  const options = unsetable ? [unsetOption, ...duotoneOptions] : duotoneOptions;
  return (0, _react.createElement)(_circularOptionPicker.default, {
    ...otherProps,
    ...metaProps,
    options: options,
    actions: !!clearable && (0, _react.createElement)(_circularOptionPicker.default.ButtonAction, {
      onClick: () => onChange(undefined)
    }, (0, _i18n.__)('Clear'))
  }, (0, _react.createElement)(_spacer.Spacer, {
    paddingTop: options.length === 0 ? 0 : 4
  }, (0, _react.createElement)(_vStack.VStack, {
    spacing: 3
  }, !disableCustomColors && !disableCustomDuotone && (0, _react.createElement)(_customDuotoneBar.default, {
    value: isUnset ? undefined : value,
    onChange: onChange
  }), !disableCustomDuotone && (0, _react.createElement)(_colorListPicker.default, {
    labels: [(0, _i18n.__)('Shadows'), (0, _i18n.__)('Highlights')],
    colors: colorPalette,
    value: isUnset ? undefined : value,
    disableCustomColors: disableCustomColors,
    enableAlpha: true,
    onChange: newColors => {
      if (!newColors[0]) {
        newColors[0] = defaultDark;
      }
      if (!newColors[1]) {
        newColors[1] = defaultLight;
      }
      const newValue = newColors.length >= 2 ? newColors : undefined;
      // @ts-expect-error TODO: The color arrays for a DuotonePicker should be a tuple of two colors,
      // but it's currently typed as a string[].
      // See also https://github.com/WordPress/gutenberg/pull/49060#discussion_r1136951035
      onChange(newValue);
    }
  }))));
}
var _default = DuotonePicker;
exports.default = _default;
//# sourceMappingURL=duotone-picker.js.map