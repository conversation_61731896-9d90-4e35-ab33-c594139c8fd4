{"version": 3, "names": ["_icons", "require", "_colorIndicator", "_interopRequireDefault", "_icon", "_utils", "DuotoneSwatch", "values", "_react", "createElement", "default", "colorValue", "getGradientFromCSSColors", "icon", "swatch", "_default", "exports"], "sources": ["@wordpress/components/src/duotone-picker/duotone-swatch.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { swatch } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport ColorIndicator from '../color-indicator';\nimport Icon from '../icon';\nimport { getGradientFromCSSColors } from './utils';\nimport type { DuotoneSwatchProps } from './types';\n\nfunction DuotoneSwatch( { values }: DuotoneSwatchProps ) {\n\treturn values ? (\n\t\t<ColorIndicator\n\t\t\tcolorValue={ getGradientFromCSSColors( values, '135deg' ) }\n\t\t/>\n\t) : (\n\t\t<Icon icon={ swatch } />\n\t);\n}\n\nexport default DuotoneSwatch;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,eAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAVA;AACA;AACA;;AAGA;AACA;AACA;;AAMA,SAASK,aAAaA,CAAE;EAAEC;AAA2B,CAAC,EAAG;EACxD,OAAOA,MAAM,GACZ,IAAAC,MAAA,CAAAC,aAAA,EAACP,eAAA,CAAAQ,OAAc;IACdC,UAAU,EAAG,IAAAC,+BAAwB,EAAEL,MAAM,EAAE,QAAS;EAAG,CAC3D,CAAC,GAEF,IAAAC,MAAA,CAAAC,aAAA,EAACL,KAAA,CAAAM,OAAI;IAACG,IAAI,EAAGC;EAAQ,CAAE,CACvB;AACF;AAAC,IAAAC,QAAA,GAEcT,aAAa;AAAAU,OAAA,CAAAN,OAAA,GAAAK,QAAA"}