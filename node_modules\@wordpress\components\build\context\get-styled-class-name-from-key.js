"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getStyledClassNameFromKey = void 0;
var _changeCase = require("change-case");
var _memize = _interopRequireDefault(require("memize"));
/**
 * External dependencies
 */

/**
 * Generates the connected component CSS className based on the namespace.
 *
 * @param namespace The name of the connected component.
 * @return The generated CSS className.
 */
function getStyledClassName(namespace) {
  const kebab = (0, _changeCase.paramCase)(namespace);
  return `components-${kebab}`;
}
const getStyledClassNameFromKey = (0, _memize.default)(getStyledClassName);
exports.getStyledClassNameFromKey = getStyledClassNameFromKey;
//# sourceMappingURL=get-styled-class-name-from-key.js.map