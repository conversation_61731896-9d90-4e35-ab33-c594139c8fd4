{"version": 3, "names": ["_reactNativeGestureHandler", "require", "_reactNativeReanimated", "_interopRequireWildcard", "_element", "_style", "_interopRequireDefault", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "Context", "createContext", "Provider", "Draggable", "children", "onDragEnd", "onDragOver", "onDragStart", "testID", "isDragging", "useSharedValue", "isPanActive", "draggingId", "panGestureRef", "useRef", "currentFirstTouchId", "initialPosition", "x", "y", "lastPosition", "useAnimatedReaction", "value", "result", "previous", "id", "getFirstTouchEvent", "event", "allTouches", "find", "touch", "panGesture", "Gesture", "Pan", "manualActivation", "onTouchesDown", "firstEvent", "onTouchesMove", "state", "activate", "end", "onTouchesCancelled", "_event", "onEnd", "with<PERSON>ef", "shouldCancelWhenOutside", "withTestId", "providerValue", "useMemo", "_react", "createElement", "GestureDetector", "gesture", "View", "style", "styles", "draggable__container", "DraggableTrigger", "enabled", "maxDistance", "minDuration", "onLongPress", "onLongPressEnd", "useContext", "<PERSON><PERSON><PERSON><PERSON>", "useAnimatedGestureHandler", "onActive", "runOnJS", "LongPressGestureHandler", "minDurationMs", "maxDist", "simultaneousHandlers", "onGestureEvent", "exports", "_default"], "sources": ["@wordpress/components/src/draggable/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tG<PERSON>ure,\n\tGestureDetector,\n\tLongPressGestureHandler,\n} from 'react-native-gesture-handler';\nimport Animated, {\n\tuseSharedValue,\n\trunOnJS,\n\tuseAnimatedReaction,\n\tuseAnimatedGestureHandler,\n} from 'react-native-reanimated';\n\n/**\n * WordPress dependencies\n */\nimport { createContext, useContext, useRef, useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nconst Context = createContext( {} );\nconst { Provider } = Context;\n\n/**\n * Draggable component.\n *\n * @param {Object}      props               Component props.\n * @param {JSX.Element} props.children      Children to be rendered.\n * @param {Function}    [props.onDragEnd]   Callback when dragging ends.\n * @param {Function}    [props.onDragOver]  Callback when dragging happens over an element.\n * @param {Function}    [props.onDragStart] Callback when dragging starts.\n * @param {string}      [props.testID]      Id used for querying the pan gesture in tests.\n *\n * @return {JSX.Element} The component to be rendered.\n */\nconst Draggable = ( {\n\tchildren,\n\tonDragEnd,\n\tonDragOver,\n\tonDragStart,\n\ttestID,\n} ) => {\n\tconst isDragging = useSharedValue( false );\n\tconst isPanActive = useSharedValue( false );\n\tconst draggingId = useSharedValue( '' );\n\tconst panGestureRef = useRef();\n\tconst currentFirstTouchId = useSharedValue( null );\n\n\tconst initialPosition = {\n\t\tx: useSharedValue( 0 ),\n\t\ty: useSharedValue( 0 ),\n\t};\n\tconst lastPosition = {\n\t\tx: useSharedValue( 0 ),\n\t\ty: useSharedValue( 0 ),\n\t};\n\n\tuseAnimatedReaction(\n\t\t() => isDragging.value,\n\t\t( result, previous ) => {\n\t\t\tif ( result === previous || previous === null ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif ( result ) {\n\t\t\t\tif ( onDragStart ) {\n\t\t\t\t\tonDragStart( {\n\t\t\t\t\t\tx: initialPosition.x.value,\n\t\t\t\t\t\ty: initialPosition.y.value,\n\t\t\t\t\t\tid: draggingId.value,\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t} else if ( onDragEnd ) {\n\t\t\t\tonDragEnd( {\n\t\t\t\t\tx: lastPosition.x.value,\n\t\t\t\t\ty: lastPosition.y.value,\n\t\t\t\t\tid: draggingId.value,\n\t\t\t\t} );\n\t\t\t}\n\t\t}\n\t);\n\n\tfunction getFirstTouchEvent( event ) {\n\t\t'worklet';\n\n\t\treturn event.allTouches.find(\n\t\t\t( touch ) => touch.id === currentFirstTouchId.value\n\t\t);\n\t}\n\n\tconst panGesture = Gesture.Pan()\n\t\t.manualActivation( true )\n\t\t.onTouchesDown( ( event ) => {\n\t\t\tif ( ! currentFirstTouchId.value ) {\n\t\t\t\tconst firstEvent = event.allTouches[ 0 ];\n\t\t\t\tconst { x = 0, y = 0 } = firstEvent;\n\n\t\t\t\tcurrentFirstTouchId.value = firstEvent.id;\n\n\t\t\t\tinitialPosition.x.value = x;\n\t\t\t\tinitialPosition.y.value = y;\n\t\t\t}\n\t\t} )\n\t\t.onTouchesMove( ( event, state ) => {\n\t\t\tif ( ! isPanActive.value && isDragging.value ) {\n\t\t\t\tisPanActive.value = true;\n\t\t\t\tstate.activate();\n\t\t\t}\n\n\t\t\tif ( isPanActive.value && isDragging.value ) {\n\t\t\t\tconst firstEvent = getFirstTouchEvent( event );\n\n\t\t\t\tif ( ! firstEvent ) {\n\t\t\t\t\tstate.end();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tlastPosition.x.value = firstEvent.x;\n\t\t\t\tlastPosition.y.value = firstEvent.y;\n\n\t\t\t\tif ( onDragOver ) {\n\t\t\t\t\tonDragOver( firstEvent );\n\t\t\t\t}\n\t\t\t}\n\t\t} )\n\t\t.onTouchesCancelled( ( _event, state ) => {\n\t\t\tstate.end();\n\t\t} )\n\t\t.onEnd( () => {\n\t\t\tcurrentFirstTouchId.value = null;\n\t\t\tisPanActive.value = false;\n\t\t\tisDragging.value = false;\n\t\t} )\n\t\t.withRef( panGestureRef )\n\t\t.shouldCancelWhenOutside( false )\n\t\t.withTestId( testID );\n\n\tconst providerValue = useMemo( () => {\n\t\treturn { panGestureRef, isDragging, isPanActive, draggingId };\n\t}, [\n\t\t// `isDragging`, `isPanActive` and `draggingId` are created using the\n\t\t// `useSharedValue` hook provided by the `react-native-reanimated`, which in\n\t\t// theory should guarantee that the value of these variables remains stable.\n\t\t// ESLint can't pick this up, and that's why they have to be specified as\n\t\t// dependencies for this hook call.\n\t\tisDragging,\n\t\tisPanActive,\n\t\tdraggingId,\n\t] );\n\n\treturn (\n\t\t<GestureDetector gesture={ panGesture }>\n\t\t\t<Animated.View style={ styles.draggable__container }>\n\t\t\t\t<Provider value={ providerValue }>{ children }</Provider>\n\t\t\t</Animated.View>\n\t\t</GestureDetector>\n\t);\n};\n\n/**\n * Draggable trigger component.\n *\n * This component acts as the trigger for the dragging functionality.\n *\n * @param {Object}      props                  Component props.\n * @param {JSX.Element} props.children         Children to be rendered.\n * @param {*}           props.id               Identifier passed within the event callbacks.\n * @param {boolean}     [props.enabled]        Enables the long-press gesture.\n * @param {number}      [props.maxDistance]    Maximum distance, that defines how far the finger is allowed to travel during a long press gesture.\n * @param {number}      [props.minDuration]    Minimum time, that a finger must remain pressed on the corresponding view.\n * @param {Function}    [props.onLongPress]    Callback when long-press gesture is triggered over an element.\n * @param {Function}    [props.onLongPressEnd] Callback when long-press gesture ends.\n * @param {string}      [props.testID]         Id used for querying the long-press gesture handler in tests.\n *\n * @return {JSX.Element} The component to be rendered.\n */\nconst DraggableTrigger = ( {\n\tchildren,\n\tenabled = true,\n\tid,\n\tmaxDistance = 1000,\n\tminDuration = 500,\n\tonLongPress,\n\tonLongPressEnd,\n\ttestID,\n} ) => {\n\tconst { panGestureRef, isDragging, isPanActive, draggingId } =\n\t\tuseContext( Context );\n\n\tconst gestureHandler = useAnimatedGestureHandler( {\n\t\tonActive: () => {\n\t\t\tif ( isDragging.value ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tdraggingId.value = id;\n\t\t\tisDragging.value = true;\n\t\t\tif ( onLongPress ) {\n\t\t\t\trunOnJS( onLongPress )( id );\n\t\t\t}\n\t\t},\n\t\tonEnd: () => {\n\t\t\tif ( ! isPanActive.value ) {\n\t\t\t\tisDragging.value = false;\n\t\t\t}\n\n\t\t\tif ( onLongPressEnd ) {\n\t\t\t\trunOnJS( onLongPressEnd )( id );\n\t\t\t}\n\t\t},\n\t} );\n\n\treturn (\n\t\t<LongPressGestureHandler\n\t\t\tenabled={ enabled }\n\t\t\tminDurationMs={ minDuration }\n\t\t\tmaxDist={ maxDistance }\n\t\t\tsimultaneousHandlers={ panGestureRef }\n\t\t\tshouldCancelWhenOutside={ false }\n\t\t\tonGestureEvent={ gestureHandler }\n\t\t\ttestID={ testID }\n\t\t>\n\t\t\t{ children }\n\t\t</LongPressGestureHandler>\n\t);\n};\n\nexport { DraggableTrigger };\nexport default Draggable;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,0BAAA,GAAAC,OAAA;AAKA,IAAAC,sBAAA,GAAAC,uBAAA,CAAAF,OAAA;AAUA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;AAAkC,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAvBlC;AACA;AACA;;AAaA;AACA;AACA;;AAGA;AACA;AACA;;AAGA,MAAMW,OAAO,GAAG,IAAAC,sBAAa,EAAE,CAAC,CAAE,CAAC;AACnC,MAAM;EAAEC;AAAS,CAAC,GAAGF,OAAO;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,SAAS,GAAGA,CAAE;EACnBC,QAAQ;EACRC,SAAS;EACTC,UAAU;EACVC,WAAW;EACXC;AACD,CAAC,KAAM;EACN,MAAMC,UAAU,GAAG,IAAAC,qCAAc,EAAE,KAAM,CAAC;EAC1C,MAAMC,WAAW,GAAG,IAAAD,qCAAc,EAAE,KAAM,CAAC;EAC3C,MAAME,UAAU,GAAG,IAAAF,qCAAc,EAAE,EAAG,CAAC;EACvC,MAAMG,aAAa,GAAG,IAAAC,eAAM,EAAC,CAAC;EAC9B,MAAMC,mBAAmB,GAAG,IAAAL,qCAAc,EAAE,IAAK,CAAC;EAElD,MAAMM,eAAe,GAAG;IACvBC,CAAC,EAAE,IAAAP,qCAAc,EAAE,CAAE,CAAC;IACtBQ,CAAC,EAAE,IAAAR,qCAAc,EAAE,CAAE;EACtB,CAAC;EACD,MAAMS,YAAY,GAAG;IACpBF,CAAC,EAAE,IAAAP,qCAAc,EAAE,CAAE,CAAC;IACtBQ,CAAC,EAAE,IAAAR,qCAAc,EAAE,CAAE;EACtB,CAAC;EAED,IAAAU,0CAAmB,EAClB,MAAMX,UAAU,CAACY,KAAK,EACtB,CAAEC,MAAM,EAAEC,QAAQ,KAAM;IACvB,IAAKD,MAAM,KAAKC,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAG;MAC/C;IACD;IAEA,IAAKD,MAAM,EAAG;MACb,IAAKf,WAAW,EAAG;QAClBA,WAAW,CAAE;UACZU,CAAC,EAAED,eAAe,CAACC,CAAC,CAACI,KAAK;UAC1BH,CAAC,EAAEF,eAAe,CAACE,CAAC,CAACG,KAAK;UAC1BG,EAAE,EAAEZ,UAAU,CAACS;QAChB,CAAE,CAAC;MACJ;IACD,CAAC,MAAM,IAAKhB,SAAS,EAAG;MACvBA,SAAS,CAAE;QACVY,CAAC,EAAEE,YAAY,CAACF,CAAC,CAACI,KAAK;QACvBH,CAAC,EAAEC,YAAY,CAACD,CAAC,CAACG,KAAK;QACvBG,EAAE,EAAEZ,UAAU,CAACS;MAChB,CAAE,CAAC;IACJ;EACD,CACD,CAAC;EAED,SAASI,kBAAkBA,CAAEC,KAAK,EAAG;IACpC,SAAS;;IAET,OAAOA,KAAK,CAACC,UAAU,CAACC,IAAI,CACzBC,KAAK,IAAMA,KAAK,CAACL,EAAE,KAAKT,mBAAmB,CAACM,KAC/C,CAAC;EACF;EAEA,MAAMS,UAAU,GAAGC,kCAAO,CAACC,GAAG,CAAC,CAAC,CAC9BC,gBAAgB,CAAE,IAAK,CAAC,CACxBC,aAAa,CAAIR,KAAK,IAAM;IAC5B,IAAK,CAAEX,mBAAmB,CAACM,KAAK,EAAG;MAClC,MAAMc,UAAU,GAAGT,KAAK,CAACC,UAAU,CAAE,CAAC,CAAE;MACxC,MAAM;QAAEV,CAAC,GAAG,CAAC;QAAEC,CAAC,GAAG;MAAE,CAAC,GAAGiB,UAAU;MAEnCpB,mBAAmB,CAACM,KAAK,GAAGc,UAAU,CAACX,EAAE;MAEzCR,eAAe,CAACC,CAAC,CAACI,KAAK,GAAGJ,CAAC;MAC3BD,eAAe,CAACE,CAAC,CAACG,KAAK,GAAGH,CAAC;IAC5B;EACD,CAAE,CAAC,CACFkB,aAAa,CAAE,CAAEV,KAAK,EAAEW,KAAK,KAAM;IACnC,IAAK,CAAE1B,WAAW,CAACU,KAAK,IAAIZ,UAAU,CAACY,KAAK,EAAG;MAC9CV,WAAW,CAACU,KAAK,GAAG,IAAI;MACxBgB,KAAK,CAACC,QAAQ,CAAC,CAAC;IACjB;IAEA,IAAK3B,WAAW,CAACU,KAAK,IAAIZ,UAAU,CAACY,KAAK,EAAG;MAC5C,MAAMc,UAAU,GAAGV,kBAAkB,CAAEC,KAAM,CAAC;MAE9C,IAAK,CAAES,UAAU,EAAG;QACnBE,KAAK,CAACE,GAAG,CAAC,CAAC;QACX;MACD;MAEApB,YAAY,CAACF,CAAC,CAACI,KAAK,GAAGc,UAAU,CAAClB,CAAC;MACnCE,YAAY,CAACD,CAAC,CAACG,KAAK,GAAGc,UAAU,CAACjB,CAAC;MAEnC,IAAKZ,UAAU,EAAG;QACjBA,UAAU,CAAE6B,UAAW,CAAC;MACzB;IACD;EACD,CAAE,CAAC,CACFK,kBAAkB,CAAE,CAAEC,MAAM,EAAEJ,KAAK,KAAM;IACzCA,KAAK,CAACE,GAAG,CAAC,CAAC;EACZ,CAAE,CAAC,CACFG,KAAK,CAAE,MAAM;IACb3B,mBAAmB,CAACM,KAAK,GAAG,IAAI;IAChCV,WAAW,CAACU,KAAK,GAAG,KAAK;IACzBZ,UAAU,CAACY,KAAK,GAAG,KAAK;EACzB,CAAE,CAAC,CACFsB,OAAO,CAAE9B,aAAc,CAAC,CACxB+B,uBAAuB,CAAE,KAAM,CAAC,CAChCC,UAAU,CAAErC,MAAO,CAAC;EAEtB,MAAMsC,aAAa,GAAG,IAAAC,gBAAO,EAAE,MAAM;IACpC,OAAO;MAAElC,aAAa;MAAEJ,UAAU;MAAEE,WAAW;MAAEC;IAAW,CAAC;EAC9D,CAAC,EAAE;EACF;EACA;EACA;EACA;EACA;EACAH,UAAU,EACVE,WAAW,EACXC,UAAU,CACT,CAAC;EAEH,OACC,IAAAoC,MAAA,CAAAC,aAAA,EAAC9E,0BAAA,CAAA+E,eAAe;IAACC,OAAO,EAAGrB;EAAY,GACtC,IAAAkB,MAAA,CAAAC,aAAA,EAAC5E,sBAAA,CAAAY,OAAQ,CAACmE,IAAI;IAACC,KAAK,EAAGC,cAAM,CAACC;EAAsB,GACnD,IAAAP,MAAA,CAAAC,aAAA,EAAC/C,QAAQ;IAACmB,KAAK,EAAGyB;EAAe,GAAG1C,QAAoB,CAC1C,CACC,CAAC;AAEpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoD,gBAAgB,GAAGA,CAAE;EAC1BpD,QAAQ;EACRqD,OAAO,GAAG,IAAI;EACdjC,EAAE;EACFkC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG,GAAG;EACjBC,WAAW;EACXC,cAAc;EACdrD;AACD,CAAC,KAAM;EACN,MAAM;IAAEK,aAAa;IAAEJ,UAAU;IAAEE,WAAW;IAAEC;EAAW,CAAC,GAC3D,IAAAkD,mBAAU,EAAE9D,OAAQ,CAAC;EAEtB,MAAM+D,cAAc,GAAG,IAAAC,gDAAyB,EAAE;IACjDC,QAAQ,EAAEA,CAAA,KAAM;MACf,IAAKxD,UAAU,CAACY,KAAK,EAAG;QACvB;MACD;MAEAT,UAAU,CAACS,KAAK,GAAGG,EAAE;MACrBf,UAAU,CAACY,KAAK,GAAG,IAAI;MACvB,IAAKuC,WAAW,EAAG;QAClB,IAAAM,8BAAO,EAAEN,WAAY,CAAC,CAAEpC,EAAG,CAAC;MAC7B;IACD,CAAC;IACDkB,KAAK,EAAEA,CAAA,KAAM;MACZ,IAAK,CAAE/B,WAAW,CAACU,KAAK,EAAG;QAC1BZ,UAAU,CAACY,KAAK,GAAG,KAAK;MACzB;MAEA,IAAKwC,cAAc,EAAG;QACrB,IAAAK,8BAAO,EAAEL,cAAe,CAAC,CAAErC,EAAG,CAAC;MAChC;IACD;EACD,CAAE,CAAC;EAEH,OACC,IAAAwB,MAAA,CAAAC,aAAA,EAAC9E,0BAAA,CAAAgG,uBAAuB;IACvBV,OAAO,EAAGA,OAAS;IACnBW,aAAa,EAAGT,WAAa;IAC7BU,OAAO,EAAGX,WAAa;IACvBY,oBAAoB,EAAGzD,aAAe;IACtC+B,uBAAuB,EAAG,KAAO;IACjC2B,cAAc,EAAGR,cAAgB;IACjCvD,MAAM,EAAGA;EAAQ,GAEfJ,QACsB,CAAC;AAE5B,CAAC;AAACoE,OAAA,CAAAhB,gBAAA,GAAAA,gBAAA;AAAA,IAAAiB,QAAA,GAGatE,SAAS;AAAAqE,OAAA,CAAAvF,OAAA,GAAAwF,QAAA"}