{"version": 3, "names": ["_vStack", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "Wrapper", "_base", "default", "VStack", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports"], "sources": ["@wordpress/components/src/date-time/date-time/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport { VStack } from '../../v-stack';\n\nexport const Wrapper = styled( VStack )`\n\tbox-sizing: border-box;\n`;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,OAAA,GAAAC,OAAA;AAAuC,SAAAC,iCAAA;AAEhC,MAAMC,OAAO,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,cAAM,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAEtC;AAACc,OAAA,CAAAb,OAAA,GAAAA,OAAA"}