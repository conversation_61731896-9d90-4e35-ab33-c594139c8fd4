{"version": 3, "names": ["_colord", "require", "_i18n", "_inputControl", "_text", "_spacer", "_space", "_colorsValues", "HexInput", "color", "onChange", "enableAlpha", "handleChange", "nextValue", "hexValue", "startsWith", "colord", "stateReducer", "state", "action", "nativeEvent", "payload", "event", "inputType", "value", "slice", "toUpperCase", "_react", "createElement", "InputControl", "prefix", "Spacer", "as", "Text", "marginLeft", "space", "COLORS", "theme", "accent", "lineHeight", "toHex", "max<PERSON><PERSON><PERSON>", "label", "__", "hideLabelFromVision", "size", "__unstableStateReducer", "__unstableInputWidth", "exports"], "sources": ["@wordpress/components/src/color-picker/hex-input.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { colord } from 'colord';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { InputControl } from '../input-control';\nimport { Text } from '../text';\nimport { Spacer } from '../spacer';\nimport { space } from '../utils/space';\nimport { COLORS } from '../utils/colors-values';\nimport type { StateReducer } from '../input-control/reducer/state';\nimport type { HexInputProps } from './types';\n\nexport const HexInput = ( { color, onChange, enableAlpha }: HexInputProps ) => {\n\tconst handleChange = ( nextValue: string | undefined ) => {\n\t\tif ( ! nextValue ) return;\n\t\tconst hexValue = nextValue.startsWith( '#' )\n\t\t\t? nextValue\n\t\t\t: '#' + nextValue;\n\n\t\tonChange( colord( hexValue ) );\n\t};\n\n\tconst stateReducer: StateReducer = ( state, action ) => {\n\t\tconst nativeEvent = action.payload?.event?.nativeEvent as InputEvent;\n\n\t\tif ( 'insertFromPaste' !== nativeEvent?.inputType ) {\n\t\t\treturn { ...state };\n\t\t}\n\n\t\tconst value = state.value?.startsWith( '#' )\n\t\t\t? state.value.slice( 1 ).toUpperCase()\n\t\t\t: state.value?.toUpperCase();\n\n\t\treturn { ...state, value };\n\t};\n\n\treturn (\n\t\t<InputControl\n\t\t\tprefix={\n\t\t\t\t<Spacer\n\t\t\t\t\tas={ Text }\n\t\t\t\t\tmarginLeft={ space( 4 ) }\n\t\t\t\t\tcolor={ COLORS.theme.accent }\n\t\t\t\t\tlineHeight={ 1 }\n\t\t\t\t>\n\t\t\t\t\t#\n\t\t\t\t</Spacer>\n\t\t\t}\n\t\t\tvalue={ color.toHex().slice( 1 ).toUpperCase() }\n\t\t\tonChange={ handleChange }\n\t\t\tmaxLength={ enableAlpha ? 9 : 7 }\n\t\t\tlabel={ __( 'Hex color' ) }\n\t\t\thideLabelFromVision\n\t\t\tsize=\"__unstable-large\"\n\t\t\t__unstableStateReducer={ stateReducer }\n\t\t\t__unstableInputWidth=\"9em\"\n\t\t/>\n\t);\n};\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AAKA,IAAAE,aAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,aAAA,GAAAN,OAAA;AAjBA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AASO,MAAMO,QAAQ,GAAGA,CAAE;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAA2B,CAAC,KAAM;EAC9E,MAAMC,YAAY,GAAKC,SAA6B,IAAM;IACzD,IAAK,CAAEA,SAAS,EAAG;IACnB,MAAMC,QAAQ,GAAGD,SAAS,CAACE,UAAU,CAAE,GAAI,CAAC,GACzCF,SAAS,GACT,GAAG,GAAGA,SAAS;IAElBH,QAAQ,CAAE,IAAAM,cAAM,EAAEF,QAAS,CAAE,CAAC;EAC/B,CAAC;EAED,MAAMG,YAA0B,GAAGA,CAAEC,KAAK,EAAEC,MAAM,KAAM;IACvD,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,EAAEC,KAAK,EAAEF,WAAyB;IAEpE,IAAK,iBAAiB,KAAKA,WAAW,EAAEG,SAAS,EAAG;MACnD,OAAO;QAAE,GAAGL;MAAM,CAAC;IACpB;IAEA,MAAMM,KAAK,GAAGN,KAAK,CAACM,KAAK,EAAET,UAAU,CAAE,GAAI,CAAC,GACzCG,KAAK,CAACM,KAAK,CAACC,KAAK,CAAE,CAAE,CAAC,CAACC,WAAW,CAAC,CAAC,GACpCR,KAAK,CAACM,KAAK,EAAEE,WAAW,CAAC,CAAC;IAE7B,OAAO;MAAE,GAAGR,KAAK;MAAEM;IAAM,CAAC;EAC3B,CAAC;EAED,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACzB,aAAA,CAAA0B,YAAY;IACZC,MAAM,EACL,IAAAH,MAAA,CAAAC,aAAA,EAACvB,OAAA,CAAA0B,MAAM;MACNC,EAAE,EAAGC,UAAM;MACXC,UAAU,EAAG,IAAAC,YAAK,EAAE,CAAE,CAAG;MACzB1B,KAAK,EAAG2B,oBAAM,CAACC,KAAK,CAACC,MAAQ;MAC7BC,UAAU,EAAG;IAAG,GAChB,GAEO,CACR;IACDf,KAAK,EAAGf,KAAK,CAAC+B,KAAK,CAAC,CAAC,CAACf,KAAK,CAAE,CAAE,CAAC,CAACC,WAAW,CAAC,CAAG;IAChDhB,QAAQ,EAAGE,YAAc;IACzB6B,SAAS,EAAG9B,WAAW,GAAG,CAAC,GAAG,CAAG;IACjC+B,KAAK,EAAG,IAAAC,QAAE,EAAE,WAAY,CAAG;IAC3BC,mBAAmB;IACnBC,IAAI,EAAC,kBAAkB;IACvBC,sBAAsB,EAAG7B,YAAc;IACvC8B,oBAAoB,EAAC;EAAK,CAC1B,CAAC;AAEJ,CAAC;AAACC,OAAA,CAAAxC,QAAA,GAAAA,QAAA"}