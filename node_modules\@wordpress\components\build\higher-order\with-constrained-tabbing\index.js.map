{"version": 3, "names": ["_compose", "require", "withConstrainedTabbing", "createHigherOrderComponent", "WrappedComponent", "ComponentWithConstrainedTabbing", "props", "ref", "useConstrainedTabbing", "_react", "createElement", "tabIndex", "_default", "exports", "default"], "sources": ["@wordpress/components/src/higher-order/with-constrained-tabbing/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport {\n\tcreateHigherOrderComponent,\n\tuseConstrainedTabbing,\n} from '@wordpress/compose';\n\n/**\n * `withConstrainedTabbing` is a React [higher-order component](https://facebook.github.io/react/docs/higher-order-components.html)\n * adding the ability to constrain keyboard navigation with the Tab key within a component.\n * For accessibility reasons, some UI components need to constrain Tab navigation, for example\n * modal dialogs or similar UI. Use of this component is recommended only in cases where a way to\n * navigate away from the wrapped component is implemented by other means, usually by pressing\n * the Escape key or using a specific UI control, e.g. a \"Close\" button.\n */\nconst withConstrainedTabbing = createHigherOrderComponent(\n\t( WrappedComponent ) =>\n\t\tfunction ComponentWithConstrainedTabbing( props ) {\n\t\t\tconst ref = useConstrainedTabbing();\n\t\t\treturn (\n\t\t\t\t<div ref={ ref } tabIndex={ -1 }>\n\t\t\t\t\t<WrappedComponent { ...props } />\n\t\t\t\t</div>\n\t\t\t);\n\t\t},\n\t'withConstrainedTabbing'\n);\n\nexport default withConstrainedTabbing;\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,IAAAC,mCAA0B,EACtDC,gBAAgB,IACjB,SAASC,+BAA+BA,CAAEC,KAAK,EAAG;EACjD,MAAMC,GAAG,GAAG,IAAAC,8BAAqB,EAAC,CAAC;EACnC,OACC,IAAAC,MAAA,CAAAC,aAAA;IAAKH,GAAG,EAAGA,GAAK;IAACI,QAAQ,EAAG,CAAC;EAAG,GAC/B,IAAAF,MAAA,CAAAC,aAAA,EAACN,gBAAgB;IAAA,GAAME;EAAK,CAAI,CAC5B,CAAC;AAER,CAAC,EACF,wBACD,CAAC;AAAC,IAAAM,QAAA,GAEaV,sBAAsB;AAAAW,OAAA,CAAAC,OAAA,GAAAF,QAAA"}