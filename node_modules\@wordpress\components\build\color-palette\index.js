"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ColorPalette = void 0;
exports.CustomColorPickerDropdown = CustomColorPickerDropdown;
exports.default = void 0;
var _react = require("react");
var _colord = require("colord");
var _names = _interopRequireDefault(require("colord/plugins/names"));
var _a11y = _interopRequireDefault(require("colord/plugins/a11y"));
var _classnames = _interopRequireDefault(require("classnames"));
var _compose = require("@wordpress/compose");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _dropdown = _interopRequireDefault(require("../dropdown"));
var _colorPicker = require("../color-picker");
var _circularOptionPicker = _interopRequireDefault(require("../circular-option-picker"));
var _vStack = require("../v-stack");
var _truncate = require("../truncate");
var _styles = require("./styles");
var _dropdownContentWrapper = _interopRequireDefault(require("../dropdown/dropdown-content-wrapper"));
var _utils = require("./utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

(0, _colord.extend)([_names.default, _a11y.default]);
function SinglePalette({
  className,
  clearColor,
  colors,
  onChange,
  value,
  ...additionalProps
}) {
  const colorOptions = (0, _element.useMemo)(() => {
    return colors.map(({
      color,
      name
    }, index) => {
      const colordColor = (0, _colord.colord)(color);
      const isSelected = value === color;
      return (0, _react.createElement)(_circularOptionPicker.default.Option, {
        key: `${color}-${index}`,
        isSelected: isSelected,
        selectedIconProps: isSelected ? {
          fill: colordColor.contrast() > colordColor.contrast('#000') ? '#fff' : '#000'
        } : {},
        tooltipText: name ||
        // translators: %s: color hex code e.g: "#f00".
        (0, _i18n.sprintf)((0, _i18n.__)('Color code: %s'), color),
        style: {
          backgroundColor: color,
          color
        },
        onClick: isSelected ? clearColor : () => onChange(color, index),
        "aria-label": name ?
        // translators: %s: The name of the color e.g: "vivid red".
        (0, _i18n.sprintf)((0, _i18n.__)('Color: %s'), name) :
        // translators: %s: color hex code e.g: "#f00".
        (0, _i18n.sprintf)((0, _i18n.__)('Color code: %s'), color)
      });
    });
  }, [colors, value, onChange, clearColor]);
  return (0, _react.createElement)(_circularOptionPicker.default.OptionGroup, {
    className: className,
    options: colorOptions,
    ...additionalProps
  });
}
function MultiplePalettes({
  className,
  clearColor,
  colors,
  onChange,
  value,
  headingLevel
}) {
  const instanceId = (0, _compose.useInstanceId)(MultiplePalettes, 'color-palette');
  if (colors.length === 0) {
    return null;
  }
  return (0, _react.createElement)(_vStack.VStack, {
    spacing: 3,
    className: className
  }, colors.map(({
    name,
    colors: colorPalette
  }, index) => {
    const id = `${instanceId}-${index}`;
    return (0, _react.createElement)(_vStack.VStack, {
      spacing: 2,
      key: index
    }, (0, _react.createElement)(_styles.ColorHeading, {
      id: id,
      level: headingLevel
    }, name), (0, _react.createElement)(SinglePalette, {
      clearColor: clearColor,
      colors: colorPalette,
      onChange: newColor => onChange(newColor, index),
      value: value,
      "aria-labelledby": id
    }));
  }));
}
function CustomColorPickerDropdown({
  isRenderedInSidebar,
  popoverProps: receivedPopoverProps,
  ...props
}) {
  const popoverProps = (0, _element.useMemo)(() => ({
    shift: true,
    // Disabling resize as it would otherwise cause the popover to show
    // scrollbars while dragging the color picker's handle close to the
    // popover edge.
    resize: false,
    ...(isRenderedInSidebar ? {
      // When in the sidebar: open to the left (stacking),
      // leaving the same gap as the parent popover.
      placement: 'left-start',
      offset: 34
    } : {
      // Default behavior: open below the anchor
      placement: 'bottom',
      offset: 8
    }),
    ...receivedPopoverProps
  }), [isRenderedInSidebar, receivedPopoverProps]);
  return (0, _react.createElement)(_dropdown.default, {
    contentClassName: "components-color-palette__custom-color-dropdown-content",
    popoverProps: popoverProps,
    ...props
  });
}
function UnforwardedColorPalette(props, forwardedRef) {
  const {
    asButtons,
    loop,
    clearable = true,
    colors = [],
    disableCustomColors = false,
    enableAlpha = false,
    onChange,
    value,
    __experimentalIsRenderedInSidebar = false,
    headingLevel = 2,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledby,
    ...additionalProps
  } = props;
  const [normalizedColorValue, setNormalizedColorValue] = (0, _element.useState)(value);
  const clearColor = (0, _element.useCallback)(() => onChange(undefined), [onChange]);
  const customColorPaletteCallbackRef = (0, _element.useCallback)(node => {
    setNormalizedColorValue((0, _utils.normalizeColorValue)(value, node));
  }, [value]);
  const hasMultipleColorOrigins = (0, _utils.isMultiplePaletteArray)(colors);
  const buttonLabelName = (0, _element.useMemo)(() => (0, _utils.extractColorNameFromCurrentValue)(value, colors, hasMultipleColorOrigins), [value, colors, hasMultipleColorOrigins]);
  const renderCustomColorPicker = () => (0, _react.createElement)(_dropdownContentWrapper.default, {
    paddingSize: "none"
  }, (0, _react.createElement)(_colorPicker.ColorPicker, {
    color: normalizedColorValue,
    onChange: color => onChange(color),
    enableAlpha: enableAlpha
  }));
  const isHex = value?.startsWith('#');

  // Leave hex values as-is. Remove the `var()` wrapper from CSS vars.
  const displayValue = value?.replace(/^var\((.+)\)$/, '$1');
  const customColorAccessibleLabel = !!displayValue ? (0, _i18n.sprintf)(
  // translators: %1$s: The name of the color e.g: "vivid red". %2$s: The color's hex code e.g: "#f00".
  (0, _i18n.__)('Custom color picker. The currently selected color is called "%1$s" and has a value of "%2$s".'), buttonLabelName, displayValue) : (0, _i18n.__)('Custom color picker.');
  const paletteCommonProps = {
    clearColor,
    onChange,
    value
  };
  const actions = !!clearable && (0, _react.createElement)(_circularOptionPicker.default.ButtonAction, {
    onClick: clearColor
  }, (0, _i18n.__)('Clear'));
  let metaProps;
  if (asButtons) {
    metaProps = {
      asButtons: true
    };
  } else {
    const _metaProps = {
      asButtons: false,
      loop
    };
    if (ariaLabel) {
      metaProps = {
        ..._metaProps,
        'aria-label': ariaLabel
      };
    } else if (ariaLabelledby) {
      metaProps = {
        ..._metaProps,
        'aria-labelledby': ariaLabelledby
      };
    } else {
      metaProps = {
        ..._metaProps,
        'aria-label': (0, _i18n.__)('Custom color picker.')
      };
    }
  }
  return (0, _react.createElement)(_vStack.VStack, {
    spacing: 3,
    ref: forwardedRef,
    ...additionalProps
  }, !disableCustomColors && (0, _react.createElement)(CustomColorPickerDropdown, {
    isRenderedInSidebar: __experimentalIsRenderedInSidebar,
    renderContent: renderCustomColorPicker,
    renderToggle: ({
      isOpen,
      onToggle
    }) => (0, _react.createElement)(_vStack.VStack, {
      className: "components-color-palette__custom-color-wrapper",
      spacing: 0
    }, (0, _react.createElement)("button", {
      ref: customColorPaletteCallbackRef,
      className: "components-color-palette__custom-color-button",
      "aria-expanded": isOpen,
      "aria-haspopup": "true",
      onClick: onToggle,
      "aria-label": customColorAccessibleLabel,
      style: {
        background: value
      },
      type: "button"
    }), (0, _react.createElement)(_vStack.VStack, {
      className: "components-color-palette__custom-color-text-wrapper",
      spacing: 0.5
    }, (0, _react.createElement)(_truncate.Truncate, {
      className: "components-color-palette__custom-color-name"
    }, value ? buttonLabelName : (0, _i18n.__)('No color selected')), (0, _react.createElement)(_truncate.Truncate, {
      className: (0, _classnames.default)('components-color-palette__custom-color-value', {
        'components-color-palette__custom-color-value--is-hex': isHex
      })
    }, displayValue)))
  }), (0, _react.createElement)(_circularOptionPicker.default, {
    ...metaProps,
    actions: actions,
    options: hasMultipleColorOrigins ? (0, _react.createElement)(MultiplePalettes, {
      ...paletteCommonProps,
      headingLevel: headingLevel,
      colors: colors,
      value: value
    }) : (0, _react.createElement)(SinglePalette, {
      ...paletteCommonProps,
      colors: colors,
      value: value
    })
  }));
}

/**
 * Allows the user to pick a color from a list of pre-defined color entries.
 *
 * ```jsx
 * import { ColorPalette } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyColorPalette = () => {
 *   const [ color, setColor ] = useState ( '#f00' )
 *   const colors = [
 *     { name: 'red', color: '#f00' },
 *     { name: 'white', color: '#fff' },
 *     { name: 'blue', color: '#00f' },
 *   ];
 *   return (
 *     <ColorPalette
 *       colors={ colors }
 *       value={ color }
 *       onChange={ ( color ) => setColor( color ) }
 *     />
 *   );
 * } );
 * ```
 */
const ColorPalette = (0, _element.forwardRef)(UnforwardedColorPalette);
exports.ColorPalette = ColorPalette;
var _default = ColorPalette;
exports.default = _default;
//# sourceMappingURL=index.js.map