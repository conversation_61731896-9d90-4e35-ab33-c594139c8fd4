"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Flex = void 0;
var _react = require("react");
var _context = require("../../context");
var _hook = require("./hook");
var _context2 = require("./../context");
var _view = require("../../view");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedFlex(props, forwardedRef) {
  const {
    children,
    isColumn,
    ...otherProps
  } = (0, _hook.useFlex)(props);
  return (0, _react.createElement)(_context2.FlexContext.Provider, {
    value: {
      flexItemDisplay: isColumn ? 'block' : undefined
    }
  }, (0, _react.createElement)(_view.View, {
    ...otherProps,
    ref: forwardedRef
  }, children));
}

/**
 * `Flex` is a primitive layout component that adaptively aligns child content
 * horizontally or vertically. `Flex` powers components like `HStack` and
 * `VStack`.
 *
 * `Flex` is used with any of its two sub-components, `FlexItem` and
 * `FlexBlock`.
 *
 * ```jsx
 * import { Flex, FlexBlock, FlexItem } from '@wordpress/components';
 *
 * function Example() {
 *   return (
 *     <Flex>
 *       <FlexItem>
 *         <p>Code</p>
 *       </FlexItem>
 *       <FlexBlock>
 *         <p>Poetry</p>
 *       </FlexBlock>
 *     </Flex>
 *   );
 * }
 * ```
 */
const Flex = (0, _context.contextConnect)(UnconnectedFlex, 'Flex');
exports.Flex = Flex;
var _default = Flex;
exports.default = _default;
//# sourceMappingURL=component.js.map