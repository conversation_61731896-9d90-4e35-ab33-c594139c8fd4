"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ColorHeading = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _heading = require("../heading");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const ColorHeading = ( /*#__PURE__*/0, _base.default)(_heading.Heading, process.env.NODE_ENV === "production" ? {
  target: "ev9wop70"
} : {
  target: "ev9wop70",
  label: "ColorHeading"
})(process.env.NODE_ENV === "production" ? {
  name: "13lxv2o",
  styles: "text-transform:uppercase;line-height:24px;font-weight:500;&&&{font-size:11px;margin-bottom:0;}"
} : {
  name: "13lxv2o",
  styles: "text-transform:uppercase;line-height:24px;font-weight:500;&&&{font-size:11px;margin-bottom:0;}",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY29sb3ItcGFsZXR0ZS9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBVTZDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY29sb3ItcGFsZXR0ZS9zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IEhlYWRpbmcgfSBmcm9tICcuLi9oZWFkaW5nJztcblxuZXhwb3J0IGNvbnN0IENvbG9ySGVhZGluZyA9IHN0eWxlZCggSGVhZGluZyApYFxuXHR0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuXHRsaW5lLWhlaWdodDogMjRweDtcblx0Zm9udC13ZWlnaHQ6IDUwMDtcblx0JiYmIHtcblx0XHRmb250LXNpemU6IDExcHg7XG5cdFx0bWFyZ2luLWJvdHRvbTogMDtcblx0fVxuYDtcbiJdfQ== */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.ColorHeading = ColorHeading;
//# sourceMappingURL=styles.js.map