"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.AnglePickerControl = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var _flex = require("../flex");
var _spacer = require("../spacer");
var _numberControl = _interopRequireDefault(require("../number-control"));
var _angleCircle = _interopRequireDefault(require("./angle-circle"));
var _anglePickerControlStyles = require("./styles/angle-picker-control-styles");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function UnforwardedAnglePickerControl(props, ref) {
  const {
    __nextHasNoMarginBottom = false,
    className,
    label = (0, _i18n.__)('Angle'),
    onChange,
    value,
    ...restProps
  } = props;
  if (!__nextHasNoMarginBottom) {
    (0, _deprecated.default)('Bottom margin styles for wp.components.AnglePickerControl', {
      since: '6.1',
      hint: 'Set the `__nextHasNoMarginBottom` prop to true to start opting into the new styles, which will become the default in a future version.'
    });
  }
  const handleOnNumberChange = unprocessedValue => {
    if (onChange === undefined) {
      return;
    }
    const inputValue = unprocessedValue !== undefined && unprocessedValue !== '' ? parseInt(unprocessedValue, 10) : 0;
    onChange(inputValue);
  };
  const classes = (0, _classnames.default)('components-angle-picker-control', className);
  const unitText = (0, _react.createElement)(_anglePickerControlStyles.UnitText, null, "\xB0");
  const [prefixedUnitText, suffixedUnitText] = (0, _i18n.isRTL)() ? [unitText, null] : [null, unitText];
  return (0, _react.createElement)(_anglePickerControlStyles.Root, {
    ...restProps,
    ref: ref,
    __nextHasNoMarginBottom: __nextHasNoMarginBottom,
    className: classes,
    gap: 2
  }, (0, _react.createElement)(_flex.FlexBlock, null, (0, _react.createElement)(_numberControl.default, {
    label: label,
    className: "components-angle-picker-control__input-field",
    max: 360,
    min: 0,
    onChange: handleOnNumberChange,
    size: "__unstable-large",
    step: "1",
    value: value,
    spinControls: "none",
    prefix: prefixedUnitText,
    suffix: suffixedUnitText
  })), (0, _react.createElement)(_spacer.Spacer, {
    marginBottom: "1",
    marginTop: "auto"
  }, (0, _react.createElement)(_angleCircle.default, {
    "aria-hidden": "true",
    value: value,
    onChange: onChange
  })));
}

/**
 * `AnglePickerControl` is a React component to render a UI that allows users to
 * pick an angle. Users can choose an angle in a visual UI with the mouse by
 * dragging an angle indicator inside a circle or by directly inserting the
 * desired angle in a text field.
 *
 * ```jsx
 * import { useState } from '@wordpress/element';
 * import { AnglePickerControl } from '@wordpress/components';
 *
 * function Example() {
 *   const [ angle, setAngle ] = useState( 0 );
 *   return (
 *     <AnglePickerControl
 *       value={ angle }
 *       onChange={ setAngle }
 *       __nextHasNoMarginBottom
 *     </>
 *   );
 * }
 * ```
 */
const AnglePickerControl = (0, _element.forwardRef)(UnforwardedAnglePickerControl);
exports.AnglePickerControl = AnglePickerControl;
var _default = AnglePickerControl;
exports.default = _default;
//# sourceMappingURL=index.js.map