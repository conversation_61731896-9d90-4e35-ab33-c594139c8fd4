"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = AllInputControl;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _boxControlStyles = require("./styles/box-control-styles");
var _hStack = require("../h-stack");
var _unitControl = require("../unit-control");
var _utils = require("./utils");
/**
 * WordPress dependencies
 */

const noop = () => {};
function AllInputControl({
  __next40pxDefaultSize,
  onChange = noop,
  onFocus = noop,
  values,
  sides,
  selectedUnits,
  setSelectedUnits,
  ...props
}) {
  var _CUSTOM_VALUE_SETTING, _CUSTOM_VALUE_SETTING2;
  const inputId = (0, _compose.useInstanceId)(AllInputControl, 'box-control-input-all');
  const allValue = (0, _utils.getAllValue)(values, selectedUnits, sides);
  const hasValues = (0, _utils.isValuesDefined)(values);
  const isMixed = hasValues && (0, _utils.isValuesMixed)(values, selectedUnits, sides);
  const allPlaceholder = isMixed ? _utils.LABELS.mixed : undefined;
  const [parsedQuantity, parsedUnit] = (0, _unitControl.parseQuantityAndUnitFromRawValue)(allValue);
  const handleOnFocus = event => {
    onFocus(event, {
      side: 'all'
    });
  };
  const onValueChange = next => {
    const isNumeric = next !== undefined && !isNaN(parseFloat(next));
    const nextValue = isNumeric ? next : undefined;
    const nextValues = (0, _utils.applyValueToSides)(values, nextValue, sides);
    onChange(nextValues);
  };
  const sliderOnChange = next => {
    onValueChange(next !== undefined ? [next, parsedUnit].join('') : undefined);
  };

  // Set selected unit so it can be used as fallback by unlinked controls
  // when individual sides do not have a value containing a unit.
  const handleOnUnitChange = unit => {
    const newUnits = (0, _utils.applyValueToSides)(selectedUnits, unit, sides);
    setSelectedUnits(newUnits);
  };
  return (0, _react.createElement)(_hStack.HStack, null, (0, _react.createElement)(_boxControlStyles.StyledUnitControl, {
    ...props,
    __next40pxDefaultSize: __next40pxDefaultSize,
    className: "component-box-control__unit-control",
    disableUnits: isMixed,
    id: inputId,
    isPressEnterToChange: true,
    value: allValue,
    onChange: onValueChange,
    onUnitChange: handleOnUnitChange,
    onFocus: handleOnFocus,
    placeholder: allPlaceholder,
    label: _utils.LABELS.all,
    hideLabelFromVision: true
  }), (0, _react.createElement)(_boxControlStyles.FlexedRangeControl, {
    __nextHasNoMarginBottom: true,
    __next40pxDefaultSize: __next40pxDefaultSize,
    "aria-controls": inputId,
    label: _utils.LABELS.all,
    hideLabelFromVision: true,
    onChange: sliderOnChange,
    min: 0,
    max: (_CUSTOM_VALUE_SETTING = _utils.CUSTOM_VALUE_SETTINGS[parsedUnit !== null && parsedUnit !== void 0 ? parsedUnit : 'px']?.max) !== null && _CUSTOM_VALUE_SETTING !== void 0 ? _CUSTOM_VALUE_SETTING : 10,
    step: (_CUSTOM_VALUE_SETTING2 = _utils.CUSTOM_VALUE_SETTINGS[parsedUnit !== null && parsedUnit !== void 0 ? parsedUnit : 'px']?.step) !== null && _CUSTOM_VALUE_SETTING2 !== void 0 ? _CUSTOM_VALUE_SETTING2 : 0.1,
    value: parsedQuantity !== null && parsedQuantity !== void 0 ? parsedQuantity : 0,
    withInputField: false
  }));
}
//# sourceMappingURL=all-input-control.js.map