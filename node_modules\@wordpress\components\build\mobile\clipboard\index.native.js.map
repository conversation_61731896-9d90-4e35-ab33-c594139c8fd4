{"version": 3, "names": ["createClipboard", "currentClipboard", "setClipboard", "clipboard", "getClipboard", "exports"], "sources": ["@wordpress/components/src/mobile/clipboard/index.native.js"], "sourcesContent": ["const createClipboard = () => {\n\tlet currentClipboard;\n\n\tconst setClipboard = ( clipboard ) => {\n\t\tcurrentClipboard = clipboard;\n\t};\n\n\tconst getClipboard = () => currentClipboard;\n\n\treturn {\n\t\tsetClipboard,\n\t\tgetClipboard,\n\t};\n};\n\nconst clipboard = createClipboard();\n\nexport const { setClipboard, getClipboard } = clipboard;\n"], "mappings": ";;;;;;AAAA,MAAMA,eAAe,GAAGA,CAAA,KAAM;EAC7B,IAAIC,gBAAgB;EAEpB,MAAMC,YAAY,GAAKC,SAAS,IAAM;IACrCF,gBAAgB,GAAGE,SAAS;EAC7B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAMH,gBAAgB;EAE3C,OAAO;IACNC,YAAY;IACZE;EACD,CAAC;AACF,CAAC;AAED,MAAMD,SAAS,GAAGH,eAAe,CAAC,CAAC;AAE5B,MAAM;EAAEE,YAAY;EAAEE;AAAa,CAAC,GAAGD,SAAS;AAACE,OAAA,CAAAD,YAAA,GAAAA,YAAA;AAAAC,OAAA,CAAAH,YAAA,GAAAA,YAAA"}