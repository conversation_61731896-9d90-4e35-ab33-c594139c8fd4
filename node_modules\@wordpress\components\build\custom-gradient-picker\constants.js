"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.HORIZONTAL_GRADIENT_ORIENTATION = exports.GRADIENT_OPTIONS = exports.DIRECTIONAL_ORIENTATION_ANGLE_MAP = exports.DEFAULT_LINEAR_GRADIENT_ANGLE = exports.DEFAULT_GRADIENT = void 0;
var _i18n = require("@wordpress/i18n");
/**
 * WordPress dependencies
 */

const DEFAULT_GRADIENT = 'linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%)';
exports.DEFAULT_GRADIENT = DEFAULT_GRADIENT;
const DEFAULT_LINEAR_GRADIENT_ANGLE = 180;
exports.DEFAULT_LINEAR_GRADIENT_ANGLE = DEFAULT_LINEAR_GRADIENT_ANGLE;
const HORIZONTAL_GRADIENT_ORIENTATION = {
  type: 'angular',
  value: '90'
};
exports.HORIZONTAL_GRADIENT_ORIENTATION = HORIZONTAL_GRADIENT_ORIENTATION;
const GRADIENT_OPTIONS = [{
  value: 'linear-gradient',
  label: (0, _i18n.__)('Linear')
}, {
  value: 'radial-gradient',
  label: (0, _i18n.__)('Radial')
}];
exports.GRADIENT_OPTIONS = GRADIENT_OPTIONS;
const DIRECTIONAL_ORIENTATION_ANGLE_MAP = {
  top: 0,
  'top right': 45,
  'right top': 45,
  right: 90,
  'right bottom': 135,
  'bottom right': 135,
  bottom: 180,
  'bottom left': 225,
  'left bottom': 225,
  left: 270,
  'top left': 315,
  'left top': 315
};
exports.DIRECTIONAL_ORIENTATION_ANGLE_MAP = DIRECTIONAL_ORIENTATION_ANGLE_MAP;
//# sourceMappingURL=constants.js.map