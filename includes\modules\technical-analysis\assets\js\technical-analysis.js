/**
 * JavaScript pour le module d'analyse technique Boss SEO
 * Interface professionnelle à onglets avec débogage complet
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/modules/technical-analysis/assets/js
 * @since      1.2.0
 */

(function($) {
    'use strict';

    console.log('🚀 Boss SEO Technical Analysis: Script chargé');

    // Variables globales
    let currentTab = 'global-analysis';
    let pagesData = [];
    let isAnalyzing = false;

    // Configuration de l'API
    const apiConfig = {
        baseUrl: bossTechnicalAnalysis?.apiUrl || '/wp-json/boss-seo/v2/',
        nonce: bossTechnicalAnalysis?.nonce || '',
        debug: bossTechnicalAnalysis?.debug || false
    };

    console.log('🔧 Configuration API:', apiConfig);

    /**
     * Initialisation du module
     */
    function init() {
        console.log('🎯 Initialisation du module d\'analyse technique');

        // Vérifier que la configuration est disponible
        if (typeof bossTechnicalAnalysis === 'undefined') {
            console.error('❌ Configuration bossTechnicalAnalysis non disponible');
            showMessage('Erreur de configuration du module', 'error');
            return;
        }

        console.log('✅ Configuration disponible:', bossTechnicalAnalysis);

        // Initialiser les onglets
        initTabs();

        // Charger les pages
        loadPages();

        // Initialiser les événements
        initEvents();

        console.log('✅ Module initialisé avec succès');
    }

    /**
     * Initialise le système d'onglets
     */
    function initTabs() {
        console.log('📋 Initialisation des onglets');

        const tabItems = document.querySelectorAll('.boss-tab-item');
        const tabContents = document.querySelectorAll('.boss-tab-content');

        console.log(`📋 Onglets trouvés: ${tabItems.length}, Contenus: ${tabContents.length}`);

        tabItems.forEach(function(tabItem) {
            tabItem.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');
                console.log(`🔄 Changement d'onglet vers: ${tabId}`);
                switchTab(tabId);
            });
        });
    }

    /**
     * Change d'onglet
     */
    function switchTab(tabId) {
        console.log(`🔄 Activation de l'onglet: ${tabId}`);

        // Désactiver tous les onglets
        document.querySelectorAll('.boss-tab-item').forEach(function(item) {
            item.classList.remove('active');
        });

        document.querySelectorAll('.boss-tab-content').forEach(function(content) {
            content.classList.remove('active');
        });

        // Activer l'onglet sélectionné
        const activeTabItem = document.querySelector(`[data-tab="${tabId}"]`);
        const activeTabContent = document.getElementById(tabId);

        if (activeTabItem && activeTabContent) {
            activeTabItem.classList.add('active');
            activeTabContent.classList.add('active');
            currentTab = tabId;
            console.log(`✅ Onglet ${tabId} activé`);
        } else {
            console.error(`❌ Onglet ${tabId} non trouvé`);
        }
    }

    /**
     * Charge la liste des pages
     */
    async function loadPages() {
        console.log('📄 Chargement de la liste des pages');

        try {
            showLoader('Chargement des pages...');

            const response = await apiCall('technical/pages', 'GET');
            console.log('📄 Réponse API pages:', response);

            if (response.success && response.data) {
                pagesData = response.data;
                console.log(`📄 ${pagesData.length} pages chargées`);
                populatePageSelectors();
            } else {
                console.error('❌ Erreur lors du chargement des pages:', response);
                showMessage('Erreur lors du chargement des pages', 'error');
            }
        } catch (error) {
            console.error('❌ Erreur API pages:', error);
            showMessage('Erreur de connexion à l\'API', 'error');
        } finally {
            hideLoader();
        }
    }

    /**
     * Remplit les sélecteurs de pages
     */
    function populatePageSelectors() {
        console.log('📄 Remplissage des sélecteurs de pages');

        const selectors = [
            'global-page-selector',
            'pagespeed-page-selector',
            'ai-page-selector',
            'technical-page-selector'
        ];

        selectors.forEach(function(selectorId) {
            const selector = document.getElementById(selectorId);
            if (selector) {
                selector.innerHTML = '<option value="">Sélectionnez une page</option>';
                
                pagesData.forEach(function(page) {
                    const option = document.createElement('option');
                    option.value = page.url;
                    option.textContent = `${page.title} (${page.type})`;
                    selector.appendChild(option);
                });

                console.log(`📄 Sélecteur ${selectorId} rempli avec ${pagesData.length} pages`);
            } else {
                console.warn(`⚠️ Sélecteur ${selectorId} non trouvé`);
            }
        });

        // Activer les boutons d'analyse
        enableAnalyzeButtons();
    }

    /**
     * Active les boutons d'analyse
     */
    function enableAnalyzeButtons() {
        const buttons = [
            'global-analyze-btn',
            'pagespeed-analyze-btn',
            'ai-analyze-btn',
            'technical-analyze-btn'
        ];

        buttons.forEach(function(buttonId) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = false;
                console.log(`🔘 Bouton ${buttonId} activé`);
            }
        });
    }

    /**
     * Initialise les événements
     */
    function initEvents() {
        console.log('🎮 Initialisation des événements');

        // Boutons d'analyse
        const analyzeButtons = {
            'global-analyze-btn': () => analyzeGlobal(),
            'pagespeed-analyze-btn': () => analyzePageSpeed(),
            'ai-analyze-btn': () => analyzeAI(),
            'technical-analyze-btn': () => analyzeTechnical()
        };

        Object.keys(analyzeButtons).forEach(function(buttonId) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.addEventListener('click', analyzeButtons[buttonId]);
                console.log(`🎮 Événement ajouté pour ${buttonId}`);
            } else {
                console.warn(`⚠️ Bouton ${buttonId} non trouvé`);
            }
        });
    }

    /**
     * Analyse globale
     */
    async function analyzeGlobal() {
        console.log('🔍 Démarrage de l\'analyse globale');
        
        const pageSelector = document.getElementById('global-page-selector');
        const url = pageSelector.value;

        if (!url) {
            console.warn('⚠️ Aucune page sélectionnée pour l\'analyse globale');
            showMessage('Veuillez sélectionner une page', 'warning');
            return;
        }

        console.log(`🔍 Analyse globale de: ${url}`);
        await performAnalysis('global', url, 'mobile', true);
    }

    /**
     * Analyse PageSpeed
     */
    async function analyzePageSpeed() {
        console.log('⚡ Démarrage de l\'analyse PageSpeed');
        
        const pageSelector = document.getElementById('pagespeed-page-selector');
        const strategySelector = document.getElementById('pagespeed-strategy');
        const url = pageSelector.value;
        const strategy = strategySelector.value;

        if (!url) {
            console.warn('⚠️ Aucune page sélectionnée pour PageSpeed');
            showMessage('Veuillez sélectionner une page', 'warning');
            return;
        }

        console.log(`⚡ Analyse PageSpeed de: ${url} (${strategy})`);
        await performAnalysis('pagespeed', url, strategy, false);
    }

    /**
     * Analyse IA
     */
    async function analyzeAI() {
        console.log('🤖 Démarrage de l\'analyse IA');
        
        const pageSelector = document.getElementById('ai-page-selector');
        const url = pageSelector.value;

        if (!url) {
            console.warn('⚠️ Aucune page sélectionnée pour l\'IA');
            showMessage('Veuillez sélectionner une page', 'warning');
            return;
        }

        console.log(`🤖 Analyse IA de: ${url}`);
        await performAnalysis('ai', url, 'mobile', true);
    }

    /**
     * Analyse technique
     */
    async function analyzeTechnical() {
        console.log('🔧 Démarrage de l\'analyse technique');
        
        const pageSelector = document.getElementById('technical-page-selector');
        const url = pageSelector.value;

        if (!url) {
            console.warn('⚠️ Aucune page sélectionnée pour l\'analyse technique');
            showMessage('Veuillez sélectionner une page', 'warning');
            return;
        }

        console.log(`🔧 Analyse technique de: ${url}`);
        await performAnalysis('technical', url, 'mobile', false);
    }

    /**
     * Effectue une analyse
     */
    async function performAnalysis(type, url, strategy, includeAI) {
        if (isAnalyzing) {
            console.warn('⚠️ Une analyse est déjà en cours');
            return;
        }

        isAnalyzing = true;
        console.log(`🚀 Démarrage de l'analyse ${type} pour ${url}`);

        try {
            showLoader(`Analyse ${type} en cours...`);

            const data = {
                url: url,
                strategy: strategy,
                include_ai_suggestions: includeAI
            };

            console.log(`📤 Données envoyées:`, data);

            const response = await apiCall('technical/analyze', 'POST', data);
            console.log(`📥 Réponse reçue:`, response);

            if (response.success) {
                console.log(`✅ Analyse ${type} réussie`);
                displayResults(type, response);
                showMessage('Analyse terminée avec succès', 'success');
            } else {
                console.error(`❌ Erreur analyse ${type}:`, response);
                showMessage(`Erreur: ${response.message || 'Analyse échouée'}`, 'error');
            }
        } catch (error) {
            console.error(`❌ Erreur API analyse ${type}:`, error);
            showMessage('Erreur de connexion à l\'API', 'error');
        } finally {
            isAnalyzing = false;
            hideLoader();
        }
    }

    /**
     * Affiche les résultats
     */
    function displayResults(type, data) {
        console.log(`📊 Affichage des résultats ${type}:`, data);

        const resultContainers = {
            'global': 'global-results',
            'pagespeed': 'pagespeed-results',
            'ai': 'ai-results',
            'technical': 'technical-results'
        };

        const containerId = resultContainers[type];
        const container = document.getElementById(containerId);

        if (!container) {
            console.error(`❌ Conteneur de résultats ${containerId} non trouvé`);
            return;
        }

        // Générer le HTML des résultats
        let html = generateResultsHTML(type, data);
        
        container.innerHTML = html;
        container.style.display = 'block';

        console.log(`✅ Résultats ${type} affichés dans ${containerId}`);
    }

    /**
     * Génère le HTML des résultats
     */
    function generateResultsHTML(type, data) {
        console.log(`🎨 Génération HTML pour ${type}`);
        
        let html = `<h3>Résultats de l'analyse ${type}</h3>`;
        
        // Ajouter les résultats selon le type
        if (data.pagespeed) {
            html += generatePageSpeedHTML(data.pagespeed);
        }
        
        if (data.technical) {
            html += generateTechnicalHTML(data.technical);
        }
        
        if (data.ai_suggestions) {
            html += generateAIHTML(data.ai_suggestions);
        }
        
        if (data.analysis_time) {
            html += `<p><small>⏱️ Analyse terminée en ${data.analysis_time}ms</small></p>`;
        }
        
        return html;
    }

    /**
     * Génère le HTML PageSpeed
     */
    function generatePageSpeedHTML(pagespeed) {
        let html = '<div class="pagespeed-results"><h4>📊 Scores PageSpeed</h4>';
        
        if (pagespeed.error) {
            html += `<div class="notice notice-warning"><p>⚠️ ${pagespeed.error}</p></div>`;
        }
        
        if (pagespeed.lighthouseResult && pagespeed.lighthouseResult.categories) {
            const categories = pagespeed.lighthouseResult.categories;
            html += '<div class="scores-grid">';
            
            Object.entries(categories).forEach(([key, category]) => {
                const score = Math.round(category.score * 100);
                const scoreClass = score >= 80 ? 'good' : score >= 50 ? 'average' : 'poor';
                const categoryName = {
                    'performance': 'Performance',
                    'seo': 'SEO',
                    'accessibility': 'Accessibilité',
                    'best-practices': 'Bonnes Pratiques'
                }[key] || key;
                
                html += `<div class="score-item ${scoreClass}">
                    <div class="score-value">${score}</div>
                    <div class="score-label">${categoryName}</div>
                </div>`;
            });
            
            html += '</div>';
        }
        
        html += '</div>';
        return html;
    }

    /**
     * Génère le HTML technique
     */
    function generateTechnicalHTML(technical) {
        let html = '<div class="technical-results"><h4>🔧 Analyse Technique</h4>';
        
        if (technical.ssl_check) {
            const sslStatus = technical.ssl_check.valid ? '✅ SSL valide' : '❌ SSL invalide';
            html += `<p>🔒 ${sslStatus}</p>`;
        }
        
        if (technical.meta_tags) {
            const meta = technical.meta_tags;
            html += '<div class="meta-tags"><h5>📝 Meta Tags</h5><ul>';
            if (meta.title) html += `<li>Titre: ${meta.title}</li>`;
            if (meta.description) html += `<li>Description: ${meta.description}</li>`;
            if (meta.viewport) html += '<li>✅ Viewport configuré</li>';
            html += '</ul></div>';
        }
        
        html += '</div>';
        return html;
    }

    /**
     * Génère le HTML IA
     */
    function generateAIHTML(aiSuggestions) {
        let html = '<div class="ai-results"><h4>🤖 Suggestions IA</h4>';
        
        if (aiSuggestions.performance && aiSuggestions.performance.length > 0) {
            html += '<div class="ai-suggestions"><h5>⚡ Performance</h5><ul>';
            aiSuggestions.performance.forEach(suggestion => {
                html += `<li>💡 ${suggestion}</li>`;
            });
            html += '</ul></div>';
        }
        
        if (aiSuggestions.seo && aiSuggestions.seo.length > 0) {
            html += '<div class="ai-suggestions"><h5>🎯 SEO</h5><ul>';
            aiSuggestions.seo.forEach(suggestion => {
                html += `<li>💡 ${suggestion}</li>`;
            });
            html += '</ul></div>';
        }
        
        html += '</div>';
        return html;
    }

    /**
     * Appel API générique
     */
    async function apiCall(endpoint, method = 'GET', data = null) {
        console.log(`🌐 Appel API: ${method} ${endpoint}`, data);

        const url = apiConfig.baseUrl + endpoint;
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': apiConfig.nonce
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        console.log(`🌐 URL: ${url}`, options);

        try {
            const response = await fetch(url, options);
            console.log(`🌐 Statut réponse: ${response.status}`);
            
            const result = await response.json();
            console.log(`🌐 Réponse JSON:`, result);
            
            return result;
        } catch (error) {
            console.error('🌐 Erreur API:', error);
            throw error;
        }
    }

    /**
     * Affiche un message
     */
    function showMessage(message, type = 'info') {
        console.log(`💬 Message ${type}: ${message}`);
        
        const container = document.getElementById('boss-technical-messages');
        if (!container) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `notice notice-${type} is-dismissible`;
        messageDiv.innerHTML = `<p>${message}</p>`;
        
        container.appendChild(messageDiv);
        
        // Auto-supprimer après 5 secondes
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 5000);
    }

    /**
     * Affiche le loader
     */
    function showLoader(text = 'Chargement...') {
        console.log(`⏳ Affichage loader: ${text}`);
        
        const loader = document.getElementById('boss-global-loader');
        if (loader) {
            const loaderText = loader.querySelector('.boss-loader-text');
            if (loaderText) {
                loaderText.textContent = text;
            }
            loader.style.display = 'flex';
        }
    }

    /**
     * Cache le loader
     */
    function hideLoader() {
        console.log('⏳ Masquage loader');
        
        const loader = document.getElementById('boss-global-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }

    // Initialisation au chargement du DOM
    $(document).ready(function() {
        console.log('📋 DOM ready - Initialisation du module');
        init();
    });

    console.log('✅ Script d\'analyse technique Boss SEO chargé complètement');

})(jQuery);
