<?php
/**
 * Gestionnaire Google Search Console
 *
 * Gère les appels API vers Google Search Console et le traitement des données
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/analytics
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe de gestion Google Search Console
 */
class Boss_GSC_Manager {

    /**
     * URL de base de l'API Google Search Console
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $api_base_url    URL de base de l'API
     */
    private $api_base_url = 'https://searchconsole.googleapis.com/webmasters/v3';

    /**
     * Gestionnaire d'authentification
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Auth    $auth_manager    Gestionnaire d'auth
     */
    private $auth_manager;

    /**
     * Gestionnaire de cache
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Cache    $cache_manager    Gestionnaire de cache
     */
    private $cache_manager;

    /**
     * Constructeur
     *
     * @since    1.2.0
     */
    public function __construct() {
        $this->auth_manager = new Boss_Analytics_Auth();
        $this->cache_manager = new Boss_Analytics_Cache();
    }

    /**
     * Teste la connexion à GSC
     *
     * @since    1.2.0
     * @return   array    Résultat du test
     */
    public function test_connection() {
        try {
            $access_token = $this->auth_manager->get_valid_access_token('gsc');
            
            if (!$access_token) {
                throw new Exception(__('Token d\'accès GSC invalide', 'boss-seo'));
            }

            // Lister les sites disponibles
            $response = wp_remote_get($this->api_base_url . '/sites', array(
                'timeout' => 30,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $access_token,
                    'Content-Type' => 'application/json'
                )
            ));

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            $status_code = wp_remote_retrieve_response_code($response);
            
            if ($status_code !== 200) {
                throw new Exception(sprintf(__('Erreur API GSC: %d', 'boss-seo'), $status_code));
            }

            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception(__('Réponse JSON invalide de GSC', 'boss-seo'));
            }

            // Extraire les propriétés
            $properties = $this->extract_properties_from_sites($data);

            return array(
                'success' => true,
                'message' => __('Connexion GSC réussie', 'boss-seo'),
                'properties' => $properties
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Récupère les données de mots-clés GSC
     *
     * @since    1.2.0
     * @param    string    $property_url    URL de la propriété
     * @param    string    $date_range      Plage de dates
     * @param    int       $limit           Limite de résultats
     * @return   array|WP_Error             Données ou erreur
     */
    public function get_keywords_data($property_url = null, $date_range = 'last30days', $limit = 100) {
        try {
            $cache_key = "gsc_keywords_" . md5($property_url . $date_range . $limit);
            $cached_data = $this->cache_manager->get($cache_key);
            
            if ($cached_data !== false) {
                return $cached_data;
            }

            if (!$property_url) {
                $property_url = $this->get_default_property_url();
                if (!$property_url) {
                    throw new Exception(__('Aucune propriété GSC configurée', 'boss-seo'));
                }
            }

            $date_ranges = $this->convert_date_range($date_range);

            // Préparer la requête pour les mots-clés
            $request_body = array(
                'startDate' => $date_ranges['start_date'],
                'endDate' => $date_ranges['end_date'],
                'dimensions' => array('query'),
                'rowLimit' => $limit,
                'startRow' => 0
            );

            $response_data = $this->make_api_request($property_url, 'searchAnalytics/query', $request_body);

            if (is_wp_error($response_data)) {
                throw new Exception($response_data->get_error_message());
            }

            $processed_data = $this->process_keywords_data($response_data);

            // Mettre en cache pour 2 heures
            $this->cache_manager->set($cache_key, $processed_data, 2 * HOUR_IN_SECONDS);

            return $processed_data;

        } catch (Exception $e) {
            return new WP_Error(
                'gsc_keywords_error',
                $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Récupère les données de performance GSC
     *
     * @since    1.2.0
     * @param    string    $property_url    URL de la propriété
     * @param    string    $date_range      Plage de dates
     * @return   array|WP_Error             Données ou erreur
     */
    public function get_performance_data($property_url = null, $date_range = 'last30days') {
        try {
            $cache_key = "gsc_performance_" . md5($property_url . $date_range);
            $cached_data = $this->cache_manager->get($cache_key);
            
            if ($cached_data !== false) {
                return $cached_data;
            }

            if (!$property_url) {
                $property_url = $this->get_default_property_url();
                if (!$property_url) {
                    throw new Exception(__('Aucune propriété GSC configurée', 'boss-seo'));
                }
            }

            $date_ranges = $this->convert_date_range($date_range);

            // Requête pour les données de performance globales
            $request_body = array(
                'startDate' => $date_ranges['start_date'],
                'endDate' => $date_ranges['end_date'],
                'dimensions' => array('date'),
                'rowLimit' => 1000,
                'startRow' => 0
            );

            $response_data = $this->make_api_request($property_url, 'searchAnalytics/query', $request_body);

            if (is_wp_error($response_data)) {
                throw new Exception($response_data->get_error_message());
            }

            $processed_data = $this->process_performance_data($response_data);

            $this->cache_manager->set($cache_key, $processed_data, 2 * HOUR_IN_SECONDS);

            return $processed_data;

        } catch (Exception $e) {
            return new WP_Error(
                'gsc_performance_error',
                $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Récupère les données des pages GSC
     *
     * @since    1.2.0
     * @param    string    $property_url    URL de la propriété
     * @param    string    $date_range      Plage de dates
     * @param    int       $limit           Limite de résultats
     * @return   array|WP_Error             Données ou erreur
     */
    public function get_pages_data($property_url = null, $date_range = 'last30days', $limit = 50) {
        try {
            $cache_key = "gsc_pages_" . md5($property_url . $date_range . $limit);
            $cached_data = $this->cache_manager->get($cache_key);
            
            if ($cached_data !== false) {
                return $cached_data;
            }

            if (!$property_url) {
                $property_url = $this->get_default_property_url();
                if (!$property_url) {
                    throw new Exception(__('Aucune propriété GSC configurée', 'boss-seo'));
                }
            }

            $date_ranges = $this->convert_date_range($date_range);

            $request_body = array(
                'startDate' => $date_ranges['start_date'],
                'endDate' => $date_ranges['end_date'],
                'dimensions' => array('page'),
                'rowLimit' => $limit,
                'startRow' => 0
            );

            $response_data = $this->make_api_request($property_url, 'searchAnalytics/query', $request_body);

            if (is_wp_error($response_data)) {
                throw new Exception($response_data->get_error_message());
            }

            $processed_data = $this->process_pages_data($response_data);

            $this->cache_manager->set($cache_key, $processed_data, 2 * HOUR_IN_SECONDS);

            return $processed_data;

        } catch (Exception $e) {
            return new WP_Error(
                'gsc_pages_error',
                $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Fait un appel à l'API GSC
     *
     * @since    1.2.0
     * @param    string    $property_url    URL de la propriété
     * @param    string    $endpoint        Endpoint API
     * @param    array     $body            Corps de la requête
     * @return   array|WP_Error             Réponse ou erreur
     */
    private function make_api_request($property_url, $endpoint, $body) {
        $access_token = $this->auth_manager->get_valid_access_token('gsc');
        
        if (!$access_token) {
            return new WP_Error(
                'gsc_auth_error',
                __('Token d\'accès GSC invalide', 'boss-seo'),
                array('status' => 401)
            );
        }

        $encoded_url = urlencode($property_url);
        $url = $this->api_base_url . '/sites/' . $encoded_url . '/' . $endpoint;

        $response = wp_remote_post($url, array(
            'timeout' => 30,
            'headers' => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type' => 'application/json'
            ),
            'body' => wp_json_encode($body)
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($status_code === 401) {
            // Token expiré, essayer de le rafraîchir
            if ($this->auth_manager->refresh_access_token('gsc')) {
                // Réessayer la requête avec le nouveau token
                return $this->make_api_request($property_url, $endpoint, $body);
            } else {
                return new WP_Error(
                    'gsc_auth_expired',
                    __('Token GSC expiré et impossible à rafraîchir', 'boss-seo'),
                    array('status' => 401)
                );
            }
        }

        if ($status_code !== 200) {
            return new WP_Error(
                'gsc_api_error',
                sprintf(__('Erreur API GSC: %d - %s', 'boss-seo'), $status_code, $response_body),
                array('status' => $status_code)
            );
        }

        $data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error(
                'gsc_json_error',
                __('Réponse JSON invalide de GSC', 'boss-seo'),
                array('status' => 500)
            );
        }

        return $data;
    }

    /**
     * Convertit une plage de dates en format GSC
     *
     * @since    1.2.0
     * @param    string    $date_range    Plage de dates
     * @return   array                    Plage formatée
     */
    private function convert_date_range($date_range) {
        $end_date = date('Y-m-d', strtotime('-1 day')); // Hier
        
        switch ($date_range) {
            case 'last7days':
                $start_date = date('Y-m-d', strtotime('-8 days'));
                break;
            case 'last30days':
                $start_date = date('Y-m-d', strtotime('-31 days'));
                break;
            case 'last90days':
                $start_date = date('Y-m-d', strtotime('-91 days'));
                break;
            case 'last12months':
                $start_date = date('Y-m-d', strtotime('-366 days'));
                break;
            default:
                $start_date = date('Y-m-d', strtotime('-31 days'));
        }

        return array(
            'start_date' => $start_date,
            'end_date' => $end_date
        );
    }

    /**
     * Obtient l'URL de la propriété par défaut
     *
     * @since    1.2.0
     * @return   string|false    URL de la propriété ou false
     */
    private function get_default_property_url() {
        $properties = $this->auth_manager->get_service_properties('gsc');
        
        if (empty($properties)) {
            // Utiliser l'URL du site par défaut
            return home_url();
        }

        // Chercher la propriété sélectionnée
        foreach ($properties as $property) {
            if (isset($property['selected']) && $property['selected']) {
                return $property['url'];
            }
        }

        // Prendre la première propriété si aucune n'est sélectionnée
        return isset($properties[0]['url']) ? $properties[0]['url'] : home_url();
    }

    /**
     * Extrait les propriétés depuis les sites
     *
     * @since    1.2.0
     * @param    array    $sites_data    Données des sites
     * @return   array                   Propriétés extraites
     */
    private function extract_properties_from_sites($sites_data) {
        $properties = array();
        $current_site_url = home_url();
        
        if (isset($sites_data['siteEntry']) && is_array($sites_data['siteEntry'])) {
            foreach ($sites_data['siteEntry'] as $site) {
                if (isset($site['siteUrl'])) {
                    $properties[] = array(
                        'url' => $site['siteUrl'],
                        'name' => $site['siteUrl'],
                        'selected' => (rtrim($site['siteUrl'], '/') === rtrim($current_site_url, '/'))
                    );
                }
            }
        }

        // Si aucune propriété trouvée, ajouter le site actuel
        if (empty($properties)) {
            $properties[] = array(
                'url' => $current_site_url,
                'name' => $current_site_url,
                'selected' => true
            );
        }

        return $properties;
    }

    /**
     * Traite les données de mots-clés
     *
     * @since    1.2.0
     * @param    array    $raw_data    Données brutes de l'API
     * @return   array                 Données traitées
     */
    private function process_keywords_data($raw_data) {
        $processed = array();

        if (isset($raw_data['rows']) && is_array($raw_data['rows'])) {
            foreach ($raw_data['rows'] as $row) {
                if (isset($row['keys']) && isset($row['clicks'])) {
                    $keyword = $row['keys'][0] ?? '';
                    
                    $processed[] = array(
                        'keyword' => $keyword,
                        'clicks' => intval($row['clicks'] ?? 0),
                        'impressions' => intval($row['impressions'] ?? 0),
                        'ctr' => floatval($row['ctr'] ?? 0) * 100, // Convertir en pourcentage
                        'position' => floatval($row['position'] ?? 0)
                    );
                }
            }
        }

        return $processed;
    }

    /**
     * Traite les données de performance
     *
     * @since    1.2.0
     * @param    array    $raw_data    Données brutes de l'API
     * @return   array                 Données traitées
     */
    private function process_performance_data($raw_data) {
        $processed = array(
            'summary' => array(
                'total_clicks' => 0,
                'total_impressions' => 0,
                'avg_ctr' => 0,
                'avg_position' => 0
            ),
            'timeline' => array()
        );

        if (isset($raw_data['rows']) && is_array($raw_data['rows'])) {
            $total_ctr = 0;
            $total_position = 0;
            $count = 0;

            foreach ($raw_data['rows'] as $row) {
                if (isset($row['keys']) && isset($row['clicks'])) {
                    $date = $row['keys'][0] ?? '';
                    $clicks = intval($row['clicks'] ?? 0);
                    $impressions = intval($row['impressions'] ?? 0);
                    $ctr = floatval($row['ctr'] ?? 0) * 100;
                    $position = floatval($row['position'] ?? 0);

                    $processed['timeline'][] = array(
                        'date' => $date,
                        'clicks' => $clicks,
                        'impressions' => $impressions,
                        'ctr' => $ctr,
                        'position' => $position
                    );

                    // Ajouter aux totaux
                    $processed['summary']['total_clicks'] += $clicks;
                    $processed['summary']['total_impressions'] += $impressions;
                    $total_ctr += $ctr;
                    $total_position += $position;
                    $count++;
                }
            }

            // Calculer les moyennes
            if ($count > 0) {
                $processed['summary']['avg_ctr'] = $total_ctr / $count;
                $processed['summary']['avg_position'] = $total_position / $count;
            }
        }

        return $processed;
    }

    /**
     * Traite les données des pages
     *
     * @since    1.2.0
     * @param    array    $raw_data    Données brutes de l'API
     * @return   array                 Données traitées
     */
    private function process_pages_data($raw_data) {
        $processed = array();

        if (isset($raw_data['rows']) && is_array($raw_data['rows'])) {
            foreach ($raw_data['rows'] as $row) {
                if (isset($row['keys']) && isset($row['clicks'])) {
                    $page_url = $row['keys'][0] ?? '';
                    
                    $processed[] = array(
                        'url' => $page_url,
                        'clicks' => intval($row['clicks'] ?? 0),
                        'impressions' => intval($row['impressions'] ?? 0),
                        'ctr' => floatval($row['ctr'] ?? 0) * 100,
                        'position' => floatval($row['position'] ?? 0)
                    );
                }
            }
        }

        return $processed;
    }
}
