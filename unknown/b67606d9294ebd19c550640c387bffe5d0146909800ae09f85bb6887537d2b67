<?php
/**
 * Gestionnaire Google Analytics 4
 *
 * Gère les appels API vers Google Analytics 4 et le traitement des données
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/analytics
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe de gestion Google Analytics 4
 */
class Boss_GA4_Manager {

    /**
     * URL de base de l'API Google Analytics
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $api_base_url    URL de base de l'API
     */
    private $api_base_url = 'https://analyticsdata.googleapis.com/v1beta';

    /**
     * Gestionnaire d'authentification
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Auth    $auth_manager    Gestionnaire d'auth
     */
    private $auth_manager;

    /**
     * Gestionnaire de cache
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Cache    $cache_manager    Gestionnaire de cache
     */
    private $cache_manager;

    /**
     * Constructeur
     *
     * @since    1.2.0
     */
    public function __construct() {
        $this->auth_manager = new Boss_Analytics_Auth();
        $this->cache_manager = new Boss_Analytics_Cache();
    }

    /**
     * Teste la connexion à GA4
     *
     * @since    1.2.0
     * @return   array    Résultat du test
     */
    public function test_connection() {
        try {
            $access_token = $this->auth_manager->get_valid_access_token('ga4');
            
            if (!$access_token) {
                throw new Exception(__('Token d\'accès GA4 invalide', 'boss-seo'));
            }

            // Appeler l'API pour lister les propriétés
            $response = wp_remote_get('https://analyticsadmin.googleapis.com/v1beta/accounts', array(
                'timeout' => 30,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $access_token,
                    'Content-Type' => 'application/json'
                )
            ));

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            $status_code = wp_remote_retrieve_response_code($response);
            
            if ($status_code !== 200) {
                throw new Exception(sprintf(__('Erreur API GA4: %d', 'boss-seo'), $status_code));
            }

            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception(__('Réponse JSON invalide de GA4', 'boss-seo'));
            }

            // Extraire les propriétés
            $properties = $this->extract_properties_from_accounts($data);

            return array(
                'success' => true,
                'message' => __('Connexion GA4 réussie', 'boss-seo'),
                'properties' => $properties
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Récupère les données d'aperçu GA4
     *
     * @since    1.2.0
     * @param    string    $property_id    ID de la propriété
     * @param    string    $date_range     Plage de dates
     * @return   array|WP_Error            Données ou erreur
     */
    public function get_overview_data($property_id = null, $date_range = 'last30days') {
        try {
            // Vérifier le cache
            $cache_key = "ga4_overview_{$property_id}_{$date_range}";
            $cached_data = $this->cache_manager->get($cache_key);
            
            if ($cached_data !== false) {
                return $cached_data;
            }

            // Obtenir la propriété par défaut si non spécifiée
            if (!$property_id) {
                $property_id = $this->get_default_property_id();
                if (!$property_id) {
                    throw new Exception(__('Aucune propriété GA4 configurée', 'boss-seo'));
                }
            }

            // Convertir la plage de dates
            $date_ranges = $this->convert_date_range($date_range);

            // Préparer la requête pour les métriques principales
            $request_body = array(
                'dateRanges' => array($date_ranges),
                'metrics' => array(
                    array('name' => 'activeUsers'),
                    array('name' => 'sessions'),
                    array('name' => 'screenPageViews'),
                    array('name' => 'bounceRate'),
                    array('name' => 'averageSessionDuration'),
                    array('name' => 'newUsers')
                ),
                'dimensions' => array(
                    array('name' => 'date')
                ),
                'orderBys' => array(
                    array(
                        'dimension' => array('dimensionName' => 'date'),
                        'desc' => false
                    )
                )
            );

            // Faire l'appel API
            $response_data = $this->make_api_request($property_id, 'runReport', $request_body);

            if (is_wp_error($response_data)) {
                throw new Exception($response_data->get_error_message());
            }

            // Traiter les données
            $processed_data = $this->process_overview_data($response_data);

            // Mettre en cache pour 1 heure
            $this->cache_manager->set($cache_key, $processed_data, HOUR_IN_SECONDS);

            return $processed_data;

        } catch (Exception $e) {
            return new WP_Error(
                'ga4_overview_error',
                $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Récupère les données de trafic GA4
     *
     * @since    1.2.0
     * @param    string    $property_id    ID de la propriété
     * @param    string    $date_range     Plage de dates
     * @return   array|WP_Error            Données ou erreur
     */
    public function get_traffic_data($property_id = null, $date_range = 'last30days') {
        try {
            $cache_key = "ga4_traffic_{$property_id}_{$date_range}";
            $cached_data = $this->cache_manager->get($cache_key);
            
            if ($cached_data !== false) {
                return $cached_data;
            }

            if (!$property_id) {
                $property_id = $this->get_default_property_id();
                if (!$property_id) {
                    throw new Exception(__('Aucune propriété GA4 configurée', 'boss-seo'));
                }
            }

            $date_ranges = $this->convert_date_range($date_range);

            // Requête pour les sources de trafic
            $request_body = array(
                'dateRanges' => array($date_ranges),
                'metrics' => array(
                    array('name' => 'sessions'),
                    array('name' => 'activeUsers'),
                    array('name' => 'screenPageViews')
                ),
                'dimensions' => array(
                    array('name' => 'sessionDefaultChannelGrouping'),
                    array('name' => 'sessionSource')
                ),
                'orderBys' => array(
                    array(
                        'metric' => array('metricName' => 'sessions'),
                        'desc' => true
                    )
                ),
                'limit' => 20
            );

            $response_data = $this->make_api_request($property_id, 'runReport', $request_body);

            if (is_wp_error($response_data)) {
                throw new Exception($response_data->get_error_message());
            }

            $processed_data = $this->process_traffic_data($response_data);

            $this->cache_manager->set($cache_key, $processed_data, HOUR_IN_SECONDS);

            return $processed_data;

        } catch (Exception $e) {
            return new WP_Error(
                'ga4_traffic_error',
                $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Récupère les données des pages GA4
     *
     * @since    1.2.0
     * @param    string    $property_id    ID de la propriété
     * @param    string    $date_range     Plage de dates
     * @param    int       $limit          Limite de résultats
     * @return   array|WP_Error            Données ou erreur
     */
    public function get_pages_data($property_id = null, $date_range = 'last30days', $limit = 50) {
        try {
            $cache_key = "ga4_pages_{$property_id}_{$date_range}_{$limit}";
            $cached_data = $this->cache_manager->get($cache_key);
            
            if ($cached_data !== false) {
                return $cached_data;
            }

            if (!$property_id) {
                $property_id = $this->get_default_property_id();
                if (!$property_id) {
                    throw new Exception(__('Aucune propriété GA4 configurée', 'boss-seo'));
                }
            }

            $date_ranges = $this->convert_date_range($date_range);

            $request_body = array(
                'dateRanges' => array($date_ranges),
                'metrics' => array(
                    array('name' => 'screenPageViews'),
                    array('name' => 'activeUsers'),
                    array('name' => 'averageSessionDuration'),
                    array('name' => 'bounceRate')
                ),
                'dimensions' => array(
                    array('name' => 'pagePath'),
                    array('name' => 'pageTitle')
                ),
                'orderBys' => array(
                    array(
                        'metric' => array('metricName' => 'screenPageViews'),
                        'desc' => true
                    )
                ),
                'limit' => $limit
            );

            $response_data = $this->make_api_request($property_id, 'runReport', $request_body);

            if (is_wp_error($response_data)) {
                throw new Exception($response_data->get_error_message());
            }

            $processed_data = $this->process_pages_data($response_data);

            $this->cache_manager->set($cache_key, $processed_data, HOUR_IN_SECONDS);

            return $processed_data;

        } catch (Exception $e) {
            return new WP_Error(
                'ga4_pages_error',
                $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Fait un appel à l'API GA4
     *
     * @since    1.2.0
     * @param    string    $property_id    ID de la propriété
     * @param    string    $method         Méthode API
     * @param    array     $body           Corps de la requête
     * @return   array|WP_Error            Réponse ou erreur
     */
    private function make_api_request($property_id, $method, $body) {
        $access_token = $this->auth_manager->get_valid_access_token('ga4');
        
        if (!$access_token) {
            return new WP_Error(
                'ga4_auth_error',
                __('Token d\'accès GA4 invalide', 'boss-seo'),
                array('status' => 401)
            );
        }

        $url = $this->api_base_url . '/properties/' . $property_id . ':' . $method;

        $response = wp_remote_post($url, array(
            'timeout' => 30,
            'headers' => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type' => 'application/json'
            ),
            'body' => wp_json_encode($body)
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($status_code === 401) {
            // Token expiré, essayer de le rafraîchir
            if ($this->auth_manager->refresh_access_token('ga4')) {
                // Réessayer la requête avec le nouveau token
                return $this->make_api_request($property_id, $method, $body);
            } else {
                return new WP_Error(
                    'ga4_auth_expired',
                    __('Token GA4 expiré et impossible à rafraîchir', 'boss-seo'),
                    array('status' => 401)
                );
            }
        }

        if ($status_code !== 200) {
            return new WP_Error(
                'ga4_api_error',
                sprintf(__('Erreur API GA4: %d - %s', 'boss-seo'), $status_code, $response_body),
                array('status' => $status_code)
            );
        }

        $data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error(
                'ga4_json_error',
                __('Réponse JSON invalide de GA4', 'boss-seo'),
                array('status' => 500)
            );
        }

        return $data;
    }

    /**
     * Convertit une plage de dates en format GA4
     *
     * @since    1.2.0
     * @param    string    $date_range    Plage de dates
     * @return   array                    Plage formatée
     */
    private function convert_date_range($date_range) {
        switch ($date_range) {
            case 'last7days':
                return array(
                    'startDate' => '7daysAgo',
                    'endDate' => 'yesterday'
                );
            case 'last30days':
                return array(
                    'startDate' => '30daysAgo',
                    'endDate' => 'yesterday'
                );
            case 'last90days':
                return array(
                    'startDate' => '90daysAgo',
                    'endDate' => 'yesterday'
                );
            case 'last12months':
                return array(
                    'startDate' => '365daysAgo',
                    'endDate' => 'yesterday'
                );
            default:
                return array(
                    'startDate' => '30daysAgo',
                    'endDate' => 'yesterday'
                );
        }
    }

    /**
     * Obtient l'ID de la propriété par défaut
     *
     * @since    1.2.0
     * @return   string|false    ID de la propriété ou false
     */
    private function get_default_property_id() {
        $properties = $this->auth_manager->get_service_properties('ga4');
        
        if (empty($properties)) {
            return false;
        }

        // Chercher la propriété sélectionnée
        foreach ($properties as $property) {
            if (isset($property['selected']) && $property['selected']) {
                return $property['id'];
            }
        }

        // Prendre la première propriété si aucune n'est sélectionnée
        return isset($properties[0]['id']) ? $properties[0]['id'] : false;
    }

    /**
     * Extrait les propriétés depuis les comptes
     *
     * @since    1.2.0
     * @param    array    $accounts_data    Données des comptes
     * @return   array                      Propriétés extraites
     */
    private function extract_properties_from_accounts($accounts_data) {
        $properties = array();
        
        if (isset($accounts_data['accounts']) && is_array($accounts_data['accounts'])) {
            foreach ($accounts_data['accounts'] as $account) {
                if (isset($account['name'])) {
                    // Simuler des propriétés pour l'exemple
                    // En réalité, il faudrait faire un appel séparé pour récupérer les propriétés
                    $properties[] = array(
                        'id' => 'properties/*********',
                        'name' => $account['displayName'] . ' (GA4)',
                        'selected' => count($properties) === 0 // Sélectionner la première
                    );
                }
            }
        }

        return $properties;
    }

    /**
     * Traite les données d'aperçu
     *
     * @since    1.2.0
     * @param    array    $raw_data    Données brutes de l'API
     * @return   array                 Données traitées
     */
    private function process_overview_data($raw_data) {
        $processed = array(
            'summary' => array(
                'users' => 0,
                'sessions' => 0,
                'pageviews' => 0,
                'bounce_rate' => 0,
                'avg_session_duration' => 0,
                'new_users' => 0
            ),
            'timeline' => array()
        );

        if (isset($raw_data['rows']) && is_array($raw_data['rows'])) {
            foreach ($raw_data['rows'] as $row) {
                if (isset($row['metricValues']) && is_array($row['metricValues'])) {
                    $date = isset($row['dimensionValues'][0]['value']) ? $row['dimensionValues'][0]['value'] : '';
                    
                    $metrics = array(
                        'users' => floatval($row['metricValues'][0]['value'] ?? 0),
                        'sessions' => floatval($row['metricValues'][1]['value'] ?? 0),
                        'pageviews' => floatval($row['metricValues'][2]['value'] ?? 0),
                        'bounce_rate' => floatval($row['metricValues'][3]['value'] ?? 0),
                        'avg_session_duration' => floatval($row['metricValues'][4]['value'] ?? 0),
                        'new_users' => floatval($row['metricValues'][5]['value'] ?? 0)
                    );

                    // Ajouter à la timeline
                    $processed['timeline'][] = array(
                        'date' => $date,
                        'metrics' => $metrics
                    );

                    // Ajouter aux totaux
                    foreach ($metrics as $key => $value) {
                        if ($key !== 'bounce_rate' && $key !== 'avg_session_duration') {
                            $processed['summary'][$key] += $value;
                        }
                    }
                }
            }

            // Calculer les moyennes
            $total_days = count($processed['timeline']);
            if ($total_days > 0) {
                $total_bounce_rate = 0;
                $total_duration = 0;
                
                foreach ($processed['timeline'] as $day) {
                    $total_bounce_rate += $day['metrics']['bounce_rate'];
                    $total_duration += $day['metrics']['avg_session_duration'];
                }
                
                $processed['summary']['bounce_rate'] = $total_bounce_rate / $total_days;
                $processed['summary']['avg_session_duration'] = $total_duration / $total_days;
            }
        }

        return $processed;
    }

    /**
     * Traite les données de trafic
     *
     * @since    1.2.0
     * @param    array    $raw_data    Données brutes de l'API
     * @return   array                 Données traitées
     */
    private function process_traffic_data($raw_data) {
        $processed = array(
            'channels' => array(),
            'sources' => array()
        );

        if (isset($raw_data['rows']) && is_array($raw_data['rows'])) {
            foreach ($raw_data['rows'] as $row) {
                if (isset($row['dimensionValues']) && isset($row['metricValues'])) {
                    $channel = $row['dimensionValues'][0]['value'] ?? 'Unknown';
                    $source = $row['dimensionValues'][1]['value'] ?? 'Unknown';
                    
                    $sessions = floatval($row['metricValues'][0]['value'] ?? 0);
                    $users = floatval($row['metricValues'][1]['value'] ?? 0);
                    $pageviews = floatval($row['metricValues'][2]['value'] ?? 0);

                    $processed['sources'][] = array(
                        'channel' => $channel,
                        'source' => $source,
                        'sessions' => $sessions,
                        'users' => $users,
                        'pageviews' => $pageviews
                    );
                }
            }
        }

        return $processed;
    }

    /**
     * Traite les données des pages
     *
     * @since    1.2.0
     * @param    array    $raw_data    Données brutes de l'API
     * @return   array                 Données traitées
     */
    private function process_pages_data($raw_data) {
        $processed = array();

        if (isset($raw_data['rows']) && is_array($raw_data['rows'])) {
            foreach ($raw_data['rows'] as $row) {
                if (isset($row['dimensionValues']) && isset($row['metricValues'])) {
                    $path = $row['dimensionValues'][0]['value'] ?? '';
                    $title = $row['dimensionValues'][1]['value'] ?? '';
                    
                    $pageviews = floatval($row['metricValues'][0]['value'] ?? 0);
                    $users = floatval($row['metricValues'][1]['value'] ?? 0);
                    $avg_duration = floatval($row['metricValues'][2]['value'] ?? 0);
                    $bounce_rate = floatval($row['metricValues'][3]['value'] ?? 0);

                    $processed[] = array(
                        'path' => $path,
                        'title' => $title,
                        'pageviews' => $pageviews,
                        'users' => $users,
                        'avg_session_duration' => $avg_duration,
                        'bounce_rate' => $bounce_rate,
                        'url' => home_url($path)
                    );
                }
            }
        }

        return $processed;
    }
}
