{"version": 3, "names": [], "sources": ["@wordpress/components/src/base-control/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\nexport type BaseControlProps = {\n\t/**\n\t * Start opting into the new margin-free styles that will become the default in a future version.\n\t *\n\t * @default false\n\t */\n\t__nextHasNoMarginBottom?: boolean;\n\t/**\n\t * The HTML `id` of the control element (passed in as a child to `BaseControl`) to which labels and help text are being generated.\n\t * This is necessary to accessibly associate the label with that element.\n\t *\n\t * The recommended way is to use the `useBaseControlProps` hook, which takes care of generating a unique `id` for you.\n\t * Otherwise, if you choose to pass an explicit `id` to this prop, you are responsible for ensuring the uniqueness of the `id`.\n\t */\n\tid?: string;\n\t/**\n\t * Additional description for the control.\n\t *\n\t * It is preferable to use plain text for `help`, as it can be accessibly associated with the control using `aria-describedby`.\n\t * When the `help` contains links, or otherwise non-plain text content, it will be associated with the control using `aria-details`.\n\t */\n\thelp?: ReactNode;\n\t/**\n\t * If this property is added, a label will be generated using label property as the content.\n\t */\n\tlabel?: ReactNode;\n\t/**\n\t * If true, the label will only be visible to screen readers.\n\t *\n\t * @default false\n\t */\n\thideLabelFromVision?: boolean;\n\tclassName?: string;\n\t/**\n\t * The content to be displayed within the `BaseControl`.\n\t */\n\tchildren: ReactNode;\n};\n\nexport type BaseControlVisualLabelProps = {\n\tchildren: ReactNode;\n};\n"], "mappings": ""}