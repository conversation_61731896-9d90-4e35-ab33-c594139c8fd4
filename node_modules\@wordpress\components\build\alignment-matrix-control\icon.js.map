{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_utils", "_alignmentMatrixControlIconStyles", "BASE_SIZE", "AlignmentMatrixControlIcon", "className", "disablePointerEvents", "size", "style", "value", "props", "alignIndex", "getAlignmentIndex", "scale", "toFixed", "classes", "classnames", "styles", "transform", "_react", "createElement", "Root", "role", "ALIGNMENTS", "map", "align", "index", "isActive", "Cell", "key", "Point", "_default", "exports", "default"], "sources": ["@wordpress/components/src/alignment-matrix-control/icon.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * Internal dependencies\n */\nimport { ALIGNMENTS, getAlignmentIndex } from './utils';\nimport {\n\tRoot,\n\tCell,\n\tPoint,\n} from './styles/alignment-matrix-control-icon-styles';\nimport type { AlignmentMatrixControlIconProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\nconst BASE_SIZE = 24;\n\nfunction AlignmentMatrixControlIcon( {\n\tclassName,\n\tdisablePointerEvents = true,\n\tsize = BASE_SIZE,\n\tstyle = {},\n\tvalue = 'center',\n\t...props\n}: WordPressComponentProps< AlignmentMatrixControlIconProps, 'div', false > ) {\n\tconst alignIndex = getAlignmentIndex( value );\n\tconst scale = ( size / BASE_SIZE ).toFixed( 2 );\n\n\tconst classes = classnames(\n\t\t'component-alignment-matrix-control-icon',\n\t\tclassName\n\t);\n\n\tconst styles = {\n\t\t...style,\n\t\ttransform: `scale(${ scale })`,\n\t};\n\n\treturn (\n\t\t<Root\n\t\t\t{ ...props }\n\t\t\tclassName={ classes }\n\t\t\tdisablePointerEvents={ disablePointerEvents }\n\t\t\trole=\"presentation\"\n\t\t\tstyle={ styles }\n\t\t>\n\t\t\t{ ALIGNMENTS.map( ( align, index ) => {\n\t\t\t\tconst isActive = alignIndex === index;\n\n\t\t\t\treturn (\n\t\t\t\t\t<Cell key={ align }>\n\t\t\t\t\t\t<Point isActive={ isActive } />\n\t\t\t\t\t</Cell>\n\t\t\t\t);\n\t\t\t} ) }\n\t\t</Root>\n\t);\n}\n\nexport default AlignmentMatrixControlIcon;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,iCAAA,GAAAF,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AAUA,MAAMG,SAAS,GAAG,EAAE;AAEpB,SAASC,0BAA0BA,CAAE;EACpCC,SAAS;EACTC,oBAAoB,GAAG,IAAI;EAC3BC,IAAI,GAAGJ,SAAS;EAChBK,KAAK,GAAG,CAAC,CAAC;EACVC,KAAK,GAAG,QAAQ;EAChB,GAAGC;AACsE,CAAC,EAAG;EAC7E,MAAMC,UAAU,GAAG,IAAAC,wBAAiB,EAAEH,KAAM,CAAC;EAC7C,MAAMI,KAAK,GAAG,CAAEN,IAAI,GAAGJ,SAAS,EAAGW,OAAO,CAAE,CAAE,CAAC;EAE/C,MAAMC,OAAO,GAAG,IAAAC,mBAAU,EACzB,yCAAyC,EACzCX,SACD,CAAC;EAED,MAAMY,MAAM,GAAG;IACd,GAAGT,KAAK;IACRU,SAAS,EAAG,SAASL,KAAO;EAC7B,CAAC;EAED,OACC,IAAAM,MAAA,CAAAC,aAAA,EAAClB,iCAAA,CAAAmB,IAAI;IAAA,GACCX,KAAK;IACVL,SAAS,EAAGU,OAAS;IACrBT,oBAAoB,EAAGA,oBAAsB;IAC7CgB,IAAI,EAAC,cAAc;IACnBd,KAAK,EAAGS;EAAQ,GAEdM,iBAAU,CAACC,GAAG,CAAE,CAAEC,KAAK,EAAEC,KAAK,KAAM;IACrC,MAAMC,QAAQ,GAAGhB,UAAU,KAAKe,KAAK;IAErC,OACC,IAAAP,MAAA,CAAAC,aAAA,EAAClB,iCAAA,CAAA0B,IAAI;MAACC,GAAG,EAAGJ;IAAO,GAClB,IAAAN,MAAA,CAAAC,aAAA,EAAClB,iCAAA,CAAA4B,KAAK;MAACH,QAAQ,EAAGA;IAAU,CAAE,CACzB,CAAC;EAET,CAAE,CACG,CAAC;AAET;AAAC,IAAAI,QAAA,GAEc3B,0BAA0B;AAAA4B,OAAA,CAAAC,OAAA,GAAAF,QAAA"}