<?php
/**
 * Gestionnaire de cache pour les données Analytics
 *
 * Gère la mise en cache des données GA4 et GSC pour optimiser les performances
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/analytics
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe de gestion du cache Analytics
 */
class Boss_Analytics_Cache {

    /**
     * Préfixe pour les clés de cache
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $cache_prefix    Préfixe des clés
     */
    private $cache_prefix = 'boss_analytics_';

    /**
     * Groupe de cache
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $cache_group    Groupe de cache
     */
    private $cache_group = 'boss_analytics';

    /**
     * Durée de cache par défaut (en secondes)
     *
     * @since    1.2.0
     * @access   private
     * @var      int    $default_expiration    Durée par défaut
     */
    private $default_expiration = 3600; // 1 heure

    /**
     * Constructeur
     *
     * @since    1.2.0
     */
    public function __construct() {
        // Initialiser le cache d'objet si disponible
        if (function_exists('wp_cache_init')) {
            wp_cache_init();
        }
    }

    /**
     * Récupère une valeur du cache
     *
     * @since    1.2.0
     * @param    string    $key    Clé de cache
     * @return   mixed             Valeur ou false si non trouvée
     */
    public function get($key) {
        $cache_key = $this->get_cache_key($key);
        
        // Essayer d'abord le cache d'objet WordPress
        if (function_exists('wp_cache_get')) {
            $value = wp_cache_get($cache_key, $this->cache_group);
            if ($value !== false) {
                return $value;
            }
        }

        // Fallback vers les transients
        $transient_key = $this->get_transient_key($key);
        $value = get_transient($transient_key);
        
        if ($value !== false) {
            // Remettre en cache d'objet si disponible
            if (function_exists('wp_cache_set')) {
                wp_cache_set($cache_key, $value, $this->cache_group, $this->default_expiration);
            }
            return $value;
        }

        return false;
    }

    /**
     * Stocke une valeur en cache
     *
     * @since    1.2.0
     * @param    string    $key         Clé de cache
     * @param    mixed     $value       Valeur à stocker
     * @param    int       $expiration  Durée d'expiration en secondes
     * @return   bool                   True si succès
     */
    public function set($key, $value, $expiration = null) {
        if ($expiration === null) {
            $expiration = $this->default_expiration;
        }

        $cache_key = $this->get_cache_key($key);
        $success = true;

        // Stocker dans le cache d'objet WordPress
        if (function_exists('wp_cache_set')) {
            $success = wp_cache_set($cache_key, $value, $this->cache_group, $expiration);
        }

        // Stocker aussi dans les transients pour la persistance
        $transient_key = $this->get_transient_key($key);
        $transient_success = set_transient($transient_key, $value, $expiration);

        // Enregistrer les métadonnées du cache
        $this->save_cache_metadata($key, $expiration);

        return $success && $transient_success;
    }

    /**
     * Supprime une valeur du cache
     *
     * @since    1.2.0
     * @param    string    $key    Clé de cache
     * @return   bool              True si succès
     */
    public function delete($key) {
        $cache_key = $this->get_cache_key($key);
        $success = true;

        // Supprimer du cache d'objet
        if (function_exists('wp_cache_delete')) {
            $success = wp_cache_delete($cache_key, $this->cache_group);
        }

        // Supprimer du transient
        $transient_key = $this->get_transient_key($key);
        $transient_success = delete_transient($transient_key);

        // Supprimer les métadonnées
        $this->delete_cache_metadata($key);

        return $success && $transient_success;
    }

    /**
     * Vide tout le cache Analytics
     *
     * @since    1.2.0
     * @return   bool    True si succès
     */
    public function flush_all() {
        global $wpdb;

        $success = true;

        // Vider le cache d'objet pour ce groupe
        if (function_exists('wp_cache_flush_group')) {
            $success = wp_cache_flush_group($this->cache_group);
        }

        // Supprimer tous les transients Analytics
        $transient_pattern = '_transient_' . $this->cache_prefix . '%';
        $timeout_pattern = '_transient_timeout_' . $this->cache_prefix . '%';

        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
            $transient_pattern,
            $timeout_pattern
        ));

        // Supprimer les métadonnées
        delete_option($this->cache_prefix . 'metadata');

        return $success;
    }

    /**
     * Vide le cache d'un service spécifique
     *
     * @since    1.2.0
     * @param    string    $service    Service (ga4 ou gsc)
     * @return   bool                  True si succès
     */
    public function clear_service_cache($service) {
        if (!in_array($service, array('ga4', 'gsc'))) {
            return false;
        }

        global $wpdb;

        // Pattern pour ce service
        $pattern = $this->cache_prefix . $service . '_%';
        $transient_pattern = '_transient_' . $pattern;
        $timeout_pattern = '_transient_timeout_' . $pattern;

        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
            $transient_pattern,
            $timeout_pattern
        ));

        // Supprimer du cache d'objet
        if (function_exists('wp_cache_flush_group')) {
            // Note: WordPress ne permet pas de vider partiellement un groupe
            // On pourrait implémenter une logique plus complexe ici
        }

        return true;
    }

    /**
     * Obtient les statistiques du cache
     *
     * @since    1.2.0
     * @return   array    Statistiques du cache
     */
    public function get_cache_stats() {
        global $wpdb;

        // Compter les transients Analytics
        $transient_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_' . $this->cache_prefix . '%'
        ));

        // Calculer la taille approximative
        $size_query = $wpdb->prepare(
            "SELECT SUM(LENGTH(option_value)) FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_' . $this->cache_prefix . '%'
        );
        $total_size = $wpdb->get_var($size_query) ?: 0;

        // Obtenir les métadonnées
        $metadata = get_option($this->cache_prefix . 'metadata', array());

        return array(
            'total_entries' => intval($transient_count),
            'total_size_bytes' => intval($total_size),
            'total_size_mb' => round($total_size / 1024 / 1024, 2),
            'metadata' => $metadata,
            'cache_group' => $this->cache_group,
            'default_expiration' => $this->default_expiration
        );
    }

    /**
     * Nettoie les caches expirés
     *
     * @since    1.2.0
     * @return   int    Nombre d'entrées supprimées
     */
    public function cleanup_expired() {
        global $wpdb;

        // Supprimer les transients expirés
        $current_time = time();
        
        $expired_transients = $wpdb->get_col($wpdb->prepare(
            "SELECT REPLACE(option_name, '_transient_timeout_', '') 
             FROM {$wpdb->options} 
             WHERE option_name LIKE %s 
             AND option_value < %d",
            '_transient_timeout_' . $this->cache_prefix . '%',
            $current_time
        ));

        $deleted_count = 0;

        foreach ($expired_transients as $transient_name) {
            if (delete_transient($transient_name)) {
                $deleted_count++;
            }
        }

        return $deleted_count;
    }

    /**
     * Préchauffe le cache avec des données essentielles
     *
     * @since    1.2.0
     * @return   bool    True si succès
     */
    public function warmup_cache() {
        try {
            // Charger les gestionnaires
            $ga4_manager = new Boss_GA4_Manager();
            $gsc_manager = new Boss_GSC_Manager();

            // Précharger les données GA4 essentielles
            $ga4_manager->get_overview_data();
            $ga4_manager->get_traffic_data();

            // Précharger les données GSC essentielles
            $gsc_manager->get_keywords_data();
            $gsc_manager->get_performance_data();

            return true;

        } catch (Exception $e) {
            error_log('[Boss SEO Analytics Cache] Erreur lors du préchauffage: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Génère une clé de cache
     *
     * @since    1.2.0
     * @param    string    $key    Clé originale
     * @return   string            Clé de cache formatée
     */
    private function get_cache_key($key) {
        return $this->cache_prefix . md5($key);
    }

    /**
     * Génère une clé de transient
     *
     * @since    1.2.0
     * @param    string    $key    Clé originale
     * @return   string            Clé de transient formatée
     */
    private function get_transient_key($key) {
        // Les clés de transient sont limitées à 172 caractères
        $transient_key = $this->cache_prefix . $key;
        
        if (strlen($transient_key) > 172) {
            $transient_key = $this->cache_prefix . md5($key);
        }
        
        return $transient_key;
    }

    /**
     * Sauvegarde les métadonnées du cache
     *
     * @since    1.2.0
     * @param    string    $key         Clé de cache
     * @param    int       $expiration  Durée d'expiration
     */
    private function save_cache_metadata($key, $expiration) {
        $metadata = get_option($this->cache_prefix . 'metadata', array());
        
        $metadata[$key] = array(
            'created_at' => time(),
            'expires_at' => time() + $expiration,
            'expiration' => $expiration
        );

        // Limiter le nombre d'entrées de métadonnées
        if (count($metadata) > 1000) {
            // Supprimer les plus anciennes
            uasort($metadata, function($a, $b) {
                return $a['created_at'] - $b['created_at'];
            });
            $metadata = array_slice($metadata, -500, null, true);
        }

        update_option($this->cache_prefix . 'metadata', $metadata, false);
    }

    /**
     * Supprime les métadonnées du cache
     *
     * @since    1.2.0
     * @param    string    $key    Clé de cache
     */
    private function delete_cache_metadata($key) {
        $metadata = get_option($this->cache_prefix . 'metadata', array());
        
        if (isset($metadata[$key])) {
            unset($metadata[$key]);
            update_option($this->cache_prefix . 'metadata', $metadata, false);
        }
    }
}
