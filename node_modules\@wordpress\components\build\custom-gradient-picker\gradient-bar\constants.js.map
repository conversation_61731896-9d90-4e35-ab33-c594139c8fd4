{"version": 3, "names": ["GRADIENT_MARKERS_WIDTH", "exports", "INSERT_POINT_WIDTH", "MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT", "MINIMUM_DISTANCE_BETWEEN_POINTS", "MINIMUM_SIGNIFICANT_MOVE", "KEYBOARD_CONTROL_POINT_VARIATION", "MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_MARKER"], "sources": ["@wordpress/components/src/custom-gradient-picker/gradient-bar/constants.ts"], "sourcesContent": ["export const GRADIENT_MARKERS_WIDTH = 16;\nexport const INSERT_POINT_WIDTH = 16;\nexport const MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT = 10;\nexport const MINIMUM_DISTANCE_BETWEEN_POINTS = 0;\nexport const MINIMUM_SIGNIFICANT_MOVE = 5;\n\nexport const KEYBOARD_CONTROL_POINT_VARIATION =\n\tMINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT;\nexport const MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_MARKER =\n\t( INSERT_POINT_WIDTH + GRADIENT_MARKERS_WIDTH ) / 2;\n"], "mappings": ";;;;;;AAAO,MAAMA,sBAAsB,GAAG,EAAE;AAACC,OAAA,CAAAD,sBAAA,GAAAA,sBAAA;AAClC,MAAME,kBAAkB,GAAG,EAAE;AAACD,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAC9B,MAAMC,2CAA2C,GAAG,EAAE;AAACF,OAAA,CAAAE,2CAAA,GAAAA,2CAAA;AACvD,MAAMC,+BAA+B,GAAG,CAAC;AAACH,OAAA,CAAAG,+BAAA,GAAAA,+BAAA;AAC1C,MAAMC,wBAAwB,GAAG,CAAC;AAACJ,OAAA,CAAAI,wBAAA,GAAAA,wBAAA;AAEnC,MAAMC,gCAAgC,GAC5CH,2CAA2C;AAACF,OAAA,CAAAK,gCAAA,GAAAA,gCAAA;AACtC,MAAMC,4CAA4C,GACxD,CAAEL,kBAAkB,GAAGF,sBAAsB,IAAK,CAAC;AAACC,OAAA,CAAAM,4CAAA,GAAAA,4CAAA"}