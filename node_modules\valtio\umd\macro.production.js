!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@babel/helper-module-imports"),require("@babel/types"),require("babel-plugin-macros")):"function"==typeof define&&define.amd?define(["exports","@babel/helper-module-imports","@babel/types","babel-plugin-macros"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtioMacro={},e.helperModuleImports,e.t,e.babelPluginMacros)}(this,(function(e,t,n,r){"use strict";function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var o=a(n),i=r.createMacro((function(e){var n;null==(n=e.references.useProxy)||n.forEach((function(e){var n,a,i,l,u,c,f=t.addNamed(e,"useSnapshot","valtio"),s=null==(n=e.parentPath)||null==(a=n.get("arguments.0"))?void 0:a.node;if(!o.isIdentifier(s))throw new r.MacroError("no proxy object");var d=o.identifier("valtio_macro_snap_"+s.name);null==(i=e.parentPath)||null==(l=i.parentPath)||l.replaceWith(o.variableDeclaration("const",[o.variableDeclarator(d,o.callExpression(f,[s]))]));var p=0;null==(u=e.parentPath)||null==(c=u.getFunctionParent())||c.traverse({Identifier:function(e){0===p&&e.node!==s&&e.node.name===s.name&&(e.node.name=d.name)},Function:{enter:function(){++p},exit:function(){--p}}})}))}),{configName:"valtio"});e.default=i,Object.defineProperty(e,"__esModule",{value:!0})}));
