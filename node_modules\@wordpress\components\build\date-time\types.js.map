{"version": 3, "names": [], "sources": ["@wordpress/components/src/date-time/types.ts"], "sourcesContent": ["export type TimePickerProps = {\n\t/**\n\t * The initial current time the time picker should render.\n\t */\n\tcurrentTime?: Date | string | number | null;\n\n\t/**\n\t * Whether we use a 12-hour clock. With a 12-hour clock, an AM/PM widget is\n\t * displayed and the time format is assumed to be `MM-DD-YYYY` (as opposed\n\t * to the default format `DD-MM-YYYY`).\n\t */\n\tis12Hour?: boolean;\n\n\t/**\n\t * The function called when a new time has been selected. It is passed the\n\t * time as an argument.\n\t */\n\tonChange?: ( time: string ) => void;\n};\n\nexport type DatePickerEvent = {\n\t/**\n\t * The date of the event.\n\t */\n\tdate: Date;\n};\n\nexport type DatePickerProps = {\n\t/**\n\t * The current date and time at initialization. Optionally pass in a `null`\n\t * value to specify no date is currently selected.\n\t */\n\tcurrentDate?: Date | string | number | null;\n\n\t/**\n\t * The function called when a new date has been selected. It is passed the\n\t * date as an argument.\n\t */\n\tonChange?: ( date: string ) => void;\n\n\t/**\n\t * A callback function which receives a Date object representing a day as an\n\t * argument, and should return a Boolean to signify if the day is valid or\n\t * not.\n\t */\n\tisInvalidDate?: ( date: Date ) => boolean;\n\n\t/**\n\t * A callback invoked when selecting the previous/next month in the date\n\t * picker. The callback receives the new month date in the ISO format as an\n\t * argument.\n\t */\n\tonMonthPreviewed?: ( date: string ) => void;\n\n\t/**\n\t * List of events to show in the date picker. Each event will appear as a\n\t * dot on the day of the event.\n\t */\n\tevents?: DatePickerEvent[];\n\n\t/**\n\t * The day that the week should start on. 0 for Sunday, 1 for Monday, etc.\n\t *\n\t * @default 0\n\t */\n\tstartOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6;\n};\n\nexport type DateTimePickerProps = Omit< DatePickerProps, 'onChange' > &\n\tOmit< TimePickerProps, 'currentTime' | 'onChange' > & {\n\t\t/**\n\t\t * The function called when a new date or time has been selected. It is\n\t\t * passed the date and time as an argument.\n\t\t */\n\t\tonChange?: ( date: string | null ) => void;\n\t};\n"], "mappings": ""}