"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _element = require("@wordpress/element");
var _primitives = require("@wordpress/primitives");
var _dashicon = _interopRequireDefault(require("../dashicon"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function Icon({
  icon = null,
  size = 'string' === typeof icon ? 20 : 24,
  ...additionalProps
}) {
  if ('string' === typeof icon) {
    return (0, _element.createElement)(_dashicon.default, {
      icon: icon,
      size: size,
      ...additionalProps
    });
  }
  if ((0, _element.isValidElement)(icon) && _dashicon.default === icon.type) {
    return (0, _element.cloneElement)(icon, {
      ...additionalProps
    });
  }
  if ('function' === typeof icon) {
    return (0, _element.createElement)(icon, {
      size,
      ...additionalProps
    });
  }
  if (icon && (icon.type === 'svg' || icon.type === _primitives.SVG)) {
    const appliedProps = {
      ...icon.props,
      width: size,
      height: size,
      ...additionalProps
    };
    return (0, _element.createElement)(_primitives.SVG, {
      ...appliedProps
    });
  }
  if ((0, _element.isValidElement)(icon)) {
    return (0, _element.cloneElement)(icon, {
      // @ts-ignore Just forwarding the size prop along
      size,
      ...additionalProps
    });
  }
  return icon;
}
var _default = Icon;
exports.default = _default;
//# sourceMappingURL=index.js.map