"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.CardBody = void 0;
var _react = require("react");
var _context = require("../../context");
var _scrollable = require("../../scrollable");
var _view = require("../../view");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedCardBody(props, forwardedRef) {
  const {
    isScrollable,
    ...otherProps
  } = (0, _hook.useCardBody)(props);
  if (isScrollable) {
    return (0, _react.createElement)(_scrollable.Scrollable, {
      ...otherProps,
      ref: forwardedRef
    });
  }
  return (0, _react.createElement)(_view.View, {
    ...otherProps,
    ref: forwardedRef
  });
}

/**
 * `CardBody` renders an optional content area for a `Card`.
 * Multiple `CardBody` components can be used within `Card` if needed.
 *
 * ```jsx
 * import { Card, CardBody } from `@wordpress/components`;
 *
 * <Card>
 * 	<CardBody>
 * 		...
 * 	</CardBody>
 * </Card>
 * ```
 */
const CardBody = (0, _context.contextConnect)(UnconnectedCardBody, 'CardBody');
exports.CardBody = CardBody;
var _default = CardBody;
exports.default = _default;
//# sourceMappingURL=component.js.map