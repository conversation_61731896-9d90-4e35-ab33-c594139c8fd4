{"version": 3, "names": ["_spacer", "require", "_context", "UnconnectedInputControlPrefixWrapper", "props", "forwardedRef", "derivedProps", "useContextSystem", "_react", "createElement", "Spacer", "marginBottom", "ref", "InputControlPrefixWrapper", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/input-control/input-prefix-wrapper.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport { Spacer } from '../spacer';\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnect, useContextSystem } from '../context';\nimport type { InputControlPrefixWrapperProps } from './types';\n\nfunction UnconnectedInputControlPrefixWrapper(\n\tprops: WordPressComponentProps< InputControlPrefixWrapperProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst derivedProps = useContextSystem( props, 'InputControlPrefixWrapper' );\n\n\treturn (\n\t\t<Spacer marginBottom={ 0 } { ...derivedProps } ref={ forwardedRef } />\n\t);\n}\n\n/**\n * A convenience wrapper for the `prefix` when you want to apply\n * standard padding in accordance with the size variant.\n *\n * ```jsx\n * import {\n *   __experimentalInputControl as InputControl,\n *   __experimentalInputControlPrefixWrapper as InputControlPrefixWrapper,\n * } from '@wordpress/components';\n *\n * <InputControl\n *   prefix={<InputControlPrefixWrapper>@</InputControlPrefixWrapper>}\n * />\n * ```\n */\nexport const InputControlPrefixWrapper = contextConnect(\n\tUnconnectedInputControlPrefixWrapper,\n\t'InputControlPrefixWrapper'\n);\n\nexport default InputControlPrefixWrapper;\n"], "mappings": ";;;;;;;AAQA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AAVA;AACA;AACA;;AAGA;AACA;AACA;;AAMA,SAASE,oCAAoCA,CAC5CC,KAAuE,EACvEC,YAAiC,EAChC;EACD,MAAMC,YAAY,GAAG,IAAAC,yBAAgB,EAAEH,KAAK,EAAE,2BAA4B,CAAC;EAE3E,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACT,OAAA,CAAAU,MAAM;IAACC,YAAY,EAAG,CAAG;IAAA,GAAML,YAAY;IAAGM,GAAG,EAAGP;EAAc,CAAE,CAAC;AAExE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMQ,yBAAyB,GAAG,IAAAC,uBAAc,EACtDX,oCAAoC,EACpC,2BACD,CAAC;AAACY,OAAA,CAAAF,yBAAA,GAAAA,yBAAA;AAAA,IAAAG,QAAA,GAEaH,yBAAyB;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}