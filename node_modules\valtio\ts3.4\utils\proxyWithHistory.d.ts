import { INTERNAL_Snapshot } from '../vanilla';
/**
 * proxyWithHistory
 *
 * This creates a new proxy with history support.
 * It includes following properties:
 * - value: any value (does not have to be an object)
 * - history: an array holding the history of snapshots
 * - historyIndex: the history index to the current snapshot
 * - canUndo: a function to return true if undo is available
 * - undo: a function to go back history
 * - canRedo: a function to return true if redo is available
 * - redo: a function to go forward history
 * - saveHistory: a function to save history
 *
 * [Notes]
 * Suspense/promise is not supported.
 *
 * @example
 * import { proxyWithHistory } from 'valtio/utils'
 * const state = proxyWithHistory({
 *   count: 1,
 * })
 */
export declare function proxyWithHistory<V>(initialValue: V, skipSubscribe?: boolean): {
    value: V;
    history: {
        wip: INTERNAL_Snapshot<V> | undefined;
        snapshots: INTERNAL_Snapshot<V>[];
        index: number;
    } & {
        $$valtioRef: true;
    };
    canUndo: () => boolean;
    undo: () => void;
    canRedo: () => boolean;
    redo: () => void;
    saveHistory: () => void;
    subscribe: () => () => void;
};
