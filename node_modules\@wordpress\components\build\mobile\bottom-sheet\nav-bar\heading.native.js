"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _compose = require("@wordpress/compose");
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function Heading({
  children
}) {
  const headingStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.heading, _styles.default['heading-dark']);
  return (0, _react.createElement)(_reactNative.Text, {
    accessibilityRole: "header",
    style: headingStyle,
    maxFontSizeMultiplier: 3
  }, children);
}
var _default = Heading;
exports.default = _default;
//# sourceMappingURL=heading.native.js.map