{"version": 3, "names": ["_i18n", "require", "DEFAULT_GRADIENT", "exports", "DEFAULT_LINEAR_GRADIENT_ANGLE", "HORIZONTAL_GRADIENT_ORIENTATION", "type", "value", "GRADIENT_OPTIONS", "label", "__", "DIRECTIONAL_ORIENTATION_ANGLE_MAP", "top", "right", "bottom", "left"], "sources": ["@wordpress/components/src/custom-gradient-picker/constants.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\nexport const DEFAULT_GRADIENT =\n\t'linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%)';\n\nexport const DEFAULT_LINEAR_GRADIENT_ANGLE = 180;\n\nexport const HORIZONTAL_GRADIENT_ORIENTATION = {\n\ttype: 'angular',\n\tvalue: '90',\n} as const;\n\nexport const GRADIENT_OPTIONS = [\n\t{ value: 'linear-gradient', label: __( 'Linear' ) },\n\t{ value: 'radial-gradient', label: __( 'Radial' ) },\n];\n\nexport const DIRECTIONAL_ORIENTATION_ANGLE_MAP = {\n\ttop: 0,\n\t'top right': 45,\n\t'right top': 45,\n\tright: 90,\n\t'right bottom': 135,\n\t'bottom right': 135,\n\tbottom: 180,\n\t'bottom left': 225,\n\t'left bottom': 225,\n\tleft: 270,\n\t'top left': 315,\n\t'left top': 315,\n};\n"], "mappings": ";;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGO,MAAMC,gBAAgB,GAC5B,0EAA0E;AAACC,OAAA,CAAAD,gBAAA,GAAAA,gBAAA;AAErE,MAAME,6BAA6B,GAAG,GAAG;AAACD,OAAA,CAAAC,6BAAA,GAAAA,6BAAA;AAE1C,MAAMC,+BAA+B,GAAG;EAC9CC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;AACR,CAAU;AAACJ,OAAA,CAAAE,+BAAA,GAAAA,+BAAA;AAEJ,MAAMG,gBAAgB,GAAG,CAC/B;EAAED,KAAK,EAAE,iBAAiB;EAAEE,KAAK,EAAE,IAAAC,QAAE,EAAE,QAAS;AAAE,CAAC,EACnD;EAAEH,KAAK,EAAE,iBAAiB;EAAEE,KAAK,EAAE,IAAAC,QAAE,EAAE,QAAS;AAAE,CAAC,CACnD;AAACP,OAAA,CAAAK,gBAAA,GAAAA,gBAAA;AAEK,MAAMG,iCAAiC,GAAG;EAChDC,GAAG,EAAE,CAAC;EACN,WAAW,EAAE,EAAE;EACf,WAAW,EAAE,EAAE;EACfC,KAAK,EAAE,EAAE;EACT,cAAc,EAAE,GAAG;EACnB,cAAc,EAAE,GAAG;EACnBC,MAAM,EAAE,GAAG;EACX,aAAa,EAAE,GAAG;EAClB,aAAa,EAAE,GAAG;EAClBC,IAAI,EAAE,GAAG;EACT,UAAU,EAAE,GAAG;EACf,UAAU,EAAE;AACb,CAAC;AAACZ,OAAA,CAAAQ,iCAAA,GAAAA,iCAAA"}