<?php

/**
 * Générateur de rapport de mots-clés Boss SEO.
 * 
 * Cette classe génère des rapports d'analyse des mots-clés
 * avec des données réelles de Google Search Console et Boss SEO.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Générateur de rapport de mots-clés.
 *
 * Génère des rapports d'analyse des mots-clés avec :
 * - Données Google Search Console
 * - Analyse des mots-clés focus Boss SEO
 * - Performance et opportunités
 * - Recommandations d'optimisation
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Keywords_Report_Generator {

    /**
     * Instance du collecteur de données.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Reports_Data_Collector    $data_collector    Collecteur de données.
     */
    private $data_collector;

    /**
     * Instance Google Search Console.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Google_Search_Console    $gsc    Instance GSC.
     */
    private $gsc;

    /**
     * Configuration du rapport.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $config    Configuration.
     */
    private $config;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    Boss_SEO_Reports_Data_Collector    $data_collector    Collecteur de données.
     * @param    Boss_SEO_Google_Search_Console     $gsc               Instance GSC.
     */
    public function __construct( $data_collector, $gsc ) {
        $this->data_collector = $data_collector;
        $this->gsc = $gsc;
        $this->config = array(
            'top_keywords_limit' => 100,
            'opportunity_threshold' => 10, // Position moyenne pour identifier les opportunités
            'low_ctr_threshold' => 2.0, // CTR faible en %
            'high_impression_threshold' => 1000 // Seuil d'impressions élevées
        );
    }

    /**
     * Génère un rapport de mots-clés complet.
     *
     * @since    1.2.0
     * @param    array    $options    Options du rapport.
     * @return   array                Données du rapport généré.
     */
    public function generate( $options = array() ) {
        // Fusionner les options avec la configuration par défaut
        $this->config = array_merge( $this->config, $options );
        
        // Collecter les données de base
        $boss_seo_keywords = $this->get_boss_seo_keywords();
        $gsc_data = $this->get_gsc_keywords_data();
        
        // Générer le rapport
        $report_data = array(
            'metadata' => $this->generate_metadata(),
            'executive_summary' => $this->generate_executive_summary( $boss_seo_keywords, $gsc_data ),
            'gsc_performance' => $this->analyze_gsc_performance( $gsc_data ),
            'boss_seo_analysis' => $this->analyze_boss_seo_keywords( $boss_seo_keywords ),
            'keyword_opportunities' => $this->identify_keyword_opportunities( $gsc_data, $boss_seo_keywords ),
            'content_gaps' => $this->identify_content_gaps( $gsc_data, $boss_seo_keywords ),
            'recommendations' => $this->generate_recommendations( $gsc_data, $boss_seo_keywords ),
            'action_plan' => $this->generate_action_plan( $gsc_data, $boss_seo_keywords )
        );
        
        return $report_data;
    }

    /**
     * Génère les métadonnées du rapport.
     *
     * @since    1.2.0
     * @return   array    Métadonnées.
     */
    private function generate_metadata() {
        return array(
            'title' => 'Rapport d\'Analyse des Mots-clés',
            'subtitle' => 'Performance et opportunités des mots-clés SEO',
            'generated_at' => current_time( 'mysql' ),
            'generated_by' => wp_get_current_user()->display_name,
            'site_url' => home_url(),
            'site_name' => get_bloginfo( 'name' ),
            'period' => array(
                'start' => date( 'Y-m-d', strtotime( '-30 days' ) ),
                'end' => date( 'Y-m-d', strtotime( '-1 day' ) ),
                'label' => '30 derniers jours'
            ),
            'data_sources' => array(
                'boss_seo' => true,
                'google_search_console' => $this->gsc->is_configured()
            )
        );
    }

    /**
     * Génère le résumé exécutif.
     *
     * @since    1.2.0
     * @param    array    $boss_keywords    Mots-clés Boss SEO.
     * @param    array    $gsc_data         Données GSC.
     * @return   array                      Résumé exécutif.
     */
    private function generate_executive_summary( $boss_keywords, $gsc_data ) {
        $summary = array(
            'total_boss_keywords' => count( $boss_keywords ),
            'total_gsc_keywords' => 0,
            'total_clicks' => 0,
            'total_impressions' => 0,
            'average_ctr' => 0,
            'average_position' => 0,
            'top_performing_keywords' => array(),
            'underperforming_keywords' => array(),
            'keyword_coverage' => 0
        );

        if ( ! is_wp_error( $gsc_data ) && isset( $gsc_data['keywords'] ) ) {
            $gsc_keywords = $gsc_data['keywords'];
            $summary['total_gsc_keywords'] = count( $gsc_keywords );
            $summary['total_clicks'] = $gsc_data['total_clicks'];
            $summary['total_impressions'] = $gsc_data['total_impressions'];
            $summary['average_ctr'] = $gsc_data['average_ctr'];
            $summary['average_position'] = $gsc_data['average_position'];

            // Top 5 mots-clés performants
            $summary['top_performing_keywords'] = array_slice( $gsc_keywords, 0, 5 );

            // Mots-clés sous-performants (position > 10 et impressions > 100)
            $underperforming = array_filter( $gsc_keywords, function( $keyword ) {
                return $keyword['position'] > 10 && $keyword['impressions'] > 100;
            } );
            $summary['underperforming_keywords'] = array_slice( $underperforming, 0, 5 );

            // Couverture des mots-clés (Boss SEO vs GSC)
            $boss_keyword_list = array_column( $boss_keywords, 'keyword' );
            $gsc_keyword_list = array_column( $gsc_keywords, 'keyword' );
            $intersection = array_intersect( $boss_keyword_list, $gsc_keyword_list );
            $summary['keyword_coverage'] = count( $boss_keyword_list ) > 0 ? 
                round( ( count( $intersection ) / count( $boss_keyword_list ) ) * 100, 1 ) : 0;
        }

        return $summary;
    }

    /**
     * Récupère les mots-clés configurés dans Boss SEO.
     *
     * @since    1.2.0
     * @return   array    Mots-clés Boss SEO.
     */
    private function get_boss_seo_keywords() {
        $keywords = array();
        
        // Récupérer tous les posts avec des mots-clés focus
        $posts = get_posts( array(
            'post_type' => array( 'post', 'page', 'product' ),
            'post_status' => 'publish',
            'numberposts' => -1,
            'meta_query' => array(
                array(
                    'key' => '_boss_seo_focus_keyword',
                    'value' => '',
                    'compare' => '!='
                )
            )
        ) );

        foreach ( $posts as $post ) {
            $focus_keyword = get_post_meta( $post->ID, '_boss_seo_focus_keyword', true );
            $seo_title = get_post_meta( $post->ID, '_boss_seo_title', true );
            $meta_description = get_post_meta( $post->ID, '_boss_seo_meta_description', true );

            if ( ! empty( $focus_keyword ) ) {
                // Traiter les mots-clés multiples (séparés par des virgules)
                $focus_keywords = array_map( 'trim', explode( ',', $focus_keyword ) );
                
                foreach ( $focus_keywords as $keyword ) {
                    if ( ! empty( $keyword ) ) {
                        $keywords[] = array(
                            'keyword' => $keyword,
                            'post_id' => $post->ID,
                            'post_title' => $post->post_title,
                            'post_url' => get_permalink( $post->ID ),
                            'post_type' => $post->post_type,
                            'seo_title' => $seo_title ?: $post->post_title,
                            'meta_description' => $meta_description,
                            'has_meta_description' => ! empty( $meta_description ),
                            'keyword_in_title' => stripos( $seo_title ?: $post->post_title, $keyword ) !== false,
                            'keyword_in_description' => stripos( $meta_description, $keyword ) !== false
                        );
                    }
                }
            }
        }

        return $keywords;
    }

    /**
     * Récupère les données de mots-clés depuis Google Search Console.
     *
     * @since    1.2.0
     * @return   array|WP_Error    Données GSC ou erreur.
     */
    private function get_gsc_keywords_data() {
        if ( ! $this->gsc->is_configured() ) {
            return new WP_Error( 'gsc_not_configured', 'Google Search Console non configuré' );
        }

        return $this->gsc->get_performance_data( array(
            'dimensions' => array( 'query' ),
            'row_limit' => $this->config['top_keywords_limit'],
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d', strtotime( '-1 day' ) )
        ) );
    }

    /**
     * Analyse la performance GSC.
     *
     * @since    1.2.0
     * @param    array    $gsc_data    Données GSC.
     * @return   array                 Analyse de performance.
     */
    private function analyze_gsc_performance( $gsc_data ) {
        $analysis = array(
            'available' => ! is_wp_error( $gsc_data ),
            'summary' => array(),
            'top_keywords' => array(),
            'performance_segments' => array()
        );

        if ( is_wp_error( $gsc_data ) ) {
            $analysis['error'] = $gsc_data->get_error_message();
            return $analysis;
        }

        $keywords = $gsc_data['keywords'] ?? array();
        
        // Résumé de performance
        $analysis['summary'] = array(
            'total_keywords' => count( $keywords ),
            'total_clicks' => $gsc_data['total_clicks'],
            'total_impressions' => $gsc_data['total_impressions'],
            'average_ctr' => $gsc_data['average_ctr'],
            'average_position' => $gsc_data['average_position']
        );

        // Top 20 mots-clés
        $analysis['top_keywords'] = array_slice( $keywords, 0, 20 );

        // Segmentation par performance
        $high_performers = array_filter( $keywords, function( $k ) {
            return $k['position'] <= 3 && $k['clicks'] > 0;
        } );

        $opportunities = array_filter( $keywords, function( $k ) {
            return $k['position'] > 3 && $k['position'] <= $this->config['opportunity_threshold'] && $k['impressions'] >= 100;
        } );

        $low_ctr = array_filter( $keywords, function( $k ) {
            return $k['ctr'] < $this->config['low_ctr_threshold'] && $k['impressions'] >= 100;
        } );

        $analysis['performance_segments'] = array(
            'high_performers' => array(
                'count' => count( $high_performers ),
                'keywords' => array_slice( $high_performers, 0, 10 )
            ),
            'opportunities' => array(
                'count' => count( $opportunities ),
                'keywords' => array_slice( $opportunities, 0, 10 )
            ),
            'low_ctr' => array(
                'count' => count( $low_ctr ),
                'keywords' => array_slice( $low_ctr, 0, 10 )
            )
        );

        return $analysis;
    }

    /**
     * Analyse les mots-clés Boss SEO.
     *
     * @since    1.2.0
     * @param    array    $boss_keywords    Mots-clés Boss SEO.
     * @return   array                      Analyse Boss SEO.
     */
    private function analyze_boss_seo_keywords( $boss_keywords ) {
        $analysis = array(
            'total_keywords' => count( $boss_keywords ),
            'unique_keywords' => 0,
            'optimization_quality' => array(),
            'content_type_distribution' => array(),
            'optimization_issues' => array()
        );

        // Mots-clés uniques
        $unique_keywords = array_unique( array_column( $boss_keywords, 'keyword' ) );
        $analysis['unique_keywords'] = count( $unique_keywords );

        // Qualité d'optimisation
        $well_optimized = 0;
        $partially_optimized = 0;
        $poorly_optimized = 0;

        foreach ( $boss_keywords as $keyword_data ) {
            $score = 0;
            if ( $keyword_data['has_meta_description'] ) $score++;
            if ( $keyword_data['keyword_in_title'] ) $score++;
            if ( $keyword_data['keyword_in_description'] ) $score++;

            if ( $score >= 3 ) {
                $well_optimized++;
            } elseif ( $score >= 2 ) {
                $partially_optimized++;
            } else {
                $poorly_optimized++;
            }
        }

        $analysis['optimization_quality'] = array(
            'well_optimized' => $well_optimized,
            'partially_optimized' => $partially_optimized,
            'poorly_optimized' => $poorly_optimized
        );

        // Distribution par type de contenu
        $content_types = array();
        foreach ( $boss_keywords as $keyword_data ) {
            $type = $keyword_data['post_type'];
            $content_types[ $type ] = ( $content_types[ $type ] ?? 0 ) + 1;
        }
        $analysis['content_type_distribution'] = $content_types;

        // Problèmes d'optimisation
        $missing_meta_desc = array_filter( $boss_keywords, function( $k ) {
            return ! $k['has_meta_description'];
        } );

        $keyword_not_in_title = array_filter( $boss_keywords, function( $k ) {
            return ! $k['keyword_in_title'];
        } );

        $analysis['optimization_issues'] = array(
            'missing_meta_description' => count( $missing_meta_desc ),
            'keyword_not_in_title' => count( $keyword_not_in_title ),
            'examples' => array(
                'missing_meta_desc' => array_slice( $missing_meta_desc, 0, 5 ),
                'keyword_not_in_title' => array_slice( $keyword_not_in_title, 0, 5 )
            )
        );

        return $analysis;
    }

    /**
     * Identifie les opportunités de mots-clés.
     *
     * @since    1.2.0
     * @param    array    $gsc_data         Données GSC.
     * @param    array    $boss_keywords    Mots-clés Boss SEO.
     * @return   array                      Opportunités identifiées.
     */
    private function identify_keyword_opportunities( $gsc_data, $boss_keywords ) {
        $opportunities = array(
            'untargeted_high_traffic' => array(),
            'position_improvement' => array(),
            'ctr_improvement' => array(),
            'new_content_opportunities' => array()
        );

        if ( is_wp_error( $gsc_data ) ) {
            return $opportunities;
        }

        $gsc_keywords = $gsc_data['keywords'] ?? array();
        $boss_keyword_list = array_column( $boss_keywords, 'keyword' );

        // Mots-clés GSC non ciblés dans Boss SEO
        foreach ( $gsc_keywords as $gsc_keyword ) {
            if ( ! in_array( $gsc_keyword['keyword'], $boss_keyword_list ) && 
                 $gsc_keyword['impressions'] >= 100 ) {
                $opportunities['untargeted_high_traffic'][] = $gsc_keyword;
            }
        }

        // Opportunités d'amélioration de position
        foreach ( $gsc_keywords as $gsc_keyword ) {
            if ( $gsc_keyword['position'] > 3 && 
                 $gsc_keyword['position'] <= 10 && 
                 $gsc_keyword['impressions'] >= 100 ) {
                $opportunities['position_improvement'][] = $gsc_keyword;
            }
        }

        // Opportunités d'amélioration du CTR
        foreach ( $gsc_keywords as $gsc_keyword ) {
            if ( $gsc_keyword['ctr'] < $this->config['low_ctr_threshold'] && 
                 $gsc_keyword['impressions'] >= 100 ) {
                $opportunities['ctr_improvement'][] = $gsc_keyword;
            }
        }

        // Limiter les résultats
        $opportunities['untargeted_high_traffic'] = array_slice( $opportunities['untargeted_high_traffic'], 0, 20 );
        $opportunities['position_improvement'] = array_slice( $opportunities['position_improvement'], 0, 15 );
        $opportunities['ctr_improvement'] = array_slice( $opportunities['ctr_improvement'], 0, 15 );

        return $opportunities;
    }

    /**
     * Identifie les lacunes de contenu.
     *
     * @since    1.2.0
     * @param    array    $gsc_data         Données GSC.
     * @param    array    $boss_keywords    Mots-clés Boss SEO.
     * @return   array                      Lacunes identifiées.
     */
    private function identify_content_gaps( $gsc_data, $boss_keywords ) {
        $gaps = array(
            'high_volume_missing' => array(),
            'competitor_keywords' => array(),
            'seasonal_opportunities' => array(),
            'long_tail_opportunities' => array()
        );

        if ( is_wp_error( $gsc_data ) ) {
            return $gaps;
        }

        $gsc_keywords = $gsc_data['keywords'] ?? array();
        $boss_keyword_list = array_column( $boss_keywords, 'keyword' );

        // Mots-clés à fort volume non couverts
        foreach ( $gsc_keywords as $keyword ) {
            if ( ! in_array( $keyword['keyword'], $boss_keyword_list ) &&
                 $keyword['impressions'] >= $this->config['high_impression_threshold'] ) {
                $gaps['high_volume_missing'][] = array(
                    'keyword' => $keyword['keyword'],
                    'impressions' => $keyword['impressions'],
                    'position' => $keyword['position'],
                    'opportunity_score' => $this->calculate_opportunity_score( $keyword )
                );
            }
        }

        // Opportunités long tail (3+ mots)
        foreach ( $gsc_keywords as $keyword ) {
            $word_count = str_word_count( $keyword['keyword'] );
            if ( $word_count >= 3 &&
                 ! in_array( $keyword['keyword'], $boss_keyword_list ) &&
                 $keyword['impressions'] >= 50 ) {
                $gaps['long_tail_opportunities'][] = $keyword;
            }
        }

        // Trier par potentiel
        usort( $gaps['high_volume_missing'], function( $a, $b ) {
            return $b['opportunity_score'] - $a['opportunity_score'];
        } );

        // Limiter les résultats
        $gaps['high_volume_missing'] = array_slice( $gaps['high_volume_missing'], 0, 15 );
        $gaps['long_tail_opportunities'] = array_slice( $gaps['long_tail_opportunities'], 0, 20 );

        return $gaps;
    }

    /**
     * Calcule le score d'opportunité pour un mot-clé.
     *
     * @since    1.2.0
     * @param    array    $keyword    Données du mot-clé.
     * @return   float                Score d'opportunité.
     */
    private function calculate_opportunity_score( $keyword ) {
        $impressions_score = min( $keyword['impressions'] / 1000, 10 ); // Max 10 points
        $position_score = max( 0, 20 - $keyword['position'] ); // Meilleur si position élevée
        $ctr_potential = max( 0, 5 - $keyword['ctr'] ); // Potentiel d'amélioration CTR

        return round( $impressions_score + $position_score + $ctr_potential, 2 );
    }

    /**
     * Génère les recommandations.
     *
     * @since    1.2.0
     * @param    array    $gsc_data         Données GSC.
     * @param    array    $boss_keywords    Mots-clés Boss SEO.
     * @return   array                      Recommandations.
     */
    private function generate_recommendations( $gsc_data, $boss_keywords ) {
        $recommendations = array();

        // Recommandations basées sur l'optimisation Boss SEO
        $poorly_optimized = array_filter( $boss_keywords, function( $k ) {
            $score = 0;
            if ( $k['has_meta_description'] ) $score++;
            if ( $k['keyword_in_title'] ) $score++;
            if ( $k['keyword_in_description'] ) $score++;
            return $score < 2;
        } );

        if ( count( $poorly_optimized ) > 0 ) {
            $recommendations[] = array(
                'category' => 'optimization',
                'priority' => 'high',
                'title' => 'Améliorer l\'optimisation des mots-clés existants',
                'description' => count( $poorly_optimized ) . ' mots-clés sont mal optimisés dans Boss SEO.',
                'actions' => array(
                    'Ajouter les mots-clés dans les titres SEO',
                    'Rédiger des méta-descriptions incluant les mots-clés',
                    'Optimiser le contenu pour les mots-clés focus'
                ),
                'estimated_impact' => 'Amélioration de 15-30% du classement'
            );
        }

        // Recommandations basées sur GSC
        if ( ! is_wp_error( $gsc_data ) ) {
            $gsc_keywords = $gsc_data['keywords'] ?? array();

            // Opportunités de position
            $position_opportunities = array_filter( $gsc_keywords, function( $k ) {
                return $k['position'] > 3 && $k['position'] <= 10 && $k['impressions'] >= 100;
            } );

            if ( count( $position_opportunities ) > 0 ) {
                $recommendations[] = array(
                    'category' => 'position',
                    'priority' => 'medium',
                    'title' => 'Améliorer le classement des mots-clés en page 1',
                    'description' => count( $position_opportunities ) . ' mots-clés sont en page 1 mais peuvent être mieux classés.',
                    'actions' => array(
                        'Optimiser le contenu pour ces mots-clés',
                        'Améliorer les signaux de pertinence',
                        'Obtenir des liens internes et externes'
                    ),
                    'estimated_impact' => 'Augmentation du trafic de 20-50%'
                );
            }

            // Opportunités de CTR
            $ctr_opportunities = array_filter( $gsc_keywords, function( $k ) {
                return $k['ctr'] < $this->config['low_ctr_threshold'] && $k['impressions'] >= 100;
            } );

            if ( count( $ctr_opportunities ) > 0 ) {
                $recommendations[] = array(
                    'category' => 'ctr',
                    'priority' => 'medium',
                    'title' => 'Améliorer le taux de clic (CTR)',
                    'description' => count( $ctr_opportunities ) . ' mots-clés ont un CTR faible malgré une bonne visibilité.',
                    'actions' => array(
                        'Réécrire les titres pour les rendre plus attractifs',
                        'Optimiser les méta-descriptions',
                        'Utiliser des rich snippets'
                    ),
                    'estimated_impact' => 'Augmentation du trafic de 10-25%'
                );
            }
        }

        return $recommendations;
    }

    /**
     * Génère un plan d'action.
     *
     * @since    1.2.0
     * @param    array    $gsc_data         Données GSC.
     * @param    array    $boss_keywords    Mots-clés Boss SEO.
     * @return   array                      Plan d'action.
     */
    private function generate_action_plan( $gsc_data, $boss_keywords ) {
        $action_plan = array(
            'immediate_actions' => array(),
            'short_term_goals' => array(),
            'long_term_strategy' => array()
        );

        // Actions immédiates
        $missing_meta = array_filter( $boss_keywords, function( $k ) {
            return ! $k['has_meta_description'];
        } );

        if ( count( $missing_meta ) > 0 ) {
            $action_plan['immediate_actions'][] = array(
                'task' => 'Ajouter les méta-descriptions manquantes',
                'description' => 'Rédiger des méta-descriptions pour ' . count( $missing_meta ) . ' pages',
                'estimated_time' => '2-4 heures',
                'priority' => 'high',
                'pages_affected' => count( $missing_meta )
            );
        }

        // Objectifs à court terme
        if ( ! is_wp_error( $gsc_data ) ) {
            $opportunities = array_filter( $gsc_data['keywords'] ?? array(), function( $k ) {
                return $k['position'] > 3 && $k['position'] <= 10;
            } );

            if ( count( $opportunities ) > 0 ) {
                $action_plan['short_term_goals'][] = array(
                    'goal' => 'Améliorer le classement des mots-clés en page 1',
                    'target' => 'Faire passer ' . min( count( $opportunities ), 10 ) . ' mots-clés dans le top 3',
                    'timeline' => '4-8 semaines',
                    'key_actions' => array(
                        'Optimiser le contenu existant',
                        'Améliorer les liens internes',
                        'Créer du contenu de support'
                    )
                );
            }
        }

        // Stratégie à long terme
        $action_plan['long_term_strategy'][] = array(
            'strategy' => 'Expansion sémantique',
            'description' => 'Développer la couverture de mots-clés et créer des clusters thématiques',
            'timeline' => '3-6 mois',
            'milestones' => array(
                'Identifier 50 nouveaux mots-clés cibles',
                'Créer du contenu pour les lacunes identifiées',
                'Mettre en place un suivi de performance'
            )
        );

        return $action_plan;
    }
}
