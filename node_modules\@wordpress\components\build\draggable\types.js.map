{"version": 3, "names": [], "sources": ["@wordpress/components/src/draggable/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { <PERSON>ag<PERSON><PERSON>, ReactNode } from 'react';\n\nexport type DraggableProps = {\n\t/**\n\t * Children.\n\t */\n\tchildren: ( props: {\n\t\t/**\n\t\t * `onDragStart` handler.\n\t\t */\n\t\tonDraggableStart: ( event: DragEvent ) => void;\n\t\t/**\n\t\t * `onDragEnd` handler.\n\t\t */\n\t\tonDraggableEnd: ( event: DragEvent ) => void;\n\t} ) => JSX.Element | null;\n\t/**\n\t * Whether to append the cloned element to the `ownerDocument` body.\n\t * By default, elements sourced by id are appended to the element's wrapper.\n\t *\n\t * @default false\n\t */\n\tappendToOwnerDocument?: boolean;\n\t/**\n\t * Classname for the cloned element.\n\t */\n\tcloneClassname?: string;\n\t/**\n\t * The HTML id of the element to clone on drag\n\t */\n\telementId: string;\n\t/**\n\t * A function called when dragging ends. This callback receives the `event`\n\t * object from the `dragend` event as its first parameter.\n\t */\n\tonDragEnd?: ( event: DragEvent ) => void;\n\t/**\n\t * A function called when the element being dragged is dragged over a valid\n\t * drop target. This callback receives the `event` object from the\n\t * `dragover` event as its first parameter.\n\t */\n\tonDragOver?: ( event: DragEvent ) => void;\n\t/**\n\t * A function called when dragging starts. This callback receives the\n\t * `event` object from the `dragstart` event as its first parameter.\n\t */\n\tonDragStart?: ( event: DragEvent ) => void;\n\t/**\n\t * Arbitrary data object attached to the drag and drop event.\n\t */\n\ttransferData: unknown;\n\t/**\n\t * The transfer data type to set.\n\t *\n\t * @default 'text'\n\t */\n\t__experimentalTransferDataType?: string;\n\t/**\n\t * Component to show when dragging.\n\t */\n\t__experimentalDragComponent?: ReactNode;\n};\n"], "mappings": ""}