{"version": 3, "names": ["_focalPointStyle", "require", "_classnames", "_interopRequireDefault", "FocalPoint", "left", "top", "props", "classes", "classnames", "style", "_react", "createElement", "PointerCircle", "className"], "sources": ["@wordpress/components/src/focal-point-picker/focal-point.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport { PointerCircle } from './styles/focal-point-style';\n\n/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { FocalPointProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\nexport default function FocalPoint( {\n\tleft = '50%',\n\ttop = '50%',\n\t...props\n}: WordPressComponentProps< FocalPointProps, 'div' > ) {\n\tconst classes = classnames(\n\t\t'components-focal-point-picker__icon_container'\n\t);\n\n\tconst style = { left, top };\n\n\treturn <PointerCircle { ...props } className={ classes } style={ style } />;\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,gBAAA,GAAAC,OAAA;AAKA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAKe,SAASG,UAAUA,CAAE;EACnCC,IAAI,GAAG,KAAK;EACZC,GAAG,GAAG,KAAK;EACX,GAAGC;AAC+C,CAAC,EAAG;EACtD,MAAMC,OAAO,GAAG,IAAAC,mBAAU,EACzB,+CACD,CAAC;EAED,MAAMC,KAAK,GAAG;IAAEL,IAAI;IAAEC;EAAI,CAAC;EAE3B,OAAO,IAAAK,MAAA,CAAAC,aAAA,EAACZ,gBAAA,CAAAa,aAAa;IAAA,GAAMN,KAAK;IAAGO,SAAS,EAAGN,OAAS;IAACE,KAAK,EAAGA;EAAO,CAAE,CAAC;AAC5E"}