"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TokenInput = void 0;
exports.UnForwardedTokenInput = UnForwardedTokenInput;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function UnForwardedTokenInput(props, ref) {
  const {
    value,
    isExpanded,
    instanceId,
    selectedSuggestionIndex,
    className,
    onChange,
    onFocus,
    onBlur,
    ...restProps
  } = props;
  const [hasFocus, setHasFocus] = (0, _element.useState)(false);
  const size = value ? value.length + 1 : 0;
  const onChangeHandler = event => {
    if (onChange) {
      onChange({
        value: event.target.value
      });
    }
  };
  const onFocusHandler = e => {
    setHasFocus(true);
    onFocus?.(e);
  };
  const onBlurHandler = e => {
    setHasFocus(false);
    onBlur?.(e);
  };
  return (0, _react.createElement)("input", {
    ref: ref,
    id: `components-form-token-input-${instanceId}`,
    type: "text",
    ...restProps,
    value: value || '',
    onChange: onChangeHandler,
    onFocus: onFocusHandler,
    onBlur: onBlurHandler,
    size: size,
    className: (0, _classnames.default)(className, 'components-form-token-field__input'),
    autoComplete: "off",
    role: "combobox",
    "aria-expanded": isExpanded,
    "aria-autocomplete": "list",
    "aria-owns": isExpanded ? `components-form-token-suggestions-${instanceId}` : undefined,
    "aria-activedescendant":
    // Only add the `aria-activedescendant` attribute when:
    // - the user is actively interacting with the input (`hasFocus`)
    // - there is a selected suggestion (`selectedSuggestionIndex !== -1`)
    // - the list of suggestions are rendered in the DOM (`isExpanded`)
    hasFocus && selectedSuggestionIndex !== -1 && isExpanded ? `components-form-token-suggestions-${instanceId}-${selectedSuggestionIndex}` : undefined,
    "aria-describedby": `components-form-token-suggestions-howto-${instanceId}`
  });
}
const TokenInput = (0, _element.forwardRef)(UnForwardedTokenInput);
exports.TokenInput = TokenInput;
var _default = TokenInput;
exports.default = _default;
//# sourceMappingURL=token-input.js.map