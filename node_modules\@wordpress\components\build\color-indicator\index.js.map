{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "UnforwardedColorIndicator", "props", "forwardedRef", "className", "colorValue", "additionalProps", "_react", "createElement", "classnames", "style", "background", "ref", "ColorIndicator", "forwardRef", "exports", "_default", "default"], "sources": ["@wordpress/components/src/color-indicator/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport type { ColorIndicatorProps } from './types';\n\nfunction UnforwardedColorIndicator(\n\tprops: WordPressComponentProps< ColorIndicatorProps, 'span', false >,\n\tforwardedRef: ForwardedRef< HTMLSpanElement >\n) {\n\tconst { className, colorValue, ...additionalProps } = props;\n\n\treturn (\n\t\t<span\n\t\t\tclassName={ classnames( 'component-color-indicator', className ) }\n\t\t\tstyle={ { background: colorValue } }\n\t\t\tref={ forwardedRef }\n\t\t\t{ ...additionalProps }\n\t\t/>\n\t);\n}\n\n/**\n * ColorIndicator is a React component that renders a specific color in a\n * circle. It's often used to summarize a collection of used colors in a child\n * component.\n *\n * ```jsx\n * import { ColorIndicator } from '@wordpress/components';\n *\n * const MyColorIndicator = () => <ColorIndicator colorValue=\"#0073aa\" />;\n * ```\n */\nexport const ColorIndicator = forwardRef( UnforwardedColorIndicator );\n\nexport default ColorIndicator;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AASA,SAASE,yBAAyBA,CACjCC,KAAoE,EACpEC,YAA6C,EAC5C;EACD,MAAM;IAAEC,SAAS;IAAEC,UAAU;IAAE,GAAGC;EAAgB,CAAC,GAAGJ,KAAK;EAE3D,OACC,IAAAK,MAAA,CAAAC,aAAA;IACCJ,SAAS,EAAG,IAAAK,mBAAU,EAAE,2BAA2B,EAAEL,SAAU,CAAG;IAClEM,KAAK,EAAG;MAAEC,UAAU,EAAEN;IAAW,CAAG;IACpCO,GAAG,EAAGT,YAAc;IAAA,GACfG;EAAe,CACpB,CAAC;AAEJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,cAAc,GAAG,IAAAC,mBAAU,EAAEb,yBAA0B,CAAC;AAACc,OAAA,CAAAF,cAAA,GAAAA,cAAA;AAAA,IAAAG,QAAA,GAEvDH,cAAc;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}