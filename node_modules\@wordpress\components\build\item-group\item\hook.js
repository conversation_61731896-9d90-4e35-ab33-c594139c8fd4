"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useItem = useItem;
var _element = require("@wordpress/element");
var _context = require("../../context");
var styles = _interopRequireWildcard(require("../styles"));
var _context2 = require("../context");
var _useCx = require("../../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function useItem(props) {
  const {
    as: asProp,
    className,
    onClick,
    role = 'listitem',
    size: sizeProp,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'Item');
  const {
    spacedAround,
    size: contextSize
  } = (0, _context2.useItemGroupContext)();
  const size = sizeProp || contextSize;
  const as = asProp || (typeof onClick !== 'undefined' ? 'button' : 'div');
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => cx((as === 'button' || as === 'a') && styles.unstyledButton(as), styles.itemSizes[size] || styles.itemSizes.medium, styles.item, spacedAround && styles.spacedAround, className), [as, className, cx, size, spacedAround]);
  const wrapperClassName = cx(styles.itemWrapper);
  return {
    as,
    className: classes,
    onClick,
    wrapperClassName,
    role,
    ...otherProps
  };
}
//# sourceMappingURL=hook.js.map