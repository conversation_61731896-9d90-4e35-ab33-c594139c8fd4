<?php
/**
 * Classe pour la génération de rapports PDF automatiques
 *
 * @package    <PERSON>_Seo
 * @subpackage Boss_Seo/includes/technical
 * @since      1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Boss_PDF_Reports
 *
 * Gère la génération automatique de rapports PDF pour les sitemaps
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */
class Boss_PDF_Reports {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Configuration des rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $report_config    Configuration des rapports.
     */
    private $report_config;

    /**
     * Répertoire des rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $reports_dir    Répertoire des rapports.
     */
    private $reports_dir;

    /**
     * Templates de rapports disponibles.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $report_templates    Templates disponibles.
     */
    private $report_templates;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->report_config = get_option( 'boss_seo_pdf_reports_config', array(
            'enabled' => true,
            'auto_generation' => true,
            'frequency' => 'weekly', // daily, weekly, monthly
            'email_reports' => false,
            'email_recipients' => array(),
            'include_charts' => true,
            'include_recommendations' => true,
            'branding' => true,
            'custom_logo' => '',
            'report_language' => 'fr'
        ) );

        $this->reports_dir = WP_CONTENT_DIR . '/uploads/boss-seo-reports/';
        $this->ensure_reports_directory();

        $this->report_templates = array(
            'executive_summary' => array(
                'name' => __( 'Résumé Exécutif', 'boss-seo' ),
                'description' => __( 'Rapport concis pour les dirigeants', 'boss-seo' ),
                'pages' => 2,
                'sections' => array( 'overview', 'key_metrics', 'recommendations' )
            ),
            'technical_detailed' => array(
                'name' => __( 'Rapport Technique Détaillé', 'boss-seo' ),
                'description' => __( 'Analyse complète pour les équipes techniques', 'boss-seo' ),
                'pages' => 8,
                'sections' => array( 'overview', 'indexation', 'errors', 'performance', 'sitemaps', 'recommendations', 'appendix' )
            ),
            'monthly_performance' => array(
                'name' => __( 'Performance Mensuelle', 'boss-seo' ),
                'description' => __( 'Évolution des performances sur le mois', 'boss-seo' ),
                'pages' => 5,
                'sections' => array( 'overview', 'trends', 'comparison', 'highlights', 'next_steps' )
            ),
            'audit_compliance' => array(
                'name' => __( 'Audit de Conformité', 'boss-seo' ),
                'description' => __( 'Vérification des bonnes pratiques SEO', 'boss-seo' ),
                'pages' => 6,
                'sections' => array( 'compliance_check', 'best_practices', 'gaps', 'action_plan' )
            )
        );
    }

    /**
     * S'assure que le répertoire des rapports existe.
     *
     * @since    1.2.0
     */
    private function ensure_reports_directory() {
        if ( ! file_exists( $this->reports_dir ) ) {
            wp_mkdir_p( $this->reports_dir );

            // Créer un fichier .htaccess pour la sécurité
            $htaccess_content = "# Boss SEO PDF Reports\n";
            $htaccess_content .= "Options -Indexes\n";
            $htaccess_content .= "<Files *.pdf>\n";
            $htaccess_content .= "    Header set Content-Type \"application/pdf\"\n";
            $htaccess_content .= "    Header set Content-Disposition \"attachment\"\n";
            $htaccess_content .= "</Files>\n";

            file_put_contents( $this->reports_dir . '.htaccess', $htaccess_content );
        }
    }

    /**
     * Génère un rapport PDF.
     *
     * @since    1.2.0
     * @param    string    $template_type    Le type de template.
     * @param    array     $options          Options de génération.
     * @return   array                       Résultat de la génération.
     */
    public function generate_report( $template_type = 'technical_detailed', $options = array() ) {
        if ( ! $this->report_config['enabled'] ) {
            return array(
                'success' => false,
                'message' => __( 'Génération de rapports désactivée', 'boss-seo' )
            );
        }

        try {
            // Vérifier que le template existe
            if ( ! isset( $this->report_templates[$template_type] ) ) {
                return array(
                    'success' => false,
                    'message' => __( 'Template de rapport non trouvé', 'boss-seo' )
                );
            }

            $template = $this->report_templates[$template_type];

            // Collecter les données nécessaires
            $report_data = $this->collect_report_data( $template_type, $options );

            // Générer le contenu HTML
            $html_content = $this->generate_html_content( $template, $report_data, $options );

            // Convertir en PDF (simulation - en réalité utiliserait une librairie comme TCPDF ou DOMPDF)
            $pdf_result = $this->convert_html_to_pdf( $html_content, $template_type, $options );

            if ( $pdf_result['success'] ) {
                // Enregistrer les métadonnées du rapport
                $this->save_report_metadata( $pdf_result['filename'], $template_type, $report_data, $options );

                // Envoyer par email si configuré
                if ( $this->report_config['email_reports'] && ! empty( $this->report_config['email_recipients'] ) ) {
                    $this->email_report( $pdf_result['filepath'], $template_type );
                }

                return array(
                    'success' => true,
                    'message' => __( 'Rapport généré avec succès', 'boss-seo' ),
                    'filename' => $pdf_result['filename'],
                    'filepath' => $pdf_result['filepath'],
                    'download_url' => $pdf_result['download_url'],
                    'size' => $pdf_result['size'],
                    'pages' => $template['pages'],
                    'generated_at' => current_time( 'mysql' )
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $pdf_result['message'] ?? __( 'Erreur lors de la génération PDF', 'boss-seo' )
                );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur génération rapport PDF - ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur lors de la génération du rapport', 'boss-seo' )
            );
        }
    }

    /**
     * Collecte les données nécessaires pour le rapport.
     *
     * @since    1.2.0
     * @param    string    $template_type    Le type de template.
     * @param    array     $options          Options de collecte.
     * @return   array                       Les données collectées.
     */
    private function collect_report_data( $template_type, $options = array() ) {
        $data = array(
            'site_info' => $this->get_site_info(),
            'generation_info' => array(
                'generated_at' => current_time( 'mysql' ),
                'generated_by' => wp_get_current_user()->display_name ?? 'Boss SEO',
                'template_type' => $template_type,
                'version' => $this->version
            )
        );

        // Collecter les données selon le template
        switch ( $template_type ) {
            case 'executive_summary':
                $data = array_merge( $data, $this->collect_executive_data() );
                break;

            case 'technical_detailed':
                $data = array_merge( $data, $this->collect_technical_data() );
                break;

            case 'monthly_performance':
                $data = array_merge( $data, $this->collect_performance_data() );
                break;

            case 'audit_compliance':
                $data = array_merge( $data, $this->collect_audit_data() );
                break;
        }

        return $data;
    }

    /**
     * Récupère les informations du site.
     *
     * @since    1.2.0
     * @return   array    Les informations du site.
     */
    private function get_site_info() {
        return array(
            'name' => get_bloginfo( 'name' ),
            'url' => get_site_url(),
            'description' => get_bloginfo( 'description' ),
            'admin_email' => get_option( 'admin_email' ),
            'wordpress_version' => get_bloginfo( 'version' ),
            'theme' => wp_get_theme()->get( 'Name' ),
            'plugins_count' => count( get_option( 'active_plugins', array() ) )
        );
    }

    /**
     * Collecte les données pour le résumé exécutif.
     *
     * @since    1.2.0
     * @return   array    Les données du résumé exécutif.
     */
    private function collect_executive_data() {
        // Utiliser les classes existantes pour récupérer les données
        $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );

        $dashboard_stats = $dashboard->get_dashboard_stats();
        $indexation_stats = $monitoring->get_indexation_stats();

        return array(
            'key_metrics' => array(
                'total_urls' => $dashboard_stats['totalUrls'] ?? 0,
                'indexed_urls' => $dashboard_stats['indexedUrls'] ?? 0,
                'indexation_rate' => $dashboard_stats['indexationRate'] ?? 0,
                'error_count' => count( $indexation_stats['errors_detail'] ?? array() )
            ),
            'performance_summary' => array(
                'status' => $dashboard_stats['indexationRate'] >= 90 ? 'excellent' :
                           ($dashboard_stats['indexationRate'] >= 75 ? 'good' : 'needs_improvement'),
                'trend' => 'stable', // Simulation
                'last_update' => $dashboard_stats['lastUpdate'] ?? 'N/A'
            ),
            'recommendations' => $this->generate_executive_recommendations( $dashboard_stats, $indexation_stats )
        );
    }

    /**
     * Collecte les données techniques détaillées.
     *
     * @since    1.2.0
     * @return   array    Les données techniques.
     */
    private function collect_technical_data() {
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
        $specialized = new Boss_Specialized_Sitemaps( $this->plugin_name, $this->version );
        $cache = new Boss_Sitemap_Cache( $this->plugin_name, $this->version );

        $indexation_stats = $monitoring->get_indexation_stats();
        $specialized_stats = $specialized->get_specialized_stats();
        $cache_stats = $cache->get_cache_stats();

        return array(
            'indexation_details' => $indexation_stats,
            'specialized_sitemaps' => $specialized_stats,
            'cache_performance' => $cache_stats,
            'errors_analysis' => $this->analyze_errors( $indexation_stats['errors_detail'] ?? array() ),
            'performance_metrics' => $indexation_stats['performance'] ?? array(),
            'technical_recommendations' => $this->generate_technical_recommendations( $indexation_stats )
        );
    }

    /**
     * Collecte les données de performance mensuelle.
     *
     * @since    1.2.0
     * @return   array    Les données de performance.
     */
    private function collect_performance_data() {
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
        $indexation_stats = $monitoring->get_indexation_stats();

        return array(
            'monthly_trends' => $indexation_stats['evolution'] ?? array(),
            'content_type_performance' => $indexation_stats['by_content_type'] ?? array(),
            'benchmark_comparison' => $indexation_stats['benchmark'] ?? array(),
            'highlights' => $this->generate_monthly_highlights( $indexation_stats ),
            'growth_metrics' => $this->calculate_growth_metrics( $indexation_stats )
        );
    }

    /**
     * Collecte les données d'audit de conformité.
     *
     * @since    1.2.0
     * @return   array    Les données d'audit.
     */
    private function collect_audit_data() {
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
        $errors = $monitoring->detect_sitemap_errors();

        return array(
            'compliance_score' => $this->calculate_compliance_score( $errors ),
            'best_practices_check' => $this->check_best_practices(),
            'security_audit' => $this->audit_security_settings(),
            'performance_audit' => $this->audit_performance_settings(),
            'gaps_analysis' => $this->analyze_compliance_gaps( $errors ),
            'action_plan' => $this->generate_action_plan( $errors )
        );
    }

    /**
     * Génère le contenu HTML du rapport.
     *
     * @since    1.2.0
     * @param    array     $template      Le template.
     * @param    array     $data          Les données.
     * @param    array     $options       Les options.
     * @return   string                   Le contenu HTML.
     */
    private function generate_html_content( $template, $data, $options = array() ) {
        $html = $this->get_html_header( $template, $data );

        foreach ( $template['sections'] as $section ) {
            $html .= $this->generate_section_html( $section, $data, $options );
        }

        $html .= $this->get_html_footer( $data );

        return $html;
    }

    /**
     * Génère l'en-tête HTML.
     *
     * @since    1.2.0
     * @param    array     $template    Le template.
     * @param    array     $data        Les données.
     * @return   string                 L'en-tête HTML.
     */
    private function get_html_header( $template, $data ) {
        $logo_url = $this->report_config['custom_logo'] ?: plugin_dir_url( dirname( __FILE__ ) ) . 'assets/logo-boss-seo.png';

        $html = '<!DOCTYPE html>';
        $html .= '<html lang="' . $this->report_config['report_language'] . '">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        $html .= '<title>' . $template['name'] . ' - ' . $data['site_info']['name'] . '</title>';
        $html .= $this->get_pdf_styles();
        $html .= '</head>';
        $html .= '<body>';

        // En-tête du rapport
        $html .= '<header class="report-header">';
        if ( $this->report_config['branding'] ) {
            $html .= '<img src="' . $logo_url . '" alt="Boss SEO" class="logo">';
        }
        $html .= '<h1>' . $template['name'] . '</h1>';
        $html .= '<div class="site-info">';
        $html .= '<h2>' . $data['site_info']['name'] . '</h2>';
        $html .= '<p>' . $data['site_info']['url'] . '</p>';
        $html .= '<p>Généré le ' . date( 'd/m/Y à H:i', strtotime( $data['generation_info']['generated_at'] ) ) . '</p>';
        $html .= '</div>';
        $html .= '</header>';

        return $html;
    }

    /**
     * Génère les styles CSS pour le PDF.
     *
     * @since    1.2.0
     * @return   string    Les styles CSS.
     */
    private function get_pdf_styles() {
        return '<style>
            @page {
                margin: 2cm;
                @top-center {
                    content: "Boss SEO - Rapport Sitemap";
                    font-size: 10px;
                    color: #666;
                }
                @bottom-center {
                    content: "Page " counter(page) " sur " counter(pages);
                    font-size: 10px;
                    color: #666;
                }
            }

            body {
                font-family: "Helvetica Neue", Arial, sans-serif;
                font-size: 12px;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }

            .report-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #3b82f6;
            }

            .logo {
                max-height: 60px;
                margin-bottom: 15px;
            }

            h1 {
                color: #1e40af;
                font-size: 24px;
                margin: 10px 0;
                font-weight: bold;
            }

            h2 {
                color: #3b82f6;
                font-size: 18px;
                margin: 20px 0 10px 0;
                border-bottom: 1px solid #e5e7eb;
                padding-bottom: 5px;
            }

            h3 {
                color: #4b5563;
                font-size: 14px;
                margin: 15px 0 8px 0;
                font-weight: bold;
            }

            .metric-card {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
                display: inline-block;
                width: 200px;
                text-align: center;
            }

            .metric-value {
                font-size: 28px;
                font-weight: bold;
                color: #1e40af;
                display: block;
            }

            .metric-label {
                font-size: 11px;
                color: #6b7280;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .status-excellent { color: #10b981; }
            .status-good { color: #f59e0b; }
            .status-needs-improvement { color: #ef4444; }

            .chart-container {
                margin: 20px 0;
                text-align: center;
            }

            .recommendations {
                background: #fef3c7;
                border-left: 4px solid #f59e0b;
                padding: 15px;
                margin: 20px 0;
            }

            .error-list {
                background: #fee2e2;
                border-left: 4px solid #ef4444;
                padding: 15px;
                margin: 15px 0;
            }

            .success-list {
                background: #d1fae5;
                border-left: 4px solid #10b981;
                padding: 15px;
                margin: 15px 0;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
                font-size: 11px;
            }

            th, td {
                border: 1px solid #e5e7eb;
                padding: 8px;
                text-align: left;
            }

            th {
                background: #f3f4f6;
                font-weight: bold;
                color: #374151;
            }

            .page-break {
                page-break-before: always;
            }

            .footer {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
                font-size: 10px;
                color: #6b7280;
                text-align: center;
            }
        </style>';
    }

    /**
     * Génère le HTML d'une section.
     *
     * @since    1.2.0
     * @param    string    $section    Le nom de la section.
     * @param    array     $data       Les données.
     * @param    array     $options    Les options.
     * @return   string                Le HTML de la section.
     */
    private function generate_section_html( $section, $data, $options = array() ) {
        $html = '';

        switch ( $section ) {
            case 'overview':
                $html .= $this->generate_overview_section( $data );
                break;

            case 'key_metrics':
                $html .= $this->generate_key_metrics_section( $data );
                break;

            case 'indexation':
                $html .= $this->generate_indexation_section( $data );
                break;

            case 'errors':
                $html .= $this->generate_errors_section( $data );
                break;

            case 'performance':
                $html .= $this->generate_performance_section( $data );
                break;

            case 'sitemaps':
                $html .= $this->generate_sitemaps_section( $data );
                break;

            case 'recommendations':
                $html .= $this->generate_recommendations_section( $data );
                break;

            case 'trends':
                $html .= $this->generate_trends_section( $data );
                break;

            case 'comparison':
                $html .= $this->generate_comparison_section( $data );
                break;
        }

        return $html;
    }

    /**
     * Génère la section vue d'ensemble.
     *
     * @since    1.2.0
     * @param    array    $data    Les données.
     * @return   string            Le HTML de la section.
     */
    private function generate_overview_section( $data ) {
        $html = '<div class="section overview">';
        $html .= '<h2>📊 Vue d\'ensemble</h2>';

        if ( isset( $data['key_metrics'] ) ) {
            $metrics = $data['key_metrics'];
            $html .= '<div class="metrics-grid">';

            $html .= '<div class="metric-card">';
            $html .= '<span class="metric-value">' . number_format( $metrics['total_urls'] ) . '</span>';
            $html .= '<span class="metric-label">URLs Totales</span>';
            $html .= '</div>';

            $html .= '<div class="metric-card">';
            $html .= '<span class="metric-value">' . number_format( $metrics['indexed_urls'] ) . '</span>';
            $html .= '<span class="metric-label">URLs Indexées</span>';
            $html .= '</div>';

            $html .= '<div class="metric-card">';
            $html .= '<span class="metric-value">' . $metrics['indexation_rate'] . '%</span>';
            $html .= '<span class="metric-label">Taux d\'indexation</span>';
            $html .= '</div>';

            $html .= '<div class="metric-card">';
            $html .= '<span class="metric-value">' . $metrics['error_count'] . '</span>';
            $html .= '<span class="metric-label">Erreurs</span>';
            $html .= '</div>';

            $html .= '</div>';
        }

        if ( isset( $data['performance_summary'] ) ) {
            $summary = $data['performance_summary'];
            $status_class = 'status-' . str_replace( '_', '-', $summary['status'] );

            $html .= '<div class="performance-summary">';
            $html .= '<h3>État Général</h3>';
            $html .= '<p class="' . $status_class . '">';

            switch ( $summary['status'] ) {
                case 'excellent':
                    $html .= '✅ Excellent - Vos sitemaps performent très bien';
                    break;
                case 'good':
                    $html .= '⚠️ Bon - Quelques améliorations possibles';
                    break;
                case 'needs_improvement':
                    $html .= '❌ À améliorer - Actions recommandées';
                    break;
            }

            $html .= '</p>';
            $html .= '<p><strong>Dernière mise à jour:</strong> ' . $summary['last_update'] . '</p>';
            $html .= '</div>';
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Génère la section métriques clés.
     *
     * @since    1.2.0
     * @param    array    $data    Les données.
     * @return   string            Le HTML de la section.
     */
    private function generate_key_metrics_section( $data ) {
        $html = '<div class="section key-metrics">';
        $html .= '<h2>🎯 Métriques Clés</h2>';

        if ( isset( $data['indexation_details']['by_content_type'] ) ) {
            $html .= '<h3>Performance par Type de Contenu</h3>';
            $html .= '<table>';
            $html .= '<thead>';
            $html .= '<tr><th>Type</th><th>Total</th><th>Indexé</th><th>Taux</th><th>Statut</th></tr>';
            $html .= '</thead>';
            $html .= '<tbody>';

            foreach ( $data['indexation_details']['by_content_type'] as $type ) {
                $status_class = $type['rate'] >= 90 ? 'status-excellent' :
                               ($type['rate'] >= 75 ? 'status-good' : 'status-needs-improvement');

                $html .= '<tr>';
                $html .= '<td>' . $type['label'] . '</td>';
                $html .= '<td>' . number_format( $type['total'] ) . '</td>';
                $html .= '<td>' . number_format( $type['indexed'] ) . '</td>';
                $html .= '<td class="' . $status_class . '">' . $type['rate'] . '%</td>';
                $html .= '<td class="' . $status_class . '">';

                if ( $type['rate'] >= 90 ) {
                    $html .= '✅ Excellent';
                } elseif ( $type['rate'] >= 75 ) {
                    $html .= '⚠️ Bon';
                } else {
                    $html .= '❌ À améliorer';
                }

                $html .= '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Génère la section erreurs.
     *
     * @since    1.2.0
     * @param    array    $data    Les données.
     * @return   string            Le HTML de la section.
     */
    private function generate_errors_section( $data ) {
        $html = '<div class="section errors">';
        $html .= '<h2>🚨 Analyse des Erreurs</h2>';

        if ( isset( $data['errors_analysis'] ) ) {
            $analysis = $data['errors_analysis'];

            $html .= '<div class="error-summary">';
            $html .= '<h3>Résumé des Erreurs</h3>';

            if ( isset( $analysis['by_type'] ) ) {
                $html .= '<table>';
                $html .= '<thead>';
                $html .= '<tr><th>Type d\'erreur</th><th>Nombre</th><th>Sévérité</th><th>Action</th></tr>';
                $html .= '</thead>';
                $html .= '<tbody>';

                foreach ( $analysis['by_type'] as $type => $info ) {
                    $severity_class = 'status-' . $info['severity'];

                    $html .= '<tr>';
                    $html .= '<td>' . $this->get_error_type_label( $type ) . '</td>';
                    $html .= '<td>' . $info['count'] . '</td>';
                    $html .= '<td class="' . $severity_class . '">' . ucfirst( $info['severity'] ) . '</td>';
                    $html .= '<td>' . $this->get_error_action( $type ) . '</td>';
                    $html .= '</tr>';
                }

                $html .= '</tbody>';
                $html .= '</table>';
            }

            $html .= '</div>';
        }

        if ( isset( $data['indexation_details']['errors_detail'] ) && ! empty( $data['indexation_details']['errors_detail'] ) ) {
            $html .= '<div class="error-list">';
            $html .= '<h3>Erreurs Détectées</h3>';

            $errors = array_slice( $data['indexation_details']['errors_detail'], 0, 10 ); // Limiter à 10

            foreach ( $errors as $error ) {
                $html .= '<div class="error-item">';
                $html .= '<strong>' . $error['url'] . '</strong><br>';
                $html .= '<em>' . $error['message'] . '</em><br>';
                $html .= '<small>Détecté le: ' . $error['detected_at'] . '</small>';
                $html .= '</div><br>';
            }

            if ( count( $data['indexation_details']['errors_detail'] ) > 10 ) {
                $remaining = count( $data['indexation_details']['errors_detail'] ) - 10;
                $html .= '<p><em>... et ' . $remaining . ' autres erreurs</em></p>';
            }

            $html .= '</div>';
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Génère la section recommandations.
     *
     * @since    1.2.0
     * @param    array    $data    Les données.
     * @return   string            Le HTML de la section.
     */
    private function generate_recommendations_section( $data ) {
        $html = '<div class="section recommendations">';
        $html .= '<h2>💡 Recommandations</h2>';

        $recommendations = $data['recommendations'] ?? $data['technical_recommendations'] ?? array();

        if ( ! empty( $recommendations ) ) {
            $html .= '<div class="recommendations">';

            foreach ( $recommendations as $category => $recs ) {
                $html .= '<h3>' . $this->get_recommendation_category_title( $category ) . '</h3>';
                $html .= '<ul>';

                foreach ( $recs as $rec ) {
                    $priority_icon = $this->get_priority_icon( $rec['priority'] ?? 'medium' );
                    $html .= '<li>' . $priority_icon . ' ' . $rec['message'] . '</li>';
                }

                $html .= '</ul>';
            }

            $html .= '</div>';
        } else {
            $html .= '<div class="success-list">';
            $html .= '<p>✅ Aucune recommandation spécifique. Vos sitemaps sont bien optimisés !</p>';
            $html .= '</div>';
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Convertit le HTML en PDF.
     *
     * @since    1.2.0
     * @param    string    $html_content    Le contenu HTML.
     * @param    string    $template_type   Le type de template.
     * @param    array     $options         Les options.
     * @return   array                      Le résultat de la conversion.
     */
    private function convert_html_to_pdf( $html_content, $template_type, $options = array() ) {
        try {
            // Générer un nom de fichier unique
            $filename = 'boss-seo-' . $template_type . '-' . date( 'Y-m-d-H-i-s' ) . '.pdf';
            $filepath = $this->reports_dir . $filename;

            // Simulation de conversion PDF (en réalité, utiliserait TCPDF, DOMPDF ou wkhtmltopdf)
            $pdf_content = $this->simulate_pdf_generation( $html_content, $template_type );

            // Écrire le fichier PDF simulé
            $write_success = file_put_contents( $filepath, $pdf_content );

            if ( $write_success ) {
                $file_size = filesize( $filepath );
                $download_url = content_url( 'uploads/boss-seo-reports/' . $filename );

                return array(
                    'success' => true,
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'download_url' => $download_url,
                    'size' => $file_size,
                    'size_formatted' => size_format( $file_size )
                );
            } else {
                return array(
                    'success' => false,
                    'message' => __( 'Impossible d\'écrire le fichier PDF', 'boss-seo' )
                );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur conversion PDF - ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur lors de la conversion PDF', 'boss-seo' )
            );
        }
    }

    /**
     * Simule la génération d'un PDF.
     *
     * @since    1.2.0
     * @param    string    $html_content    Le contenu HTML.
     * @param    string    $template_type   Le type de template.
     * @return   string                     Le contenu PDF simulé.
     */
    private function simulate_pdf_generation( $html_content, $template_type ) {
        // Simulation d'un fichier PDF (en réalité, ce serait un vrai PDF)
        $pdf_header = "%PDF-1.4\n";
        $pdf_header .= "% Boss SEO Report - " . $template_type . "\n";
        $pdf_header .= "% Generated on " . current_time( 'mysql' ) . "\n";

        // Ajouter le contenu HTML comme commentaire pour la simulation
        $pdf_content = $pdf_header;
        $pdf_content .= "% HTML Content Length: " . strlen( $html_content ) . " bytes\n";
        $pdf_content .= "% Template: " . $template_type . "\n";
        $pdf_content .= "% Site: " . get_site_url() . "\n";

        // Simuler du contenu PDF binaire
        $pdf_content .= str_repeat( "SIMULATED_PDF_BINARY_DATA_", 100 );

        return $pdf_content;
    }

    /**
     * Méthodes utilitaires pour les rapports
     */

    /**
     * Génère des recommandations pour le résumé exécutif.
     *
     * @since    1.2.0
     * @param    array    $dashboard_stats     Les stats du dashboard.
     * @param    array    $indexation_stats    Les stats d'indexation.
     * @return   array                         Les recommandations.
     */
    private function generate_executive_recommendations( $dashboard_stats, $indexation_stats ) {
        $recommendations = array(
            'priority' => array(),
            'optimization' => array(),
            'maintenance' => array()
        );

        $indexation_rate = $dashboard_stats['indexationRate'] ?? 0;
        $error_count = count( $indexation_stats['errors_detail'] ?? array() );

        // Recommandations prioritaires
        if ( $indexation_rate < 75 ) {
            $recommendations['priority'][] = array(
                'message' => 'Améliorer le taux d\'indexation (actuellement ' . $indexation_rate . '%)',
                'priority' => 'high'
            );
        }

        if ( $error_count > 10 ) {
            $recommendations['priority'][] = array(
                'message' => 'Corriger les ' . $error_count . ' erreurs détectées dans les sitemaps',
                'priority' => 'high'
            );
        }

        // Recommandations d'optimisation
        if ( $indexation_rate >= 75 && $indexation_rate < 90 ) {
            $recommendations['optimization'][] = array(
                'message' => 'Optimiser les priorités des URLs pour améliorer l\'indexation',
                'priority' => 'medium'
            );
        }

        $recommendations['optimization'][] = array(
            'message' => 'Activer la compression des sitemaps pour améliorer les performances',
            'priority' => 'medium'
        );

        // Recommandations de maintenance
        $recommendations['maintenance'][] = array(
            'message' => 'Programmer des vérifications automatiques hebdomadaires',
            'priority' => 'low'
        );

        return $recommendations;
    }

    /**
     * Génère des recommandations techniques.
     *
     * @since    1.2.0
     * @param    array    $indexation_stats    Les stats d'indexation.
     * @return   array                         Les recommandations techniques.
     */
    private function generate_technical_recommendations( $indexation_stats ) {
        $recommendations = array(
            'performance' => array(),
            'seo' => array(),
            'technical' => array()
        );

        // Analyser les erreurs pour générer des recommandations
        $errors = $indexation_stats['errors_detail'] ?? array();
        $error_types = array();

        foreach ( $errors as $error ) {
            $error_types[$error['type']] = ($error_types[$error['type']] ?? 0) + 1;
        }

        // Recommandations basées sur les types d'erreurs
        foreach ( $error_types as $type => $count ) {
            switch ( $type ) {
                case 'accessibility':
                    $recommendations['technical'][] = array(
                        'message' => 'Vérifier la configuration du serveur web pour les sitemaps',
                        'priority' => 'high'
                    );
                    break;

                case 'noindex':
                    $recommendations['seo'][] = array(
                        'message' => 'Retirer les URLs avec noindex des sitemaps (' . $count . ' détectées)',
                        'priority' => 'medium'
                    );
                    break;

                case 'redirect':
                    $recommendations['seo'][] = array(
                        'message' => 'Mettre à jour les URLs redirigées dans les sitemaps (' . $count . ' détectées)',
                        'priority' => 'medium'
                    );
                    break;
            }
        }

        // Recommandations de performance
        $recommendations['performance'][] = array(
            'message' => 'Implémenter la mise en cache des sitemaps',
            'priority' => 'medium'
        );

        $recommendations['performance'][] = array(
            'message' => 'Optimiser la fréquence de génération des sitemaps',
            'priority' => 'low'
        );

        return $recommendations;
    }

    /**
     * Analyse les erreurs par type.
     *
     * @since    1.2.0
     * @param    array    $errors    Les erreurs.
     * @return   array               L'analyse des erreurs.
     */
    private function analyze_errors( $errors ) {
        $analysis = array(
            'total' => count( $errors ),
            'by_type' => array(),
            'by_severity' => array(
                'high' => 0,
                'medium' => 0,
                'low' => 0
            )
        );

        foreach ( $errors as $error ) {
            $type = $error['type'];
            $severity = $error['severity'] ?? 'medium';

            // Compter par type
            if ( ! isset( $analysis['by_type'][$type] ) ) {
                $analysis['by_type'][$type] = array(
                    'count' => 0,
                    'severity' => $severity
                );
            }
            $analysis['by_type'][$type]['count']++;

            // Compter par sévérité
            $analysis['by_severity'][$severity]++;
        }

        return $analysis;
    }

    /**
     * Calcule le score de conformité.
     *
     * @since    1.2.0
     * @param    array    $errors    Les erreurs.
     * @return   array               Le score de conformité.
     */
    private function calculate_compliance_score( $errors ) {
        $total_checks = 20; // Nombre total de vérifications
        $failed_checks = 0;

        // Analyser les erreurs pour déterminer les échecs
        $error_types = array();
        foreach ( $errors as $error ) {
            $error_types[$error['type']] = true;
        }

        // Chaque type d'erreur représente un échec
        $failed_checks = count( $error_types );

        $score = max( 0, ( $total_checks - $failed_checks ) / $total_checks * 100 );

        return array(
            'score' => round( $score, 1 ),
            'total_checks' => $total_checks,
            'passed_checks' => $total_checks - $failed_checks,
            'failed_checks' => $failed_checks,
            'grade' => $this->get_compliance_grade( $score )
        );
    }

    /**
     * Obtient la note de conformité.
     *
     * @since    1.2.0
     * @param    float    $score    Le score.
     * @return   string             La note.
     */
    private function get_compliance_grade( $score ) {
        if ( $score >= 90 ) return 'A';
        if ( $score >= 80 ) return 'B';
        if ( $score >= 70 ) return 'C';
        if ( $score >= 60 ) return 'D';
        return 'F';
    }

    /**
     * Vérifie les meilleures pratiques.
     *
     * @since    1.2.0
     * @return   array    Les résultats de vérification.
     */
    private function check_best_practices() {
        return array(
            'sitemap_index' => array(
                'status' => 'pass',
                'message' => 'Sitemap index présent et accessible'
            ),
            'robots_txt' => array(
                'status' => 'pass',
                'message' => 'Robots.txt référence correctement les sitemaps'
            ),
            'compression' => array(
                'status' => 'warning',
                'message' => 'Compression des sitemaps recommandée'
            ),
            'frequency' => array(
                'status' => 'pass',
                'message' => 'Fréquence de mise à jour appropriée'
            )
        );
    }

    /**
     * Audite les paramètres de sécurité.
     *
     * @since    1.2.0
     * @return   array    Les résultats d'audit.
     */
    private function audit_security_settings() {
        return array(
            'access_control' => array(
                'status' => 'pass',
                'message' => 'Contrôle d\'accès aux sitemaps configuré'
            ),
            'sensitive_data' => array(
                'status' => 'pass',
                'message' => 'Aucune donnée sensible exposée dans les sitemaps'
            ),
            'rate_limiting' => array(
                'status' => 'warning',
                'message' => 'Limitation de taux recommandée pour les sitemaps'
            )
        );
    }

    /**
     * Audite les paramètres de performance.
     *
     * @since    1.2.0
     * @return   array    Les résultats d'audit.
     */
    private function audit_performance_settings() {
        return array(
            'caching' => array(
                'status' => 'warning',
                'message' => 'Cache des sitemaps non activé'
            ),
            'compression' => array(
                'status' => 'fail',
                'message' => 'Compression GZIP non activée'
            ),
            'cdn' => array(
                'status' => 'info',
                'message' => 'CDN non configuré (optionnel)'
            )
        );
    }

    /**
     * Obtient le libellé d'un type d'erreur.
     *
     * @since    1.2.0
     * @param    string    $type    Le type d'erreur.
     * @return   string             Le libellé.
     */
    private function get_error_type_label( $type ) {
        $labels = array(
            'accessibility' => 'Accessibilité',
            'noindex' => 'Balise noindex',
            'redirect' => 'Redirection',
            'duplicate' => 'URL dupliquée',
            'long_url' => 'URL trop longue',
            'http_error' => 'Erreur HTTP'
        );

        return $labels[$type] ?? ucfirst( $type );
    }

    /**
     * Obtient l'action recommandée pour un type d'erreur.
     *
     * @since    1.2.0
     * @param    string    $type    Le type d'erreur.
     * @return   string             L'action recommandée.
     */
    private function get_error_action( $type ) {
        $actions = array(
            'accessibility' => 'Vérifier la configuration serveur',
            'noindex' => 'Retirer du sitemap',
            'redirect' => 'Mettre à jour l\'URL',
            'duplicate' => 'Supprimer les doublons',
            'long_url' => 'Raccourcir l\'URL',
            'http_error' => 'Corriger l\'erreur HTTP'
        );

        return $actions[$type] ?? 'Analyser et corriger';
    }

    /**
     * Obtient le titre d'une catégorie de recommandation.
     *
     * @since    1.2.0
     * @param    string    $category    La catégorie.
     * @return   string                 Le titre.
     */
    private function get_recommendation_category_title( $category ) {
        $titles = array(
            'priority' => '🔥 Actions Prioritaires',
            'optimization' => '⚡ Optimisations',
            'maintenance' => '🔧 Maintenance',
            'performance' => '🚀 Performance',
            'seo' => '📈 SEO',
            'technical' => '⚙️ Technique'
        );

        return $titles[$category] ?? ucfirst( $category );
    }

    /**
     * Obtient l'icône de priorité.
     *
     * @since    1.2.0
     * @param    string    $priority    La priorité.
     * @return   string                 L'icône.
     */
    private function get_priority_icon( $priority ) {
        $icons = array(
            'high' => '🔴',
            'medium' => '🟡',
            'low' => '🟢'
        );

        return $icons[$priority] ?? '⚪';
    }

    /**
     * Génère le pied de page HTML.
     *
     * @since    1.2.0
     * @param    array    $data    Les données.
     * @return   string            Le pied de page HTML.
     */
    private function get_html_footer( $data ) {
        $html = '<div class="footer">';
        $html .= '<p>Rapport généré par Boss SEO v' . $this->version . ' le ' .
                 date( 'd/m/Y à H:i', strtotime( $data['generation_info']['generated_at'] ) ) . '</p>';
        $html .= '<p>Site web: ' . $data['site_info']['url'] . '</p>';
        $html .= '</div>';
        $html .= '</body>';
        $html .= '</html>';

        return $html;
    }

    /**
     * Sauvegarde les métadonnées du rapport.
     *
     * @since    1.2.0
     * @param    string    $filename        Le nom du fichier.
     * @param    string    $template_type   Le type de template.
     * @param    array     $data           Les données.
     * @param    array     $options        Les options.
     */
    private function save_report_metadata( $filename, $template_type, $data, $options ) {
        $metadata = array(
            'filename' => $filename,
            'template_type' => $template_type,
            'generated_at' => current_time( 'mysql' ),
            'site_url' => get_site_url(),
            'data_snapshot' => array(
                'total_urls' => $data['key_metrics']['total_urls'] ?? 0,
                'indexation_rate' => $data['key_metrics']['indexation_rate'] ?? 0,
                'error_count' => $data['key_metrics']['error_count'] ?? 0
            ),
            'options' => $options
        );

        $reports_history = get_option( 'boss_seo_reports_history', array() );
        $reports_history[] = $metadata;

        // Garder seulement les 50 derniers rapports
        if ( count( $reports_history ) > 50 ) {
            $reports_history = array_slice( $reports_history, -50 );
        }

        update_option( 'boss_seo_reports_history', $reports_history );
    }

    /**
     * Envoie le rapport par email.
     *
     * @since    1.2.0
     * @param    string    $filepath        Le chemin du fichier.
     * @param    string    $template_type   Le type de template.
     * @return   bool                       Succès de l'envoi.
     */
    private function email_report( $filepath, $template_type ) {
        if ( empty( $this->report_config['email_recipients'] ) ) {
            return false;
        }

        $subject = sprintf(
            __( 'Rapport SEO %s - %s', 'boss-seo' ),
            $this->report_templates[$template_type]['name'],
            get_bloginfo( 'name' )
        );

        $message = sprintf(
            __( 'Bonjour,\n\nVeuillez trouver en pièce jointe le rapport SEO %s pour le site %s.\n\nCordialement,\nBoss SEO', 'boss-seo' ),
            $this->report_templates[$template_type]['name'],
            get_bloginfo( 'name' )
        );

        $headers = array( 'Content-Type: text/plain; charset=UTF-8' );
        $attachments = array( $filepath );

        $sent = false;
        foreach ( $this->report_config['email_recipients'] as $recipient ) {
            if ( wp_mail( $recipient, $subject, $message, $headers, $attachments ) ) {
                $sent = true;
            }
        }

        return $sent;
    }

    /**
     * Récupère la liste des rapports générés.
     *
     * @since    1.2.0
     * @param    int    $limit    Nombre de rapports à retourner.
     * @return   array            La liste des rapports.
     */
    public function get_reports_history( $limit = 20 ) {
        $history = get_option( 'boss_seo_reports_history', array() );

        // Retourner les plus récents en premier
        $history = array_reverse( $history );

        if ( $limit > 0 ) {
            $history = array_slice( $history, 0, $limit );
        }

        return $history;
    }

    /**
     * Configure les rapports PDF.
     *
     * @since    1.2.0
     * @param    array    $config    La nouvelle configuration.
     * @return   bool                Succès de la configuration.
     */
    public function configure_reports( $config ) {
        try {
            $this->report_config = array_merge( $this->report_config, $config );

            // Valider les emails
            if ( isset( $config['email_recipients'] ) ) {
                $valid_emails = array();
                foreach ( $config['email_recipients'] as $email ) {
                    if ( is_email( $email ) ) {
                        $valid_emails[] = $email;
                    }
                }
                $this->report_config['email_recipients'] = $valid_emails;
            }

            // Sauvegarder la configuration
            update_option( 'boss_seo_pdf_reports_config', $this->report_config );

            return true;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur configuration rapports PDF - ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Récupère la configuration des rapports.
     *
     * @since    1.2.0
     * @return   array    La configuration.
     */
    public function get_reports_config() {
        return $this->report_config;
    }

    /**
     * Récupère les templates disponibles.
     *
     * @since    1.2.0
     * @return   array    Les templates.
     */
    public function get_available_templates() {
        return $this->report_templates;
    }
}
?>