<?php
/**
 * Classe pour la génération de rapports PDF automatiques
 *
 * @package    <PERSON>_Seo
 * @subpackage Boss_Seo/includes/technical
 * @since      1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Boss_PDF_Reports
 *
 * Gère la génération automatique de rapports PDF pour les sitemaps
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */
class Boss_PDF_Reports {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Configuration des rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $report_config    Configuration des rapports.
     */
    private $report_config;

    /**
     * Répertoire des rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $reports_dir    Répertoire des rapports.
     */
    private $reports_dir;

    /**
     * Templates de rapports disponibles.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $report_templates    Templates disponibles.
     */
    private $report_templates;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        $this->report_config = get_option( 'boss_seo_pdf_reports_config', array(
            'enabled' => true,
            'auto_generation' => true,
            'frequency' => 'weekly', // daily, weekly, monthly
            'email_reports' => false,
            'email_recipients' => array(),
            'include_charts' => true,
            'include_recommendations' => true,
            'branding' => true,
            'custom_logo' => '',
            'report_language' => 'fr'
        ) );

        $this->reports_dir = WP_CONTENT_DIR . '/uploads/boss-seo-reports/';
        $this->ensure_reports_directory();

        $this->report_templates = array(
            'executive_summary' => array(
                'name' => __( 'Résumé Exécutif', 'boss-seo' ),
                'description' => __( 'Rapport concis pour les dirigeants', 'boss-seo' ),
                'pages' => 2,
                'sections' => array( 'overview', 'key_metrics', 'recommendations' )
            ),
            'technical_detailed' => array(
                'name' => __( 'Rapport Technique Détaillé', 'boss-seo' ),
                'description' => __( 'Analyse complète pour les équipes techniques', 'boss-seo' ),
                'pages' => 8,
                'sections' => array( 'overview', 'indexation', 'errors', 'performance', 'sitemaps', 'recommendations', 'appendix' )
            ),
            'monthly_performance' => array(
                'name' => __( 'Performance Mensuelle', 'boss-seo' ),
                'description' => __( 'Évolution des performances sur le mois', 'boss-seo' ),
                'pages' => 5,
                'sections' => array( 'overview', 'trends', 'comparison', 'highlights', 'next_steps' )
            ),
            'audit_compliance' => array(
                'name' => __( 'Audit de Conformité', 'boss-seo' ),
                'description' => __( 'Vérification des bonnes pratiques SEO', 'boss-seo' ),
                'pages' => 6,
                'sections' => array( 'compliance_check', 'best_practices', 'gaps', 'action_plan' )
            )
        );
    }

    /**
     * S'assure que le répertoire des rapports existe.
     *
     * @since    1.2.0
     */
    private function ensure_reports_directory() {
        if ( ! file_exists( $this->reports_dir ) ) {
            wp_mkdir_p( $this->reports_dir );

            // Créer un fichier .htaccess pour la sécurité
            $htaccess_content = "# Boss SEO PDF Reports\n";
            $htaccess_content .= "Options -Indexes\n";
            $htaccess_content .= "<Files *.pdf>\n";
            $htaccess_content .= "    Header set Content-Type \"application/pdf\"\n";
            $htaccess_content .= "    Header set Content-Disposition \"attachment\"\n";
            $htaccess_content .= "</Files>\n";

            file_put_contents( $this->reports_dir . '.htaccess', $htaccess_content );
        }
    }

    /**
     * Génère un rapport PDF.
     *
     * @since    1.2.0
     * @param    string    $template_type    Le type de template.
     * @param    array     $options          Options de génération.
     * @return   array                       Résultat de la génération.
     */
    public function generate_report( $template_type = 'technical_detailed', $options = array() ) {
        if ( ! $this->report_config['enabled'] ) {
            return array(
                'success' => false,
                'message' => __( 'Génération de rapports désactivée', 'boss-seo' )
            );
        }

        try {
            // Vérifier que le template existe
            if ( ! isset( $this->report_templates[$template_type] ) ) {
                return array(
                    'success' => false,
                    'message' => __( 'Template de rapport non trouvé', 'boss-seo' )
                );
            }

            $template = $this->report_templates[$template_type];

            // Collecter les données nécessaires
            $report_data = $this->collect_report_data( $template_type, $options );

            // Générer le contenu HTML
            $html_content = $this->generate_html_content( $template, $report_data, $options );

            // Convertir en PDF (simulation - en réalité utiliserait une librairie comme TCPDF ou DOMPDF)
            $pdf_result = $this->convert_html_to_pdf( $html_content, $template_type, $options );

            if ( $pdf_result['success'] ) {
                // Enregistrer les métadonnées du rapport
                $this->save_report_metadata( $pdf_result['filename'], $template_type, $report_data, $options );

                // Envoyer par email si configuré
                if ( $this->report_config['email_reports'] && ! empty( $this->report_config['email_recipients'] ) ) {
                    $this->email_report( $pdf_result['filepath'], $template_type );
                }

                return array(
                    'success' => true,
                    'message' => __( 'Rapport généré avec succès', 'boss-seo' ),
                    'filename' => $pdf_result['filename'],
                    'filepath' => $pdf_result['filepath'],
                    'download_url' => $pdf_result['download_url'],
                    'size' => $pdf_result['size'],
                    'pages' => $template['pages'],
                    'generated_at' => current_time( 'mysql' )
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $pdf_result['message'] ?? __( 'Erreur lors de la génération PDF', 'boss-seo' )
                );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur génération rapport PDF - ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur lors de la génération du rapport', 'boss-seo' )
            );
        }
    }

    /**
     * Collecte les données nécessaires pour le rapport.
     *
     * @since    1.2.0
     * @param    string    $template_type    Le type de template.
     * @param    array     $options          Options de collecte.
     * @return   array                       Les données collectées.
     */
    private function collect_report_data( $template_type, $options = array() ) {
        $data = array(
            'site_info' => $this->get_site_info(),
            'generation_info' => array(
                'generated_at' => current_time( 'mysql' ),
                'generated_by' => wp_get_current_user()->display_name ?? 'Boss SEO',
                'template_type' => $template_type,
                'version' => $this->version
            )
        );

        // Collecter les données selon le template
        switch ( $template_type ) {
            case 'executive_summary':
                $data = array_merge( $data, $this->collect_executive_data() );
                break;

            case 'technical_detailed':
                $data = array_merge( $data, $this->collect_technical_data() );
                break;

            case 'monthly_performance':
                $data = array_merge( $data, $this->collect_performance_data() );
                break;

            case 'audit_compliance':
                $data = array_merge( $data, $this->collect_audit_data() );
                break;
        }

        return $data;
    }

    /**
     * Récupère les informations du site.
     *
     * @since    1.2.0
     * @return   array    Les informations du site.
     */
    private function get_site_info() {
        return array(
            'name' => get_bloginfo( 'name' ),
            'url' => get_site_url(),
            'description' => get_bloginfo( 'description' ),
            'admin_email' => get_option( 'admin_email' ),
            'wordpress_version' => get_bloginfo( 'version' ),
            'theme' => wp_get_theme()->get( 'Name' ),
            'plugins_count' => count( get_option( 'active_plugins', array() ) )
        );
    }

    /**
     * Collecte les données pour le résumé exécutif.
     *
     * @since    1.2.0
     * @return   array    Les données du résumé exécutif.
     */
    private function collect_executive_data() {
        // Utiliser les classes existantes pour récupérer les données
        $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );

        $dashboard_stats = $dashboard->get_dashboard_stats();
        $indexation_stats = $monitoring->get_indexation_stats();

        return array(
            'key_metrics' => array(
                'total_urls' => $dashboard_stats['totalUrls'] ?? 0,
                'indexed_urls' => $dashboard_stats['indexedUrls'] ?? 0,
                'indexation_rate' => $dashboard_stats['indexationRate'] ?? 0,
                'error_count' => count( $indexation_stats['errors_detail'] ?? array() )
            ),
            'performance_summary' => array(
                'status' => $dashboard_stats['indexationRate'] >= 90 ? 'excellent' :
                           ($dashboard_stats['indexationRate'] >= 75 ? 'good' : 'needs_improvement'),
                'trend' => 'stable', // Simulation
                'last_update' => $dashboard_stats['lastUpdate'] ?? 'N/A'
            ),
            'recommendations' => $this->generate_executive_recommendations( $dashboard_stats, $indexation_stats )
        );
    }

    /**
     * Collecte les données techniques détaillées.
     *
     * @since    1.2.0
     * @return   array    Les données techniques.
     */
    private function collect_technical_data() {
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
        $specialized = new Boss_Specialized_Sitemaps( $this->plugin_name, $this->version );
        $cache = new Boss_Sitemap_Cache( $this->plugin_name, $this->version );

        $indexation_stats = $monitoring->get_indexation_stats();
        $specialized_stats = $specialized->get_specialized_stats();
        $cache_stats = $cache->get_cache_stats();

        return array(
            'indexation_details' => $indexation_stats,
            'specialized_sitemaps' => $specialized_stats,
            'cache_performance' => $cache_stats,
            'errors_analysis' => $this->analyze_errors( $indexation_stats['errors_detail'] ?? array() ),
            'performance_metrics' => $indexation_stats['performance'] ?? array(),
            'technical_recommendations' => $this->generate_technical_recommendations( $indexation_stats )
        );
    }

    /**
     * Collecte les données de performance mensuelle.
     *
     * @since    1.2.0
     * @return   array    Les données de performance.
     */
    private function collect_performance_data() {
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
        $indexation_stats = $monitoring->get_indexation_stats();

        return array(
            'monthly_trends' => $indexation_stats['evolution'] ?? array(),
            'content_type_performance' => $indexation_stats['by_content_type'] ?? array(),
            'benchmark_comparison' => $indexation_stats['benchmark'] ?? array(),
            'highlights' => $this->generate_monthly_highlights( $indexation_stats ),
            'growth_metrics' => $this->calculate_growth_metrics( $indexation_stats )
        );
    }

    /**
     * Collecte les données d'audit de conformité.
     *
     * @since    1.2.0
     * @return   array    Les données d'audit.
     */
    private function collect_audit_data() {
        $monitoring = new Boss_Sitemap_Monitoring( $this->plugin_name, $this->version );
        $errors = $monitoring->detect_sitemap_errors();

        return array(
            'compliance_score' => $this->calculate_compliance_score( $errors ),
            'best_practices_check' => $this->check_best_practices(),
            'security_audit' => $this->audit_security_settings(),
            'performance_audit' => $this->audit_performance_settings(),
            'gaps_analysis' => $this->analyze_compliance_gaps( $errors ),
            'action_plan' => $this->generate_action_plan( $errors )
        );
    }

    /**
     * Génère le contenu HTML du rapport.
     *
     * @since    1.2.0
     * @param    array     $template      Le template.
     * @param    array     $data          Les données.
     * @param    array     $options       Les options.
     * @return   string                   Le contenu HTML.
     */
    private function generate_html_content( $template, $data, $options = array() ) {
        $html = $this->get_html_header( $template, $data );

        foreach ( $template['sections'] as $section ) {
            $html .= $this->generate_section_html( $section, $data, $options );
        }

        $html .= $this->get_html_footer( $data );

        return $html;
    }

    /**
     * Génère l'en-tête HTML.
     *
     * @since    1.2.0
     * @param    array     $template    Le template.
     * @param    array     $data        Les données.
     * @return   string                 L'en-tête HTML.
     */
    private function get_html_header( $template, $data ) {
        $logo_url = $this->report_config['custom_logo'] ?: plugin_dir_url( dirname( __FILE__ ) ) . 'assets/logo-boss-seo.png';

        $html = '<!DOCTYPE html>';
        $html .= '<html lang="' . $this->report_config['report_language'] . '">';
        $html .= '<head>';
        $html .= '<meta charset="UTF-8">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        $html .= '<title>' . $template['name'] . ' - ' . $data['site_info']['name'] . '</title>';
        $html .= $this->get_pdf_styles();
        $html .= '</head>';
        $html .= '<body>';

        // En-tête du rapport
        $html .= '<header class="report-header">';
        if ( $this->report_config['branding'] ) {
            $html .= '<img src="' . $logo_url . '" alt="Boss SEO" class="logo">';
        }
        $html .= '<h1>' . $template['name'] . '</h1>';
        $html .= '<div class="site-info">';
        $html .= '<h2>' . $data['site_info']['name'] . '</h2>';
        $html .= '<p>' . $data['site_info']['url'] . '</p>';
        $html .= '<p>Généré le ' . date( 'd/m/Y à H:i', strtotime( $data['generation_info']['generated_at'] ) ) . '</p>';
        $html .= '</div>';
        $html .= '</header>';

        return $html;
    }
}