{"version": 3, "names": ["_react", "require", "_utils", "_EMOTION_STRINGIFIED_CSS_ERROR__", "_ref", "process", "env", "NODE_ENV", "name", "styles", "map", "toString", "rootBase", "exports", "rootSize", "size", "css", "Root", "_base", "default", "target", "label", "Row", "pointActive", "isActive", "boxShadow", "COLORS", "gray", "pointColor", "pointColorHover", "theme", "accent", "pointBase", "props", "reduceMotion", "Point", "Cell"], "sources": ["@wordpress/components/src/alignment-matrix-control/styles/alignment-matrix-control-styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { COLORS, reduceMotion } from '../../utils';\nimport type {\n\tAlignmentMatrixControlProps,\n\tAlignmentMatrixControlCellProps,\n} from '../types';\n\nexport const rootBase = () => {\n\treturn css`\n\t\tborder-radius: 2px;\n\t\tbox-sizing: border-box;\n\t\tdirection: ltr;\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat( 3, 1fr );\n\t\toutline: none;\n\t`;\n};\n\nconst rootSize = ( { size = 92 } ) => {\n\treturn css`\n\t\tgrid-template-rows: repeat( 3, calc( ${ size }px / 3 ) );\n\t\twidth: ${ size }px;\n\t`;\n};\n\nexport const Root = styled.div< {\n\tsize: AlignmentMatrixControlProps[ 'width' ];\n} >`\n\t${ rootBase };\n\n\tborder: 1px solid transparent;\n\tcursor: pointer;\n\tgrid-template-columns: auto;\n\n\t${ rootSize };\n`;\n\nexport const Row = styled.div`\n\tbox-sizing: border-box;\n\tdisplay: grid;\n\tgrid-template-columns: repeat( 3, 1fr );\n`;\n\nconst pointActive = ( {\n\tisActive,\n}: Pick< AlignmentMatrixControlCellProps, 'isActive' > ) => {\n\tconst boxShadow = isActive ? `0 0 0 2px ${ COLORS.gray[ 900 ] }` : null;\n\tconst pointColor = isActive ? COLORS.gray[ 900 ] : COLORS.gray[ 400 ];\n\tconst pointColorHover = isActive ? COLORS.gray[ 900 ] : COLORS.theme.accent;\n\n\treturn css`\n\t\tbox-shadow: ${ boxShadow };\n\t\tcolor: ${ pointColor };\n\n\t\t*:hover > & {\n\t\t\tcolor: ${ pointColorHover };\n\t\t}\n\t`;\n};\n\nexport const pointBase = (\n\tprops: Pick< AlignmentMatrixControlCellProps, 'isActive' >\n) => {\n\treturn css`\n\t\tbackground: currentColor;\n\t\tbox-sizing: border-box;\n\t\tdisplay: grid;\n\t\tmargin: auto;\n\t\ttransition: all 120ms linear;\n\n\t\t${ reduceMotion( 'transition' ) }\n\t\t${ pointActive( props ) }\n\t`;\n};\n\nexport const Point = styled.span`\n\theight: 6px;\n\twidth: 6px;\n\t${ pointBase }\n`;\n\nexport const Cell = styled.span`\n\tappearance: none;\n\tborder: none;\n\tbox-sizing: border-box;\n\tmargin: 0;\n\tdisplay: flex;\n\tposition: relative;\n\toutline: none;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 0;\n`;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AAAmD,SAAAE,iCAAA;AAAA,IAAAC,IAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA;AAM5C,MAAMS,QAAQ,GAAGA,CAAA,KAAM;EAC7B,OAAAR,IAAA;AAQD,CAAC;AAACS,OAAA,CAAAD,QAAA,GAAAA,QAAA;AAEF,MAAME,QAAQ,GAAGA,CAAE;EAAEC,IAAI,GAAG;AAAG,CAAC,KAAM;EACrC,wBAAOC,UAAG,0CAC+BD,IAAI,uBAClCA,IAAI,WAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA,8CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAEhB,CAAC;AAEM,MAAMU,IAAI,OAAAC,KAAA,CAAAC,OAAA,SAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAa,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAGbT,QAAQ,8EAMRE,QAAQ,SAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA,gzGACX;AAACM,OAAA,CAAAI,IAAA,GAAAA,IAAA;AAEK,MAAMK,GAAG,OAAAJ,KAAA,CAAAC,OAAA,SAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAa,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,EAIf;AAACU,OAAA,CAAAS,GAAA,GAAAA,GAAA;AAEF,MAAMC,WAAW,GAAGA,CAAE;EACrBC;AACoD,CAAC,KAAM;EAC3D,MAAMC,SAAS,GAAGD,QAAQ,GAAI,aAAaE,aAAM,CAACC,IAAI,CAAE,GAAG,CAAI,EAAC,GAAG,IAAI;EACvE,MAAMC,UAAU,GAAGJ,QAAQ,GAAGE,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,GAAGD,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE;EACrE,MAAME,eAAe,GAAGL,QAAQ,GAAGE,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,GAAGD,aAAM,CAACI,KAAK,CAACC,MAAM;EAE3E,wBAAOf,UAAG,iBACMS,SAAS,aACdG,UAAU,uBAGTC,eAAe,UAAAxB,OAAA,CAAAC,GAAA,CAAAC,QAAA,iDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAG5B,CAAC;AAEM,MAAMyB,SAAS,GACrBC,KAA0D,IACtD;EACJ,wBAAOjB,UAAG,yGAON,IAAAkB,mBAAY,EAAE,YAAa,CAAC,OAC5BX,WAAW,CAAEU,KAAM,CAAC,SAAA5B,OAAA,CAAAC,GAAA,CAAAC,QAAA,+CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAEzB,CAAC;AAACM,OAAA,CAAAmB,SAAA,GAAAA,SAAA;AAEK,MAAMG,KAAK,OAAAjB,KAAA,CAAAC,OAAA,UAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAa,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,4BAGdW,SAAS,SAAA3B,OAAA,CAAAC,GAAA,CAAAC,QAAA,ozGACZ;AAACM,OAAA,CAAAsB,KAAA,GAAAA,KAAA;AAEK,MAAMC,IAAI,OAAAlB,KAAA,CAAAC,OAAA,UAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAa,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,EAWhB;AAACU,OAAA,CAAAuB,IAAA,GAAAA,IAAA"}