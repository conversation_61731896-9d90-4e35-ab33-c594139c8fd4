{"version": 3, "names": ["_element", "require", "_deprecated", "_interopRequireDefault", "stopPropagation", "event", "IsolatedEventContainer", "forwardRef", "props", "ref", "deprecated", "since", "_react", "createElement", "onMouseDown", "_default", "exports", "default"], "sources": ["@wordpress/components/src/isolated-event-container/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ComponentPropsWithoutRef, MouseEvent } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef } from '@wordpress/element';\nimport deprecated from '@wordpress/deprecated';\n\nfunction stopPropagation( event: MouseEvent ) {\n\tevent.stopPropagation();\n}\n\ntype DivProps = ComponentPropsWithoutRef< 'div' >;\n\nconst IsolatedEventContainer = forwardRef< HTMLDivElement, DivProps >(\n\t( props, ref ) => {\n\t\tdeprecated( 'wp.components.IsolatedEventContainer', {\n\t\t\tsince: '5.7',\n\t\t} );\n\n\t\t// Disable reason: this stops certain events from propagating outside of the component.\n\t\t// - onMouseDown is disabled as this can cause interactions with other DOM elements.\n\t\t/* eslint-disable jsx-a11y/no-static-element-interactions */\n\t\treturn <div { ...props } ref={ ref } onMouseDown={ stopPropagation } />;\n\t\t/* eslint-enable jsx-a11y/no-static-element-interactions */\n\t}\n);\n\nexport default IsolatedEventContainer;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AAIA,SAASG,eAAeA,CAAEC,KAAiB,EAAG;EAC7CA,KAAK,CAACD,eAAe,CAAC,CAAC;AACxB;AAIA,MAAME,sBAAsB,GAAG,IAAAC,mBAAU,EACxC,CAAEC,KAAK,EAAEC,GAAG,KAAM;EACjB,IAAAC,mBAAU,EAAE,sCAAsC,EAAE;IACnDC,KAAK,EAAE;EACR,CAAE,CAAC;;EAEH;EACA;EACA;EACA,OAAO,IAAAC,MAAA,CAAAC,aAAA;IAAA,GAAUL,KAAK;IAAGC,GAAG,EAAGA,GAAK;IAACK,WAAW,EAAGV;EAAiB,CAAE,CAAC;EACvE;AACD,CACD,CAAC;AAAC,IAAAW,QAAA,GAEaT,sBAAsB;AAAAU,OAAA,CAAAC,OAAA,GAAAF,QAAA"}