"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useGrid;
var _react = require("@emotion/react");
var _element = require("@wordpress/element");
var _context = require("../context");
var _utils = require("./utils");
var _useResponsiveValue = require("../utils/use-responsive-value");
var _configValues = _interopRequireDefault(require("../utils/config-values"));
var _useCx = require("../utils/hooks/use-cx");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function useGrid(props) {
  const {
    align,
    alignment,
    className,
    columnGap,
    columns = 2,
    gap = 3,
    isInline = false,
    justify,
    rowGap,
    rows,
    templateColumns,
    templateRows,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'Grid');
  const columnsAsArray = Array.isArray(columns) ? columns : [columns];
  const column = (0, _useResponsiveValue.useResponsiveValue)(columnsAsArray);
  const rowsAsArray = Array.isArray(rows) ? rows : [rows];
  const row = (0, _useResponsiveValue.useResponsiveValue)(rowsAsArray);
  const gridTemplateColumns = templateColumns || !!columns && `repeat( ${column}, 1fr )`;
  const gridTemplateRows = templateRows || !!rows && `repeat( ${row}, 1fr )`;
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => {
    const alignmentProps = (0, _utils.getAlignmentProps)(alignment);
    const gridClasses = /*#__PURE__*/(0, _react.css)({
      alignItems: align,
      display: isInline ? 'inline-grid' : 'grid',
      gap: `calc( ${_configValues.default.gridBase} * ${gap} )`,
      gridTemplateColumns: gridTemplateColumns || undefined,
      gridTemplateRows: gridTemplateRows || undefined,
      gridRowGap: rowGap,
      gridColumnGap: columnGap,
      justifyContent: justify,
      verticalAlign: isInline ? 'middle' : undefined,
      ...alignmentProps
    }, process.env.NODE_ENV === "production" ? "" : ";label:gridClasses;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
    return cx(gridClasses, className);
  }, [align, alignment, className, columnGap, cx, gap, gridTemplateColumns, gridTemplateRows, isInline, justify, rowGap]);
  return {
    ...otherProps,
    className: classes
  };
}
//# sourceMappingURL=hook.js.map