/**
 * Styles pour la meta box Boss SEO
 */

.boss-seo-metabox {
    padding: 15px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.boss-seo-metabox-section {
    margin-bottom: 24px;
    position: relative;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20px;
}

.boss-seo-metabox-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.boss-seo-metabox-section h4 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 15px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
}

.boss-seo-metabox-section h4 .dashicons {
    margin-right: 8px;
    color: #0073aa;
}

.boss-seo-counter {
    position: absolute;
    right: 5px;
    bottom: 25px;
    font-size: 12px;
    color: #666;
    background: rgba(255, 255, 255, 0.9);
    padding: 3px 8px;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.boss-seo-counter-current.too-short,
.boss-seo-counter-current.too-long {
    color: #dc3232;
    font-weight: 600;
}

.boss-seo-counter-current.good {
    color: #46b450;
    font-weight: 600;
}

.boss-seo-score-container {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.boss-seo-score-indicator {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-weight: bold;
    font-size: 20px;
    color: white;
}

.boss-seo-score-good {
    background-color: #46b450;
}

.boss-seo-score-ok {
    background-color: #ffb900;
}

.boss-seo-score-poor {
    background-color: #f56e28;
}

.boss-seo-score-bad {
    background-color: #dc3232;
}

.boss-seo-score-details {
    flex: 1;
}

.boss-seo-score-details p {
    margin: 0 0 10px 0;
}

.boss-seo-recommendations {
    margin-top: 15px;
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
    color: #495057;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.boss-seo-recommendation {
    padding: 12px 15px;
    margin-bottom: 8px;
    border-radius: 6px;
    display: flex;
    align-items: flex-start;
    background: #ffffff;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.boss-seo-recommendation:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.boss-seo-recommendation:last-child {
    margin-bottom: 0;
}

.boss-seo-recommendation-critical {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-left: 4px solid #e53e3e;
    color: #742a2a;
}

.boss-seo-recommendation-warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-left: 4px solid #f59e0b;
    color: #92400e;
}

.boss-seo-recommendation-info {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-left: 4px solid #3b82f6;
    color: #1e40af;
}

.boss-seo-recommendation-success {
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    border-left: 4px solid #10b981;
    color: #065f46;
}

.boss-seo-recommendation-icon {
    margin-right: 8px;
    font-size: 18px;
}

.boss-seo-recommendation-critical .boss-seo-recommendation-icon {
    color: #dc3232;
}

.boss-seo-recommendation-warning .boss-seo-recommendation-icon {
    color: #ffb900;
}

.boss-seo-recommendation-info .boss-seo-recommendation-icon {
    color: #72aee6;
}

.boss-seo-recommendation-success .boss-seo-recommendation-icon {
    color: #46b450;
}

.boss-seo-recommendation-text {
    flex: 1;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
}

/* Styles pour le champ de mot-clé principal */
.boss-seo-keywords-container {
    margin-bottom: 15px;
}

.boss-seo-keywords-input-container {
    position: relative;
    margin-bottom: 10px;
}

.boss-seo-keywords-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.boss-seo-keywords-input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.boss-seo-keywords-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    min-height: 30px;
}

.boss-seo-keyword-tag {
    display: inline-flex;
    align-items: center;
    background-color: #f0f6fc;
    border: 1px solid #c5d9ed;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 13px;
    color: #2271b1;
    transition: all 0.2s ease;
}

.boss-seo-keyword-tag.primary {
    background-color: #e6f6e8;
    border-color: #c3e6c8;
    color: #2a7d3f;
    font-weight: 600;
}

.boss-seo-keyword-tag .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-left: 5px;
    cursor: pointer;
    color: #72aee6;
}

.boss-seo-keyword-tag.primary .dashicons {
    color: #46b450;
}

.boss-seo-keyword-tag .dashicons:hover {
    color: #dc3232;
}

.boss-seo-keywords-suggestions {
    margin-top: 15px;
}

.boss-seo-keywords-suggestions-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #23282d;
}

.boss-seo-keywords-suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.boss-seo-keyword-suggestion {
    display: inline-flex;
    align-items: center;
    background-color: #f6f7f7;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 3px 8px;
    font-size: 12px;
    color: #50575e;
    cursor: pointer;
    transition: all 0.2s ease;
}

.boss-seo-keyword-suggestion:hover {
    background-color: #f0f6fc;
    border-color: #c5d9ed;
    color: #2271b1;
}

.boss-seo-keyword-suggestion .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 3px;
}

/* Styles pour les métadonnées supplémentaires */
.boss-seo-metadata-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.boss-seo-metadata-field {
    margin-bottom: 10px;
}

.boss-seo-metadata-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 13px;
}

.boss-seo-metadata-field input,
.boss-seo-metadata-field select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.boss-seo-metadata-field input:focus,
.boss-seo-metadata-field select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.boss-seo-metadata-field .description {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* Styles pour les boutons */
#boss_seo_optimize_button {
    margin-right: 10px;
    background-color: #0073aa;
    border-color: #0073aa;
}

#boss_seo_optimize_button:hover {
    background-color: #006291;
    border-color: #006291;
}

#boss_seo_analyze_button {
    background-color: #f7f7f7;
    border-color: #ccc;
}

#boss_seo_analyze_button:hover {
    background-color: #f0f0f0;
    border-color: #999;
}

/* Styles pour les états de chargement */
.boss-seo-loading {
    opacity: 0.6;
    pointer-events: none;
}

.boss-seo-loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-left-color: #0073aa;
    border-radius: 50%;
    vertical-align: middle;
    animation: boss-seo-spin 1s linear infinite;
}

@keyframes boss-seo-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
