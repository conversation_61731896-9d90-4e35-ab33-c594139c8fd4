{"version": 3, "names": [], "sources": ["@wordpress/components/src/form-toggle/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ChangeEvent } from 'react';\n\nexport type FormToggleProps = {\n\t/**\n\t * If checked is true the toggle will be checked. If checked is false the\n\t * toggle will be unchecked. If no value is passed the toggle will be\n\t * unchecked.\n\t */\n\tchecked?: boolean;\n\t/**\n\t * If disabled is true the toggle will be disabled and apply the appropriate\n\t * styles.\n\t */\n\tdisabled?: boolean;\n\t/**\n\t * A callback function invoked when the toggle is clicked.\n\t */\n\tonChange: ( event: ChangeEvent< HTMLInputElement > ) => void;\n};\n"], "mappings": ""}