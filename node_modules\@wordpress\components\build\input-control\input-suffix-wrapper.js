"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.InputControlSuffixWrapper = void 0;
var _react = require("react");
var _spacer = require("../spacer");
var _context = require("../context");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedInputControlSuffixWrapper(props, forwardedRef) {
  const derivedProps = (0, _context.useContextSystem)(props, 'InputControlSuffixWrapper');
  return (0, _react.createElement)(_spacer.Spacer, {
    marginBottom: 0,
    ...derivedProps,
    ref: forwardedRef
  });
}

/**
 * A convenience wrapper for the `suffix` when you want to apply
 * standard padding in accordance with the size variant.
 *
 * ```jsx
 * import {
 *   __experimentalInputControl as InputControl,
 *   __experimentalInputControlSuffixWrapper as InputControlSuffixWrapper,
 * } from '@wordpress/components';
 *
 * <InputControl
 *   suffix={<InputControlSuffixWrapper>%</InputControlSuffixWrapper>}
 * />
 * ```
 */
const InputControlSuffixWrapper = (0, _context.contextConnect)(UnconnectedInputControlSuffixWrapper, 'InputControlSuffixWrapper');
exports.InputControlSuffixWrapper = InputControlSuffixWrapper;
var _default = InputControlSuffixWrapper;
exports.default = _default;
//# sourceMappingURL=input-suffix-wrapper.js.map