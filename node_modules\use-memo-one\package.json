{"name": "use-memo-one", "version": "1.1.3", "description": "useMemo and useCallback but with a stable cache", "keywords": ["memoization", "react", "useMemo", "useCallback", "memoize-one"], "main": "dist/use-memo-one.cjs.js", "module": "dist/use-memo-one.esm.js", "sideEffects": false, "files": ["/dist", "/src", "index.d.ts"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/alexreardon/use-memo-one.git"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/preset-env": "^7.5.2", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "babel-core": "^6.26.3", "babel-eslint": "^10.0.2", "babel-jest": "^24.8.0", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.14.0", "eslint": "^6.0.1", "eslint-config-airbnb": "^17.1.1", "eslint-config-prettier": "^6.0.0", "eslint-plugin-flowtype": "^3.11.1", "eslint-plugin-import": "^2.18.0", "eslint-plugin-jest": "^22.7.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-react": "^7.14.2", "eslint-plugin-react-hooks": "^1.6.1", "flow-bin": "^0.102.0", "jest": "^24.8.0", "jest-watch-typeahead": "^0.3.1", "prettier": "^1.18.2", "react": "^16.8.6", "react-dom": "^16.8.6", "rimraf": "^2.6.3", "rollup": "^1.16.7", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.2"}, "config": {"prettier_target": "*.{js,jsx,md,json} src/**/*.{js,jsx,md,json} test/**/*.{js,jsx,md,json}"}, "scripts": {"validate": "yarn run lint && yarn run typecheck", "test": "jest", "typecheck": "flow check", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint src test", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn run build:clean && yarn run build:dist && yarn run build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:flow": "echo \"// @flow\n\nexport * from '../src';\" > dist/use-memo-one.cjs.js.flow", "prepublish": "yarn run build"}}