"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = LockIcon;
var _react = require("react");
var _icons = require("@wordpress/icons");
var _compose = require("@wordpress/compose");
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function LockIcon() {
  const iconStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.icon, _styles.default['icon--dark']);
  return (0, _react.createElement)(_icons.Icon, {
    icon: _icons.lock,
    color: iconStyle.color,
    style: iconStyle
  });
}
//# sourceMappingURL=index.native.js.map