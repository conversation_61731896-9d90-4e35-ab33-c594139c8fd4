"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _I3Y4EXEFcjs = require('./I3Y4EXEF.cjs');





var _F6HPKLO2cjs = require('./F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('./KBNYGXWI.cjs');




var _AV6KTKLEcjs = require('./AV6KTKLE.cjs');

// src/popover/popover-store.ts
function createPopoverStore(_a = {}) {
  var _b = _a, {
    popover: otherPopover
  } = _b, props = _AV6KTKLEcjs.__objRest.call(void 0, _b, [
    "popover"
  ]);
  const store = _F6HPKLO2cjs.mergeStore.call(void 0, 
    props.store,
    _F6HPKLO2cjs.omit.call(void 0, otherPopover, [
      "arrowElement",
      "anchorElement",
      "contentElement",
      "popoverElement",
      "disclosureElement"
    ])
  );
  _F6HPKLO2cjs.throwOnConflictingProps.call(void 0, props, store);
  const syncState = store == null ? void 0 : store.getState();
  const dialog = _I3Y4EXEFcjs.createDialogStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), { store }));
  const placement = _KBNYGXWIcjs.defaultValue.call(void 0, 
    props.placement,
    syncState == null ? void 0 : syncState.placement,
    "bottom"
  );
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, dialog.getState()), {
    placement,
    currentPlacement: placement,
    anchorElement: _KBNYGXWIcjs.defaultValue.call(void 0, syncState == null ? void 0 : syncState.anchorElement, null),
    popoverElement: _KBNYGXWIcjs.defaultValue.call(void 0, syncState == null ? void 0 : syncState.popoverElement, null),
    arrowElement: _KBNYGXWIcjs.defaultValue.call(void 0, syncState == null ? void 0 : syncState.arrowElement, null),
    rendered: Symbol("rendered")
  });
  const popover = _F6HPKLO2cjs.createStore.call(void 0, initialState, dialog, store);
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, dialog), popover), {
    setAnchorElement: (element) => popover.setState("anchorElement", element),
    setPopoverElement: (element) => popover.setState("popoverElement", element),
    setArrowElement: (element) => popover.setState("arrowElement", element),
    render: () => popover.setState("rendered", Symbol("rendered"))
  });
}



exports.createPopoverStore = createPopoverStore;
