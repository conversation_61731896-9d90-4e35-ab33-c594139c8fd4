"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _inputControlStyles = require("./styles/input-control-styles");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function Backdrop({
  disabled = false,
  isFocused = false
}) {
  return (0, _react.createElement)(_inputControlStyles.BackdropUI, {
    "aria-hidden": "true",
    className: "components-input-control__backdrop",
    disabled: disabled,
    isFocused: isFocused
  });
}
const MemoizedBackdrop = (0, _element.memo)(Backdrop);
var _default = MemoizedBackdrop;
exports.default = _default;
//# sourceMappingURL=backdrop.js.map