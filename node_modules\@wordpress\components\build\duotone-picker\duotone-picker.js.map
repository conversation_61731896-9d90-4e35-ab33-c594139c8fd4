{"version": 3, "names": ["_es", "_interopRequireDefault", "require", "_element", "_i18n", "_colorListPicker", "_circularOptionPicker", "_vStack", "_customDuotoneBar", "_utils", "_spacer", "DuotonePicker", "asButtons", "loop", "clearable", "unsetable", "colorPalette", "duotonePalette", "disableCustomColors", "disableCustomDuotone", "value", "onChange", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otherProps", "defaultDark", "defaultLight", "useMemo", "getDefaultColors", "isUnset", "unsetOptionLabel", "__", "unsetOption", "_react", "createElement", "default", "Option", "key", "isSelected", "tooltipText", "className", "onClick", "undefined", "duotoneOptions", "map", "colors", "slug", "name", "style", "background", "getGradientFromCSSColors", "color", "sprintf", "label", "fastDeepEqual", "metaProps", "_metaProps", "options", "actions", "ButtonAction", "Spacer", "paddingTop", "length", "VStack", "spacing", "labels", "enableAlpha", "newColors", "newValue", "_default", "exports"], "sources": ["@wordpress/components/src/duotone-picker/duotone-picker.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport fastDeepEqual from 'fast-deep-equal/es6';\n\n/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\nimport { __, sprintf } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport ColorListPicker from './color-list-picker';\nimport CircularOptionPicker from '../circular-option-picker';\nimport { VStack } from '../v-stack';\n\nimport CustomDuotoneBar from './custom-duotone-bar';\nimport { getDefaultColors, getGradientFromCSSColors } from './utils';\nimport { Spacer } from '../spacer';\nimport type { DuotonePickerProps } from './types';\n\n/**\n * ```jsx\n * import { DuotonePicker, DuotoneSwatch } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const DUOTONE_PALETTE = [\n * \t{ colors: [ '#8c00b7', '#fcff41' ], name: 'Purple and yellow', slug: 'purple-yellow' },\n * \t{ colors: [ '#000097', '#ff4747' ], name: 'Blue and red', slug: 'blue-red' },\n * ];\n *\n * const COLOR_PALETTE = [\n * \t{ color: '#ff4747', name: 'Red', slug: 'red' },\n * \t{ color: '#fcff41', name: 'Yellow', slug: 'yellow' },\n * \t{ color: '#000097', name: 'Blue', slug: 'blue' },\n * \t{ color: '#8c00b7', name: 'Purple', slug: 'purple' },\n * ];\n *\n * const Example = () => {\n * \tconst [ duotone, setDuotone ] = useState( [ '#000000', '#ffffff' ] );\n * \treturn (\n * \t\t<>\n * \t\t\t<DuotonePicker\n * \t\t\t\tduotonePalette={ DUOTONE_PALETTE }\n * \t\t\t\tcolorPalette={ COLOR_PALETTE }\n * \t\t\t\tvalue={ duotone }\n * \t\t\t\tonChange={ setDuotone }\n * \t\t\t/>\n * \t\t\t<DuotoneSwatch values={ duotone } />\n * \t\t</>\n * \t);\n * };\n * ```\n */\nfunction DuotonePicker( {\n\tasButtons,\n\tloop,\n\tclearable = true,\n\tunsetable = true,\n\tcolorPalette,\n\tduotonePalette,\n\tdisableCustomColors,\n\tdisableCustomDuotone,\n\tvalue,\n\tonChange,\n\t'aria-label': ariaLabel,\n\t'aria-labelledby': ariaLabelledby,\n\t...otherProps\n}: DuotonePickerProps ) {\n\tconst [ defaultDark, defaultLight ] = useMemo(\n\t\t() => getDefaultColors( colorPalette ),\n\t\t[ colorPalette ]\n\t);\n\n\tconst isUnset = value === 'unset';\n\tconst unsetOptionLabel = __( 'Unset' );\n\n\tconst unsetOption = (\n\t\t<CircularOptionPicker.Option\n\t\t\tkey=\"unset\"\n\t\t\tvalue=\"unset\"\n\t\t\tisSelected={ isUnset }\n\t\t\ttooltipText={ unsetOptionLabel }\n\t\t\taria-label={ unsetOptionLabel }\n\t\t\tclassName=\"components-duotone-picker__color-indicator\"\n\t\t\tonClick={ () => {\n\t\t\t\tonChange( isUnset ? undefined : 'unset' );\n\t\t\t} }\n\t\t/>\n\t);\n\n\tconst duotoneOptions = duotonePalette.map( ( { colors, slug, name } ) => {\n\t\tconst style = {\n\t\t\tbackground: getGradientFromCSSColors( colors, '135deg' ),\n\t\t\tcolor: 'transparent',\n\t\t};\n\t\tconst tooltipText =\n\t\t\tname ??\n\t\t\tsprintf(\n\t\t\t\t// translators: %s: duotone code e.g: \"dark-grayscale\" or \"7f7f7f-ffffff\".\n\t\t\t\t__( 'Duotone code: %s' ),\n\t\t\t\tslug\n\t\t\t);\n\t\tconst label = name\n\t\t\t? sprintf(\n\t\t\t\t\t// translators: %s: The name of the option e.g: \"Dark grayscale\".\n\t\t\t\t\t__( 'Duotone: %s' ),\n\t\t\t\t\tname\n\t\t\t  )\n\t\t\t: tooltipText;\n\t\tconst isSelected = fastDeepEqual( colors, value );\n\n\t\treturn (\n\t\t\t<CircularOptionPicker.Option\n\t\t\t\tkey={ slug }\n\t\t\t\tvalue={ colors }\n\t\t\t\tisSelected={ isSelected }\n\t\t\t\taria-label={ label }\n\t\t\t\ttooltipText={ tooltipText }\n\t\t\t\tstyle={ style }\n\t\t\t\tonClick={ () => {\n\t\t\t\t\tonChange( isSelected ? undefined : colors );\n\t\t\t\t} }\n\t\t\t/>\n\t\t);\n\t} );\n\n\tlet metaProps:\n\t\t| { asButtons: false; loop?: boolean; 'aria-label': string }\n\t\t| { asButtons: false; loop?: boolean; 'aria-labelledby': string }\n\t\t| { asButtons: true };\n\n\tif ( asButtons ) {\n\t\tmetaProps = { asButtons: true };\n\t} else {\n\t\tconst _metaProps: { asButtons: false; loop?: boolean } = {\n\t\t\tasButtons: false,\n\t\t\tloop,\n\t\t};\n\n\t\tif ( ariaLabel ) {\n\t\t\tmetaProps = { ..._metaProps, 'aria-label': ariaLabel };\n\t\t} else if ( ariaLabelledby ) {\n\t\t\tmetaProps = {\n\t\t\t\t..._metaProps,\n\t\t\t\t'aria-labelledby': ariaLabelledby,\n\t\t\t};\n\t\t} else {\n\t\t\tmetaProps = {\n\t\t\t\t..._metaProps,\n\t\t\t\t'aria-label': __( 'Custom color picker.' ),\n\t\t\t};\n\t\t}\n\t}\n\n\tconst options = unsetable\n\t\t? [ unsetOption, ...duotoneOptions ]\n\t\t: duotoneOptions;\n\n\treturn (\n\t\t<CircularOptionPicker\n\t\t\t{ ...otherProps }\n\t\t\t{ ...metaProps }\n\t\t\toptions={ options }\n\t\t\tactions={\n\t\t\t\t!! clearable && (\n\t\t\t\t\t<CircularOptionPicker.ButtonAction\n\t\t\t\t\t\tonClick={ () => onChange( undefined ) }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ __( 'Clear' ) }\n\t\t\t\t\t</CircularOptionPicker.ButtonAction>\n\t\t\t\t)\n\t\t\t}\n\t\t>\n\t\t\t<Spacer paddingTop={ options.length === 0 ? 0 : 4 }>\n\t\t\t\t<VStack spacing={ 3 }>\n\t\t\t\t\t{ ! disableCustomColors && ! disableCustomDuotone && (\n\t\t\t\t\t\t<CustomDuotoneBar\n\t\t\t\t\t\t\tvalue={ isUnset ? undefined : value }\n\t\t\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t\t{ ! disableCustomDuotone && (\n\t\t\t\t\t\t<ColorListPicker\n\t\t\t\t\t\t\tlabels={ [ __( 'Shadows' ), __( 'Highlights' ) ] }\n\t\t\t\t\t\t\tcolors={ colorPalette }\n\t\t\t\t\t\t\tvalue={ isUnset ? undefined : value }\n\t\t\t\t\t\t\tdisableCustomColors={ disableCustomColors }\n\t\t\t\t\t\t\tenableAlpha\n\t\t\t\t\t\t\tonChange={ ( newColors ) => {\n\t\t\t\t\t\t\t\tif ( ! newColors[ 0 ] ) {\n\t\t\t\t\t\t\t\t\tnewColors[ 0 ] = defaultDark;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif ( ! newColors[ 1 ] ) {\n\t\t\t\t\t\t\t\t\tnewColors[ 1 ] = defaultLight;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconst newValue =\n\t\t\t\t\t\t\t\t\tnewColors.length >= 2\n\t\t\t\t\t\t\t\t\t\t? newColors\n\t\t\t\t\t\t\t\t\t\t: undefined;\n\t\t\t\t\t\t\t\t// @ts-expect-error TODO: The color arrays for a DuotonePicker should be a tuple of two colors,\n\t\t\t\t\t\t\t\t// but it's currently typed as a string[].\n\t\t\t\t\t\t\t\t// See also https://github.com/WordPress/gutenberg/pull/49060#discussion_r1136951035\n\t\t\t\t\t\t\t\tonChange( newValue );\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t</VStack>\n\t\t\t</Spacer>\n\t\t</CircularOptionPicker>\n\t);\n}\n\nexport default DuotonePicker;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,GAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAKA,IAAAG,gBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,qBAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AAEA,IAAAM,iBAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AApBA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,aAAaA,CAAE;EACvBC,SAAS;EACTC,IAAI;EACJC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,IAAI;EAChBC,YAAY;EACZC,cAAc;EACdC,mBAAmB;EACnBC,oBAAoB;EACpBC,KAAK;EACLC,QAAQ;EACR,YAAY,EAAEC,SAAS;EACvB,iBAAiB,EAAEC,cAAc;EACjC,GAAGC;AACgB,CAAC,EAAG;EACvB,MAAM,CAAEC,WAAW,EAAEC,YAAY,CAAE,GAAG,IAAAC,gBAAO,EAC5C,MAAM,IAAAC,uBAAgB,EAAEZ,YAAa,CAAC,EACtC,CAAEA,YAAY,CACf,CAAC;EAED,MAAMa,OAAO,GAAGT,KAAK,KAAK,OAAO;EACjC,MAAMU,gBAAgB,GAAG,IAAAC,QAAE,EAAE,OAAQ,CAAC;EAEtC,MAAMC,WAAW,GAChB,IAAAC,MAAA,CAAAC,aAAA,EAAC5B,qBAAA,CAAA6B,OAAoB,CAACC,MAAM;IAC3BC,GAAG,EAAC,OAAO;IACXjB,KAAK,EAAC,OAAO;IACbkB,UAAU,EAAGT,OAAS;IACtBU,WAAW,EAAGT,gBAAkB;IAChC,cAAaA,gBAAkB;IAC/BU,SAAS,EAAC,4CAA4C;IACtDC,OAAO,EAAGA,CAAA,KAAM;MACfpB,QAAQ,CAAEQ,OAAO,GAAGa,SAAS,GAAG,OAAQ,CAAC;IAC1C;EAAG,CACH,CACD;EAED,MAAMC,cAAc,GAAG1B,cAAc,CAAC2B,GAAG,CAAE,CAAE;IAAEC,MAAM;IAAEC,IAAI;IAAEC;EAAK,CAAC,KAAM;IACxE,MAAMC,KAAK,GAAG;MACbC,UAAU,EAAE,IAAAC,+BAAwB,EAAEL,MAAM,EAAE,QAAS,CAAC;MACxDM,KAAK,EAAE;IACR,CAAC;IACD,MAAMZ,WAAW,GAChBQ,IAAI,aAAJA,IAAI,cAAJA,IAAI,GACJ,IAAAK,aAAO;IACN;IACA,IAAArB,QAAE,EAAE,kBAAmB,CAAC,EACxBe,IACD,CAAC;IACF,MAAMO,KAAK,GAAGN,IAAI,GACf,IAAAK,aAAO;IACP;IACA,IAAArB,QAAE,EAAE,aAAc,CAAC,EACnBgB,IACA,CAAC,GACDR,WAAW;IACd,MAAMD,UAAU,GAAG,IAAAgB,WAAa,EAAET,MAAM,EAAEzB,KAAM,CAAC;IAEjD,OACC,IAAAa,MAAA,CAAAC,aAAA,EAAC5B,qBAAA,CAAA6B,OAAoB,CAACC,MAAM;MAC3BC,GAAG,EAAGS,IAAM;MACZ1B,KAAK,EAAGyB,MAAQ;MAChBP,UAAU,EAAGA,UAAY;MACzB,cAAae,KAAO;MACpBd,WAAW,EAAGA,WAAa;MAC3BS,KAAK,EAAGA,KAAO;MACfP,OAAO,EAAGA,CAAA,KAAM;QACfpB,QAAQ,CAAEiB,UAAU,GAAGI,SAAS,GAAGG,MAAO,CAAC;MAC5C;IAAG,CACH,CAAC;EAEJ,CAAE,CAAC;EAEH,IAAIU,SAGkB;EAEtB,IAAK3C,SAAS,EAAG;IAChB2C,SAAS,GAAG;MAAE3C,SAAS,EAAE;IAAK,CAAC;EAChC,CAAC,MAAM;IACN,MAAM4C,UAAgD,GAAG;MACxD5C,SAAS,EAAE,KAAK;MAChBC;IACD,CAAC;IAED,IAAKS,SAAS,EAAG;MAChBiC,SAAS,GAAG;QAAE,GAAGC,UAAU;QAAE,YAAY,EAAElC;MAAU,CAAC;IACvD,CAAC,MAAM,IAAKC,cAAc,EAAG;MAC5BgC,SAAS,GAAG;QACX,GAAGC,UAAU;QACb,iBAAiB,EAAEjC;MACpB,CAAC;IACF,CAAC,MAAM;MACNgC,SAAS,GAAG;QACX,GAAGC,UAAU;QACb,YAAY,EAAE,IAAAzB,QAAE,EAAE,sBAAuB;MAC1C,CAAC;IACF;EACD;EAEA,MAAM0B,OAAO,GAAG1C,SAAS,GACtB,CAAEiB,WAAW,EAAE,GAAGW,cAAc,CAAE,GAClCA,cAAc;EAEjB,OACC,IAAAV,MAAA,CAAAC,aAAA,EAAC5B,qBAAA,CAAA6B,OAAoB;IAAA,GACfX,UAAU;IAAA,GACV+B,SAAS;IACdE,OAAO,EAAGA,OAAS;IACnBC,OAAO,EACN,CAAC,CAAE5C,SAAS,IACX,IAAAmB,MAAA,CAAAC,aAAA,EAAC5B,qBAAA,CAAA6B,OAAoB,CAACwB,YAAY;MACjClB,OAAO,EAAGA,CAAA,KAAMpB,QAAQ,CAAEqB,SAAU;IAAG,GAErC,IAAAX,QAAE,EAAE,OAAQ,CACoB;EAEpC,GAED,IAAAE,MAAA,CAAAC,aAAA,EAACxB,OAAA,CAAAkD,MAAM;IAACC,UAAU,EAAGJ,OAAO,CAACK,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG;EAAG,GAClD,IAAA7B,MAAA,CAAAC,aAAA,EAAC3B,OAAA,CAAAwD,MAAM;IAACC,OAAO,EAAG;EAAG,GAClB,CAAE9C,mBAAmB,IAAI,CAAEC,oBAAoB,IAChD,IAAAc,MAAA,CAAAC,aAAA,EAAC1B,iBAAA,CAAA2B,OAAgB;IAChBf,KAAK,EAAGS,OAAO,GAAGa,SAAS,GAAGtB,KAAO;IACrCC,QAAQ,EAAGA;EAAU,CACrB,CACD,EACC,CAAEF,oBAAoB,IACvB,IAAAc,MAAA,CAAAC,aAAA,EAAC7B,gBAAA,CAAA8B,OAAe;IACf8B,MAAM,EAAG,CAAE,IAAAlC,QAAE,EAAE,SAAU,CAAC,EAAE,IAAAA,QAAE,EAAE,YAAa,CAAC,CAAI;IAClDc,MAAM,EAAG7B,YAAc;IACvBI,KAAK,EAAGS,OAAO,GAAGa,SAAS,GAAGtB,KAAO;IACrCF,mBAAmB,EAAGA,mBAAqB;IAC3CgD,WAAW;IACX7C,QAAQ,EAAK8C,SAAS,IAAM;MAC3B,IAAK,CAAEA,SAAS,CAAE,CAAC,CAAE,EAAG;QACvBA,SAAS,CAAE,CAAC,CAAE,GAAG1C,WAAW;MAC7B;MACA,IAAK,CAAE0C,SAAS,CAAE,CAAC,CAAE,EAAG;QACvBA,SAAS,CAAE,CAAC,CAAE,GAAGzC,YAAY;MAC9B;MACA,MAAM0C,QAAQ,GACbD,SAAS,CAACL,MAAM,IAAI,CAAC,GAClBK,SAAS,GACTzB,SAAS;MACb;MACA;MACA;MACArB,QAAQ,CAAE+C,QAAS,CAAC;IACrB;EAAG,CACH,CAEK,CACD,CACa,CAAC;AAEzB;AAAC,IAAAC,QAAA,GAEc1D,aAAa;AAAA2D,OAAA,CAAAnC,OAAA,GAAAkC,QAAA"}