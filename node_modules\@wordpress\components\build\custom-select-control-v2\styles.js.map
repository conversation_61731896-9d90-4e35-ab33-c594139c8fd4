{"version": 3, "names": ["Ariakit", "_interopRequireWildcard", "require", "_utils", "_space", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "CustomSelectLabel", "_base", "SelectLabel", "process", "env", "NODE_ENV", "target", "label", "space", "exports", "inputHeights", "small", "CustomSelectButton", "Select", "shouldForwardProp", "prop", "size", "hasCustomRenderProp", "isSmallSize", "heightProperty", "display", "justifyContent", "alignItems", "backgroundColor", "COLORS", "white", "border", "gray", "borderRadius", "cursor", "width", "padding", "fontSize", "outlineStyle", "theme", "accent", "CustomSelectPopover", "SelectPopover", "CustomSelectItem", "SelectItem"], "sources": ["@wordpress/components/src/custom-select-control-v2/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n// eslint-disable-next-line no-restricted-imports\nimport * as Ariakit from '@ariakit/react';\n\n/**\n * Internal dependencies\n */\nimport { COLORS } from '../utils';\nimport { space } from '../utils/space';\nimport type { CustomSelectProps } from './types';\n\nexport const CustomSelectLabel = styled( Ariakit.SelectLabel )`\n\tfont-size: 11px;\n\tfont-weight: 500;\n\tline-height: 1.4;\n\ttext-transform: uppercase;\n\tmargin-bottom: ${ space( 2 ) };\n`;\n\nconst inputHeights = {\n\tdefault: 40,\n\tsmall: 24,\n};\n\nexport const CustomSelectButton = styled( Ariakit.Select, {\n\t// Do not forward `hasCustomRenderProp` to the underlying Ariakit.Select component\n\tshouldForwardProp: ( prop ) => prop !== 'hasCustomRenderProp',\n} )( ( {\n\tsize,\n\thasCustomRenderProp,\n}: {\n\tsize: NonNullable< CustomSelectProps[ 'size' ] >;\n\thasCustomRenderProp: boolean;\n} ) => {\n\tconst isSmallSize = size === 'small' && ! hasCustomRenderProp;\n\tconst heightProperty = hasCustomRenderProp ? 'minHeight' : 'height';\n\n\treturn {\n\t\tdisplay: 'flex',\n\t\tjustifyContent: 'space-between',\n\t\talignItems: 'center',\n\t\tbackgroundColor: COLORS.white,\n\t\tborder: `1px solid ${ COLORS.gray[ 600 ] }`,\n\t\tborderRadius: space( 0.5 ),\n\t\tcursor: 'pointer',\n\t\twidth: '100%',\n\t\t[ heightProperty ]: `${ inputHeights[ size ] }px`,\n\t\tpadding: isSmallSize ? space( 2 ) : space( 4 ),\n\t\tfontSize: isSmallSize ? '11px' : '13px',\n\t\t'&[data-focus-visible]': {\n\t\t\toutlineStyle: 'solid',\n\t\t},\n\t\t'&[aria-expanded=\"true\"]': {\n\t\t\toutlineStyle: `1.5px solid ${ COLORS.theme.accent }`,\n\t\t},\n\t};\n} );\n\nexport const CustomSelectPopover = styled( Ariakit.SelectPopover )`\n\tborder-radius: ${ space( 0.5 ) };\n\tbackground: ${ COLORS.white };\n\tborder: 1px solid ${ COLORS.gray[ 900 ] };\n`;\n\nexport const CustomSelectItem = styled( Ariakit.SelectItem )`\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: ${ space( 2 ) };\n\t&[data-active-item] {\n\t\tbackground-color: ${ COLORS.gray[ 300 ] };\n\t}\n`;\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAAuC,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAXvC;AACA;AACA;;AAEA;;AAGA;AACA;AACA;;AAKO,MAAMW,iBAAiB,GAAG,kBAAAC,KAAA,CAAAhB,OAAA,EAAQZ,OAAO,CAAC6B,WAAW,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,2FAK3C,IAAAC,YAAK,EAAE,CAAE,CAAC,SAAAL,OAAA,CAAAC,GAAA,CAAAC,QAAA,oqGAC5B;AAACI,OAAA,CAAAT,iBAAA,GAAAA,iBAAA;AAEF,MAAMU,YAAY,GAAG;EACpBzB,OAAO,EAAE,EAAE;EACX0B,KAAK,EAAE;AACR,CAAC;AAEM,MAAMC,kBAAkB,GAAG,kBAAAX,KAAA,CAAAhB,OAAA,EAAQZ,OAAO,CAACwC,MAAM,EAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACvD;EACAS,iBAAiB,EAAIC,IAAI,IAAMA,IAAI,KAAK,qBAAqB;EAAAT,MAAA;AAAA;EAD7D;EACAQ,iBAAiB,EAAIC,IAAI,IAAMA,IAAI,KAAK,qBAAqB;EAAAT,MAAA;EAAAC,KAAA;AAAA,CAC5D,CAAC,CAAE,CAAE;EACNS,IAAI;EACJC;AAID,CAAC,KAAM;EACN,MAAMC,WAAW,GAAGF,IAAI,KAAK,OAAO,IAAI,CAAEC,mBAAmB;EAC7D,MAAME,cAAc,GAAGF,mBAAmB,GAAG,WAAW,GAAG,QAAQ;EAEnE,OAAO;IACNG,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,eAAe,EAAEC,aAAM,CAACC,KAAK;IAC7BC,MAAM,EAAG,aAAaF,aAAM,CAACG,IAAI,CAAE,GAAG,CAAI,EAAC;IAC3CC,YAAY,EAAE,IAAApB,YAAK,EAAE,GAAI,CAAC;IAC1BqB,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,MAAM;IACb,CAAEX,cAAc,GAAK,GAAGT,YAAY,CAAEM,IAAI,CAAI,IAAG;IACjDe,OAAO,EAAEb,WAAW,GAAG,IAAAV,YAAK,EAAE,CAAE,CAAC,GAAG,IAAAA,YAAK,EAAE,CAAE,CAAC;IAC9CwB,QAAQ,EAAEd,WAAW,GAAG,MAAM,GAAG,MAAM;IACvC,uBAAuB,EAAE;MACxBe,YAAY,EAAE;IACf,CAAC;IACD,yBAAyB,EAAE;MAC1BA,YAAY,EAAG,eAAeT,aAAM,CAACU,KAAK,CAACC,MAAQ;IACpD;EACD,CAAC;AACF,CAAC,EAAAhC,OAAA,CAAAC,GAAA,CAAAC,QAAA,kqGAAC,CAAC;AAACI,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AAEG,MAAMwB,mBAAmB,GAAG,kBAAAnC,KAAA,CAAAhB,OAAA,EAAQZ,OAAO,CAACgE,aAAa,EAAAlC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,mBAC/C,IAAAC,YAAK,EAAE,GAAI,CAAC,kBACfgB,aAAM,CAACC,KAAK,wBACND,aAAM,CAACG,IAAI,CAAE,GAAG,CAAE,SAAAxB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oqGACvC;AAACI,OAAA,CAAA2B,mBAAA,GAAAA,mBAAA;AAEK,MAAME,gBAAgB,GAAG,kBAAArC,KAAA,CAAAhB,OAAA,EAAQZ,OAAO,CAACkE,UAAU,EAAApC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,2EAI/C,IAAAC,YAAK,EAAE,CAAE,CAAC,4CAEAgB,aAAM,CAACG,IAAI,CAAE,GAAG,CAAE,UAAAxB,OAAA,CAAAC,GAAA,CAAAC,QAAA,oqGAExC;AAACI,OAAA,CAAA6B,gBAAA,GAAAA,gBAAA"}