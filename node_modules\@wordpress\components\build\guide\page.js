"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = GuidePage;
var _react = require("react");
var _element = require("@wordpress/element");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
/**
 * WordPress dependencies
 */

function GuidePage(props) {
  (0, _element.useEffect)(() => {
    (0, _deprecated.default)('<GuidePage>', {
      since: '5.5',
      alternative: 'the `pages` prop in <Guide>'
    });
  }, []);
  return (0, _react.createElement)("div", {
    ...props
  });
}
//# sourceMappingURL=page.js.map