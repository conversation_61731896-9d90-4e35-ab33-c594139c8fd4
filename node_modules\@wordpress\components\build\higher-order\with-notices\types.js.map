{"version": 3, "names": [], "sources": ["@wordpress/components/src/higher-order/with-notices/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { NoticeListProps } from '../../notice/types';\n\nexport type WithNoticeProps = {\n\tnoticeList: NoticeListProps[ 'notices' ];\n\tnoticeOperations: {\n\t\t/**\n\t\t * Function passed down as a prop that adds a new notice.\n\t\t *\n\t\t * @param notice Notice to add.\n\t\t */\n\t\tcreateNotice: (\n\t\t\tnotice: NoticeListProps[ 'notices' ][ number ]\n\t\t) => void;\n\t\t/**\n\t\t * Function passed as a prop that adds a new error notice.\n\t\t *\n\t\t * @param msg Error message of the notice.\n\t\t */\n\t\tcreateErrorNotice: ( msg: string ) => void;\n\t\t/**\n\t\t * Removes a notice by id.\n\t\t *\n\t\t * @param id Id of the notice to remove.\n\t\t */\n\t\tremoveNotice: ( id: string ) => void;\n\t\t/**\n\t\t * Removes all notices\n\t\t */\n\t\tremoveAllNotices: () => void;\n\t};\n\tnoticeUI: false | JSX.Element;\n};\n"], "mappings": ""}