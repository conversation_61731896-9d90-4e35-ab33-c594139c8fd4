"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DropZoneComponent = DropZoneComponent;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _dom = require("@wordpress/dom");
var _compose = require("@wordpress/compose");
var _animation = require("../animation");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * `DropZone` is a component creating a drop zone area taking the full size of its parent element. It supports dropping files, HTML content or any other HTML drop event.
 *
 * ```jsx
 * import { DropZone } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyDropZone = () => {
 *   const [ hasDropped, setHasDropped ] = useState( false );
 *
 *   return (
 *     <div>
 *       { hasDropped ? 'Dropped!' : 'Drop something here' }
 *       <DropZone
 *         onFilesDrop={ () => setHasDropped( true ) }
 *         onHTMLDrop={ () => setHasDropped( true ) }
 *         onDrop={ () => setHasDropped( true ) }
 *       />
 *     </div>
 *   );
 * }
 * ```
 */
function DropZoneComponent({
  className,
  label,
  onFilesDrop,
  onHTMLDrop,
  onDrop,
  ...restProps
}) {
  const [isDraggingOverDocument, setIsDraggingOverDocument] = (0, _element.useState)();
  const [isDraggingOverElement, setIsDraggingOverElement] = (0, _element.useState)();
  const [type, setType] = (0, _element.useState)();
  const ref = (0, _compose.__experimentalUseDropZone)({
    onDrop(event) {
      const files = event.dataTransfer ? (0, _dom.getFilesFromDataTransfer)(event.dataTransfer) : [];
      const html = event.dataTransfer?.getData('text/html');

      /**
       * From Windows Chrome 96, the `event.dataTransfer` returns both file object and HTML.
       * The order of the checks is important to recognise the HTML drop.
       */
      if (html && onHTMLDrop) {
        onHTMLDrop(html);
      } else if (files.length && onFilesDrop) {
        onFilesDrop(files);
      } else if (onDrop) {
        onDrop(event);
      }
    },
    onDragStart(event) {
      setIsDraggingOverDocument(true);
      let _type = 'default';

      /**
       * From Windows Chrome 96, the `event.dataTransfer` returns both file object and HTML.
       * The order of the checks is important to recognise the HTML drop.
       */
      if (event.dataTransfer?.types.includes('text/html')) {
        _type = 'html';
      } else if (
      // Check for the types because sometimes the files themselves
      // are only available on drop.
      event.dataTransfer?.types.includes('Files') || (event.dataTransfer ? (0, _dom.getFilesFromDataTransfer)(event.dataTransfer) : []).length > 0) {
        _type = 'file';
      }
      setType(_type);
    },
    onDragEnd() {
      setIsDraggingOverDocument(false);
      setType(undefined);
    },
    onDragEnter() {
      setIsDraggingOverElement(true);
    },
    onDragLeave() {
      setIsDraggingOverElement(false);
    }
  });
  const disableMotion = (0, _compose.useReducedMotion)();
  let children;
  const backdrop = {
    hidden: {
      opacity: 0
    },
    show: {
      opacity: 1,
      transition: {
        type: 'tween',
        duration: 0.2,
        delay: 0,
        delayChildren: 0.1
      }
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.2,
        delayChildren: 0
      }
    }
  };
  const foreground = {
    hidden: {
      opacity: 0,
      scale: 0.9
    },
    show: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.1
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9
    }
  };
  if (isDraggingOverElement) {
    children = (0, _react.createElement)(_animation.__unstableMotion.div, {
      variants: backdrop,
      initial: disableMotion ? 'show' : 'hidden',
      animate: "show",
      exit: disableMotion ? 'show' : 'exit',
      className: "components-drop-zone__content"
      // Without this, when this div is shown,
      // Safari calls a onDropZoneLeave causing a loop because of this bug
      // https://bugs.webkit.org/show_bug.cgi?id=66547
      ,
      style: {
        pointerEvents: 'none'
      }
    }, (0, _react.createElement)(_animation.__unstableMotion.div, {
      variants: foreground
    }, (0, _react.createElement)(_icons.Icon, {
      icon: _icons.upload,
      className: "components-drop-zone__content-icon"
    }), (0, _react.createElement)("span", {
      className: "components-drop-zone__content-text"
    }, label ? label : (0, _i18n.__)('Drop files to upload'))));
  }
  const classes = (0, _classnames.default)('components-drop-zone', className, {
    'is-active': (isDraggingOverDocument || isDraggingOverElement) && (type === 'file' && onFilesDrop || type === 'html' && onHTMLDrop || type === 'default' && onDrop),
    'is-dragging-over-document': isDraggingOverDocument,
    'is-dragging-over-element': isDraggingOverElement,
    [`is-dragging-${type}`]: !!type
  });
  return (0, _react.createElement)("div", {
    ...restProps,
    ref: ref,
    className: classes
  }, disableMotion ? children : (0, _react.createElement)(_animation.__unstableAnimatePresence, null, children));
}
var _default = DropZoneComponent;
exports.default = _default;
//# sourceMappingURL=index.js.map