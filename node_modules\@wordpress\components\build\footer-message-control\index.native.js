"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _footerMessageCell = _interopRequireDefault(require("../mobile/bottom-sheet/footer-message-cell"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function FooterMessageControl({
  ...props
}) {
  return (0, _react.createElement)(_footerMessageCell.default, {
    ...props
  });
}
var _default = (0, _element.memo)(FooterMessageControl);
exports.default = _default;
//# sourceMappingURL=index.native.js.map