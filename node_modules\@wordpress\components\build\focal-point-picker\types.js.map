{"version": 3, "names": [], "sources": ["@wordpress/components/src/focal-point-picker/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type {\n\tCSSProperties,\n\tReactEventHandler,\n\t<PERSON>f,\n\tVideoHTMLAttributes,\n} from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { BaseControlProps } from '../base-control/types';\n\nexport type FocalPoint = Record< FocalPointAxis, number >;\nexport type FocalPointAxis = 'x' | 'y';\n\nexport type FocalPointPickerProps = Pick<\n\tBaseControlProps,\n\t'help' | 'hideLabelFromVision' | 'label'\n> & {\n\t/**\n\t * Start opting into the new margin-free styles that will become the default in a future version.\n\t *\n\t * @default false\n\t */\n\t__nextHasNoMarginBottom?: boolean;\n\t/**\n\t * Start opting into the larger default height that will become the default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n\t/**\n\t * Autoplays HTML5 video. This only applies to video sources (`url`).\n\t *\n\t * @default true\n\t */\n\tautoPlay?: FocalPointPickerMediaProps[ 'autoPlay' ];\n\t/**\n\t * Callback which is called when the focal point changes.\n\t */\n\tonChange: ( value: FocalPoint ) => void;\n\tonDrag?: ( value: FocalPoint, event: MouseEvent ) => void;\n\t/**\n\t * Callback which is called at the end of drag operations.\n\t */\n\tonDragEnd?: () => void;\n\t/**\n\t * Callback which is called at the start of drag operations.\n\t */\n\tonDragStart?: ( value: FocalPoint, event: React.MouseEvent ) => void;\n\t/**\n\t * Function which is called before internal updates to the value state.\n\t * It receives the upcoming value and may return a modified one.\n\t */\n\tresolvePoint?: ( point: FocalPoint ) => FocalPoint;\n\t/**\n\t * URL of the image or video to be displayed.\n\t */\n\turl: FocalPointPickerMediaProps[ 'src' ];\n\t/**\n\t * The focal point. Should be an object containing `x` and `y` params.\n\t */\n\tvalue: FocalPoint;\n};\n\nexport type FocalPointPickerControlsProps = {\n\t__nextHasNoMarginBottom?: boolean;\n\t__next40pxDefaultSize?: boolean;\n\t/**\n\t * A bit of extra bottom margin will be added if a `help` text\n\t * needs to be rendered under it.\n\t */\n\thasHelpText: boolean;\n\tonChange?: ( value: FocalPoint ) => void;\n\tpoint?: FocalPoint;\n};\n\nexport type FocalPointPickerGridProps = {\n\tbounds: { width: number; height: number };\n\tshowOverlay?: boolean;\n};\n\nexport type FocalPointPickerMediaProps = Pick<\n\tVideoHTMLAttributes< HTMLVideoElement >,\n\t'autoPlay' | 'muted'\n> & {\n\talt: string;\n\tmediaRef?: Ref< HTMLDivElement | HTMLVideoElement | HTMLImageElement >;\n\tonLoad?: ReactEventHandler< HTMLVideoElement | HTMLImageElement >;\n\tsrc: string;\n};\n\nexport type FocalPointProps = {\n\tisDragging: boolean;\n\tleft: CSSProperties[ 'left' ];\n\ttop: CSSProperties[ 'top' ];\n};\n"], "mappings": ""}