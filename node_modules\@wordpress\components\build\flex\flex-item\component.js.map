{"version": 3, "names": ["_context", "require", "_view", "_hook", "UnconnectedFlexItem", "props", "forwardedRef", "flexItemProps", "useFlexItem", "_react", "createElement", "View", "ref", "FlexItem", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/flex/flex-item/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { View } from '../../view';\nimport { useFlexItem } from './hook';\nimport type { FlexItemProps } from '../types';\n\nfunction UnconnectedFlexItem(\n\tprops: WordPressComponentProps< FlexItemProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst flexItemProps = useFlexItem( props );\n\n\treturn <View { ...flexItemProps } ref={ forwardedRef } />;\n}\n\n/**\n * `FlexItem` is a primitive layout component that aligns content within layout\n * containers like `Flex`.\n *\n * ```jsx\n * import { Flex, FlexItem } from '@wordpress/components';\n *\n * function Example() {\n *   return (\n *     <Flex>\n *       <FlexItem>...</FlexItem>\n *     </Flex>\n *   );\n * }\n * ```\n */\nexport const FlexItem = contextConnect( UnconnectedFlexItem, 'FlexItem' );\n\nexport default FlexItem;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,mBAAmBA,CAC3BC,KAAsD,EACtDC,YAAiC,EAChC;EACD,MAAMC,aAAa,GAAG,IAAAC,iBAAW,EAAEH,KAAM,CAAC;EAE1C,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,aAAa;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,QAAQ,GAAG,IAAAC,uBAAc,EAAEV,mBAAmB,EAAE,UAAW,CAAC;AAACW,OAAA,CAAAF,QAAA,GAAAA,QAAA;AAAA,IAAAG,QAAA,GAE3DH,QAAQ;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}