{"version": 3, "names": ["_react", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "Elevation", "process", "env", "NODE_ENV", "name", "styles", "map", "toString", "exports"], "sources": ["@wordpress/components/src/elevation/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\nexport const Elevation = css`\n\tbackground: transparent;\n\tdisplay: block;\n\tmargin: 0 !important;\n\tpointer-events: none;\n\tposition: absolute;\n\twill-change: box-shadow;\n`;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAAqC,SAAAC,iCAAA;AAE9B,MAAMC,SAAS,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAOrB;AAACS,OAAA,CAAAR,SAAA,GAAAA,SAAA"}