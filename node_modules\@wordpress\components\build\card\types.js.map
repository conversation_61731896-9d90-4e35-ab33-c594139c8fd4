{"version": 3, "names": [], "sources": ["@wordpress/components/src/card/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { SurfaceProps } from '../surface/types';\n\ntype DeprecatedSizeOptions = 'extraSmall';\nexport type SizeOptions = 'xSmall' | 'small' | 'medium' | 'large';\n\ntype SizeableProps = {\n\t/**\n\t * Determines the amount of padding within the component.\n\t *\n\t * @default 'medium'\n\t */\n\tsize?: SizeOptions | DeprecatedSizeOptions;\n};\n\nexport type Props = SurfaceProps &\n\tSizeableProps & {\n\t\t/**\n\t\t * Size of the elevation shadow, based on the Style system's elevation system.\n\t\t * Elevating a `Card` can be done by adjusting the `elevation` prop. This may\n\t\t * be helpful in highlighting certain content. For more information, check out\n\t\t * `Elevation`.\n\t\t *\n\t\t * @default 0\n\t\t */\n\t\televation?: number;\n\t\t/**\n\t\t * Renders without a border.\n\t\t *\n\t\t * @default false\n\t\t */\n\t\tisBorderless?: boolean;\n\t\t/**\n\t\t * Renders with rounded corners.\n\t\t *\n\t\t * @default true\n\t\t */\n\t\tisRounded?: boolean;\n\t\t/**\n\t\t * Renders with elevation styles (box shadow).\n\t\t *\n\t\t * @default false\n\t\t * @deprecated\n\t\t */\n\t\tisElevated?: boolean;\n\t};\n\ntype BaseSubComponentProps = SizeableProps & {\n\t/**\n\t * The children elements.\n\t */\n\tchildren: React.ReactNode;\n\t/**\n\t * Renders with a light gray background color.\n\t *\n\t * @default false\n\t */\n\tisShady?: boolean;\n};\n\nexport type BodyProps = BaseSubComponentProps & {\n\t/**\n\t * Determines if the component is scrollable.\n\t *\n\t * @default false\n\t */\n\tisScrollable?: boolean;\n};\n\nexport type MediaProps = {\n\t/**\n\t * The children elements.\n\t */\n\tchildren: React.ReactNode;\n};\n\ntype MarginalSubComponentProps = BaseSubComponentProps & {\n\t/**\n\t * Renders without a border.\n\t *\n\t * @default false\n\t */\n\tisBorderless?: boolean;\n};\n\nexport type HeaderProps = MarginalSubComponentProps;\n\nexport type FooterProps = MarginalSubComponentProps & {\n\tjustify?: CSSProperties[ 'justifyContent' ];\n};\n"], "mappings": ""}