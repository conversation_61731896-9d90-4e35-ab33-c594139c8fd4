"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = FocalPointPickerControls;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _focalPointPickerStyle = require("./styles/focal-point-picker-style");
var _utils = require("./utils");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const TEXTCONTROL_MIN = 0;
const TEXTCONTROL_MAX = 100;
const noop = () => {};
function FocalPointPickerControls({
  __nextHasNoMarginBottom,
  __next40pxDefaultSize,
  hasHelpText,
  onChange = noop,
  point = {
    x: 0.5,
    y: 0.5
  }
}) {
  const valueX = (0, _utils.fractionToPercentage)(point.x);
  const valueY = (0, _utils.fractionToPercentage)(point.y);
  const handleChange = (value, axis) => {
    if (value === undefined) return;
    const num = parseInt(value, 10);
    if (!isNaN(num)) {
      onChange({
        ...point,
        [axis]: num / 100
      });
    }
  };
  return (0, _react.createElement)(_focalPointPickerStyle.ControlWrapper, {
    className: "focal-point-picker__controls",
    __nextHasNoMarginBottom: __nextHasNoMarginBottom,
    hasHelpText: hasHelpText,
    gap: 4
  }, (0, _react.createElement)(FocalPointUnitControl, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    label: (0, _i18n.__)('Left'),
    "aria-label": (0, _i18n.__)('Focal point left position'),
    value: [valueX, '%'].join(''),
    onChange: next => handleChange(next, 'x'),
    dragDirection: "e"
  }), (0, _react.createElement)(FocalPointUnitControl, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    label: (0, _i18n.__)('Top'),
    "aria-label": (0, _i18n.__)('Focal point top position'),
    value: [valueY, '%'].join(''),
    onChange: next => handleChange(next, 'y'),
    dragDirection: "s"
  }));
}
function FocalPointUnitControl(props) {
  return (0, _react.createElement)(_focalPointPickerStyle.StyledUnitControl, {
    className: "focal-point-picker__controls-position-unit-control",
    labelPosition: "top",
    max: TEXTCONTROL_MAX,
    min: TEXTCONTROL_MIN,
    units: [{
      value: '%',
      label: '%'
    }],
    ...props
  });
}
//# sourceMappingURL=controls.js.map