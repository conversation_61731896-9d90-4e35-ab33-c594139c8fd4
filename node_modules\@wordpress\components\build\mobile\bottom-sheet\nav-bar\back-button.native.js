"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _compose = require("@wordpress/compose");
var _styles = _interopRequireDefault(require("./styles.scss"));
var _actionButton = _interopRequireDefault(require("./action-button"));
var _chevronBack = _interopRequireDefault(require("./../chevron-back"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function Button({
  onPress,
  icon,
  text
}) {
  const buttonTextStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default['button-text'], _styles.default['button-text-dark']);
  return (0, _react.createElement)(_reactNative.View, {
    style: _styles.default['back-button']
  }, (0, _react.createElement)(_actionButton.default, {
    onPress: onPress,
    accessibilityLabel: (0, _i18n.__)('Go back'),
    accessibilityHint: (0, _i18n.__)('Navigates to the previous content sheet')
  }, icon, text && (0, _react.createElement)(_reactNative.Text, {
    style: buttonTextStyle,
    maxFontSizeMultiplier: 2
  }, text)));
}
function BackButton({
  onPress
}) {
  const chevronLeftStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default['chevron-left-icon'], _styles.default['chevron-left-icon-dark']);
  const arrowLeftStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default['arrow-left-icon'], _styles.default['arrow-left-icon-dark']);
  let backIcon;
  let backText;
  if (_reactNative.Platform.OS === 'ios') {
    backIcon = (0, _react.createElement)(_icons.Icon, {
      icon: _chevronBack.default,
      size: 21,
      style: chevronLeftStyle
    });
    backText = (0, _i18n.__)('Back');
  } else {
    backIcon = (0, _react.createElement)(_icons.Icon, {
      icon: _icons.arrowLeft,
      size: 24,
      style: arrowLeftStyle
    });
  }
  return (0, _react.createElement)(Button, {
    onPress: onPress,
    icon: backIcon,
    text: backText
  });
}
function DismissButton({
  onPress,
  iosText
}) {
  const arrowLeftStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default['arrow-left-icon'], _styles.default['arrow-left-icon-dark']);
  let backIcon;
  let backText;
  if (_reactNative.Platform.OS === 'ios') {
    backText = iosText ? iosText : (0, _i18n.__)('Cancel');
  } else {
    backIcon = (0, _react.createElement)(_icons.Icon, {
      icon: _icons.close,
      size: 24,
      style: arrowLeftStyle
    });
  }
  return (0, _react.createElement)(Button, {
    onPress: onPress,
    icon: backIcon,
    text: backText
  });
}
Button.Back = BackButton;
Button.Dismiss = DismissButton; // Cancel or Close Button.
var _default = Button;
exports.default = _default;
//# sourceMappingURL=back-button.native.js.map