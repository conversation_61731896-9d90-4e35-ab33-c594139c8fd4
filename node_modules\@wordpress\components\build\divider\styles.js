"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DividerView = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react = require("@emotion/react");
var _space = require("../utils/space");
var _utils = require("../utils");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const MARGIN_DIRECTIONS = {
  vertical: {
    start: 'marginLeft',
    end: 'marginRight'
  },
  horizontal: {
    start: 'marginTop',
    end: 'marginBottom'
  }
};

// Renders the correct margins given the Divider's `orientation` and the writing direction.
// When both the generic `margin` and the specific `marginStart|marginEnd` props are defined,
// the latter will take priority.
const renderMargin = ({
  'aria-orientation': orientation = 'horizontal',
  margin,
  marginStart,
  marginEnd
}) => /*#__PURE__*/(0, _react.css)((0, _utils.rtl)({
  [MARGIN_DIRECTIONS[orientation].start]: (0, _space.space)(marginStart !== null && marginStart !== void 0 ? marginStart : margin),
  [MARGIN_DIRECTIONS[orientation].end]: (0, _space.space)(marginEnd !== null && marginEnd !== void 0 ? marginEnd : margin)
})(), process.env.NODE_ENV === "production" ? "" : ";label:renderMargin;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
var _ref = process.env.NODE_ENV === "production" ? {
  name: "1u4hpl4",
  styles: "display:inline"
} : {
  name: "9r0jm7-renderDisplay",
  styles: "display:inline;label:renderDisplay;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
const renderDisplay = ({
  'aria-orientation': orientation = 'horizontal'
}) => {
  return orientation === 'vertical' ? _ref : undefined;
};
const renderBorder = ({
  'aria-orientation': orientation = 'horizontal'
}) => {
  return /*#__PURE__*/(0, _react.css)({
    [orientation === 'vertical' ? 'borderRight' : 'borderBottom']: '1px solid currentColor'
  }, process.env.NODE_ENV === "production" ? "" : ";label:renderBorder;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const renderSize = ({
  'aria-orientation': orientation = 'horizontal'
}) => /*#__PURE__*/(0, _react.css)({
  height: orientation === 'vertical' ? 'auto' : 0,
  width: orientation === 'vertical' ? 0 : 'auto'
}, process.env.NODE_ENV === "production" ? "" : ";label:renderSize;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
const DividerView = (0, _base.default)("hr", process.env.NODE_ENV === "production" ? {
  target: "e19on6iw0"
} : {
  target: "e19on6iw0",
  label: "DividerView"
})("border:0;margin:0;", renderDisplay, " ", renderBorder, " ", renderSize, " ", renderMargin, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.DividerView = DividerView;
//# sourceMappingURL=styles.js.map