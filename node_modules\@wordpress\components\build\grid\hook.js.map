{"version": 3, "names": ["_react", "require", "_element", "_context", "_utils", "_useResponsiveValue", "_configValues", "_interopRequireDefault", "_useCx", "useGrid", "props", "align", "alignment", "className", "columnGap", "columns", "gap", "isInline", "justify", "rowGap", "rows", "templateColumns", "templateRows", "otherProps", "useContextSystem", "columnsAsArray", "Array", "isArray", "column", "useResponsiveValue", "rowsAsArray", "row", "gridTemplateColumns", "gridTemplateRows", "cx", "useCx", "classes", "useMemo", "alignmentProps", "getAlignmentProps", "gridClasses", "css", "alignItems", "display", "CONFIG", "gridBase", "undefined", "gridRowGap", "gridColumnGap", "justifyContent", "verticalAlign", "process", "env", "NODE_ENV"], "sources": ["@wordpress/components/src/grid/hook.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\n/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { useContextSystem } from '../context';\nimport { getAlignmentProps } from './utils';\nimport { useResponsiveValue } from '../utils/use-responsive-value';\nimport CONFIG from '../utils/config-values';\nimport { useCx } from '../utils/hooks/use-cx';\nimport type { GridProps } from './types';\n\nexport default function useGrid(\n\tprops: WordPressComponentProps< GridProps, 'div' >\n) {\n\tconst {\n\t\talign,\n\t\talignment,\n\t\tclassName,\n\t\tcolumnGap,\n\t\tcolumns = 2,\n\t\tgap = 3,\n\t\tisInline = false,\n\t\tjustify,\n\t\trowGap,\n\t\trows,\n\t\ttemplateColumns,\n\t\ttemplateRows,\n\t\t...otherProps\n\t} = useContextSystem( props, 'Grid' );\n\n\tconst columnsAsArray = Array.isArray( columns ) ? columns : [ columns ];\n\tconst column = useResponsiveValue( columnsAsArray );\n\tconst rowsAsArray = Array.isArray( rows ) ? rows : [ rows ];\n\tconst row = useResponsiveValue( rowsAsArray );\n\n\tconst gridTemplateColumns =\n\t\ttemplateColumns || ( !! columns && `repeat( ${ column }, 1fr )` );\n\tconst gridTemplateRows =\n\t\ttemplateRows || ( !! rows && `repeat( ${ row }, 1fr )` );\n\n\tconst cx = useCx();\n\n\tconst classes = useMemo( () => {\n\t\tconst alignmentProps = getAlignmentProps( alignment );\n\n\t\tconst gridClasses = css( {\n\t\t\talignItems: align,\n\t\t\tdisplay: isInline ? 'inline-grid' : 'grid',\n\t\t\tgap: `calc( ${ CONFIG.gridBase } * ${ gap } )`,\n\t\t\tgridTemplateColumns: gridTemplateColumns || undefined,\n\t\t\tgridTemplateRows: gridTemplateRows || undefined,\n\t\t\tgridRowGap: rowGap,\n\t\t\tgridColumnGap: columnGap,\n\t\t\tjustifyContent: justify,\n\t\t\tverticalAlign: isInline ? 'middle' : undefined,\n\t\t\t...alignmentProps,\n\t\t} );\n\n\t\treturn cx( gridClasses, className );\n\t}, [\n\t\talign,\n\t\talignment,\n\t\tclassName,\n\t\tcolumnGap,\n\t\tcx,\n\t\tgap,\n\t\tgridTemplateColumns,\n\t\tgridTemplateRows,\n\t\tisInline,\n\t\tjustify,\n\t\trowGap,\n\t] );\n\n\treturn { ...otherProps, className: classes };\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAMA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AAlBA;AACA;AACA;;AAGA;AACA;AACA;;AAce,SAASQ,OAAOA,CAC9BC,KAAkD,EACjD;EACD,MAAM;IACLC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,SAAS;IACTC,OAAO,GAAG,CAAC;IACXC,GAAG,GAAG,CAAC;IACPC,QAAQ,GAAG,KAAK;IAChBC,OAAO;IACPC,MAAM;IACNC,IAAI;IACJC,eAAe;IACfC,YAAY;IACZ,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEd,KAAK,EAAE,MAAO,CAAC;EAErC,MAAMe,cAAc,GAAGC,KAAK,CAACC,OAAO,CAAEZ,OAAQ,CAAC,GAAGA,OAAO,GAAG,CAAEA,OAAO,CAAE;EACvE,MAAMa,MAAM,GAAG,IAAAC,sCAAkB,EAAEJ,cAAe,CAAC;EACnD,MAAMK,WAAW,GAAGJ,KAAK,CAACC,OAAO,CAAEP,IAAK,CAAC,GAAGA,IAAI,GAAG,CAAEA,IAAI,CAAE;EAC3D,MAAMW,GAAG,GAAG,IAAAF,sCAAkB,EAAEC,WAAY,CAAC;EAE7C,MAAME,mBAAmB,GACxBX,eAAe,IAAM,CAAC,CAAEN,OAAO,IAAK,WAAWa,MAAQ,SAAU;EAClE,MAAMK,gBAAgB,GACrBX,YAAY,IAAM,CAAC,CAAEF,IAAI,IAAK,WAAWW,GAAK,SAAU;EAEzD,MAAMG,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,MAAMC,cAAc,GAAG,IAAAC,wBAAiB,EAAE3B,SAAU,CAAC;IAErD,MAAM4B,WAAW,gBAAG,IAAAC,UAAG,EAAE;MACxBC,UAAU,EAAE/B,KAAK;MACjBgC,OAAO,EAAE1B,QAAQ,GAAG,aAAa,GAAG,MAAM;MAC1CD,GAAG,EAAG,SAAS4B,qBAAM,CAACC,QAAU,MAAM7B,GAAK,IAAG;MAC9CgB,mBAAmB,EAAEA,mBAAmB,IAAIc,SAAS;MACrDb,gBAAgB,EAAEA,gBAAgB,IAAIa,SAAS;MAC/CC,UAAU,EAAE5B,MAAM;MAClB6B,aAAa,EAAElC,SAAS;MACxBmC,cAAc,EAAE/B,OAAO;MACvBgC,aAAa,EAAEjC,QAAQ,GAAG,QAAQ,GAAG6B,SAAS;MAC9C,GAAGR;IACJ,CAAC,EAAAa,OAAA,CAAAC,GAAA,CAAAC,QAAA,gDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,suGAAC,CAAC;IAEH,OAAOnB,EAAE,CAAEM,WAAW,EAAE3B,SAAU,CAAC;EACpC,CAAC,EAAE,CACFF,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,SAAS,EACToB,EAAE,EACFlB,GAAG,EACHgB,mBAAmB,EACnBC,gBAAgB,EAChBhB,QAAQ,EACRC,OAAO,EACPC,MAAM,CACL,CAAC;EAEH,OAAO;IAAE,GAAGI,UAAU;IAAEV,SAAS,EAAEuB;EAAQ,CAAC;AAC7C"}