{"version": 3, "names": ["_element", "require", "CardContext", "createContext", "exports", "useCardContext", "useContext"], "sources": ["@wordpress/components/src/card/context.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { createContext, useContext } from '@wordpress/element';\n\nexport const CardContext = createContext( {} );\nexport const useCardContext = () => useContext( CardContext );\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGO,MAAMC,WAAW,GAAG,IAAAC,sBAAa,EAAE,CAAC,CAAE,CAAC;AAACC,OAAA,CAAAF,WAAA,GAAAA,WAAA;AACxC,MAAMG,cAAc,GAAGA,CAAA,KAAM,IAAAC,mBAAU,EAAEJ,WAAY,CAAC;AAACE,OAAA,CAAAC,cAAA,GAAAA,cAAA"}