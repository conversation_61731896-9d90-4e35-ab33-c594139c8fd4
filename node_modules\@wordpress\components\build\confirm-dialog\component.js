"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ConfirmDialog = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _modal = _interopRequireDefault(require("../modal"));
var _context = require("../context");
var _flex = require("../flex");
var _button = _interopRequireDefault(require("../button"));
var _text = require("../text");
var _vStack = require("../v-stack");
var styles = _interopRequireWildcard(require("./styles"));
var _useCx = require("../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const UnconnectedConfirmDialog = (props, forwardedRef) => {
  const {
    isOpen: isOpenProp,
    onConfirm,
    onCancel,
    children,
    confirmButtonText,
    cancelButtonText,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'ConfirmDialog');
  const cx = (0, _useCx.useCx)();
  const wrapperClassName = cx(styles.wrapper);
  const cancelButtonRef = (0, _element.useRef)();
  const confirmButtonRef = (0, _element.useRef)();
  const [isOpen, setIsOpen] = (0, _element.useState)();
  const [shouldSelfClose, setShouldSelfClose] = (0, _element.useState)();
  (0, _element.useEffect)(() => {
    // We only allow the dialog to close itself if `isOpenProp` is *not* set.
    // If `isOpenProp` is set, then it (probably) means it's controlled by a
    // parent component. In that case, `shouldSelfClose` might do more harm than
    // good, so we disable it.
    const isIsOpenSet = typeof isOpenProp !== 'undefined';
    setIsOpen(isIsOpenSet ? isOpenProp : true);
    setShouldSelfClose(!isIsOpenSet);
  }, [isOpenProp]);
  const handleEvent = (0, _element.useCallback)(callback => event => {
    callback?.(event);
    if (shouldSelfClose) {
      setIsOpen(false);
    }
  }, [shouldSelfClose, setIsOpen]);
  const handleEnter = (0, _element.useCallback)(event => {
    // Avoid triggering the 'confirm' action when a button is focused,
    // as this can cause a double submission.
    const isConfirmOrCancelButton = event.target === cancelButtonRef.current || event.target === confirmButtonRef.current;
    if (!isConfirmOrCancelButton && event.key === 'Enter') {
      handleEvent(onConfirm)(event);
    }
  }, [handleEvent, onConfirm]);
  const cancelLabel = cancelButtonText !== null && cancelButtonText !== void 0 ? cancelButtonText : (0, _i18n.__)('Cancel');
  const confirmLabel = confirmButtonText !== null && confirmButtonText !== void 0 ? confirmButtonText : (0, _i18n.__)('OK');
  return (0, _react.createElement)(_react.Fragment, null, isOpen && (0, _react.createElement)(_modal.default, {
    onRequestClose: handleEvent(onCancel),
    onKeyDown: handleEnter,
    closeButtonLabel: cancelLabel,
    isDismissible: true,
    ref: forwardedRef,
    overlayClassName: wrapperClassName,
    __experimentalHideHeader: true,
    ...otherProps
  }, (0, _react.createElement)(_vStack.VStack, {
    spacing: 8
  }, (0, _react.createElement)(_text.Text, null, children), (0, _react.createElement)(_flex.Flex, {
    direction: "row",
    justify: "flex-end"
  }, (0, _react.createElement)(_button.default, {
    ref: cancelButtonRef,
    variant: "tertiary",
    onClick: handleEvent(onCancel)
  }, cancelLabel), (0, _react.createElement)(_button.default, {
    ref: confirmButtonRef,
    variant: "primary",
    onClick: handleEvent(onConfirm)
  }, confirmLabel)))));
};

/**
 * `ConfirmDialog` is built of top of [`Modal`](/packages/components/src/modal/README.md)
 * and displays a confirmation dialog, with _confirm_ and _cancel_ buttons.
 * The dialog is confirmed by clicking the _confirm_ button or by pressing the `Enter` key.
 * It is cancelled (closed) by clicking the _cancel_ button, by pressing the `ESC` key, or by
 * clicking outside the dialog focus (i.e, the overlay).
 *
 * `ConfirmDialog` has two main implicit modes: controlled and uncontrolled.
 *
 * UnControlled:
 *
 * Allows the component to be used standalone, just by declaring it as part of another React's component render method:
 * -   It will be automatically open (displayed) upon mounting;
 * -   It will be automatically closed when clicking the _cancel_ button, by pressing the `ESC` key, or by clicking outside the dialog focus (i.e, the overlay);
 * -   `onCancel` is not mandatory but can be passed. Even if passed, the dialog will still be able to close itself.
 *
 * Activating this mode is as simple as omitting the `isOpen` prop. The only mandatory prop, in this case, is the `onConfirm` callback. The message is passed as the `children`. You can pass any JSX you'd like, which allows to further format the message or include sub-component if you'd like:
 *
 * ```jsx
 * import { __experimentalConfirmDialog as ConfirmDialog } from '@wordpress/components';
 *
 * function Example() {
 * 	return (
 * 		<ConfirmDialog onConfirm={ () => console.debug( ' Confirmed! ' ) }>
 * 			Are you sure? <strong>This action cannot be undone!</strong>
 * 		</ConfirmDialog>
 * 	);
 * }
 * ```
 *
 *
 * Controlled mode:
 *  Let the parent component control when the dialog is open/closed. It's activated when a
 * boolean value is passed to `isOpen`:
 * -   It will not be automatically closed. You need to let it know when to open/close by updating the value of the `isOpen` prop;
 * -   Both `onConfirm` and the `onCancel` callbacks are mandatory props in this mode;
 * -   You'll want to update the state that controls `isOpen` by updating it from the `onCancel` and `onConfirm` callbacks.
 *
 *```jsx
 * import { __experimentalConfirmDialog as ConfirmDialog } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * function Example() {
 * 	const [ isOpen, setIsOpen ] = useState( true );
 *
 * 	const handleConfirm = () => {
 * 		console.debug( 'Confirmed!' );
 * 		setIsOpen( false );
 * 	};
 *
 * 	const handleCancel = () => {
 * 		console.debug( 'Cancelled!' );
 * 		setIsOpen( false );
 * 	};
 *
 * 	return (
 * 		<ConfirmDialog
 * 			isOpen={ isOpen }
 * 			onConfirm={ handleConfirm }
 * 			onCancel={ handleCancel }
 * 		>
 * 			Are you sure? <strong>This action cannot be undone!</strong>
 * 		</ConfirmDialog>
 * 	);
 * }
 * ```
 */
const ConfirmDialog = (0, _context.contextConnect)(UnconnectedConfirmDialog, 'ConfirmDialog');
exports.ConfirmDialog = ConfirmDialog;
var _default = ConfirmDialog;
exports.default = _default;
//# sourceMappingURL=component.js.map