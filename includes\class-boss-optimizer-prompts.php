<?php
/**
 * Gestionnaire de prompts IA optimisés pour Boss SEO
 * Prompts directs et factuels sans réponses conversationnelles
 *
 * @package Boss_SEO
 * @subpackage Boss_SEO/includes
 * @since 1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe pour gérer les prompts IA optimisés
 */
class Boss_Optimizer_Prompts {

    /**
     * Génère un prompt pour les mots-clés (format direct)
     *
     * @param string $topic Sujet/thématique
     * @param int $count Nombre de mots-clés souhaités
     * @return string Prompt optimisé
     */
    public static function get_keywords_prompt( $topic, $count = 5 ) {
        return sprintf(
            'Donne-moi exactement %d mots-clés pertinents, séparés par des virgules, sur la thématique suivante : %s. Ne réponds par aucun autre message ou explication, uniquement les mots-clés.',
            $count,
            $topic
        );
    }

    /**
     * Génère un prompt pour le titre SEO (format direct)
     *
     * @param string $content Contenu de base
     * @param string $keyword Mot-clé principal
     * @param int $max_length Longueur maximale
     * @return string Prompt optimisé
     */
    public static function get_title_prompt( $content, $keyword, $max_length = 60 ) {
        return sprintf(
            'Génère un titre SEO optimisé de maximum %d caractères incluant le mot-clé "%s" pour ce contenu : %s. Réponds uniquement avec le titre, sans explication.',
            $max_length,
            $keyword,
            self::truncate_content( $content, 500 )
        );
    }

    /**
     * Génère un prompt pour la meta description (format direct)
     *
     * @param string $content Contenu de base
     * @param string $keyword Mot-clé principal
     * @param int $max_length Longueur maximale
     * @return string Prompt optimisé
     */
    public static function get_meta_description_prompt( $content, $keyword, $max_length = 160 ) {
        return sprintf(
            'Génère une meta description SEO de maximum %d caractères incluant le mot-clé "%s" pour ce contenu : %s. Réponds uniquement avec la description, sans explication.',
            $max_length,
            $keyword,
            self::truncate_content( $content, 500 )
        );
    }

    /**
     * Génère un prompt pour les mots-clés secondaires (format direct)
     *
     * @param string $content Contenu de base
     * @param string $main_keyword Mot-clé principal
     * @param int $count Nombre de mots-clés secondaires
     * @return string Prompt optimisé
     */
    public static function get_secondary_keywords_prompt( $content, $main_keyword, $count = 3 ) {
        return sprintf(
            'Génère exactement %d mots-clés secondaires pertinents (différents de "%s"), séparés par des virgules, pour ce contenu : %s. Réponds uniquement avec les mots-clés, sans explication.',
            $count,
            $main_keyword,
            self::truncate_content( $content, 500 )
        );
    }

    /**
     * Génère un prompt pour l'optimisation complète (format JSON)
     *
     * @param string $content Contenu de base
     * @param string $keyword Mot-clé principal (optionnel)
     * @return string Prompt optimisé
     */
    public static function get_complete_optimization_prompt( $content, $keyword = '' ) {
        $keyword_instruction = $keyword ? sprintf( ' incluant le mot-clé "%s"', $keyword ) : '';
        
        return sprintf(
            'Optimise ce contenu pour le SEO%s. Réponds uniquement en JSON avec cette structure exacte :
{
  "title": "titre optimisé (max 60 caractères)",
  "meta_description": "description optimisée (max 160 caractères)",
  "focus_keyword": "mot-clé principal",
  "secondary_keywords": "mot-clé1,mot-clé2,mot-clé3"
}

Contenu à optimiser : %s',
            $keyword_instruction,
            self::truncate_content( $content, 1000 )
        );
    }

    /**
     * Génère un prompt pour l'analyse de contenu (format JSON)
     *
     * @param string $content Contenu à analyser
     * @return string Prompt optimisé
     */
    public static function get_content_analysis_prompt( $content ) {
        return sprintf(
            'Analyse ce contenu SEO et réponds uniquement en JSON avec cette structure exacte :
{
  "score": 85,
  "issues": ["problème1", "problème2"],
  "suggestions": ["suggestion1", "suggestion2"],
  "keyword_density": 2.5,
  "readability": "good"
}

Contenu à analyser : %s',
            self::truncate_content( $content, 1000 )
        );
    }

    /**
     * Génère un prompt pour les suggestions d'amélioration (format liste)
     *
     * @param string $content Contenu à améliorer
     * @param array $current_seo Données SEO actuelles
     * @return string Prompt optimisé
     */
    public static function get_improvement_suggestions_prompt( $content, $current_seo = array() ) {
        $seo_info = '';
        if ( ! empty( $current_seo ) ) {
            $seo_info = sprintf(
                ' Données SEO actuelles - Titre: "%s", Description: "%s", Mot-clé: "%s".',
                $current_seo['title'] ?? '',
                $current_seo['description'] ?? '',
                $current_seo['keyword'] ?? ''
            );
        }

        return sprintf(
            'Liste 5 suggestions concrètes pour améliorer le SEO de ce contenu.%s Réponds avec une liste numérotée simple, sans explication supplémentaire.

Contenu : %s',
            $seo_info,
            self::truncate_content( $content, 800 )
        );
    }

    /**
     * Génère un prompt pour les titres alternatifs (format liste)
     *
     * @param string $content Contenu de base
     * @param string $keyword Mot-clé principal
     * @param int $count Nombre d'alternatives
     * @return string Prompt optimisé
     */
    public static function get_alternative_titles_prompt( $content, $keyword, $count = 3 ) {
        return sprintf(
            'Génère %d titres SEO alternatifs (max 60 caractères chacun) incluant le mot-clé "%s" pour ce contenu : %s. Réponds avec une liste numérotée simple, sans explication.',
            $count,
            $keyword,
            self::truncate_content( $content, 500 )
        );
    }

    /**
     * Génère un prompt pour les descriptions alternatives (format liste)
     *
     * @param string $content Contenu de base
     * @param string $keyword Mot-clé principal
     * @param int $count Nombre d'alternatives
     * @return string Prompt optimisé
     */
    public static function get_alternative_descriptions_prompt( $content, $keyword, $count = 3 ) {
        return sprintf(
            'Génère %d meta descriptions SEO alternatives (max 160 caractères chacune) incluant le mot-clé "%s" pour ce contenu : %s. Réponds avec une liste numérotée simple, sans explication.',
            $count,
            $keyword,
            self::truncate_content( $content, 500 )
        );
    }

    /**
     * Génère un prompt pour l'optimisation d'images (format JSON)
     *
     * @param string $content Contenu de base
     * @param array $images Liste des images
     * @return string Prompt optimisé
     */
    public static function get_image_optimization_prompt( $content, $images = array() ) {
        $images_info = '';
        if ( ! empty( $images ) ) {
            $images_info = ' Images actuelles : ' . implode( ', ', array_slice( $images, 0, 5 ) );
        }

        return sprintf(
            'Optimise les balises alt et titres d\'images pour ce contenu.%s Réponds uniquement en JSON avec cette structure :
{
  "suggestions": [
    {"filename": "image1.jpg", "alt": "texte alt optimisé", "title": "titre optimisé"},
    {"filename": "image2.jpg", "alt": "texte alt optimisé", "title": "titre optimisé"}
  ]
}

Contenu : %s',
            $images_info,
            self::truncate_content( $content, 600 )
        );
    }

    /**
     * Tronque le contenu pour les prompts
     *
     * @param string $content Contenu à tronquer
     * @param int $max_length Longueur maximale
     * @return string Contenu tronqué
     */
    private static function truncate_content( $content, $max_length = 500 ) {
        // Nettoyer le contenu HTML
        $content = wp_strip_all_tags( $content );
        
        // Supprimer les espaces multiples
        $content = preg_replace( '/\s+/', ' ', $content );
        
        // Tronquer si nécessaire
        if ( strlen( $content ) > $max_length ) {
            $content = substr( $content, 0, $max_length ) . '...';
        }
        
        return trim( $content );
    }

    /**
     * Parse une réponse IA pour extraire les données JSON
     *
     * @param string $response Réponse de l'IA
     * @return array|false Données parsées ou false
     */
    public static function parse_json_response( $response ) {
        // Nettoyer la réponse
        $response = trim( $response );
        
        // Chercher le JSON dans la réponse
        if ( preg_match( '/\{.*\}/s', $response, $matches ) ) {
            $json_data = json_decode( $matches[0], true );
            if ( json_last_error() === JSON_ERROR_NONE ) {
                return $json_data;
            }
        }
        
        return false;
    }

    /**
     * Parse une réponse IA pour extraire une liste
     *
     * @param string $response Réponse de l'IA
     * @return array Liste extraite
     */
    public static function parse_list_response( $response ) {
        $items = array();
        
        // Diviser par lignes
        $lines = explode( "\n", trim( $response ) );
        
        foreach ( $lines as $line ) {
            $line = trim( $line );
            
            // Supprimer les numéros de liste
            $line = preg_replace( '/^\d+[\.\)]\s*/', '', $line );
            
            // Supprimer les puces
            $line = preg_replace( '/^[-\*\•]\s*/', '', $line );
            
            if ( ! empty( $line ) ) {
                $items[] = $line;
            }
        }
        
        return $items;
    }

    /**
     * Parse une réponse IA pour extraire des mots-clés séparés par des virgules
     *
     * @param string $response Réponse de l'IA
     * @return array Mots-clés extraits
     */
    public static function parse_keywords_response( $response ) {
        // Nettoyer la réponse
        $response = trim( $response );
        
        // Supprimer les guillemets et autres caractères indésirables
        $response = preg_replace( '/^["\'\`]|["\'\`]$/', '', $response );
        
        // Diviser par virgules
        $keywords = explode( ',', $response );
        
        // Nettoyer chaque mot-clé
        $keywords = array_map( 'trim', $keywords );
        $keywords = array_filter( $keywords, function( $keyword ) {
            return ! empty( $keyword ) && strlen( $keyword ) > 1;
        } );
        
        return array_values( $keywords );
    }
}
