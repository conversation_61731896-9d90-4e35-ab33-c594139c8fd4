{"version": 3, "names": ["_warning", "_interopRequireDefault", "require", "_contextSystemProvider", "_utils", "_getStyledClassNameFromKey", "_useCx", "useContextSystem", "props", "namespace", "contextSystemProps", "useComponentsContext", "SCRIPT_DEBUG", "warn", "contextProps", "finalComponentProps", "getConnectedNamespace", "getNamespace", "_overrides", "overrideProps", "otherContextProps", "initialMergedProps", "Object", "entries", "length", "assign", "cx", "useCx", "classes", "getStyledClassNameFromKey", "className", "rendered", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "key", "undefined"], "sources": ["@wordpress/components/src/context/use-context-system.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport warn from '@wordpress/warning';\n\n/**\n * Internal dependencies\n */\nimport { useComponentsContext } from './context-system-provider';\nimport { getNamespace, getConnectedNamespace } from './utils';\nimport { getStyledClassNameFromKey } from './get-styled-class-name-from-key';\nimport { useCx } from '../utils/hooks/use-cx';\n\n/**\n * @template TProps\n * @typedef {TProps & { className: string }} ConnectedProps\n */\n\n/**\n * Custom hook that derives registered props from the Context system.\n * These derived props are then consolidated with incoming component props.\n *\n * @template {{ className?: string }} P\n * @param {P}      props     Incoming props from the component.\n * @param {string} namespace The namespace to register and to derive context props from.\n * @return {ConnectedProps<P>} The connected props.\n */\nexport function useContextSystem( props, namespace ) {\n\tconst contextSystemProps = useComponentsContext();\n\tif ( typeof namespace === 'undefined' ) {\n\t\twarn( 'useContextSystem: Please provide a namespace' );\n\t}\n\n\tconst contextProps = contextSystemProps?.[ namespace ] || {};\n\n\t/* eslint-disable jsdoc/no-undefined-types */\n\t/** @type {ConnectedProps<P>} */\n\t// @ts-ignore We fill in the missing properties below\n\tconst finalComponentProps = {\n\t\t...getConnectedNamespace(),\n\t\t...getNamespace( namespace ),\n\t};\n\t/* eslint-enable jsdoc/no-undefined-types */\n\n\tconst { _overrides: overrideProps, ...otherContextProps } = contextProps;\n\n\tconst initialMergedProps = Object.entries( otherContextProps ).length\n\t\t? Object.assign( {}, otherContextProps, props )\n\t\t: props;\n\n\tconst cx = useCx();\n\n\tconst classes = cx(\n\t\tgetStyledClassNameFromKey( namespace ),\n\t\tprops.className\n\t);\n\n\t// Provides the ability to customize the render of the component.\n\tconst rendered =\n\t\ttypeof initialMergedProps.renderChildren === 'function'\n\t\t\t? initialMergedProps.renderChildren( initialMergedProps )\n\t\t\t: initialMergedProps.children;\n\n\tfor ( const key in initialMergedProps ) {\n\t\t// @ts-ignore filling in missing props\n\t\tfinalComponentProps[ key ] = initialMergedProps[ key ];\n\t}\n\n\tfor ( const key in overrideProps ) {\n\t\t// @ts-ignore filling in missing props\n\t\tfinalComponentProps[ key ] = overrideProps[ key ];\n\t}\n\n\t// Setting an `undefined` explicitly can cause unintended overwrites\n\t// when a `cloneElement()` is involved.\n\tif ( rendered !== undefined ) {\n\t\t// @ts-ignore\n\t\tfinalComponentProps.children = rendered;\n\t}\n\n\tfinalComponentProps.className = classes;\n\n\treturn finalComponentProps;\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,0BAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAMA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,gBAAgBA,CAAEC,KAAK,EAAEC,SAAS,EAAG;EACpD,MAAMC,kBAAkB,GAAG,IAAAC,2CAAoB,EAAC,CAAC;EACjD,IAAK,OAAOF,SAAS,KAAK,WAAW,EAAG;IACvC,OAAAG,YAAA,oBAAAA,YAAA,gBAAAC,gBAAI,EAAE,8CAA+C,CAAC;EACvD;EAEA,MAAMC,YAAY,GAAGJ,kBAAkB,GAAID,SAAS,CAAE,IAAI,CAAC,CAAC;;EAE5D;EACA;EACA;EACA,MAAMM,mBAAmB,GAAG;IAC3B,GAAG,IAAAC,4BAAqB,EAAC,CAAC;IAC1B,GAAG,IAAAC,mBAAY,EAAER,SAAU;EAC5B,CAAC;EACD;;EAEA,MAAM;IAAES,UAAU,EAAEC,aAAa;IAAE,GAAGC;EAAkB,CAAC,GAAGN,YAAY;EAExE,MAAMO,kBAAkB,GAAGC,MAAM,CAACC,OAAO,CAAEH,iBAAkB,CAAC,CAACI,MAAM,GAClEF,MAAM,CAACG,MAAM,CAAE,CAAC,CAAC,EAAEL,iBAAiB,EAAEZ,KAAM,CAAC,GAC7CA,KAAK;EAER,MAAMkB,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAGF,EAAE,CACjB,IAAAG,oDAAyB,EAAEpB,SAAU,CAAC,EACtCD,KAAK,CAACsB,SACP,CAAC;;EAED;EACA,MAAMC,QAAQ,GACb,OAAOV,kBAAkB,CAACW,cAAc,KAAK,UAAU,GACpDX,kBAAkB,CAACW,cAAc,CAAEX,kBAAmB,CAAC,GACvDA,kBAAkB,CAACY,QAAQ;EAE/B,KAAM,MAAMC,GAAG,IAAIb,kBAAkB,EAAG;IACvC;IACAN,mBAAmB,CAAEmB,GAAG,CAAE,GAAGb,kBAAkB,CAAEa,GAAG,CAAE;EACvD;EAEA,KAAM,MAAMA,GAAG,IAAIf,aAAa,EAAG;IAClC;IACAJ,mBAAmB,CAAEmB,GAAG,CAAE,GAAGf,aAAa,CAAEe,GAAG,CAAE;EAClD;;EAEA;EACA;EACA,IAAKH,QAAQ,KAAKI,SAAS,EAAG;IAC7B;IACApB,mBAAmB,CAACkB,QAAQ,GAAGF,QAAQ;EACxC;EAEAhB,mBAAmB,CAACe,SAAS,GAAGF,OAAO;EAEvC,OAAOb,mBAAmB;AAC3B"}