"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _compose = require("@wordpress/compose");
var _icons = require("@wordpress/icons");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function Stepper({
  getStylesFromColorScheme,
  isMaxValue,
  isMinValue,
  onPressInDecrement,
  onPressInIncrement,
  onPressOut,
  value,
  children,
  shouldDisplayTextInput
}) {
  const valueStyle = getStylesFromColorScheme(_style.default.value, _style.default.valueTextDark);
  const buttonStyle = getStylesFromColorScheme(_style.default.button, _style.default.buttonDark);
  return (0, _react.createElement)(_reactNative.View, {
    style: _style.default.container
  }, !shouldDisplayTextInput && (0, _react.createElement)(_reactNative.Text, {
    style: valueStyle
  }, value), children, (0, _react.createElement)(_reactNative.TouchableOpacity, {
    disabled: isMinValue,
    onPressIn: onPressInDecrement,
    onPressOut: onPressOut,
    style: [buttonStyle, isMinValue ? {
      opacity: 0.4
    } : null]
  }, (0, _react.createElement)(_icons.Icon, {
    icon: _icons.reset,
    size: 24,
    color: buttonStyle.color
  })), (0, _react.createElement)(_reactNative.TouchableOpacity, {
    testID: 'Increment',
    disabled: isMaxValue,
    onPressIn: onPressInIncrement,
    onPressOut: onPressOut,
    style: [buttonStyle, isMaxValue ? {
      opacity: 0.4
    } : null]
  }, (0, _react.createElement)(_icons.Icon, {
    icon: _icons.plus,
    size: 24,
    color: buttonStyle.color
  })));
}
var _default = (0, _compose.withPreferredColorScheme)(Stepper);
exports.default = _default;
//# sourceMappingURL=stepper.ios.js.map