"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _toggleGroupControl = require("../toggle-group-control");
var _constants = require("./constants");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const FontSizePickerToggleGroup = props => {
  const {
    fontSizes,
    value,
    __nextHasNoMarginBottom,
    __next40pxDefaultSize,
    size,
    onChange
  } = props;
  return (0, _react.createElement)(_toggleGroupControl.ToggleGroupControl, {
    __nextHasNoMarginBottom: __nextHasNoMarginBottom,
    __next40pxDefaultSize: __next40pxDefaultSize,
    label: (0, _i18n.__)('Font size'),
    hideLabelFromVision: true,
    value: value,
    onChange: onChange,
    isBlock: true,
    size: size
  }, fontSizes.map((fontSize, index) => (0, _react.createElement)(_toggleGroupControl.ToggleGroupControlOption, {
    key: fontSize.slug,
    value: fontSize.size,
    label: _constants.T_SHIRT_ABBREVIATIONS[index],
    "aria-label": fontSize.name || _constants.T_SHIRT_NAMES[index],
    showTooltip: true
  })));
};
var _default = FontSizePickerToggleGroup;
exports.default = _default;
//# sourceMappingURL=font-size-picker-toggle-group.js.map