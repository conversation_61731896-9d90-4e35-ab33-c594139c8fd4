{"version": 3, "names": ["_react", "require", "_element", "_context", "styles", "_interopRequireWildcard", "_utils", "_useCx", "_values", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "getBoxShadow", "value", "boxShadowColor", "boxShadow", "useElevation", "props", "active", "borderRadius", "className", "focus", "hover", "isInteractive", "offset", "otherProps", "useContextSystem", "cx", "useCx", "classes", "useMemo", "hoverValue", "isValueDefined", "activeValue", "undefined", "transition", "CONFIG", "transitionDuration", "transitionTimingFunction", "sx", "Base", "css", "bottom", "opacity", "elevationIntensity", "left", "right", "top", "reduceMotion", "process", "env", "NODE_ENV", "Elevation"], "sources": ["@wordpress/components/src/elevation/hook.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { SerializedStyles } from '@emotion/react';\nimport { css } from '@emotion/react';\n\n/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { useContextSystem } from '../context';\nimport * as styles from './styles';\nimport { CONFIG, reduceMotion } from '../utils';\nimport { useCx } from '../utils/hooks/use-cx';\nimport { isValueDefined } from '../utils/values';\nimport type { ElevationProps } from './types';\n\nexport function getBoxShadow( value: number ) {\n\tconst boxShadowColor = `rgba(0, 0, 0, ${ value / 20 })`;\n\tconst boxShadow = `0 ${ value }px ${ value * 2 }px 0\n\t${ boxShadowColor }`;\n\n\treturn boxShadow;\n}\n\nexport function useElevation(\n\tprops: WordPressComponentProps< ElevationProps, 'div' >\n) {\n\tconst {\n\t\tactive,\n\t\tborderRadius = 'inherit',\n\t\tclassName,\n\t\tfocus,\n\t\thover,\n\t\tisInteractive = false,\n\t\toffset = 0,\n\t\tvalue = 0,\n\t\t...otherProps\n\t} = useContextSystem( props, 'Elevation' );\n\n\tconst cx = useCx();\n\n\tconst classes = useMemo( () => {\n\t\tlet hoverValue: number | undefined = isValueDefined( hover )\n\t\t\t? hover\n\t\t\t: value * 2;\n\t\tlet activeValue: number | undefined = isValueDefined( active )\n\t\t\t? active\n\t\t\t: value / 2;\n\n\t\tif ( ! isInteractive ) {\n\t\t\thoverValue = isValueDefined( hover ) ? hover : undefined;\n\t\t\tactiveValue = isValueDefined( active ) ? active : undefined;\n\t\t}\n\n\t\tconst transition = `box-shadow ${ CONFIG.transitionDuration } ${ CONFIG.transitionTimingFunction }`;\n\n\t\tconst sx: {\n\t\t\tBase?: SerializedStyles;\n\t\t\thover?: SerializedStyles;\n\t\t\tactive?: SerializedStyles;\n\t\t\tfocus?: SerializedStyles;\n\t\t} = {};\n\n\t\tsx.Base = css(\n\t\t\t{\n\t\t\t\tborderRadius,\n\t\t\t\tbottom: offset,\n\t\t\t\tboxShadow: getBoxShadow( value ),\n\t\t\t\topacity: CONFIG.elevationIntensity,\n\t\t\t\tleft: offset,\n\t\t\t\tright: offset,\n\t\t\t\ttop: offset,\n\t\t\t\ttransition,\n\t\t\t},\n\t\t\treduceMotion( 'transition' )\n\t\t);\n\n\t\tif ( isValueDefined( hoverValue ) ) {\n\t\t\tsx.hover = css`\n\t\t\t\t*:hover > & {\n\t\t\t\t\tbox-shadow: ${ getBoxShadow( hoverValue ) };\n\t\t\t\t}\n\t\t\t`;\n\t\t}\n\n\t\tif ( isValueDefined( activeValue ) ) {\n\t\t\tsx.active = css`\n\t\t\t\t*:active > & {\n\t\t\t\t\tbox-shadow: ${ getBoxShadow( activeValue ) };\n\t\t\t\t}\n\t\t\t`;\n\t\t}\n\n\t\tif ( isValueDefined( focus ) ) {\n\t\t\tsx.focus = css`\n\t\t\t\t*:focus > & {\n\t\t\t\t\tbox-shadow: ${ getBoxShadow( focus ) };\n\t\t\t\t}\n\t\t\t`;\n\t\t}\n\n\t\treturn cx(\n\t\t\tstyles.Elevation,\n\t\t\tsx.Base,\n\t\t\tsx.hover,\n\t\t\tsx.focus,\n\t\t\tsx.active,\n\t\t\tclassName\n\t\t);\n\t}, [\n\t\tactive,\n\t\tborderRadius,\n\t\tclassName,\n\t\tcx,\n\t\tfocus,\n\t\thover,\n\t\tisInteractive,\n\t\toffset,\n\t\tvalue,\n\t] );\n\n\treturn { ...otherProps, className: classes, 'aria-hidden': true };\n}\n"], "mappings": ";;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAMA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AAAiD,SAAAQ,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAnBjD;AACA;AACA;;AAIA;AACA;AACA;;AAcO,SAASW,YAAYA,CAAEC,KAAa,EAAG;EAC7C,MAAMC,cAAc,GAAI,iBAAiBD,KAAK,GAAG,EAAI,GAAE;EACvD,MAAME,SAAS,GAAI,KAAKF,KAAO,MAAMA,KAAK,GAAG,CAAG;AACjD,GAAIC,cAAgB,EAAC;EAEpB,OAAOC,SAAS;AACjB;AAEO,SAASC,YAAYA,CAC3BC,KAAuD,EACtD;EACD,MAAM;IACLC,MAAM;IACNC,YAAY,GAAG,SAAS;IACxBC,SAAS;IACTC,KAAK;IACLC,KAAK;IACLC,aAAa,GAAG,KAAK;IACrBC,MAAM,GAAG,CAAC;IACVX,KAAK,GAAG,CAAC;IACT,GAAGY;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAET,KAAK,EAAE,WAAY,CAAC;EAE1C,MAAMU,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,IAAIC,UAA8B,GAAG,IAAAC,sBAAc,EAAEV,KAAM,CAAC,GACzDA,KAAK,GACLT,KAAK,GAAG,CAAC;IACZ,IAAIoB,WAA+B,GAAG,IAAAD,sBAAc,EAAEd,MAAO,CAAC,GAC3DA,MAAM,GACNL,KAAK,GAAG,CAAC;IAEZ,IAAK,CAAEU,aAAa,EAAG;MACtBQ,UAAU,GAAG,IAAAC,sBAAc,EAAEV,KAAM,CAAC,GAAGA,KAAK,GAAGY,SAAS;MACxDD,WAAW,GAAG,IAAAD,sBAAc,EAAEd,MAAO,CAAC,GAAGA,MAAM,GAAGgB,SAAS;IAC5D;IAEA,MAAMC,UAAU,GAAI,cAAcC,aAAM,CAACC,kBAAoB,IAAID,aAAM,CAACE,wBAA0B,EAAC;IAEnG,MAAMC,EAKL,GAAG,CAAC,CAAC;IAENA,EAAE,CAACC,IAAI,gBAAG,IAAAC,UAAG,EACZ;MACCtB,YAAY;MACZuB,MAAM,EAAElB,MAAM;MACdT,SAAS,EAAEH,YAAY,CAAEC,KAAM,CAAC;MAChC8B,OAAO,EAAEP,aAAM,CAACQ,kBAAkB;MAClCC,IAAI,EAAErB,MAAM;MACZsB,KAAK,EAAEtB,MAAM;MACbuB,GAAG,EAAEvB,MAAM;MACXW;IACD,CAAC,EACD,IAAAa,mBAAY,EAAE,YAAa,CAAC,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,4CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,kxIAC7B,CAAC;IAED,IAAK,IAAAnB,sBAAc,EAAED,UAAW,CAAC,EAAG;MACnCQ,EAAE,CAACjB,KAAK,oBAAGmB,UAAG,2BAEG7B,YAAY,CAAEmB,UAAW,CAAC,UAAAkB,OAAA,CAAAC,GAAA,CAAAC,QAAA,8CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mxIAE1C;IACF;IAEA,IAAK,IAAAnB,sBAAc,EAAEC,WAAY,CAAC,EAAG;MACpCM,EAAE,CAACrB,MAAM,oBAAGuB,UAAG,4BAEE7B,YAAY,CAAEqB,WAAY,CAAC,UAAAgB,OAAA,CAAAC,GAAA,CAAAC,QAAA,+CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mxIAE3C;IACF;IAEA,IAAK,IAAAnB,sBAAc,EAAEX,KAAM,CAAC,EAAG;MAC9BkB,EAAE,CAAClB,KAAK,oBAAGoB,UAAG,2BAEG7B,YAAY,CAAES,KAAM,CAAC,UAAA4B,OAAA,CAAAC,GAAA,CAAAC,QAAA,8CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mxIAErC;IACF;IAEA,OAAOxB,EAAE,CACR1C,MAAM,CAACmE,SAAS,EAChBb,EAAE,CAACC,IAAI,EACPD,EAAE,CAACjB,KAAK,EACRiB,EAAE,CAAClB,KAAK,EACRkB,EAAE,CAACrB,MAAM,EACTE,SACD,CAAC;EACF,CAAC,EAAE,CACFF,MAAM,EACNC,YAAY,EACZC,SAAS,EACTO,EAAE,EACFN,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,MAAM,EACNX,KAAK,CACJ,CAAC;EAEH,OAAO;IAAE,GAAGY,UAAU;IAAEL,SAAS,EAAES,OAAO;IAAE,aAAa,EAAE;EAAK,CAAC;AAClE"}