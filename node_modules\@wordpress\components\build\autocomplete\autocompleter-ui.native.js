"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getAutoCompleterUI = getAutoCompleterUI;
var _react = require("react");
var _reactNative = require("react-native");
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var _components = require("@wordpress/components");
var _compose = require("@wordpress/compose");
var _backgroundView = _interopRequireDefault(require("./background-view"));
var _getDefaultUseItems = _interopRequireDefault(require("./get-default-use-items"));
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const {
  compose: stylesCompose
} = _reactNative.StyleSheet;
function getAutoCompleterUI(autocompleter) {
  const useItems = autocompleter.useItems ? autocompleter.useItems : (0, _getDefaultUseItems.default)(autocompleter);
  function AutocompleterUI({
    filterValue,
    selectedIndex,
    onChangeOptions,
    onSelect,
    value,
    reset
  }) {
    const [items] = useItems(filterValue);
    const filteredItems = items.filter(item => !item.isDisabled);
    const scrollViewRef = (0, _element.useRef)();
    const animationValue = (0, _element.useRef)(new _reactNative.Animated.Value(0)).current;
    const [isVisible, setIsVisible] = (0, _element.useState)(false);
    const {
      text
    } = value;
    (0, _element.useEffect)(() => {
      if (!isVisible && text.length > 0) {
        setIsVisible(true);
      }
    }, [isVisible, text]);
    (0, _element.useLayoutEffect)(() => {
      onChangeOptions(items);
      scrollViewRef.current?.scrollTo({
        x: 0,
        animated: false
      });
      if (isVisible && text.length > 0) {
        startAnimation(true);
      } else if (isVisible && text.length === 0) {
        startAnimation(false);
      }
      // Temporarily disabling exhaustive-deps to avoid introducing unexpected side effecst.
      // See https://github.com/WordPress/gutenberg/pull/41820
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [items, isVisible, text]);
    const activeItemStyles = (0, _compose.usePreferredColorSchemeStyle)(_style.default['components-autocomplete__item-active'], _style.default['components-autocomplete__item-active-dark']);
    const iconStyles = (0, _compose.usePreferredColorSchemeStyle)(_style.default['components-autocomplete__icon'], _style.default['components-autocomplete__icon-active-dark']);
    const activeIconStyles = (0, _compose.usePreferredColorSchemeStyle)(_style.default['components-autocomplete__icon-active '], _style.default['components-autocomplete__icon-active-dark']);
    const textStyles = (0, _compose.usePreferredColorSchemeStyle)(_style.default['components-autocomplete__text'], _style.default['components-autocomplete__text-dark']);
    const activeTextStyles = (0, _compose.usePreferredColorSchemeStyle)(_style.default['components-autocomplete__text-active'], _style.default['components-autocomplete__text-active-dark']);
    const startAnimation = (0, _element.useCallback)(show => {
      _reactNative.Animated.timing(animationValue, {
        toValue: show ? 1 : 0,
        duration: show ? 200 : 100,
        useNativeDriver: true
      }).start(({
        finished
      }) => {
        if (finished && !show && isVisible) {
          setIsVisible(false);
          reset();
        }
      });
    },
    // Temporarily disabling exhaustive-deps to avoid introducing unexpected side effecst.
    // See https://github.com/WordPress/gutenberg/pull/41820
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isVisible]);
    const contentStyles = {
      transform: [{
        translateY: animationValue.interpolate({
          inputRange: [0, 1],
          outputRange: [_style.default['components-autocomplete'].height, 0]
        })
      }]
    };
    if (!filteredItems.length > 0 || !isVisible) {
      return null;
    }
    return (0, _react.createElement)(_components.__unstableAutocompletionItemsFill, null, (0, _react.createElement)(_reactNative.View, {
      style: _style.default['components-autocomplete']
    }, (0, _react.createElement)(_reactNative.Animated.View, {
      style: contentStyles
    }, (0, _react.createElement)(_backgroundView.default, null, (0, _react.createElement)(_reactNative.ScrollView, {
      testID: "autocompleter",
      ref: scrollViewRef,
      horizontal: true,
      contentContainerStyle: _style.default['components-autocomplete__content'],
      showsHorizontalScrollIndicator: false,
      keyboardShouldPersistTaps: "always",
      accessibilityLabel:
      // translators: Slash inserter autocomplete results
      (0, _i18n.__)('Slash inserter results')
    }, filteredItems.map((option, index) => {
      const isActive = index === selectedIndex;
      const itemStyle = stylesCompose(_style.default['components-autocomplete__item'], isActive && activeItemStyles);
      const textStyle = stylesCompose(textStyles, isActive && activeTextStyles);
      const iconStyle = stylesCompose(iconStyles, isActive && activeIconStyles);
      const iconSource = option?.value?.icon?.src || option?.value?.icon;
      return (0, _react.createElement)(_reactNative.TouchableOpacity, {
        activeOpacity: 0.5,
        style: itemStyle,
        key: index,
        onPress: () => onSelect(option),
        accessibilityLabel: (0, _i18n.sprintf)(
        // translators: %s: Block name e.g. "Image block"
        (0, _i18n.__)('%s block'), option?.value?.title)
      }, (0, _react.createElement)(_reactNative.View, {
        style: _style.default['components-autocomplete__icon']
      }, (0, _react.createElement)(_components.Icon, {
        icon: iconSource,
        size: 24,
        style: iconStyle
      })), (0, _react.createElement)(_reactNative.Text, {
        style: textStyle
      }, option?.value?.title));
    }))))));
  }
  return AutocompleterUI;
}
var _default = getAutoCompleterUI;
exports.default = _default;
//# sourceMappingURL=autocompleter-ui.native.js.map