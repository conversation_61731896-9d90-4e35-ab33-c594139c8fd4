"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useBaseControlProps = useBaseControlProps;
var _compose = require("@wordpress/compose");
var _ = _interopRequireDefault(require("."));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * Generate props for the `BaseControl` and the inner control itself.
 *
 * Namely, it takes care of generating a unique `id`, properly associating it with the `label` and `help` elements.
 *
 * @param props
 */
function useBaseControlProps(props) {
  const {
    help,
    id: preferredId,
    ...restProps
  } = props;
  const uniqueId = (0, _compose.useInstanceId)(_.default, 'wp-components-base-control', preferredId);

  // ARIA descriptions can only contain plain text, so fall back to aria-details if not.
  const helpPropName = typeof help === 'string' ? 'aria-describedby' : 'aria-details';
  return {
    baseControlProps: {
      id: uniqueId,
      help,
      ...restProps
    },
    controlProps: {
      id: uniqueId,
      ...(!!help ? {
        [helpPropName]: `${uniqueId}__help`
      } : {})
    }
  };
}
//# sourceMappingURL=hooks.js.map