{"version": 3, "names": [], "sources": ["@wordpress/components/src/color-palette/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties, ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { DropdownProps } from '../dropdown/types';\nimport type { HeadingSize } from '../heading/types';\n\nexport type ColorObject = {\n\tname: string;\n\tcolor: NonNullable< CSSProperties[ 'color' ] >;\n};\n\nexport type PaletteObject = {\n\tname: string;\n\tcolors: ColorObject[];\n};\n\ntype PaletteProps = {\n\tclassName?: string;\n\tclearColor: () => void;\n\t/**\n\t * Callback called when a color is selected.\n\t */\n\tonChange: ( newColor?: string, index?: number ) => void;\n\tvalue?: string;\n\tactions?: ReactNode;\n\theadingLevel?: HeadingSize;\n};\n\nexport type SinglePaletteProps = PaletteProps & {\n\tcolors: ColorObject[];\n};\n\nexport type MultiplePalettesProps = PaletteProps & {\n\tcolors: PaletteObject[];\n};\n\nexport type CustomColorPickerDropdownProps = DropdownProps & {\n\tisRenderedInSidebar: boolean;\n};\n\nexport type ColorPaletteProps = Pick< PaletteProps, 'onChange' > & {\n\t/**\n\t * Whether the palette should have a clearing button.\n\t *\n\t * @default true\n\t */\n\tclearable?: boolean;\n\t/**\n\t * Array with the colors to be shown. When displaying multiple color palettes\n\t * to choose from, the format of the array changes from an array of colors\n\t * objects, to an array of color palettes.\n\t *\n\t * @default []\n\t */\n\tcolors?: PaletteObject[] | ColorObject[];\n\t/**\n\t * Whether to allow the user to pick a custom color on top of the predefined\n\t * choices (defined via the `colors` prop).\n\t *\n\t * @default false\n\t */\n\tdisableCustomColors?: boolean;\n\t/**\n\t * This controls whether the alpha channel will be offered when selecting\n\t * custom colors.\n\t *\n\t * @default false\n\t */\n\tenableAlpha?: boolean;\n\t/**\n\t * The heading level.\n\t *\n\t * @default 2\n\t */\n\theadingLevel?: HeadingSize;\n\t/**\n\t * Currently active value.\n\t */\n\tvalue?: string;\n\t/**\n\t * Whether the control should present as a set of buttons,\n\t * each with its own tab stop.\n\t *\n\t * @default false\n\t */\n\tasButtons?: boolean;\n\t/**\n\t * Prevents keyboard interaction from wrapping around.\n\t * Only used when `asButtons` is not true.\n\t *\n\t * @default true\n\t */\n\tloop?: boolean;\n\t/**\n\t * Whether this is rendered in the sidebar.\n\t *\n\t * @default false\n\t */\n\t__experimentalIsRenderedInSidebar?: boolean;\n} & (\n\t\t| {\n\t\t\t\t/**\n\t\t\t\t * A label to identify the purpose of the control.\n\t\t\t\t *\n\t\t\t\t * @todo [#54055] Either this or `aria-labelledby` should be required\n\t\t\t\t */\n\t\t\t\t'aria-label'?: string;\n\t\t\t\t'aria-labelledby'?: never;\n\t\t  }\n\t\t| {\n\t\t\t\t/**\n\t\t\t\t * An ID of an element to provide a label for the control.\n\t\t\t\t *\n\t\t\t\t * @todo [#54055] Either this or `aria-label` should be required\n\t\t\t\t */\n\t\t\t\t'aria-labelledby'?: string;\n\t\t\t\t'aria-label'?: never;\n\t\t  }\n\t);\n"], "mappings": ""}