/**
 * Breakpoints & Media Queries
 */

// Most used breakpoints
$break-xhuge: 1920px;
$break-huge: 1440px;
$break-wide: 1280px;
$break-xlarge: 1080px;
$break-large: 960px;	// admin sidebar auto folds
$break-medium: 782px;	// adminbar goes big
$break-small: 600px;
$break-mobile: 480px;
$break-zoomed-in: 280px;

// All media queries currently in WordPress:
//
// min-width: 2000px
// min-width: 1680px
// min-width: 1250px
// max-width: 1120px *
// max-width: 1000px
// min-width: 769px and max-width: 1000px
// max-width: 960px *
// max-width: 900px
// max-width: 850px
// min-width: 800px and max-width: 1499px
// max-width: 800px
// max-width: 799px
// max-width: 782px *
// max-width: 768px
// max-width: 640px *
// max-width: 600px *
// max-width: 520px
// max-width: 500px
// max-width: 480px *
// max-width: 400px *
// max-width: 380px
// max-width: 320px *
//
// Those marked * seem to be more commonly used than the others.
// Let's try and use as few of these as possible, and be mindful about adding new ones, so we don't make the situation worse
