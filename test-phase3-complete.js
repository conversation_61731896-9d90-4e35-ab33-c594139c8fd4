/**
 * Script de test complet pour la Phase 3 - Optimisations Avancées
 * À exécuter dans la console du navigateur sur la page d'admin WordPress
 */

console.log('🚀 Test Complet Phase 3 - Optimisations Avancées');
console.log('====================================================');

// Fonction utilitaire pour tester une API
async function testAPI(name, apiCall) {
    console.log(`\n🔍 Test: ${name}`);
    console.log('📡 Appel en cours...');
    
    try {
        const startTime = Date.now();
        const result = await apiCall();
        const endTime = Date.now();
        
        console.log(`✅ Succès (${endTime - startTime}ms):`, result);
        return { success: true, result, duration: endTime - startTime };
    } catch (error) {
        console.log('❌ Erreur:', error);
        return { success: false, error, duration: 0 };
    }
}

// Tests des APIs d'Optimisation Intelligente
async function testSmartOptimizationAPIs() {
    console.log('\n🧠 === TESTS OPTIMISATION INTELLIGENTE ===');
    
    // Test 1: Récupération des priorités intelligentes
    await testAPI('Priorités Intelligentes', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/priorities', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Configuration des algorithmes
    await testAPI('Configuration Algorithmes', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/algorithms', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 3: Calcul des priorités
    await testAPI('Calcul Priorités Intelligentes', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/calculate', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 4: Configuration des algorithmes
    await testAPI('Configuration Algorithmes', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/configure', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                recency: { enabled: true, weight: 0.3 },
                popularity: { enabled: true, weight: 0.25 }
            })
        });
        return await response.json();
    });
}

// Tests des APIs Search Console
async function testSearchConsoleAPIs() {
    console.log('\n🔍 === TESTS SEARCH CONSOLE ===');
    
    // Test 1: Récupération des données Search Console
    await testAPI('Données Search Console', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/search-console/data', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Test de connexion
    await testAPI('Test Connexion Search Console', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/search-console/test', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 3: Soumission de sitemap
    await testAPI('Soumission Sitemap à Search Console', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/search-console/submit/main', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 4: Synchronisation des données
    await testAPI('Synchronisation Search Console', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/search-console/sync', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 5: Configuration Search Console
    await testAPI('Configuration Search Console', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/search-console/configure', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                site_url: window.location.origin,
                enabled: true
            })
        });
        return await response.json();
    });
}

// Tests des APIs Cache Avancé
async function testCacheAPIs() {
    console.log('\n⚡ === TESTS CACHE AVANCÉ ===');
    
    // Test 1: Statistiques du cache
    await testAPI('Statistiques Cache', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/cache/stats', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Configuration du cache
    await testAPI('Configuration Cache', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/cache/configure', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                enabled: true,
                compression: true,
                cache_duration: 3600
            })
        });
        return await response.json();
    });
    
    // Test 3: Préchargement du cache
    await testAPI('Préchargement Cache', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/cache/preload', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 4: Vidage du cache
    await testAPI('Vidage Cache', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/cache/clear', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
}

// Tests des APIs Rapports PDF
async function testPdfReportsAPIs() {
    console.log('\n📋 === TESTS RAPPORTS PDF ===');
    
    // Test 1: Templates disponibles
    await testAPI('Templates Rapports', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/reports/templates', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 2: Historique des rapports
    await testAPI('Historique Rapports', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/reports/history?limit=5', {
            method: 'GET',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        return await response.json();
    });
    
    // Test 3: Génération de rapport
    await testAPI('Génération Rapport PDF', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/reports/generate', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                template_type: 'executive_summary'
            })
        });
        return await response.json();
    });
    
    // Test 4: Configuration des rapports
    await testAPI('Configuration Rapports', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/reports/configure', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                enabled: true,
                auto_generation: true,
                frequency: 'weekly'
            })
        });
        return await response.json();
    });
}

// Test d'intégration complète
async function testCompleteIntegration() {
    console.log('\n🔄 === TEST D\'INTÉGRATION COMPLÈTE ===');
    
    // Scénario complet : Optimisation -> Cache -> Rapport
    console.log('📋 Scénario: Optimisation complète des sitemaps');
    
    // 1. Calculer les priorités intelligentes
    const prioritiesResult = await testAPI('1. Calcul Priorités', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/calculate', {
            method: 'POST',
            headers: { 'X-WP-Nonce': wpApiSettings.nonce }
        });
        return await response.json();
    });
    
    // 2. Précharger le cache
    const cacheResult = await testAPI('2. Préchargement Cache', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/cache/preload', {
            method: 'POST',
            headers: { 'X-WP-Nonce': wpApiSettings.nonce }
        });
        return await response.json();
    });
    
    // 3. Générer un rapport
    const reportResult = await testAPI('3. Génération Rapport', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/reports/generate', {
            method: 'POST',
            headers: { 
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ template_type: 'technical_detailed' })
        });
        return await response.json();
    });
    
    // 4. Synchroniser avec Search Console
    const syncResult = await testAPI('4. Sync Search Console', async () => {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/search-console/sync', {
            method: 'POST',
            headers: { 'X-WP-Nonce': wpApiSettings.nonce }
        });
        return await response.json();
    });
    
    console.log('\n📊 === RÉSULTATS DU SCÉNARIO COMPLET ===');
    console.log(`✅ Priorités calculées: ${prioritiesResult.success ? 'OUI' : 'NON'}`);
    console.log(`✅ Cache préchargé: ${cacheResult.success ? 'OUI' : 'NON'}`);
    console.log(`✅ Rapport généré: ${reportResult.success ? 'OUI' : 'NON'}`);
    console.log(`✅ Search Console sync: ${syncResult.success ? 'OUI' : 'NON'}`);
    
    return {
        priorities: prioritiesResult.success,
        cache: cacheResult.success,
        report: reportResult.success,
        sync: syncResult.success
    };
}

// Test de performance et stress
async function testPerformancePhase3() {
    console.log('\n💪 === TEST DE PERFORMANCE PHASE 3 ===');
    
    const startTime = Date.now();
    const promises = [];
    
    // Lancer 5 appels simultanés sur différentes APIs
    const apis = [
        '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/priorities',
        '/wp-json/boss-seo/v1/robots-sitemap/search-console/data',
        '/wp-json/boss-seo/v1/robots-sitemap/cache/stats',
        '/wp-json/boss-seo/v1/robots-sitemap/reports/templates',
        '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats'
    ];
    
    apis.forEach(api => {
        promises.push(
            fetch(api, {
                headers: { 'X-WP-Nonce': wpApiSettings.nonce }
            })
        );
    });
    
    try {
        const results = await Promise.all(promises);
        const endTime = Date.now();
        
        const successCount = results.filter(r => r.ok).length;
        const avgTime = (endTime - startTime) / apis.length;
        
        console.log(`✅ Test de performance Phase 3 réussi:`);
        console.log(`  📊 ${successCount}/${apis.length} APIs répondent`);
        console.log(`  ⏱️  Temps moyen: ${avgTime.toFixed(2)}ms`);
        console.log(`  🚀 Temps total: ${endTime - startTime}ms`);
        
        return true;
    } catch (error) {
        console.log('❌ Erreur lors du test de performance:', error);
        return false;
    }
}

// Fonction principale pour exécuter tous les tests Phase 3
async function runAllPhase3Tests() {
    console.log('🚀 Démarrage des tests Phase 3...\n');
    
    const startTime = Date.now();
    
    try {
        await testSmartOptimizationAPIs();
        await testSearchConsoleAPIs();
        await testCacheAPIs();
        await testPdfReportsAPIs();
        
        const integrationResults = await testCompleteIntegration();
        await testPerformancePhase3();
        
        const endTime = Date.now();
        
        console.log('\n✅ === RÉSUMÉ COMPLET PHASE 3 ===');
        console.log(`⏱️  Durée totale: ${endTime - startTime}ms`);
        console.log('🎯 Toutes les fonctionnalités Phase 3 testées !');
        
        console.log('\n🏆 === FONCTIONNALITÉS OPÉRATIONNELLES ===');
        console.log('✅ 🧠 Optimisation Intelligente avec IA');
        console.log('✅ 🔍 Intégration Google Search Console');
        console.log('✅ ⚡ Cache Avancé avec Compression');
        console.log('✅ 📋 Rapports PDF Automatiques');
        console.log('✅ 🔄 Intégration Complète des Phases 1-2-3');
        
        console.log('\n📈 === SYSTÈME BOSS SEO COMPLET ===');
        console.log('🎉 TOUTES LES PHASES SONT OPÉRATIONNELLES !');
        console.log('🚀 Prêt pour la production !');
        
    } catch (error) {
        console.log('\n💥 Erreur lors de l\'exécution des tests Phase 3:', error);
    }
}

// Fonctions utilitaires disponibles globalement
window.testPhase3 = {
    runAll: runAllPhase3Tests,
    smartOptimization: testSmartOptimizationAPIs,
    searchConsole: testSearchConsoleAPIs,
    cache: testCacheAPIs,
    reports: testPdfReportsAPIs,
    integration: testCompleteIntegration,
    performance: testPerformancePhase3
};

console.log('\n🛠️ Fonctions de test Phase 3 disponibles:');
console.log('- testPhase3.runAll() - Exécuter tous les tests Phase 3');
console.log('- testPhase3.smartOptimization() - Tester l\'optimisation IA');
console.log('- testPhase3.searchConsole() - Tester Search Console');
console.log('- testPhase3.cache() - Tester le cache avancé');
console.log('- testPhase3.reports() - Tester les rapports PDF');
console.log('- testPhase3.integration() - Test d\'intégration complète');
console.log('- testPhase3.performance() - Test de performance');

console.log('\n🎯 Pour commencer, tapez: testPhase3.runAll()');
