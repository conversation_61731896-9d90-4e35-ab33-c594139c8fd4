{"version": 3, "names": ["_i18n", "require", "_element", "_compose", "_borderBoxControlVisualizer", "_interopRequireDefault", "_borderControl", "_grid", "_context", "_hook", "BorderBoxControlSplitControls", "props", "forwardedRef", "centeredClassName", "colors", "disableCustomColors", "enableAlpha", "enableStyle", "onChange", "popoverPlacement", "popoverOffset", "rightAlignedClassName", "size", "value", "__experimentalIsRenderedInSidebar", "otherProps", "useBorderBoxControlSplitControls", "popoverAnchor", "setPopoverAnchor", "useState", "popoverProps", "useMemo", "placement", "offset", "anchor", "shift", "undefined", "sharedBorderControlProps", "isCompact", "mergedRef", "useMergeRefs", "_react", "createElement", "Grid", "ref", "gap", "default", "BorderControl", "className", "hideLabelFromVision", "label", "__", "newBorder", "__unstablePopoverProps", "top", "left", "right", "bottom", "ConnectedBorderBoxControlSplitControls", "contextConnect", "_default", "exports"], "sources": ["@wordpress/components/src/border-box-control/border-box-control-split-controls/component.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { useMemo, useState } from '@wordpress/element';\nimport { useMergeRefs } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport BorderBoxControlVisualizer from '../border-box-control-visualizer';\nimport { BorderControl } from '../../border-control';\nimport { Grid } from '../../grid';\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useBorderBoxControlSplitControls } from './hook';\n\nimport type { BorderControlProps } from '../../border-control/types';\nimport type { SplitControlsProps } from '../types';\n\nconst BorderBoxControlSplitControls = (\n\tprops: WordPressComponentProps< SplitControlsProps, 'div' >,\n\tforwardedRef: React.ForwardedRef< any >\n) => {\n\tconst {\n\t\tcenteredClassName,\n\t\tcolors,\n\t\tdisableCustomColors,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\tonChange,\n\t\tpopoverPlacement,\n\t\tpopoverOffset,\n\t\trightAlignedClassName,\n\t\tsize = 'default',\n\t\tvalue,\n\t\t__experimentalIsRenderedInSidebar,\n\t\t...otherProps\n\t} = useBorderBoxControlSplitControls( props );\n\n\t// Use internal state instead of a ref to make sure that the component\n\t// re-renders when the popover's anchor updates.\n\tconst [ popoverAnchor, setPopoverAnchor ] = useState< Element | null >(\n\t\tnull\n\t);\n\n\t// Memoize popoverProps to avoid returning a new object every time.\n\tconst popoverProps: BorderControlProps[ '__unstablePopoverProps' ] =\n\t\tuseMemo(\n\t\t\t() =>\n\t\t\t\tpopoverPlacement\n\t\t\t\t\t? {\n\t\t\t\t\t\t\tplacement: popoverPlacement,\n\t\t\t\t\t\t\toffset: popoverOffset,\n\t\t\t\t\t\t\tanchor: popoverAnchor,\n\t\t\t\t\t\t\tshift: true,\n\t\t\t\t\t  }\n\t\t\t\t\t: undefined,\n\t\t\t[ popoverPlacement, popoverOffset, popoverAnchor ]\n\t\t);\n\n\tconst sharedBorderControlProps = {\n\t\tcolors,\n\t\tdisableCustomColors,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\tisCompact: true,\n\t\t__experimentalIsRenderedInSidebar,\n\t\tsize,\n\t};\n\n\tconst mergedRef = useMergeRefs( [ setPopoverAnchor, forwardedRef ] );\n\n\treturn (\n\t\t<Grid { ...otherProps } ref={ mergedRef } gap={ 4 }>\n\t\t\t<BorderBoxControlVisualizer value={ value } size={ size } />\n\t\t\t<BorderControl\n\t\t\t\tclassName={ centeredClassName }\n\t\t\t\thideLabelFromVision={ true }\n\t\t\t\tlabel={ __( 'Top border' ) }\n\t\t\t\tonChange={ ( newBorder ) => onChange( newBorder, 'top' ) }\n\t\t\t\t__unstablePopoverProps={ popoverProps }\n\t\t\t\tvalue={ value?.top }\n\t\t\t\t{ ...sharedBorderControlProps }\n\t\t\t/>\n\t\t\t<BorderControl\n\t\t\t\thideLabelFromVision={ true }\n\t\t\t\tlabel={ __( 'Left border' ) }\n\t\t\t\tonChange={ ( newBorder ) => onChange( newBorder, 'left' ) }\n\t\t\t\t__unstablePopoverProps={ popoverProps }\n\t\t\t\tvalue={ value?.left }\n\t\t\t\t{ ...sharedBorderControlProps }\n\t\t\t/>\n\t\t\t<BorderControl\n\t\t\t\tclassName={ rightAlignedClassName }\n\t\t\t\thideLabelFromVision={ true }\n\t\t\t\tlabel={ __( 'Right border' ) }\n\t\t\t\tonChange={ ( newBorder ) => onChange( newBorder, 'right' ) }\n\t\t\t\t__unstablePopoverProps={ popoverProps }\n\t\t\t\tvalue={ value?.right }\n\t\t\t\t{ ...sharedBorderControlProps }\n\t\t\t/>\n\t\t\t<BorderControl\n\t\t\t\tclassName={ centeredClassName }\n\t\t\t\thideLabelFromVision={ true }\n\t\t\t\tlabel={ __( 'Bottom border' ) }\n\t\t\t\tonChange={ ( newBorder ) => onChange( newBorder, 'bottom' ) }\n\t\t\t\t__unstablePopoverProps={ popoverProps }\n\t\t\t\tvalue={ value?.bottom }\n\t\t\t\t{ ...sharedBorderControlProps }\n\t\t\t/>\n\t\t</Grid>\n\t);\n};\n\nconst ConnectedBorderBoxControlSplitControls = contextConnect(\n\tBorderBoxControlSplitControls,\n\t'BorderBoxControlSplitControls'\n);\nexport default ConnectedBorderBoxControlSplitControls;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,2BAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AAEA,IAAAO,QAAA,GAAAP,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AAfA;AACA;AACA;;AAKA;AACA;AACA;;AAWA,MAAMS,6BAA6B,GAAGA,CACrCC,KAA2D,EAC3DC,YAAuC,KACnC;EACJ,MAAM;IACLC,iBAAiB;IACjBC,MAAM;IACNC,mBAAmB;IACnBC,WAAW;IACXC,WAAW;IACXC,QAAQ;IACRC,gBAAgB;IAChBC,aAAa;IACbC,qBAAqB;IACrBC,IAAI,GAAG,SAAS;IAChBC,KAAK;IACLC,iCAAiC;IACjC,GAAGC;EACJ,CAAC,GAAG,IAAAC,sCAAgC,EAAEf,KAAM,CAAC;;EAE7C;EACA;EACA,MAAM,CAAEgB,aAAa,EAAEC,gBAAgB,CAAE,GAAG,IAAAC,iBAAQ,EACnD,IACD,CAAC;;EAED;EACA,MAAMC,YAA4D,GACjE,IAAAC,gBAAO,EACN,MACCZ,gBAAgB,GACb;IACAa,SAAS,EAAEb,gBAAgB;IAC3Bc,MAAM,EAAEb,aAAa;IACrBc,MAAM,EAAEP,aAAa;IACrBQ,KAAK,EAAE;EACP,CAAC,GACDC,SAAS,EACb,CAAEjB,gBAAgB,EAAEC,aAAa,EAAEO,aAAa,CACjD,CAAC;EAEF,MAAMU,wBAAwB,GAAG;IAChCvB,MAAM;IACNC,mBAAmB;IACnBC,WAAW;IACXC,WAAW;IACXqB,SAAS,EAAE,IAAI;IACfd,iCAAiC;IACjCF;EACD,CAAC;EAED,MAAMiB,SAAS,GAAG,IAAAC,qBAAY,EAAE,CAAEZ,gBAAgB,EAAEhB,YAAY,CAAG,CAAC;EAEpE,OACC,IAAA6B,MAAA,CAAAC,aAAA,EAACnC,KAAA,CAAAoC,IAAI;IAAA,GAAMlB,UAAU;IAAGmB,GAAG,EAAGL,SAAW;IAACM,GAAG,EAAG;EAAG,GAClD,IAAAJ,MAAA,CAAAC,aAAA,EAACtC,2BAAA,CAAA0C,OAA0B;IAACvB,KAAK,EAAGA,KAAO;IAACD,IAAI,EAAGA;EAAM,CAAE,CAAC,EAC5D,IAAAmB,MAAA,CAAAC,aAAA,EAACpC,cAAA,CAAAyC,aAAa;IACbC,SAAS,EAAGnC,iBAAmB;IAC/BoC,mBAAmB,EAAG,IAAM;IAC5BC,KAAK,EAAG,IAAAC,QAAE,EAAE,YAAa,CAAG;IAC5BjC,QAAQ,EAAKkC,SAAS,IAAMlC,QAAQ,CAAEkC,SAAS,EAAE,KAAM,CAAG;IAC1DC,sBAAsB,EAAGvB,YAAc;IACvCP,KAAK,EAAGA,KAAK,EAAE+B,GAAK;IAAA,GACfjB;EAAwB,CAC7B,CAAC,EACF,IAAAI,MAAA,CAAAC,aAAA,EAACpC,cAAA,CAAAyC,aAAa;IACbE,mBAAmB,EAAG,IAAM;IAC5BC,KAAK,EAAG,IAAAC,QAAE,EAAE,aAAc,CAAG;IAC7BjC,QAAQ,EAAKkC,SAAS,IAAMlC,QAAQ,CAAEkC,SAAS,EAAE,MAAO,CAAG;IAC3DC,sBAAsB,EAAGvB,YAAc;IACvCP,KAAK,EAAGA,KAAK,EAAEgC,IAAM;IAAA,GAChBlB;EAAwB,CAC7B,CAAC,EACF,IAAAI,MAAA,CAAAC,aAAA,EAACpC,cAAA,CAAAyC,aAAa;IACbC,SAAS,EAAG3B,qBAAuB;IACnC4B,mBAAmB,EAAG,IAAM;IAC5BC,KAAK,EAAG,IAAAC,QAAE,EAAE,cAAe,CAAG;IAC9BjC,QAAQ,EAAKkC,SAAS,IAAMlC,QAAQ,CAAEkC,SAAS,EAAE,OAAQ,CAAG;IAC5DC,sBAAsB,EAAGvB,YAAc;IACvCP,KAAK,EAAGA,KAAK,EAAEiC,KAAO;IAAA,GACjBnB;EAAwB,CAC7B,CAAC,EACF,IAAAI,MAAA,CAAAC,aAAA,EAACpC,cAAA,CAAAyC,aAAa;IACbC,SAAS,EAAGnC,iBAAmB;IAC/BoC,mBAAmB,EAAG,IAAM;IAC5BC,KAAK,EAAG,IAAAC,QAAE,EAAE,eAAgB,CAAG;IAC/BjC,QAAQ,EAAKkC,SAAS,IAAMlC,QAAQ,CAAEkC,SAAS,EAAE,QAAS,CAAG;IAC7DC,sBAAsB,EAAGvB,YAAc;IACvCP,KAAK,EAAGA,KAAK,EAAEkC,MAAQ;IAAA,GAClBpB;EAAwB,CAC7B,CACI,CAAC;AAET,CAAC;AAED,MAAMqB,sCAAsC,GAAG,IAAAC,uBAAc,EAC5DjD,6BAA6B,EAC7B,+BACD,CAAC;AAAC,IAAAkD,QAAA,GACaF,sCAAsC;AAAAG,OAAA,CAAAf,OAAA,GAAAc,QAAA"}