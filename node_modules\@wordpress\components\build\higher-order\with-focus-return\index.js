"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Provider = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
/**
 * WordPress dependencies
 */

/**
 * Returns true if the given object is component-like. An object is component-
 * like if it is an instance of wp.element.Component, or is a function.
 *
 * @param object Object to test.
 *
 * @return Whether object is component-like.
 */
function isComponentLike(object) {
  return object instanceof _element.Component || typeof object === 'function';
}
/**
 * Higher Order Component used to be used to wrap disposable elements like
 * sidebars, modals, dropdowns. When mounting the wrapped component, we track a
 * reference to the current active element so we know where to restore focus
 * when the component is unmounted.
 *
 * @param options The component to be enhanced with
 *                focus return behavior, or an object
 *                describing the component and the
 *                focus return characteristics.
 *
 * @return Higher Order Component with the focus restauration behaviour.
 */
var _default = (0, _compose.createHigherOrderComponent)(
// @ts-expect-error TODO: Reconcile with intended `createHigherOrderComponent` types
options => {
  const HoC = ({
    onFocusReturn
  } = {}) => WrappedComponent => {
    const WithFocusReturn = props => {
      const ref = (0, _compose.useFocusReturn)(onFocusReturn);
      return (0, _react.createElement)("div", {
        ref: ref
      }, (0, _react.createElement)(WrappedComponent, {
        ...props
      }));
    };
    return WithFocusReturn;
  };
  if (isComponentLike(options)) {
    const WrappedComponent = options;
    return HoC()(WrappedComponent);
  }
  return HoC(options);
}, 'withFocusReturn');
exports.default = _default;
const Provider = ({
  children
}) => {
  (0, _deprecated.default)('wp.components.FocusReturnProvider component', {
    since: '5.7',
    hint: 'This provider is not used anymore. You can just remove it from your codebase'
  });
  return children;
};
exports.Provider = Provider;
//# sourceMappingURL=index.js.map