{"version": 3, "names": [], "sources": ["@wordpress/components/src/duotone-picker/types.ts"], "sourcesContent": ["export type DuotonePickerProps = {\n\t/**\n\t * Whether there should be a button to clear the duotone value.\n\t *\n\t * @default true\n\t */\n\tclearable?: boolean;\n\t/**\n\t * Whether there should be an `unset` option.\n\t *\n\t * @default true\n\t */\n\tunsetable?: boolean;\n\t/**\n\t * Array of color presets of the form `{ color: '#000000', name: 'Black', slug: 'black' }`.\n\t */\n\tcolorPalette: Color[];\n\t/**\n\t * Array of duotone presets of the form `{ colors: [ '#000000', '#ffffff' ], name: 'Graysca<PERSON>', slug: 'grayscale' }`.\n\t */\n\tduotonePalette: DuotoneColor[];\n\t/**\n\t * Whether custom colors should be disabled.\n\t *\n\t * @default false\n\t */\n\tdisableCustomColors?: boolean;\n\t/**\n\t * Whether custom duotone values should be disabled.\n\t *\n\t * @default false\n\t */\n\tdisableCustomDuotone?: boolean;\n\t/**\n\t * An array of colors for the duotone effect.\n\t */\n\tvalue?: string[] | 'unset';\n\t/**\n\t * Callback which is called when the duotone colors change.\n\t */\n\tonChange: ( value: DuotonePickerProps[ 'value' ] | undefined ) => void;\n\t/**\n\t * Whether the control should present as a set of buttons,\n\t * each with its own tab stop.\n\t *\n\t * @default false\n\t */\n\tasButtons?: boolean;\n\t/**\n\t * Prevents keyboard interaction from wrapping around.\n\t * Only used when `asButtons` is not true.\n\t *\n\t * @default true\n\t */\n\tloop?: boolean;\n} & (\n\t| {\n\t\t\t/**\n\t\t\t * A label to identify the purpose of the control.\n\t\t\t *\n\t\t\t * @todo [#54055] Either this or `aria-labelledby` should be required\n\t\t\t */\n\t\t\t'aria-label'?: string;\n\t\t\t'aria-labelledby'?: never;\n\t  }\n\t| {\n\t\t\t/**\n\t\t\t * An ID of an element to provide a label for the control.\n\t\t\t *\n\t\t\t * @todo [#54055] Either this or `aria-label` should be required\n\t\t\t */\n\t\t\t'aria-labelledby'?: string;\n\t\t\t'aria-label'?: never;\n\t  }\n);\n\ntype Color = {\n\tcolor: string;\n\tname: string;\n\tslug: string;\n};\n\ntype DuotoneColor = {\n\tcolors: string[];\n\tname: string;\n\tslug: string;\n};\n\nexport type DuotoneSwatchProps = {\n\t/**\n\t * An array of colors to show or `null` to show the placeholder swatch icon.\n\t */\n\tvalues?: string[] | null;\n};\n"], "mappings": ""}