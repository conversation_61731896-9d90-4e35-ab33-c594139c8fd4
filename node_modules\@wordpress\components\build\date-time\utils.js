"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.inputToDate = inputToDate;
var _dateFns = require("date-fns");
/**
 * External dependencies
 */

/**
 * Like date-fn's toDate, but tries to guess the format when a string is
 * given.
 *
 * @param input Value to turn into a date.
 */
function inputToDate(input) {
  if (typeof input === 'string') {
    return new Date(input);
  }
  return (0, _dateFns.toDate)(input);
}
//# sourceMappingURL=utils.js.map