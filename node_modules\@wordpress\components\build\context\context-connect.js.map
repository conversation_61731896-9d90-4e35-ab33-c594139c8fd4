{"version": 3, "names": ["_element", "require", "_warning", "_interopRequireDefault", "_constants", "_getStyledClassNameFromKey", "contextConnect", "Component", "namespace", "_contextConnect", "forwardsRef", "contextConnectWithoutRef", "options", "WrappedComponent", "forwardRef", "SCRIPT_DEBUG", "warn", "mergedNamespace", "CONNECT_STATIC_NAMESPACE", "Array", "isArray", "Object", "assign", "Set", "displayName", "selector", "getStyledClassNameFromKey", "getConnectNamespace", "namespaces", "type", "hasConnectNamespace", "match", "includes", "some", "result"], "sources": ["@wordpress/components/src/context/context-connect.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef, ReactChild, ReactNode } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef } from '@wordpress/element';\nimport warn from '@wordpress/warning';\n\n/**\n * Internal dependencies\n */\nimport { CONNECT_STATIC_NAMESPACE } from './constants';\nimport { getStyledClassNameFromKey } from './get-styled-class-name-from-key';\nimport type { WordPressComponentFromProps } from '.';\n\ntype AcceptsTwoArgs<\n\tF extends ( ...args: any ) => any,\n\tErrorMessage = never,\n> = Parameters< F >[ 'length' ] extends 2 ? {} : ErrorMessage;\n\ntype ContextConnectOptions = {\n\tforwardsRef?: boolean;\n};\n\n/**\n * Forwards ref (React.ForwardRef) and \"Connects\" (or registers) a component\n * within the Context system under a specified namespace.\n *\n * @param Component The component to register into the Context system.\n * @param namespace The namespace to register the component under.\n * @return The connected WordPressComponent\n */\nexport function contextConnect<\n\tC extends ( props: any, ref: ForwardedRef< any > ) => JSX.Element | null,\n>(\n\tComponent: C &\n\t\tAcceptsTwoArgs<\n\t\t\tC,\n\t\t\t'Warning: Your component function does not take a ref as the second argument. Did you mean to use `contextConnectWithoutRef`?'\n\t\t>,\n\tnamespace: string\n) {\n\treturn _contextConnect( Component, namespace, { forwardsRef: true } );\n}\n\n/**\n * \"Connects\" (or registers) a component within the Context system under a specified namespace.\n * Does not forward a ref.\n *\n * @param Component The component to register into the Context system.\n * @param namespace The namespace to register the component under.\n * @return The connected WordPressComponent\n */\nexport function contextConnectWithoutRef< P >(\n\tComponent: ( props: P ) => JSX.Element | null,\n\tnamespace: string\n) {\n\treturn _contextConnect( Component, namespace );\n}\n\n// This is an (experimental) evolution of the initial connect() HOC.\n// The hope is that we can improve render performance by removing functional\n// component wrappers.\nfunction _contextConnect<\n\tC extends ( props: any, ref: ForwardedRef< any > ) => JSX.Element | null,\n\tO extends ContextConnectOptions,\n>(\n\tComponent: C,\n\tnamespace: string,\n\toptions?: O\n): WordPressComponentFromProps<\n\tParameters< C >[ 0 ],\n\tO[ 'forwardsRef' ] extends true ? true : false\n> {\n\tconst WrappedComponent = options?.forwardsRef\n\t\t? forwardRef< any, Parameters< C >[ 0 ] >( Component )\n\t\t: Component;\n\n\tif ( typeof namespace === 'undefined' ) {\n\t\twarn( 'contextConnect: Please provide a namespace' );\n\t}\n\n\t// @ts-expect-error internal property\n\tlet mergedNamespace = WrappedComponent[ CONNECT_STATIC_NAMESPACE ] || [\n\t\tnamespace,\n\t];\n\n\t/**\n\t * Consolidate (merge) namespaces before attaching it to the WrappedComponent.\n\t */\n\tif ( Array.isArray( namespace ) ) {\n\t\tmergedNamespace = [ ...mergedNamespace, ...namespace ];\n\t}\n\tif ( typeof namespace === 'string' ) {\n\t\tmergedNamespace = [ ...mergedNamespace, namespace ];\n\t}\n\n\t// @ts-expect-error We can't rely on inferred types here because of the\n\t// `as` prop polymorphism we're handling in https://github.com/WordPress/gutenberg/blob/4f3a11243c365f94892e479bff0b922ccc4ccda3/packages/components/src/context/wordpress-component.ts#L32-L33\n\treturn Object.assign( WrappedComponent, {\n\t\t[ CONNECT_STATIC_NAMESPACE ]: [ ...new Set( mergedNamespace ) ],\n\t\tdisplayName: namespace,\n\t\tselector: `.${ getStyledClassNameFromKey( namespace ) }`,\n\t} );\n}\n\n/**\n * Attempts to retrieve the connected namespace from a component.\n *\n * @param Component The component to retrieve a namespace from.\n * @return The connected namespaces.\n */\nexport function getConnectNamespace(\n\tComponent: ReactChild | undefined | {}\n): string[] {\n\tif ( ! Component ) return [];\n\n\tlet namespaces = [];\n\n\t// @ts-ignore internal property\n\tif ( Component[ CONNECT_STATIC_NAMESPACE ] ) {\n\t\t// @ts-ignore internal property\n\t\tnamespaces = Component[ CONNECT_STATIC_NAMESPACE ];\n\t}\n\n\t// @ts-ignore\n\tif ( Component.type && Component.type[ CONNECT_STATIC_NAMESPACE ] ) {\n\t\t// @ts-ignore\n\t\tnamespaces = Component.type[ CONNECT_STATIC_NAMESPACE ];\n\t}\n\n\treturn namespaces;\n}\n\n/**\n * Checks to see if a component is connected within the Context system.\n *\n * @param Component The component to retrieve a namespace from.\n * @param match     The namespace to check.\n */\nexport function hasConnectNamespace(\n\tComponent: ReactNode,\n\tmatch: string[] | string\n): boolean {\n\tif ( ! Component ) return false;\n\n\tif ( typeof match === 'string' ) {\n\t\treturn getConnectNamespace( Component ).includes( match );\n\t}\n\tif ( Array.isArray( match ) ) {\n\t\treturn match.some( ( result ) =>\n\t\t\tgetConnectNamespace( Component ).includes( result )\n\t\t);\n\t}\n\n\treturn false;\n}\n"], "mappings": ";;;;;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,0BAAA,GAAAJ,OAAA;AAfA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,cAAcA,CAG7BC,SAIE,EACFC,SAAiB,EAChB;EACD,OAAOC,eAAe,CAAEF,SAAS,EAAEC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAK,CAAE,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,wBAAwBA,CACvCJ,SAA6C,EAC7CC,SAAiB,EAChB;EACD,OAAOC,eAAe,CAAEF,SAAS,EAAEC,SAAU,CAAC;AAC/C;;AAEA;AACA;AACA;AACA,SAASC,eAAeA,CAIvBF,SAAY,EACZC,SAAiB,EACjBI,OAAW,EAIV;EACD,MAAMC,gBAAgB,GAAGD,OAAO,EAAEF,WAAW,GAC1C,IAAAI,mBAAU,EAA+BP,SAAU,CAAC,GACpDA,SAAS;EAEZ,IAAK,OAAOC,SAAS,KAAK,WAAW,EAAG;IACvC,OAAAO,YAAA,oBAAAA,YAAA,gBAAAC,gBAAI,EAAE,4CAA6C,CAAC;EACrD;;EAEA;EACA,IAAIC,eAAe,GAAGJ,gBAAgB,CAAEK,mCAAwB,CAAE,IAAI,CACrEV,SAAS,CACT;;EAED;AACD;AACA;EACC,IAAKW,KAAK,CAACC,OAAO,CAAEZ,SAAU,CAAC,EAAG;IACjCS,eAAe,GAAG,CAAE,GAAGA,eAAe,EAAE,GAAGT,SAAS,CAAE;EACvD;EACA,IAAK,OAAOA,SAAS,KAAK,QAAQ,EAAG;IACpCS,eAAe,GAAG,CAAE,GAAGA,eAAe,EAAET,SAAS,CAAE;EACpD;;EAEA;EACA;EACA,OAAOa,MAAM,CAACC,MAAM,CAAET,gBAAgB,EAAE;IACvC,CAAEK,mCAAwB,GAAI,CAAE,GAAG,IAAIK,GAAG,CAAEN,eAAgB,CAAC,CAAE;IAC/DO,WAAW,EAAEhB,SAAS;IACtBiB,QAAQ,EAAG,IAAI,IAAAC,oDAAyB,EAAElB,SAAU,CAAG;EACxD,CAAE,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASmB,mBAAmBA,CAClCpB,SAAsC,EAC3B;EACX,IAAK,CAAEA,SAAS,EAAG,OAAO,EAAE;EAE5B,IAAIqB,UAAU,GAAG,EAAE;;EAEnB;EACA,IAAKrB,SAAS,CAAEW,mCAAwB,CAAE,EAAG;IAC5C;IACAU,UAAU,GAAGrB,SAAS,CAAEW,mCAAwB,CAAE;EACnD;;EAEA;EACA,IAAKX,SAAS,CAACsB,IAAI,IAAItB,SAAS,CAACsB,IAAI,CAAEX,mCAAwB,CAAE,EAAG;IACnE;IACAU,UAAU,GAAGrB,SAAS,CAACsB,IAAI,CAAEX,mCAAwB,CAAE;EACxD;EAEA,OAAOU,UAAU;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,mBAAmBA,CAClCvB,SAAoB,EACpBwB,KAAwB,EACd;EACV,IAAK,CAAExB,SAAS,EAAG,OAAO,KAAK;EAE/B,IAAK,OAAOwB,KAAK,KAAK,QAAQ,EAAG;IAChC,OAAOJ,mBAAmB,CAAEpB,SAAU,CAAC,CAACyB,QAAQ,CAAED,KAAM,CAAC;EAC1D;EACA,IAAKZ,KAAK,CAACC,OAAO,CAAEW,KAAM,CAAC,EAAG;IAC7B,OAAOA,KAAK,CAACE,IAAI,CAAIC,MAAM,IAC1BP,mBAAmB,CAAEpB,SAAU,CAAC,CAACyB,QAAQ,CAAEE,MAAO,CACnD,CAAC;EACF;EAEA,OAAO,KAAK;AACb"}