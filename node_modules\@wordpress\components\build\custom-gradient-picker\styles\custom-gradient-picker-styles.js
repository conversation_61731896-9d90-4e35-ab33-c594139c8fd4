"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SelectWrapper = exports.AccessoryWrapper = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _flex = require("../../flex");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const SelectWrapper = ( /*#__PURE__*/0, _base.default)(_flex.FlexBlock, process.env.NODE_ENV === "production" ? {
  target: "e10bzpgi1"
} : {
  target: "e10bzpgi1",
  label: "SelectWrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "1gvx10y",
  styles: "flex-grow:5"
} : {
  name: "1gvx10y",
  styles: "flex-grow:5",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY3VzdG9tLWdyYWRpZW50LXBpY2tlci9zdHlsZXMvY3VzdG9tLWdyYWRpZW50LXBpY2tlci1zdHlsZXMudHN4Il0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQVNnRCIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2N1c3RvbS1ncmFkaWVudC1waWNrZXIvc3R5bGVzL2N1c3RvbS1ncmFkaWVudC1waWNrZXItc3R5bGVzLnRzeCIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IEZsZXhCbG9jayB9IGZyb20gJy4uLy4uL2ZsZXgnO1xuXG5leHBvcnQgY29uc3QgU2VsZWN0V3JhcHBlciA9IHN0eWxlZCggRmxleEJsb2NrIClgXG5cdGZsZXgtZ3JvdzogNTtcbmA7XG5cbmV4cG9ydCBjb25zdCBBY2Nlc3NvcnlXcmFwcGVyID0gc3R5bGVkKCBGbGV4QmxvY2sgKWBcblx0ZmxleC1ncm93OiA1O1xuYDtcbiJdfQ== */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.SelectWrapper = SelectWrapper;
const AccessoryWrapper = ( /*#__PURE__*/0, _base.default)(_flex.FlexBlock, process.env.NODE_ENV === "production" ? {
  target: "e10bzpgi0"
} : {
  target: "e10bzpgi0",
  label: "AccessoryWrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "1gvx10y",
  styles: "flex-grow:5"
} : {
  name: "1gvx10y",
  styles: "flex-grow:5",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY3VzdG9tLWdyYWRpZW50LXBpY2tlci9zdHlsZXMvY3VzdG9tLWdyYWRpZW50LXBpY2tlci1zdHlsZXMudHN4Il0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQWFtRCIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2N1c3RvbS1ncmFkaWVudC1waWNrZXIvc3R5bGVzL2N1c3RvbS1ncmFkaWVudC1waWNrZXItc3R5bGVzLnRzeCIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IEZsZXhCbG9jayB9IGZyb20gJy4uLy4uL2ZsZXgnO1xuXG5leHBvcnQgY29uc3QgU2VsZWN0V3JhcHBlciA9IHN0eWxlZCggRmxleEJsb2NrIClgXG5cdGZsZXgtZ3JvdzogNTtcbmA7XG5cbmV4cG9ydCBjb25zdCBBY2Nlc3NvcnlXcmFwcGVyID0gc3R5bGVkKCBGbGV4QmxvY2sgKWBcblx0ZmxleC1ncm93OiA1O1xuYDtcbiJdfQ== */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.AccessoryWrapper = AccessoryWrapper;
//# sourceMappingURL=custom-gradient-picker-styles.js.map