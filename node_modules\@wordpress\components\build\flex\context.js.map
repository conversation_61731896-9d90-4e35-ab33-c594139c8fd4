{"version": 3, "names": ["_element", "require", "FlexContext", "createContext", "flexItemDisplay", "undefined", "exports", "useFlexContext", "useContext"], "sources": ["@wordpress/components/src/flex/context.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { createContext, useContext } from '@wordpress/element';\n\nexport const FlexContext = createContext< {\n\tflexItemDisplay: 'block' | undefined;\n} >( {\n\tflexItemDisplay: undefined,\n} );\n\nexport const useFlexContext = () => useContext( FlexContext );\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGO,MAAMC,WAAW,GAAG,IAAAC,sBAAa,EAEnC;EACJC,eAAe,EAAEC;AAClB,CAAE,CAAC;AAACC,OAAA,CAAAJ,WAAA,GAAAA,WAAA;AAEG,MAAMK,cAAc,GAAGA,CAAA,KAAM,IAAAC,mBAAU,EAAEN,WAAY,CAAC;AAACI,OAAA,CAAAC,cAAA,GAAAA,cAAA"}