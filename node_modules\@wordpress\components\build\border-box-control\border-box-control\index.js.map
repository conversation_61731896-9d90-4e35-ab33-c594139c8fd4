{"version": 3, "names": ["_component", "_interopRequireDefault", "require", "_hook"], "sources": ["@wordpress/components/src/border-box-control/border-box-control/index.ts"], "sourcesContent": ["export { default as BorderBoxControl } from './component';\nexport { useBorderBoxControl } from './hook';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA"}