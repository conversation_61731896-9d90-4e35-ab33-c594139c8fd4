!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("proxy-compare")):"function"==typeof define&&define.amd?define(["exports","proxy-compare"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).valtioVanilla={},e.proxyCompare)}(this,(function(e,t){"use strict";var n=function(e){return"object"==typeof e&&null!==e},r=new WeakSet,o=Symbol(),i=Symbol(),c=Symbol(),f=function(e,f,a,l,s,d,v,p,y,b){return void 0===e&&(e=Object.is),void 0===f&&(f=function(e,t){return new Proxy(e,t)}),void 0===a&&(a=function(e){return n(e)&&!r.has(e)&&(Array.isArray(e)||!(Symbol.iterator in e))&&!(e instanceof WeakMap)&&!(e instanceof WeakSet)&&!(e instanceof Error)&&!(e instanceof Number)&&!(e instanceof Date)&&!(e instanceof String)&&!(e instanceof RegExp)&&!(e instanceof ArrayBuffer)}),void 0===l&&(l=Symbol()),void 0===s&&(s=Symbol()),void 0===d&&(d=new WeakMap),void 0===v&&(v=function(e,n,o){var f=d.get(o);if((null==f?void 0:f[0])===e)return f[1];var a=Array.isArray(n)?[]:Object.create(Object.getPrototypeOf(n));return t.markToTrack(a,!0),d.set(o,[e,a]),Reflect.ownKeys(n).forEach((function(e){var f=Reflect.get(n,e,o);if(r.has(f))t.markToTrack(f,!1),a[e]=f;else if(f instanceof Promise)if(l in f)a[e]=f[l];else{var u=f[s]||f;Object.defineProperty(a,e,{get:function(){if(l in f)return f[l];throw u}})}else null!=f&&f[i]?a[e]=f[c]:a[e]=f})),Object.freeze(a)}),void 0===p&&(p=new WeakMap),void 0===y&&(y=[1]),void 0===b&&(b=function(r){if(!n(r))throw new Error("object required");var d=p.get(r);if(d)return d;var b=y[0],g=new Set,m=function(e,t){void 0===t&&(t=++y[0]),b!==t&&(b=t,g.forEach((function(n){return n(e,t)})))},h=new Map,j=function(e){var t=h.get(e);return t||(t=function(t,n){var r=[].concat(t);r[1]=[e].concat(r[1]),m(r,n)},h.set(e,t)),t},O=function(e){var t=h.get(e);return h.delete(e),t},P=Array.isArray(r)?[]:Object.create(Object.getPrototypeOf(r)),w=f(P,{get:function(e,t,n){return t===o?b:t===i?g:t===c?v(b,e,n):Reflect.get(e,t,n)},deleteProperty:function(e,t){var n=Reflect.get(e,t),r=null==n?void 0:n[i];r&&r.delete(O(t));var o=Reflect.deleteProperty(e,t);return o&&m(["delete",[t],n]),o},set:function(r,o,c,f){var d,v,p=Reflect.has(r,o),y=Reflect.get(r,o,f);if(p&&e(y,c))return!0;var b,g=null==y?void 0:y[i];return g&&g.delete(O(o)),n(c)&&(c=t.getUntracked(c)||c),null!=(d=Object.getOwnPropertyDescriptor(r,o))&&d.set?b=c:c instanceof Promise?b=c.then((function(e){return b[l]=e,m(["resolve",[o],e]),e})).catch((function(e){b[s]=e,m(["reject",[o],e])})):null!=(v=c)&&v[i]?(b=c)[i].add(j(o)):a(c)?(b=u(c))[i].add(j(o)):b=c,Reflect.set(r,o,b,f),m(["set",[o],c,y]),!0}});return p.set(r,w),Reflect.ownKeys(r).forEach((function(e){var t=Object.getOwnPropertyDescriptor(r,e);t.get||t.set?Object.defineProperty(P,e,t):w[e]=r[e]})),w}),[b,r,o,i,c,e,f,a,l,s,d,v,p,y]},a=f()[0];function u(e){return void 0===e&&(e={}),a(e)}var l=f;e.getVersion=function(e){return n(e)?e[o]:void 0},e.proxy=u,e.ref=function(e){return r.add(e),e},e.snapshot=function(e){return e[c]},e.subscribe=function(e,t,n){var r,o=[],c=function(e){o.push(e),n?t(o.splice(0)):r||(r=Promise.resolve().then((function(){r=void 0,t(o.splice(0))})))};return e[i].add(c),function(){e[i].delete(c)}},e.unstable_buildProxyFunction=l,Object.defineProperty(e,"__esModule",{value:!0})}));
