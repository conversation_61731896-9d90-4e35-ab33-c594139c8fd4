"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.T_SHIRT_NAMES = exports.T_SHIRT_ABBREVIATIONS = void 0;
var _i18n = require("@wordpress/i18n");
/**
 * WordPress dependencies
 */

/**
 * List of T-shirt abbreviations.
 *
 * When there are 5 font sizes or fewer, we assume that the font sizes are
 * ordered by size and show T-shirt labels.
 */
const T_SHIRT_ABBREVIATIONS = [/* translators: S stands for 'small' and is a size label. */
(0, _i18n.__)('S'), /* translators: M stands for 'medium' and is a size label. */
(0, _i18n.__)('M'), /* translators: L stands for 'large' and is a size label. */
(0, _i18n.__)('L'), /* translators: XL stands for 'extra large' and is a size label. */
(0, _i18n.__)('XL'), /* translators: XXL stands for 'extra extra large' and is a size label. */
(0, _i18n.__)('XXL')];

/**
 * List of T-shirt names.
 *
 * When there are 5 font sizes or fewer, we assume that the font sizes are
 * ordered by size and show T-shirt labels.
 */
exports.T_SHIRT_ABBREVIATIONS = T_SHIRT_ABBREVIATIONS;
const T_SHIRT_NAMES = [(0, _i18n.__)('Small'), (0, _i18n.__)('Medium'), (0, _i18n.__)('Large'), (0, _i18n.__)('Extra Large'), (0, _i18n.__)('Extra Extra Large')];
exports.T_SHIRT_NAMES = T_SHIRT_NAMES;
//# sourceMappingURL=constants.js.map