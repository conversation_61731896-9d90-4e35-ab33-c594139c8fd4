{"version": 3, "names": ["_reactNative", "require", "_reactNativeModal", "_interopRequireDefault", "_reactNativeSafeArea", "_reactNativeBridge", "_element", "_compose", "_styles", "_button", "_cell", "_cyclePickerCell", "_picker<PERSON>ell", "_switchCell", "_rangeCell", "_colorCell", "_linkCell", "_linkSuggestionItemCell", "_radioCell", "_navigationScreen", "_navigationContainer", "_keyboardAvoidingView", "_subSheet", "_navBar", "_bottomSheetContext", "DEFAULT_LAYOUT_ANIMATION", "LayoutAnimation", "Presets", "easeInEaseOut", "BottomSheet", "Component", "constructor", "arguments", "onSafeAreaInsetsUpdate", "bind", "onScroll", "isScrolling", "onShouldEnableScroll", "on<PERSON><PERSON><PERSON>", "onShouldSetBottomSheetMaxHeight", "setIsFullScreen", "onDimensionsChange", "onHeaderLayout", "onCloseBottomSheet", "onHandleClosingBottomSheet", "onHardwareButtonPress", "onHandleHardwareButtonPress", "keyboardShow", "keyboardHide", "headerHeight", "keyboardHeight", "lastLayoutAnimation", "lastLayoutAnimationFinished", "state", "safeAreaBottomInset", "safeAreaTopInset", "bounces", "maxHeight", "scrollEnabled", "handleClosingBottomSheet", "handleHardwareButtonPress", "isMaxHeightSet", "isFullScreen", "props", "e", "isVisible", "height", "endCoordinates", "performKeyboardLayoutAnimation", "onSetMaxHeight", "onKeyboardShow", "onKeyboardHide", "event", "duration", "easing", "animationConfig", "type", "Types", "layoutAnimation", "update", "create", "property", "Properties", "opacity", "delete", "configureNext", "performRegularLayoutAnimation", "useLastLayoutAnimation", "Platform", "OS", "componentDidMount", "SafeArea", "getSafeAreaInsetsForRootView", "then", "androidModalClosedSubscription", "subscribeAndroidModalClosed", "onClose", "dimensionsChangeSubscription", "Dimensions", "addEventListener", "keyboardShowListener", "Keyboard", "addListener", "keyboardHideListener", "safeAreaEventSubscription", "componentWillUnmount", "remove", "showAndroidSoftKeyboard", "result", "safeAreaInsets", "bottom", "top", "setState", "width", "get", "statusBarHeight", "StatusBar", "currentHeight", "maxHeightWithOpenKeyboard", "Math", "min", "nativeEvent", "layout", "round", "isCloseToBottom", "layoutMeasurement", "contentOffset", "contentSize", "y", "isCloseToTop", "value", "action", "getContentStyle", "paddingBottom", "styles", "scrollableContent", "render", "title", "leftButton", "rightB<PERSON>on", "header", "<PERSON><PERSON>ead<PERSON>", "style", "contentStyle", "getStylesFromColorScheme", "children", "withHeaderSeparator", "hasNavigation", "rest", "panResponder", "PanResponder", "onMoveShouldSetPanResponder", "evt", "gestureState", "dy", "backgroundStyle", "background", "backgroundDark", "bottomSheetHeaderTitleStyle", "bottomSheetHeaderTitle", "bottomSheetHeaderTitleDark", "listStyle", "flexGrow", "flexShrink", "setMinHeightToMaxHeight", "minHeight", "listProps", "disableScrollViewPanResponder", "onScrollBeginDrag", "onScrollEndDrag", "scrollEventThrottle", "contentContainerStyle", "content", "emptyHeader", "automaticallyAdjustContentInsets", "<PERSON><PERSON><PERSON><PERSON>iew", "View", "ScrollView", "<PERSON><PERSON><PERSON><PERSON>", "_react", "createElement", "Fragment", "bottomSheetHeader", "flex", "Text", "maxFontSizeMultiplier", "separator", "showDragIndicator", "allowDragIndicator", "default", "bottomModal", "animationInTiming", "animationOutTiming", "backdropTransitionInTiming", "backdropTransitionOutTiming", "backdropOpacity", "onBackdropPress", "onBackButtonPress", "onSwipeComplete", "undefined", "onModalHide", "swipeDirection", "onMoveShouldSetResponder", "panHandlers", "onMoveShouldSetResponderCapture", "onAccessibilityEscape", "testID", "hardwareAccelerated", "useNativeDriverForBackdrop", "behavior", "borderColor", "marginTop", "backgroundFullScreen", "keyboardVerticalOffset", "onLayout", "dragIndicator", "BottomSheetProvider", "shouldEnableBottomSheetScroll", "shouldEnableBottomSheetMaxHeight", "isBottomSheetContentScrolling", "TouchableHighlight", "accessible", "getWidth", "max<PERSON><PERSON><PERSON>", "ThemedBottomSheet", "withPreferredColorScheme", "<PERSON><PERSON>", "Cell", "SubSheet", "BottomSheetSubSheet", "NavBar", "CyclePickerCell", "<PERSON><PERSON><PERSON><PERSON>", "SwitchCell", "RangeCell", "ColorCell", "LinkCell", "LinkSuggestionItemCell", "RadioCell", "NavigationScreen", "NavigationContainer", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tDimensions,\n\tKeyboard,\n\tLayoutAnimation,\n\tPanResponder,\n\tPlatform,\n\tScrollView,\n\tStatusBar,\n\tText,\n\tTouchableHighlight,\n\tView,\n} from 'react-native';\nimport Modal from 'react-native-modal';\nimport SafeArea from 'react-native-safe-area';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tsubscribeAndroidModalClosed,\n\tshowAndroidSoftKeyboard,\n} from '@wordpress/react-native-bridge';\nimport { Component } from '@wordpress/element';\nimport { withPreferredColorScheme } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\nimport Button from './button';\nimport Cell from './cell';\nimport CyclePickerCell from './cycle-picker-cell';\nimport PickerCell from './picker-cell';\nimport SwitchCell from './switch-cell';\nimport RangeCell from './range-cell';\nimport ColorCell from './color-cell';\nimport LinkCell from './link-cell';\nimport LinkSuggestionItemCell from './link-suggestion-item-cell';\nimport RadioCell from './radio-cell';\nimport NavigationScreen from './bottom-sheet-navigation/navigation-screen';\nimport NavigationContainer from './bottom-sheet-navigation/navigation-container';\nimport KeyboardAvoidingView from './keyboard-avoiding-view';\nimport BottomSheetSubSheet from './sub-sheet';\nimport NavBar from './nav-bar';\nimport { BottomSheetProvider } from './bottom-sheet-context';\n\nconst DEFAULT_LAYOUT_ANIMATION = LayoutAnimation.Presets.easeInEaseOut;\n\nclass BottomSheet extends Component {\n\tconstructor() {\n\t\tsuper( ...arguments );\n\t\tthis.onSafeAreaInsetsUpdate = this.onSafeAreaInsetsUpdate.bind( this );\n\t\tthis.onScroll = this.onScroll.bind( this );\n\t\tthis.isScrolling = this.isScrolling.bind( this );\n\t\tthis.onShouldEnableScroll = this.onShouldEnableScroll.bind( this );\n\t\tthis.onDismiss = this.onDismiss.bind( this );\n\t\tthis.onShouldSetBottomSheetMaxHeight =\n\t\t\tthis.onShouldSetBottomSheetMaxHeight.bind( this );\n\n\t\tthis.setIsFullScreen = this.setIsFullScreen.bind( this );\n\n\t\tthis.onDimensionsChange = this.onDimensionsChange.bind( this );\n\t\tthis.onHeaderLayout = this.onHeaderLayout.bind( this );\n\t\tthis.onCloseBottomSheet = this.onCloseBottomSheet.bind( this );\n\t\tthis.onHandleClosingBottomSheet =\n\t\t\tthis.onHandleClosingBottomSheet.bind( this );\n\t\tthis.onHardwareButtonPress = this.onHardwareButtonPress.bind( this );\n\t\tthis.onHandleHardwareButtonPress =\n\t\t\tthis.onHandleHardwareButtonPress.bind( this );\n\t\tthis.keyboardShow = this.keyboardShow.bind( this );\n\t\tthis.keyboardHide = this.keyboardHide.bind( this );\n\n\t\tthis.headerHeight = 0;\n\t\tthis.keyboardHeight = 0;\n\t\tthis.lastLayoutAnimation = null;\n\t\tthis.lastLayoutAnimationFinished = false;\n\n\t\tthis.state = {\n\t\t\tsafeAreaBottomInset: 0,\n\t\t\tsafeAreaTopInset: 0,\n\t\t\tbounces: false,\n\t\t\tmaxHeight: 0,\n\t\t\tscrollEnabled: true,\n\t\t\tisScrolling: false,\n\t\t\thandleClosingBottomSheet: null,\n\t\t\thandleHardwareButtonPress: null,\n\t\t\tisMaxHeightSet: true,\n\t\t\tisFullScreen: this.props.isFullScreen || false,\n\t\t};\n\t}\n\n\tkeyboardShow( e ) {\n\t\tif ( ! this.props.isVisible ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst { height } = e.endCoordinates;\n\t\tthis.keyboardHeight = height;\n\t\tthis.performKeyboardLayoutAnimation( e );\n\t\tthis.onSetMaxHeight();\n\t\tthis.props.onKeyboardShow?.();\n\t}\n\n\tkeyboardHide( e ) {\n\t\tif ( ! this.props.isVisible ) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.keyboardHeight = 0;\n\t\tthis.performKeyboardLayoutAnimation( e );\n\t\tthis.onSetMaxHeight();\n\t\tthis.props.onKeyboardHide?.();\n\t}\n\n\tperformKeyboardLayoutAnimation( event ) {\n\t\tconst { duration, easing } = event;\n\n\t\tif ( duration && easing ) {\n\t\t\t// This layout animation is the same as the React Native's KeyboardAvoidingView component.\n\t\t\t// Reference: https://github.com/facebook/react-native/blob/266b21baf35e052ff28120f79c06c4f6dddc51a9/Libraries/Components/Keyboard/KeyboardAvoidingView.js#L119-L128.\n\t\t\tconst animationConfig = {\n\t\t\t\t// We have to pass the duration equal to minimal accepted duration defined here: RCTLayoutAnimation.m.\n\t\t\t\tduration: duration > 10 ? duration : 10,\n\t\t\t\ttype: LayoutAnimation.Types[ easing ] || 'keyboard',\n\t\t\t};\n\t\t\tconst layoutAnimation = {\n\t\t\t\tduration: animationConfig.duration,\n\t\t\t\tupdate: animationConfig,\n\t\t\t\tcreate: {\n\t\t\t\t\t...animationConfig,\n\t\t\t\t\tproperty: LayoutAnimation.Properties.opacity,\n\t\t\t\t},\n\t\t\t\tdelete: {\n\t\t\t\t\t...animationConfig,\n\t\t\t\t\tproperty: LayoutAnimation.Properties.opacity,\n\t\t\t\t},\n\t\t\t};\n\t\t\tthis.lastLayoutAnimationFinished = false;\n\t\t\tLayoutAnimation.configureNext( layoutAnimation, () => {\n\t\t\t\tthis.lastLayoutAnimationFinished = true;\n\t\t\t} );\n\t\t\tthis.lastLayoutAnimation = layoutAnimation;\n\t\t} else {\n\t\t\t// TODO: Reinstate animations, possibly replacing `LayoutAnimation` with\n\t\t\t// more nuanced `Animated` usage or replacing our custom `BottomSheet`\n\t\t\t// with `@gorhom/bottom-sheet`. This animation was disabled to avoid a\n\t\t\t// preexisting bug: https://github.com/WordPress/gutenberg/issues/30562\n\t\t\t// this.performRegularLayoutAnimation( {\n\t\t\t// \tuseLastLayoutAnimation: false,\n\t\t\t// } );.\n\t\t}\n\t}\n\n\tperformRegularLayoutAnimation( { useLastLayoutAnimation } ) {\n\t\t// On Android, we should prevent triggering multiple layout animations at the same time because it can produce visual glitches.\n\t\tif (\n\t\t\tPlatform.OS === 'android' &&\n\t\t\tthis.lastLayoutAnimation &&\n\t\t\t! this.lastLayoutAnimationFinished\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst layoutAnimation = useLastLayoutAnimation\n\t\t\t? this.lastLayoutAnimation || DEFAULT_LAYOUT_ANIMATION\n\t\t\t: DEFAULT_LAYOUT_ANIMATION;\n\n\t\tthis.lastLayoutAnimationFinished = false;\n\t\tLayoutAnimation.configureNext( layoutAnimation, () => {\n\t\t\tthis.lastLayoutAnimationFinished = true;\n\t\t} );\n\t\tthis.lastLayoutAnimation = layoutAnimation;\n\t}\n\n\tcomponentDidMount() {\n\t\tSafeArea.getSafeAreaInsetsForRootView().then(\n\t\t\tthis.onSafeAreaInsetsUpdate\n\t\t);\n\n\t\tif ( Platform.OS === 'android' ) {\n\t\t\tthis.androidModalClosedSubscription = subscribeAndroidModalClosed(\n\t\t\t\t() => {\n\t\t\t\t\tthis.props.onClose();\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tthis.dimensionsChangeSubscription = Dimensions.addEventListener(\n\t\t\t'change',\n\t\t\tthis.onDimensionsChange\n\t\t);\n\n\t\t// 'Will' keyboard events are not available on Android.\n\t\t// Reference: https://reactnative.dev/docs/0.61/keyboard#addlistener.\n\t\tthis.keyboardShowListener = Keyboard.addListener(\n\t\t\tPlatform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',\n\t\t\tthis.keyboardShow\n\t\t);\n\t\tthis.keyboardHideListener = Keyboard.addListener(\n\t\t\tPlatform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',\n\t\t\tthis.keyboardHide\n\t\t);\n\n\t\tthis.safeAreaEventSubscription = SafeArea.addEventListener(\n\t\t\t'safeAreaInsetsForRootViewDidChange',\n\t\t\tthis.onSafeAreaInsetsUpdate\n\t\t);\n\t\tthis.onSetMaxHeight();\n\t}\n\n\tcomponentWillUnmount() {\n\t\tthis.dimensionsChangeSubscription.remove();\n\t\tthis.keyboardShowListener.remove();\n\t\tthis.keyboardHideListener.remove();\n\t\tif ( this.androidModalClosedSubscription ) {\n\t\t\tthis.androidModalClosedSubscription.remove();\n\t\t}\n\n\t\tif ( this.props.isVisible ) {\n\t\t\tshowAndroidSoftKeyboard();\n\t\t}\n\n\t\tif ( this.safeAreaEventSubscription === null ) {\n\t\t\treturn;\n\t\t}\n\t\tthis.safeAreaEventSubscription.remove();\n\t\tthis.safeAreaEventSubscription = null;\n\t}\n\n\tonSafeAreaInsetsUpdate( result ) {\n\t\tconst { safeAreaBottomInset, safeAreaTopInset } = this.state;\n\t\tif ( this.safeAreaEventSubscription === null ) {\n\t\t\treturn;\n\t\t}\n\t\tconst { safeAreaInsets } = result;\n\t\tif (\n\t\t\tsafeAreaBottomInset !== safeAreaInsets.bottom ||\n\t\t\tsafeAreaTopInset !== safeAreaInsets.top\n\t\t) {\n\t\t\tthis.setState( {\n\t\t\t\tsafeAreaBottomInset: safeAreaInsets.bottom,\n\t\t\t\tsafeAreaTopInset: safeAreaInsets.top,\n\t\t\t} );\n\t\t}\n\t}\n\n\tonSetMaxHeight() {\n\t\tconst { height, width } = Dimensions.get( 'window' );\n\t\tconst { safeAreaBottomInset } = this.state;\n\t\tconst statusBarHeight =\n\t\t\tPlatform.OS === 'android' ? StatusBar.currentHeight : 0;\n\n\t\t// `maxHeight` when modal is opened along with a keyboard.\n\t\tconst maxHeightWithOpenKeyboard =\n\t\t\t0.95 *\n\t\t\t( Dimensions.get( 'window' ).height -\n\t\t\t\tthis.keyboardHeight -\n\t\t\t\tstatusBarHeight -\n\t\t\t\tthis.headerHeight );\n\n\t\t// In landscape orientation, set `maxHeight` to ~96% of the height.\n\t\tif ( width > height ) {\n\t\t\tthis.setState( {\n\t\t\t\tmaxHeight: Math.min(\n\t\t\t\t\t0.96 * height - this.headerHeight,\n\t\t\t\t\tmaxHeightWithOpenKeyboard\n\t\t\t\t),\n\t\t\t} );\n\t\t\t// In portrait orientation, set `maxHeight` to ~59% of the height.\n\t\t} else {\n\t\t\tthis.setState( {\n\t\t\t\tmaxHeight: Math.min(\n\t\t\t\t\theight * 0.59 - safeAreaBottomInset - this.headerHeight,\n\t\t\t\t\tmaxHeightWithOpenKeyboard\n\t\t\t\t),\n\t\t\t} );\n\t\t}\n\t}\n\n\tonDimensionsChange() {\n\t\tthis.onSetMaxHeight();\n\t\tthis.setState( { bounces: false } );\n\t}\n\n\tonHeaderLayout( { nativeEvent } ) {\n\t\tconst { height } = nativeEvent.layout;\n\t\t// The layout animation should only be triggered if the header\n\t\t// height has changed after being mounted.\n\t\tif (\n\t\t\tthis.headerHeight !== 0 &&\n\t\t\tMath.round( height ) !== Math.round( this.headerHeight )\n\t\t) {\n\t\t\tthis.performRegularLayoutAnimation( {\n\t\t\t\tuseLastLayoutAnimation: true,\n\t\t\t} );\n\t\t}\n\t\tthis.headerHeight = height;\n\t\tthis.onSetMaxHeight();\n\t}\n\n\tisCloseToBottom( { layoutMeasurement, contentOffset, contentSize } ) {\n\t\treturn (\n\t\t\tlayoutMeasurement.height + contentOffset.y >=\n\t\t\tcontentSize.height - contentOffset.y\n\t\t);\n\t}\n\n\tisCloseToTop( { contentOffset } ) {\n\t\treturn contentOffset.y < 10;\n\t}\n\n\tonScroll( { nativeEvent } ) {\n\t\tif ( this.isCloseToTop( nativeEvent ) ) {\n\t\t\tthis.setState( { bounces: false } );\n\t\t} else {\n\t\t\tthis.setState( { bounces: true } );\n\t\t}\n\t}\n\n\tonDismiss() {\n\t\tconst { onDismiss } = this.props;\n\n\t\t// Restore Keyboard Visibility\n\t\tshowAndroidSoftKeyboard();\n\n\t\tif ( onDismiss ) {\n\t\t\tonDismiss();\n\t\t}\n\n\t\tthis.onCloseBottomSheet();\n\t}\n\n\tonShouldEnableScroll( value ) {\n\t\tthis.setState( { scrollEnabled: value } );\n\t}\n\n\tonShouldSetBottomSheetMaxHeight( value ) {\n\t\tthis.setState( { isMaxHeightSet: value } );\n\t}\n\n\tisScrolling( value ) {\n\t\tthis.setState( { isScrolling: value } );\n\t}\n\n\tonHandleClosingBottomSheet( action ) {\n\t\tthis.setState( { handleClosingBottomSheet: action } );\n\t}\n\n\tonHandleHardwareButtonPress( action ) {\n\t\tthis.setState( { handleHardwareButtonPress: action } );\n\t}\n\n\tonCloseBottomSheet() {\n\t\tconst { onClose } = this.props;\n\t\tconst { handleClosingBottomSheet } = this.state;\n\t\tif ( handleClosingBottomSheet ) {\n\t\t\thandleClosingBottomSheet();\n\t\t\tthis.onHandleClosingBottomSheet( null );\n\t\t}\n\t\tif ( onClose ) {\n\t\t\tonClose();\n\t\t}\n\t\tthis.onShouldSetBottomSheetMaxHeight( true );\n\t}\n\n\tsetIsFullScreen( isFullScreen ) {\n\t\tif ( isFullScreen !== this.state.isFullScreen ) {\n\t\t\tif ( isFullScreen ) {\n\t\t\t\tthis.setState( { isFullScreen, isMaxHeightSet: false } );\n\t\t\t} else {\n\t\t\t\tthis.setState( { isFullScreen, isMaxHeightSet: true } );\n\t\t\t}\n\t\t}\n\t}\n\n\tonHardwareButtonPress() {\n\t\tconst { onClose } = this.props;\n\t\tconst { handleHardwareButtonPress } = this.state;\n\n\t\tif ( handleHardwareButtonPress && handleHardwareButtonPress() ) {\n\t\t\treturn;\n\t\t}\n\t\tif ( onClose ) {\n\t\t\treturn onClose();\n\t\t}\n\t}\n\n\tgetContentStyle() {\n\t\tconst { safeAreaBottomInset } = this.state;\n\t\treturn {\n\t\t\tpaddingBottom:\n\t\t\t\t( safeAreaBottomInset || 0 ) +\n\t\t\t\tstyles.scrollableContent.paddingBottom,\n\t\t};\n\t}\n\n\trender() {\n\t\tconst {\n\t\t\ttitle = '',\n\t\t\tisVisible,\n\t\t\tleftButton,\n\t\t\trightButton,\n\t\t\theader,\n\t\t\thideHeader,\n\t\t\tstyle = {},\n\t\t\tcontentStyle = {},\n\t\t\tgetStylesFromColorScheme,\n\t\t\tchildren,\n\t\t\twithHeaderSeparator = false,\n\t\t\thasNavigation,\n\t\t\tonDismiss,\n\t\t\t...rest\n\t\t} = this.props;\n\t\tconst {\n\t\t\tmaxHeight,\n\t\t\tbounces,\n\t\t\tsafeAreaBottomInset,\n\t\t\tsafeAreaTopInset,\n\t\t\tisScrolling,\n\t\t\tscrollEnabled,\n\t\t\tisMaxHeightSet,\n\t\t\tisFullScreen,\n\t\t} = this.state;\n\n\t\tconst panResponder = PanResponder.create( {\n\t\t\tonMoveShouldSetPanResponder: ( evt, gestureState ) => {\n\t\t\t\t// 'swiping-to-close' option is temporarily and partially disabled\n\t\t\t\t// on Android ( swipe / drag is still available in the top most area - near drag indicator).\n\t\t\t\tif ( Platform.OS === 'ios' ) {\n\t\t\t\t\t// Activates swipe down over child Touchables if the swipe is long enough.\n\t\t\t\t\t// With this we can adjust sensibility on the swipe vs tap gestures.\n\t\t\t\t\tif ( gestureState.dy > 3 && ! bounces ) {\n\t\t\t\t\t\tgestureState.dy = 0;\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t} );\n\n\t\tconst backgroundStyle = getStylesFromColorScheme(\n\t\t\tstyles.background,\n\t\t\tstyles.backgroundDark\n\t\t);\n\n\t\tconst bottomSheetHeaderTitleStyle = getStylesFromColorScheme(\n\t\t\tstyles.bottomSheetHeaderTitle,\n\t\t\tstyles.bottomSheetHeaderTitleDark\n\t\t);\n\n\t\tlet listStyle = {};\n\t\tif ( isFullScreen ) {\n\t\t\tlistStyle = { flexGrow: 1, flexShrink: 1 };\n\t\t} else if ( isMaxHeightSet ) {\n\t\t\tlistStyle = { maxHeight };\n\n\t\t\t// Allow setting a \"static\" height of the bottom sheet\n\t\t\t// by setting the min height to the max height.\n\t\t\tif ( this.props.setMinHeightToMaxHeight ) {\n\t\t\t\tlistStyle.minHeight = maxHeight;\n\t\t\t}\n\t\t}\n\n\t\tconst listProps = {\n\t\t\tdisableScrollViewPanResponder: true,\n\t\t\tbounces,\n\t\t\tonScroll: this.onScroll,\n\t\t\tonScrollBeginDrag: this.onScrollBeginDrag,\n\t\t\tonScrollEndDrag: this.onScrollEndDrag,\n\t\t\tscrollEventThrottle: 16,\n\t\t\tcontentContainerStyle: [\n\t\t\t\tstyles.content,\n\t\t\t\thideHeader && styles.emptyHeader,\n\t\t\t\tcontentStyle,\n\t\t\t\tisFullScreen && { flexGrow: 1 },\n\t\t\t],\n\t\t\tstyle: listStyle,\n\t\t\tsafeAreaBottomInset,\n\t\t\tscrollEnabled,\n\t\t\tautomaticallyAdjustContentInsets: false,\n\t\t};\n\n\t\tconst WrapperView = hasNavigation ? View : ScrollView;\n\n\t\tconst getHeader = () => (\n\t\t\t<>\n\t\t\t\t{ header || (\n\t\t\t\t\t<View style={ styles.bottomSheetHeader }>\n\t\t\t\t\t\t<View style={ styles.flex }>{ leftButton }</View>\n\t\t\t\t\t\t<Text\n\t\t\t\t\t\t\tstyle={ bottomSheetHeaderTitleStyle }\n\t\t\t\t\t\t\tmaxFontSizeMultiplier={ 3 }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ title }\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<View style={ styles.flex }>{ rightButton }</View>\n\t\t\t\t\t</View>\n\t\t\t\t) }\n\t\t\t\t{ withHeaderSeparator && <View style={ styles.separator } /> }\n\t\t\t</>\n\t\t);\n\n\t\tconst showDragIndicator = () => {\n\t\t\t// If iOS or not fullscreen show the drag indicator.\n\t\t\tif ( Platform.OS === 'ios' || ! this.state.isFullScreen ) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// Otherwise check the allowDragIndicator.\n\t\t\treturn this.props.allowDragIndicator;\n\t\t};\n\n\t\treturn (\n\t\t\t<Modal\n\t\t\t\tisVisible={ isVisible }\n\t\t\t\tstyle={ styles.bottomModal }\n\t\t\t\tanimationInTiming={ 400 }\n\t\t\t\tanimationOutTiming={ 300 }\n\t\t\t\tbackdropTransitionInTiming={ 50 }\n\t\t\t\tbackdropTransitionOutTiming={ 50 }\n\t\t\t\tbackdropOpacity={ 0.2 }\n\t\t\t\tonBackdropPress={ this.onCloseBottomSheet }\n\t\t\t\tonBackButtonPress={ this.onHardwareButtonPress }\n\t\t\t\tonSwipeComplete={ this.onCloseBottomSheet }\n\t\t\t\tonDismiss={ Platform.OS === 'ios' ? this.onDismiss : undefined }\n\t\t\t\tonModalHide={\n\t\t\t\t\tPlatform.OS === 'android' ? this.onDismiss : undefined\n\t\t\t\t}\n\t\t\t\tswipeDirection=\"down\"\n\t\t\t\tonMoveShouldSetResponder={\n\t\t\t\t\tscrollEnabled &&\n\t\t\t\t\tpanResponder.panHandlers.onMoveShouldSetResponder\n\t\t\t\t}\n\t\t\t\tonMoveShouldSetResponderCapture={\n\t\t\t\t\tscrollEnabled &&\n\t\t\t\t\tpanResponder.panHandlers.onMoveShouldSetResponderCapture\n\t\t\t\t}\n\t\t\t\tonAccessibilityEscape={ this.onCloseBottomSheet }\n\t\t\t\ttestID=\"bottom-sheet\"\n\t\t\t\thardwareAccelerated={ true }\n\t\t\t\tuseNativeDriverForBackdrop={ true }\n\t\t\t\t{ ...rest }\n\t\t\t>\n\t\t\t\t<KeyboardAvoidingView\n\t\t\t\t\tbehavior={ Platform.OS === 'ios' && 'padding' }\n\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t...backgroundStyle,\n\t\t\t\t\t\tborderColor: 'rgba(0, 0, 0, 0.1)',\n\t\t\t\t\t\tmarginTop:\n\t\t\t\t\t\t\tPlatform.OS === 'ios' && isFullScreen\n\t\t\t\t\t\t\t\t? safeAreaTopInset\n\t\t\t\t\t\t\t\t: 0,\n\t\t\t\t\t\tflex: isFullScreen ? 1 : undefined,\n\t\t\t\t\t\t...( Platform.OS === 'android' && isFullScreen\n\t\t\t\t\t\t\t? styles.backgroundFullScreen\n\t\t\t\t\t\t\t: {} ),\n\t\t\t\t\t\t...style,\n\t\t\t\t\t} }\n\t\t\t\t\tkeyboardVerticalOffset={ -safeAreaBottomInset }\n\t\t\t\t>\n\t\t\t\t\t<View\n\t\t\t\t\t\tstyle={ styles.header }\n\t\t\t\t\t\tonLayout={ this.onHeaderLayout }\n\t\t\t\t\t\ttestID={ `${ rest.testID || 'bottom-sheet' }-header` }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ showDragIndicator() && (\n\t\t\t\t\t\t\t<View style={ styles.dragIndicator } />\n\t\t\t\t\t\t) }\n\t\t\t\t\t\t{ ! hideHeader && getHeader() }\n\t\t\t\t\t</View>\n\t\t\t\t\t<WrapperView\n\t\t\t\t\t\t{ ...( hasNavigation\n\t\t\t\t\t\t\t? { style: listProps.style }\n\t\t\t\t\t\t\t: listProps ) }\n\t\t\t\t\t>\n\t\t\t\t\t\t<BottomSheetProvider\n\t\t\t\t\t\t\tvalue={ {\n\t\t\t\t\t\t\t\tshouldEnableBottomSheetScroll:\n\t\t\t\t\t\t\t\t\tthis.onShouldEnableScroll,\n\t\t\t\t\t\t\t\tshouldEnableBottomSheetMaxHeight:\n\t\t\t\t\t\t\t\t\tthis.onShouldSetBottomSheetMaxHeight,\n\t\t\t\t\t\t\t\tisBottomSheetContentScrolling: isScrolling,\n\t\t\t\t\t\t\t\tonHandleClosingBottomSheet:\n\t\t\t\t\t\t\t\t\tthis.onHandleClosingBottomSheet,\n\t\t\t\t\t\t\t\tonHandleHardwareButtonPress:\n\t\t\t\t\t\t\t\t\tthis.onHandleHardwareButtonPress,\n\t\t\t\t\t\t\t\tlistProps,\n\t\t\t\t\t\t\t\tsetIsFullScreen: this.setIsFullScreen,\n\t\t\t\t\t\t\t\tsafeAreaBottomInset,\n\t\t\t\t\t\t\t\tmaxHeight,\n\t\t\t\t\t\t\t\tisMaxHeightSet,\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ hasNavigation ? (\n\t\t\t\t\t\t\t\t<>{ children }</>\n\t\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t\t<TouchableHighlight accessible={ false }>\n\t\t\t\t\t\t\t\t\t<>{ children }</>\n\t\t\t\t\t\t\t\t</TouchableHighlight>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</BottomSheetProvider>\n\t\t\t\t\t\t{ ! hasNavigation && (\n\t\t\t\t\t\t\t<View\n\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\theight:\n\t\t\t\t\t\t\t\t\t\tsafeAreaBottomInset ||\n\t\t\t\t\t\t\t\t\t\tstyles.scrollableContent.paddingBottom,\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</WrapperView>\n\t\t\t\t</KeyboardAvoidingView>\n\t\t\t</Modal>\n\t\t);\n\t}\n}\n\nfunction getWidth() {\n\treturn Math.min(\n\t\tDimensions.get( 'window' ).width,\n\t\tstyles.background.maxWidth\n\t);\n}\n\nconst ThemedBottomSheet = withPreferredColorScheme( BottomSheet );\n\nThemedBottomSheet.getWidth = getWidth;\nThemedBottomSheet.Button = Button;\nThemedBottomSheet.Cell = Cell;\nThemedBottomSheet.SubSheet = BottomSheetSubSheet;\nThemedBottomSheet.NavBar = NavBar;\nThemedBottomSheet.CyclePickerCell = CyclePickerCell;\nThemedBottomSheet.PickerCell = PickerCell;\nThemedBottomSheet.SwitchCell = SwitchCell;\nThemedBottomSheet.RangeCell = RangeCell;\nThemedBottomSheet.ColorCell = ColorCell;\nThemedBottomSheet.LinkCell = LinkCell;\nThemedBottomSheet.LinkSuggestionItemCell = LinkSuggestionItemCell;\nThemedBottomSheet.RadioCell = RadioCell;\nThemedBottomSheet.NavigationScreen = NavigationScreen;\nThemedBottomSheet.NavigationContainer = NavigationContainer;\n\nexport default ThemedBottomSheet;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAYA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,oBAAA,GAAAD,sBAAA,CAAAF,OAAA;AAKA,IAAAI,kBAAA,GAAAJ,OAAA;AAIA,IAAAK,QAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAKA,IAAAO,OAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,OAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,KAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,gBAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,WAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,WAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,UAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,UAAA,GAAAZ,sBAAA,CAAAF,OAAA;AACA,IAAAe,SAAA,GAAAb,sBAAA,CAAAF,OAAA;AACA,IAAAgB,uBAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,UAAA,GAAAf,sBAAA,CAAAF,OAAA;AACA,IAAAkB,iBAAA,GAAAhB,sBAAA,CAAAF,OAAA;AACA,IAAAmB,oBAAA,GAAAjB,sBAAA,CAAAF,OAAA;AACA,IAAAoB,qBAAA,GAAAlB,sBAAA,CAAAF,OAAA;AACA,IAAAqB,SAAA,GAAAnB,sBAAA,CAAAF,OAAA;AACA,IAAAsB,OAAA,GAAApB,sBAAA,CAAAF,OAAA;AACA,IAAAuB,mBAAA,GAAAvB,OAAA;AA/CA;AACA;AACA;;AAgBA;AACA;AACA;;AAQA;AACA;AACA;;AAmBA,MAAMwB,wBAAwB,GAAGC,4BAAe,CAACC,OAAO,CAACC,aAAa;AAEtE,MAAMC,WAAW,SAASC,kBAAS,CAAC;EACnCC,WAAWA,CAAA,EAAG;IACb,KAAK,CAAE,GAAGC,SAAU,CAAC;IACrB,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACC,IAAI,CAAE,IAAK,CAAC;IACtE,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACD,IAAI,CAAE,IAAK,CAAC;IAC1C,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAE,IAAK,CAAC;IAChD,IAAI,CAACG,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACH,IAAI,CAAE,IAAK,CAAC;IAClE,IAAI,CAACI,SAAS,GAAG,IAAI,CAACA,SAAS,CAACJ,IAAI,CAAE,IAAK,CAAC;IAC5C,IAAI,CAACK,+BAA+B,GACnC,IAAI,CAACA,+BAA+B,CAACL,IAAI,CAAE,IAAK,CAAC;IAElD,IAAI,CAACM,eAAe,GAAG,IAAI,CAACA,eAAe,CAACN,IAAI,CAAE,IAAK,CAAC;IAExD,IAAI,CAACO,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACP,IAAI,CAAE,IAAK,CAAC;IAC9D,IAAI,CAACQ,cAAc,GAAG,IAAI,CAACA,cAAc,CAACR,IAAI,CAAE,IAAK,CAAC;IACtD,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACT,IAAI,CAAE,IAAK,CAAC;IAC9D,IAAI,CAACU,0BAA0B,GAC9B,IAAI,CAACA,0BAA0B,CAACV,IAAI,CAAE,IAAK,CAAC;IAC7C,IAAI,CAACW,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACX,IAAI,CAAE,IAAK,CAAC;IACpE,IAAI,CAACY,2BAA2B,GAC/B,IAAI,CAACA,2BAA2B,CAACZ,IAAI,CAAE,IAAK,CAAC;IAC9C,IAAI,CAACa,YAAY,GAAG,IAAI,CAACA,YAAY,CAACb,IAAI,CAAE,IAAK,CAAC;IAClD,IAAI,CAACc,YAAY,GAAG,IAAI,CAACA,YAAY,CAACd,IAAI,CAAE,IAAK,CAAC;IAElD,IAAI,CAACe,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,2BAA2B,GAAG,KAAK;IAExC,IAAI,CAACC,KAAK,GAAG;MACZC,mBAAmB,EAAE,CAAC;MACtBC,gBAAgB,EAAE,CAAC;MACnBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE,IAAI;MACnBtB,WAAW,EAAE,KAAK;MAClBuB,wBAAwB,EAAE,IAAI;MAC9BC,yBAAyB,EAAE,IAAI;MAC/BC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,IAAI,CAACC,KAAK,CAACD,YAAY,IAAI;IAC1C,CAAC;EACF;EAEAf,YAAYA,CAAEiB,CAAC,EAAG;IACjB,IAAK,CAAE,IAAI,CAACD,KAAK,CAACE,SAAS,EAAG;MAC7B;IACD;IAEA,MAAM;MAAEC;IAAO,CAAC,GAAGF,CAAC,CAACG,cAAc;IACnC,IAAI,CAACjB,cAAc,GAAGgB,MAAM;IAC5B,IAAI,CAACE,8BAA8B,CAAEJ,CAAE,CAAC;IACxC,IAAI,CAACK,cAAc,CAAC,CAAC;IACrB,IAAI,CAACN,KAAK,CAACO,cAAc,GAAG,CAAC;EAC9B;EAEAtB,YAAYA,CAAEgB,CAAC,EAAG;IACjB,IAAK,CAAE,IAAI,CAACD,KAAK,CAACE,SAAS,EAAG;MAC7B;IACD;IAEA,IAAI,CAACf,cAAc,GAAG,CAAC;IACvB,IAAI,CAACkB,8BAA8B,CAAEJ,CAAE,CAAC;IACxC,IAAI,CAACK,cAAc,CAAC,CAAC;IACrB,IAAI,CAACN,KAAK,CAACQ,cAAc,GAAG,CAAC;EAC9B;EAEAH,8BAA8BA,CAAEI,KAAK,EAAG;IACvC,MAAM;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAGF,KAAK;IAElC,IAAKC,QAAQ,IAAIC,MAAM,EAAG;MACzB;MACA;MACA,MAAMC,eAAe,GAAG;QACvB;QACAF,QAAQ,EAAEA,QAAQ,GAAG,EAAE,GAAGA,QAAQ,GAAG,EAAE;QACvCG,IAAI,EAAElD,4BAAe,CAACmD,KAAK,CAAEH,MAAM,CAAE,IAAI;MAC1C,CAAC;MACD,MAAMI,eAAe,GAAG;QACvBL,QAAQ,EAAEE,eAAe,CAACF,QAAQ;QAClCM,MAAM,EAAEJ,eAAe;QACvBK,MAAM,EAAE;UACP,GAAGL,eAAe;UAClBM,QAAQ,EAAEvD,4BAAe,CAACwD,UAAU,CAACC;QACtC,CAAC;QACDC,MAAM,EAAE;UACP,GAAGT,eAAe;UAClBM,QAAQ,EAAEvD,4BAAe,CAACwD,UAAU,CAACC;QACtC;MACD,CAAC;MACD,IAAI,CAAC/B,2BAA2B,GAAG,KAAK;MACxC1B,4BAAe,CAAC2D,aAAa,CAAEP,eAAe,EAAE,MAAM;QACrD,IAAI,CAAC1B,2BAA2B,GAAG,IAAI;MACxC,CAAE,CAAC;MACH,IAAI,CAACD,mBAAmB,GAAG2B,eAAe;IAC3C,CAAC,MAAM;MACN;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;EAEF;EAEAQ,6BAA6BA,CAAE;IAAEC;EAAuB,CAAC,EAAG;IAC3D;IACA,IACCC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzB,IAAI,CAACtC,mBAAmB,IACxB,CAAE,IAAI,CAACC,2BAA2B,EACjC;MACD;IACD;IAEA,MAAM0B,eAAe,GAAGS,sBAAsB,GAC3C,IAAI,CAACpC,mBAAmB,IAAI1B,wBAAwB,GACpDA,wBAAwB;IAE3B,IAAI,CAAC2B,2BAA2B,GAAG,KAAK;IACxC1B,4BAAe,CAAC2D,aAAa,CAAEP,eAAe,EAAE,MAAM;MACrD,IAAI,CAAC1B,2BAA2B,GAAG,IAAI;IACxC,CAAE,CAAC;IACH,IAAI,CAACD,mBAAmB,GAAG2B,eAAe;EAC3C;EAEAY,iBAAiBA,CAAA,EAAG;IACnBC,4BAAQ,CAACC,4BAA4B,CAAC,CAAC,CAACC,IAAI,CAC3C,IAAI,CAAC5D,sBACN,CAAC;IAED,IAAKuD,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAG;MAChC,IAAI,CAACK,8BAA8B,GAAG,IAAAC,8CAA2B,EAChE,MAAM;QACL,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAAC,CAAC;MACrB,CACD,CAAC;IACF;IAEA,IAAI,CAACC,4BAA4B,GAAGC,uBAAU,CAACC,gBAAgB,CAC9D,QAAQ,EACR,IAAI,CAAC1D,kBACN,CAAC;;IAED;IACA;IACA,IAAI,CAAC2D,oBAAoB,GAAGC,qBAAQ,CAACC,WAAW,CAC/Cd,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,kBAAkB,GAAG,iBAAiB,EAC9D,IAAI,CAAC1C,YACN,CAAC;IACD,IAAI,CAACwD,oBAAoB,GAAGF,qBAAQ,CAACC,WAAW,CAC/Cd,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,kBAAkB,GAAG,iBAAiB,EAC9D,IAAI,CAACzC,YACN,CAAC;IAED,IAAI,CAACwD,yBAAyB,GAAGb,4BAAQ,CAACQ,gBAAgB,CACzD,oCAAoC,EACpC,IAAI,CAAClE,sBACN,CAAC;IACD,IAAI,CAACoC,cAAc,CAAC,CAAC;EACtB;EAEAoC,oBAAoBA,CAAA,EAAG;IACtB,IAAI,CAACR,4BAA4B,CAACS,MAAM,CAAC,CAAC;IAC1C,IAAI,CAACN,oBAAoB,CAACM,MAAM,CAAC,CAAC;IAClC,IAAI,CAACH,oBAAoB,CAACG,MAAM,CAAC,CAAC;IAClC,IAAK,IAAI,CAACZ,8BAA8B,EAAG;MAC1C,IAAI,CAACA,8BAA8B,CAACY,MAAM,CAAC,CAAC;IAC7C;IAEA,IAAK,IAAI,CAAC3C,KAAK,CAACE,SAAS,EAAG;MAC3B,IAAA0C,0CAAuB,EAAC,CAAC;IAC1B;IAEA,IAAK,IAAI,CAACH,yBAAyB,KAAK,IAAI,EAAG;MAC9C;IACD;IACA,IAAI,CAACA,yBAAyB,CAACE,MAAM,CAAC,CAAC;IACvC,IAAI,CAACF,yBAAyB,GAAG,IAAI;EACtC;EAEAvE,sBAAsBA,CAAE2E,MAAM,EAAG;IAChC,MAAM;MAAEtD,mBAAmB;MAAEC;IAAiB,CAAC,GAAG,IAAI,CAACF,KAAK;IAC5D,IAAK,IAAI,CAACmD,yBAAyB,KAAK,IAAI,EAAG;MAC9C;IACD;IACA,MAAM;MAAEK;IAAe,CAAC,GAAGD,MAAM;IACjC,IACCtD,mBAAmB,KAAKuD,cAAc,CAACC,MAAM,IAC7CvD,gBAAgB,KAAKsD,cAAc,CAACE,GAAG,EACtC;MACD,IAAI,CAACC,QAAQ,CAAE;QACd1D,mBAAmB,EAAEuD,cAAc,CAACC,MAAM;QAC1CvD,gBAAgB,EAAEsD,cAAc,CAACE;MAClC,CAAE,CAAC;IACJ;EACD;EAEA1C,cAAcA,CAAA,EAAG;IAChB,MAAM;MAAEH,MAAM;MAAE+C;IAAM,CAAC,GAAGf,uBAAU,CAACgB,GAAG,CAAE,QAAS,CAAC;IACpD,MAAM;MAAE5D;IAAoB,CAAC,GAAG,IAAI,CAACD,KAAK;IAC1C,MAAM8D,eAAe,GACpB3B,qBAAQ,CAACC,EAAE,KAAK,SAAS,GAAG2B,sBAAS,CAACC,aAAa,GAAG,CAAC;;IAExD;IACA,MAAMC,yBAAyB,GAC9B,IAAI,IACFpB,uBAAU,CAACgB,GAAG,CAAE,QAAS,CAAC,CAAChD,MAAM,GAClC,IAAI,CAAChB,cAAc,GACnBiE,eAAe,GACf,IAAI,CAAClE,YAAY,CAAE;;IAErB;IACA,IAAKgE,KAAK,GAAG/C,MAAM,EAAG;MACrB,IAAI,CAAC8C,QAAQ,CAAE;QACdvD,SAAS,EAAE8D,IAAI,CAACC,GAAG,CAClB,IAAI,GAAGtD,MAAM,GAAG,IAAI,CAACjB,YAAY,EACjCqE,yBACD;MACD,CAAE,CAAC;MACH;IACD,CAAC,MAAM;MACN,IAAI,CAACN,QAAQ,CAAE;QACdvD,SAAS,EAAE8D,IAAI,CAACC,GAAG,CAClBtD,MAAM,GAAG,IAAI,GAAGZ,mBAAmB,GAAG,IAAI,CAACL,YAAY,EACvDqE,yBACD;MACD,CAAE,CAAC;IACJ;EACD;EAEA7E,kBAAkBA,CAAA,EAAG;IACpB,IAAI,CAAC4B,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC2C,QAAQ,CAAE;MAAExD,OAAO,EAAE;IAAM,CAAE,CAAC;EACpC;EAEAd,cAAcA,CAAE;IAAE+E;EAAY,CAAC,EAAG;IACjC,MAAM;MAAEvD;IAAO,CAAC,GAAGuD,WAAW,CAACC,MAAM;IACrC;IACA;IACA,IACC,IAAI,CAACzE,YAAY,KAAK,CAAC,IACvBsE,IAAI,CAACI,KAAK,CAAEzD,MAAO,CAAC,KAAKqD,IAAI,CAACI,KAAK,CAAE,IAAI,CAAC1E,YAAa,CAAC,EACvD;MACD,IAAI,CAACqC,6BAA6B,CAAE;QACnCC,sBAAsB,EAAE;MACzB,CAAE,CAAC;IACJ;IACA,IAAI,CAACtC,YAAY,GAAGiB,MAAM;IAC1B,IAAI,CAACG,cAAc,CAAC,CAAC;EACtB;EAEAuD,eAAeA,CAAE;IAAEC,iBAAiB;IAAEC,aAAa;IAAEC;EAAY,CAAC,EAAG;IACpE,OACCF,iBAAiB,CAAC3D,MAAM,GAAG4D,aAAa,CAACE,CAAC,IAC1CD,WAAW,CAAC7D,MAAM,GAAG4D,aAAa,CAACE,CAAC;EAEtC;EAEAC,YAAYA,CAAE;IAAEH;EAAc,CAAC,EAAG;IACjC,OAAOA,aAAa,CAACE,CAAC,GAAG,EAAE;EAC5B;EAEA7F,QAAQA,CAAE;IAAEsF;EAAY,CAAC,EAAG;IAC3B,IAAK,IAAI,CAACQ,YAAY,CAAER,WAAY,CAAC,EAAG;MACvC,IAAI,CAACT,QAAQ,CAAE;QAAExD,OAAO,EAAE;MAAM,CAAE,CAAC;IACpC,CAAC,MAAM;MACN,IAAI,CAACwD,QAAQ,CAAE;QAAExD,OAAO,EAAE;MAAK,CAAE,CAAC;IACnC;EACD;EAEAlB,SAASA,CAAA,EAAG;IACX,MAAM;MAAEA;IAAU,CAAC,GAAG,IAAI,CAACyB,KAAK;;IAEhC;IACA,IAAA4C,0CAAuB,EAAC,CAAC;IAEzB,IAAKrE,SAAS,EAAG;MAChBA,SAAS,CAAC,CAAC;IACZ;IAEA,IAAI,CAACK,kBAAkB,CAAC,CAAC;EAC1B;EAEAN,oBAAoBA,CAAE6F,KAAK,EAAG;IAC7B,IAAI,CAAClB,QAAQ,CAAE;MAAEtD,aAAa,EAAEwE;IAAM,CAAE,CAAC;EAC1C;EAEA3F,+BAA+BA,CAAE2F,KAAK,EAAG;IACxC,IAAI,CAAClB,QAAQ,CAAE;MAAEnD,cAAc,EAAEqE;IAAM,CAAE,CAAC;EAC3C;EAEA9F,WAAWA,CAAE8F,KAAK,EAAG;IACpB,IAAI,CAAClB,QAAQ,CAAE;MAAE5E,WAAW,EAAE8F;IAAM,CAAE,CAAC;EACxC;EAEAtF,0BAA0BA,CAAEuF,MAAM,EAAG;IACpC,IAAI,CAACnB,QAAQ,CAAE;MAAErD,wBAAwB,EAAEwE;IAAO,CAAE,CAAC;EACtD;EAEArF,2BAA2BA,CAAEqF,MAAM,EAAG;IACrC,IAAI,CAACnB,QAAQ,CAAE;MAAEpD,yBAAyB,EAAEuE;IAAO,CAAE,CAAC;EACvD;EAEAxF,kBAAkBA,CAAA,EAAG;IACpB,MAAM;MAAEqD;IAAQ,CAAC,GAAG,IAAI,CAACjC,KAAK;IAC9B,MAAM;MAAEJ;IAAyB,CAAC,GAAG,IAAI,CAACN,KAAK;IAC/C,IAAKM,wBAAwB,EAAG;MAC/BA,wBAAwB,CAAC,CAAC;MAC1B,IAAI,CAACf,0BAA0B,CAAE,IAAK,CAAC;IACxC;IACA,IAAKoD,OAAO,EAAG;MACdA,OAAO,CAAC,CAAC;IACV;IACA,IAAI,CAACzD,+BAA+B,CAAE,IAAK,CAAC;EAC7C;EAEAC,eAAeA,CAAEsB,YAAY,EAAG;IAC/B,IAAKA,YAAY,KAAK,IAAI,CAACT,KAAK,CAACS,YAAY,EAAG;MAC/C,IAAKA,YAAY,EAAG;QACnB,IAAI,CAACkD,QAAQ,CAAE;UAAElD,YAAY;UAAED,cAAc,EAAE;QAAM,CAAE,CAAC;MACzD,CAAC,MAAM;QACN,IAAI,CAACmD,QAAQ,CAAE;UAAElD,YAAY;UAAED,cAAc,EAAE;QAAK,CAAE,CAAC;MACxD;IACD;EACD;EAEAhB,qBAAqBA,CAAA,EAAG;IACvB,MAAM;MAAEmD;IAAQ,CAAC,GAAG,IAAI,CAACjC,KAAK;IAC9B,MAAM;MAAEH;IAA0B,CAAC,GAAG,IAAI,CAACP,KAAK;IAEhD,IAAKO,yBAAyB,IAAIA,yBAAyB,CAAC,CAAC,EAAG;MAC/D;IACD;IACA,IAAKoC,OAAO,EAAG;MACd,OAAOA,OAAO,CAAC,CAAC;IACjB;EACD;EAEAoC,eAAeA,CAAA,EAAG;IACjB,MAAM;MAAE9E;IAAoB,CAAC,GAAG,IAAI,CAACD,KAAK;IAC1C,OAAO;MACNgF,aAAa,EACZ,CAAE/E,mBAAmB,IAAI,CAAC,IAC1BgF,eAAM,CAACC,iBAAiB,CAACF;IAC3B,CAAC;EACF;EAEAG,MAAMA,CAAA,EAAG;IACR,MAAM;MACLC,KAAK,GAAG,EAAE;MACVxE,SAAS;MACTyE,UAAU;MACVC,WAAW;MACXC,MAAM;MACNC,UAAU;MACVC,KAAK,GAAG,CAAC,CAAC;MACVC,YAAY,GAAG,CAAC,CAAC;MACjBC,wBAAwB;MACxBC,QAAQ;MACRC,mBAAmB,GAAG,KAAK;MAC3BC,aAAa;MACb7G,SAAS;MACT,GAAG8G;IACJ,CAAC,GAAG,IAAI,CAACrF,KAAK;IACd,MAAM;MACLN,SAAS;MACTD,OAAO;MACPF,mBAAmB;MACnBC,gBAAgB;MAChBnB,WAAW;MACXsB,aAAa;MACbG,cAAc;MACdC;IACD,CAAC,GAAG,IAAI,CAACT,KAAK;IAEd,MAAMgG,YAAY,GAAGC,yBAAY,CAACtE,MAAM,CAAE;MACzCuE,2BAA2B,EAAEA,CAAEC,GAAG,EAAEC,YAAY,KAAM;QACrD;QACA;QACA,IAAKjE,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAG;UAC5B;UACA;UACA,IAAKgE,YAAY,CAACC,EAAE,GAAG,CAAC,IAAI,CAAElG,OAAO,EAAG;YACvCiG,YAAY,CAACC,EAAE,GAAG,CAAC;YACnB,OAAO,IAAI;UACZ;QACD;QACA,OAAO,KAAK;MACb;IACD,CAAE,CAAC;IAEH,MAAMC,eAAe,GAAGX,wBAAwB,CAC/CV,eAAM,CAACsB,UAAU,EACjBtB,eAAM,CAACuB,cACR,CAAC;IAED,MAAMC,2BAA2B,GAAGd,wBAAwB,CAC3DV,eAAM,CAACyB,sBAAsB,EAC7BzB,eAAM,CAAC0B,0BACR,CAAC;IAED,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAKnG,YAAY,EAAG;MACnBmG,SAAS,GAAG;QAAEC,QAAQ,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;IAC3C,CAAC,MAAM,IAAKtG,cAAc,EAAG;MAC5BoG,SAAS,GAAG;QAAExG;MAAU,CAAC;;MAEzB;MACA;MACA,IAAK,IAAI,CAACM,KAAK,CAACqG,uBAAuB,EAAG;QACzCH,SAAS,CAACI,SAAS,GAAG5G,SAAS;MAChC;IACD;IAEA,MAAM6G,SAAS,GAAG;MACjBC,6BAA6B,EAAE,IAAI;MACnC/G,OAAO;MACPrB,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBqI,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,mBAAmB,EAAE,EAAE;MACvBC,qBAAqB,EAAE,CACtBrC,eAAM,CAACsC,OAAO,EACd/B,UAAU,IAAIP,eAAM,CAACuC,WAAW,EAChC9B,YAAY,EACZjF,YAAY,IAAI;QAAEoG,QAAQ,EAAE;MAAE,CAAC,CAC/B;MACDpB,KAAK,EAAEmB,SAAS;MAChB3G,mBAAmB;MACnBI,aAAa;MACboH,gCAAgC,EAAE;IACnC,CAAC;IAED,MAAMC,WAAW,GAAG5B,aAAa,GAAG6B,iBAAI,GAAGC,uBAAU;IAErD,MAAMC,SAAS,GAAGA,CAAA,KACjB,IAAAC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGzC,MAAM,IACP,IAAAuC,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAgL,IAAI;MAAClC,KAAK,EAAGR,eAAM,CAACgD;IAAmB,GACvC,IAAAH,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAgL,IAAI;MAAClC,KAAK,EAAGR,eAAM,CAACiD;IAAM,GAAG7C,UAAkB,CAAC,EACjD,IAAAyC,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAwL,IAAI;MACJ1C,KAAK,EAAGgB,2BAA6B;MACrC2B,qBAAqB,EAAG;IAAG,GAEzBhD,KACG,CAAC,EACP,IAAA0C,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAgL,IAAI;MAAClC,KAAK,EAAGR,eAAM,CAACiD;IAAM,GAAG5C,WAAmB,CAC5C,CACN,EACCO,mBAAmB,IAAI,IAAAiC,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAgL,IAAI;MAAClC,KAAK,EAAGR,eAAM,CAACoD;IAAW,CAAE,CAC1D,CACF;IAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC/B;MACA,IAAKnG,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI,CAAE,IAAI,CAACpC,KAAK,CAACS,YAAY,EAAG;QACzD,OAAO,IAAI;MACZ;;MAEA;MACA,OAAO,IAAI,CAACC,KAAK,CAAC6H,kBAAkB;IACrC,CAAC;IAED,OACC,IAAAT,MAAA,CAAAC,aAAA,EAAClL,iBAAA,CAAA2L,OAAK;MACL5H,SAAS,EAAGA,SAAW;MACvB6E,KAAK,EAAGR,eAAM,CAACwD,WAAa;MAC5BC,iBAAiB,EAAG,GAAK;MACzBC,kBAAkB,EAAG,GAAK;MAC1BC,0BAA0B,EAAG,EAAI;MACjCC,2BAA2B,EAAG,EAAI;MAClCC,eAAe,EAAG,GAAK;MACvBC,eAAe,EAAG,IAAI,CAACzJ,kBAAoB;MAC3C0J,iBAAiB,EAAG,IAAI,CAACxJ,qBAAuB;MAChDyJ,eAAe,EAAG,IAAI,CAAC3J,kBAAoB;MAC3CL,SAAS,EAAGkD,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,IAAI,CAACnD,SAAS,GAAGiK,SAAW;MAChEC,WAAW,EACVhH,qBAAQ,CAACC,EAAE,KAAK,SAAS,GAAG,IAAI,CAACnD,SAAS,GAAGiK,SAC7C;MACDE,cAAc,EAAC,MAAM;MACrBC,wBAAwB,EACvBhJ,aAAa,IACb2F,YAAY,CAACsD,WAAW,CAACD,wBACzB;MACDE,+BAA+B,EAC9BlJ,aAAa,IACb2F,YAAY,CAACsD,WAAW,CAACC,+BACzB;MACDC,qBAAqB,EAAG,IAAI,CAAClK,kBAAoB;MACjDmK,MAAM,EAAC,cAAc;MACrBC,mBAAmB,EAAG,IAAM;MAC5BC,0BAA0B,EAAG,IAAM;MAAA,GAC9B5D;IAAI,GAET,IAAA+B,MAAA,CAAAC,aAAA,EAAC/J,qBAAA,CAAAwK,OAAoB;MACpBoB,QAAQ,EAAGzH,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI,SAAW;MAC/CqD,KAAK,EAAG;QACP,GAAGa,eAAe;QAClBuD,WAAW,EAAE,oBAAoB;QACjCC,SAAS,EACR3H,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI3B,YAAY,GAClCP,gBAAgB,GAChB,CAAC;QACLgI,IAAI,EAAEzH,YAAY,GAAG,CAAC,GAAGyI,SAAS;QAClC,IAAK/G,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAI3B,YAAY,GAC3CwE,eAAM,CAAC8E,oBAAoB,GAC3B,CAAC,CAAC,CAAE;QACP,GAAGtE;MACJ,CAAG;MACHuE,sBAAsB,EAAG,CAAC/J;IAAqB,GAE/C,IAAA6H,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAgL,IAAI;MACJlC,KAAK,EAAGR,eAAM,CAACM,MAAQ;MACvB0E,QAAQ,EAAG,IAAI,CAAC5K,cAAgB;MAChCoK,MAAM,EAAI,GAAG1D,IAAI,CAAC0D,MAAM,IAAI,cAAgB;IAAU,GAEpDnB,iBAAiB,CAAC,CAAC,IACpB,IAAAR,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAgL,IAAI;MAAClC,KAAK,EAAGR,eAAM,CAACiF;IAAe,CAAE,CACtC,EACC,CAAE1E,UAAU,IAAIqC,SAAS,CAAC,CACvB,CAAC,EACP,IAAAC,MAAA,CAAAC,aAAA,EAACL,WAAW;MAAA,IACJ5B,aAAa,GACjB;QAAEL,KAAK,EAAEwB,SAAS,CAACxB;MAAM,CAAC,GAC1BwB,SAAS;IAAA,GAEZ,IAAAa,MAAA,CAAAC,aAAA,EAAC5J,mBAAA,CAAAgM,mBAAmB;MACnBtF,KAAK,EAAG;QACPuF,6BAA6B,EAC5B,IAAI,CAACpL,oBAAoB;QAC1BqL,gCAAgC,EAC/B,IAAI,CAACnL,+BAA+B;QACrCoL,6BAA6B,EAAEvL,WAAW;QAC1CQ,0BAA0B,EACzB,IAAI,CAACA,0BAA0B;QAChCE,2BAA2B,EAC1B,IAAI,CAACA,2BAA2B;QACjCwH,SAAS;QACT9H,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCc,mBAAmB;QACnBG,SAAS;QACTI;MACD;IAAG,GAEDsF,aAAa,GACd,IAAAgC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QAAIpC,QAAY,CAAC,GAEjB,IAAAkC,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAA4N,kBAAkB;MAACC,UAAU,EAAG;IAAO,GACvC,IAAA1C,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QAAIpC,QAAY,CACG,CAED,CAAC,EACpB,CAAEE,aAAa,IAChB,IAAAgC,MAAA,CAAAC,aAAA,EAACpL,YAAA,CAAAgL,IAAI;MACJlC,KAAK,EAAG;QACP5E,MAAM,EACLZ,mBAAmB,IACnBgF,eAAM,CAACC,iBAAiB,CAACF;MAC3B;IAAG,CACH,CAEU,CACQ,CAChB,CAAC;EAEV;AACD;AAEA,SAASyF,QAAQA,CAAA,EAAG;EACnB,OAAOvG,IAAI,CAACC,GAAG,CACdtB,uBAAU,CAACgB,GAAG,CAAE,QAAS,CAAC,CAACD,KAAK,EAChCqB,eAAM,CAACsB,UAAU,CAACmE,QACnB,CAAC;AACF;AAEA,MAAMC,iBAAiB,GAAG,IAAAC,iCAAwB,EAAEpM,WAAY,CAAC;AAEjEmM,iBAAiB,CAACF,QAAQ,GAAGA,QAAQ;AACrCE,iBAAiB,CAACE,MAAM,GAAGA,eAAM;AACjCF,iBAAiB,CAACG,IAAI,GAAGA,aAAI;AAC7BH,iBAAiB,CAACI,QAAQ,GAAGC,iBAAmB;AAChDL,iBAAiB,CAACM,MAAM,GAAGA,eAAM;AACjCN,iBAAiB,CAACO,eAAe,GAAGA,wBAAe;AACnDP,iBAAiB,CAACQ,UAAU,GAAGA,mBAAU;AACzCR,iBAAiB,CAACS,UAAU,GAAGA,mBAAU;AACzCT,iBAAiB,CAACU,SAAS,GAAGA,kBAAS;AACvCV,iBAAiB,CAACW,SAAS,GAAGA,kBAAS;AACvCX,iBAAiB,CAACY,QAAQ,GAAGA,iBAAQ;AACrCZ,iBAAiB,CAACa,sBAAsB,GAAGA,+BAAsB;AACjEb,iBAAiB,CAACc,SAAS,GAAGA,kBAAS;AACvCd,iBAAiB,CAACe,gBAAgB,GAAGA,yBAAgB;AACrDf,iBAAiB,CAACgB,mBAAmB,GAAGA,4BAAmB;AAAC,IAAAC,QAAA,GAE7CjB,iBAAiB;AAAAkB,OAAA,CAAArD,OAAA,GAAAoD,QAAA"}