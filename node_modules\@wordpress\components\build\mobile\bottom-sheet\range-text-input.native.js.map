{"version": 3, "names": ["_reactNative", "require", "_element", "_compose", "_i18n", "_utils", "_styles", "_interopRequireDefault", "_borderStyles", "isIOS", "Platform", "OS", "RangeTextInput", "Component", "constructor", "props", "announceCurrentValue", "bind", "onInputFocus", "onInputBlur", "handleChangePixelRatio", "onSubmitEditing", "onChangeText", "value", "defaultValue", "min", "decimalNum", "initialValue", "toFixed", "fontScale", "getFontScale", "state", "inputValue", "controlValue", "hasFocus", "componentDidMount", "appStateChangeSubscription", "AppState", "addEventListener", "componentWillUnmount", "remove", "clearTimeout", "timeoutAnnounceValue", "componentDidUpdate", "prevProps", "prevState", "setState", "validValue", "validateInput", "_valueTextInput", "focus", "PixelRatio", "nextAppState", "text", "max", "result", "Math", "removeNonDigit", "updateValue", "onChange", "textValue", "replace", "nativeEvent", "isNaN", "Number", "announcement", "sprintf", "__", "AccessibilityInfo", "announceForAccessibility", "render", "getStylesFromColorScheme", "children", "label", "textInputStyle", "styles", "textInput", "textInputDark", "textInputIOSStyle", "textInputIOS", "textInputIOSDark", "inputBorderStyles", "borderStyles", "borderStyle", "isSelected", "valueFinalStyle", "select", "android", "ios", "width", "borderRightWidth", "_react", "createElement", "TouchableWithoutFeedback", "onPress", "accessible", "View", "style", "textInputContainer", "TextInput", "accessibilityLabel", "ref", "c", "onFocus", "onBlur", "keyboardType", "returnKeyType", "numberOfLines", "toString", "pointerEvents", "Text", "ellipsizeMode", "_default", "withPreferredColorScheme", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/range-text-input.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tAccessibilityInfo,\n\tView,\n\tTextInput,\n\tPixelRatio,\n\tAppState,\n\tPlatform,\n\tText,\n\tTouchableWithoutFeedback,\n} from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { Component } from '@wordpress/element';\nimport { withPreferredColorScheme } from '@wordpress/compose';\nimport { __, sprintf } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { toFixed, removeNonDigit } from '../utils';\nimport styles from './styles.scss';\nimport borderStyles from './borderStyles.scss';\n\nconst isIOS = Platform.OS === 'ios';\n\nclass RangeTextInput extends Component {\n\tconstructor( props ) {\n\t\tsuper( props );\n\n\t\tthis.announceCurrentValue = this.announceCurrentValue.bind( this );\n\t\tthis.onInputFocus = this.onInputFocus.bind( this );\n\t\tthis.onInputBlur = this.onInputBlur.bind( this );\n\t\tthis.handleChangePixelRatio = this.handleChangePixelRatio.bind( this );\n\t\tthis.onSubmitEditing = this.onSubmitEditing.bind( this );\n\t\tthis.onChangeText = this.onChangeText.bind( this );\n\n\t\tconst { value, defaultValue, min, decimalNum } = props;\n\t\tconst initialValue = toFixed(\n\t\t\tvalue || defaultValue || min,\n\t\t\tdecimalNum\n\t\t);\n\n\t\tconst fontScale = this.getFontScale();\n\n\t\tthis.state = {\n\t\t\tfontScale,\n\t\t\tinputValue: initialValue,\n\t\t\tcontrolValue: initialValue,\n\t\t\thasFocus: false,\n\t\t};\n\t}\n\n\tcomponentDidMount() {\n\t\tthis.appStateChangeSubscription = AppState.addEventListener(\n\t\t\t'change',\n\t\t\tthis.handleChangePixelRatio\n\t\t);\n\t}\n\n\tcomponentWillUnmount() {\n\t\tthis.appStateChangeSubscription.remove();\n\t\tclearTimeout( this.timeoutAnnounceValue );\n\t}\n\n\tcomponentDidUpdate( prevProps, prevState ) {\n\t\tconst { value } = this.props;\n\t\tconst { hasFocus, inputValue } = this.state;\n\n\t\tif ( prevProps.value !== value ) {\n\t\t\tthis.setState( { inputValue: value } );\n\t\t}\n\n\t\tif ( prevState.hasFocus !== hasFocus ) {\n\t\t\tconst validValue = this.validateInput( inputValue );\n\t\t\tthis.setState( { inputValue: validValue } );\n\t\t}\n\n\t\tif ( ! prevState.hasFocus && hasFocus ) {\n\t\t\tthis._valueTextInput.focus();\n\t\t}\n\t}\n\n\tgetFontScale() {\n\t\treturn PixelRatio.getFontScale() < 1 ? 1 : PixelRatio.getFontScale();\n\t}\n\n\thandleChangePixelRatio( nextAppState ) {\n\t\tif ( nextAppState === 'active' ) {\n\t\t\tthis.setState( { fontScale: this.getFontScale() } );\n\t\t}\n\t}\n\n\tonInputFocus() {\n\t\tthis.setState( {\n\t\t\thasFocus: true,\n\t\t} );\n\t}\n\n\tonInputBlur() {\n\t\tconst { inputValue } = this.state;\n\t\tthis.onChangeText( `${ inputValue }` );\n\t\tthis.setState( {\n\t\t\thasFocus: false,\n\t\t} );\n\t}\n\n\tvalidateInput( text ) {\n\t\tconst { min, max, decimalNum } = this.props;\n\t\tlet result = min;\n\t\tif ( ! text ) {\n\t\t\treturn min;\n\t\t}\n\n\t\tif ( typeof text === 'number' ) {\n\t\t\tresult = Math.max( text, min );\n\t\t\treturn max ? Math.min( result, max ) : result;\n\t\t}\n\n\t\tresult = Math.max( removeNonDigit( text, decimalNum ), min );\n\t\treturn max ? Math.min( result, max ) : result;\n\t}\n\n\tupdateValue( value ) {\n\t\tconst { onChange } = this.props;\n\t\tconst validValue = this.validateInput( value );\n\n\t\tthis.announceCurrentValue( `${ validValue }` );\n\n\t\tonChange( validValue );\n\t}\n\n\tonChangeText( textValue ) {\n\t\tconst { decimalNum } = this.props;\n\t\tconst inputValue = removeNonDigit( textValue, decimalNum );\n\n\t\ttextValue = inputValue.replace( ',', '.' );\n\t\ttextValue = toFixed( textValue, decimalNum );\n\t\tconst value = this.validateInput( textValue );\n\t\tthis.setState( {\n\t\t\tinputValue,\n\t\t\tcontrolValue: value,\n\t\t} );\n\t\tthis.updateValue( value );\n\t}\n\n\tonSubmitEditing( { nativeEvent: { text } } ) {\n\t\tconst { decimalNum } = this.props;\n\t\tconst { inputValue } = this.state;\n\n\t\tif ( ! isNaN( Number( text ) ) ) {\n\t\t\ttext = toFixed( text.replace( ',', '.' ), decimalNum );\n\t\t\tconst validValue = this.validateInput( text );\n\n\t\t\tif ( inputValue !== validValue ) {\n\t\t\t\tthis.setState( { inputValue: validValue } );\n\t\t\t\tthis.announceCurrentValue( `${ validValue }` );\n\t\t\t\tthis.props.onChange( validValue );\n\t\t\t}\n\t\t}\n\t}\n\n\tannounceCurrentValue( value ) {\n\t\t/* translators: %s: current cell value. */\n\t\tconst announcement = sprintf( __( 'Current value is %s' ), value );\n\t\tAccessibilityInfo.announceForAccessibility( announcement );\n\t}\n\n\trender() {\n\t\tconst { getStylesFromColorScheme, children, label } = this.props;\n\t\tconst { fontScale, inputValue, hasFocus } = this.state;\n\n\t\tconst textInputStyle = getStylesFromColorScheme(\n\t\t\tstyles.textInput,\n\t\t\tstyles.textInputDark\n\t\t);\n\n\t\tconst textInputIOSStyle = getStylesFromColorScheme(\n\t\t\tstyles.textInputIOS,\n\t\t\tstyles.textInputIOSDark\n\t\t);\n\n\t\tconst inputBorderStyles = [\n\t\t\ttextInputStyle,\n\t\t\tborderStyles.borderStyle,\n\t\t\thasFocus && borderStyles.isSelected,\n\t\t];\n\n\t\tconst valueFinalStyle = [\n\t\t\tPlatform.select( {\n\t\t\t\tandroid: inputBorderStyles,\n\t\t\t\tios: textInputIOSStyle,\n\t\t\t} ),\n\t\t\t{\n\t\t\t\twidth: 50 * fontScale,\n\t\t\t\tborderRightWidth: children ? 1 : 0,\n\t\t\t},\n\t\t];\n\n\t\treturn (\n\t\t\t<TouchableWithoutFeedback\n\t\t\t\tonPress={ this.onInputFocus }\n\t\t\t\taccessible={ false }\n\t\t\t>\n\t\t\t\t<View\n\t\t\t\t\tstyle={ [\n\t\t\t\t\t\tstyles.textInputContainer,\n\t\t\t\t\t\tisIOS && inputBorderStyles,\n\t\t\t\t\t] }\n\t\t\t\t\taccessible={ false }\n\t\t\t\t>\n\t\t\t\t\t{ isIOS || hasFocus ? (\n\t\t\t\t\t\t<TextInput\n\t\t\t\t\t\t\taccessibilityLabel={ label }\n\t\t\t\t\t\t\tref={ ( c ) => ( this._valueTextInput = c ) }\n\t\t\t\t\t\t\tstyle={ valueFinalStyle }\n\t\t\t\t\t\t\tonChangeText={ this.onChangeText }\n\t\t\t\t\t\t\tonSubmitEditing={ this.onSubmitEditing }\n\t\t\t\t\t\t\tonFocus={ this.onInputFocus }\n\t\t\t\t\t\t\tonBlur={ this.onInputBlur }\n\t\t\t\t\t\t\tkeyboardType=\"numeric\"\n\t\t\t\t\t\t\treturnKeyType=\"done\"\n\t\t\t\t\t\t\tnumberOfLines={ 1 }\n\t\t\t\t\t\t\tdefaultValue={ `${ inputValue }` }\n\t\t\t\t\t\t\tvalue={ inputValue.toString() }\n\t\t\t\t\t\t\tpointerEvents={ hasFocus ? 'auto' : 'none' }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<Text\n\t\t\t\t\t\t\tstyle={ valueFinalStyle }\n\t\t\t\t\t\t\tnumberOfLines={ 1 }\n\t\t\t\t\t\t\tellipsizeMode=\"clip\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ inputValue }\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t) }\n\t\t\t\t\t{ children }\n\t\t\t\t</View>\n\t\t\t</TouchableWithoutFeedback>\n\t\t);\n\t}\n}\n\nexport default withPreferredColorScheme( RangeTextInput );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAcA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAKA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,aAAA,GAAAD,sBAAA,CAAAN,OAAA;AA1BA;AACA;AACA;;AAYA;AACA;AACA;;AAKA;AACA;AACA;;AAKA,MAAMQ,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;AAEnC,MAAMC,cAAc,SAASC,kBAAS,CAAC;EACtCC,WAAWA,CAAEC,KAAK,EAAG;IACpB,KAAK,CAAEA,KAAM,CAAC;IAEd,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACC,IAAI,CAAE,IAAK,CAAC;IAClE,IAAI,CAACC,YAAY,GAAG,IAAI,CAACA,YAAY,CAACD,IAAI,CAAE,IAAK,CAAC;IAClD,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,CAAE,IAAK,CAAC;IAChD,IAAI,CAACG,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACH,IAAI,CAAE,IAAK,CAAC;IACtE,IAAI,CAACI,eAAe,GAAG,IAAI,CAACA,eAAe,CAACJ,IAAI,CAAE,IAAK,CAAC;IACxD,IAAI,CAACK,YAAY,GAAG,IAAI,CAACA,YAAY,CAACL,IAAI,CAAE,IAAK,CAAC;IAElD,MAAM;MAAEM,KAAK;MAAEC,YAAY;MAAEC,GAAG;MAAEC;IAAW,CAAC,GAAGX,KAAK;IACtD,MAAMY,YAAY,GAAG,IAAAC,cAAO,EAC3BL,KAAK,IAAIC,YAAY,IAAIC,GAAG,EAC5BC,UACD,CAAC;IAED,MAAMG,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAErC,IAAI,CAACC,KAAK,GAAG;MACZF,SAAS;MACTG,UAAU,EAAEL,YAAY;MACxBM,YAAY,EAAEN,YAAY;MAC1BO,QAAQ,EAAE;IACX,CAAC;EACF;EAEAC,iBAAiBA,CAAA,EAAG;IACnB,IAAI,CAACC,0BAA0B,GAAGC,qBAAQ,CAACC,gBAAgB,CAC1D,QAAQ,EACR,IAAI,CAAClB,sBACN,CAAC;EACF;EAEAmB,oBAAoBA,CAAA,EAAG;IACtB,IAAI,CAACH,0BAA0B,CAACI,MAAM,CAAC,CAAC;IACxCC,YAAY,CAAE,IAAI,CAACC,oBAAqB,CAAC;EAC1C;EAEAC,kBAAkBA,CAAEC,SAAS,EAAEC,SAAS,EAAG;IAC1C,MAAM;MAAEtB;IAAM,CAAC,GAAG,IAAI,CAACR,KAAK;IAC5B,MAAM;MAAEmB,QAAQ;MAAEF;IAAW,CAAC,GAAG,IAAI,CAACD,KAAK;IAE3C,IAAKa,SAAS,CAACrB,KAAK,KAAKA,KAAK,EAAG;MAChC,IAAI,CAACuB,QAAQ,CAAE;QAAEd,UAAU,EAAET;MAAM,CAAE,CAAC;IACvC;IAEA,IAAKsB,SAAS,CAACX,QAAQ,KAAKA,QAAQ,EAAG;MACtC,MAAMa,UAAU,GAAG,IAAI,CAACC,aAAa,CAAEhB,UAAW,CAAC;MACnD,IAAI,CAACc,QAAQ,CAAE;QAAEd,UAAU,EAAEe;MAAW,CAAE,CAAC;IAC5C;IAEA,IAAK,CAAEF,SAAS,CAACX,QAAQ,IAAIA,QAAQ,EAAG;MACvC,IAAI,CAACe,eAAe,CAACC,KAAK,CAAC,CAAC;IAC7B;EACD;EAEApB,YAAYA,CAAA,EAAG;IACd,OAAOqB,uBAAU,CAACrB,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGqB,uBAAU,CAACrB,YAAY,CAAC,CAAC;EACrE;EAEAV,sBAAsBA,CAAEgC,YAAY,EAAG;IACtC,IAAKA,YAAY,KAAK,QAAQ,EAAG;MAChC,IAAI,CAACN,QAAQ,CAAE;QAAEjB,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC;MAAE,CAAE,CAAC;IACpD;EACD;EAEAZ,YAAYA,CAAA,EAAG;IACd,IAAI,CAAC4B,QAAQ,CAAE;MACdZ,QAAQ,EAAE;IACX,CAAE,CAAC;EACJ;EAEAf,WAAWA,CAAA,EAAG;IACb,MAAM;MAAEa;IAAW,CAAC,GAAG,IAAI,CAACD,KAAK;IACjC,IAAI,CAACT,YAAY,CAAG,GAAGU,UAAY,EAAE,CAAC;IACtC,IAAI,CAACc,QAAQ,CAAE;MACdZ,QAAQ,EAAE;IACX,CAAE,CAAC;EACJ;EAEAc,aAAaA,CAAEK,IAAI,EAAG;IACrB,MAAM;MAAE5B,GAAG;MAAE6B,GAAG;MAAE5B;IAAW,CAAC,GAAG,IAAI,CAACX,KAAK;IAC3C,IAAIwC,MAAM,GAAG9B,GAAG;IAChB,IAAK,CAAE4B,IAAI,EAAG;MACb,OAAO5B,GAAG;IACX;IAEA,IAAK,OAAO4B,IAAI,KAAK,QAAQ,EAAG;MAC/BE,MAAM,GAAGC,IAAI,CAACF,GAAG,CAAED,IAAI,EAAE5B,GAAI,CAAC;MAC9B,OAAO6B,GAAG,GAAGE,IAAI,CAAC/B,GAAG,CAAE8B,MAAM,EAAED,GAAI,CAAC,GAAGC,MAAM;IAC9C;IAEAA,MAAM,GAAGC,IAAI,CAACF,GAAG,CAAE,IAAAG,qBAAc,EAAEJ,IAAI,EAAE3B,UAAW,CAAC,EAAED,GAAI,CAAC;IAC5D,OAAO6B,GAAG,GAAGE,IAAI,CAAC/B,GAAG,CAAE8B,MAAM,EAAED,GAAI,CAAC,GAAGC,MAAM;EAC9C;EAEAG,WAAWA,CAAEnC,KAAK,EAAG;IACpB,MAAM;MAAEoC;IAAS,CAAC,GAAG,IAAI,CAAC5C,KAAK;IAC/B,MAAMgC,UAAU,GAAG,IAAI,CAACC,aAAa,CAAEzB,KAAM,CAAC;IAE9C,IAAI,CAACP,oBAAoB,CAAG,GAAG+B,UAAY,EAAE,CAAC;IAE9CY,QAAQ,CAAEZ,UAAW,CAAC;EACvB;EAEAzB,YAAYA,CAAEsC,SAAS,EAAG;IACzB,MAAM;MAAElC;IAAW,CAAC,GAAG,IAAI,CAACX,KAAK;IACjC,MAAMiB,UAAU,GAAG,IAAAyB,qBAAc,EAAEG,SAAS,EAAElC,UAAW,CAAC;IAE1DkC,SAAS,GAAG5B,UAAU,CAAC6B,OAAO,CAAE,GAAG,EAAE,GAAI,CAAC;IAC1CD,SAAS,GAAG,IAAAhC,cAAO,EAAEgC,SAAS,EAAElC,UAAW,CAAC;IAC5C,MAAMH,KAAK,GAAG,IAAI,CAACyB,aAAa,CAAEY,SAAU,CAAC;IAC7C,IAAI,CAACd,QAAQ,CAAE;MACdd,UAAU;MACVC,YAAY,EAAEV;IACf,CAAE,CAAC;IACH,IAAI,CAACmC,WAAW,CAAEnC,KAAM,CAAC;EAC1B;EAEAF,eAAeA,CAAE;IAAEyC,WAAW,EAAE;MAAET;IAAK;EAAE,CAAC,EAAG;IAC5C,MAAM;MAAE3B;IAAW,CAAC,GAAG,IAAI,CAACX,KAAK;IACjC,MAAM;MAAEiB;IAAW,CAAC,GAAG,IAAI,CAACD,KAAK;IAEjC,IAAK,CAAEgC,KAAK,CAAEC,MAAM,CAAEX,IAAK,CAAE,CAAC,EAAG;MAChCA,IAAI,GAAG,IAAAzB,cAAO,EAAEyB,IAAI,CAACQ,OAAO,CAAE,GAAG,EAAE,GAAI,CAAC,EAAEnC,UAAW,CAAC;MACtD,MAAMqB,UAAU,GAAG,IAAI,CAACC,aAAa,CAAEK,IAAK,CAAC;MAE7C,IAAKrB,UAAU,KAAKe,UAAU,EAAG;QAChC,IAAI,CAACD,QAAQ,CAAE;UAAEd,UAAU,EAAEe;QAAW,CAAE,CAAC;QAC3C,IAAI,CAAC/B,oBAAoB,CAAG,GAAG+B,UAAY,EAAE,CAAC;QAC9C,IAAI,CAAChC,KAAK,CAAC4C,QAAQ,CAAEZ,UAAW,CAAC;MAClC;IACD;EACD;EAEA/B,oBAAoBA,CAAEO,KAAK,EAAG;IAC7B;IACA,MAAM0C,YAAY,GAAG,IAAAC,aAAO,EAAE,IAAAC,QAAE,EAAE,qBAAsB,CAAC,EAAE5C,KAAM,CAAC;IAClE6C,8BAAiB,CAACC,wBAAwB,CAAEJ,YAAa,CAAC;EAC3D;EAEAK,MAAMA,CAAA,EAAG;IACR,MAAM;MAAEC,wBAAwB;MAAEC,QAAQ;MAAEC;IAAM,CAAC,GAAG,IAAI,CAAC1D,KAAK;IAChE,MAAM;MAAEc,SAAS;MAAEG,UAAU;MAAEE;IAAS,CAAC,GAAG,IAAI,CAACH,KAAK;IAEtD,MAAM2C,cAAc,GAAGH,wBAAwB,CAC9CI,eAAM,CAACC,SAAS,EAChBD,eAAM,CAACE,aACR,CAAC;IAED,MAAMC,iBAAiB,GAAGP,wBAAwB,CACjDI,eAAM,CAACI,YAAY,EACnBJ,eAAM,CAACK,gBACR,CAAC;IAED,MAAMC,iBAAiB,GAAG,CACzBP,cAAc,EACdQ,qBAAY,CAACC,WAAW,EACxBjD,QAAQ,IAAIgD,qBAAY,CAACE,UAAU,CACnC;IAED,MAAMC,eAAe,GAAG,CACvB3E,qBAAQ,CAAC4E,MAAM,CAAE;MAChBC,OAAO,EAAEN,iBAAiB;MAC1BO,GAAG,EAAEV;IACN,CAAE,CAAC,EACH;MACCW,KAAK,EAAE,EAAE,GAAG5D,SAAS;MACrB6D,gBAAgB,EAAElB,QAAQ,GAAG,CAAC,GAAG;IAClC,CAAC,CACD;IAED,OACC,IAAAmB,MAAA,CAAAC,aAAA,EAAC5F,YAAA,CAAA6F,wBAAwB;MACxBC,OAAO,EAAG,IAAI,CAAC5E,YAAc;MAC7B6E,UAAU,EAAG;IAAO,GAEpB,IAAAJ,MAAA,CAAAC,aAAA,EAAC5F,YAAA,CAAAgG,IAAI;MACJC,KAAK,EAAG,CACPtB,eAAM,CAACuB,kBAAkB,EACzBzF,KAAK,IAAIwE,iBAAiB,CACxB;MACHc,UAAU,EAAG;IAAO,GAElBtF,KAAK,IAAIyB,QAAQ,GAClB,IAAAyD,MAAA,CAAAC,aAAA,EAAC5F,YAAA,CAAAmG,SAAS;MACTC,kBAAkB,EAAG3B,KAAO;MAC5B4B,GAAG,EAAKC,CAAC,IAAQ,IAAI,CAACrD,eAAe,GAAGqD,CAAK;MAC7CL,KAAK,EAAGZ,eAAiB;MACzB/D,YAAY,EAAG,IAAI,CAACA,YAAc;MAClCD,eAAe,EAAG,IAAI,CAACA,eAAiB;MACxCkF,OAAO,EAAG,IAAI,CAACrF,YAAc;MAC7BsF,MAAM,EAAG,IAAI,CAACrF,WAAa;MAC3BsF,YAAY,EAAC,SAAS;MACtBC,aAAa,EAAC,MAAM;MACpBC,aAAa,EAAG,CAAG;MACnBnF,YAAY,EAAI,GAAGQ,UAAY,EAAG;MAClCT,KAAK,EAAGS,UAAU,CAAC4E,QAAQ,CAAC,CAAG;MAC/BC,aAAa,EAAG3E,QAAQ,GAAG,MAAM,GAAG;IAAQ,CAC5C,CAAC,GAEF,IAAAyD,MAAA,CAAAC,aAAA,EAAC5F,YAAA,CAAA8G,IAAI;MACJb,KAAK,EAAGZ,eAAiB;MACzBsB,aAAa,EAAG,CAAG;MACnBI,aAAa,EAAC;IAAM,GAElB/E,UACG,CACN,EACCwC,QACG,CACmB,CAAC;EAE7B;AACD;AAAC,IAAAwC,QAAA,GAEc,IAAAC,iCAAwB,EAAErG,cAAe,CAAC;AAAAsG,OAAA,CAAAC,OAAA,GAAAH,QAAA"}