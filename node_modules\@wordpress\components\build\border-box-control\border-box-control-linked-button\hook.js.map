{"version": 3, "names": ["_element", "require", "styles", "_interopRequireWildcard", "_context", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useBorderBoxControlLinkedButton", "props", "className", "size", "otherProps", "useContextSystem", "cx", "useCx", "classes", "useMemo", "borderBoxControlLinkedButton"], "sources": ["@wordpress/components/src/border-box-control/border-box-control-linked-button/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport * as styles from '../styles';\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useCx } from '../../utils/hooks/use-cx';\n\nimport type { LinkedButtonProps } from '../types';\n\nexport function useBorderBoxControlLinkedButton(\n\tprops: WordPressComponentProps< LinkedButtonProps, 'button' >\n) {\n\tconst {\n\t\tclassName,\n\t\tsize = 'default',\n\t\t...otherProps\n\t} = useContextSystem( props, 'BorderBoxControlLinkedButton' );\n\n\t// Generate class names.\n\tconst cx = useCx();\n\tconst classes = useMemo( () => {\n\t\treturn cx( styles.borderBoxControlLinkedButton( size ), className );\n\t}, [ className, cx, size ] );\n\n\treturn { ...otherProps, className: classes };\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAiD,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAXjD;AACA;AACA;;AAGA;AACA;AACA;;AAQO,SAASW,+BAA+BA,CAC9CC,KAA6D,EAC5D;EACD,MAAM;IACLC,SAAS;IACTC,IAAI,GAAG,SAAS;IAChB,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEJ,KAAK,EAAE,8BAA+B,CAAC;;EAE7D;EACA,MAAMK,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,OAAOH,EAAE,CAAEhC,MAAM,CAACoC,4BAA4B,CAAEP,IAAK,CAAC,EAAED,SAAU,CAAC;EACpE,CAAC,EAAE,CAAEA,SAAS,EAAEI,EAAE,EAAEH,IAAI,CAAG,CAAC;EAE5B,OAAO;IAAE,GAAGC,UAAU;IAAEF,SAAS,EAAEM;EAAQ,CAAC;AAC7C"}