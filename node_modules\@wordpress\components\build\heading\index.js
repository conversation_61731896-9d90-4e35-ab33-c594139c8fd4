"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Heading", {
  enumerable: true,
  get: function () {
    return _component.default;
  }
});
Object.defineProperty(exports, "useHeading", {
  enumerable: true,
  get: function () {
    return _hook.useHeading;
  }
});
var _component = _interopRequireDefault(require("./component"));
var _hook = require("./hook");
//# sourceMappingURL=index.js.map