{"version": 3, "names": ["_element", "require", "Dropdown", "Component", "constructor", "arguments", "toggle", "bind", "close", "state", "isOpen", "componentWillUnmount", "onToggle", "props", "componentDidUpdate", "prevProps", "prevState", "setState", "render", "renderContent", "renderToggle", "args", "onClose", "_react", "createElement", "Fragment", "_default", "exports", "default"], "sources": ["@wordpress/components/src/dropdown/index.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { Component } from '@wordpress/element';\n\nclass Dropdown extends Component {\n\tconstructor() {\n\t\tsuper( ...arguments );\n\n\t\tthis.toggle = this.toggle.bind( this );\n\t\tthis.close = this.close.bind( this );\n\n\t\tthis.state = {\n\t\t\tisOpen: false,\n\t\t};\n\t}\n\n\tcomponentWillUnmount() {\n\t\tconst { isOpen } = this.state;\n\t\tconst { onToggle } = this.props;\n\t\tif ( isOpen && onToggle ) {\n\t\t\tonToggle( false );\n\t\t}\n\t}\n\n\tcomponentDidUpdate( prevProps, prevState ) {\n\t\tconst { isOpen } = this.state;\n\t\tconst { onToggle } = this.props;\n\t\tif ( prevState.isOpen !== isOpen && onToggle ) {\n\t\t\tonToggle( isOpen );\n\t\t}\n\t}\n\n\ttoggle() {\n\t\tthis.setState( ( state ) => ( {\n\t\t\tisOpen: ! state.isOpen,\n\t\t} ) );\n\t}\n\n\tclose() {\n\t\tthis.setState( { isOpen: false } );\n\t}\n\n\trender() {\n\t\tconst { isOpen } = this.state;\n\t\tconst { renderContent, renderToggle } = this.props;\n\n\t\tconst args = { isOpen, onToggle: this.toggle, onClose: this.close };\n\n\t\treturn (\n\t\t\t<>\n\t\t\t\t{ renderToggle( args ) }\n\t\t\t\t{ isOpen && renderContent( args ) }\n\t\t\t</>\n\t\t);\n\t}\n}\n\nexport default Dropdown;\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA,MAAMC,QAAQ,SAASC,kBAAS,CAAC;EAChCC,WAAWA,CAAA,EAAG;IACb,KAAK,CAAE,GAAGC,SAAU,CAAC;IAErB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAE,IAAK,CAAC;IACtC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK,CAACD,IAAI,CAAE,IAAK,CAAC;IAEpC,IAAI,CAACE,KAAK,GAAG;MACZC,MAAM,EAAE;IACT,CAAC;EACF;EAEAC,oBAAoBA,CAAA,EAAG;IACtB,MAAM;MAAED;IAAO,CAAC,GAAG,IAAI,CAACD,KAAK;IAC7B,MAAM;MAAEG;IAAS,CAAC,GAAG,IAAI,CAACC,KAAK;IAC/B,IAAKH,MAAM,IAAIE,QAAQ,EAAG;MACzBA,QAAQ,CAAE,KAAM,CAAC;IAClB;EACD;EAEAE,kBAAkBA,CAAEC,SAAS,EAAEC,SAAS,EAAG;IAC1C,MAAM;MAAEN;IAAO,CAAC,GAAG,IAAI,CAACD,KAAK;IAC7B,MAAM;MAAEG;IAAS,CAAC,GAAG,IAAI,CAACC,KAAK;IAC/B,IAAKG,SAAS,CAACN,MAAM,KAAKA,MAAM,IAAIE,QAAQ,EAAG;MAC9CA,QAAQ,CAAEF,MAAO,CAAC;IACnB;EACD;EAEAJ,MAAMA,CAAA,EAAG;IACR,IAAI,CAACW,QAAQ,CAAIR,KAAK,KAAQ;MAC7BC,MAAM,EAAE,CAAED,KAAK,CAACC;IACjB,CAAC,CAAG,CAAC;EACN;EAEAF,KAAKA,CAAA,EAAG;IACP,IAAI,CAACS,QAAQ,CAAE;MAAEP,MAAM,EAAE;IAAM,CAAE,CAAC;EACnC;EAEAQ,MAAMA,CAAA,EAAG;IACR,MAAM;MAAER;IAAO,CAAC,GAAG,IAAI,CAACD,KAAK;IAC7B,MAAM;MAAEU,aAAa;MAAEC;IAAa,CAAC,GAAG,IAAI,CAACP,KAAK;IAElD,MAAMQ,IAAI,GAAG;MAAEX,MAAM;MAAEE,QAAQ,EAAE,IAAI,CAACN,MAAM;MAAEgB,OAAO,EAAE,IAAI,CAACd;IAAM,CAAC;IAEnE,OACC,IAAAe,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGL,YAAY,CAAEC,IAAK,CAAC,EACpBX,MAAM,IAAIS,aAAa,CAAEE,IAAK,CAC/B,CAAC;EAEL;AACD;AAAC,IAAAK,QAAA,GAEcxB,QAAQ;AAAAyB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}