<?php
/**
 * Script de diagnostic et correction pour les problèmes robots.txt et sitemap
 * 
 * À exécuter dans l'admin WordPress ou via WP-CLI
 */

// Vérifier que nous sommes dans WordPress
if (!defined('ABSPATH')) {
    die('Ce script doit être exécuté dans WordPress');
}

echo "<h2>🔧 Diagnostic et Correction - Robots.txt & Sitemap</h2>";

// 1. Vérifier les conflits de sitemap
echo "<h3>1. Vérification des conflits de sitemap</h3>";

// Désactiver le sitemap WordPress natif si activé
if (function_exists('wp_sitemaps_get_server')) {
    echo "<p>⚠️ Sitemap WordPress natif détecté. Désactivation...</p>";
    
    // Désactiver les sitemaps WordPress natifs
    add_filter('wp_sitemaps_enabled', '__return_false');
    
    // Supprimer les règles de réécriture WordPress pour les sitemaps
    remove_action('init', 'wp_sitemaps_get_server');
    
    echo "<p>✅ Sitemap WordPress natif désactivé</p>";
} else {
    echo "<p>✅ Pas de conflit avec le sitemap WordPress natif</p>";
}

// 2. Vérifier et corriger les règles de réécriture
echo "<h3>2. Correction des règles de réécriture</h3>";

// Forcer la régénération des règles de réécriture
flush_rewrite_rules(true);
echo "<p>✅ Règles de réécriture régénérées</p>";

// Supprimer le flag de règles déjà vidées pour forcer la régénération
delete_option('boss_seo_sitemap_rules_flushed');
echo "<p>✅ Flag de règles de réécriture réinitialisé</p>";

// 3. Vérifier la configuration du sitemap
echo "<h3>3. Vérification de la configuration du sitemap</h3>";

$sitemap_settings = get_option('boss-seo_advanced_sitemap_settings', array());

if (empty($sitemap_settings)) {
    echo "<p>⚠️ Aucune configuration de sitemap trouvée. Création d'une configuration par défaut...</p>";
    
    $default_settings = array(
        'enabled' => true,
        'includedPostTypes' => array('post', 'page'),
        'includedTaxonomies' => array('category', 'post_tag'),
        'defaultChangeFreq' => 'weekly',
        'defaultPriority' => 0.7,
        'includeImages' => true,
        'includeLastMod' => true,
        'enableAutoUpdate' => false,
        'enableImageSitemap' => false,
        'enableVideoSitemap' => false,
        'enableStoriesSitemap' => false,
        'enableNewsSitemap' => false,
        'enableCustomSitemap' => false,
        'enableTaxonomySitemaps' => false,
        'enablePostTypeSitemaps' => array(
            'post' => true,
            'page' => true
        )
    );
    
    update_option('boss-seo_advanced_sitemap_settings', $default_settings);
    echo "<p>✅ Configuration par défaut créée</p>";
} else {
    echo "<p>✅ Configuration du sitemap trouvée</p>";
    echo "<pre>" . print_r($sitemap_settings, true) . "</pre>";
}

// 4. Vérifier le contenu robots.txt
echo "<h3>4. Vérification du contenu robots.txt</h3>";

$robots_content = get_option('boss-seo_robots_content', '');

if (empty($robots_content)) {
    echo "<p>⚠️ Aucun contenu robots.txt trouvé. Création d'un contenu par défaut...</p>";
    
    $site_url = get_site_url();
    $default_robots = "User-agent: *\n";
    $default_robots .= "Disallow: /wp-admin/\n";
    $default_robots .= "Allow: /wp-admin/admin-ajax.php\n";
    $default_robots .= "Disallow: /wp-includes/\n";
    $default_robots .= "Disallow: /wp-content/plugins/\n";
    $default_robots .= "Disallow: /wp-login.php\n";
    $default_robots .= "Disallow: /xmlrpc.php\n";
    $default_robots .= "Disallow: /readme.html\n\n";
    $default_robots .= "Sitemap: {$site_url}/sitemap.xml\n";
    
    update_option('boss-seo_robots_content', $default_robots);
    echo "<p>✅ Contenu robots.txt par défaut créé</p>";
} else {
    echo "<p>✅ Contenu robots.txt trouvé</p>";
    echo "<pre>" . esc_html($robots_content) . "</pre>";
}

// 5. Tester les URLs de sitemap
echo "<h3>5. Test des URLs de sitemap</h3>";

$site_url = get_site_url();
$sitemap_urls = array(
    'Index' => $site_url . '/sitemap.xml',
    'Posts' => $site_url . '/sitemap-post.xml',
    'Pages' => $site_url . '/sitemap-page.xml'
);

foreach ($sitemap_urls as $name => $url) {
    echo "<p>🔗 <strong>{$name}:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
}

// 6. Vérifier les hooks et actions
echo "<h3>6. Vérification des hooks WordPress</h3>";

// Vérifier si les hooks sont enregistrés
$hooks_to_check = array(
    'rest_api_init' => 'Routes API REST',
    'init' => 'Initialisation des sitemaps',
    'template_redirect' => 'Redirection des templates'
);

foreach ($hooks_to_check as $hook => $description) {
    $priority = has_action($hook);
    if ($priority !== false) {
        echo "<p>✅ Hook '{$hook}' enregistré (priorité: {$priority}) - {$description}</p>";
    } else {
        echo "<p>❌ Hook '{$hook}' non trouvé - {$description}</p>";
    }
}

// 7. Actions correctives
echo "<h3>7. Actions correctives</h3>";

// Réenregistrer les hooks si nécessaire
if (class_exists('Boss_Technical_Management')) {
    echo "<p>🔄 Réenregistrement des hooks de gestion technique...</p>";
    
    // Simuler le réenregistrement des hooks
    do_action('init');
    do_action('rest_api_init');
    
    echo "<p>✅ Hooks réenregistrés</p>";
}

// 8. Instructions pour l'utilisateur
echo "<h3>8. Instructions de test</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h4>🧪 Pour tester les corrections :</h4>";
echo "<ol>";
echo "<li><strong>Testez le sitemap :</strong> Visitez <a href='{$site_url}/sitemap.xml' target='_blank'>{$site_url}/sitemap.xml</a></li>";
echo "<li><strong>Vérifiez robots.txt :</strong> Visitez <a href='{$site_url}/robots.txt' target='_blank'>{$site_url}/robots.txt</a></li>";
echo "<li><strong>Testez l'interface :</strong> Allez dans Boss SEO > Gestion technique > Robots.txt & Sitemap</li>";
echo "<li><strong>Sauvegardez des modifications :</strong> Modifiez le contenu robots.txt et cliquez sur Sauvegarder</li>";
echo "<li><strong>Régénérez le sitemap :</strong> Cliquez sur 'Régénérer tous les sitemaps'</li>";
echo "</ol>";
echo "</div>";

// 9. Code de débogage pour l'interface React
echo "<h3>9. Code de débogage JavaScript</h3>";
echo "<div style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
echo "<h4>📝 Ajoutez ce code dans la console du navigateur pour déboguer :</h4>";
echo "<pre style='background: #2d2d2d; color: #f8f8f2; padding: 10px; overflow-x: auto;'>";
echo "// Test des routes API
fetch('/wp-json/boss-seo/v1/robots-sitemap/robots')
  .then(response => response.json())
  .then(data => console.log('Robots content:', data))
  .catch(error => console.error('Error:', error));

// Test sauvegarde
fetch('/wp-json/boss-seo/v1/robots-sitemap/robots', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-WP-Nonce': wpApiSettings.nonce
  },
  body: JSON.stringify({
    content: 'User-agent: *\\nDisallow: /wp-admin/'
  })
})
.then(response => response.json())
.then(data => console.log('Save result:', data))
.catch(error => console.error('Save error:', error));";
echo "</pre>";
echo "</div>";

echo "<h3>✅ Diagnostic terminé !</h3>";
echo "<p><strong>Prochaines étapes :</strong> Testez les URLs ci-dessus et vérifiez que l'interface fonctionne correctement.</p>";
?>
