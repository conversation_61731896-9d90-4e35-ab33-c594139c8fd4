{"version": 3, "names": ["_i18n", "require", "_element", "_modal", "_interopRequireDefault", "_context", "_flex", "_button", "_text", "_vStack", "styles", "_interopRequireWildcard", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "UnconnectedConfirmDialog", "props", "forwardedRef", "isOpen", "isOpenProp", "onConfirm", "onCancel", "children", "confirmButtonText", "cancelButtonText", "otherProps", "useContextSystem", "cx", "useCx", "wrapperClassName", "wrapper", "cancelButtonRef", "useRef", "confirmButtonRef", "setIsOpen", "useState", "shouldSelfClose", "setShouldSelfClose", "useEffect", "isIsOpenSet", "handleEvent", "useCallback", "callback", "event", "handleEnter", "isConfirmOrCancelButton", "target", "current", "cancelLabel", "__", "<PERSON><PERSON><PERSON><PERSON>", "_react", "createElement", "Fragment", "onRequestClose", "onKeyDown", "closeButtonLabel", "isDismissible", "ref", "overlayClassName", "__<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VStack", "spacing", "Text", "Flex", "direction", "justify", "variant", "onClick", "ConfirmDialog", "contextConnect", "exports", "_default"], "sources": ["@wordpress/components/src/confirm-dialog/component.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { useCallback, useEffect, useRef, useState } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport Modal from '../modal';\nimport type { ConfirmDialogProps, DialogInputEvent } from './types';\nimport type { WordPressComponentProps } from '../context';\nimport { useContextSystem, contextConnect } from '../context';\nimport { Flex } from '../flex';\nimport Button from '../button';\nimport { Text } from '../text';\nimport { VStack } from '../v-stack';\nimport * as styles from './styles';\nimport { useCx } from '../utils/hooks/use-cx';\n\nconst UnconnectedConfirmDialog = (\n\tprops: WordPressComponentProps< ConfirmDialogProps, 'div', false >,\n\tforwardedRef: React.ForwardedRef< any >\n) => {\n\tconst {\n\t\tisOpen: isOpenProp,\n\t\tonConfirm,\n\t\tonCancel,\n\t\tchildren,\n\t\tconfirmButtonText,\n\t\tcancelButtonText,\n\t\t...otherProps\n\t} = useContextSystem( props, 'ConfirmDialog' );\n\n\tconst cx = useCx();\n\tconst wrapperClassName = cx( styles.wrapper );\n\tconst cancelButtonRef = useRef();\n\tconst confirmButtonRef = useRef();\n\n\tconst [ isOpen, setIsOpen ] = useState< boolean >();\n\tconst [ shouldSelfClose, setShouldSelfClose ] = useState< boolean >();\n\n\tuseEffect( () => {\n\t\t// We only allow the dialog to close itself if `isOpenProp` is *not* set.\n\t\t// If `isOpenProp` is set, then it (probably) means it's controlled by a\n\t\t// parent component. In that case, `shouldSelfClose` might do more harm than\n\t\t// good, so we disable it.\n\t\tconst isIsOpenSet = typeof isOpenProp !== 'undefined';\n\t\tsetIsOpen( isIsOpenSet ? isOpenProp : true );\n\t\tsetShouldSelfClose( ! isIsOpenSet );\n\t}, [ isOpenProp ] );\n\n\tconst handleEvent = useCallback(\n\t\t( callback?: ( event: DialogInputEvent ) => void ) =>\n\t\t\t( event: DialogInputEvent ) => {\n\t\t\t\tcallback?.( event );\n\t\t\t\tif ( shouldSelfClose ) {\n\t\t\t\t\tsetIsOpen( false );\n\t\t\t\t}\n\t\t\t},\n\t\t[ shouldSelfClose, setIsOpen ]\n\t);\n\n\tconst handleEnter = useCallback(\n\t\t( event: React.KeyboardEvent< HTMLDivElement > ) => {\n\t\t\t// Avoid triggering the 'confirm' action when a button is focused,\n\t\t\t// as this can cause a double submission.\n\t\t\tconst isConfirmOrCancelButton =\n\t\t\t\tevent.target === cancelButtonRef.current ||\n\t\t\t\tevent.target === confirmButtonRef.current;\n\n\t\t\tif ( ! isConfirmOrCancelButton && event.key === 'Enter' ) {\n\t\t\t\thandleEvent( onConfirm )( event );\n\t\t\t}\n\t\t},\n\t\t[ handleEvent, onConfirm ]\n\t);\n\n\tconst cancelLabel = cancelButtonText ?? __( 'Cancel' );\n\tconst confirmLabel = confirmButtonText ?? __( 'OK' );\n\n\treturn (\n\t\t<>\n\t\t\t{ isOpen && (\n\t\t\t\t<Modal\n\t\t\t\t\tonRequestClose={ handleEvent( onCancel ) }\n\t\t\t\t\tonKeyDown={ handleEnter }\n\t\t\t\t\tcloseButtonLabel={ cancelLabel }\n\t\t\t\t\tisDismissible={ true }\n\t\t\t\t\tref={ forwardedRef }\n\t\t\t\t\toverlayClassName={ wrapperClassName }\n\t\t\t\t\t__experimentalHideHeader\n\t\t\t\t\t{ ...otherProps }\n\t\t\t\t>\n\t\t\t\t\t<VStack spacing={ 8 }>\n\t\t\t\t\t\t<Text>{ children }</Text>\n\t\t\t\t\t\t<Flex direction=\"row\" justify=\"flex-end\">\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tref={ cancelButtonRef }\n\t\t\t\t\t\t\t\tvariant=\"tertiary\"\n\t\t\t\t\t\t\t\tonClick={ handleEvent( onCancel ) }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{ cancelLabel }\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tref={ confirmButtonRef }\n\t\t\t\t\t\t\t\tvariant=\"primary\"\n\t\t\t\t\t\t\t\tonClick={ handleEvent( onConfirm ) }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{ confirmLabel }\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</Flex>\n\t\t\t\t\t</VStack>\n\t\t\t\t</Modal>\n\t\t\t) }\n\t\t</>\n\t);\n};\n\n/**\n * `ConfirmDialog` is built of top of [`Modal`](/packages/components/src/modal/README.md)\n * and displays a confirmation dialog, with _confirm_ and _cancel_ buttons.\n * The dialog is confirmed by clicking the _confirm_ button or by pressing the `Enter` key.\n * It is cancelled (closed) by clicking the _cancel_ button, by pressing the `ESC` key, or by\n * clicking outside the dialog focus (i.e, the overlay).\n *\n * `ConfirmDialog` has two main implicit modes: controlled and uncontrolled.\n *\n * UnControlled:\n *\n * Allows the component to be used standalone, just by declaring it as part of another React's component render method:\n * -   It will be automatically open (displayed) upon mounting;\n * -   It will be automatically closed when clicking the _cancel_ button, by pressing the `ESC` key, or by clicking outside the dialog focus (i.e, the overlay);\n * -   `onCancel` is not mandatory but can be passed. Even if passed, the dialog will still be able to close itself.\n *\n * Activating this mode is as simple as omitting the `isOpen` prop. The only mandatory prop, in this case, is the `onConfirm` callback. The message is passed as the `children`. You can pass any JSX you'd like, which allows to further format the message or include sub-component if you'd like:\n *\n * ```jsx\n * import { __experimentalConfirmDialog as ConfirmDialog } from '@wordpress/components';\n *\n * function Example() {\n * \treturn (\n * \t\t<ConfirmDialog onConfirm={ () => console.debug( ' Confirmed! ' ) }>\n * \t\t\tAre you sure? <strong>This action cannot be undone!</strong>\n * \t\t</ConfirmDialog>\n * \t);\n * }\n * ```\n *\n *\n * Controlled mode:\n *  Let the parent component control when the dialog is open/closed. It's activated when a\n * boolean value is passed to `isOpen`:\n * -   It will not be automatically closed. You need to let it know when to open/close by updating the value of the `isOpen` prop;\n * -   Both `onConfirm` and the `onCancel` callbacks are mandatory props in this mode;\n * -   You'll want to update the state that controls `isOpen` by updating it from the `onCancel` and `onConfirm` callbacks.\n *\n *```jsx\n * import { __experimentalConfirmDialog as ConfirmDialog } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * function Example() {\n * \tconst [ isOpen, setIsOpen ] = useState( true );\n *\n * \tconst handleConfirm = () => {\n * \t\tconsole.debug( 'Confirmed!' );\n * \t\tsetIsOpen( false );\n * \t};\n *\n * \tconst handleCancel = () => {\n * \t\tconsole.debug( 'Cancelled!' );\n * \t\tsetIsOpen( false );\n * \t};\n *\n * \treturn (\n * \t\t<ConfirmDialog\n * \t\t\tisOpen={ isOpen }\n * \t\t\tonConfirm={ handleConfirm }\n * \t\t\tonCancel={ handleCancel }\n * \t\t>\n * \t\t\tAre you sure? <strong>This action cannot be undone!</strong>\n * \t\t</ConfirmDialog>\n * \t);\n * }\n * ```\n */\nexport const ConfirmDialog = contextConnect(\n\tUnconnectedConfirmDialog,\n\t'ConfirmDialog'\n);\nexport default ConfirmDialog;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAGA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,OAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,MAAA,GAAAC,uBAAA,CAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AAA8C,SAAAY,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAlB9C;AACA;AACA;;AAIA;AACA;AACA;;AAYA,MAAMW,wBAAwB,GAAGA,CAChCC,KAAkE,EAClEC,YAAuC,KACnC;EACJ,MAAM;IACLC,MAAM,EAAEC,UAAU;IAClBC,SAAS;IACTC,QAAQ;IACRC,QAAQ;IACRC,iBAAiB;IACjBC,gBAAgB;IAChB,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEV,KAAK,EAAE,eAAgB,CAAC;EAE9C,MAAMW,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAClB,MAAMC,gBAAgB,GAAGF,EAAE,CAAErC,MAAM,CAACwC,OAAQ,CAAC;EAC7C,MAAMC,eAAe,GAAG,IAAAC,eAAM,EAAC,CAAC;EAChC,MAAMC,gBAAgB,GAAG,IAAAD,eAAM,EAAC,CAAC;EAEjC,MAAM,CAAEd,MAAM,EAAEgB,SAAS,CAAE,GAAG,IAAAC,iBAAQ,EAAY,CAAC;EACnD,MAAM,CAAEC,eAAe,EAAEC,kBAAkB,CAAE,GAAG,IAAAF,iBAAQ,EAAY,CAAC;EAErE,IAAAG,kBAAS,EAAE,MAAM;IAChB;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAG,OAAOpB,UAAU,KAAK,WAAW;IACrDe,SAAS,CAAEK,WAAW,GAAGpB,UAAU,GAAG,IAAK,CAAC;IAC5CkB,kBAAkB,CAAE,CAAEE,WAAY,CAAC;EACpC,CAAC,EAAE,CAAEpB,UAAU,CAAG,CAAC;EAEnB,MAAMqB,WAAW,GAAG,IAAAC,oBAAW,EAC5BC,QAA8C,IAC7CC,KAAuB,IAAM;IAC9BD,QAAQ,GAAIC,KAAM,CAAC;IACnB,IAAKP,eAAe,EAAG;MACtBF,SAAS,CAAE,KAAM,CAAC;IACnB;EACD,CAAC,EACF,CAAEE,eAAe,EAAEF,SAAS,CAC7B,CAAC;EAED,MAAMU,WAAW,GAAG,IAAAH,oBAAW,EAC5BE,KAA4C,IAAM;IACnD;IACA;IACA,MAAME,uBAAuB,GAC5BF,KAAK,CAACG,MAAM,KAAKf,eAAe,CAACgB,OAAO,IACxCJ,KAAK,CAACG,MAAM,KAAKb,gBAAgB,CAACc,OAAO;IAE1C,IAAK,CAAEF,uBAAuB,IAAIF,KAAK,CAAClC,GAAG,KAAK,OAAO,EAAG;MACzD+B,WAAW,CAAEpB,SAAU,CAAC,CAAEuB,KAAM,CAAC;IAClC;EACD,CAAC,EACD,CAAEH,WAAW,EAAEpB,SAAS,CACzB,CAAC;EAED,MAAM4B,WAAW,GAAGxB,gBAAgB,aAAhBA,gBAAgB,cAAhBA,gBAAgB,GAAI,IAAAyB,QAAE,EAAE,QAAS,CAAC;EACtD,MAAMC,YAAY,GAAG3B,iBAAiB,aAAjBA,iBAAiB,cAAjBA,iBAAiB,GAAI,IAAA0B,QAAE,EAAE,IAAK,CAAC;EAEpD,OACC,IAAAE,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGnC,MAAM,IACP,IAAAiC,MAAA,CAAAC,aAAA,EAACrE,MAAA,CAAAiB,OAAK;IACLsD,cAAc,EAAGd,WAAW,CAAEnB,QAAS,CAAG;IAC1CkC,SAAS,EAAGX,WAAa;IACzBY,gBAAgB,EAAGR,WAAa;IAChCS,aAAa,EAAG,IAAM;IACtBC,GAAG,EAAGzC,YAAc;IACpB0C,gBAAgB,EAAG9B,gBAAkB;IACrC+B,wBAAwB;IAAA,GACnBnC;EAAU,GAEf,IAAA0B,MAAA,CAAAC,aAAA,EAAC/D,OAAA,CAAAwE,MAAM;IAACC,OAAO,EAAG;EAAG,GACpB,IAAAX,MAAA,CAAAC,aAAA,EAAChE,KAAA,CAAA2E,IAAI,QAAGzC,QAAgB,CAAC,EACzB,IAAA6B,MAAA,CAAAC,aAAA,EAAClE,KAAA,CAAA8E,IAAI;IAACC,SAAS,EAAC,KAAK;IAACC,OAAO,EAAC;EAAU,GACvC,IAAAf,MAAA,CAAAC,aAAA,EAACjE,OAAA,CAAAa,OAAM;IACN0D,GAAG,EAAG3B,eAAiB;IACvBoC,OAAO,EAAC,UAAU;IAClBC,OAAO,EAAG5B,WAAW,CAAEnB,QAAS;EAAG,GAEjC2B,WACK,CAAC,EACT,IAAAG,MAAA,CAAAC,aAAA,EAACjE,OAAA,CAAAa,OAAM;IACN0D,GAAG,EAAGzB,gBAAkB;IACxBkC,OAAO,EAAC,SAAS;IACjBC,OAAO,EAAG5B,WAAW,CAAEpB,SAAU;EAAG,GAElC8B,YACK,CACH,CACC,CACF,CAEP,CAAC;AAEL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMmB,aAAa,GAAG,IAAAC,uBAAc,EAC1CvD,wBAAwB,EACxB,eACD,CAAC;AAACwD,OAAA,CAAAF,aAAA,GAAAA,aAAA;AAAA,IAAAG,QAAA,GACaH,aAAa;AAAAE,OAAA,CAAAvE,OAAA,GAAAwE,QAAA"}