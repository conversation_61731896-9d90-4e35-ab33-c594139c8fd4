{"version": 3, "names": [], "sources": ["@wordpress/components/src/custom-gradient-picker/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type gradientParser from 'gradient-parser';\n\nexport type CustomGradientPickerProps = {\n\t/**\n\t * Start opting in to the new margin-free styles that will become the default\n\t * in a future version, currently scheduled to be WordPress 6.4. (The prop\n\t * can be safely removed once this happens.)\n\t *\n\t * @default false\n\t */\n\t__nextHasNoMargin?: boolean;\n\t/**\n\t * The current value of the gradient. Pass a css gradient string (See default value for example).\n\t * Optionally pass in a `null` value to specify no gradient is currently selected.\n\t *\n\t * @default 'linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%)'\n\t */\n\tvalue?: string | null;\n\t/**\n\t * The function called when a new gradient has been defined. It is passed to\n\t * the `currentGradient` as an argument.\n\t */\n\tonChange: ( currentGradient: string ) => void;\n\t/**\n\t * Whether this is rendered in the sidebar.\n\t *\n\t * @default false\n\t */\n\t__experimentalIsRenderedInSidebar?: boolean;\n};\n\nexport type GradientAnglePickerProps = {\n\tgradientAST:\n\t\t| gradientParser.LinearGradientNode\n\t\t| gradientParser.RepeatingLinearGradientNode;\n\thasGradient: boolean;\n\tonChange: ( gradient: string ) => void;\n};\n\nexport type GradientTypePickerProps = {\n\tgradientAST: gradientParser.GradientNode;\n\thasGradient: boolean;\n\tonChange: ( gradient: string ) => void;\n};\n\nexport type ControlPoint = { color: string; position: number };\n\nexport type CustomGradientBarProps = {\n\tbackground: React.CSSProperties[ 'background' ];\n\thasGradient: boolean;\n\tvalue: ControlPoint[];\n\tonChange: ( newControlPoints: ControlPoint[] ) => void;\n\tdisableInserter?: boolean;\n\tdisableAlpha?: boolean;\n\t__experimentalIsRenderedInSidebar?: boolean;\n};\n\nexport type CustomGradientBarIdleState = { id: 'IDLE' };\ntype CustomGradientBarMovingInserterState = {\n\tid: 'MOVING_INSERTER';\n\tinsertPosition: number;\n};\ntype CustomGradientBarInsertingControlPointState = {\n\tid: 'INSERTING_CONTROL_POINT';\n\tinsertPosition: number;\n};\ntype CustomGradientBarMovingControlPointState = { id: 'MOVING_CONTROL_POINT' };\n\nexport type CustomGradientBarReducerState =\n\t| CustomGradientBarIdleState\n\t| CustomGradientBarMovingInserterState\n\t| CustomGradientBarInsertingControlPointState\n\t| CustomGradientBarMovingControlPointState;\n\nexport type CustomGradientBarReducerAction =\n\t| { type: 'MOVE_INSERTER'; insertPosition: number }\n\t| { type: 'STOP_INSERTER_MOVE' }\n\t| { type: 'OPEN_INSERTER' }\n\t| { type: 'CLOSE_INSERTER' }\n\t| { type: 'START_CONTROL_CHANGE' }\n\t| { type: 'STOP_CONTROL_CHANGE' };\n\nexport type ControlPointButtonProps = {\n\tisOpen: boolean;\n\tposition: ControlPoint[ 'position' ];\n\tcolor: string;\n};\n\nexport type ControlPointsProps = {\n\tdisableRemove: boolean;\n\tdisableAlpha: boolean;\n\tgradientPickerDomRef: React.RefObject< HTMLDivElement >;\n\tignoreMarkerPosition?: number;\n\tvalue: ControlPoint[];\n\tonChange: ( controlPoints: ControlPoint[] ) => void;\n\tonStartControlPointChange: () => void;\n\tonStopControlPointChange: () => void;\n\t__experimentalIsRenderedInSidebar: boolean;\n};\n\nexport type ControlPointMoveState = {\n\tinitialPosition: number;\n\tindex: number;\n\tsignificantMoveHappened: boolean;\n\tlistenersActivated: boolean;\n};\n\nexport type InsertPointProps = {\n\tvalue: ControlPoint[];\n\tonChange: ( controlPoints: ControlPoint[] ) => void;\n\tonOpenInserter: () => void;\n\tonCloseInserter: () => void;\n\tinsertPosition: number;\n\tdisableAlpha: boolean;\n\t__experimentalIsRenderedInSidebar: boolean;\n};\n"], "mappings": ""}