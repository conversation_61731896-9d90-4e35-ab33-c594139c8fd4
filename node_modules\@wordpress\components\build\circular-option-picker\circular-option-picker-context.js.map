{"version": 3, "names": ["_element", "require", "CircularOptionPickerContext", "createContext", "exports"], "sources": ["@wordpress/components/src/circular-option-picker/circular-option-picker-context.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { createContext } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { CircularOptionPickerContextProps } from './types';\n\nexport const CircularOptionPickerContext =\n\tcreateContext< CircularOptionPickerContextProps >( {} );\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAQO,MAAMC,2BAA2B,GACvC,IAAAC,sBAAa,EAAsC,CAAC,CAAE,CAAC;AAACC,OAAA,CAAAF,2BAAA,GAAAA,2BAAA"}