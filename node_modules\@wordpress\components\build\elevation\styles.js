"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Elevation = void 0;
var _react = require("@emotion/react");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const Elevation = process.env.NODE_ENV === "production" ? {
  name: "12ip69d",
  styles: "background:transparent;display:block;margin:0!important;pointer-events:none;position:absolute;will-change:box-shadow"
} : {
  name: "1n58bsy-Elevation",
  styles: "background:transparent;display:block;margin:0!important;pointer-events:none;position:absolute;will-change:box-shadow;label:Elevation;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZWxldmF0aW9uL3N0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLNEIiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9lbGV2YXRpb24vc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHsgY3NzIH0gZnJvbSAnQGVtb3Rpb24vcmVhY3QnO1xuXG5leHBvcnQgY29uc3QgRWxldmF0aW9uID0gY3NzYFxuXHRiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcblx0ZGlzcGxheTogYmxvY2s7XG5cdG1hcmdpbjogMCAhaW1wb3J0YW50O1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHR3aWxsLWNoYW5nZTogYm94LXNoYWRvdztcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
exports.Elevation = Elevation;
//# sourceMappingURL=styles.js.map