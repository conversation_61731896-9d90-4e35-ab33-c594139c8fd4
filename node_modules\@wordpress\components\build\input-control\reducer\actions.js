"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RESET = exports.PRESS_UP = exports.PRESS_ENTER = exports.PRESS_DOWN = exports.INVALIDATE = exports.DRAG_START = exports.DRAG_END = exports.DRAG = exports.CONTROL = exports.COMMIT = exports.CHANGE = void 0;
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const CHANGE = 'CHANGE';
exports.CHANGE = CHANGE;
const COMMIT = 'COMMIT';
exports.COMMIT = COMMIT;
const CONTROL = 'CONTROL';
exports.CONTROL = CONTROL;
const DRAG_END = 'DRAG_END';
exports.DRAG_END = DRAG_END;
const DRAG_START = 'DRAG_START';
exports.DRAG_START = DRAG_START;
const DRAG = 'DRAG';
exports.DRAG = DRAG;
const INVALIDATE = 'INVALIDATE';
exports.INVALIDATE = INVALIDATE;
const PRESS_DOWN = 'PRESS_DOWN';
exports.PRESS_DOWN = PRESS_DOWN;
const PRESS_ENTER = 'PRESS_ENTER';
exports.PRESS_ENTER = PRESS_ENTER;
const PRESS_UP = 'PRESS_UP';
exports.PRESS_UP = PRESS_UP;
const RESET = 'RESET';
exports.RESET = RESET;
//# sourceMappingURL=actions.js.map