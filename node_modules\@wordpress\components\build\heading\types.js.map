{"version": 3, "names": [], "sources": ["@wordpress/components/src/heading/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { Props as TextProps } from '../text/types';\n\nexport type HeadingSize =\n\t| 1\n\t| 2\n\t| 3\n\t| 4\n\t| 5\n\t| 6\n\t| '1'\n\t| '2'\n\t| '3'\n\t| '4'\n\t| '5'\n\t| '6';\n\nexport type HeadingProps = Omit<\n\tTextProps,\n\t'size' | 'isBlock' | 'color' | 'weight'\n> & {\n\t/**\n\t * Passing any of the heading levels to `level` will both render the correct\n\t * typographic text size as well as the semantic element corresponding to\n\t * the level (`h1` for `1` for example).\n\t *\n\t * @default 2\n\t */\n\tlevel?: HeadingSize;\n\t/**\n\t * Sets `Heading` to have `display: block`. Note: text truncation only works\n\t * when `isBlock` is `false`.\n\t *\n\t * @default true\n\t */\n\tisBlock?: TextProps[ 'isBlock' ];\n\t/**\n\t * Adjusts the text color.\n\t *\n\t * @default '#1e1e1e'\n\t */\n\tcolor?: TextProps[ 'color' ];\n\t/**\n\t * Adjusts font-weight of the text.\n\t *\n\t * @default '600'\n\t */\n\tweight?: TextProps[ 'weight' ];\n};\n"], "mappings": ""}