{"version": 3, "names": ["_react", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "Root", "_base", "default", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports", "Viewbox", "strokeFocus", "isFocused", "css", "backgroundColor", "opacity", "Stroke", "VerticalStroke", "HorizontalStroke", "TopStroke", "RightStroke", "BottomStroke", "LeftStroke"], "sources": ["@wordpress/components/src/box-control/styles/box-control-icon-styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\nimport styled from '@emotion/styled';\n\nexport const Root = styled.span`\n\tbox-sizing: border-box;\n\tdisplay: block;\n\twidth: 24px;\n\theight: 24px;\n\tposition: relative;\n\tpadding: 4px;\n`;\n\nexport const Viewbox = styled.span`\n\tbox-sizing: border-box;\n\tdisplay: block;\n\tposition: relative;\n\twidth: 100%;\n\theight: 100%;\n`;\n\nconst strokeFocus = ( { isFocused }: { isFocused: boolean } ) => {\n\treturn css( {\n\t\tbackgroundColor: 'currentColor',\n\t\topacity: isFocused ? 1 : 0.3,\n\t} );\n};\n\nconst Stroke = styled.span`\n\tbox-sizing: border-box;\n\tdisplay: block;\n\tpointer-events: none;\n\tposition: absolute;\n\t${ strokeFocus };\n`;\n\nconst VerticalStroke = styled( Stroke )`\n\tbottom: 3px;\n\ttop: 3px;\n\twidth: 2px;\n`;\n\nconst HorizontalStroke = styled( Stroke )`\n\theight: 2px;\n\tleft: 3px;\n\tright: 3px;\n`;\n\nexport const TopStroke = styled( HorizontalStroke )`\n\ttop: 0;\n`;\n\nexport const RightStroke = styled( VerticalStroke )`\n\tright: 0;\n`;\n\nexport const BottomStroke = styled( HorizontalStroke )`\n\tbottom: 0;\n`;\n\nexport const LeftStroke = styled( VerticalStroke )`\n\tleft: 0;\n`;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAAqC,SAAAC,iCAAA;AAG9B,MAAMC,IAAI,OAAAC,KAAA,CAAAC,OAAA,UAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAOhB;AAACa,OAAA,CAAAZ,IAAA,GAAAA,IAAA;AAEK,MAAMa,OAAO,OAAAZ,KAAA,CAAAC,OAAA,UAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAMnB;AAACa,OAAA,CAAAC,OAAA,GAAAA,OAAA;AAEF,MAAMC,WAAW,GAAGA,CAAE;EAAEC;AAAkC,CAAC,KAAM;EAChE,oBAAO,IAAAC,UAAG,EAAE;IACXC,eAAe,EAAE,cAAc;IAC/BC,OAAO,EAAEH,SAAS,GAAG,CAAC,GAAG;EAC1B,CAAC,EAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,gDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,8+DAAC,CAAC;AACJ,CAAC;AAED,MAAMc,MAAM,OAAAlB,KAAA,CAAAC,OAAA,UAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,iFAKRO,WAAW,SAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,g/DACd;AAED,MAAMe,cAAc,GAAG,kBAAAnB,KAAA,CAAAC,OAAA,EAAQiB,MAAM,EAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAItC;AAED,MAAMsB,gBAAgB,GAAG,kBAAApB,KAAA,CAAAC,OAAA,EAAQiB,MAAM,EAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAIxC;AAEM,MAAMuB,SAAS,GAAG,kBAAArB,KAAA,CAAAC,OAAA,EAAQmB,gBAAgB,EAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAElD;AAACa,OAAA,CAAAU,SAAA,GAAAA,SAAA;AAEK,MAAMC,WAAW,GAAG,kBAAAtB,KAAA,CAAAC,OAAA,EAAQkB,cAAc,EAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAElD;AAACa,OAAA,CAAAW,WAAA,GAAAA,WAAA;AAEK,MAAMC,YAAY,GAAG,kBAAAvB,KAAA,CAAAC,OAAA,EAAQmB,gBAAgB,EAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAErD;AAACa,OAAA,CAAAY,YAAA,GAAAA,YAAA;AAEK,MAAMC,UAAU,GAAG,kBAAAxB,KAAA,CAAAC,OAAA,EAAQkB,cAAc,EAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAEjD;AAACa,OAAA,CAAAa,UAAA,GAAAA,UAAA"}