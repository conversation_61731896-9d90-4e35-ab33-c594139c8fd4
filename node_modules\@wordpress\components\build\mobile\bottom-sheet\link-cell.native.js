"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = LinkCell;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _cell = _interopRequireDefault(require("./cell"));
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const {
  placeholderColor
} = _styles.default;
function LinkCell({
  value,
  valueMask,
  onPress,
  showIcon = true
}) {
  return (0, _react.createElement)(_cell.default, {
    icon: showIcon && _icons.link,
    label: (0, _i18n.__)('Link to')
    // Since this is not actually editable, we treat value as a placeholder.
    ,
    value: valueMask || value || (0, _i18n.__)('Search or type URL'),
    valueStyle: !!(value || valueMask) ? undefined : placeholderColor,
    onPress: onPress
  }, (0, _react.createElement)(_icons.Icon, {
    icon: _icons.chevronRight
  }));
}
//# sourceMappingURL=link-cell.native.js.map