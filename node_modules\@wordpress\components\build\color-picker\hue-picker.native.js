"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _reactNativeLinearGradient = _interopRequireDefault(require("react-native-linear-gradient"));
var _element = _interopRequireWildcard(require("@wordpress/element"));
var _style = _interopRequireDefault(require("./style.scss"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

class HuePicker extends _element.Component {
  constructor(props) {
    super(props);
    this.hueColors = ['#ff0000', '#ffff00', '#00ff00', '#00ffff', '#0000ff', '#ff00ff', '#ff0000'];
    this.sliderX = new _reactNative.Animated.Value(props.barHeight * props.hue / 360);
    this.panResponder = _reactNative.PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onStartShouldSetPanResponderCapture: () => true,
      onMoveShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponderCapture: () => true,
      onPanResponderGrant: (evt, gestureState) => {
        const {
          onPress
        } = this.props;
        this.dragStartValue = this.computeHueValuePress(evt);
        if (onPress) {
          onPress({
            hue: this.computeHueValuePress(evt),
            nativeEvent: evt.nativeEvent
          });
        }
        this.fireDragEvent('onDragStart', gestureState);
      },
      onPanResponderMove: (evt, gestureState) => {
        this.fireDragEvent('onDragMove', gestureState);
      },
      onPanResponderTerminationRequest: () => true,
      onPanResponderRelease: (evt, gestureState) => {
        this.fireDragEvent('onDragEnd', gestureState);
      },
      onPanResponderTerminate: (evt, gestureState) => {
        this.fireDragEvent('onDragTerminate', gestureState);
      },
      onShouldBlockNativeResponder: () => true
    });
  }
  componentDidUpdate(prevProps) {
    const {
      hue = 0,
      barWidth = 200,
      sliderSize = 24
    } = this.props;
    const borderWidth = sliderSize / 10;
    if (prevProps.hue !== hue || prevProps.barWidth !== barWidth) {
      this.sliderX.setValue((barWidth - sliderSize + borderWidth) * hue / 360);
    }
  }
  normalizeValue(value) {
    if (value < 0) return 0;
    if (value > 1) return 1;
    return value;
  }
  getContainerStyle() {
    const {
      sliderSize = 24,
      barHeight = 12,
      containerStyle = {}
    } = this.props;
    const paddingLeft = sliderSize / 2;
    const paddingTop = sliderSize - barHeight > 0 ? (sliderSize - barHeight) / 2 : 0;
    return [_style.default['hsv-container'], containerStyle, {
      paddingTop,
      paddingBottom: paddingTop,
      paddingLeft,
      paddingRight: paddingLeft
    }];
  }
  computeHueValueDrag(gestureState) {
    const {
      dx
    } = gestureState;
    const {
      barWidth = 200
    } = this.props;
    const {
      dragStartValue
    } = this;
    const diff = dx / barWidth;
    const updatedHue = this.normalizeValue(dragStartValue / 360 + diff) * 360;
    return updatedHue;
  }
  computeHueValuePress(event) {
    const {
      nativeEvent
    } = event;
    const {
      locationX
    } = nativeEvent;
    const {
      barWidth = 200
    } = this.props;
    const updatedHue = this.normalizeValue(locationX / barWidth) * 360;
    return updatedHue;
  }
  fireDragEvent(eventName, gestureState) {
    const {
      [eventName]: event
    } = this.props;
    if (event) {
      event({
        hue: this.computeHueValueDrag(gestureState),
        gestureState
      });
    }
  }
  firePressEvent(event) {
    const {
      onPress
    } = this.props;
    if (onPress) {
      onPress({
        hue: this.computeHueValuePress(event),
        nativeEvent: event.nativeEvent
      });
    }
  }
  render() {
    const {
      hueColors
    } = this;
    const {
      sliderSize = 24,
      barWidth = 200,
      barHeight = 12,
      borderRadius = 0
    } = this.props;
    const borderWidth = sliderSize / 10;
    return (0, _react.createElement)(_reactNative.View, {
      style: this.getContainerStyle(),
      ...this.panResponder.panHandlers,
      hitSlop: {
        top: 10,
        bottom: 10,
        left: 0,
        right: 0
      }
    }, (0, _react.createElement)(_reactNativeLinearGradient.default, {
      colors: hueColors,
      style: {
        borderRadius
      },
      start: {
        x: 0,
        y: 0
      },
      end: {
        x: 1,
        y: 0
      }
    }, (0, _react.createElement)(_reactNative.View, {
      style: {
        width: barWidth,
        height: barHeight
      }
    })), (0, _react.createElement)(_reactNative.Animated.View, {
      pointerEvents: "none",
      style: [_style.default['hue-slider'], {
        width: sliderSize,
        height: sliderSize,
        left: (sliderSize - borderWidth) / 2,
        borderRadius: sliderSize / 2,
        transform: [{
          translateX: this.sliderX
        }]
      }]
    }));
  }
}
exports.default = HuePicker;
//# sourceMappingURL=hue-picker.native.js.map