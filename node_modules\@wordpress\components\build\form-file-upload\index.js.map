{"version": 3, "names": ["_element", "require", "_button", "_interopRequireDefault", "FormFileUpload", "accept", "children", "multiple", "onChange", "onClick", "render", "props", "ref", "useRef", "openFileDialog", "current", "click", "ui", "_react", "createElement", "default", "className", "type", "style", "display", "_default", "exports"], "sources": ["@wordpress/components/src/form-file-upload/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport Button from '../button';\nimport type { WordPressComponentProps } from '../context';\nimport type { FormFileUploadProps } from './types';\n\n/**\n * FormFileUpload is a component that allows users to select files from their local device.\n *\n * ```jsx\n * import { FormFileUpload } from '@wordpress/components';\n *\n * const MyFormFileUpload = () => (\n *   <FormFileUpload\n *     accept=\"image/*\"\n *     onChange={ ( event ) => console.log( event.currentTarget.files ) }\n *   >\n *     Upload\n *   </FormFileUpload>\n * );\n * ```\n */\nexport function FormFileUpload( {\n\taccept,\n\tchildren,\n\tmultiple = false,\n\tonChange,\n\tonClick,\n\trender,\n\t...props\n}: WordPressComponentProps< FormFileUploadProps, 'button', false > ) {\n\tconst ref = useRef< HTMLInputElement >( null );\n\tconst openFileDialog = () => {\n\t\tref.current?.click();\n\t};\n\n\tconst ui = render ? (\n\t\trender( { openFileDialog } )\n\t) : (\n\t\t<Button onClick={ openFileDialog } { ...props }>\n\t\t\t{ children }\n\t\t</Button>\n\t);\n\n\treturn (\n\t\t<div className=\"components-form-file-upload\">\n\t\t\t{ ui }\n\t\t\t<input\n\t\t\t\ttype=\"file\"\n\t\t\t\tref={ ref }\n\t\t\t\tmultiple={ multiple }\n\t\t\t\tstyle={ { display: 'none' } }\n\t\t\t\taccept={ accept }\n\t\t\t\tonChange={ onChange }\n\t\t\t\tonClick={ onClick }\n\t\t\t\tdata-testid=\"form-file-upload-input\"\n\t\t\t/>\n\t\t</div>\n\t);\n}\n\nexport default FormFileUpload;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,cAAcA,CAAE;EAC/BC,MAAM;EACNC,QAAQ;EACRC,QAAQ,GAAG,KAAK;EAChBC,QAAQ;EACRC,OAAO;EACPC,MAAM;EACN,GAAGC;AAC6D,CAAC,EAAG;EACpE,MAAMC,GAAG,GAAG,IAAAC,eAAM,EAAsB,IAAK,CAAC;EAC9C,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC5BF,GAAG,CAACG,OAAO,EAAEC,KAAK,CAAC,CAAC;EACrB,CAAC;EAED,MAAMC,EAAE,GAAGP,MAAM,GAChBA,MAAM,CAAE;IAAEI;EAAe,CAAE,CAAC,GAE5B,IAAAI,MAAA,CAAAC,aAAA,EAACjB,OAAA,CAAAkB,OAAM;IAACX,OAAO,EAAGK,cAAgB;IAAA,GAAMH;EAAK,GAC1CL,QACK,CACR;EAED,OACC,IAAAY,MAAA,CAAAC,aAAA;IAAKE,SAAS,EAAC;EAA6B,GACzCJ,EAAE,EACJ,IAAAC,MAAA,CAAAC,aAAA;IACCG,IAAI,EAAC,MAAM;IACXV,GAAG,EAAGA,GAAK;IACXL,QAAQ,EAAGA,QAAU;IACrBgB,KAAK,EAAG;MAAEC,OAAO,EAAE;IAAO,CAAG;IAC7BnB,MAAM,EAAGA,MAAQ;IACjBG,QAAQ,EAAGA,QAAU;IACrBC,OAAO,EAAGA,OAAS;IACnB,eAAY;EAAwB,CACpC,CACG,CAAC;AAER;AAAC,IAAAgB,QAAA,GAEcrB,cAAc;AAAAsB,OAAA,CAAAN,OAAA,GAAAK,QAAA"}