{"version": 3, "names": ["_reactNative", "require", "_components", "_icons", "_element", "_i18n", "_compose", "_styles", "_interopRequireDefault", "_cellStyles", "_ripple", "_lockIcon", "isIOS", "Platform", "OS", "BottomSheetCell", "Component", "constructor", "props", "arguments", "state", "isEditingValue", "autoFocus", "isScreenReaderEnabled", "handleScreenReaderToggled", "bind", "isCurrent", "componentDidUpdate", "prevProps", "prevState", "_valueTextInput", "focus", "componentDidMount", "a11yInfoChangeSubscription", "AccessibilityInfo", "addEventListener", "then", "setState", "componentWillUnmount", "remove", "typeToKeyboardType", "type", "step", "keyboardType", "Math", "abs", "render", "accessible", "accessibilityLabel", "accessibilityHint", "accessibilityRole", "disabled", "disabledStyle", "styles", "cellDisabled", "showLockIcon", "activeOpacity", "onPress", "onLongPress", "label", "subLabel", "value", "valuePlaceholder", "icon", "leftAlign", "iconStyle", "labelStyle", "valueStyle", "cellContainerStyle", "cellRowContainerStyle", "onChangeValue", "onSubmit", "children", "editable", "isSelected", "separatorType", "style", "getStylesFromColorScheme", "customActionButton", "borderless", "help", "valueProps", "showValue", "undefined", "isValueEditable", "cellLabelStyle", "cellLabel", "cellTextDark", "cellLabelCenteredStyle", "cellLabelCentered", "cellLabelLeftAlignNoIconStyle", "cellLabelLeftAlignNoIcon", "defaultMissingIconAndValue", "defaultLabelStyle", "defaultSubLabelStyleText", "cellSubLabelText", "cellSubLabelTextDark", "drawSeparator", "separatorStyle", "drawTopSeparator", "cellContainerStyles", "cellContainer", "rowContainerStyles", "cellRowContainer", "isInteractive", "onCellPress", "startEditing", "finishEditing", "defaultSeparatorStyle", "separator", "separatorDark", "cellSeparatorStyle", "cellSeparator", "cellSeparatorDark", "leftMarginStyle", "platformStyles", "separatorMarginLeft", "getValueComponent", "styleRTL", "I18nManager", "isRTL", "cellValueRTL", "cellValueStyle", "cellValue", "textInputStyle", "placeholderTextColor", "placeholderColorDisabled", "placeholderColorDisabledDark", "color", "placeholderColor", "textStyle", "shouldShowPlaceholder", "_react", "createElement", "TextInput", "ref", "c", "numberOfLines", "placeholder", "onChangeText", "pointerEvents", "onFocus", "onBlur", "onSubmitEditing", "Text", "ellipsizeMode", "getAccessibilityLabel", "sprintf", "_x", "iconStyleBase", "iconDark", "resetButtonStyle", "resetButton", "resetButtonDark", "cellHelpStyle", "cellHelpLabel", "cellHelpLabelIOS", "containerPointerEvents", "title", "handler", "opacity", "default", "__", "clipToBounds", "View", "cellRowIcon", "Icon", "lock", "size", "fill", "isPressed", "labelIconSeparator", "TouchableOpacity", "check", "testID", "_default", "withPreferredColorScheme", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/cell.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tTouchableOpacity,\n\tText,\n\tView,\n\tTextInput,\n\tI18nManager,\n\tAccessibilityInfo,\n\tPlatform,\n} from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { Icon } from '@wordpress/components';\nimport { check } from '@wordpress/icons';\nimport { Component } from '@wordpress/element';\nimport { __, _x, sprintf } from '@wordpress/i18n';\nimport { withPreferredColorScheme } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\nimport platformStyles from './cellStyles.scss';\nimport TouchableRipple from './ripple';\nimport LockIcon from './lock-icon';\n\nconst isIOS = Platform.OS === 'ios';\nclass BottomSheetCell extends Component {\n\tconstructor( props ) {\n\t\tsuper( ...arguments );\n\t\tthis.state = {\n\t\t\tisEditingValue: props.autoFocus || false,\n\t\t\tisScreenReaderEnabled: false,\n\t\t};\n\n\t\tthis.handleScreenReaderToggled =\n\t\t\tthis.handleScreenReaderToggled.bind( this );\n\n\t\tthis.isCurrent = false;\n\t}\n\n\tcomponentDidUpdate( prevProps, prevState ) {\n\t\tif ( ! prevState.isEditingValue && this.state.isEditingValue ) {\n\t\t\tthis._valueTextInput.focus();\n\t\t}\n\t}\n\n\tcomponentDidMount() {\n\t\tthis.isCurrent = true;\n\t\tthis.a11yInfoChangeSubscription = AccessibilityInfo.addEventListener(\n\t\t\t'screenReaderChanged',\n\t\t\tthis.handleScreenReaderToggled\n\t\t);\n\n\t\tAccessibilityInfo.isScreenReaderEnabled().then(\n\t\t\t( isScreenReaderEnabled ) => {\n\t\t\t\tif ( this.isCurrent && isScreenReaderEnabled ) {\n\t\t\t\t\tthis.setState( { isScreenReaderEnabled } );\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t}\n\n\tcomponentWillUnmount() {\n\t\tthis.isCurrent = false;\n\t\tthis.a11yInfoChangeSubscription.remove();\n\t}\n\n\thandleScreenReaderToggled( isScreenReaderEnabled ) {\n\t\tthis.setState( { isScreenReaderEnabled } );\n\t}\n\n\ttypeToKeyboardType( type, step ) {\n\t\tlet keyboardType = `default`;\n\t\tif ( type === `number` ) {\n\t\t\tif ( step && Math.abs( step ) < 1 ) {\n\t\t\t\tkeyboardType = `decimal-pad`;\n\t\t\t} else {\n\t\t\t\tkeyboardType = `number-pad`;\n\t\t\t}\n\t\t}\n\t\treturn keyboardType;\n\t}\n\n\trender() {\n\t\tconst {\n\t\t\taccessible,\n\t\t\taccessibilityLabel,\n\t\t\taccessibilityHint,\n\t\t\taccessibilityRole,\n\t\t\tdisabled = false,\n\t\t\tdisabledStyle = styles.cellDisabled,\n\t\t\tshowLockIcon = true,\n\t\t\tactiveOpacity,\n\t\t\tonPress,\n\t\t\tonLongPress,\n\t\t\tlabel,\n\t\t\tsubLabel,\n\t\t\tvalue,\n\t\t\tvaluePlaceholder = '',\n\t\t\ticon,\n\t\t\tleftAlign,\n\t\t\ticonStyle = {},\n\t\t\tlabelStyle = {},\n\t\t\tvalueStyle = {},\n\t\t\tcellContainerStyle = {},\n\t\t\tcellRowContainerStyle = {},\n\t\t\tonChangeValue,\n\t\t\tonSubmit,\n\t\t\tchildren,\n\t\t\teditable = true,\n\t\t\tisSelected = false,\n\t\t\tseparatorType,\n\t\t\tstyle = {},\n\t\t\tgetStylesFromColorScheme,\n\t\t\tcustomActionButton,\n\t\t\ttype,\n\t\t\tstep,\n\t\t\tborderless,\n\t\t\thelp,\n\t\t\t...valueProps\n\t\t} = this.props;\n\n\t\tconst showValue = value !== undefined;\n\t\tconst isValueEditable = editable && onChangeValue !== undefined;\n\t\tconst cellLabelStyle = getStylesFromColorScheme(\n\t\t\tstyles.cellLabel,\n\t\t\tstyles.cellTextDark\n\t\t);\n\t\tconst cellLabelCenteredStyle = getStylesFromColorScheme(\n\t\t\tstyles.cellLabelCentered,\n\t\t\tstyles.cellTextDark\n\t\t);\n\t\tconst cellLabelLeftAlignNoIconStyle = getStylesFromColorScheme(\n\t\t\tstyles.cellLabelLeftAlignNoIcon,\n\t\t\tstyles.cellTextDark\n\t\t);\n\t\tconst defaultMissingIconAndValue = leftAlign\n\t\t\t? cellLabelLeftAlignNoIconStyle\n\t\t\t: cellLabelCenteredStyle;\n\t\tconst defaultLabelStyle =\n\t\t\tshowValue || customActionButton || icon\n\t\t\t\t? cellLabelStyle\n\t\t\t\t: defaultMissingIconAndValue;\n\n\t\tconst defaultSubLabelStyleText = getStylesFromColorScheme(\n\t\t\tstyles.cellSubLabelText,\n\t\t\tstyles.cellSubLabelTextDark\n\t\t);\n\n\t\tconst drawSeparator =\n\t\t\t( separatorType && separatorType !== 'none' ) ||\n\t\t\tseparatorStyle === undefined;\n\t\tconst drawTopSeparator =\n\t\t\tdrawSeparator && separatorType === 'topFullWidth';\n\n\t\tconst cellContainerStyles = [\n\t\t\tstyles.cellContainer,\n\t\t\tcellContainerStyle,\n\t\t];\n\t\tconst rowContainerStyles = [\n\t\t\tstyles.cellRowContainer,\n\t\t\tcellRowContainerStyle,\n\t\t];\n\n\t\tconst isInteractive =\n\t\t\tisValueEditable ||\n\t\t\tonPress !== undefined ||\n\t\t\tonLongPress !== undefined;\n\n\t\tconst onCellPress = () => {\n\t\t\tif ( isValueEditable ) {\n\t\t\t\tstartEditing();\n\t\t\t} else if ( onPress !== undefined ) {\n\t\t\t\tonPress();\n\t\t\t}\n\t\t};\n\n\t\tconst finishEditing = () => {\n\t\t\tthis.setState( { isEditingValue: false } );\n\t\t};\n\n\t\tconst startEditing = () => {\n\t\t\tif ( this.state.isEditingValue === false ) {\n\t\t\t\tthis.setState( { isEditingValue: true } );\n\t\t\t}\n\t\t};\n\n\t\tconst separatorStyle = () => {\n\t\t\t// eslint-disable-next-line @wordpress/no-unused-vars-before-return\n\t\t\tconst defaultSeparatorStyle = this.props.getStylesFromColorScheme(\n\t\t\t\tstyles.separator,\n\t\t\t\tstyles.separatorDark\n\t\t\t);\n\t\t\tconst cellSeparatorStyle = this.props.getStylesFromColorScheme(\n\t\t\t\tstyles.cellSeparator,\n\t\t\t\tstyles.cellSeparatorDark\n\t\t\t);\n\t\t\tconst leftMarginStyle = {\n\t\t\t\t...cellSeparatorStyle,\n\t\t\t\t...platformStyles.separatorMarginLeft,\n\t\t\t};\n\t\t\tswitch ( separatorType ) {\n\t\t\t\tcase 'leftMargin':\n\t\t\t\t\treturn leftMarginStyle;\n\t\t\t\tcase 'fullWidth':\n\t\t\t\tcase 'topFullWidth':\n\t\t\t\t\treturn defaultSeparatorStyle;\n\t\t\t\tcase 'none':\n\t\t\t\t\treturn undefined;\n\t\t\t\tcase undefined:\n\t\t\t\t\tif ( showValue && icon ) {\n\t\t\t\t\t\treturn leftMarginStyle;\n\t\t\t\t\t}\n\t\t\t\t\treturn defaultSeparatorStyle;\n\t\t\t}\n\t\t};\n\n\t\tconst getValueComponent = () => {\n\t\t\tconst styleRTL = I18nManager.isRTL && styles.cellValueRTL;\n\t\t\tconst cellValueStyle = this.props.getStylesFromColorScheme(\n\t\t\t\tstyles.cellValue,\n\t\t\t\tstyles.cellTextDark\n\t\t\t);\n\t\t\tconst textInputStyle = {\n\t\t\t\t...cellValueStyle,\n\t\t\t\t...valueStyle,\n\t\t\t\t...styleRTL,\n\t\t\t};\n\t\t\tconst placeholderTextColor = disabled\n\t\t\t\t? this.props.getStylesFromColorScheme(\n\t\t\t\t\t\tstyles.placeholderColorDisabled,\n\t\t\t\t\t\tstyles.placeholderColorDisabledDark\n\t\t\t\t  ).color\n\t\t\t\t: styles.placeholderColor.color;\n\t\t\tconst textStyle = {\n\t\t\t\t...( disabled && styles.cellDisabled ),\n\t\t\t\t...cellValueStyle,\n\t\t\t\t...valueStyle,\n\t\t\t};\n\n\t\t\t// To be able to show the `middle` ellipsizeMode on editable cells\n\t\t\t// we show the TextInput just when the user wants to edit the value,\n\t\t\t// and the Text component to display it.\n\t\t\t// We also show the TextInput to display placeholder.\n\t\t\tconst shouldShowPlaceholder = isInteractive && value === '';\n\t\t\treturn this.state.isEditingValue || shouldShowPlaceholder ? (\n\t\t\t\t<TextInput\n\t\t\t\t\tref={ ( c ) => ( this._valueTextInput = c ) }\n\t\t\t\t\tnumberOfLines={ 1 }\n\t\t\t\t\tstyle={ textInputStyle }\n\t\t\t\t\tvalue={ value }\n\t\t\t\t\tplaceholder={ valuePlaceholder }\n\t\t\t\t\tplaceholderTextColor={ placeholderTextColor }\n\t\t\t\t\tonChangeText={ onChangeValue }\n\t\t\t\t\teditable={ isValueEditable && ! disabled }\n\t\t\t\t\tpointerEvents={\n\t\t\t\t\t\tthis.state.isEditingValue ? 'auto' : 'none'\n\t\t\t\t\t}\n\t\t\t\t\tonFocus={ startEditing }\n\t\t\t\t\tonBlur={ finishEditing }\n\t\t\t\t\tonSubmitEditing={ onSubmit }\n\t\t\t\t\tkeyboardType={ this.typeToKeyboardType( type, step ) }\n\t\t\t\t\tdisabled={ disabled }\n\t\t\t\t\t{ ...valueProps }\n\t\t\t\t/>\n\t\t\t) : (\n\t\t\t\t<Text\n\t\t\t\t\tstyle={ textStyle }\n\t\t\t\t\tnumberOfLines={ 1 }\n\t\t\t\t\tellipsizeMode={ 'middle' }\n\t\t\t\t>\n\t\t\t\t\t{ value }\n\t\t\t\t</Text>\n\t\t\t);\n\t\t};\n\n\t\tconst getAccessibilityLabel = () => {\n\t\t\tif ( accessible === false ) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif ( accessibilityLabel || ! showValue ) {\n\t\t\t\treturn accessibilityLabel || label;\n\t\t\t}\n\n\t\t\tif ( ! value ) {\n\t\t\t\treturn ! help\n\t\t\t\t\t? sprintf(\n\t\t\t\t\t\t\t/* translators: accessibility text. Empty state of a inline textinput cell. %s: The cell's title */\n\t\t\t\t\t\t\t_x( '%s. Empty', 'inline textinput cell' ),\n\t\t\t\t\t\t\tlabel\n\t\t\t\t\t  )\n\t\t\t\t\t: // Separating by ',' is necessary to make a pause on urls (non-capitalized text)\n\t\t\t\t\t  sprintf(\n\t\t\t\t\t\t\t/* translators: accessibility text. Empty state of a inline textinput cell. %1: Cell title, %2: cell help. */\n\t\t\t\t\t\t\t_x( '%1$s, %2$s. Empty', 'inline textinput cell' ),\n\t\t\t\t\t\t\tlabel,\n\t\t\t\t\t\t\thelp\n\t\t\t\t\t  );\n\t\t\t}\n\t\t\treturn ! help\n\t\t\t\t? sprintf(\n\t\t\t\t\t\t/* translators: accessibility text. Inline textinput title and value.%1: Cell title, %2: cell value. */\n\t\t\t\t\t\t_x( '%1$s, %2$s', 'inline textinput cell' ),\n\t\t\t\t\t\tlabel,\n\t\t\t\t\t\tvalue\n\t\t\t\t  ) // Separating by ',' is necessary to make a pause on urls (non-capitalized text)\n\t\t\t\t: sprintf(\n\t\t\t\t\t\t/* translators: accessibility text. Inline textinput title, value and help text.%1: Cell title, %2: cell value, , %3: cell help. */\n\t\t\t\t\t\t_x( '%1$s, %2$s, %3$s', 'inline textinput cell' ),\n\t\t\t\t\t\tlabel,\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t\thelp\n\t\t\t\t  );\n\t\t};\n\n\t\tconst iconStyleBase = getStylesFromColorScheme(\n\t\t\tstyles.icon,\n\t\t\tstyles.iconDark\n\t\t);\n\t\tconst resetButtonStyle = getStylesFromColorScheme(\n\t\t\tstyles.resetButton,\n\t\t\tstyles.resetButtonDark\n\t\t);\n\t\tconst cellHelpStyle = [\n\t\t\tstyles.cellHelpLabel,\n\t\t\tisIOS && styles.cellHelpLabelIOS,\n\t\t];\n\t\tconst containerPointerEvents =\n\t\t\tthis.state.isScreenReaderEnabled && accessible ? 'none' : 'auto';\n\t\tconst { title, handler } = customActionButton || {};\n\n\t\tconst opacity =\n\t\t\tactiveOpacity !== undefined\n\t\t\t\t? activeOpacity\n\t\t\t\t: platformStyles.activeOpacity?.opacity;\n\n\t\treturn (\n\t\t\t<TouchableRipple\n\t\t\t\taccessible={\n\t\t\t\t\taccessible !== undefined\n\t\t\t\t\t\t? accessible\n\t\t\t\t\t\t: ! this.state.isEditingValue\n\t\t\t\t}\n\t\t\t\taccessibilityLabel={ getAccessibilityLabel() }\n\t\t\t\taccessibilityRole={ accessibilityRole || 'button' }\n\t\t\t\taccessibilityHint={\n\t\t\t\t\tisValueEditable\n\t\t\t\t\t\t? /* translators: accessibility text */\n\t\t\t\t\t\t  __( 'Double tap to edit this value' )\n\t\t\t\t\t\t: accessibilityHint\n\t\t\t\t}\n\t\t\t\tdisabled={ disabled || ! isInteractive }\n\t\t\t\tactiveOpacity={ opacity }\n\t\t\t\tonPress={ onCellPress }\n\t\t\t\tonLongPress={ onLongPress }\n\t\t\t\tstyle={ [ styles.clipToBounds, style ] }\n\t\t\t\tborderless={ borderless }\n\t\t\t>\n\t\t\t\t{ drawTopSeparator && <View style={ separatorStyle() } /> }\n\t\t\t\t<View\n\t\t\t\t\tstyle={ cellContainerStyles }\n\t\t\t\t\tpointerEvents={ containerPointerEvents }\n\t\t\t\t>\n\t\t\t\t\t<View style={ rowContainerStyles }>\n\t\t\t\t\t\t<View style={ styles.cellRowContainer }>\n\t\t\t\t\t\t\t{ icon && (\n\t\t\t\t\t\t\t\t<View\n\t\t\t\t\t\t\t\t\tstyle={ [\n\t\t\t\t\t\t\t\t\t\tstyles.cellRowContainer,\n\t\t\t\t\t\t\t\t\t\tstyles.cellRowIcon,\n\t\t\t\t\t\t\t\t\t] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Icon\n\t\t\t\t\t\t\t\t\t\tlock\n\t\t\t\t\t\t\t\t\t\ticon={ icon }\n\t\t\t\t\t\t\t\t\t\tsize={ 24 }\n\t\t\t\t\t\t\t\t\t\tfill={\n\t\t\t\t\t\t\t\t\t\t\ticonStyle.color ||\n\t\t\t\t\t\t\t\t\t\t\ticonStyleBase.color\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tstyle={ iconStyle }\n\t\t\t\t\t\t\t\t\t\tisPressed={ false }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t<View\n\t\t\t\t\t\t\t\t\t\tstyle={\n\t\t\t\t\t\t\t\t\t\t\tplatformStyles.labelIconSeparator\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</View>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t{ subLabel && label && (\n\t\t\t\t\t\t\t\t<View>\n\t\t\t\t\t\t\t\t\t<Text\n\t\t\t\t\t\t\t\t\t\tstyle={ [\n\t\t\t\t\t\t\t\t\t\t\tdefaultLabelStyle,\n\t\t\t\t\t\t\t\t\t\t\tlabelStyle,\n\t\t\t\t\t\t\t\t\t\t] }\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t{ label }\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t<Text style={ defaultSubLabelStyleText }>\n\t\t\t\t\t\t\t\t\t\t{ subLabel }\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t</View>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t{ ! subLabel && label && (\n\t\t\t\t\t\t\t\t<Text\n\t\t\t\t\t\t\t\t\tstyle={ [ defaultLabelStyle, labelStyle ] }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ label }\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</View>\n\t\t\t\t\t\t{ customActionButton && (\n\t\t\t\t\t\t\t<TouchableOpacity\n\t\t\t\t\t\t\t\tonPress={ handler }\n\t\t\t\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<Text style={ resetButtonStyle }>\n\t\t\t\t\t\t\t\t\t{ title }\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t</TouchableOpacity>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</View>\n\t\t\t\t\t{ isSelected && (\n\t\t\t\t\t\t<Icon\n\t\t\t\t\t\t\ticon={ check }\n\t\t\t\t\t\t\tfill={ platformStyles.isSelected.color }\n\t\t\t\t\t\t\ttestID=\"bottom-sheet-cell-selected-icon\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t\t{ showValue && getValueComponent() }\n\t\t\t\t\t<View\n\t\t\t\t\t\tstyle={ [\n\t\t\t\t\t\t\tdisabled && disabledStyle,\n\t\t\t\t\t\t\tstyles.cellRowContainer,\n\t\t\t\t\t\t] }\n\t\t\t\t\t\tpointerEvents={ disabled ? 'none' : 'auto' }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ children }\n\t\t\t\t\t</View>\n\t\t\t\t\t{ disabled && showLockIcon && (\n\t\t\t\t\t\t<View style={ styles.cellDisabled }>\n\t\t\t\t\t\t\t<LockIcon />\n\t\t\t\t\t\t</View>\n\t\t\t\t\t) }\n\t\t\t\t</View>\n\t\t\t\t{ help && (\n\t\t\t\t\t<Text style={ [ cellHelpStyle, styles.placeholderColor ] }>\n\t\t\t\t\t\t{ help }\n\t\t\t\t\t</Text>\n\t\t\t\t) }\n\t\t\t\t{ ! drawTopSeparator && <View style={ separatorStyle() } /> }\n\t\t\t</TouchableRipple>\n\t\t);\n\t}\n}\n\nexport default withPreferredColorScheme( BottomSheetCell );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAaA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAKA,IAAAM,OAAA,GAAAC,sBAAA,CAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAD,sBAAA,CAAAP,OAAA;AACA,IAAAS,OAAA,GAAAF,sBAAA,CAAAP,OAAA;AACA,IAAAU,SAAA,GAAAH,sBAAA,CAAAP,OAAA;AA5BA;AACA;AACA;;AAWA;AACA;AACA;;AAOA;AACA;AACA;;AAMA,MAAMW,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;AACnC,MAAMC,eAAe,SAASC,kBAAS,CAAC;EACvCC,WAAWA,CAAEC,KAAK,EAAG;IACpB,KAAK,CAAE,GAAGC,SAAU,CAAC;IACrB,IAAI,CAACC,KAAK,GAAG;MACZC,cAAc,EAAEH,KAAK,CAACI,SAAS,IAAI,KAAK;MACxCC,qBAAqB,EAAE;IACxB,CAAC;IAED,IAAI,CAACC,yBAAyB,GAC7B,IAAI,CAACA,yBAAyB,CAACC,IAAI,CAAE,IAAK,CAAC;IAE5C,IAAI,CAACC,SAAS,GAAG,KAAK;EACvB;EAEAC,kBAAkBA,CAAEC,SAAS,EAAEC,SAAS,EAAG;IAC1C,IAAK,CAAEA,SAAS,CAACR,cAAc,IAAI,IAAI,CAACD,KAAK,CAACC,cAAc,EAAG;MAC9D,IAAI,CAACS,eAAe,CAACC,KAAK,CAAC,CAAC;IAC7B;EACD;EAEAC,iBAAiBA,CAAA,EAAG;IACnB,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACO,0BAA0B,GAAGC,8BAAiB,CAACC,gBAAgB,CACnE,qBAAqB,EACrB,IAAI,CAACX,yBACN,CAAC;IAEDU,8BAAiB,CAACX,qBAAqB,CAAC,CAAC,CAACa,IAAI,CAC3Cb,qBAAqB,IAAM;MAC5B,IAAK,IAAI,CAACG,SAAS,IAAIH,qBAAqB,EAAG;QAC9C,IAAI,CAACc,QAAQ,CAAE;UAAEd;QAAsB,CAAE,CAAC;MAC3C;IACD,CACD,CAAC;EACF;EAEAe,oBAAoBA,CAAA,EAAG;IACtB,IAAI,CAACZ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACO,0BAA0B,CAACM,MAAM,CAAC,CAAC;EACzC;EAEAf,yBAAyBA,CAAED,qBAAqB,EAAG;IAClD,IAAI,CAACc,QAAQ,CAAE;MAAEd;IAAsB,CAAE,CAAC;EAC3C;EAEAiB,kBAAkBA,CAAEC,IAAI,EAAEC,IAAI,EAAG;IAChC,IAAIC,YAAY,GAAI,SAAQ;IAC5B,IAAKF,IAAI,KAAM,QAAO,EAAG;MACxB,IAAKC,IAAI,IAAIE,IAAI,CAACC,GAAG,CAAEH,IAAK,CAAC,GAAG,CAAC,EAAG;QACnCC,YAAY,GAAI,aAAY;MAC7B,CAAC,MAAM;QACNA,YAAY,GAAI,YAAW;MAC5B;IACD;IACA,OAAOA,YAAY;EACpB;EAEAG,MAAMA,CAAA,EAAG;IACR,MAAM;MACLC,UAAU;MACVC,kBAAkB;MAClBC,iBAAiB;MACjBC,iBAAiB;MACjBC,QAAQ,GAAG,KAAK;MAChBC,aAAa,GAAGC,eAAM,CAACC,YAAY;MACnCC,YAAY,GAAG,IAAI;MACnBC,aAAa;MACbC,OAAO;MACPC,WAAW;MACXC,KAAK;MACLC,QAAQ;MACRC,KAAK;MACLC,gBAAgB,GAAG,EAAE;MACrBC,IAAI;MACJC,SAAS;MACTC,SAAS,GAAG,CAAC,CAAC;MACdC,UAAU,GAAG,CAAC,CAAC;MACfC,UAAU,GAAG,CAAC,CAAC;MACfC,kBAAkB,GAAG,CAAC,CAAC;MACvBC,qBAAqB,GAAG,CAAC,CAAC;MAC1BC,aAAa;MACbC,QAAQ;MACRC,QAAQ;MACRC,QAAQ,GAAG,IAAI;MACfC,UAAU,GAAG,KAAK;MAClBC,aAAa;MACbC,KAAK,GAAG,CAAC,CAAC;MACVC,wBAAwB;MACxBC,kBAAkB;MAClBrC,IAAI;MACJC,IAAI;MACJqC,UAAU;MACVC,IAAI;MACJ,GAAGC;IACJ,CAAC,GAAG,IAAI,CAAC/D,KAAK;IAEd,MAAMgE,SAAS,GAAGrB,KAAK,KAAKsB,SAAS;IACrC,MAAMC,eAAe,GAAGX,QAAQ,IAAIH,aAAa,KAAKa,SAAS;IAC/D,MAAME,cAAc,GAAGR,wBAAwB,CAC9CxB,eAAM,CAACiC,SAAS,EAChBjC,eAAM,CAACkC,YACR,CAAC;IACD,MAAMC,sBAAsB,GAAGX,wBAAwB,CACtDxB,eAAM,CAACoC,iBAAiB,EACxBpC,eAAM,CAACkC,YACR,CAAC;IACD,MAAMG,6BAA6B,GAAGb,wBAAwB,CAC7DxB,eAAM,CAACsC,wBAAwB,EAC/BtC,eAAM,CAACkC,YACR,CAAC;IACD,MAAMK,0BAA0B,GAAG5B,SAAS,GACzC0B,6BAA6B,GAC7BF,sBAAsB;IACzB,MAAMK,iBAAiB,GACtBX,SAAS,IAAIJ,kBAAkB,IAAIf,IAAI,GACpCsB,cAAc,GACdO,0BAA0B;IAE9B,MAAME,wBAAwB,GAAGjB,wBAAwB,CACxDxB,eAAM,CAAC0C,gBAAgB,EACvB1C,eAAM,CAAC2C,oBACR,CAAC;IAED,MAAMC,aAAa,GAChBtB,aAAa,IAAIA,aAAa,KAAK,MAAM,IAC3CuB,cAAc,KAAKf,SAAS;IAC7B,MAAMgB,gBAAgB,GACrBF,aAAa,IAAItB,aAAa,KAAK,cAAc;IAElD,MAAMyB,mBAAmB,GAAG,CAC3B/C,eAAM,CAACgD,aAAa,EACpBjC,kBAAkB,CAClB;IACD,MAAMkC,kBAAkB,GAAG,CAC1BjD,eAAM,CAACkD,gBAAgB,EACvBlC,qBAAqB,CACrB;IAED,MAAMmC,aAAa,GAClBpB,eAAe,IACf3B,OAAO,KAAK0B,SAAS,IACrBzB,WAAW,KAAKyB,SAAS;IAE1B,MAAMsB,WAAW,GAAGA,CAAA,KAAM;MACzB,IAAKrB,eAAe,EAAG;QACtBsB,YAAY,CAAC,CAAC;MACf,CAAC,MAAM,IAAKjD,OAAO,KAAK0B,SAAS,EAAG;QACnC1B,OAAO,CAAC,CAAC;MACV;IACD,CAAC;IAED,MAAMkD,aAAa,GAAGA,CAAA,KAAM;MAC3B,IAAI,CAACtE,QAAQ,CAAE;QAAEhB,cAAc,EAAE;MAAM,CAAE,CAAC;IAC3C,CAAC;IAED,MAAMqF,YAAY,GAAGA,CAAA,KAAM;MAC1B,IAAK,IAAI,CAACtF,KAAK,CAACC,cAAc,KAAK,KAAK,EAAG;QAC1C,IAAI,CAACgB,QAAQ,CAAE;UAAEhB,cAAc,EAAE;QAAK,CAAE,CAAC;MAC1C;IACD,CAAC;IAED,MAAM6E,cAAc,GAAGA,CAAA,KAAM;MAC5B;MACA,MAAMU,qBAAqB,GAAG,IAAI,CAAC1F,KAAK,CAAC2D,wBAAwB,CAChExB,eAAM,CAACwD,SAAS,EAChBxD,eAAM,CAACyD,aACR,CAAC;MACD,MAAMC,kBAAkB,GAAG,IAAI,CAAC7F,KAAK,CAAC2D,wBAAwB,CAC7DxB,eAAM,CAAC2D,aAAa,EACpB3D,eAAM,CAAC4D,iBACR,CAAC;MACD,MAAMC,eAAe,GAAG;QACvB,GAAGH,kBAAkB;QACrB,GAAGI,mBAAc,CAACC;MACnB,CAAC;MACD,QAASzC,aAAa;QACrB,KAAK,YAAY;UAChB,OAAOuC,eAAe;QACvB,KAAK,WAAW;QAChB,KAAK,cAAc;UAClB,OAAON,qBAAqB;QAC7B,KAAK,MAAM;UACV,OAAOzB,SAAS;QACjB,KAAKA,SAAS;UACb,IAAKD,SAAS,IAAInB,IAAI,EAAG;YACxB,OAAOmD,eAAe;UACvB;UACA,OAAON,qBAAqB;MAC9B;IACD,CAAC;IAED,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,MAAMC,QAAQ,GAAGC,wBAAW,CAACC,KAAK,IAAInE,eAAM,CAACoE,YAAY;MACzD,MAAMC,cAAc,GAAG,IAAI,CAACxG,KAAK,CAAC2D,wBAAwB,CACzDxB,eAAM,CAACsE,SAAS,EAChBtE,eAAM,CAACkC,YACR,CAAC;MACD,MAAMqC,cAAc,GAAG;QACtB,GAAGF,cAAc;QACjB,GAAGvD,UAAU;QACb,GAAGmD;MACJ,CAAC;MACD,MAAMO,oBAAoB,GAAG1E,QAAQ,GAClC,IAAI,CAACjC,KAAK,CAAC2D,wBAAwB,CACnCxB,eAAM,CAACyE,wBAAwB,EAC/BzE,eAAM,CAAC0E,4BACP,CAAC,CAACC,KAAK,GACP3E,eAAM,CAAC4E,gBAAgB,CAACD,KAAK;MAChC,MAAME,SAAS,GAAG;QACjB,IAAK/E,QAAQ,IAAIE,eAAM,CAACC,YAAY,CAAE;QACtC,GAAGoE,cAAc;QACjB,GAAGvD;MACJ,CAAC;;MAED;MACA;MACA;MACA;MACA,MAAMgE,qBAAqB,GAAG3B,aAAa,IAAI3C,KAAK,KAAK,EAAE;MAC3D,OAAO,IAAI,CAACzC,KAAK,CAACC,cAAc,IAAI8G,qBAAqB,GACxD,IAAAC,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAsI,SAAS;QACTC,GAAG,EAAKC,CAAC,IAAQ,IAAI,CAAC1G,eAAe,GAAG0G,CAAK;QAC7CC,aAAa,EAAG,CAAG;QACnB7D,KAAK,EAAGgD,cAAgB;QACxB/D,KAAK,EAAGA,KAAO;QACf6E,WAAW,EAAG5E,gBAAkB;QAChC+D,oBAAoB,EAAGA,oBAAsB;QAC7Cc,YAAY,EAAGrE,aAAe;QAC9BG,QAAQ,EAAGW,eAAe,IAAI,CAAEjC,QAAU;QAC1CyF,aAAa,EACZ,IAAI,CAACxH,KAAK,CAACC,cAAc,GAAG,MAAM,GAAG,MACrC;QACDwH,OAAO,EAAGnC,YAAc;QACxBoC,MAAM,EAAGnC,aAAe;QACxBoC,eAAe,EAAGxE,QAAU;QAC5B5B,YAAY,EAAG,IAAI,CAACH,kBAAkB,CAAEC,IAAI,EAAEC,IAAK,CAAG;QACtDS,QAAQ,EAAGA,QAAU;QAAA,GAChB8B;MAAU,CACf,CAAC,GAEF,IAAAmD,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAgJ,IAAI;QACJpE,KAAK,EAAGsD,SAAW;QACnBO,aAAa,EAAG,CAAG;QACnBQ,aAAa,EAAG;MAAU,GAExBpF,KACG,CACN;IACF,CAAC;IAED,MAAMqF,qBAAqB,GAAGA,CAAA,KAAM;MACnC,IAAKnG,UAAU,KAAK,KAAK,EAAG;QAC3B;MACD;MACA,IAAKC,kBAAkB,IAAI,CAAEkC,SAAS,EAAG;QACxC,OAAOlC,kBAAkB,IAAIW,KAAK;MACnC;MAEA,IAAK,CAAEE,KAAK,EAAG;QACd,OAAO,CAAEmB,IAAI,GACV,IAAAmE,aAAO,GACP;QACA,IAAAC,QAAE,EAAE,WAAW,EAAE,uBAAwB,CAAC,EAC1CzF,KACA,CAAC;QACD;QACA,IAAAwF,aAAO,GACP;QACA,IAAAC,QAAE,EAAE,mBAAmB,EAAE,uBAAwB,CAAC,EAClDzF,KAAK,EACLqB,IACA,CAAC;MACL;MACA,OAAO,CAAEA,IAAI,GACV,IAAAmE,aAAO,GACP;MACA,IAAAC,QAAE,EAAE,YAAY,EAAE,uBAAwB,CAAC,EAC3CzF,KAAK,EACLE,KACA,CAAC,CAAC;MAAA,EACF,IAAAsF,aAAO,GACP;MACA,IAAAC,QAAE,EAAE,kBAAkB,EAAE,uBAAwB,CAAC,EACjDzF,KAAK,EACLE,KAAK,EACLmB,IACA,CAAC;IACL,CAAC;IAED,MAAMqE,aAAa,GAAGxE,wBAAwB,CAC7CxB,eAAM,CAACU,IAAI,EACXV,eAAM,CAACiG,QACR,CAAC;IACD,MAAMC,gBAAgB,GAAG1E,wBAAwB,CAChDxB,eAAM,CAACmG,WAAW,EAClBnG,eAAM,CAACoG,eACR,CAAC;IACD,MAAMC,aAAa,GAAG,CACrBrG,eAAM,CAACsG,aAAa,EACpB/I,KAAK,IAAIyC,eAAM,CAACuG,gBAAgB,CAChC;IACD,MAAMC,sBAAsB,GAC3B,IAAI,CAACzI,KAAK,CAACG,qBAAqB,IAAIwB,UAAU,GAAG,MAAM,GAAG,MAAM;IACjE,MAAM;MAAE+G,KAAK;MAAEC;IAAQ,CAAC,GAAGjF,kBAAkB,IAAI,CAAC,CAAC;IAEnD,MAAMkF,OAAO,GACZxG,aAAa,KAAK2B,SAAS,GACxB3B,aAAa,GACb2D,mBAAc,CAAC3D,aAAa,EAAEwG,OAAO;IAEzC,OACC,IAAA5B,MAAA,CAAAC,aAAA,EAAC3H,OAAA,CAAAuJ,OAAe;MACflH,UAAU,EACTA,UAAU,KAAKoC,SAAS,GACrBpC,UAAU,GACV,CAAE,IAAI,CAAC3B,KAAK,CAACC,cAChB;MACD2B,kBAAkB,EAAGkG,qBAAqB,CAAC,CAAG;MAC9ChG,iBAAiB,EAAGA,iBAAiB,IAAI,QAAU;MACnDD,iBAAiB,EAChBmC,eAAe,GACZ;MACA,IAAA8E,QAAE,EAAE,+BAAgC,CAAC,GACrCjH,iBACH;MACDE,QAAQ,EAAGA,QAAQ,IAAI,CAAEqD,aAAe;MACxChD,aAAa,EAAGwG,OAAS;MACzBvG,OAAO,EAAGgD,WAAa;MACvB/C,WAAW,EAAGA,WAAa;MAC3BkB,KAAK,EAAG,CAAEvB,eAAM,CAAC8G,YAAY,EAAEvF,KAAK,CAAI;MACxCG,UAAU,EAAGA;IAAY,GAEvBoB,gBAAgB,IAAI,IAAAiC,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MAACxF,KAAK,EAAGsB,cAAc,CAAC;IAAG,CAAE,CAAC,EACzD,IAAAkC,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MACJxF,KAAK,EAAGwB,mBAAqB;MAC7BwC,aAAa,EAAGiB;IAAwB,GAExC,IAAAzB,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MAACxF,KAAK,EAAG0B;IAAoB,GACjC,IAAA8B,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MAACxF,KAAK,EAAGvB,eAAM,CAACkD;IAAkB,GACpCxC,IAAI,IACL,IAAAqE,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MACJxF,KAAK,EAAG,CACPvB,eAAM,CAACkD,gBAAgB,EACvBlD,eAAM,CAACgH,WAAW;IAChB,GAEH,IAAAjC,MAAA,CAAAC,aAAA,EAACnI,WAAA,CAAAoK,IAAI;MACJC,IAAI;MACJxG,IAAI,EAAGA,IAAM;MACbyG,IAAI,EAAG,EAAI;MACXC,IAAI,EACHxG,SAAS,CAAC+D,KAAK,IACfqB,aAAa,CAACrB,KACd;MACDpD,KAAK,EAAGX,SAAW;MACnByG,SAAS,EAAG;IAAO,CACnB,CAAC,EACF,IAAAtC,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MACJxF,KAAK,EACJuC,mBAAc,CAACwD;IACf,CACD,CACI,CACN,EACC/G,QAAQ,IAAID,KAAK,IAClB,IAAAyE,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI,QACJ,IAAAhC,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAgJ,IAAI;MACJpE,KAAK,EAAG,CACPiB,iBAAiB,EACjB3B,UAAU;IACR,GAEDP,KACG,CAAC,EACP,IAAAyE,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAgJ,IAAI;MAACpE,KAAK,EAAGkB;IAA0B,GACrClC,QACG,CACD,CACN,EACC,CAAEA,QAAQ,IAAID,KAAK,IACpB,IAAAyE,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAgJ,IAAI;MACJpE,KAAK,EAAG,CAAEiB,iBAAiB,EAAE3B,UAAU;IAAI,GAEzCP,KACG,CAEF,CAAC,EACLmB,kBAAkB,IACnB,IAAAsD,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAA4K,gBAAgB;MAChBnH,OAAO,EAAGsG,OAAS;MACnB7G,iBAAiB,EAAG;IAAU,GAE9B,IAAAkF,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAgJ,IAAI;MAACpE,KAAK,EAAG2E;IAAkB,GAC7BO,KACG,CACW,CAEd,CAAC,EACLpF,UAAU,IACX,IAAA0D,MAAA,CAAAC,aAAA,EAACnI,WAAA,CAAAoK,IAAI;MACJvG,IAAI,EAAG8G,YAAO;MACdJ,IAAI,EAAGtD,mBAAc,CAACzC,UAAU,CAACsD,KAAO;MACxC8C,MAAM,EAAC;IAAiC,CACxC,CACD,EACC5F,SAAS,IAAImC,iBAAiB,CAAC,CAAC,EAClC,IAAAe,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MACJxF,KAAK,EAAG,CACPzB,QAAQ,IAAIC,aAAa,EACzBC,eAAM,CAACkD,gBAAgB,CACrB;MACHqC,aAAa,EAAGzF,QAAQ,GAAG,MAAM,GAAG;IAAQ,GAE1CqB,QACG,CAAC,EACLrB,QAAQ,IAAII,YAAY,IACzB,IAAA6E,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MAACxF,KAAK,EAAGvB,eAAM,CAACC;IAAc,GAClC,IAAA8E,MAAA,CAAAC,aAAA,EAAC1H,SAAA,CAAAsJ,OAAQ,MAAE,CACN,CAEF,CAAC,EACLjF,IAAI,IACL,IAAAoD,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAgJ,IAAI;MAACpE,KAAK,EAAG,CAAE8E,aAAa,EAAErG,eAAM,CAAC4E,gBAAgB;IAAI,GACvDjD,IACG,CACN,EACC,CAAEmB,gBAAgB,IAAI,IAAAiC,MAAA,CAAAC,aAAA,EAACrI,YAAA,CAAAoK,IAAI;MAACxF,KAAK,EAAGsB,cAAc,CAAC;IAAG,CAAE,CAC1C,CAAC;EAEpB;AACD;AAAC,IAAA6E,QAAA,GAEc,IAAAC,iCAAwB,EAAEjK,eAAgB,CAAC;AAAAkK,OAAA,CAAAhB,OAAA,GAAAc,QAAA"}