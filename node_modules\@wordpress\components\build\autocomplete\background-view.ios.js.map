{"version": 3, "names": ["_blur", "require", "_style", "_interopRequireDefault", "<PERSON><PERSON><PERSON><PERSON>", "children", "_react", "createElement", "BlurView", "style", "styles", "blurType", "blurAmount", "_default", "exports", "default"], "sources": ["@wordpress/components/src/autocomplete/background-view.ios.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { BlurView } from '@react-native-community/blur';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nconst BackgroundView = ( { children } ) => {\n\treturn (\n\t\t<BlurView\n\t\t\tstyle={ styles[ 'components-autocomplete__background-blur' ] }\n\t\t\tblurType=\"prominent\"\n\t\t\tblurAmount={ 10 }\n\t\t>\n\t\t\t{ children }\n\t\t</BlurView>\n\t);\n};\n\nexport default BackgroundView;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAGA,MAAMG,cAAc,GAAGA,CAAE;EAAEC;AAAS,CAAC,KAAM;EAC1C,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACP,KAAA,CAAAQ,QAAQ;IACRC,KAAK,EAAGC,cAAM,CAAE,0CAA0C,CAAI;IAC9DC,QAAQ,EAAC,WAAW;IACpBC,UAAU,EAAG;EAAI,GAEfP,QACO,CAAC;AAEb,CAAC;AAAC,IAAAQ,QAAA,GAEaT,cAAc;AAAAU,OAAA,CAAAC,OAAA,GAAAF,QAAA"}