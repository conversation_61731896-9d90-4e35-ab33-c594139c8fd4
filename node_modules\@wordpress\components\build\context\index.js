"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  ContextSystemProvider: true,
  useComponentsContext: true,
  contextConnect: true,
  contextConnectWithoutRef: true,
  hasConnectNamespace: true,
  getConnectNamespace: true,
  useContextSystem: true
};
Object.defineProperty(exports, "ContextSystemProvider", {
  enumerable: true,
  get: function () {
    return _contextSystemProvider.ContextSystemProvider;
  }
});
Object.defineProperty(exports, "contextConnect", {
  enumerable: true,
  get: function () {
    return _contextConnect.contextConnect;
  }
});
Object.defineProperty(exports, "contextConnectWithoutRef", {
  enumerable: true,
  get: function () {
    return _contextConnect.contextConnectWithoutRef;
  }
});
Object.defineProperty(exports, "getConnectNamespace", {
  enumerable: true,
  get: function () {
    return _contextConnect.getConnectNamespace;
  }
});
Object.defineProperty(exports, "hasConnectNamespace", {
  enumerable: true,
  get: function () {
    return _contextConnect.hasConnectNamespace;
  }
});
Object.defineProperty(exports, "useComponentsContext", {
  enumerable: true,
  get: function () {
    return _contextSystemProvider.useComponentsContext;
  }
});
Object.defineProperty(exports, "useContextSystem", {
  enumerable: true,
  get: function () {
    return _useContextSystem.useContextSystem;
  }
});
var _contextSystemProvider = require("./context-system-provider");
var _contextConnect = require("./context-connect");
var _useContextSystem = require("./use-context-system");
var _wordpressComponent = require("./wordpress-component");
Object.keys(_wordpressComponent).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _wordpressComponent[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _wordpressComponent[key];
    }
  });
});
//# sourceMappingURL=index.js.map