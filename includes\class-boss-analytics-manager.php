<?php
/**
 * Gestionnaire principal du module Analytics
 *
 * Gère l'authentification OAuth, les tokens, et les API REST pour GA4 et GSC
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe principale du gestionnaire Analytics
 */
class Boss_Analytics_Manager {

    /**
     * Instance unique de la classe
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Manager    $instance    Instance unique
     */
    private static $instance = null;

    /**
     * URL du proxy OAuth
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $proxy_url    URL du proxy OAuth
     */
    private $proxy_url;

    /**
     * Gestionnaire Google Analytics
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_GA4_Manager    $ga4_manager    Gestionnaire GA4
     */
    private $ga4_manager;

    /**
     * Gestionnaire Google Search Console
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_GSC_Manager    $gsc_manager    Gestionnaire GSC
     */
    private $gsc_manager;

    /**
     * Gestionnaire de cache
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Cache    $cache_manager    Gestionnaire de cache
     */
    private $cache_manager;

    /**
     * Constructeur privé pour le singleton
     *
     * @since    1.2.0
     */
    private function __construct() {
        $this->proxy_url = 'https://oauth-proxy.bossseo.com'; // URL du proxy OAuth
        $this->init_managers();
        $this->register_hooks();
    }

    /**
     * Obtient l'instance unique de la classe
     *
     * @since    1.2.0
     * @return   Boss_Analytics_Manager    Instance unique
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialise les gestionnaires
     *
     * @since    1.2.0
     */
    private function init_managers() {
        // Charger les gestionnaires
        require_once plugin_dir_path(__FILE__) . 'analytics/class-boss-ga4-manager.php';
        require_once plugin_dir_path(__FILE__) . 'analytics/class-boss-gsc-manager.php';
        require_once plugin_dir_path(__FILE__) . 'analytics/class-boss-analytics-cache.php';
        require_once plugin_dir_path(__FILE__) . 'analytics/class-boss-analytics-auth.php';

        // Initialiser les gestionnaires
        $this->ga4_manager = new Boss_GA4_Manager();
        $this->gsc_manager = new Boss_GSC_Manager();
        $this->cache_manager = new Boss_Analytics_Cache();
    }

    /**
     * Enregistre les hooks WordPress
     *
     * @since    1.2.0
     */
    private function register_hooks() {
        // Hooks d'initialisation
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        add_action('init', array($this, 'schedule_data_sync'));

        // Hooks de nettoyage
        add_action('boss_analytics_sync_data', array($this, 'sync_analytics_data'));
        add_action('boss_analytics_cleanup_cache', array($this, 'cleanup_old_cache'));
    }

    /**
     * Enregistre les routes REST API
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        $namespace = 'boss-seo/v1';

        // Routes d'authentification
        register_rest_route($namespace, '/auth/callback', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_oauth_callback'),
            'permission_callback' => array($this, 'check_permissions'),
            'args' => array(
                'service' => array(
                    'required' => true,
                    'type' => 'string',
                    'enum' => array('ga4', 'gsc'),
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'tokens' => array(
                    'required' => true,
                    'type' => 'object'
                )
            )
        ));

        register_rest_route($namespace, '/auth/status', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_auth_status'),
            'permission_callback' => array($this, 'check_permissions')
        ));

        register_rest_route($namespace, '/auth/disconnect/(?P<service>[a-zA-Z0-9-]+)', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'disconnect_service'),
            'permission_callback' => array($this, 'check_permissions')
        ));

        // Routes Google Analytics 4
        register_rest_route($namespace, '/ga4/overview', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_ga4_overview'),
            'permission_callback' => array($this, 'check_permissions'),
            'args' => array(
                'property_id' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'date_range' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'last30days',
                    'enum' => array('last7days', 'last30days', 'last90days', 'last12months'),
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        register_rest_route($namespace, '/ga4/pages', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_ga4_pages'),
            'permission_callback' => array($this, 'check_permissions'),
            'args' => array(
                'property_id' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'date_range' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'last30days',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'limit' => array(
                    'required' => false,
                    'type' => 'integer',
                    'default' => 50,
                    'sanitize_callback' => 'absint'
                )
            )
        ));

        register_rest_route($namespace, '/ga4/traffic', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_ga4_traffic'),
            'permission_callback' => array($this, 'check_permissions'),
            'args' => array(
                'property_id' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'date_range' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'last30days',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Routes Google Search Console
        register_rest_route($namespace, '/gsc/keywords', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_gsc_keywords'),
            'permission_callback' => array($this, 'check_permissions'),
            'args' => array(
                'property_url' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'esc_url_raw'
                ),
                'date_range' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'last30days',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'limit' => array(
                    'required' => false,
                    'type' => 'integer',
                    'default' => 100,
                    'sanitize_callback' => 'absint'
                )
            )
        ));

        register_rest_route($namespace, '/gsc/performance', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_gsc_performance'),
            'permission_callback' => array($this, 'check_permissions'),
            'args' => array(
                'property_url' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'esc_url_raw'
                ),
                'date_range' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'last30days',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Routes de gestion
        register_rest_route($namespace, '/analytics/sync', array(
            'methods' => 'POST',
            'callback' => array($this, 'force_sync_data'),
            'permission_callback' => array($this, 'check_permissions')
        ));

        register_rest_route($namespace, '/analytics/cache/clear', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'clear_analytics_cache'),
            'permission_callback' => array($this, 'check_permissions')
        ));
    }

    /**
     * Vérifie les permissions utilisateur
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions
     */
    public function check_permissions() {
        return current_user_can('manage_options');
    }

    /**
     * Gère le callback OAuth depuis le proxy
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function handle_oauth_callback($request) {
        $service = $request->get_param('service');
        $tokens = $request->get_param('tokens');

        try {
            // Valider les tokens
            if (!isset($tokens['access_token']) || empty($tokens['access_token'])) {
                throw new Exception(__('Token d\'accès manquant', 'boss-seo'));
            }

            // Sauvegarder les tokens selon le service
            $auth_manager = new Boss_Analytics_Auth();
            $result = $auth_manager->save_tokens($service, $tokens);

            if (!$result) {
                throw new Exception(__('Erreur lors de la sauvegarde des tokens', 'boss-seo'));
            }

            // Tester la connexion
            $test_result = $this->test_service_connection($service);

            if (!$test_result['success']) {
                throw new Exception($test_result['message']);
            }

            // Déclencher une synchronisation initiale
            wp_schedule_single_event(time() + 30, 'boss_analytics_sync_data');

            return rest_ensure_response(array(
                'success' => true,
                'message' => sprintf(__('Connexion %s établie avec succès', 'boss-seo'), strtoupper($service)),
                'service' => $service,
                'properties' => $test_result['properties'] ?? array()
            ));

        } catch (Exception $e) {
            return new WP_Error(
                'oauth_callback_error',
                $e->getMessage(),
                array('status' => 400)
            );
        }
    }

    /**
     * Obtient le statut d'authentification des services
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function get_auth_status($request) {
        $auth_manager = new Boss_Analytics_Auth();

        $status = array(
            'ga4' => array(
                'connected' => $auth_manager->is_service_connected('ga4'),
                'properties' => $auth_manager->get_service_properties('ga4'),
                'last_sync' => $auth_manager->get_last_sync('ga4')
            ),
            'gsc' => array(
                'connected' => $auth_manager->is_service_connected('gsc'),
                'properties' => $auth_manager->get_service_properties('gsc'),
                'last_sync' => $auth_manager->get_last_sync('gsc')
            )
        );

        return rest_ensure_response($status);
    }

    /**
     * Déconnecte un service
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function disconnect_service($request) {
        $service = $request->get_param('service');

        if (!in_array($service, array('ga4', 'gsc'))) {
            return new WP_Error(
                'invalid_service',
                __('Service invalide', 'boss-seo'),
                array('status' => 400)
            );
        }

        $auth_manager = new Boss_Analytics_Auth();
        $result = $auth_manager->disconnect_service($service);

        if ($result) {
            // Nettoyer le cache du service
            $this->cache_manager->clear_service_cache($service);

            return rest_ensure_response(array(
                'success' => true,
                'message' => sprintf(__('Service %s déconnecté', 'boss-seo'), strtoupper($service))
            ));
        } else {
            return new WP_Error(
                'disconnect_error',
                __('Erreur lors de la déconnexion', 'boss-seo'),
                array('status' => 500)
            );
        }
    }

    /**
     * Teste la connexion à un service
     *
     * @since    1.2.0
     * @param    string    $service    Service à tester (ga4 ou gsc)
     * @return   array                 Résultat du test
     */
    private function test_service_connection($service) {
        try {
            switch ($service) {
                case 'ga4':
                    return $this->ga4_manager->test_connection();
                case 'gsc':
                    return $this->gsc_manager->test_connection();
                default:
                    return array(
                        'success' => false,
                        'message' => __('Service non supporté', 'boss-seo')
                    );
            }
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Programme la synchronisation des données
     *
     * @since    1.2.0
     */
    public function schedule_data_sync() {
        // Synchronisation des données toutes les 6 heures
        if (!wp_next_scheduled('boss_analytics_sync_data')) {
            wp_schedule_event(time(), 'boss_analytics_6hours', 'boss_analytics_sync_data');
        }

        // Nettoyage du cache tous les jours
        if (!wp_next_scheduled('boss_analytics_cleanup_cache')) {
            wp_schedule_event(time(), 'daily', 'boss_analytics_cleanup_cache');
        }

        // Ajouter l'intervalle personnalisé
        add_filter('cron_schedules', array($this, 'add_custom_cron_intervals'));
    }

    /**
     * Ajoute des intervalles CRON personnalisés
     *
     * @since    1.2.0
     * @param    array    $schedules    Intervalles existants
     * @return   array                  Intervalles avec les nouveaux
     */
    public function add_custom_cron_intervals($schedules) {
        $schedules['boss_analytics_6hours'] = array(
            'interval' => 6 * HOUR_IN_SECONDS,
            'display' => __('Toutes les 6 heures', 'boss-seo')
        );

        return $schedules;
    }

    /**
     * Récupère les données d'aperçu GA4
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function get_ga4_overview($request) {
        $property_id = $request->get_param('property_id');
        $date_range = $request->get_param('date_range');

        $data = $this->ga4_manager->get_overview_data($property_id, $date_range);

        if (is_wp_error($data)) {
            return $data;
        }

        return rest_ensure_response($data);
    }

    /**
     * Récupère les données de trafic GA4
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function get_ga4_traffic($request) {
        $property_id = $request->get_param('property_id');
        $date_range = $request->get_param('date_range');

        $data = $this->ga4_manager->get_traffic_data($property_id, $date_range);

        if (is_wp_error($data)) {
            return $data;
        }

        return rest_ensure_response($data);
    }

    /**
     * Récupère les données des pages GA4
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function get_ga4_pages($request) {
        $property_id = $request->get_param('property_id');
        $date_range = $request->get_param('date_range');
        $limit = $request->get_param('limit');

        $data = $this->ga4_manager->get_pages_data($property_id, $date_range, $limit);

        if (is_wp_error($data)) {
            return $data;
        }

        return rest_ensure_response($data);
    }

    /**
     * Récupère les données de mots-clés GSC
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function get_gsc_keywords($request) {
        $property_url = $request->get_param('property_url');
        $date_range = $request->get_param('date_range');
        $limit = $request->get_param('limit');

        $data = $this->gsc_manager->get_keywords_data($property_url, $date_range, $limit);

        if (is_wp_error($data)) {
            return $data;
        }

        return rest_ensure_response($data);
    }

    /**
     * Récupère les données de performance GSC
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function get_gsc_performance($request) {
        $property_url = $request->get_param('property_url');
        $date_range = $request->get_param('date_range');

        $data = $this->gsc_manager->get_performance_data($property_url, $date_range);

        if (is_wp_error($data)) {
            return $data;
        }

        return rest_ensure_response($data);
    }

    /**
     * Force la synchronisation des données
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function force_sync_data($request) {
        try {
            // Vider le cache
            $this->cache_manager->flush_all();

            // Déclencher la synchronisation
            wp_schedule_single_event(time() + 10, 'boss_analytics_sync_data');

            return rest_ensure_response(array(
                'success' => true,
                'message' => __('Synchronisation des données lancée', 'boss-seo')
            ));

        } catch (Exception $e) {
            return new WP_Error(
                'sync_error',
                $e->getMessage(),
                array('status' => 500)
            );
        }
    }

    /**
     * Vide le cache Analytics
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Requête REST
     * @return   WP_REST_Response               Réponse REST
     */
    public function clear_analytics_cache($request) {
        $result = $this->cache_manager->flush_all();

        if ($result) {
            return rest_ensure_response(array(
                'success' => true,
                'message' => __('Cache Analytics vidé avec succès', 'boss-seo')
            ));
        } else {
            return new WP_Error(
                'cache_clear_error',
                __('Erreur lors du vidage du cache', 'boss-seo'),
                array('status' => 500)
            );
        }
    }

    /**
     * Synchronise les données Analytics (CRON)
     *
     * @since    1.2.0
     */
    public function sync_analytics_data() {
        try {
            // Précharger le cache avec les données essentielles
            $this->cache_manager->warmup_cache();

            // Logger l'événement
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('[Boss SEO Analytics] Synchronisation des données terminée');
            }

        } catch (Exception $e) {
            error_log('[Boss SEO Analytics] Erreur lors de la synchronisation: ' . $e->getMessage());
        }
    }

    /**
     * Nettoie le cache ancien (CRON)
     *
     * @since    1.2.0
     */
    public function cleanup_old_cache() {
        $deleted_count = $this->cache_manager->cleanup_expired();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("[Boss SEO Analytics] Nettoyage du cache: {$deleted_count} entrées supprimées");
        }
    }
}
