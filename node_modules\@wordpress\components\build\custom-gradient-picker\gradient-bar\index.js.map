{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_controlPoints", "_utils", "_constants", "customGradientBarReducer", "state", "action", "type", "id", "insertPosition", "customGradientBarReducerInitialState", "CustomGradientBar", "background", "hasGradient", "value", "controlPoints", "onChange", "disableInserter", "disable<PERSON><PERSON>pha", "__experimentalIsRenderedInSidebar", "gradientMarkersContainerDomRef", "useRef", "gradientBarState", "gradientBarStateDispatch", "useReducer", "onMouseEnterAndMove", "event", "current", "getHorizontalRelativeGradientPosition", "clientX", "some", "position", "Math", "abs", "MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT", "onMouseLeave", "isMovingInserter", "isInsertingControlPoint", "_react", "createElement", "className", "classnames", "onMouseEnter", "onMouseMove", "style", "opacity", "ref", "default", "InsertPoint", "onOpenInserter", "onCloseInserter", "disable<PERSON><PERSON><PERSON>", "gradientPickerDomRef", "ignoreMarkerPosition", "undefined", "onStartControlPointChange", "onStopControlPointChange"], "sources": ["@wordpress/components/src/custom-gradient-picker/gradient-bar/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { useRef, useReducer } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport ControlPoints from './control-points';\nimport { getHorizontalRelativeGradientPosition } from './utils';\nimport { MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT } from './constants';\nimport type {\n\tCustomGradientBarProps,\n\tCustomGradientBarReducerState,\n\tCustomGradientBarReducerAction,\n\tCustomGradientBarIdleState,\n} from '../types';\nimport type { <PERSON><PERSON>vent<PERSON>andler } from 'react';\n\nconst customGradientBarReducer = (\n\tstate: CustomGradientBarReducerState,\n\taction: CustomGradientBarReducerAction\n): CustomGradientBarReducerState => {\n\tswitch ( action.type ) {\n\t\tcase 'MOVE_INSERTER':\n\t\t\tif ( state.id === 'IDLE' || state.id === 'MOVING_INSERTER' ) {\n\t\t\t\treturn {\n\t\t\t\t\tid: 'MOVING_INSERTER',\n\t\t\t\t\tinsertPosition: action.insertPosition,\n\t\t\t\t};\n\t\t\t}\n\t\t\tbreak;\n\t\tcase 'STOP_INSERTER_MOVE':\n\t\t\tif ( state.id === 'MOVING_INSERTER' ) {\n\t\t\t\treturn {\n\t\t\t\t\tid: 'IDLE',\n\t\t\t\t};\n\t\t\t}\n\t\t\tbreak;\n\t\tcase 'OPEN_INSERTER':\n\t\t\tif ( state.id === 'MOVING_INSERTER' ) {\n\t\t\t\treturn {\n\t\t\t\t\tid: 'INSERTING_CONTROL_POINT',\n\t\t\t\t\tinsertPosition: state.insertPosition,\n\t\t\t\t};\n\t\t\t}\n\t\t\tbreak;\n\t\tcase 'CLOSE_INSERTER':\n\t\t\tif ( state.id === 'INSERTING_CONTROL_POINT' ) {\n\t\t\t\treturn {\n\t\t\t\t\tid: 'IDLE',\n\t\t\t\t};\n\t\t\t}\n\t\t\tbreak;\n\t\tcase 'START_CONTROL_CHANGE':\n\t\t\tif ( state.id === 'IDLE' ) {\n\t\t\t\treturn {\n\t\t\t\t\tid: 'MOVING_CONTROL_POINT',\n\t\t\t\t};\n\t\t\t}\n\t\t\tbreak;\n\t\tcase 'STOP_CONTROL_CHANGE':\n\t\t\tif ( state.id === 'MOVING_CONTROL_POINT' ) {\n\t\t\t\treturn {\n\t\t\t\t\tid: 'IDLE',\n\t\t\t\t};\n\t\t\t}\n\t\t\tbreak;\n\t}\n\treturn state;\n};\nconst customGradientBarReducerInitialState: CustomGradientBarIdleState = {\n\tid: 'IDLE',\n};\n\nexport default function CustomGradientBar( {\n\tbackground,\n\thasGradient,\n\tvalue: controlPoints,\n\tonChange,\n\tdisableInserter = false,\n\tdisableAlpha = false,\n\t__experimentalIsRenderedInSidebar = false,\n}: CustomGradientBarProps ) {\n\tconst gradientMarkersContainerDomRef = useRef< HTMLDivElement >( null );\n\n\tconst [ gradientBarState, gradientBarStateDispatch ] = useReducer(\n\t\tcustomGradientBarReducer,\n\t\tcustomGradientBarReducerInitialState\n\t);\n\tconst onMouseEnterAndMove: MouseEventHandler< HTMLDivElement > = (\n\t\tevent\n\t) => {\n\t\tif ( ! gradientMarkersContainerDomRef.current ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst insertPosition = getHorizontalRelativeGradientPosition(\n\t\t\tevent.clientX,\n\t\t\tgradientMarkersContainerDomRef.current\n\t\t);\n\n\t\t// If the insert point is close to an existing control point don't show it.\n\t\tif (\n\t\t\tcontrolPoints.some( ( { position } ) => {\n\t\t\t\treturn (\n\t\t\t\t\tMath.abs( insertPosition - position ) <\n\t\t\t\t\tMINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT\n\t\t\t\t);\n\t\t\t} )\n\t\t) {\n\t\t\tif ( gradientBarState.id === 'MOVING_INSERTER' ) {\n\t\t\t\tgradientBarStateDispatch( { type: 'STOP_INSERTER_MOVE' } );\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tgradientBarStateDispatch( { type: 'MOVE_INSERTER', insertPosition } );\n\t};\n\n\tconst onMouseLeave = () => {\n\t\tgradientBarStateDispatch( { type: 'STOP_INSERTER_MOVE' } );\n\t};\n\n\tconst isMovingInserter = gradientBarState.id === 'MOVING_INSERTER';\n\tconst isInsertingControlPoint =\n\t\tgradientBarState.id === 'INSERTING_CONTROL_POINT';\n\n\treturn (\n\t\t<div\n\t\t\tclassName={ classnames(\n\t\t\t\t'components-custom-gradient-picker__gradient-bar',\n\t\t\t\t{ 'has-gradient': hasGradient }\n\t\t\t) }\n\t\t\tonMouseEnter={ onMouseEnterAndMove }\n\t\t\tonMouseMove={ onMouseEnterAndMove }\n\t\t\tonMouseLeave={ onMouseLeave }\n\t\t>\n\t\t\t<div\n\t\t\t\tclassName=\"components-custom-gradient-picker__gradient-bar-background\"\n\t\t\t\tstyle={ {\n\t\t\t\t\tbackground,\n\t\t\t\t\topacity: hasGradient ? 1 : 0.4,\n\t\t\t\t} }\n\t\t\t/>\n\t\t\t<div\n\t\t\t\tref={ gradientMarkersContainerDomRef }\n\t\t\t\tclassName=\"components-custom-gradient-picker__markers-container\"\n\t\t\t>\n\t\t\t\t{ ! disableInserter &&\n\t\t\t\t\t( isMovingInserter || isInsertingControlPoint ) && (\n\t\t\t\t\t\t<ControlPoints.InsertPoint\n\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdisableAlpha={ disableAlpha }\n\t\t\t\t\t\t\tinsertPosition={ gradientBarState.insertPosition }\n\t\t\t\t\t\t\tvalue={ controlPoints }\n\t\t\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\t\t\tonOpenInserter={ () => {\n\t\t\t\t\t\t\t\tgradientBarStateDispatch( {\n\t\t\t\t\t\t\t\t\ttype: 'OPEN_INSERTER',\n\t\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\tonCloseInserter={ () => {\n\t\t\t\t\t\t\t\tgradientBarStateDispatch( {\n\t\t\t\t\t\t\t\t\ttype: 'CLOSE_INSERTER',\n\t\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t<ControlPoints\n\t\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t}\n\t\t\t\t\tdisableAlpha={ disableAlpha }\n\t\t\t\t\tdisableRemove={ disableInserter }\n\t\t\t\t\tgradientPickerDomRef={ gradientMarkersContainerDomRef }\n\t\t\t\t\tignoreMarkerPosition={\n\t\t\t\t\t\tisInsertingControlPoint\n\t\t\t\t\t\t\t? gradientBarState.insertPosition\n\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t}\n\t\t\t\t\tvalue={ controlPoints }\n\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\tonStartControlPointChange={ () => {\n\t\t\t\t\t\tgradientBarStateDispatch( {\n\t\t\t\t\t\t\ttype: 'START_CONTROL_CHANGE',\n\t\t\t\t\t\t} );\n\t\t\t\t\t} }\n\t\t\t\t\tonStopControlPointChange={ () => {\n\t\t\t\t\t\tgradientBarStateDispatch( {\n\t\t\t\t\t\t\ttype: 'STOP_CONTROL_CHANGE',\n\t\t\t\t\t\t} );\n\t\t\t\t\t} }\n\t\t\t\t/>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AAfA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAYA,MAAMK,wBAAwB,GAAGA,CAChCC,KAAoC,EACpCC,MAAsC,KACH;EACnC,QAASA,MAAM,CAACC,IAAI;IACnB,KAAK,eAAe;MACnB,IAAKF,KAAK,CAACG,EAAE,KAAK,MAAM,IAAIH,KAAK,CAACG,EAAE,KAAK,iBAAiB,EAAG;QAC5D,OAAO;UACNA,EAAE,EAAE,iBAAiB;UACrBC,cAAc,EAAEH,MAAM,CAACG;QACxB,CAAC;MACF;MACA;IACD,KAAK,oBAAoB;MACxB,IAAKJ,KAAK,CAACG,EAAE,KAAK,iBAAiB,EAAG;QACrC,OAAO;UACNA,EAAE,EAAE;QACL,CAAC;MACF;MACA;IACD,KAAK,eAAe;MACnB,IAAKH,KAAK,CAACG,EAAE,KAAK,iBAAiB,EAAG;QACrC,OAAO;UACNA,EAAE,EAAE,yBAAyB;UAC7BC,cAAc,EAAEJ,KAAK,CAACI;QACvB,CAAC;MACF;MACA;IACD,KAAK,gBAAgB;MACpB,IAAKJ,KAAK,CAACG,EAAE,KAAK,yBAAyB,EAAG;QAC7C,OAAO;UACNA,EAAE,EAAE;QACL,CAAC;MACF;MACA;IACD,KAAK,sBAAsB;MAC1B,IAAKH,KAAK,CAACG,EAAE,KAAK,MAAM,EAAG;QAC1B,OAAO;UACNA,EAAE,EAAE;QACL,CAAC;MACF;MACA;IACD,KAAK,qBAAqB;MACzB,IAAKH,KAAK,CAACG,EAAE,KAAK,sBAAsB,EAAG;QAC1C,OAAO;UACNA,EAAE,EAAE;QACL,CAAC;MACF;MACA;EACF;EACA,OAAOH,KAAK;AACb,CAAC;AACD,MAAMK,oCAAgE,GAAG;EACxEF,EAAE,EAAE;AACL,CAAC;AAEc,SAASG,iBAAiBA,CAAE;EAC1CC,UAAU;EACVC,WAAW;EACXC,KAAK,EAAEC,aAAa;EACpBC,QAAQ;EACRC,eAAe,GAAG,KAAK;EACvBC,YAAY,GAAG,KAAK;EACpBC,iCAAiC,GAAG;AACb,CAAC,EAAG;EAC3B,MAAMC,8BAA8B,GAAG,IAAAC,eAAM,EAAoB,IAAK,CAAC;EAEvE,MAAM,CAAEC,gBAAgB,EAAEC,wBAAwB,CAAE,GAAG,IAAAC,mBAAU,EAChEpB,wBAAwB,EACxBM,oCACD,CAAC;EACD,MAAMe,mBAAwD,GAC7DC,KAAK,IACD;IACJ,IAAK,CAAEN,8BAA8B,CAACO,OAAO,EAAG;MAC/C;IACD;IAEA,MAAMlB,cAAc,GAAG,IAAAmB,4CAAqC,EAC3DF,KAAK,CAACG,OAAO,EACbT,8BAA8B,CAACO,OAChC,CAAC;;IAED;IACA,IACCZ,aAAa,CAACe,IAAI,CAAE,CAAE;MAAEC;IAAS,CAAC,KAAM;MACvC,OACCC,IAAI,CAACC,GAAG,CAAExB,cAAc,GAAGsB,QAAS,CAAC,GACrCG,sDAA2C;IAE7C,CAAE,CAAC,EACF;MACD,IAAKZ,gBAAgB,CAACd,EAAE,KAAK,iBAAiB,EAAG;QAChDe,wBAAwB,CAAE;UAAEhB,IAAI,EAAE;QAAqB,CAAE,CAAC;MAC3D;MACA;IACD;IAEAgB,wBAAwB,CAAE;MAAEhB,IAAI,EAAE,eAAe;MAAEE;IAAe,CAAE,CAAC;EACtE,CAAC;EAED,MAAM0B,YAAY,GAAGA,CAAA,KAAM;IAC1BZ,wBAAwB,CAAE;MAAEhB,IAAI,EAAE;IAAqB,CAAE,CAAC;EAC3D,CAAC;EAED,MAAM6B,gBAAgB,GAAGd,gBAAgB,CAACd,EAAE,KAAK,iBAAiB;EAClE,MAAM6B,uBAAuB,GAC5Bf,gBAAgB,CAACd,EAAE,KAAK,yBAAyB;EAElD,OACC,IAAA8B,MAAA,CAAAC,aAAA;IACCC,SAAS,EAAG,IAAAC,mBAAU,EACrB,iDAAiD,EACjD;MAAE,cAAc,EAAE5B;IAAY,CAC/B,CAAG;IACH6B,YAAY,EAAGjB,mBAAqB;IACpCkB,WAAW,EAAGlB,mBAAqB;IACnCU,YAAY,EAAGA;EAAc,GAE7B,IAAAG,MAAA,CAAAC,aAAA;IACCC,SAAS,EAAC,4DAA4D;IACtEI,KAAK,EAAG;MACPhC,UAAU;MACViC,OAAO,EAAEhC,WAAW,GAAG,CAAC,GAAG;IAC5B;EAAG,CACH,CAAC,EACF,IAAAyB,MAAA,CAAAC,aAAA;IACCO,GAAG,EAAG1B,8BAAgC;IACtCoB,SAAS,EAAC;EAAsD,GAE9D,CAAEvB,eAAe,KAChBmB,gBAAgB,IAAIC,uBAAuB,CAAE,IAC9C,IAAAC,MAAA,CAAAC,aAAA,EAACtC,cAAA,CAAA8C,OAAa,CAACC,WAAW;IACzB7B,iCAAiC,EAChCA,iCACA;IACDD,YAAY,EAAGA,YAAc;IAC7BT,cAAc,EAAGa,gBAAgB,CAACb,cAAgB;IAClDK,KAAK,EAAGC,aAAe;IACvBC,QAAQ,EAAGA,QAAU;IACrBiC,cAAc,EAAGA,CAAA,KAAM;MACtB1B,wBAAwB,CAAE;QACzBhB,IAAI,EAAE;MACP,CAAE,CAAC;IACJ,CAAG;IACH2C,eAAe,EAAGA,CAAA,KAAM;MACvB3B,wBAAwB,CAAE;QACzBhB,IAAI,EAAE;MACP,CAAE,CAAC;IACJ;EAAG,CACH,CACD,EACF,IAAA+B,MAAA,CAAAC,aAAA,EAACtC,cAAA,CAAA8C,OAAa;IACb5B,iCAAiC,EAChCA,iCACA;IACDD,YAAY,EAAGA,YAAc;IAC7BiC,aAAa,EAAGlC,eAAiB;IACjCmC,oBAAoB,EAAGhC,8BAAgC;IACvDiC,oBAAoB,EACnBhB,uBAAuB,GACpBf,gBAAgB,CAACb,cAAc,GAC/B6C,SACH;IACDxC,KAAK,EAAGC,aAAe;IACvBC,QAAQ,EAAGA,QAAU;IACrBuC,yBAAyB,EAAGA,CAAA,KAAM;MACjChC,wBAAwB,CAAE;QACzBhB,IAAI,EAAE;MACP,CAAE,CAAC;IACJ,CAAG;IACHiD,wBAAwB,EAAGA,CAAA,KAAM;MAChCjC,wBAAwB,CAAE;QACzBhB,IAAI,EAAE;MACP,CAAE,CAAC;IACJ;EAAG,CACH,CACG,CACD,CAAC;AAER"}