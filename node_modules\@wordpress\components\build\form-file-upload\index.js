"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FormFileUpload = FormFileUpload;
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _button = _interopRequireDefault(require("../button"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * FormFileUpload is a component that allows users to select files from their local device.
 *
 * ```jsx
 * import { FormFileUpload } from '@wordpress/components';
 *
 * const MyFormFileUpload = () => (
 *   <FormFileUpload
 *     accept="image/*"
 *     onChange={ ( event ) => console.log( event.currentTarget.files ) }
 *   >
 *     Upload
 *   </FormFileUpload>
 * );
 * ```
 */
function FormFileUpload({
  accept,
  children,
  multiple = false,
  onChange,
  onClick,
  render,
  ...props
}) {
  const ref = (0, _element.useRef)(null);
  const openFileDialog = () => {
    ref.current?.click();
  };
  const ui = render ? render({
    openFileDialog
  }) : (0, _react.createElement)(_button.default, {
    onClick: openFileDialog,
    ...props
  }, children);
  return (0, _react.createElement)("div", {
    className: "components-form-file-upload"
  }, ui, (0, _react.createElement)("input", {
    type: "file",
    ref: ref,
    multiple: multiple,
    style: {
      display: 'none'
    },
    accept: accept,
    onChange: onChange,
    onClick: onClick,
    "data-testid": "form-file-upload-input"
  }));
}
var _default = FormFileUpload;
exports.default = _default;
//# sourceMappingURL=index.js.map