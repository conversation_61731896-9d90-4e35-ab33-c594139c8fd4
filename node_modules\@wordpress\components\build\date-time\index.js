"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "DatePicker", {
  enumerable: true,
  get: function () {
    return _date.default;
  }
});
Object.defineProperty(exports, "TimePicker", {
  enumerable: true,
  get: function () {
    return _time.default;
  }
});
exports.default = void 0;
var _date = _interopRequireDefault(require("./date"));
var _time = _interopRequireDefault(require("./time"));
var _dateTime = _interopRequireDefault(require("./date-time"));
/**
 * Internal dependencies
 */
var _default = _dateTime.default;
exports.default = _default;
//# sourceMappingURL=index.js.map