{"version": 3, "names": ["_element", "require", "styles", "_interopRequireWildcard", "_utils", "_context", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useBorderBoxControl", "props", "className", "colors", "onChange", "enableAlpha", "enableStyle", "size", "value", "__experimentalIsRenderedInSidebar", "__next40pxDefaultSize", "otherProps", "useContextSystem", "computedSize", "mixedBorders", "hasMixedBorders", "splitBorders", "hasSplitBorders", "linkedValue", "getCommonBorder", "splitValue", "getSplitBorders", "hasWidthValue", "isNaN", "parseFloat", "width", "isLinked", "setIsLinked", "useState", "toggleLinked", "onLinkedChange", "newBorder", "undefined", "isCompleteBorder", "isEmptyBorder", "changes", "getBorderDiff", "updatedBorders", "top", "right", "bottom", "left", "filteredResult", "onSplitChange", "side", "cx", "useCx", "classes", "useMemo", "borderBoxControl", "linkedControlClassName", "linkedBorderControl", "wrapperClassName", "wrapper", "disableUnits"], "sources": ["@wordpress/components/src/border-box-control/border-box-control/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMemo, useState } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport * as styles from '../styles';\nimport {\n\tgetBorderDiff,\n\tgetCommonBorder,\n\tgetSplitBorders,\n\thasMixedBorders,\n\thasSplitBorders,\n\tisCompleteBorder,\n\tisEmptyBorder,\n} from '../utils';\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useCx } from '../../utils/hooks/use-cx';\n\nimport type { Border } from '../../border-control/types';\nimport type { Borders, BorderSide, BorderBoxControlProps } from '../types';\n\nexport function useBorderBoxControl(\n\tprops: WordPressComponentProps< BorderBoxControlProps, 'div' >\n) {\n\tconst {\n\t\tclassName,\n\t\tcolors = [],\n\t\tonChange,\n\t\tenableAlpha = false,\n\t\tenableStyle = true,\n\t\tsize = 'default',\n\t\tvalue,\n\t\t__experimentalIsRenderedInSidebar = false,\n\t\t__next40pxDefaultSize,\n\t\t...otherProps\n\t} = useContextSystem( props, 'BorderBoxControl' );\n\n\tconst computedSize =\n\t\tsize === 'default' && __next40pxDefaultSize ? '__unstable-large' : size;\n\n\tconst mixedBorders = hasMixedBorders( value );\n\tconst splitBorders = hasSplitBorders( value );\n\n\tconst linkedValue = splitBorders\n\t\t? getCommonBorder( value as Borders | undefined )\n\t\t: ( value as Border );\n\n\tconst splitValue = splitBorders\n\t\t? ( value as Borders )\n\t\t: getSplitBorders( value as Border | undefined );\n\n\t// If no numeric width value is set, the unit select will be disabled.\n\tconst hasWidthValue = ! isNaN( parseFloat( `${ linkedValue?.width }` ) );\n\n\tconst [ isLinked, setIsLinked ] = useState( ! mixedBorders );\n\tconst toggleLinked = () => setIsLinked( ! isLinked );\n\n\tconst onLinkedChange = ( newBorder?: Border ) => {\n\t\tif ( ! newBorder ) {\n\t\t\treturn onChange( undefined );\n\t\t}\n\n\t\t// If we have all props defined on the new border apply it.\n\t\tif ( ! mixedBorders || isCompleteBorder( newBorder ) ) {\n\t\t\treturn onChange(\n\t\t\t\tisEmptyBorder( newBorder ) ? undefined : newBorder\n\t\t\t);\n\t\t}\n\n\t\t// If we had mixed borders we might have had some shared border props\n\t\t// that we need to maintain. For example; we could have mixed borders\n\t\t// with all the same color but different widths. Then from the linked\n\t\t// control we change the color. We should keep the separate  widths.\n\t\tconst changes = getBorderDiff(\n\t\t\tlinkedValue as Border,\n\t\t\tnewBorder as Border\n\t\t);\n\t\tconst updatedBorders = {\n\t\t\ttop: { ...( value as Borders )?.top, ...changes },\n\t\t\tright: { ...( value as Borders )?.right, ...changes },\n\t\t\tbottom: { ...( value as Borders )?.bottom, ...changes },\n\t\t\tleft: { ...( value as Borders )?.left, ...changes },\n\t\t};\n\n\t\tif ( hasMixedBorders( updatedBorders ) ) {\n\t\t\treturn onChange( updatedBorders );\n\t\t}\n\n\t\tconst filteredResult = isEmptyBorder( updatedBorders.top )\n\t\t\t? undefined\n\t\t\t: updatedBorders.top;\n\n\t\tonChange( filteredResult );\n\t};\n\n\tconst onSplitChange = (\n\t\tnewBorder: Border | undefined,\n\t\tside: BorderSide\n\t) => {\n\t\tconst updatedBorders = { ...splitValue, [ side ]: newBorder };\n\n\t\tif ( hasMixedBorders( updatedBorders ) ) {\n\t\t\tonChange( updatedBorders );\n\t\t} else {\n\t\t\tonChange( newBorder );\n\t\t}\n\t};\n\n\tconst cx = useCx();\n\tconst classes = useMemo( () => {\n\t\treturn cx( styles.borderBoxControl, className );\n\t}, [ cx, className ] );\n\n\tconst linkedControlClassName = useMemo( () => {\n\t\treturn cx( styles.linkedBorderControl() );\n\t}, [ cx ] );\n\n\tconst wrapperClassName = useMemo( () => {\n\t\treturn cx( styles.wrapper );\n\t}, [ cx ] );\n\n\treturn {\n\t\t...otherProps,\n\t\tclassName: classes,\n\t\tcolors,\n\t\tdisableUnits: mixedBorders && ! hasWidthValue,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\thasMixedBorders: mixedBorders,\n\t\tisLinked,\n\t\tlinkedControlClassName,\n\t\tonLinkedChange,\n\t\tonSplitChange,\n\t\ttoggleLinked,\n\t\tlinkedValue,\n\t\tsize: computedSize,\n\t\tsplitValue,\n\t\twrapperClassName,\n\t\t__experimentalIsRenderedInSidebar,\n\t};\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAUA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAAiD,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AApBjD;AACA;AACA;;AAGA;AACA;AACA;;AAkBO,SAASW,mBAAmBA,CAClCC,KAA8D,EAC7D;EACD,MAAM;IACLC,SAAS;IACTC,MAAM,GAAG,EAAE;IACXC,QAAQ;IACRC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAG,IAAI;IAClBC,IAAI,GAAG,SAAS;IAChBC,KAAK;IACLC,iCAAiC,GAAG,KAAK;IACzCC,qBAAqB;IACrB,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEX,KAAK,EAAE,kBAAmB,CAAC;EAEjD,MAAMY,YAAY,GACjBN,IAAI,KAAK,SAAS,IAAIG,qBAAqB,GAAG,kBAAkB,GAAGH,IAAI;EAExE,MAAMO,YAAY,GAAG,IAAAC,sBAAe,EAAEP,KAAM,CAAC;EAC7C,MAAMQ,YAAY,GAAG,IAAAC,sBAAe,EAAET,KAAM,CAAC;EAE7C,MAAMU,WAAW,GAAGF,YAAY,GAC7B,IAAAG,sBAAe,EAAEX,KAA6B,CAAC,GAC7CA,KAAiB;EAEtB,MAAMY,UAAU,GAAGJ,YAAY,GAC1BR,KAAK,GACP,IAAAa,sBAAe,EAAEb,KAA4B,CAAC;;EAEjD;EACA,MAAMc,aAAa,GAAG,CAAEC,KAAK,CAAEC,UAAU,CAAG,GAAGN,WAAW,EAAEO,KAAO,EAAE,CAAE,CAAC;EAExE,MAAM,CAAEC,QAAQ,EAAEC,WAAW,CAAE,GAAG,IAAAC,iBAAQ,EAAE,CAAEd,YAAa,CAAC;EAC5D,MAAMe,YAAY,GAAGA,CAAA,KAAMF,WAAW,CAAE,CAAED,QAAS,CAAC;EAEpD,MAAMI,cAAc,GAAKC,SAAkB,IAAM;IAChD,IAAK,CAAEA,SAAS,EAAG;MAClB,OAAO3B,QAAQ,CAAE4B,SAAU,CAAC;IAC7B;;IAEA;IACA,IAAK,CAAElB,YAAY,IAAI,IAAAmB,uBAAgB,EAAEF,SAAU,CAAC,EAAG;MACtD,OAAO3B,QAAQ,CACd,IAAA8B,oBAAa,EAAEH,SAAU,CAAC,GAAGC,SAAS,GAAGD,SAC1C,CAAC;IACF;;IAEA;IACA;IACA;IACA;IACA,MAAMI,OAAO,GAAG,IAAAC,oBAAa,EAC5BlB,WAAW,EACXa,SACD,CAAC;IACD,MAAMM,cAAc,GAAG;MACtBC,GAAG,EAAE;QAAE,GAAK9B,KAAK,EAAe8B,GAAG;QAAE,GAAGH;MAAQ,CAAC;MACjDI,KAAK,EAAE;QAAE,GAAK/B,KAAK,EAAe+B,KAAK;QAAE,GAAGJ;MAAQ,CAAC;MACrDK,MAAM,EAAE;QAAE,GAAKhC,KAAK,EAAegC,MAAM;QAAE,GAAGL;MAAQ,CAAC;MACvDM,IAAI,EAAE;QAAE,GAAKjC,KAAK,EAAeiC,IAAI;QAAE,GAAGN;MAAQ;IACnD,CAAC;IAED,IAAK,IAAApB,sBAAe,EAAEsB,cAAe,CAAC,EAAG;MACxC,OAAOjC,QAAQ,CAAEiC,cAAe,CAAC;IAClC;IAEA,MAAMK,cAAc,GAAG,IAAAR,oBAAa,EAAEG,cAAc,CAACC,GAAI,CAAC,GACvDN,SAAS,GACTK,cAAc,CAACC,GAAG;IAErBlC,QAAQ,CAAEsC,cAAe,CAAC;EAC3B,CAAC;EAED,MAAMC,aAAa,GAAGA,CACrBZ,SAA6B,EAC7Ba,IAAgB,KACZ;IACJ,MAAMP,cAAc,GAAG;MAAE,GAAGjB,UAAU;MAAE,CAAEwB,IAAI,GAAIb;IAAU,CAAC;IAE7D,IAAK,IAAAhB,sBAAe,EAAEsB,cAAe,CAAC,EAAG;MACxCjC,QAAQ,CAAEiC,cAAe,CAAC;IAC3B,CAAC,MAAM;MACNjC,QAAQ,CAAE2B,SAAU,CAAC;IACtB;EACD,CAAC;EAED,MAAMc,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,OAAOH,EAAE,CAAExE,MAAM,CAAC4E,gBAAgB,EAAE/C,SAAU,CAAC;EAChD,CAAC,EAAE,CAAE2C,EAAE,EAAE3C,SAAS,CAAG,CAAC;EAEtB,MAAMgD,sBAAsB,GAAG,IAAAF,gBAAO,EAAE,MAAM;IAC7C,OAAOH,EAAE,CAAExE,MAAM,CAAC8E,mBAAmB,CAAC,CAAE,CAAC;EAC1C,CAAC,EAAE,CAAEN,EAAE,CAAG,CAAC;EAEX,MAAMO,gBAAgB,GAAG,IAAAJ,gBAAO,EAAE,MAAM;IACvC,OAAOH,EAAE,CAAExE,MAAM,CAACgF,OAAQ,CAAC;EAC5B,CAAC,EAAE,CAAER,EAAE,CAAG,CAAC;EAEX,OAAO;IACN,GAAGlC,UAAU;IACbT,SAAS,EAAE6C,OAAO;IAClB5C,MAAM;IACNmD,YAAY,EAAExC,YAAY,IAAI,CAAEQ,aAAa;IAC7CjB,WAAW;IACXC,WAAW;IACXS,eAAe,EAAED,YAAY;IAC7BY,QAAQ;IACRwB,sBAAsB;IACtBpB,cAAc;IACda,aAAa;IACbd,YAAY;IACZX,WAAW;IACXX,IAAI,EAAEM,YAAY;IAClBO,UAAU;IACVgC,gBAAgB;IAChB3C;EACD,CAAC;AACF"}