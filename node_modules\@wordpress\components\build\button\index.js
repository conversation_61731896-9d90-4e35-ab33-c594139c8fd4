"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Button = void 0;
exports.UnforwardedButton = UnforwardedButton;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _tooltip = _interopRequireDefault(require("../tooltip"));
var _icon = _interopRequireDefault(require("../icon"));
var _visuallyHidden = require("../visually-hidden");
var _utils = require("../popover/utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const disabledEventsOnDisabledButton = ['onMouseDown', 'onClick'];
function useDeprecatedProps({
  isDefault,
  isPrimary,
  isSecondary,
  isTertiary,
  isLink,
  isPressed,
  isSmall,
  size,
  variant,
  ...otherProps
}) {
  let computedSize = size;
  let computedVariant = variant;
  const newProps = {
    // @TODO Mark `isPressed` as deprecated
    'aria-pressed': isPressed
  };
  if (isSmall) {
    var _computedSize;
    (_computedSize = computedSize) !== null && _computedSize !== void 0 ? _computedSize : computedSize = 'small';
  }
  if (isPrimary) {
    var _computedVariant;
    (_computedVariant = computedVariant) !== null && _computedVariant !== void 0 ? _computedVariant : computedVariant = 'primary';
  }
  if (isTertiary) {
    var _computedVariant2;
    (_computedVariant2 = computedVariant) !== null && _computedVariant2 !== void 0 ? _computedVariant2 : computedVariant = 'tertiary';
  }
  if (isSecondary) {
    var _computedVariant3;
    (_computedVariant3 = computedVariant) !== null && _computedVariant3 !== void 0 ? _computedVariant3 : computedVariant = 'secondary';
  }
  if (isDefault) {
    var _computedVariant4;
    (0, _deprecated.default)('Button isDefault prop', {
      since: '5.4',
      alternative: 'variant="secondary"',
      version: '6.2'
    });
    (_computedVariant4 = computedVariant) !== null && _computedVariant4 !== void 0 ? _computedVariant4 : computedVariant = 'secondary';
  }
  if (isLink) {
    var _computedVariant5;
    (_computedVariant5 = computedVariant) !== null && _computedVariant5 !== void 0 ? _computedVariant5 : computedVariant = 'link';
  }
  return {
    ...newProps,
    ...otherProps,
    size: computedSize,
    variant: computedVariant
  };
}
function UnforwardedButton(props, ref) {
  const {
    __next40pxDefaultSize,
    isBusy,
    isDestructive,
    className,
    disabled,
    icon,
    iconPosition = 'left',
    iconSize,
    showTooltip,
    tooltipPosition,
    shortcut,
    label,
    children,
    size = 'default',
    text,
    variant,
    __experimentalIsFocusable: isFocusable,
    describedBy,
    ...buttonOrAnchorProps
  } = useDeprecatedProps(props);
  const {
    href,
    target,
    'aria-checked': ariaChecked,
    'aria-pressed': ariaPressed,
    'aria-selected': ariaSelected,
    ...additionalProps
  } = 'href' in buttonOrAnchorProps ? buttonOrAnchorProps : {
    href: undefined,
    target: undefined,
    ...buttonOrAnchorProps
  };
  const instanceId = (0, _compose.useInstanceId)(Button, 'components-button__description');
  const hasChildren = 'string' === typeof children && !!children || Array.isArray(children) && children?.[0] && children[0] !== null &&
  // Tooltip should not considered as a child
  children?.[0]?.props?.className !== 'components-tooltip';
  const truthyAriaPressedValues = [true, 'true', 'mixed'];
  const classes = (0, _classnames.default)('components-button', className, {
    'is-next-40px-default-size': __next40pxDefaultSize,
    'is-secondary': variant === 'secondary',
    'is-primary': variant === 'primary',
    'is-small': size === 'small',
    'is-compact': size === 'compact',
    'is-tertiary': variant === 'tertiary',
    'is-pressed': truthyAriaPressedValues.includes(ariaPressed),
    'is-pressed-mixed': ariaPressed === 'mixed',
    'is-busy': isBusy,
    'is-link': variant === 'link',
    'is-destructive': isDestructive,
    'has-text': !!icon && (hasChildren || text),
    'has-icon': !!icon
  });
  const trulyDisabled = disabled && !isFocusable;
  const Tag = href !== undefined && !trulyDisabled ? 'a' : 'button';
  const buttonProps = Tag === 'button' ? {
    type: 'button',
    disabled: trulyDisabled,
    'aria-checked': ariaChecked,
    'aria-pressed': ariaPressed,
    'aria-selected': ariaSelected
  } : {};
  const anchorProps = Tag === 'a' ? {
    href,
    target
  } : {};
  if (disabled && isFocusable) {
    // In this case, the button will be disabled, but still focusable and
    // perceivable by screen reader users.
    buttonProps['aria-disabled'] = true;
    anchorProps['aria-disabled'] = true;
    for (const disabledEvent of disabledEventsOnDisabledButton) {
      additionalProps[disabledEvent] = event => {
        if (event) {
          event.stopPropagation();
          event.preventDefault();
        }
      };
    }
  }

  // Should show the tooltip if...
  const shouldShowTooltip = !trulyDisabled && (
  // An explicit tooltip is passed or...
  showTooltip && !!label ||
  // There's a shortcut or...
  !!shortcut ||
  // There's a label and...
  !!label &&
  // The children are empty and...
  !children?.length &&
  // The tooltip is not explicitly disabled.
  false !== showTooltip);
  const descriptionId = describedBy ? instanceId : undefined;
  const describedById = additionalProps['aria-describedby'] || descriptionId;
  const commonProps = {
    className: classes,
    'aria-label': additionalProps['aria-label'] || label,
    'aria-describedby': describedById,
    ref
  };
  const elementChildren = (0, _react.createElement)(_react.Fragment, null, icon && iconPosition === 'left' && (0, _react.createElement)(_icon.default, {
    icon: icon,
    size: iconSize
  }), text && (0, _react.createElement)(_react.Fragment, null, text), icon && iconPosition === 'right' && (0, _react.createElement)(_icon.default, {
    icon: icon,
    size: iconSize
  }), children);
  const element = Tag === 'a' ? (0, _react.createElement)("a", {
    ...anchorProps,
    ...additionalProps,
    ...commonProps
  }, elementChildren) : (0, _react.createElement)("button", {
    ...buttonProps,
    ...additionalProps,
    ...commonProps
  }, elementChildren);

  // In order to avoid some React reconciliation issues, we are always rendering
  // the `Tooltip` component even when `shouldShowTooltip` is `false`.
  // In order to make sure that the tooltip doesn't show when it shouldn't,
  // we don't pass the props to the `Tooltip` component.
  const tooltipProps = shouldShowTooltip ? {
    text: children?.length && describedBy ? describedBy : label,
    shortcut,
    placement: tooltipPosition &&
    // Convert legacy `position` values to be used with the new `placement` prop
    (0, _utils.positionToPlacement)(tooltipPosition)
  } : {};
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_tooltip.default, {
    ...tooltipProps
  }, element), describedBy && (0, _react.createElement)(_visuallyHidden.VisuallyHidden, null, (0, _react.createElement)("span", {
    id: descriptionId
  }, describedBy)));
}

/**
 * Lets users take actions and make choices with a single click or tap.
 *
 * ```jsx
 * import { Button } from '@wordpress/components';
 * const Mybutton = () => (
 *   <Button
 *     variant="primary"
 *     onClick={ handleClick }
 *   >
 *     Click here
 *   </Button>
 * );
 * ```
 */
const Button = (0, _element.forwardRef)(UnforwardedButton);
exports.Button = Button;
var _default = Button;
exports.default = _default;
//# sourceMappingURL=index.js.map