{"version": 3, "names": ["_reactNative", "require", "_i18n", "_cell", "_interopRequireDefault", "EMPTY_STYLE", "BottomSheetSwitchCell", "props", "value", "onValueChange", "disabled", "cellProps", "onPress", "getAccessibilityLabel", "help", "sprintf", "_x", "label", "_react", "createElement", "default", "accessibilityLabel", "accessibilityRole", "accessibilityHint", "__", "editable", "disabledStyle", "Switch"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/switch-cell.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Switch } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { __, _x, sprintf } from '@wordpress/i18n';\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\n\nconst EMPTY_STYLE = {};\n\nexport default function BottomSheetSwitchCell( props ) {\n\tconst { value, onValueChange, disabled, ...cellProps } = props;\n\n\tconst onPress = () => {\n\t\tonValueChange( ! value );\n\t};\n\n\tconst getAccessibilityLabel = () => {\n\t\tif ( ! cellProps.help ) {\n\t\t\treturn value\n\t\t\t\t? sprintf(\n\t\t\t\t\t\t/* translators: accessibility text. Switch setting ON state. %s: Switch title. */\n\t\t\t\t\t\t_x( '%s. On', 'switch control' ),\n\t\t\t\t\t\tcellProps.label\n\t\t\t\t  )\n\t\t\t\t: sprintf(\n\t\t\t\t\t\t/* translators: accessibility text. Switch setting OFF state. %s: Switch title. */\n\t\t\t\t\t\t_x( '%s. Off', 'switch control' ),\n\t\t\t\t\t\tcellProps.label\n\t\t\t\t  );\n\t\t}\n\t\treturn value\n\t\t\t? sprintf(\n\t\t\t\t\t/* translators: accessibility text. Switch setting ON state. %1: Switch title, %2: switch help. */\n\t\t\t\t\t_x( '%1$s, %2$s. On', 'switch control' ),\n\t\t\t\t\tcellProps.label,\n\t\t\t\t\tcellProps.help\n\t\t\t  )\n\t\t\t: sprintf(\n\t\t\t\t\t/* translators: accessibility text. Switch setting OFF state. %1: Switch title, %2: switch help. */\n\t\t\t\t\t_x( '%1$s, %2$s. Off', 'switch control' ),\n\t\t\t\t\tcellProps.label,\n\t\t\t\t\tcellProps.help\n\t\t\t  );\n\t};\n\n\treturn (\n\t\t<Cell\n\t\t\t{ ...cellProps }\n\t\t\taccessibilityLabel={ getAccessibilityLabel() }\n\t\t\taccessibilityRole={ 'none' }\n\t\t\taccessibilityHint={\n\t\t\t\t/* translators: accessibility text (hint for switches) */\n\t\t\t\t__( 'Double tap to toggle setting' )\n\t\t\t}\n\t\t\tonPress={ onPress }\n\t\t\teditable={ false }\n\t\t\tvalue={ '' }\n\t\t\tdisabled={ disabled }\n\t\t\tdisabledStyle={ EMPTY_STYLE }\n\t\t>\n\t\t\t<Switch\n\t\t\t\tvalue={ value }\n\t\t\t\tonValueChange={ onValueChange }\n\t\t\t\tdisabled={ disabled }\n\t\t\t/>\n\t\t</Cell>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AAIA,IAAAE,KAAA,GAAAC,sBAAA,CAAAH,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAGA,MAAMI,WAAW,GAAG,CAAC,CAAC;AAEP,SAASC,qBAAqBA,CAAEC,KAAK,EAAG;EACtD,MAAM;IAAEC,KAAK;IAAEC,aAAa;IAAEC,QAAQ;IAAE,GAAGC;EAAU,CAAC,GAAGJ,KAAK;EAE9D,MAAMK,OAAO,GAAGA,CAAA,KAAM;IACrBH,aAAa,CAAE,CAAED,KAAM,CAAC;EACzB,CAAC;EAED,MAAMK,qBAAqB,GAAGA,CAAA,KAAM;IACnC,IAAK,CAAEF,SAAS,CAACG,IAAI,EAAG;MACvB,OAAON,KAAK,GACT,IAAAO,aAAO,GACP;MACA,IAAAC,QAAE,EAAE,QAAQ,EAAE,gBAAiB,CAAC,EAChCL,SAAS,CAACM,KACV,CAAC,GACD,IAAAF,aAAO,GACP;MACA,IAAAC,QAAE,EAAE,SAAS,EAAE,gBAAiB,CAAC,EACjCL,SAAS,CAACM,KACV,CAAC;IACL;IACA,OAAOT,KAAK,GACT,IAAAO,aAAO,GACP;IACA,IAAAC,QAAE,EAAE,gBAAgB,EAAE,gBAAiB,CAAC,EACxCL,SAAS,CAACM,KAAK,EACfN,SAAS,CAACG,IACV,CAAC,GACD,IAAAC,aAAO,GACP;IACA,IAAAC,QAAE,EAAE,iBAAiB,EAAE,gBAAiB,CAAC,EACzCL,SAAS,CAACM,KAAK,EACfN,SAAS,CAACG,IACV,CAAC;EACL,CAAC;EAED,OACC,IAAAI,MAAA,CAAAC,aAAA,EAAChB,KAAA,CAAAiB,OAAI;IAAA,GACCT,SAAS;IACdU,kBAAkB,EAAGR,qBAAqB,CAAC,CAAG;IAC9CS,iBAAiB,EAAG,MAAQ;IAC5BC,iBAAiB,EAChB;IACA,IAAAC,QAAE,EAAE,8BAA+B,CACnC;IACDZ,OAAO,EAAGA,OAAS;IACnBa,QAAQ,EAAG,KAAO;IAClBjB,KAAK,EAAG,EAAI;IACZE,QAAQ,EAAGA,QAAU;IACrBgB,aAAa,EAAGrB;EAAa,GAE7B,IAAAa,MAAA,CAAAC,aAAA,EAACnB,YAAA,CAAA2B,MAAM;IACNnB,KAAK,EAAGA,KAAO;IACfC,aAAa,EAAGA,aAAe;IAC/BC,QAAQ,EAAGA;EAAU,CACrB,CACI,CAAC;AAET"}