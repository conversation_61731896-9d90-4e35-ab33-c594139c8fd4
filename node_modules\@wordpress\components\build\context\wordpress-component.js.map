{"version": 3, "names": [], "sources": ["@wordpress/components/src/context/wordpress-component.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type * as React from 'react';\n\n// Based on https://github.com/reakit/reakit/blob/master/packages/reakit-utils/src/types.ts\nexport type WordPressComponentProps<\n\t/** Prop types. */\n\tP,\n\t/** The HTML element to inherit props from. */\n\tT extends React.ElementType | null,\n\t/** Supports polymorphism through the `as` prop. */\n\tIsPolymorphic extends boolean = true,\n> = P &\n\t( T extends React.ElementType\n\t\t? // The `children` prop is being explicitly omitted since it is otherwise implicitly added\n\t\t  // by `ComponentPropsWithRef`. The context is that components should require the `children`\n\t\t  // prop explicitly when needed (see https://github.com/WordPress/gutenberg/pull/31817).\n\t\t  Omit<\n\t\t\t\tReact.ComponentPropsWithoutRef< T >,\n\t\t\t\t'as' | keyof P | 'children'\n\t\t  >\n\t\t: {} ) &\n\t( IsPolymorphic extends true\n\t\t? {\n\t\t\t\t/** The HTML element or React component to render the component as. */\n\t\t\t\tas?: T | keyof JSX.IntrinsicElements;\n\t\t  }\n\t\t: {} );\n\nexport type WordPressComponent<\n\tT extends React.ElementType | null,\n\tO,\n\tIsPolymorphic extends boolean,\n> = {\n\t< TT extends React.ElementType >(\n\t\tprops: WordPressComponentProps< O, TT, IsPolymorphic > &\n\t\t\t( IsPolymorphic extends true ? { as: TT } : {} )\n\t): JSX.Element | null;\n\t(\n\t\tprops: WordPressComponentProps< O, T, IsPolymorphic >\n\t): JSX.Element | null;\n\tdisplayName?: string;\n\t/**\n\t * A CSS selector used to fake component interpolation in styled components\n\t * for components not generated by `styled`. Anything passed to `contextConnect`\n\t * will get this property.\n\t *\n\t * We restrict it to a class to align with the already existing class names that\n\t * are generated by the context system.\n\t */\n\tselector?: `.${ string }`;\n};\n\nexport type WordPressComponentFromProps<\n\tProps,\n\tForwardsRef extends boolean = true,\n> = Props extends WordPressComponentProps< infer P, infer T, infer I >\n\t? WordPressComponent<\n\t\t\tT,\n\t\t\tP & ( ForwardsRef extends true ? React.RefAttributes< any > : {} ),\n\t\t\tI\n\t  >\n\t: never;\n"], "mappings": ""}