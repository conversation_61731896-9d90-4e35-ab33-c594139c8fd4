{"version": 3, "names": ["_icons", "require", "_i18n", "_context", "_toggleGroupControl", "BORDER_STYLES", "label", "__", "icon", "lineSolid", "value", "lineDashed", "lineDotted", "UnconnectedBorderControlStylePicker", "onChange", "restProps", "forwardedRef", "_react", "createElement", "ToggleGroupControl", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "ref", "isDeselectable", "map", "borderStyle", "ToggleGroupControlOptionIcon", "key", "BorderControlStylePicker", "contextConnect", "_default", "exports", "default"], "sources": ["@wordpress/components/src/border-control/border-control-style-picker/component.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { lineDashed, lineDotted, lineSolid } from '@wordpress/icons';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { contextConnect } from '../../context';\nimport type { StylePickerProps } from '../types';\nimport {\n\tToggleGroupControl,\n\tToggleGroupControlOptionIcon,\n} from '../../toggle-group-control';\n\nconst BORDER_STYLES = [\n\t{ label: __( 'Solid' ), icon: lineSolid, value: 'solid' },\n\t{ label: __( 'Dashed' ), icon: lineDashed, value: 'dashed' },\n\t{ label: __( 'Dotted' ), icon: lineDotted, value: 'dotted' },\n];\n\nfunction UnconnectedBorderControlStylePicker(\n\t{ onChange, ...restProps }: StylePickerProps,\n\tforwardedRef: React.ForwardedRef< any >\n) {\n\treturn (\n\t\t<ToggleGroupControl\n\t\t\t__nextHasNoMarginBottom\n\t\t\t__next40pxDefaultSize\n\t\t\tref={ forwardedRef }\n\t\t\tisDeselectable\n\t\t\tonChange={ ( value ) => {\n\t\t\t\tonChange?.( value as string | undefined );\n\t\t\t} }\n\t\t\t{ ...restProps }\n\t\t>\n\t\t\t{ BORDER_STYLES.map( ( borderStyle ) => (\n\t\t\t\t<ToggleGroupControlOptionIcon\n\t\t\t\t\tkey={ borderStyle.value }\n\t\t\t\t\tvalue={ borderStyle.value }\n\t\t\t\t\ticon={ borderStyle.icon }\n\t\t\t\t\tlabel={ borderStyle.label }\n\t\t\t\t/>\n\t\t\t) ) }\n\t\t</ToggleGroupControl>\n\t);\n}\n\nconst BorderControlStylePicker = contextConnect(\n\tUnconnectedBorderControlStylePicker,\n\t'BorderControlStylePicker'\n);\n\nexport default BorderControlStylePicker;\n"], "mappings": ";;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,mBAAA,GAAAH,OAAA;AAXA;AACA;AACA;;AAIA;AACA;AACA;;AAQA,MAAMI,aAAa,GAAG,CACrB;EAAEC,KAAK,EAAE,IAAAC,QAAE,EAAE,OAAQ,CAAC;EAAEC,IAAI,EAAEC,gBAAS;EAAEC,KAAK,EAAE;AAAQ,CAAC,EACzD;EAAEJ,KAAK,EAAE,IAAAC,QAAE,EAAE,QAAS,CAAC;EAAEC,IAAI,EAAEG,iBAAU;EAAED,KAAK,EAAE;AAAS,CAAC,EAC5D;EAAEJ,KAAK,EAAE,IAAAC,QAAE,EAAE,QAAS,CAAC;EAAEC,IAAI,EAAEI,iBAAU;EAAEF,KAAK,EAAE;AAAS,CAAC,CAC5D;AAED,SAASG,mCAAmCA,CAC3C;EAAEC,QAAQ;EAAE,GAAGC;AAA4B,CAAC,EAC5CC,YAAuC,EACtC;EACD,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACd,mBAAA,CAAAe,kBAAkB;IAClBC,uBAAuB;IACvBC,qBAAqB;IACrBC,GAAG,EAAGN,YAAc;IACpBO,cAAc;IACdT,QAAQ,EAAKJ,KAAK,IAAM;MACvBI,QAAQ,GAAIJ,KAA4B,CAAC;IAC1C,CAAG;IAAA,GACEK;EAAS,GAEZV,aAAa,CAACmB,GAAG,CAAIC,WAAW,IACjC,IAAAR,MAAA,CAAAC,aAAA,EAACd,mBAAA,CAAAsB,4BAA4B;IAC5BC,GAAG,EAAGF,WAAW,CAACf,KAAO;IACzBA,KAAK,EAAGe,WAAW,CAACf,KAAO;IAC3BF,IAAI,EAAGiB,WAAW,CAACjB,IAAM;IACzBF,KAAK,EAAGmB,WAAW,CAACnB;EAAO,CAC3B,CACA,CACiB,CAAC;AAEvB;AAEA,MAAMsB,wBAAwB,GAAG,IAAAC,uBAAc,EAC9ChB,mCAAmC,EACnC,0BACD,CAAC;AAAC,IAAAiB,QAAA,GAEaF,wBAAwB;AAAAG,OAAA,CAAAC,OAAA,GAAAF,QAAA"}