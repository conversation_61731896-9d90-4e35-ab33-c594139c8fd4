import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Dashicon,
  Spinner,
  Notice,
  TextControl,
  SelectControl,
  TabPanel
} from '@wordpress/components';
import { useState, useEffect } from '@wordpress/element';

// Services
import PerformanceService from '../../services/PerformanceService';
import TechnicalAnalysisService from '../../services/TechnicalAnalysisService';

// Composants internes
import AIRecommendationsSection from './AIRecommendationsSection';

/**
 * Composant pour afficher un indicateur Core Web Vital
 */
const WebVitalIndicator = ({ metric }) => {
  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'good':
        return 'boss-text-green-600';
      case 'needs-improvement':
        return 'boss-text-yellow-600';
      case 'poor':
        return 'boss-text-red-600';
      default:
        return 'boss-text-boss-gray';
    }
  };

  // Fonction pour obtenir la classe de fond en fonction du statut
  const getStatusBgClass = (status) => {
    switch (status) {
      case 'good':
        return 'boss-bg-green-100';
      case 'needs-improvement':
        return 'boss-bg-yellow-100';
      case 'poor':
        return 'boss-bg-red-100';
      default:
        return 'boss-bg-gray-100';
    }
  };

  // Fonction pour obtenir le libellé du statut
  const getStatusLabel = (status) => {
    switch (status) {
      case 'good':
        return __('Bon', 'boss-seo');
      case 'needs-improvement':
        return __('À améliorer', 'boss-seo');
      case 'poor':
        return __('Mauvais', 'boss-seo');
      default:
        return __('Non mesuré', 'boss-seo');
    }
  };

  // Fonction pour obtenir l'icône en fonction du statut
  const getStatusIcon = (status) => {
    switch (status) {
      case 'good':
        return 'yes-alt';
      case 'needs-improvement':
        return 'warning';
      case 'poor':
        return 'dismiss';
      default:
        return 'info';
    }
  };

  return (
    <Card className="boss-h-full">
      <CardBody>
        <div className="boss-flex boss-flex-col boss-items-center boss-text-center">
          <div className={`boss-w-16 boss-h-16 boss-rounded-full boss-flex boss-items-center boss-justify-center boss-mb-3 ${getStatusBgClass(metric.status)}`}>
            <span className={`boss-text-2xl boss-font-bold ${getStatusColorClass(metric.status)}`}>
              {metric.value}{metric.unit}
            </span>
          </div>

          <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
            {metric.description}
          </h4>

          <div className={`boss-flex boss-items-center boss-mt-2 boss-text-sm boss-font-medium ${getStatusColorClass(metric.status)}`}>
            <Dashicon icon={getStatusIcon(metric.status)} className="boss-mr-1" />
            <span>{getStatusLabel(metric.status)}</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

/**
 * Composant pour afficher un graphique d'évolution simple
 */
const SimpleChart = ({ data, color = 'boss-primary' }) => {
  // Trouver les valeurs min et max pour l'échelle
  const values = data.map(item => item.score);
  const maxValue = Math.max(...values, 0);
  const minValue = Math.min(...values, 0);
  const range = maxValue - minValue;

  // Calculer la tendance
  const firstValue = data[data.length - 1]?.score || 0;
  const lastValue = data[0]?.score || 0;
  const trend = lastValue - firstValue;

  return (
    <div className="boss-relative boss-h-40 boss-w-full">
      <div className="boss-absolute boss-inset-0 boss-flex boss-items-end boss-justify-between boss-pb-6">
        {data.map((item, index) => {
          // Calculer la hauteur de la barre
          const barHeight = range !== 0
            ? ((item.score - minValue) / range) * 150
            : 0;

          return (
            <div
              key={index}
              className="boss-relative boss-flex boss-flex-col boss-items-center boss-group"
              style={{ height: '100%', width: `${100 / data.length}%` }}
            >
              {/* Info bulle au survol */}
              <div className="boss-absolute boss-bottom-full boss-mb-2 boss-opacity-0 boss-group-hover:boss-opacity-100 boss-transition-opacity boss-duration-200 boss-pointer-events-none boss-z-10">
                <div className="boss-bg-boss-dark boss-text-white boss-rounded boss-py-1 boss-px-2 boss-text-xs boss-whitespace-nowrap">
                  <div className="boss-font-bold">{item.score}</div>
                  <div>{item.date}</div>
                </div>
                <div className="boss-w-2 boss-h-2 boss-bg-boss-dark boss-transform boss-rotate-45 boss-mx-auto boss-mt-[-4px]"></div>
              </div>

              {/* Barre du graphique */}
              <div
                className={`boss-w-6 boss-rounded-t boss-transition-all boss-duration-500 boss-ease-out boss-bg-${color}`}
                style={{ height: `${Math.max(barHeight, 2)}px` }}
              ></div>

              {/* Étiquette */}
              <div className="boss-text-xs boss-text-boss-gray boss-mt-2 boss-truncate boss-w-full boss-text-center">
                {item.date.split('-')[1]}
              </div>
            </div>
          );
        })}
      </div>

      {/* Ligne de base */}
      <div className="boss-absolute boss-bottom-6 boss-left-0 boss-right-0 boss-h-px boss-bg-gray-200"></div>
    </div>
  );
};

/**
 * Composant pour la section Performance
 */
const PerformanceSection = ({ performance }) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [performanceData, setPerformanceData] = useState(performance);
  const [activeTab, setActiveTab] = useState('general');

  // États pour l'analyse d'URL spécifique
  const [urlToAnalyze, setUrlToAnalyze] = useState('');
  const [strategy, setStrategy] = useState('mobile');
  const [urlAnalysisResults, setUrlAnalysisResults] = useState(null);
  const [urlAnalysisHistory, setUrlAnalysisHistory] = useState([]);
  const [popularPages, setPopularPages] = useState([]);
  const [aiSuggestions, setAiSuggestions] = useState({});

  // Charger les recommandations et l'historique des analyses d'URL au montage du composant
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setIsLoading(true);

        // Charger les recommandations générales
        const recommendationsResponse = await PerformanceService.getPerformanceRecommendations();
        setRecommendations(recommendationsResponse);

        // Charger l'historique des analyses d'URL
        try {
          const historyResponse = await TechnicalAnalysisService.getUrlAnalysisHistory();
          setUrlAnalysisHistory(historyResponse || []);

          // Si des analyses existent, charger la plus récente
          if (historyResponse && historyResponse.length > 0) {
            const latestAnalysis = historyResponse[0];
            const analysisResults = await TechnicalAnalysisService.getUrlAnalysisResults(latestAnalysis.id);
            setUrlAnalysisResults(analysisResults);

            // Récupérer les suggestions d'IA
            if (analysisResults && analysisResults.ai_suggestions) {
              setAiSuggestions(analysisResults.ai_suggestions);
            }
          }
        } catch (historyError) {
          console.warn('Erreur lors du chargement de l\'historique des analyses d\'URL:', historyError);
        }

        // Charger les pages populaires du site pour la sélection
        try {
          // Simuler la récupération des pages populaires (à remplacer par un appel API réel)
          setPopularPages([
            { url: window.location.origin, title: __('Page d\'accueil', 'boss-seo') },
            { url: window.location.origin + '/blog', title: __('Blog', 'boss-seo') },
            { url: window.location.origin + '/contact', title: __('Contact', 'boss-seo') },
            { url: window.location.origin + '/a-propos', title: __('À propos', 'boss-seo') },
          ]);
        } catch (pagesError) {
          console.warn('Erreur lors du chargement des pages populaires:', pagesError);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement des données initiales:', error);
        setError(__('Erreur lors du chargement des données. Veuillez réessayer.', 'boss-seo'));
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Fonction pour lancer une analyse de performance générale
  const handleAnalyzePerformance = async () => {
    try {
      setIsAnalyzing(true);
      setError(null);

      const response = await PerformanceService.analyzePerformance();

      if (response.success) {
        // Récupérer les nouvelles données de performance
        const newPerformanceData = await PerformanceService.getCoreWebVitals();
        setPerformanceData(newPerformanceData);

        // Récupérer les nouvelles recommandations
        const newRecommendations = await PerformanceService.getPerformanceRecommendations();
        setRecommendations(newRecommendations);
      } else {
        setError(response.message || __('Erreur lors de l\'analyse de performance. Veuillez réessayer.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse de performance:', error);
      setError(__('Erreur lors de l\'analyse de performance. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Fonction pour analyser une URL spécifique
  const handleAnalyzeUrl = async () => {
    try {
      setIsAnalyzing(true);
      setError(null);

      // Vérifier si l'URL est valide
      if (!urlToAnalyze) {
        setUrlToAnalyze(window.location.origin); // Utiliser la page d'accueil par défaut
      }

      // Lancer l'analyse
      const response = await TechnicalAnalysisService.analyzeSpecificUrl(urlToAnalyze, strategy);

      if (response.success) {
        // Mettre à jour les résultats
        setUrlAnalysisResults(response.results);

        // Mettre à jour l'historique des analyses
        const updatedHistory = await TechnicalAnalysisService.getUrlAnalysisHistory();
        setUrlAnalysisHistory(updatedHistory || []);

        // Mettre à jour les suggestions d'IA
        if (response.results && response.results.ai_suggestions) {
          setAiSuggestions(response.results.ai_suggestions);
        }

        // Passer à l'onglet des résultats
        setActiveTab('results');
      } else {
        setError(response.message || __('Erreur lors de l\'analyse de l\'URL. Veuillez réessayer.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse de l\'URL:', error);
      setError(__('Erreur lors de l\'analyse de l\'URL. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Fonction pour charger une analyse précédente
  const handleLoadAnalysis = async (analysisId) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await TechnicalAnalysisService.getUrlAnalysisResults(analysisId);

      if (response) {
        setUrlAnalysisResults(response);

        // Mettre à jour les suggestions d'IA
        if (response.ai_suggestions) {
          setAiSuggestions(response.ai_suggestions);
        }

        // Passer à l'onglet des résultats
        setActiveTab('results');
      } else {
        setError(__('Erreur lors du chargement de l\'analyse. Veuillez réessayer.', 'boss-seo'));
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'analyse:', error);
      setError(__('Erreur lors du chargement de l\'analyse. Veuillez réessayer.', 'boss-seo'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour implémenter une suggestion d'IA
  const handleImplementSuggestion = (suggestion, category) => {
    // Cette fonction pourrait être implémentée pour appliquer automatiquement les suggestions
    // Pour l'instant, afficher simplement un message d'information
    alert(__('Fonctionnalité d\'implémentation automatique en cours de développement. Veuillez appliquer manuellement cette suggestion.', 'boss-seo'));
  };

  // Extraire les données
  const { coreWebVitals, history } = performanceData;

  // Calculer le score global de performance
  const getOverallPerformanceScore = () => {
    const metrics = Object.values(coreWebVitals);
    const goodCount = metrics.filter(metric => metric.status === 'good').length;
    const needsImprovementCount = metrics.filter(metric => metric.status === 'needs-improvement').length;
    const poorCount = metrics.filter(metric => metric.status === 'poor').length;

    // Pondération : bon = 1, à améliorer = 0.5, mauvais = 0
    return Math.round((goodCount + needsImprovementCount * 0.5) / metrics.length * 100);
  };

  // Obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 90) return 'boss-text-green-600';
    if (score >= 70) return 'boss-text-yellow-600';
    if (score >= 50) return 'boss-text-orange-600';
    return 'boss-text-red-600';
  };

  // Obtenir la classe de fond en fonction du score
  const getScoreBgClass = (score) => {
    if (score >= 90) return 'boss-bg-green-100';
    if (score >= 70) return 'boss-bg-yellow-100';
    if (score >= 50) return 'boss-bg-orange-100';
    return 'boss-bg-red-100';
  };

  // Obtenir le libellé d'évaluation en fonction du score
  const getScoreLabel = (score) => {
    if (score >= 90) return __('Excellent', 'boss-seo');
    if (score >= 70) return __('Bon', 'boss-seo');
    if (score >= 50) return __('Moyen', 'boss-seo');
    return __('À améliorer', 'boss-seo');
  };

  const performanceScore = getOverallPerformanceScore();

  // Formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div>
      {/* Onglets principaux */}
      <TabPanel
        className="boss-mb-6"
        activeClass="boss-bg-white boss-border-b-2 boss-border-boss-primary boss-text-boss-primary"
        tabs={[
          {
            name: 'general',
            title: (
              <div className="boss-flex boss-items-center boss-px-4 boss-py-2">
                <Dashicon icon="dashboard" className="boss-mr-2" />
                {__('Analyse générale', 'boss-seo')}
              </div>
            ),
          },
          {
            name: 'specific',
            title: (
              <div className="boss-flex boss-items-center boss-px-4 boss-py-2">
                <Dashicon icon="search" className="boss-mr-2" />
                {__('Analyse d\'URL spécifique', 'boss-seo')}
              </div>
            ),
          },
          {
            name: 'results',
            title: (
              <div className="boss-flex boss-items-center boss-px-4 boss-py-2">
                <Dashicon icon="chart-bar" className="boss-mr-2" />
                {__('Résultats d\'analyse', 'boss-seo')}
              </div>
            ),
          },
          {
            name: 'ai',
            title: (
              <div className="boss-flex boss-items-center boss-px-4 boss-py-2">
                <Dashicon icon="lightbulb" className="boss-mr-2" />
                {__('Suggestions IA', 'boss-seo')}
              </div>
            ),
          },
        ]}
        onSelect={(tabName) => setActiveTab(tabName)}
        selected={activeTab}
      >
        {(tab) => {
          if (tab.name === 'general') {
            // Onglet d'analyse générale
            return (
              <div>
                {/* Score de performance global */}
                <Card className="boss-mb-6">
                  <CardBody>
                    <div className="boss-flex boss-flex-wrap boss-items-center boss-justify-between boss-gap-6">
                      <div className="boss-flex boss-items-center">
                        <div className="boss-relative boss-w-24 boss-h-24 boss-mr-6">
                          {/* Cercle de fond */}
                          <svg className="boss-w-full boss-h-full boss-transform boss-rotate-[-90deg]" viewBox="0 0 100 100">
                            <circle
                              cx="50"
                              cy="50"
                              r="45"
                              fill="transparent"
                              stroke="#e5e7eb"
                              strokeWidth="10"
                            />
                            {/* Cercle de progression */}
                            <circle
                              cx="50"
                              cy="50"
                              r="45"
                              fill="transparent"
                              stroke={
                                performanceScore >= 90 ? '#10B981' :
                                performanceScore >= 70 ? '#FBBF24' :
                                performanceScore >= 50 ? '#F97316' :
                                '#EF4444'
                              }
                              strokeWidth="10"
                              strokeDasharray={`${2 * Math.PI * 45 * performanceScore / 100} ${2 * Math.PI * 45 * (1 - performanceScore / 100)}`}
                              strokeLinecap="round"
                              className="boss-transition-all boss-duration-1000 boss-ease-out"
                            />
                          </svg>
                          {/* Score au centre */}
                          <div className="boss-absolute boss-inset-0 boss-flex boss-flex-col boss-items-center boss-justify-center">
                            <span className={`boss-text-3xl boss-font-bold ${getScoreColorClass(performanceScore)}`}>
                              {performanceScore}
                            </span>
                          </div>
                        </div>

                        <div>
                          <h3 className="boss-text-xl boss-font-bold boss-text-boss-dark boss-mb-2">
                            {__('Performance globale', 'boss-seo')}
                          </h3>
                          <div className={`boss-inline-block boss-px-3 boss-py-1 boss-rounded-full boss-text-sm boss-font-medium ${getScoreBgClass(performanceScore)} ${getScoreColorClass(performanceScore)}`}>
                            {getScoreLabel(performanceScore)}
                          </div>
                          <p className="boss-text-boss-gray boss-mt-2">
                            {__('Basé sur les Core Web Vitals et autres métriques de performance', 'boss-seo')}
                          </p>
                        </div>
                      </div>

                      <div>
                        <Button
                          isPrimary
                          className="boss-flex boss-items-center"
                          onClick={handleAnalyzePerformance}
                          isBusy={isAnalyzing}
                          disabled={isAnalyzing}
                        >
                          <Dashicon icon="dashboard" className="boss-mr-1" />
                          {isAnalyzing
                            ? __('Analyse en cours...', 'boss-seo')
                            : __('Analyser la performance', 'boss-seo')
                          }
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            );
          } else if (tab.name === 'specific') {
            // Onglet d'analyse d'URL spécifique
            return (
              <div>
                <Card className="boss-mb-6">
                  <CardHeader className="boss-border-b boss-border-gray-200">
                    <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                      {__('Analyser une URL spécifique', 'boss-seo')}
                    </h3>
                  </CardHeader>
                  <CardBody>
                    <div className="boss-space-y-4">
                      {error && (
                        <Notice status="error" isDismissible={true} onRemove={() => setError(null)}>
                          {error}
                        </Notice>
                      )}

                      <div>
                        <label className="boss-block boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('URL à analyser', 'boss-seo')}
                        </label>
                        <TextControl
                          value={urlToAnalyze}
                          onChange={setUrlToAnalyze}
                          placeholder={__('Entrez l\'URL à analyser (ex: https://example.com/page)', 'boss-seo')}
                          className="boss-w-full"
                        />
                        <p className="boss-text-xs boss-text-boss-gray boss-mt-1">
                          {__('Laissez vide pour analyser la page d\'accueil', 'boss-seo')}
                        </p>
                      </div>

                      <div>
                        <label className="boss-block boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Pages populaires', 'boss-seo')}
                        </label>
                        <div className="boss-flex boss-flex-wrap boss-gap-2">
                          {popularPages.map((page, index) => (
                            <Button
                              key={index}
                              isSecondary
                              isSmall
                              onClick={() => setUrlToAnalyze(page.url)}
                            >
                              {page.title}
                            </Button>
                          ))}
                        </div>
                      </div>

                      <div>
                        <label className="boss-block boss-text-sm boss-font-medium boss-text-boss-dark boss-mb-1">
                          {__('Stratégie d\'analyse', 'boss-seo')}
                        </label>
                        <SelectControl
                          value={strategy}
                          options={[
                            { label: __('Mobile', 'boss-seo'), value: 'mobile' },
                            { label: __('Desktop', 'boss-seo'), value: 'desktop' }
                          ]}
                          onChange={setStrategy}
                        />
                      </div>

                      <div className="boss-flex boss-justify-end">
                        <Button
                          isPrimary
                          className="boss-flex boss-items-center"
                          onClick={handleAnalyzeUrl}
                          isBusy={isAnalyzing}
                          disabled={isAnalyzing}
                        >
                          <Dashicon icon="search" className="boss-mr-1" />
                          {isAnalyzing
                            ? __('Analyse en cours...', 'boss-seo')
                            : __('Analyser cette URL', 'boss-seo')
                          }
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                <Card>
                  <CardHeader className="boss-border-b boss-border-gray-200">
                    <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                      {__('Historique des analyses', 'boss-seo')}
                    </h3>
                  </CardHeader>
                  <CardBody>
                    {isLoading ? (
                      <div className="boss-p-8 boss-flex boss-justify-center boss-items-center">
                        <Spinner />
                      </div>
                    ) : urlAnalysisHistory && urlAnalysisHistory.length > 0 ? (
                      <div className="boss-divide-y boss-divide-gray-200">
                        {urlAnalysisHistory.map((analysis, index) => (
                          <div key={index} className="boss-py-3 boss-flex boss-justify-between boss-items-center">
                            <div>
                              <div className="boss-font-medium boss-text-boss-dark boss-truncate boss-max-w-md">
                                {analysis.url}
                              </div>
                              <div className="boss-text-sm boss-text-boss-gray boss-flex boss-items-center">
                                <Dashicon icon="calendar-alt" className="boss-mr-1 boss-text-xs" />
                                {formatDate(analysis.date)}
                                <span className="boss-mx-2">•</span>
                                <Dashicon icon={analysis.strategy === 'mobile' ? 'smartphone' : 'desktop'} className="boss-mr-1 boss-text-xs" />
                                {analysis.strategy === 'mobile' ? __('Mobile', 'boss-seo') : __('Desktop', 'boss-seo')}
                              </div>
                            </div>
                            <Button
                              isSecondary
                              isSmall
                              onClick={() => handleLoadAnalysis(analysis.id)}
                            >
                              {__('Voir les résultats', 'boss-seo')}
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="boss-p-6 boss-text-center boss-text-boss-gray">
                        <p>{__('Aucune analyse d\'URL n\'a été effectuée.', 'boss-seo')}</p>
                        <p className="boss-mt-2 boss-text-sm">
                          {__('Analysez une URL pour voir les résultats ici.', 'boss-seo')}
                        </p>
                      </div>
                    )}
                  </CardBody>
                </Card>
              </div>
            );
          } else if (tab.name === 'results') {
            // Onglet des résultats d'analyse
            return (
              <div>
                {urlAnalysisResults ? (
                  <div>
                    <Card className="boss-mb-6">
                      <CardHeader className="boss-border-b boss-border-gray-200">
                        <div className="boss-flex boss-justify-between boss-items-center">
                          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
                            {__('Résultats d\'analyse', 'boss-seo')}
                          </h3>
                          <div className="boss-text-sm boss-text-boss-gray boss-flex boss-items-center">
                            <Dashicon icon="calendar-alt" className="boss-mr-1" />
                            {formatDate(urlAnalysisResults.date)}
                            <span className="boss-mx-2">•</span>
                            <Dashicon icon={urlAnalysisResults.strategy === 'mobile' ? 'smartphone' : 'desktop'} className="boss-mr-1" />
                            {urlAnalysisResults.strategy === 'mobile' ? __('Mobile', 'boss-seo') : __('Desktop', 'boss-seo')}
                          </div>
                        </div>
                      </CardHeader>
                      <CardBody>
                        <div className="boss-mb-4">
                          <h4 className="boss-text-md boss-font-medium boss-text-boss-dark boss-mb-2">
                            {__('URL analysée', 'boss-seo')}
                          </h4>
                          <div className="boss-bg-gray-50 boss-p-3 boss-rounded boss-break-all">
                            <a href={urlAnalysisResults.url} target="_blank" rel="noopener noreferrer" className="boss-text-boss-primary boss-hover:boss-underline">
                              {urlAnalysisResults.url}
                            </a>
                          </div>
                        </div>

                        <div className="boss-mb-6">
                          <h4 className="boss-text-md boss-font-medium boss-text-boss-dark boss-mb-2">
                            {__('Scores par catégorie', 'boss-seo')}
                          </h4>
                          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-4 boss-gap-4">
                            {urlAnalysisResults.scores && Object.entries(urlAnalysisResults.scores).map(([category, data]) => {
                              // Déterminer la couleur en fonction du score
                              let colorClass = 'boss-text-red-600';
                              let bgColorClass = 'boss-bg-red-100';

                              if (data.score >= 90) {
                                colorClass = 'boss-text-green-600';
                                bgColorClass = 'boss-bg-green-100';
                              } else if (data.score >= 70) {
                                colorClass = 'boss-text-yellow-600';
                                bgColorClass = 'boss-bg-yellow-100';
                              } else if (data.score >= 50) {
                                colorClass = 'boss-text-orange-600';
                                bgColorClass = 'boss-bg-orange-100';
                              }

                              return (
                                <div key={category} className="boss-border boss-border-gray-200 boss-rounded-lg boss-p-4">
                                  <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
                                    <h5 className="boss-font-medium boss-text-boss-dark">{data.title}</h5>
                                    <div className={`boss-w-12 boss-h-12 boss-rounded-full ${bgColorClass} boss-flex boss-items-center boss-justify-center`}>
                                      <span className={`boss-text-xl boss-font-bold ${colorClass}`}>
                                        {data.score}
                                      </span>
                                    </div>
                                  </div>
                                  <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5 boss-mt-2">
                                    <div
                                      className={`boss-h-2.5 boss-rounded-full ${colorClass.replace('text', 'bg')}`}
                                      style={{ width: `${data.score}%` }}
                                    ></div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>

                        <div className="boss-mb-6">
                          <h4 className="boss-text-md boss-font-medium boss-text-boss-dark boss-mb-2">
                            {__('Core Web Vitals', 'boss-seo')}
                          </h4>
                          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 xl:boss-grid-cols-5 boss-gap-4">
                            {urlAnalysisResults.metrics && Object.entries(urlAnalysisResults.metrics).map(([metricId, metric]) => (
                              <Card key={metricId} className="boss-h-full">
                                <CardBody>
                                  <div className="boss-flex boss-flex-col boss-items-center boss-text-center">
                                    <div className={`boss-w-16 boss-h-16 boss-rounded-full boss-flex boss-items-center boss-justify-center boss-mb-3 ${getScoreBgClass(metric.score)}`}>
                                      <span className={`boss-text-2xl boss-font-bold ${getScoreColorClass(metric.score)}`}>
                                        {metric.value}{metric.unit}
                                      </span>
                                    </div>
                                    <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                                      {metric.full_name}
                                    </h4>
                                    <div className={`boss-flex boss-items-center boss-mt-2 boss-text-sm boss-font-medium ${getScoreColorClass(metric.score)}`}>
                                      <Dashicon icon={metric.score >= 90 ? 'yes-alt' : (metric.score >= 50 ? 'warning' : 'dismiss')} className="boss-mr-1" />
                                      <span>{getScoreLabel(metric.score)}</span>
                                    </div>
                                  </div>
                                </CardBody>
                              </Card>
                            ))}
                          </div>
                        </div>

                        <div className="boss-mb-6">
                          <h4 className="boss-text-md boss-font-medium boss-text-boss-dark boss-mb-2">
                            {__('Opportunités d\'amélioration', 'boss-seo')}
                          </h4>
                          {urlAnalysisResults.opportunities && Object.keys(urlAnalysisResults.opportunities).length > 0 ? (
                            <div className="boss-divide-y boss-divide-gray-200">
                              {Object.entries(urlAnalysisResults.opportunities).map(([opportunityId, opportunity], index) => (
                                <div key={opportunityId} className="boss-py-4">
                                  <div className="boss-flex boss-items-start">
                                    <div className={`boss-w-8 boss-h-8 boss-rounded-full boss-bg-yellow-100 boss-flex boss-items-center boss-justify-center boss-mr-3 boss-flex-shrink-0`}>
                                      <Dashicon icon="flag" className="boss-text-yellow-600" />
                                    </div>
                                    <div>
                                      <h5 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                                        {opportunity.title}
                                      </h5>
                                      <p className="boss-text-boss-gray boss-text-sm boss-mb-2">
                                        {opportunity.description}
                                      </p>
                                      {opportunity.display_value && (
                                        <div className="boss-text-sm boss-text-boss-gray boss-mb-2">
                                          {opportunity.display_value}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="boss-p-6 boss-text-center boss-text-boss-gray">
                              <p>{__('Aucune opportunité d\'amélioration identifiée.', 'boss-seo')}</p>
                            </div>
                          )}
                        </div>

                        <div className="boss-flex boss-justify-end">
                          <Button
                            isPrimary
                            onClick={() => setActiveTab('ai')}
                          >
                            <Dashicon icon="lightbulb" className="boss-mr-1" />
                            {__('Voir les suggestions d\'amélioration IA', 'boss-seo')}
                          </Button>
                        </div>
                      </CardBody>
                    </Card>
                  </div>
                ) : (
                  <div className="boss-p-8 boss-text-center boss-text-boss-gray boss-bg-white boss-rounded-lg boss-shadow-sm">
                    <Dashicon icon="chart-bar" className="boss-text-5xl boss-mb-4" />
                    <h3 className="boss-text-lg boss-font-medium boss-mb-2">
                      {__('Aucun résultat d\'analyse disponible', 'boss-seo')}
                    </h3>
                    <p className="boss-mb-4">
                      {__('Lancez une analyse d\'URL spécifique pour voir les résultats ici.', 'boss-seo')}
                    </p>
                    <Button
                      isPrimary
                      onClick={() => setActiveTab('specific')}
                    >
                      <Dashicon icon="search" className="boss-mr-1" />
                      {__('Analyser une URL', 'boss-seo')}
                    </Button>
                  </div>
                )}
              </div>
            );
          } else if (tab.name === 'ai') {
            // Onglet des suggestions IA
            return (
              <div>
                {Object.keys(aiSuggestions).length > 0 ? (
                  <AIRecommendationsSection
                    suggestions={aiSuggestions}
                    onImplement={handleImplementSuggestion}
                  />
                ) : (
                  <div className="boss-p-8 boss-text-center boss-text-boss-gray boss-bg-white boss-rounded-lg boss-shadow-sm">
                    <Dashicon icon="lightbulb" className="boss-text-5xl boss-mb-4" />
                    <h3 className="boss-text-lg boss-font-medium boss-mb-2">
                      {__('Aucune suggestion IA disponible', 'boss-seo')}
                    </h3>
                    <p className="boss-mb-4">
                      {__('Lancez une analyse d\'URL spécifique pour obtenir des suggestions d\'amélioration générées par l\'IA.', 'boss-seo')}
                    </p>
                    <Button
                      isPrimary
                      onClick={() => setActiveTab('specific')}
                    >
                      <Dashicon icon="search" className="boss-mr-1" />
                      {__('Analyser une URL', 'boss-seo')}
                    </Button>
                  </div>
                )}
              </div>
            );
          }
        }}
      </TabPanel>

      {/* Contenu de l'onglet général */}
      {activeTab === 'general' && (
        <div>
          {/* Core Web Vitals */}
          <div className="boss-mb-8">
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-4">
              {__('Core Web Vitals', 'boss-seo')}
            </h3>

            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 xl:boss-grid-cols-5 boss-gap-4">
              <WebVitalIndicator metric={coreWebVitals.lcp} />
              <WebVitalIndicator metric={coreWebVitals.fid} />
              <WebVitalIndicator metric={coreWebVitals.cls} />
              <WebVitalIndicator metric={coreWebVitals.ttfb} />
              <WebVitalIndicator metric={coreWebVitals.fcp} />
            </div>
          </div>

          {/* Évolution de la performance */}
          <div className="boss-mb-8">
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-4">
              {__('Évolution de la performance', 'boss-seo')}
            </h3>

            <Card>
              <CardBody>
                <SimpleChart data={history} color="boss-primary" />
              </CardBody>
              <CardFooter>
                <div className="boss-flex boss-justify-between boss-items-center boss-text-sm boss-text-boss-gray">
                  <span>{__('Évolution sur les 5 derniers mois', 'boss-seo')}</span>
                  <span>
                    {history[0].score > history[history.length - 1].score ? (
                      <span className="boss-text-green-600 boss-font-medium boss-flex boss-items-center">
                        <Dashicon icon="arrow-up-alt" className="boss-mr-1" />
                        +{history[0].score - history[history.length - 1].score} {__('points', 'boss-seo')}
                      </span>
                    ) : (
                      <span className="boss-text-red-600 boss-font-medium boss-flex boss-items-center">
                        <Dashicon icon="arrow-down-alt" className="boss-mr-1" />
                        {history[0].score - history[history.length - 1].score} {__('points', 'boss-seo')}
                      </span>
                    )}
                  </span>
                </div>
              </CardFooter>
            </Card>
          </div>

          {/* Recommandations d'amélioration */}
          <div className="boss-mb-8">
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-4">
              {__('Recommandations d\'amélioration', 'boss-seo')}
            </h3>

            {error && (
              <Notice status="error" isDismissible={true} onRemove={() => setError(null)} className="boss-mb-4">
                {error}
              </Notice>
            )}

            <Card>
              <CardBody className="boss-p-0">
                {isLoading ? (
                  <div className="boss-p-8 boss-flex boss-justify-center boss-items-center">
                    <Spinner />
                  </div>
                ) : recommendations && recommendations.length > 0 ? (
                  <div className="boss-divide-y boss-divide-gray-200">
                    {recommendations.map((recommendation, index) => {
                      // Déterminer l'icône et la couleur en fonction de la priorité
                      let iconName = 'flag';
                      let bgColorClass = 'boss-bg-yellow-100';
                      let textColorClass = 'boss-text-yellow-600';

                      if (recommendation.priority === 'high') {
                        iconName = 'warning';
                        bgColorClass = 'boss-bg-red-100';
                        textColorClass = 'boss-text-red-600';
                      } else if (recommendation.priority === 'low') {
                        iconName = 'info';
                        bgColorClass = 'boss-bg-blue-100';
                        textColorClass = 'boss-text-blue-600';
                      }

                      return (
                        <div key={index} className="boss-p-4 boss-flex boss-items-start">
                          <div className={`boss-w-8 boss-h-8 boss-rounded-full ${bgColorClass} boss-flex boss-items-center boss-justify-center boss-mr-3 boss-flex-shrink-0`}>
                            <Dashicon icon={iconName} className={textColorClass} />
                          </div>
                          <div>
                            <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                              {recommendation.title}
                            </h4>
                            <p className="boss-text-boss-gray boss-text-sm boss-mb-2">
                              {recommendation.description}
                            </p>
                            {recommendation.link && (
                              <Button
                                isSmall
                                isSecondary
                                href={recommendation.link}
                                target="_blank"
                              >
                                {__('Comment améliorer', 'boss-seo')}
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="boss-p-6 boss-text-center boss-text-boss-gray">
                    <p>{__('Aucune recommandation disponible pour le moment.', 'boss-seo')}</p>
                    <p className="boss-mt-2 boss-text-sm">
                      {__('Lancez une analyse de performance pour obtenir des recommandations personnalisées.', 'boss-seo')}
                    </p>
                  </div>
                )}
              </CardBody>
            </Card>
          </div>

          {/* Ressources utiles */}
          <Card className="boss-bg-gradient-to-r boss-from-indigo-50 boss-to-blue-50 boss-border-0">
            <CardBody className="boss-flex boss-flex-col md:boss-flex-row boss-items-center">
              <div className="boss-mr-6 boss-mb-4 md:boss-mb-0">
                <Dashicon icon="book-alt" className="boss-text-indigo-600 boss-text-5xl" />
              </div>
              <div className="boss-flex-1">
                <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {__('Ressources pour améliorer la performance', 'boss-seo')}
                </h3>
                <p className="boss-text-boss-gray boss-mb-4">
                  {__('Consultez nos guides et ressources pour améliorer la performance de votre site et optimiser les Core Web Vitals.', 'boss-seo')}
                </p>
                <div className="boss-flex boss-flex-wrap boss-gap-3">
                  <Button
                    isSecondary
                    href="#"
                    target="_blank"
                  >
                    {__('Guide complet des Core Web Vitals', 'boss-seo')}
                  </Button>
                  <Button
                    isSecondary
                    href="#"
                    target="_blank"
                  >
                    {__('Optimisation des images', 'boss-seo')}
                  </Button>
                  <Button
                    isSecondary
                    href="#"
                    target="_blank"
                  >
                    {__('Mise en cache et CDN', 'boss-seo')}
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  );
};

export default PerformanceSection;
