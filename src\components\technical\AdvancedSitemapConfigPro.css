/**
 * Styles pour le composant AdvancedSitemapConfigPro
 * Interface professionnelle pour la gestion des sitemaps
 */

.boss-advanced-sitemap-config-pro {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Dashboard de statut */
.boss-advanced-sitemap-config-pro .boss-dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.boss-advanced-sitemap-config-pro .boss-stat-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  border: 1px solid;
}

.boss-advanced-sitemap-config-pro .boss-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Cartes de types de contenu */
.boss-advanced-sitemap-config-pro .boss-content-type-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.boss-advanced-sitemap-config-pro .boss-content-type-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.boss-advanced-sitemap-config-pro .boss-content-type-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

/* Sitemaps spécialisés */
.boss-advanced-sitemap-config-pro .boss-specialized-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.boss-advanced-sitemap-config-pro .boss-specialized-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.boss-advanced-sitemap-config-pro .boss-specialized-card.boss-green {
  border-color: #10b981;
}

.boss-advanced-sitemap-config-pro .boss-specialized-card.boss-purple {
  border-color: #8b5cf6;
}

.boss-advanced-sitemap-config-pro .boss-specialized-card.boss-blue {
  border-color: #3b82f6;
}

.boss-advanced-sitemap-config-pro .boss-specialized-card.boss-orange {
  border-color: #f59e0b;
}

/* Tableau des URLs personnalisées */
.boss-advanced-sitemap-config-pro .boss-custom-urls-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.boss-advanced-sitemap-config-pro .boss-custom-urls-table th {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  font-weight: 600;
  color: #374151;
  padding: 12px 16px;
  border-bottom: 2px solid #e5e7eb;
}

.boss-advanced-sitemap-config-pro .boss-custom-urls-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.boss-advanced-sitemap-config-pro .boss-custom-urls-table tr:hover {
  background-color: #f9fafb;
}

/* Badges et indicateurs */
.boss-advanced-sitemap-config-pro .boss-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.boss-advanced-sitemap-config-pro .boss-status-badge.boss-success {
  background-color: #d1fae5;
  color: #065f46;
}

.boss-advanced-sitemap-config-pro .boss-status-badge.boss-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.boss-advanced-sitemap-config-pro .boss-status-badge.boss-error {
  background-color: #fee2e2;
  color: #991b1b;
}

.boss-advanced-sitemap-config-pro .boss-status-badge.boss-info {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Barres de progression */
.boss-advanced-sitemap-config-pro .boss-progress-bar {
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
  height: 8px;
}

.boss-advanced-sitemap-config-pro .boss-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 9999px;
  transition: width 0.5s ease;
}

/* Boutons personnalisés */
.boss-advanced-sitemap-config-pro .boss-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.boss-advanced-sitemap-config-pro .boss-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.boss-advanced-sitemap-config-pro .boss-btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.boss-advanced-sitemap-config-pro .boss-btn-success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Animations */
@keyframes boss-fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.boss-advanced-sitemap-config-pro .boss-fade-in {
  animation: boss-fadeIn 0.3s ease-out;
}

@keyframes boss-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.boss-advanced-sitemap-config-pro .boss-pulse {
  animation: boss-pulse 2s infinite;
}

/* Responsive */
@media (max-width: 768px) {
  .boss-advanced-sitemap-config-pro .boss-grid-responsive {
    grid-template-columns: 1fr;
  }
  
  .boss-advanced-sitemap-config-pro .boss-flex-responsive {
    flex-direction: column;
    gap: 12px;
  }
  
  .boss-advanced-sitemap-config-pro .boss-hide-mobile {
    display: none;
  }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
  .boss-advanced-sitemap-config-pro.boss-dark-mode {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .boss-advanced-sitemap-config-pro.boss-dark-mode .boss-card {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .boss-advanced-sitemap-config-pro.boss-dark-mode .boss-custom-urls-table th {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    color: #f9fafb;
  }
}

/* Tooltips */
.boss-advanced-sitemap-config-pro .boss-tooltip {
  position: relative;
  display: inline-block;
}

.boss-advanced-sitemap-config-pro .boss-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 5px;
}

.boss-advanced-sitemap-config-pro .boss-tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #1f2937;
  z-index: 1000;
}
