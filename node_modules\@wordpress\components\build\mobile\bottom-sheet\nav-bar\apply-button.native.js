"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _compose = require("@wordpress/compose");
var _styles = _interopRequireDefault(require("./styles.scss"));
var _actionButton = _interopRequireDefault(require("./action-button"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function ApplyButton({
  onPress
}) {
  const buttonTextStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default['button-text'], _styles.default['button-text-dark']);
  const applyButtonStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default['apply-button-icon'], _styles.default['apply-button-icon-dark']);
  return (0, _react.createElement)(_reactNative.View, {
    style: _styles.default['apply-button']
  }, (0, _react.createElement)(_actionButton.default, {
    onPress: onPress,
    accessibilityLabel: (0, _i18n.__)('Apply'),
    accessibilityHint: (0, _i18n.__)('Applies the setting')
  }, _reactNative.Platform.OS === 'ios' ? (0, _react.createElement)(_reactNative.Text, {
    style: buttonTextStyle,
    maxFontSizeMultiplier: 2
  }, (0, _i18n.__)('Apply')) : (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check,
    size: 24,
    style: applyButtonStyle
  })));
}
var _default = ApplyButton;
exports.default = _default;
//# sourceMappingURL=apply-button.native.js.map