"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _element = require("@wordpress/element");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const TooltipContext = (0, _element.createContext)();
function Tooltip({
  children,
  onPress,
  style,
  visible
}) {
  const panResponder = (0, _element.useRef)(_reactNative.PanResponder.create({
    /**
     * To allow dimissing the tooltip on press while also avoiding blocking
     * interactivity within the child context, we place this `onPress` side
     * effect within the `onStartShouldSetPanResponderCapture` callback.
     *
     * This is a bit unorthodox, but may be the simplest approach to achieving
     * this outcome. This is effectively a gesture responder that never
     * becomes the controlling responder. https://bit.ly/2J3ugKF
     */
    onStartShouldSetPanResponderCapture: () => {
      if (onPress) {
        onPress();
      }
      return false;
    }
  })).current;
  return (0, _react.createElement)(TooltipContext.Provider, {
    value: visible
  }, (0, _react.createElement)(_reactNative.View, {
    ...(visible ? panResponder.panHandlers : {}),
    style: style
  }, children));
}
function Label({
  align,
  text,
  xOffset,
  yOffset
}) {
  const animationValue = (0, _element.useRef)(new _reactNative.Animated.Value(0)).current;
  const [dimensions, setDimensions] = (0, _element.useState)(null);
  const visible = (0, _element.useContext)(TooltipContext);
  if (typeof visible === 'undefined') {
    throw new Error('Tooltip.Label cannot be rendered outside of the Tooltip component');
  }
  (0, _element.useEffect)(() => {
    const startAnimation = () => {
      _reactNative.Animated.timing(animationValue, {
        toValue: visible ? 1 : 0,
        duration: visible ? 300 : 150,
        useNativeDriver: true,
        delay: visible ? 500 : 0,
        easing: _reactNative.Easing.out(_reactNative.Easing.quad)
      }).start();
    };
    startAnimation();
  }, [animationValue, visible]);

  // Transforms rely upon onLayout to enable custom offsets additions.
  let tooltipTransforms;
  if (dimensions) {
    tooltipTransforms = [{
      translateX: (align === 'center' ? -dimensions.width / 2 : 0) + xOffset
    }, {
      translateY: -dimensions.height + yOffset
    }];
  }
  const tooltipStyles = [_style.default.tooltip, {
    shadowColor: _style.default.tooltipShadow?.color,
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 2,
    elevation: 2,
    transform: tooltipTransforms
  }, align === 'left' && _style.default.tooltipLeftAlign];
  const arrowStyles = [_style.default.arrow, align === 'left' && _style.default.arrowLeftAlign];
  return (0, _react.createElement)(_reactNative.Animated.View, {
    style: {
      opacity: animationValue,
      transform: [{
        translateY: animationValue.interpolate({
          inputRange: [0, 1],
          outputRange: [visible ? 4 : -8, -8]
        })
      }]
    }
  }, (0, _react.createElement)(_reactNative.View, {
    onLayout: ({
      nativeEvent
    }) => {
      const {
        height,
        width
      } = nativeEvent.layout;
      setDimensions({
        height,
        width
      });
    },
    style: tooltipStyles
  }, (0, _react.createElement)(_reactNative.Text, {
    style: _style.default.text
  }, text), (0, _react.createElement)(_reactNative.View, {
    style: arrowStyles
  })));
}
Label.defaultProps = {
  align: 'center',
  xOffset: 0,
  yOffset: 0
};
Tooltip.Label = Label;
var _default = Tooltip;
exports.default = _default;
//# sourceMappingURL=index.native.js.map