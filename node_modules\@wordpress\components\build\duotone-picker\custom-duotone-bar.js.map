{"version": 3, "names": ["_gradientBar", "_interopRequireDefault", "require", "_utils", "PLACEHOLDER_VALUES", "CustomDuotoneBar", "value", "onChange", "hasGradient", "values", "background", "getGradientFromCSSColors", "controlPoints", "getColorStopsFromColors", "_react", "createElement", "default", "disableInserter", "newColorStops", "newValue", "getColorsFromColorStops"], "sources": ["@wordpress/components/src/duotone-picker/custom-duotone-bar.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport CustomGradientBar from '../custom-gradient-picker/gradient-bar';\n\nimport {\n\tgetColorStopsFromColors,\n\tgetGradientFromCSSColors,\n\tgetColorsFromColorStops,\n} from './utils';\n\nconst PLACEHOLDER_VALUES = [ '#333', '#CCC' ];\n\nexport default function CustomDuotoneBar( {\n\tvalue,\n\tonChange,\n}: {\n\tvalue?: string[];\n\tonChange: ( value?: string[] ) => void;\n} ) {\n\tconst hasGradient = !! value;\n\tconst values = hasGradient ? value : PLACEHOLDER_VALUES;\n\tconst background = getGradientFromCSSColors( values );\n\tconst controlPoints = getColorStopsFromColors( values );\n\treturn (\n\t\t<CustomGradientBar\n\t\t\tdisableInserter\n\t\t\tbackground={ background }\n\t\t\thasGradient={ hasGradient }\n\t\t\tvalue={ controlPoints }\n\t\t\tonChange={ ( newColorStops ) => {\n\t\t\t\tconst newValue = getColorsFromColorStops( newColorStops );\n\t\t\t\tonChange( newValue );\n\t\t\t} }\n\t\t/>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AALA;AACA;AACA;;AASA,MAAME,kBAAkB,GAAG,CAAE,MAAM,EAAE,MAAM,CAAE;AAE9B,SAASC,gBAAgBA,CAAE;EACzCC,KAAK;EACLC;AAID,CAAC,EAAG;EACH,MAAMC,WAAW,GAAG,CAAC,CAAEF,KAAK;EAC5B,MAAMG,MAAM,GAAGD,WAAW,GAAGF,KAAK,GAAGF,kBAAkB;EACvD,MAAMM,UAAU,GAAG,IAAAC,+BAAwB,EAAEF,MAAO,CAAC;EACrD,MAAMG,aAAa,GAAG,IAAAC,8BAAuB,EAAEJ,MAAO,CAAC;EACvD,OACC,IAAAK,MAAA,CAAAC,aAAA,EAACf,YAAA,CAAAgB,OAAiB;IACjBC,eAAe;IACfP,UAAU,EAAGA,UAAY;IACzBF,WAAW,EAAGA,WAAa;IAC3BF,KAAK,EAAGM,aAAe;IACvBL,QAAQ,EAAKW,aAAa,IAAM;MAC/B,MAAMC,QAAQ,GAAG,IAAAC,8BAAuB,EAAEF,aAAc,CAAC;MACzDX,QAAQ,CAAEY,QAAS,CAAC;IACrB;EAAG,CACH,CAAC;AAEJ"}