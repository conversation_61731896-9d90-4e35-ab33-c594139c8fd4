"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "DatePicker", {
  enumerable: true,
  get: function () {
    return _date.default;
  }
});
exports.DateTimePicker = void 0;
Object.defineProperty(exports, "TimePicker", {
  enumerable: true,
  get: function () {
    return _time.default;
  }
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _date = _interopRequireDefault(require("../date"));
var _time = _interopRequireDefault(require("../time"));
var _styles = require("./styles");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const noop = () => {};
function UnforwardedDateTimePicker({
  currentDate,
  is12Hour,
  isInvalidDate,
  onMonthPreviewed = noop,
  onChange,
  events,
  startOfWeek
}, ref) {
  return (0, _react.createElement)(_styles.Wrapper, {
    ref: ref,
    className: "components-datetime",
    spacing: 4
  }, (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_time.default, {
    currentTime: currentDate,
    onChange: onChange,
    is12Hour: is12Hour
  }), (0, _react.createElement)(_date.default, {
    currentDate: currentDate,
    onChange: onChange,
    isInvalidDate: isInvalidDate,
    events: events,
    onMonthPreviewed: onMonthPreviewed,
    startOfWeek: startOfWeek
  })));
}

/**
 * DateTimePicker is a React component that renders a calendar and clock for
 * date and time selection. The calendar and clock components can be accessed
 * individually using the `DatePicker` and `TimePicker` components respectively.
 *
 * ```jsx
 * import { DateTimePicker } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyDateTimePicker = () => {
 *   const [ date, setDate ] = useState( new Date() );
 *
 *   return (
 *     <DateTimePicker
 *       currentDate={ date }
 *       onChange={ ( newDate ) => setDate( newDate ) }
 *       is12Hour
 *     />
 *   );
 * };
 * ```
 */
const DateTimePicker = (0, _element.forwardRef)(UnforwardedDateTimePicker);
exports.DateTimePicker = DateTimePicker;
var _default = DateTimePicker;
exports.default = _default;
//# sourceMappingURL=index.js.map