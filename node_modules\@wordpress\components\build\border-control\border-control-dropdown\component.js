"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _borderControlStylePicker = _interopRequireDefault(require("../border-control-style-picker"));
var _button = _interopRequireDefault(require("../../button"));
var _colorIndicator = _interopRequireDefault(require("../../color-indicator"));
var _colorPalette = _interopRequireDefault(require("../../color-palette"));
var _dropdown = _interopRequireDefault(require("../../dropdown"));
var _hStack = require("../../h-stack");
var _vStack = require("../../v-stack");
var _context = require("../../context");
var _hook = require("./hook");
var _baseControlStyles = require("../../base-control/styles/base-control-styles");
var _dropdownContentWrapper = _interopRequireDefault(require("../../dropdown/dropdown-content-wrapper"));
var _utils = require("../../color-palette/utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const getAriaLabelColorValue = colorValue => {
  // Leave hex values as-is. Remove the `var()` wrapper from CSS vars.
  return colorValue.replace(/^var\((.+)\)$/, '$1');
};
const getColorObject = (colorValue, colors) => {
  if (!colorValue || !colors) {
    return;
  }
  if ((0, _utils.isMultiplePaletteArray)(colors)) {
    // Multiple origins
    let matchedColor;
    colors.some(origin => origin.colors.some(color => {
      if (color.color === colorValue) {
        matchedColor = color;
        return true;
      }
      return false;
    }));
    return matchedColor;
  }

  // Single origin
  return colors.find(color => color.color === colorValue);
};
const getToggleAriaLabel = (colorValue, colorObject, style, isStyleEnabled) => {
  if (isStyleEnabled) {
    if (colorObject) {
      const ariaLabelValue = getAriaLabelColorValue(colorObject.color);
      return style ? (0, _i18n.sprintf)(
      // translators: %1$s: The name of the color e.g. "vivid red". %2$s: The color's hex code e.g.: "#f00:". %3$s: The current border style selection e.g. "solid".
      'Border color and style picker. The currently selected color is called "%1$s" and has a value of "%2$s". The currently selected style is "%3$s".', colorObject.name, ariaLabelValue, style) : (0, _i18n.sprintf)(
      // translators: %1$s: The name of the color e.g. "vivid red". %2$s: The color's hex code e.g.: "#f00:".
      'Border color and style picker. The currently selected color is called "%1$s" and has a value of "%2$s".', colorObject.name, ariaLabelValue);
    }
    if (colorValue) {
      const ariaLabelValue = getAriaLabelColorValue(colorValue);
      return style ? (0, _i18n.sprintf)(
      // translators: %1$s: The color's hex code e.g.: "#f00:". %2$s: The current border style selection e.g. "solid".
      'Border color and style picker. The currently selected color has a value of "%1$s". The currently selected style is "%2$s".', ariaLabelValue, style) : (0, _i18n.sprintf)(
      // translators: %1$s: The color's hex code e.g: "#f00".
      'Border color and style picker. The currently selected color has a value of "%1$s".', ariaLabelValue);
    }
    return (0, _i18n.__)('Border color and style picker.');
  }
  if (colorObject) {
    return (0, _i18n.sprintf)(
    // translators: %1$s: The name of the color e.g. "vivid red". %2$s: The color's hex code e.g: "#f00".
    'Border color picker. The currently selected color is called "%1$s" and has a value of "%2$s".', colorObject.name, getAriaLabelColorValue(colorObject.color));
  }
  if (colorValue) {
    return (0, _i18n.sprintf)(
    // translators: %1$s: The color's hex code e.g: "#f00".
    'Border color picker. The currently selected color has a value of "%1$s".', getAriaLabelColorValue(colorValue));
  }
  return (0, _i18n.__)('Border color picker.');
};
const BorderControlDropdown = (props, forwardedRef) => {
  const {
    __experimentalIsRenderedInSidebar,
    border,
    colors,
    disableCustomColors,
    enableAlpha,
    enableStyle,
    indicatorClassName,
    indicatorWrapperClassName,
    isStyleSettable,
    onReset,
    onColorChange,
    onStyleChange,
    popoverContentClassName,
    popoverControlsClassName,
    resetButtonClassName,
    showDropdownHeader,
    size,
    __unstablePopoverProps,
    ...otherProps
  } = (0, _hook.useBorderControlDropdown)(props);
  const {
    color,
    style
  } = border || {};
  const colorObject = getColorObject(color, colors);
  const toggleAriaLabel = getToggleAriaLabel(color, colorObject, style, enableStyle);
  const showResetButton = color || style && style !== 'none';
  const dropdownPosition = __experimentalIsRenderedInSidebar ? 'bottom left' : undefined;
  const renderToggle = ({
    onToggle
  }) => (0, _react.createElement)(_button.default, {
    onClick: onToggle,
    variant: "tertiary",
    "aria-label": toggleAriaLabel,
    tooltipPosition: dropdownPosition,
    label: (0, _i18n.__)('Border color and style picker'),
    showTooltip: true,
    __next40pxDefaultSize: size === '__unstable-large' ? true : false
  }, (0, _react.createElement)("span", {
    className: indicatorWrapperClassName
  }, (0, _react.createElement)(_colorIndicator.default, {
    className: indicatorClassName,
    colorValue: color
  })));
  const renderContent = ({
    onClose
  }) => (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_dropdownContentWrapper.default, {
    paddingSize: "medium"
  }, (0, _react.createElement)(_vStack.VStack, {
    className: popoverControlsClassName,
    spacing: 6
  }, showDropdownHeader ? (0, _react.createElement)(_hStack.HStack, null, (0, _react.createElement)(_baseControlStyles.StyledLabel, null, (0, _i18n.__)('Border color')), (0, _react.createElement)(_button.default, {
    size: "small",
    label: (0, _i18n.__)('Close border color'),
    icon: _icons.closeSmall,
    onClick: onClose
  })) : undefined, (0, _react.createElement)(_colorPalette.default, {
    className: popoverContentClassName,
    value: color,
    onChange: onColorChange,
    colors,
    disableCustomColors,
    __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
    clearable: false,
    enableAlpha: enableAlpha
  }), enableStyle && isStyleSettable && (0, _react.createElement)(_borderControlStylePicker.default, {
    label: (0, _i18n.__)('Style'),
    value: style,
    onChange: onStyleChange
  }))), showResetButton && (0, _react.createElement)(_dropdownContentWrapper.default, {
    paddingSize: "none"
  }, (0, _react.createElement)(_button.default, {
    className: resetButtonClassName,
    variant: "tertiary",
    onClick: () => {
      onReset();
      onClose();
    }
  }, (0, _i18n.__)('Reset'))));
  return (0, _react.createElement)(_dropdown.default, {
    renderToggle: renderToggle,
    renderContent: renderContent,
    popoverProps: {
      ...__unstablePopoverProps
    },
    ...otherProps,
    ref: forwardedRef
  });
};
const ConnectedBorderControlDropdown = (0, _context.contextConnect)(BorderControlDropdown, 'BorderControlDropdown');
var _default = ConnectedBorderControlDropdown;
exports.default = _default;
//# sourceMappingURL=component.js.map