{"version": 3, "names": ["_colorCell", "_interopRequireDefault", "require", "ColorControl", "label", "onPress", "color", "withColorIndicator", "props", "_react", "createElement", "default", "_default", "exports"], "sources": ["@wordpress/components/src/color-control/index.native.js"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport ColorCell from '../mobile/bottom-sheet/color-cell';\n\nfunction ColorControl( {\n\tlabel,\n\tonPress,\n\tcolor,\n\twithColorIndicator,\n\t...props\n} ) {\n\treturn (\n\t\t<ColorCell\n\t\t\tlabel={ label }\n\t\t\tonPress={ onPress }\n\t\t\tcolor={ color }\n\t\t\twithColorIndicator={ withColorIndicator }\n\t\t\t{ ...props }\n\t\t/>\n\t);\n}\n\nexport default ColorControl;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAHA;AACA;AACA;;AAGA,SAASC,YAAYA,CAAE;EACtBC,KAAK;EACLC,OAAO;EACPC,KAAK;EACLC,kBAAkB;EAClB,GAAGC;AACJ,CAAC,EAAG;EACH,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACV,UAAA,CAAAW,OAAS;IACTP,KAAK,EAAGA,KAAO;IACfC,OAAO,EAAGA,OAAS;IACnBC,KAAK,EAAGA,KAAO;IACfC,kBAAkB,EAAGA,kBAAoB;IAAA,GACpCC;EAAK,CACV,CAAC;AAEJ;AAAC,IAAAI,QAAA,GAEcT,YAAY;AAAAU,OAAA,CAAAF,OAAA,GAAAC,QAAA"}