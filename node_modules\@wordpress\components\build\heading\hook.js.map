{"version": 3, "names": ["_context", "require", "_text", "_fontSize", "_utils", "useHeading", "props", "as", "asProp", "level", "color", "COLORS", "gray", "isBlock", "weight", "CONFIG", "fontWeightHeading", "otherProps", "useContextSystem", "a11yProps", "role", "parseInt", "textProps", "useText", "size", "getHeadingFontSize"], "sources": ["@wordpress/components/src/heading/hook.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { useContextSystem } from '../context';\nimport { useText } from '../text';\nimport { getHeadingFontSize } from '../utils/font-size';\nimport { CONFIG, COLORS } from '../utils';\nimport type { HeadingProps } from './types';\n\nexport function useHeading(\n\tprops: WordPressComponentProps< HeadingProps, 'h1' >\n) {\n\tconst {\n\t\tas: asProp,\n\t\tlevel = 2,\n\t\tcolor = COLORS.gray[ 900 ],\n\t\tisBlock = true,\n\t\tweight = CONFIG.fontWeightHeading as import('react').CSSProperties[ 'fontWeight' ],\n\t\t...otherProps\n\t} = useContextSystem( props, 'Heading' );\n\n\tconst as = ( asProp || `h${ level }` ) as keyof JSX.IntrinsicElements;\n\n\tconst a11yProps: {\n\t\trole?: string;\n\t\t'aria-level'?: number;\n\t} = {};\n\tif ( typeof as === 'string' && as[ 0 ] !== 'h' ) {\n\t\t// If not a semantic `h` element, add a11y props:\n\t\ta11yProps.role = 'heading';\n\t\ta11yProps[ 'aria-level' ] =\n\t\t\ttypeof level === 'string' ? parseInt( level ) : level;\n\t}\n\n\tconst textProps = useText( {\n\t\tcolor,\n\t\tisBlock,\n\t\tweight,\n\t\tsize: getHeadingFontSize( level ),\n\t\t...otherProps,\n\t} );\n\n\treturn { ...textProps, ...a11yProps, as };\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAPA;AACA;AACA;;AAQO,SAASI,UAAUA,CACzBC,KAAoD,EACnD;EACD,MAAM;IACLC,EAAE,EAAEC,MAAM;IACVC,KAAK,GAAG,CAAC;IACTC,KAAK,GAAGC,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE;IAC1BC,OAAO,GAAG,IAAI;IACdC,MAAM,GAAGC,aAAM,CAACC,iBAAkE;IAClF,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEZ,KAAK,EAAE,SAAU,CAAC;EAExC,MAAMC,EAAE,GAAKC,MAAM,IAAK,IAAIC,KAAO,EAAkC;EAErE,MAAMU,SAGL,GAAG,CAAC,CAAC;EACN,IAAK,OAAOZ,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAAE,CAAC,CAAE,KAAK,GAAG,EAAG;IAChD;IACAY,SAAS,CAACC,IAAI,GAAG,SAAS;IAC1BD,SAAS,CAAE,YAAY,CAAE,GACxB,OAAOV,KAAK,KAAK,QAAQ,GAAGY,QAAQ,CAAEZ,KAAM,CAAC,GAAGA,KAAK;EACvD;EAEA,MAAMa,SAAS,GAAG,IAAAC,aAAO,EAAE;IAC1Bb,KAAK;IACLG,OAAO;IACPC,MAAM;IACNU,IAAI,EAAE,IAAAC,4BAAkB,EAAEhB,KAAM,CAAC;IACjC,GAAGQ;EACJ,CAAE,CAAC;EAEH,OAAO;IAAE,GAAGK,SAAS;IAAE,GAAGH,SAAS;IAAEZ;EAAG,CAAC;AAC1C"}