"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BottomSheetColorCell;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _components = require("@wordpress/components");
var _cell = _interopRequireDefault(require("./cell"));
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function BottomSheetColorCell(props) {
  const {
    color,
    withColorIndicator = true,
    disabled,
    ...cellProps
  } = props;
  return (0, _react.createElement)(_cell.default, {
    ...cellProps,
    accessibilityRole: 'button',
    accessibilityHint: /* translators: accessibility text (hint for moving to color settings) */
    (0, _i18n.__)('Double tap to go to color settings'),
    editable: false,
    disabled: disabled,
    value: withColorIndicator && !color && (0, _i18n.__)('Default')
  }, withColorIndicator && color && (0, _react.createElement)(_components.ColorIndicator, {
    color: color,
    style: _styles.default.colorCircle
  }), disabled ? null : (0, _react.createElement)(_icons.Icon, {
    icon: _icons.chevronRight
  }));
}
//# sourceMappingURL=color-cell.native.js.map