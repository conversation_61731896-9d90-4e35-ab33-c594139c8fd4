{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_shortcut", "_button", "_icon", "UnforwardedMenuItem", "props", "ref", "children", "info", "className", "icon", "iconPosition", "shortcut", "isSelected", "role", "suffix", "buttonProps", "classnames", "_react", "createElement", "cloneElement", "default", "undefined", "MenuItem", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/menu-item/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { cloneElement, forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport Shortcut from '../shortcut';\nimport Button from '../button';\nimport Icon from '../icon';\nimport type { WordPressComponentProps } from '../context';\nimport type { MenuItemProps } from './types';\n\nfunction UnforwardedMenuItem(\n\tprops: WordPressComponentProps< MenuItemProps, 'button', false >,\n\tref: ForwardedRef< HTMLButtonElement >\n) {\n\tlet {\n\t\tchildren,\n\t\tinfo,\n\t\tclassName,\n\t\ticon,\n\t\ticonPosition = 'right',\n\t\tshortcut,\n\t\tisSelected,\n\t\trole = 'menuitem',\n\t\tsuffix,\n\t\t...buttonProps\n\t} = props;\n\n\tclassName = classnames( 'components-menu-item__button', className );\n\n\tif ( info ) {\n\t\tchildren = (\n\t\t\t<span className=\"components-menu-item__info-wrapper\">\n\t\t\t\t<span className=\"components-menu-item__item\">{ children }</span>\n\t\t\t\t<span className=\"components-menu-item__info\">{ info }</span>\n\t\t\t</span>\n\t\t);\n\t}\n\n\tif ( icon && typeof icon !== 'string' ) {\n\t\ticon = cloneElement( icon, {\n\t\t\tclassName: classnames( 'components-menu-items__item-icon', {\n\t\t\t\t'has-icon-right': iconPosition === 'right',\n\t\t\t} ),\n\t\t} );\n\t}\n\n\treturn (\n\t\t<Button\n\t\t\tref={ ref }\n\t\t\t// Make sure aria-checked matches spec https://www.w3.org/TR/wai-aria-1.1/#aria-checked\n\t\t\taria-checked={\n\t\t\t\trole === 'menuitemcheckbox' || role === 'menuitemradio'\n\t\t\t\t\t? isSelected\n\t\t\t\t\t: undefined\n\t\t\t}\n\t\t\trole={ role }\n\t\t\ticon={ iconPosition === 'left' ? icon : undefined }\n\t\t\tclassName={ className }\n\t\t\t{ ...buttonProps }\n\t\t>\n\t\t\t<span className=\"components-menu-item__item\">{ children }</span>\n\t\t\t{ ! suffix && (\n\t\t\t\t<Shortcut\n\t\t\t\t\tclassName=\"components-menu-item__shortcut\"\n\t\t\t\t\tshortcut={ shortcut }\n\t\t\t\t/>\n\t\t\t) }\n\t\t\t{ ! suffix && icon && iconPosition === 'right' && (\n\t\t\t\t<Icon icon={ icon } />\n\t\t\t) }\n\t\t\t{ suffix }\n\t\t</Button>\n\t);\n}\n\n/**\n * MenuItem is a component which renders a button intended to be used in combination with the `DropdownMenu` component.\n *\n * ```jsx\n * import { MenuItem } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyMenuItem = () => {\n * \tconst [ isActive, setIsActive ] = useState( true );\n *\n * \treturn (\n * \t\t<MenuItem\n * \t\t\ticon={ isActive ? 'yes' : 'no' }\n * \t\t\tisSelected={ isActive }\n * \t\t\trole=\"menuitemcheckbox\"\n * \t\t\tonClick={ () => setIsActive( ( state ) => ! state ) }\n * \t\t>\n * \t\t\tToggle\n * \t\t</MenuItem>\n * \t);\n * };\n * ```\n */\nexport const MenuItem = forwardRef( UnforwardedMenuItem );\n\nexport default MenuItem;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAL,sBAAA,CAAAC,OAAA;AAhBA;AACA;AACA;;AAIA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASK,mBAAmBA,CAC3BC,KAAgE,EAChEC,GAAsC,EACrC;EACD,IAAI;IACHC,QAAQ;IACRC,IAAI;IACJC,SAAS;IACTC,IAAI;IACJC,YAAY,GAAG,OAAO;IACtBC,QAAQ;IACRC,UAAU;IACVC,IAAI,GAAG,UAAU;IACjBC,MAAM;IACN,GAAGC;EACJ,CAAC,GAAGX,KAAK;EAETI,SAAS,GAAG,IAAAQ,mBAAU,EAAE,8BAA8B,EAAER,SAAU,CAAC;EAEnE,IAAKD,IAAI,EAAG;IACXD,QAAQ,GACP,IAAAW,MAAA,CAAAC,aAAA;MAAMV,SAAS,EAAC;IAAoC,GACnD,IAAAS,MAAA,CAAAC,aAAA;MAAMV,SAAS,EAAC;IAA4B,GAAGF,QAAgB,CAAC,EAChE,IAAAW,MAAA,CAAAC,aAAA;MAAMV,SAAS,EAAC;IAA4B,GAAGD,IAAY,CACtD,CACN;EACF;EAEA,IAAKE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAG;IACvCA,IAAI,GAAG,IAAAU,qBAAY,EAAEV,IAAI,EAAE;MAC1BD,SAAS,EAAE,IAAAQ,mBAAU,EAAE,kCAAkC,EAAE;QAC1D,gBAAgB,EAAEN,YAAY,KAAK;MACpC,CAAE;IACH,CAAE,CAAC;EACJ;EAEA,OACC,IAAAO,MAAA,CAAAC,aAAA,EAACjB,OAAA,CAAAmB,OAAM;IACNf,GAAG,EAAGA;IACN;IAAA;IACA,gBACCQ,IAAI,KAAK,kBAAkB,IAAIA,IAAI,KAAK,eAAe,GACpDD,UAAU,GACVS,SACH;IACDR,IAAI,EAAGA,IAAM;IACbJ,IAAI,EAAGC,YAAY,KAAK,MAAM,GAAGD,IAAI,GAAGY,SAAW;IACnDb,SAAS,EAAGA,SAAW;IAAA,GAClBO;EAAW,GAEhB,IAAAE,MAAA,CAAAC,aAAA;IAAMV,SAAS,EAAC;EAA4B,GAAGF,QAAgB,CAAC,EAC9D,CAAEQ,MAAM,IACT,IAAAG,MAAA,CAAAC,aAAA,EAAClB,SAAA,CAAAoB,OAAQ;IACRZ,SAAS,EAAC,gCAAgC;IAC1CG,QAAQ,EAAGA;EAAU,CACrB,CACD,EACC,CAAEG,MAAM,IAAIL,IAAI,IAAIC,YAAY,KAAK,OAAO,IAC7C,IAAAO,MAAA,CAAAC,aAAA,EAAChB,KAAA,CAAAkB,OAAI;IAACX,IAAI,EAAGA;EAAM,CAAE,CACrB,EACCK,MACK,CAAC;AAEX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMQ,QAAQ,GAAG,IAAAC,mBAAU,EAAEpB,mBAAoB,CAAC;AAACqB,OAAA,CAAAF,QAAA,GAAAA,QAAA;AAAA,IAAAG,QAAA,GAE3CH,QAAQ;AAAAE,OAAA,CAAAJ,OAAA,GAAAK,QAAA"}