{"version": 3, "names": ["_compose", "require", "_a11y", "_default", "createHigherOrderComponent", "Component", "props", "_react", "createElement", "speak", "debouncedSpeak", "useDebounce", "exports", "default"], "sources": ["@wordpress/components/src/higher-order/with-spoken-messages/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { createHigherOrderComponent, useDebounce } from '@wordpress/compose';\nimport { speak } from '@wordpress/a11y';\n\n/** @typedef {import('react').ComponentType} ComponentType */\n\n/**\n * A Higher Order Component used to be provide speak and debounced speak\n * functions.\n *\n * @see https://developer.wordpress.org/block-editor/packages/packages-a11y/#speak\n *\n * @param {ComponentType} Component The component to be wrapped.\n *\n * @return {ComponentType} The wrapped component.\n */\nexport default createHigherOrderComponent(\n\t( Component ) => ( props ) => (\n\t\t<Component\n\t\t\t{ ...props }\n\t\t\tspeak={ speak }\n\t\t\tdebouncedSpeak={ useDebounce( speak, 500 ) }\n\t\t/>\n\t),\n\t'withSpokenMessages'\n);\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAJA;AACA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,IAAAE,QAAA,GAUe,IAAAC,mCAA0B,EACtCC,SAAS,IAAQC,KAAK,IACvB,IAAAC,MAAA,CAAAC,aAAA,EAACH,SAAS;EAAA,GACJC,KAAK;EACVG,KAAK,EAAGA,WAAO;EACfC,cAAc,EAAG,IAAAC,oBAAW,EAAEF,WAAK,EAAE,GAAI;AAAG,CAC5C,CACD,EACD,oBACD,CAAC;AAAAG,OAAA,CAAAC,OAAA,GAAAV,QAAA"}