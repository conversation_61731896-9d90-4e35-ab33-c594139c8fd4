"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _es = _interopRequireDefault(require("fast-deep-equal/es6"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
/**
 * External dependencies
 */
/**
 * WordPress dependencies
 */
var _default = mapNodeToProps => (0, _compose.createHigherOrderComponent)(WrappedComponent => {
  return class extends _element.Component {
    constructor(props) {
      super(props);
      this.nodeRef = this.props.node;
      this.state = {
        fallbackStyles: undefined,
        grabStylesCompleted: false
      };
      this.bindRef = this.bindRef.bind(this);
    }
    bindRef(node) {
      if (!node) {
        return;
      }
      this.nodeRef = node;
    }
    componentDidMount() {
      this.grabFallbackStyles();
    }
    componentDidUpdate() {
      this.grabFallbackStyles();
    }
    grabFallbackStyles() {
      const {
        grabStylesCompleted,
        fallbackStyles
      } = this.state;
      if (this.nodeRef && !grabStylesCompleted) {
        const newFallbackStyles = mapNodeToProps(this.nodeRef, this.props);
        if (!(0, _es.default)(newFallbackStyles, fallbackStyles)) {
          this.setState({
            fallbackStyles: newFallbackStyles,
            grabStylesCompleted: Object.values(newFallbackStyles).every(Boolean)
          });
        }
      }
    }
    render() {
      const wrappedComponent = (0, _react.createElement)(WrappedComponent, {
        ...this.props,
        ...this.state.fallbackStyles
      });
      return this.props.node ? wrappedComponent : (0, _react.createElement)("div", {
        ref: this.bindRef
      }, " ", wrappedComponent, " ");
    }
  };
}, 'withFallbackStyles');
exports.default = _default;
//# sourceMappingURL=index.js.map