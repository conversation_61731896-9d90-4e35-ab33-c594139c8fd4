{"version": 3, "names": ["_element", "require", "_hooks", "_compose", "ANIMATION_FRAME_PERIOD", "withFilters", "<PERSON><PERSON><PERSON>", "createHigherOrderComponent", "OriginalComponent", "namespace", "FilteredComponent", "ensureFilteredComponent", "undefined", "applyFilters", "FilteredComponent<PERSON>enderer", "Component", "constructor", "props", "componentDidMount", "instances", "push", "length", "addAction", "onHooksUpdated", "componentWillUnmount", "filter", "instance", "removeAction", "render", "_react", "createElement", "throttledForceUpdate", "debounce", "for<PERSON>ach", "forceUpdate", "updatedHookName"], "sources": ["@wordpress/components/src/higher-order/with-filters/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { Component } from '@wordpress/element';\nimport { addAction, applyFilters, removeAction } from '@wordpress/hooks';\nimport { createHigherOrderComponent, debounce } from '@wordpress/compose';\n\nconst ANIMATION_FRAME_PERIOD = 16;\n\n/**\n * Creates a higher-order component which adds filtering capability to the\n * wrapped component. Filters get applied when the original component is about\n * to be mounted. When a filter is added or removed that matches the hook name,\n * the wrapped component re-renders.\n *\n * @param hookName Hook name exposed to be used by filters.\n *\n * @return Higher-order component factory.\n *\n * ```jsx\n * import { withFilters } from '@wordpress/components';\n * import { addFilter } from '@wordpress/hooks';\n *\n * const MyComponent = ( { title } ) => <h1>{ title }</h1>;\n *\n * const ComponentToAppend = () => <div>Appended component</div>;\n *\n * function withComponentAppended( FilteredComponent ) {\n * \treturn ( props ) => (\n * \t\t<>\n * \t\t\t<FilteredComponent { ...props } />\n * \t\t\t<ComponentToAppend />\n * \t\t</>\n * \t);\n * }\n *\n * addFilter(\n * \t'MyHookName',\n * \t'my-plugin/with-component-appended',\n * \twithComponentAppended\n * );\n *\n * const MyComponentWithFilters = withFilters( 'MyHookName' )( MyComponent );\n * ```\n */\nexport default function withFilters( hookName: string ) {\n\treturn createHigherOrderComponent( ( OriginalComponent ) => {\n\t\tconst namespace = 'core/with-filters/' + hookName;\n\n\t\t/**\n\t\t * The component definition with current filters applied. Each instance\n\t\t * reuse this shared reference as an optimization to avoid excessive\n\t\t * calls to `applyFilters` when many instances exist.\n\t\t */\n\t\tlet FilteredComponent: React.ComponentType;\n\n\t\t/**\n\t\t * Initializes the FilteredComponent variable once, if not already\n\t\t * assigned. Subsequent calls are effectively a noop.\n\t\t */\n\t\tfunction ensureFilteredComponent() {\n\t\t\tif ( FilteredComponent === undefined ) {\n\t\t\t\tFilteredComponent = applyFilters(\n\t\t\t\t\thookName,\n\t\t\t\t\tOriginalComponent\n\t\t\t\t) as React.ComponentType;\n\t\t\t}\n\t\t}\n\n\t\tclass FilteredComponentRenderer extends Component {\n\t\t\tstatic instances: FilteredComponentRenderer[];\n\n\t\t\tconstructor( props: { [ key: string ]: any } ) {\n\t\t\t\tsuper( props );\n\n\t\t\t\tensureFilteredComponent();\n\t\t\t}\n\n\t\t\tcomponentDidMount() {\n\t\t\t\tFilteredComponentRenderer.instances.push( this );\n\n\t\t\t\t// If there were previously no mounted instances for components\n\t\t\t\t// filtered on this hook, add the hook handler.\n\t\t\t\tif ( FilteredComponentRenderer.instances.length === 1 ) {\n\t\t\t\t\taddAction( 'hookRemoved', namespace, onHooksUpdated );\n\t\t\t\t\taddAction( 'hookAdded', namespace, onHooksUpdated );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcomponentWillUnmount() {\n\t\t\t\tFilteredComponentRenderer.instances =\n\t\t\t\t\tFilteredComponentRenderer.instances.filter(\n\t\t\t\t\t\t( instance ) => instance !== this\n\t\t\t\t\t);\n\n\t\t\t\t// If this was the last of the mounted components filtered on\n\t\t\t\t// this hook, remove the hook handler.\n\t\t\t\tif ( FilteredComponentRenderer.instances.length === 0 ) {\n\t\t\t\t\tremoveAction( 'hookRemoved', namespace );\n\t\t\t\t\tremoveAction( 'hookAdded', namespace );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\trender() {\n\t\t\t\treturn <FilteredComponent { ...this.props } />;\n\t\t\t}\n\t\t}\n\n\t\tFilteredComponentRenderer.instances = [];\n\n\t\t/**\n\t\t * Updates the FilteredComponent definition, forcing a render for each\n\t\t * mounted instance. This occurs a maximum of once per animation frame.\n\t\t */\n\t\tconst throttledForceUpdate = debounce( () => {\n\t\t\t// Recreate the filtered component, only after delay so that it's\n\t\t\t// computed once, even if many filters added.\n\t\t\tFilteredComponent = applyFilters(\n\t\t\t\thookName,\n\t\t\t\tOriginalComponent\n\t\t\t) as React.ComponentType;\n\n\t\t\t// Force each instance to render.\n\t\t\tFilteredComponentRenderer.instances.forEach( ( instance ) => {\n\t\t\t\tinstance.forceUpdate();\n\t\t\t} );\n\t\t}, ANIMATION_FRAME_PERIOD );\n\n\t\t/**\n\t\t * When a filter is added or removed for the matching hook name, each\n\t\t * mounted instance should re-render with the new filters having been\n\t\t * applied to the original component.\n\t\t *\n\t\t * @param updatedHookName Name of the hook that was updated.\n\t\t */\n\t\tfunction onHooksUpdated( updatedHookName: string ) {\n\t\t\tif ( updatedHookName === hookName ) {\n\t\t\t\tthrottledForceUpdate();\n\t\t\t}\n\t\t}\n\n\t\treturn FilteredComponentRenderer;\n\t}, 'withFilters' );\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AALA;AACA;AACA;;AAKA,MAAMG,sBAAsB,GAAG,EAAE;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASC,WAAWA,CAAEC,QAAgB,EAAG;EACvD,OAAO,IAAAC,mCAA0B,EAAIC,iBAAiB,IAAM;IAC3D,MAAMC,SAAS,GAAG,oBAAoB,GAAGH,QAAQ;;IAEjD;AACF;AACA;AACA;AACA;IACE,IAAII,iBAAsC;;IAE1C;AACF;AACA;AACA;IACE,SAASC,uBAAuBA,CAAA,EAAG;MAClC,IAAKD,iBAAiB,KAAKE,SAAS,EAAG;QACtCF,iBAAiB,GAAG,IAAAG,mBAAY,EAC/BP,QAAQ,EACRE,iBACD,CAAwB;MACzB;IACD;IAEA,MAAMM,yBAAyB,SAASC,kBAAS,CAAC;MAGjDC,WAAWA,CAAEC,KAA+B,EAAG;QAC9C,KAAK,CAAEA,KAAM,CAAC;QAEdN,uBAAuB,CAAC,CAAC;MAC1B;MAEAO,iBAAiBA,CAAA,EAAG;QACnBJ,yBAAyB,CAACK,SAAS,CAACC,IAAI,CAAE,IAAK,CAAC;;QAEhD;QACA;QACA,IAAKN,yBAAyB,CAACK,SAAS,CAACE,MAAM,KAAK,CAAC,EAAG;UACvD,IAAAC,gBAAS,EAAE,aAAa,EAAEb,SAAS,EAAEc,cAAe,CAAC;UACrD,IAAAD,gBAAS,EAAE,WAAW,EAAEb,SAAS,EAAEc,cAAe,CAAC;QACpD;MACD;MAEAC,oBAAoBA,CAAA,EAAG;QACtBV,yBAAyB,CAACK,SAAS,GAClCL,yBAAyB,CAACK,SAAS,CAACM,MAAM,CACvCC,QAAQ,IAAMA,QAAQ,KAAK,IAC9B,CAAC;;QAEF;QACA;QACA,IAAKZ,yBAAyB,CAACK,SAAS,CAACE,MAAM,KAAK,CAAC,EAAG;UACvD,IAAAM,mBAAY,EAAE,aAAa,EAAElB,SAAU,CAAC;UACxC,IAAAkB,mBAAY,EAAE,WAAW,EAAElB,SAAU,CAAC;QACvC;MACD;MAEAmB,MAAMA,CAAA,EAAG;QACR,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACpB,iBAAiB;UAAA,GAAM,IAAI,CAACO;QAAK,CAAI,CAAC;MAC/C;IACD;IAEAH,yBAAyB,CAACK,SAAS,GAAG,EAAE;;IAExC;AACF;AACA;AACA;IACE,MAAMY,oBAAoB,GAAG,IAAAC,iBAAQ,EAAE,MAAM;MAC5C;MACA;MACAtB,iBAAiB,GAAG,IAAAG,mBAAY,EAC/BP,QAAQ,EACRE,iBACD,CAAwB;;MAExB;MACAM,yBAAyB,CAACK,SAAS,CAACc,OAAO,CAAIP,QAAQ,IAAM;QAC5DA,QAAQ,CAACQ,WAAW,CAAC,CAAC;MACvB,CAAE,CAAC;IACJ,CAAC,EAAE9B,sBAAuB,CAAC;;IAE3B;AACF;AACA;AACA;AACA;AACA;AACA;IACE,SAASmB,cAAcA,CAAEY,eAAuB,EAAG;MAClD,IAAKA,eAAe,KAAK7B,QAAQ,EAAG;QACnCyB,oBAAoB,CAAC,CAAC;MACvB;IACD;IAEA,OAAOjB,yBAAyB;EACjC,CAAC,EAAE,aAAc,CAAC;AACnB"}