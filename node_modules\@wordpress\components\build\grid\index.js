"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Grid", {
  enumerable: true,
  get: function () {
    return _component.default;
  }
});
Object.defineProperty(exports, "useGrid", {
  enumerable: true,
  get: function () {
    return _hook.default;
  }
});
var _component = _interopRequireDefault(require("./component"));
var _hook = _interopRequireDefault(require("./hook"));
//# sourceMappingURL=index.js.map