{"version": 3, "names": ["_contextSystemProvider", "require", "_contextConnect", "_useContextSystem", "_wordpressComponent", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get"], "sources": ["@wordpress/components/src/context/index.ts"], "sourcesContent": ["export {\n\tContextSystemProvider,\n\tuseComponentsContext,\n} from './context-system-provider';\nexport {\n\tcontextConnect,\n\tcontextConnectWithoutRef,\n\thasConnectNamespace,\n\tgetConnectNamespace,\n} from './context-connect';\nexport { useContextSystem } from './use-context-system';\nexport * from './wordpress-component';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAIA,IAAAC,eAAA,GAAAD,OAAA;AAMA,IAAAE,iBAAA,GAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AAAAI,MAAA,CAAAC,IAAA,CAAAF,mBAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,mBAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,mBAAA,CAAAI,GAAA;IAAA;EAAA;AAAA"}