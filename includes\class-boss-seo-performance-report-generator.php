<?php

/**
 * Générateur de rapport de performance SEO Boss SEO.
 * 
 * Cette classe génère des rapports de performance détaillés
 * avec des données réelles et des visualisations.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Générateur de rapport de performance.
 *
 * Génère des rapports de performance SEO complets avec :
 * - Statistiques globales du site
 * - Analyse des pages les plus/moins performantes
 * - Évolution des scores dans le temps
 * - Recommandations d'amélioration
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Performance_Report_Generator {

    /**
     * Instance du collecteur de données.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Reports_Data_Collector    $data_collector    Collecteur de données.
     */
    private $data_collector;

    /**
     * Données du rapport.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $report_data    Données du rapport.
     */
    private $report_data;

    /**
     * Configuration du rapport.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $config    Configuration.
     */
    private $config;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    Boss_SEO_Reports_Data_Collector    $data_collector    Collecteur de données.
     */
    public function __construct( $data_collector ) {
        $this->data_collector = $data_collector;
        $this->config = array(
            'include_charts' => true,
            'include_recommendations' => true,
            'max_pages_analysis' => 50,
            'chart_colors' => array(
                'primary' => '#0073aa',
                'success' => '#46b450',
                'warning' => '#ffb900',
                'danger' => '#dc3232'
            )
        );
    }

    /**
     * Génère un rapport de performance complet.
     *
     * @since    1.2.0
     * @param    array    $options    Options du rapport.
     * @return   array                Données du rapport généré.
     */
    public function generate( $options = array() ) {
        // Fusionner les options avec la configuration par défaut
        $this->config = array_merge( $this->config, $options );
        
        // Collecter les données de performance
        $performance_data = $this->data_collector->get_performance_data();
        
        if ( isset( $performance_data['error'] ) ) {
            throw new Exception( 'Erreur lors de la collecte des données de performance' );
        }
        
        // Enrichir les données avec des analyses avancées
        $this->report_data = array(
            'metadata' => $this->generate_metadata(),
            'executive_summary' => $this->generate_executive_summary( $performance_data ),
            'global_statistics' => $this->enhance_global_statistics( $performance_data['global_stats'] ),
            'performance_trends' => $this->analyze_performance_trends( $performance_data ),
            'pages_analysis' => $this->analyze_pages_performance( $performance_data['pages_performance'] ),
            'technical_performance' => $this->analyze_technical_performance( $performance_data['technical_performance'] ),
            'recommendations' => $this->generate_recommendations( $performance_data ),
            'action_plan' => $this->generate_action_plan( $performance_data ),
            'charts_data' => $this->generate_charts_data( $performance_data )
        );
        
        return $this->report_data;
    }

    /**
     * Génère les métadonnées du rapport.
     *
     * @since    1.2.0
     * @return   array    Métadonnées.
     */
    private function generate_metadata() {
        return array(
            'title' => 'Rapport de Performance SEO',
            'subtitle' => 'Analyse complète des performances SEO du site',
            'generated_at' => current_time( 'mysql' ),
            'generated_by' => wp_get_current_user()->display_name,
            'site_url' => home_url(),
            'site_name' => get_bloginfo( 'name' ),
            'period' => array(
                'start' => date( 'Y-m-d', strtotime( '-30 days' ) ),
                'end' => date( 'Y-m-d' ),
                'label' => '30 derniers jours'
            ),
            'version' => '1.2.0'
        );
    }

    /**
     * Génère le résumé exécutif.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Résumé exécutif.
     */
    private function generate_executive_summary( $performance_data ) {
        $global_stats = $performance_data['global_stats'];
        $total_content = $global_stats['totalContent'] ?? 0;
        $optimized_content = $global_stats['optimizedContent'] ?? 0;
        $average_score = $global_stats['averageScore'] ?? 0;
        
        $optimization_rate = $total_content > 0 ? round( ( $optimized_content / $total_content ) * 100, 1 ) : 0;
        
        // Déterminer le niveau de performance
        $performance_level = $this->get_performance_level( $average_score );
        
        // Identifier les problèmes principaux
        $main_issues = $this->identify_main_issues( $performance_data );
        
        return array(
            'total_content' => $total_content,
            'optimized_content' => $optimized_content,
            'optimization_rate' => $optimization_rate,
            'average_score' => $average_score,
            'performance_level' => $performance_level,
            'main_issues' => $main_issues,
            'key_insights' => $this->generate_key_insights( $performance_data ),
            'priority_actions' => $this->get_priority_actions( $performance_data )
        );
    }

    /**
     * Enrichit les statistiques globales.
     *
     * @since    1.2.0
     * @param    array    $global_stats    Statistiques globales.
     * @return   array                     Statistiques enrichies.
     */
    private function enhance_global_statistics( $global_stats ) {
        $enhanced = $global_stats;
        
        // Ajouter des métriques calculées
        $total = $global_stats['totalContent'] ?? 0;
        $optimized = $global_stats['optimizedContent'] ?? 0;
        
        $enhanced['unoptimized_content'] = $total - $optimized;
        $enhanced['optimization_percentage'] = $total > 0 ? round( ( $optimized / $total ) * 100, 1 ) : 0;
        
        // Analyser la répartition par type de contenu
        if ( isset( $global_stats['contentTypes'] ) ) {
            $enhanced['content_distribution'] = $this->analyze_content_distribution( $global_stats['contentTypes'] );
        }
        
        // Analyser les erreurs communes
        if ( isset( $global_stats['commonErrors'] ) ) {
            $enhanced['error_analysis'] = $this->analyze_common_errors( $global_stats['commonErrors'] );
        }
        
        return $enhanced;
    }

    /**
     * Analyse les tendances de performance.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Analyse des tendances.
     */
    private function analyze_performance_trends( $performance_data ) {
        // Pour l'instant, simuler des données de tendance
        // Dans une version future, on stockera l'historique des scores
        
        return array(
            'score_evolution' => array(
                'current_period' => $performance_data['global_stats']['averageScore'] ?? 0,
                'previous_period' => 0, // À implémenter avec l'historique
                'trend' => 'stable', // 'improving', 'declining', 'stable'
                'change_percentage' => 0
            ),
            'content_growth' => array(
                'new_content_added' => 0,
                'content_optimized' => 0,
                'optimization_rate_change' => 0
            ),
            'technical_improvements' => array(
                'errors_fixed' => 0,
                'new_errors' => 0,
                'net_improvement' => 0
            )
        );
    }

    /**
     * Analyse les performances des pages.
     *
     * @since    1.2.0
     * @param    array    $pages_performance    Données des pages.
     * @return   array                          Analyse des pages.
     */
    private function analyze_pages_performance( $pages_performance ) {
        $top_pages = $pages_performance['top_pages'] ?? array();
        $bottom_pages = $pages_performance['bottom_pages'] ?? array();
        
        return array(
            'top_performers' => array(
                'pages' => array_slice( $top_pages, 0, 10 ),
                'average_score' => $this->calculate_average_score( $top_pages ),
                'common_factors' => $this->identify_success_factors( $top_pages )
            ),
            'underperformers' => array(
                'pages' => array_slice( $bottom_pages, 0, 10 ),
                'average_score' => $this->calculate_average_score( $bottom_pages ),
                'common_issues' => $this->identify_common_issues( $bottom_pages )
            ),
            'improvement_opportunities' => $this->identify_improvement_opportunities( $bottom_pages )
        );
    }

    /**
     * Analyse les performances techniques.
     *
     * @since    1.2.0
     * @param    array    $technical_data    Données techniques.
     * @return   array                       Analyse technique.
     */
    private function analyze_technical_performance( $technical_data ) {
        $analysis = array(
            'pagespeed_available' => $technical_data['available'] ?? false,
            'overall_health' => 'good', // 'excellent', 'good', 'fair', 'poor'
            'critical_issues' => array(),
            'recommendations' => array()
        );
        
        if ( $technical_data['available'] ) {
            // Analyser les scores PageSpeed si disponibles
            $performance_score = $technical_data['performance_score'] ?? 0;
            $seo_score = $technical_data['seo_score_pagespeed'] ?? 0;
            
            $analysis['pagespeed_scores'] = array(
                'performance' => $performance_score,
                'seo' => $seo_score,
                'accessibility' => $technical_data['accessibility_score'] ?? 0,
                'best_practices' => $technical_data['best_practices_score'] ?? 0
            );
            
            // Déterminer la santé globale
            $average_score = ( $performance_score + $seo_score ) / 2;
            $analysis['overall_health'] = $this->get_health_level( $average_score );
            
            // Analyser les Core Web Vitals
            if ( isset( $technical_data['core_web_vitals'] ) ) {
                $analysis['core_web_vitals'] = $this->analyze_core_web_vitals( $technical_data['core_web_vitals'] );
            }
        } else {
            $analysis['recommendations'][] = array(
                'type' => 'setup',
                'title' => 'Configurer l\'API Google PageSpeed',
                'description' => 'Configurez l\'API Google PageSpeed dans les paramètres pour obtenir des données de performance détaillées.',
                'priority' => 'medium'
            );
        }
        
        return $analysis;
    }

    /**
     * Génère les recommandations.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Recommandations.
     */
    private function generate_recommendations( $performance_data ) {
        $recommendations = array();
        
        $global_stats = $performance_data['global_stats'];
        $average_score = $global_stats['averageScore'] ?? 0;
        $optimization_rate = $global_stats['totalContent'] > 0 ? 
            ( $global_stats['optimizedContent'] / $global_stats['totalContent'] ) * 100 : 0;
        
        // Recommandations basées sur le score moyen
        if ( $average_score < 50 ) {
            $recommendations[] = array(
                'category' => 'urgent',
                'title' => 'Amélioration urgente du SEO',
                'description' => 'Le score SEO moyen est critique. Concentrez-vous sur l\'optimisation des méta-données et du contenu.',
                'actions' => array(
                    'Optimiser les titres et méta-descriptions manquants',
                    'Ajouter des mots-clés focus aux pages',
                    'Améliorer la qualité du contenu'
                ),
                'priority' => 'high',
                'estimated_impact' => 'high'
            );
        } elseif ( $average_score < 70 ) {
            $recommendations[] = array(
                'category' => 'improvement',
                'title' => 'Optimisation du contenu existant',
                'description' => 'Le SEO est correct mais peut être amélioré. Focalisez-vous sur les pages les moins performantes.',
                'actions' => array(
                    'Réviser les pages avec un score inférieur à 60',
                    'Optimiser la structure des titres (H1, H2, H3)',
                    'Améliorer la densité des mots-clés'
                ),
                'priority' => 'medium',
                'estimated_impact' => 'medium'
            );
        }
        
        // Recommandations basées sur le taux d\'optimisation
        if ( $optimization_rate < 30 ) {
            $recommendations[] = array(
                'category' => 'coverage',
                'title' => 'Augmenter la couverture SEO',
                'description' => 'Beaucoup de contenu n\'est pas optimisé. Étendez vos efforts d\'optimisation.',
                'actions' => array(
                    'Identifier le contenu non optimisé',
                    'Prioriser par trafic et importance',
                    'Mettre en place un plan d\'optimisation systématique'
                ),
                'priority' => 'high',
                'estimated_impact' => 'high'
            );
        }
        
        return $recommendations;
    }

    /**
     * Génère un plan d'action.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Plan d'action.
     */
    private function generate_action_plan( $performance_data ) {
        $action_plan = array(
            'immediate_actions' => array(),
            'short_term_goals' => array(),
            'long_term_strategy' => array()
        );
        
        $global_stats = $performance_data['global_stats'];
        $average_score = $global_stats['averageScore'] ?? 0;
        
        // Actions immédiates (0-7 jours)
        if ( $average_score < 60 ) {
            $action_plan['immediate_actions'][] = array(
                'task' => 'Audit des méta-données manquantes',
                'description' => 'Identifier et corriger les titres et descriptions manquants',
                'estimated_time' => '2-4 heures',
                'impact' => 'high'
            );
        }
        
        // Objectifs à court terme (1-4 semaines)
        $action_plan['short_term_goals'][] = array(
            'goal' => 'Améliorer le score SEO moyen',
            'target' => 'Atteindre un score moyen de ' . min( $average_score + 20, 90 ),
            'timeline' => '2-3 semaines',
            'key_actions' => array(
                'Optimiser les 20 pages les moins performantes',
                'Améliorer la qualité du contenu',
                'Corriger les erreurs techniques'
            )
        );
        
        // Stratégie à long terme (1-6 mois)
        $action_plan['long_term_strategy'][] = array(
            'strategy' => 'Optimisation systématique',
            'description' => 'Mettre en place un processus d\'optimisation continue',
            'timeline' => '3-6 mois',
            'milestones' => array(
                'Optimiser 100% du contenu existant',
                'Mettre en place des templates SEO',
                'Former l\'équipe aux bonnes pratiques'
            )
        );
        
        return $action_plan;
    }

    /**
     * Génère les données pour les graphiques.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Données des graphiques.
     */
    private function generate_charts_data( $performance_data ) {
        $global_stats = $performance_data['global_stats'];

        return array(
            'score_distribution' => $this->generate_score_distribution_chart( $performance_data ),
            'content_types_pie' => $this->generate_content_types_pie_chart( $global_stats ),
            'optimization_progress' => $this->generate_optimization_progress_chart( $global_stats ),
            'errors_breakdown' => $this->generate_errors_breakdown_chart( $global_stats )
        );
    }

    /**
     * Détermine le niveau de performance.
     *
     * @since    1.2.0
     * @param    float    $score    Score moyen.
     * @return   array              Niveau de performance.
     */
    private function get_performance_level( $score ) {
        if ( $score >= 80 ) {
            return array(
                'level' => 'excellent',
                'label' => 'Excellent',
                'color' => $this->config['chart_colors']['success'],
                'description' => 'Votre SEO est excellent. Continuez sur cette voie !'
            );
        } elseif ( $score >= 60 ) {
            return array(
                'level' => 'good',
                'label' => 'Bon',
                'color' => $this->config['chart_colors']['primary'],
                'description' => 'Votre SEO est bon mais peut être amélioré.'
            );
        } elseif ( $score >= 40 ) {
            return array(
                'level' => 'fair',
                'label' => 'Moyen',
                'color' => $this->config['chart_colors']['warning'],
                'description' => 'Votre SEO nécessite des améliorations importantes.'
            );
        } else {
            return array(
                'level' => 'poor',
                'label' => 'Faible',
                'color' => $this->config['chart_colors']['danger'],
                'description' => 'Votre SEO nécessite une attention urgente.'
            );
        }
    }

    /**
     * Identifie les problèmes principaux.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Problèmes principaux.
     */
    private function identify_main_issues( $performance_data ) {
        $issues = array();
        $global_stats = $performance_data['global_stats'];

        // Analyser les erreurs communes
        if ( isset( $global_stats['commonErrors'] ) && ! empty( $global_stats['commonErrors'] ) ) {
            foreach ( $global_stats['commonErrors'] as $error ) {
                if ( $error['count'] > 5 ) { // Seuil de 5 occurrences
                    $issues[] = array(
                        'type' => 'error',
                        'title' => $error['type'],
                        'count' => $error['count'],
                        'severity' => $error['severity'] ?? 'medium'
                    );
                }
            }
        }

        // Vérifier le taux d'optimisation
        $total = $global_stats['totalContent'] ?? 0;
        $optimized = $global_stats['optimizedContent'] ?? 0;
        $optimization_rate = $total > 0 ? ( $optimized / $total ) * 100 : 0;

        if ( $optimization_rate < 50 ) {
            $issues[] = array(
                'type' => 'coverage',
                'title' => 'Faible taux d\'optimisation',
                'description' => "Seulement {$optimization_rate}% du contenu est optimisé",
                'severity' => 'high'
            );
        }

        return array_slice( $issues, 0, 5 ); // Limiter à 5 problèmes principaux
    }

    /**
     * Génère les insights clés.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Insights clés.
     */
    private function generate_key_insights( $performance_data ) {
        $insights = array();
        $global_stats = $performance_data['global_stats'];

        $total = $global_stats['totalContent'] ?? 0;
        $optimized = $global_stats['optimizedContent'] ?? 0;
        $average_score = $global_stats['averageScore'] ?? 0;

        // Insight sur le contenu
        if ( $total > 0 ) {
            $unoptimized = $total - $optimized;
            if ( $unoptimized > 0 ) {
                $insights[] = "Vous avez {$unoptimized} pages qui pourraient bénéficier d'une optimisation SEO.";
            }
        }

        // Insight sur les performances
        if ( $average_score > 0 ) {
            $potential_improvement = 90 - $average_score;
            if ( $potential_improvement > 10 ) {
                $insights[] = "Votre score SEO pourrait être amélioré de {$potential_improvement} points avec les bonnes optimisations.";
            }
        }

        // Insight sur les types de contenu
        if ( isset( $global_stats['contentTypes'] ) ) {
            $content_types = $global_stats['contentTypes'];
            $most_content_type = array_keys( $content_types, max( $content_types ) )[0];
            $insights[] = "Vos {$most_content_type} représentent la majorité de votre contenu et méritent une attention particulière.";
        }

        return $insights;
    }

    /**
     * Obtient les actions prioritaires.
     *
     * @since    1.2.0
     * @param    array    $performance_data    Données de performance.
     * @return   array                         Actions prioritaires.
     */
    private function get_priority_actions( $performance_data ) {
        $actions = array();
        $global_stats = $performance_data['global_stats'];
        $average_score = $global_stats['averageScore'] ?? 0;

        if ( $average_score < 50 ) {
            $actions[] = array(
                'action' => 'Optimiser les méta-données',
                'priority' => 'urgent',
                'estimated_impact' => '+15-25 points de score'
            );
        }

        if ( isset( $global_stats['commonErrors'] ) && ! empty( $global_stats['commonErrors'] ) ) {
            $actions[] = array(
                'action' => 'Corriger les erreurs techniques',
                'priority' => 'high',
                'estimated_impact' => '+10-20 points de score'
            );
        }

        $total = $global_stats['totalContent'] ?? 0;
        $optimized = $global_stats['optimizedContent'] ?? 0;
        $optimization_rate = $total > 0 ? ( $optimized / $total ) * 100 : 0;

        if ( $optimization_rate < 30 ) {
            $actions[] = array(
                'action' => 'Étendre l\'optimisation à plus de contenu',
                'priority' => 'medium',
                'estimated_impact' => 'Amélioration globale significative'
            );
        }

        return array_slice( $actions, 0, 3 ); // Top 3 actions
    }

    /**
     * Calcule le score moyen d'un ensemble de pages.
     *
     * @since    1.2.0
     * @param    array    $pages    Pages à analyser.
     * @return   float              Score moyen.
     */
    private function calculate_average_score( $pages ) {
        if ( empty( $pages ) ) {
            return 0;
        }

        $total_score = 0;
        foreach ( $pages as $page ) {
            $total_score += $page['score'] ?? 0;
        }

        return round( $total_score / count( $pages ), 1 );
    }

    /**
     * Identifie les facteurs de succès.
     *
     * @since    1.2.0
     * @param    array    $top_pages    Pages les plus performantes.
     * @return   array                  Facteurs de succès.
     */
    private function identify_success_factors( $top_pages ) {
        // Analyse basique des facteurs communs
        $factors = array();

        if ( ! empty( $top_pages ) ) {
            $factors[] = 'Méta-données complètes et optimisées';
            $factors[] = 'Contenu de qualité avec mots-clés pertinents';
            $factors[] = 'Structure HTML bien organisée';
        }

        return $factors;
    }

    /**
     * Identifie les problèmes communs.
     *
     * @since    1.2.0
     * @param    array    $bottom_pages    Pages les moins performantes.
     * @return   array                     Problèmes communs.
     */
    private function identify_common_issues( $bottom_pages ) {
        $issues = array();

        if ( ! empty( $bottom_pages ) ) {
            $issues[] = 'Méta-données manquantes ou incomplètes';
            $issues[] = 'Contenu trop court ou peu optimisé';
            $issues[] = 'Absence de mots-clés focus';
        }

        return $issues;
    }

    /**
     * Identifie les opportunités d'amélioration.
     *
     * @since    1.2.0
     * @param    array    $bottom_pages    Pages les moins performantes.
     * @return   array                     Opportunités.
     */
    private function identify_improvement_opportunities( $bottom_pages ) {
        $opportunities = array();

        foreach ( array_slice( $bottom_pages, 0, 5 ) as $page ) {
            $opportunities[] = array(
                'page_id' => $page['id'],
                'title' => $page['title'],
                'current_score' => $page['score'],
                'potential_score' => min( $page['score'] + 30, 90 ),
                'quick_wins' => array(
                    'Ajouter une méta-description',
                    'Optimiser le titre SEO',
                    'Ajouter des mots-clés focus'
                )
            );
        }

        return $opportunities;
    }

    /**
     * Détermine le niveau de santé technique.
     *
     * @since    1.2.0
     * @param    float    $score    Score technique moyen.
     * @return   string             Niveau de santé.
     */
    private function get_health_level( $score ) {
        if ( $score >= 80 ) return 'excellent';
        if ( $score >= 60 ) return 'good';
        if ( $score >= 40 ) return 'fair';
        return 'poor';
    }

    /**
     * Analyse les Core Web Vitals.
     *
     * @since    1.2.0
     * @param    array    $vitals    Données Core Web Vitals.
     * @return   array               Analyse des vitals.
     */
    private function analyze_core_web_vitals( $vitals ) {
        return array(
            'lcp' => array(
                'value' => $vitals['lcp'] ?? 'N/A',
                'status' => $this->get_vital_status( $vitals['lcp'] ?? null, 'lcp' )
            ),
            'fid' => array(
                'value' => $vitals['fid'] ?? 'N/A',
                'status' => $this->get_vital_status( $vitals['fid'] ?? null, 'fid' )
            ),
            'cls' => array(
                'value' => $vitals['cls'] ?? 'N/A',
                'status' => $this->get_vital_status( $vitals['cls'] ?? null, 'cls' )
            )
        );
    }

    /**
     * Détermine le statut d'un Core Web Vital.
     *
     * @since    1.2.0
     * @param    mixed     $value    Valeur du vital.
     * @param    string    $type     Type de vital.
     * @return   string              Statut (good, needs-improvement, poor).
     */
    private function get_vital_status( $value, $type ) {
        if ( $value === null || $value === 'N/A' ) {
            return 'unknown';
        }

        // Logique simplifiée - à améliorer avec les vrais seuils
        return 'good'; // Par défaut
    }
}
