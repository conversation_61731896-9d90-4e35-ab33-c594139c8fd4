{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_deprecated", "_element", "_compose", "_tooltip", "_icon", "_visually<PERSON><PERSON>den", "_utils", "disabledEventsOnDisabledButton", "useDeprecatedProps", "isDefault", "isPrimary", "isSecondary", "isTertiary", "isLink", "isPressed", "isSmall", "size", "variant", "otherProps", "computedSize", "computed<PERSON><PERSON>t", "newProps", "_computedSize", "_computedVariant", "_computedVariant2", "_computedVariant3", "_computedVariant4", "deprecated", "since", "alternative", "version", "_computedVariant5", "UnforwardedButton", "props", "ref", "__next40pxDefaultSize", "isBusy", "isDestructive", "className", "disabled", "icon", "iconPosition", "iconSize", "showTooltip", "tooltipPosition", "shortcut", "label", "children", "text", "__experimentalIsFocusable", "isFocusable", "describedBy", "buttonOrAnchorProps", "href", "target", "ariaChe<PERSON>", "ariaPressed", "ariaSelected", "additionalProps", "undefined", "instanceId", "useInstanceId", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "truthyAriaPressedValues", "classes", "classnames", "includes", "trulyDisabled", "Tag", "buttonProps", "type", "anchorProps", "disabledEvent", "event", "stopPropagation", "preventDefault", "shouldShowTooltip", "length", "descriptionId", "describedById", "commonProps", "elementChildren", "_react", "createElement", "Fragment", "default", "element", "tooltipProps", "placement", "positionToPlacement", "VisuallyHidden", "id", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/button/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type {\n\tComponentPropsWithoutRef,\n\tForwardedRef,\n\tHTMLAttributes,\n\tMouseEvent,\n\tReactElement,\n} from 'react';\n\n/**\n * WordPress dependencies\n */\nimport deprecated from '@wordpress/deprecated';\nimport { forwardRef } from '@wordpress/element';\nimport { useInstanceId } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport Tooltip from '../tooltip';\nimport Icon from '../icon';\nimport { VisuallyHidden } from '../visually-hidden';\nimport type { ButtonProps, DeprecatedButtonProps } from './types';\nimport { positionToPlacement } from '../popover/utils';\n\nconst disabledEventsOnDisabledButton = [ 'onMouseDown', 'onClick' ] as const;\n\nfunction useDeprecatedProps( {\n\tisDefault,\n\tisPrimary,\n\tisSecondary,\n\tisTertiary,\n\tisLink,\n\tisPressed,\n\tisSmall,\n\tsize,\n\tvariant,\n\t...otherProps\n}: ButtonProps & DeprecatedButtonProps ): ButtonProps {\n\tlet computedSize = size;\n\tlet computedVariant = variant;\n\n\tconst newProps: { 'aria-pressed'?: boolean } = {\n\t\t// @TODO Mark `isPressed` as deprecated\n\t\t'aria-pressed': isPressed,\n\t};\n\n\tif ( isSmall ) {\n\t\tcomputedSize ??= 'small';\n\t}\n\n\tif ( isPrimary ) {\n\t\tcomputedVariant ??= 'primary';\n\t}\n\n\tif ( isTertiary ) {\n\t\tcomputedVariant ??= 'tertiary';\n\t}\n\n\tif ( isSecondary ) {\n\t\tcomputedVariant ??= 'secondary';\n\t}\n\n\tif ( isDefault ) {\n\t\tdeprecated( 'Button isDefault prop', {\n\t\t\tsince: '5.4',\n\t\t\talternative: 'variant=\"secondary\"',\n\t\t\tversion: '6.2',\n\t\t} );\n\n\t\tcomputedVariant ??= 'secondary';\n\t}\n\n\tif ( isLink ) {\n\t\tcomputedVariant ??= 'link';\n\t}\n\n\treturn {\n\t\t...newProps,\n\t\t...otherProps,\n\t\tsize: computedSize,\n\t\tvariant: computedVariant,\n\t};\n}\n\nexport function UnforwardedButton(\n\tprops: ButtonProps,\n\tref: ForwardedRef< any >\n) {\n\tconst {\n\t\t__next40pxDefaultSize,\n\t\tisBusy,\n\t\tisDestructive,\n\t\tclassName,\n\t\tdisabled,\n\t\ticon,\n\t\ticonPosition = 'left',\n\t\ticonSize,\n\t\tshowTooltip,\n\t\ttooltipPosition,\n\t\tshortcut,\n\t\tlabel,\n\t\tchildren,\n\t\tsize = 'default',\n\t\ttext,\n\t\tvariant,\n\t\t__experimentalIsFocusable: isFocusable,\n\t\tdescribedBy,\n\t\t...buttonOrAnchorProps\n\t} = useDeprecatedProps( props );\n\n\tconst {\n\t\thref,\n\t\ttarget,\n\t\t'aria-checked': ariaChecked,\n\t\t'aria-pressed': ariaPressed,\n\t\t'aria-selected': ariaSelected,\n\t\t...additionalProps\n\t} = 'href' in buttonOrAnchorProps\n\t\t? buttonOrAnchorProps\n\t\t: { href: undefined, target: undefined, ...buttonOrAnchorProps };\n\n\tconst instanceId = useInstanceId(\n\t\tButton,\n\t\t'components-button__description'\n\t);\n\n\tconst hasChildren =\n\t\t( 'string' === typeof children && !! children ) ||\n\t\t( Array.isArray( children ) &&\n\t\t\tchildren?.[ 0 ] &&\n\t\t\tchildren[ 0 ] !== null &&\n\t\t\t// Tooltip should not considered as a child\n\t\t\tchildren?.[ 0 ]?.props?.className !== 'components-tooltip' );\n\n\tconst truthyAriaPressedValues: ( typeof ariaPressed )[] = [\n\t\ttrue,\n\t\t'true',\n\t\t'mixed',\n\t];\n\n\tconst classes = classnames( 'components-button', className, {\n\t\t'is-next-40px-default-size': __next40pxDefaultSize,\n\t\t'is-secondary': variant === 'secondary',\n\t\t'is-primary': variant === 'primary',\n\t\t'is-small': size === 'small',\n\t\t'is-compact': size === 'compact',\n\t\t'is-tertiary': variant === 'tertiary',\n\n\t\t'is-pressed': truthyAriaPressedValues.includes( ariaPressed ),\n\t\t'is-pressed-mixed': ariaPressed === 'mixed',\n\n\t\t'is-busy': isBusy,\n\t\t'is-link': variant === 'link',\n\t\t'is-destructive': isDestructive,\n\t\t'has-text': !! icon && ( hasChildren || text ),\n\t\t'has-icon': !! icon,\n\t} );\n\n\tconst trulyDisabled = disabled && ! isFocusable;\n\tconst Tag = href !== undefined && ! trulyDisabled ? 'a' : 'button';\n\tconst buttonProps: ComponentPropsWithoutRef< 'button' > =\n\t\tTag === 'button'\n\t\t\t? {\n\t\t\t\t\ttype: 'button',\n\t\t\t\t\tdisabled: trulyDisabled,\n\t\t\t\t\t'aria-checked': ariaChecked,\n\t\t\t\t\t'aria-pressed': ariaPressed,\n\t\t\t\t\t'aria-selected': ariaSelected,\n\t\t\t  }\n\t\t\t: {};\n\tconst anchorProps: ComponentPropsWithoutRef< 'a' > =\n\t\tTag === 'a' ? { href, target } : {};\n\n\tif ( disabled && isFocusable ) {\n\t\t// In this case, the button will be disabled, but still focusable and\n\t\t// perceivable by screen reader users.\n\t\tbuttonProps[ 'aria-disabled' ] = true;\n\t\tanchorProps[ 'aria-disabled' ] = true;\n\n\t\tfor ( const disabledEvent of disabledEventsOnDisabledButton ) {\n\t\t\tadditionalProps[ disabledEvent ] = ( event: MouseEvent ) => {\n\t\t\t\tif ( event ) {\n\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t}\n\n\t// Should show the tooltip if...\n\tconst shouldShowTooltip =\n\t\t! trulyDisabled &&\n\t\t// An explicit tooltip is passed or...\n\t\t( ( showTooltip && !! label ) ||\n\t\t\t// There's a shortcut or...\n\t\t\t!! shortcut ||\n\t\t\t// There's a label and...\n\t\t\t( !! label &&\n\t\t\t\t// The children are empty and...\n\t\t\t\t! ( children as string | ReactElement[] )?.length &&\n\t\t\t\t// The tooltip is not explicitly disabled.\n\t\t\t\tfalse !== showTooltip ) );\n\n\tconst descriptionId = describedBy ? instanceId : undefined;\n\n\tconst describedById =\n\t\tadditionalProps[ 'aria-describedby' ] || descriptionId;\n\n\tconst commonProps = {\n\t\tclassName: classes,\n\t\t'aria-label': additionalProps[ 'aria-label' ] || label,\n\t\t'aria-describedby': describedById,\n\t\tref,\n\t};\n\n\tconst elementChildren = (\n\t\t<>\n\t\t\t{ icon && iconPosition === 'left' && (\n\t\t\t\t<Icon icon={ icon } size={ iconSize } />\n\t\t\t) }\n\t\t\t{ text && <>{ text }</> }\n\t\t\t{ icon && iconPosition === 'right' && (\n\t\t\t\t<Icon icon={ icon } size={ iconSize } />\n\t\t\t) }\n\t\t\t{ children }\n\t\t</>\n\t);\n\n\tconst element =\n\t\tTag === 'a' ? (\n\t\t\t<a\n\t\t\t\t{ ...anchorProps }\n\t\t\t\t{ ...( additionalProps as HTMLAttributes< HTMLAnchorElement > ) }\n\t\t\t\t{ ...commonProps }\n\t\t\t>\n\t\t\t\t{ elementChildren }\n\t\t\t</a>\n\t\t) : (\n\t\t\t<button\n\t\t\t\t{ ...buttonProps }\n\t\t\t\t{ ...( additionalProps as HTMLAttributes< HTMLButtonElement > ) }\n\t\t\t\t{ ...commonProps }\n\t\t\t>\n\t\t\t\t{ elementChildren }\n\t\t\t</button>\n\t\t);\n\n\t// In order to avoid some React reconciliation issues, we are always rendering\n\t// the `Tooltip` component even when `shouldShowTooltip` is `false`.\n\t// In order to make sure that the tooltip doesn't show when it shouldn't,\n\t// we don't pass the props to the `Tooltip` component.\n\tconst tooltipProps = shouldShowTooltip\n\t\t? {\n\t\t\t\ttext:\n\t\t\t\t\t( children as string | ReactElement[] )?.length &&\n\t\t\t\t\tdescribedBy\n\t\t\t\t\t\t? describedBy\n\t\t\t\t\t\t: label,\n\t\t\t\tshortcut,\n\t\t\t\tplacement:\n\t\t\t\t\ttooltipPosition &&\n\t\t\t\t\t// Convert legacy `position` values to be used with the new `placement` prop\n\t\t\t\t\tpositionToPlacement( tooltipPosition ),\n\t\t  }\n\t\t: {};\n\n\treturn (\n\t\t<>\n\t\t\t<Tooltip { ...tooltipProps }>{ element }</Tooltip>\n\t\t\t{ describedBy && (\n\t\t\t\t<VisuallyHidden>\n\t\t\t\t\t<span id={ descriptionId }>{ describedBy }</span>\n\t\t\t\t</VisuallyHidden>\n\t\t\t) }\n\t\t</>\n\t);\n}\n\n/**\n * Lets users take actions and make choices with a single click or tap.\n *\n * ```jsx\n * import { Button } from '@wordpress/components';\n * const Mybutton = () => (\n *   <Button\n *     variant=\"primary\"\n *     onClick={ handleClick }\n *   >\n *     Click here\n *   </Button>\n * );\n * ```\n */\nexport const Button = forwardRef( UnforwardedButton );\nexport default Button;\n"], "mappings": ";;;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAYA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,QAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,eAAA,GAAAN,OAAA;AAEA,IAAAO,MAAA,GAAAP,OAAA;AA1BA;AACA;AACA;;AAUA;AACA;AACA;;AAKA;AACA;AACA;;AAOA,MAAMQ,8BAA8B,GAAG,CAAE,aAAa,EAAE,SAAS,CAAW;AAE5E,SAASC,kBAAkBA,CAAE;EAC5BC,SAAS;EACTC,SAAS;EACTC,WAAW;EACXC,UAAU;EACVC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,IAAI;EACJC,OAAO;EACP,GAAGC;AACiC,CAAC,EAAgB;EACrD,IAAIC,YAAY,GAAGH,IAAI;EACvB,IAAII,eAAe,GAAGH,OAAO;EAE7B,MAAMI,QAAsC,GAAG;IAC9C;IACA,cAAc,EAAEP;EACjB,CAAC;EAED,IAAKC,OAAO,EAAG;IAAA,IAAAO,aAAA;IACd,CAAAA,aAAA,GAAAH,YAAY,cAAAG,aAAA,cAAAA,aAAA,GAAZH,YAAY,GAAK,OAAO;EACzB;EAEA,IAAKT,SAAS,EAAG;IAAA,IAAAa,gBAAA;IAChB,CAAAA,gBAAA,GAAAH,eAAe,cAAAG,gBAAA,cAAAA,gBAAA,GAAfH,eAAe,GAAK,SAAS;EAC9B;EAEA,IAAKR,UAAU,EAAG;IAAA,IAAAY,iBAAA;IACjB,CAAAA,iBAAA,GAAAJ,eAAe,cAAAI,iBAAA,cAAAA,iBAAA,GAAfJ,eAAe,GAAK,UAAU;EAC/B;EAEA,IAAKT,WAAW,EAAG;IAAA,IAAAc,iBAAA;IAClB,CAAAA,iBAAA,GAAAL,eAAe,cAAAK,iBAAA,cAAAA,iBAAA,GAAfL,eAAe,GAAK,WAAW;EAChC;EAEA,IAAKX,SAAS,EAAG;IAAA,IAAAiB,iBAAA;IAChB,IAAAC,mBAAU,EAAE,uBAAuB,EAAE;MACpCC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE,qBAAqB;MAClCC,OAAO,EAAE;IACV,CAAE,CAAC;IAEH,CAAAJ,iBAAA,GAAAN,eAAe,cAAAM,iBAAA,cAAAA,iBAAA,GAAfN,eAAe,GAAK,WAAW;EAChC;EAEA,IAAKP,MAAM,EAAG;IAAA,IAAAkB,iBAAA;IACb,CAAAA,iBAAA,GAAAX,eAAe,cAAAW,iBAAA,cAAAA,iBAAA,GAAfX,eAAe,GAAK,MAAM;EAC3B;EAEA,OAAO;IACN,GAAGC,QAAQ;IACX,GAAGH,UAAU;IACbF,IAAI,EAAEG,YAAY;IAClBF,OAAO,EAAEG;EACV,CAAC;AACF;AAEO,SAASY,iBAAiBA,CAChCC,KAAkB,EAClBC,GAAwB,EACvB;EACD,MAAM;IACLC,qBAAqB;IACrBC,MAAM;IACNC,aAAa;IACbC,SAAS;IACTC,QAAQ;IACRC,IAAI;IACJC,YAAY,GAAG,MAAM;IACrBC,QAAQ;IACRC,WAAW;IACXC,eAAe;IACfC,QAAQ;IACRC,KAAK;IACLC,QAAQ;IACR/B,IAAI,GAAG,SAAS;IAChBgC,IAAI;IACJ/B,OAAO;IACPgC,yBAAyB,EAAEC,WAAW;IACtCC,WAAW;IACX,GAAGC;EACJ,CAAC,GAAG5C,kBAAkB,CAAEyB,KAAM,CAAC;EAE/B,MAAM;IACLoB,IAAI;IACJC,MAAM;IACN,cAAc,EAAEC,WAAW;IAC3B,cAAc,EAAEC,WAAW;IAC3B,eAAe,EAAEC,YAAY;IAC7B,GAAGC;EACJ,CAAC,GAAG,MAAM,IAAIN,mBAAmB,GAC9BA,mBAAmB,GACnB;IAAEC,IAAI,EAAEM,SAAS;IAAEL,MAAM,EAAEK,SAAS;IAAE,GAAGP;EAAoB,CAAC;EAEjE,MAAMQ,UAAU,GAAG,IAAAC,sBAAa,EAC/BC,MAAM,EACN,gCACD,CAAC;EAED,MAAMC,WAAW,GACd,QAAQ,KAAK,OAAOhB,QAAQ,IAAI,CAAC,CAAEA,QAAQ,IAC3CiB,KAAK,CAACC,OAAO,CAAElB,QAAS,CAAC,IAC1BA,QAAQ,GAAI,CAAC,CAAE,IACfA,QAAQ,CAAE,CAAC,CAAE,KAAK,IAAI;EACtB;EACAA,QAAQ,GAAI,CAAC,CAAE,EAAEd,KAAK,EAAEK,SAAS,KAAK,oBAAsB;EAE9D,MAAM4B,uBAAiD,GAAG,CACzD,IAAI,EACJ,MAAM,EACN,OAAO,CACP;EAED,MAAMC,OAAO,GAAG,IAAAC,mBAAU,EAAE,mBAAmB,EAAE9B,SAAS,EAAE;IAC3D,2BAA2B,EAAEH,qBAAqB;IAClD,cAAc,EAAElB,OAAO,KAAK,WAAW;IACvC,YAAY,EAAEA,OAAO,KAAK,SAAS;IACnC,UAAU,EAAED,IAAI,KAAK,OAAO;IAC5B,YAAY,EAAEA,IAAI,KAAK,SAAS;IAChC,aAAa,EAAEC,OAAO,KAAK,UAAU;IAErC,YAAY,EAAEiD,uBAAuB,CAACG,QAAQ,CAAEb,WAAY,CAAC;IAC7D,kBAAkB,EAAEA,WAAW,KAAK,OAAO;IAE3C,SAAS,EAAEpB,MAAM;IACjB,SAAS,EAAEnB,OAAO,KAAK,MAAM;IAC7B,gBAAgB,EAAEoB,aAAa;IAC/B,UAAU,EAAE,CAAC,CAAEG,IAAI,KAAMuB,WAAW,IAAIf,IAAI,CAAE;IAC9C,UAAU,EAAE,CAAC,CAAER;EAChB,CAAE,CAAC;EAEH,MAAM8B,aAAa,GAAG/B,QAAQ,IAAI,CAAEW,WAAW;EAC/C,MAAMqB,GAAG,GAAGlB,IAAI,KAAKM,SAAS,IAAI,CAAEW,aAAa,GAAG,GAAG,GAAG,QAAQ;EAClE,MAAME,WAAiD,GACtDD,GAAG,KAAK,QAAQ,GACb;IACAE,IAAI,EAAE,QAAQ;IACdlC,QAAQ,EAAE+B,aAAa;IACvB,cAAc,EAAEf,WAAW;IAC3B,cAAc,EAAEC,WAAW;IAC3B,eAAe,EAAEC;EACjB,CAAC,GACD,CAAC,CAAC;EACN,MAAMiB,WAA4C,GACjDH,GAAG,KAAK,GAAG,GAAG;IAAElB,IAAI;IAAEC;EAAO,CAAC,GAAG,CAAC,CAAC;EAEpC,IAAKf,QAAQ,IAAIW,WAAW,EAAG;IAC9B;IACA;IACAsB,WAAW,CAAE,eAAe,CAAE,GAAG,IAAI;IACrCE,WAAW,CAAE,eAAe,CAAE,GAAG,IAAI;IAErC,KAAM,MAAMC,aAAa,IAAIpE,8BAA8B,EAAG;MAC7DmD,eAAe,CAAEiB,aAAa,CAAE,GAAKC,KAAiB,IAAM;QAC3D,IAAKA,KAAK,EAAG;UACZA,KAAK,CAACC,eAAe,CAAC,CAAC;UACvBD,KAAK,CAACE,cAAc,CAAC,CAAC;QACvB;MACD,CAAC;IACF;EACD;;EAEA;EACA,MAAMC,iBAAiB,GACtB,CAAET,aAAa;EACf;EACI3B,WAAW,IAAI,CAAC,CAAEG,KAAK;EAC1B;EACA,CAAC,CAAED,QAAQ;EACX;EACE,CAAC,CAAEC,KAAK;EACT;EACA,CAAIC,QAAQ,EAA+BiC,MAAM;EACjD;EACA,KAAK,KAAKrC,WAAa,CAAE;EAE5B,MAAMsC,aAAa,GAAG9B,WAAW,GAAGS,UAAU,GAAGD,SAAS;EAE1D,MAAMuB,aAAa,GAClBxB,eAAe,CAAE,kBAAkB,CAAE,IAAIuB,aAAa;EAEvD,MAAME,WAAW,GAAG;IACnB7C,SAAS,EAAE6B,OAAO;IAClB,YAAY,EAAET,eAAe,CAAE,YAAY,CAAE,IAAIZ,KAAK;IACtD,kBAAkB,EAAEoC,aAAa;IACjChD;EACD,CAAC;EAED,MAAMkD,eAAe,GACpB,IAAAC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACG/C,IAAI,IAAIC,YAAY,KAAK,MAAM,IAChC,IAAA4C,MAAA,CAAAC,aAAA,EAAClF,KAAA,CAAAoF,OAAI;IAAChD,IAAI,EAAGA,IAAM;IAACxB,IAAI,EAAG0B;EAAU,CAAE,CACvC,EACCM,IAAI,IAAI,IAAAqC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QAAIvC,IAAQ,CAAC,EACrBR,IAAI,IAAIC,YAAY,KAAK,OAAO,IACjC,IAAA4C,MAAA,CAAAC,aAAA,EAAClF,KAAA,CAAAoF,OAAI;IAAChD,IAAI,EAAGA,IAAM;IAACxB,IAAI,EAAG0B;EAAU,CAAE,CACvC,EACCK,QACD,CACF;EAED,MAAM0C,OAAO,GACZlB,GAAG,KAAK,GAAG,GACV,IAAAc,MAAA,CAAAC,aAAA;IAAA,GACMZ,WAAW;IAAA,GACThB,eAAe;IAAA,GACjByB;EAAW,GAEdC,eACA,CAAC,GAEJ,IAAAC,MAAA,CAAAC,aAAA;IAAA,GACMd,WAAW;IAAA,GACTd,eAAe;IAAA,GACjByB;EAAW,GAEdC,eACK,CACR;;EAEF;EACA;EACA;EACA;EACA,MAAMM,YAAY,GAAGX,iBAAiB,GACnC;IACA/B,IAAI,EACDD,QAAQ,EAA+BiC,MAAM,IAC/C7B,WAAW,GACRA,WAAW,GACXL,KAAK;IACTD,QAAQ;IACR8C,SAAS,EACR/C,eAAe;IACf;IACA,IAAAgD,0BAAmB,EAAEhD,eAAgB;EACtC,CAAC,GACD,CAAC,CAAC;EAEL,OACC,IAAAyC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAACnF,QAAA,CAAAqF,OAAO;IAAA,GAAME;EAAY,GAAKD,OAAkB,CAAC,EAChDtC,WAAW,IACZ,IAAAkC,MAAA,CAAAC,aAAA,EAACjF,eAAA,CAAAwF,cAAc,QACd,IAAAR,MAAA,CAAAC,aAAA;IAAMQ,EAAE,EAAGb;EAAe,GAAG9B,WAAmB,CACjC,CAEhB,CAAC;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMW,MAAM,GAAG,IAAAiC,mBAAU,EAAE/D,iBAAkB,CAAC;AAACgE,OAAA,CAAAlC,MAAA,GAAAA,MAAA;AAAA,IAAAmC,QAAA,GACvCnC,MAAM;AAAAkC,OAAA,CAAAR,OAAA,GAAAS,QAAA"}