"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = ClipboardButton;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _button = _interopRequireDefault(require("../button"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const TIMEOUT = 4000;
function ClipboardButton({
  className,
  children,
  onCopy,
  onFinishCopy,
  text,
  ...buttonProps
}) {
  (0, _deprecated.default)('wp.components.ClipboardButton', {
    since: '5.8',
    alternative: 'wp.compose.useCopyToClipboard'
  });
  const timeoutId = (0, _element.useRef)();
  const ref = (0, _compose.useCopyToClipboard)(text, () => {
    onCopy();
    if (timeoutId.current) {
      clearTimeout(timeoutId.current);
    }
    if (onFinishCopy) {
      timeoutId.current = setTimeout(() => onFinishCopy(), TIMEOUT);
    }
  });
  (0, _element.useEffect)(() => {
    if (timeoutId.current) {
      clearTimeout(timeoutId.current);
    }
  }, []);
  const classes = (0, _classnames.default)('components-clipboard-button', className);

  // Workaround for inconsistent behavior in Safari, where <textarea> is not
  // the document.activeElement at the moment when the copy event fires.
  // This causes documentHasSelection() in the copy-handler component to
  // mistakenly override the ClipboardButton, and copy a serialized string
  // of the current block instead.
  const focusOnCopyEventTarget = event => {
    // @ts-expect-error: Should be currentTarget, but not changing because this component is deprecated.
    event.target.focus();
  };
  return (0, _react.createElement)(_button.default, {
    ...buttonProps,
    className: classes,
    ref: ref,
    onCopy: focusOnCopyEventTarget
  }, children);
}
//# sourceMappingURL=index.js.map