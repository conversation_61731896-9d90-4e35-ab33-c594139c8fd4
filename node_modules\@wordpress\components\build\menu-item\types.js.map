{"version": 3, "names": [], "sources": ["@wordpress/components/src/menu-item/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { ButtonAsButtonProps } from '../button/types';\n\nexport type MenuItemProps = Pick< ButtonAsButtonProps, 'isDestructive' > & {\n\t/**\n\t * A CSS `class` to give to the container element.\n\t */\n\tclassName?: string;\n\t/**\n\t * The children elements.\n\t */\n\tchildren?: ReactNode;\n\t/**\n\t * Text to use as description for button text.\n\t */\n\tinfo?: string;\n\t/**\n\t * The icon to render. Supported values are: Dashicons (specified as\n\t * strings), functions, Component instances and `null`.\n\t *\n\t * @default null\n\t */\n\ticon?: JSX.Element | null;\n\t/**\n\t * Determines where to display the provided `icon`.\n\t */\n\ticonPosition?: ButtonAsButtonProps[ 'iconPosition' ];\n\t/**\n\t * Whether or not the menu item is currently selected, `isSelected` is only taken into\n\t * account when the `role` prop is either `\"menuitemcheckbox\"` or `\"menuitemradio\"`.\n\t */\n\tisSelected?: boolean;\n\t/**\n\t * If shortcut is a string, it is expecting the display text. If shortcut is an object,\n\t * it will accept the properties of `display` (string) and `ariaLabel` (string).\n\t */\n\tshortcut?: string | { display: string; ariaLabel: string };\n\t/**\n\t * If you need to have selectable menu items use menuitemradio for single select,\n\t * and menuitemcheckbox for multiselect.\n\t *\n\t * @default 'menuitem'\n\t */\n\trole?: string;\n\t/**\n\t * Allows for markup other than icons or shortcuts to be added to the menu item.\n\t *\n\t */\n\tsuffix?: ReactNode;\n\t/**\n\t * Human-readable label for item.\n\t */\n\tlabel?: string;\n};\n"], "mappings": ""}