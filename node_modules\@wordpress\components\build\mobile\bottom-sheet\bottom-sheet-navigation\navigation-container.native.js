"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _native = require("@react-navigation/native");
var _stack = require("@react-navigation/stack");
var _reactNativeReanimated = _interopRequireWildcard(require("react-native-reanimated"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _bottomSheetNavigationContext = require("./bottom-sheet-navigation-context");
var _bottomSheetContext = require("../bottom-sheet-context");
var _styles = _interopRequireDefault(require("./styles.scss"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const AnimationSpec = {
  animation: 'timing',
  config: {
    duration: 200,
    easing: _reactNativeReanimated.Easing.ease
  }
};
const fadeConfig = ({
  current
}) => {
  return {
    cardStyle: {
      opacity: current.progress
    }
  };
};
const options = {
  transitionSpec: {
    open: AnimationSpec,
    close: AnimationSpec
  },
  headerShown: false,
  gestureEnabled: false,
  cardStyleInterpolator: fadeConfig,
  keyboardHandlingEnabled: false
};
const HEIGHT_ANIMATION_DURATION = 300;
const DEFAULT_HEIGHT = 1;
function BottomSheetNavigationContainer({
  children,
  animate,
  main,
  theme,
  style,
  testID
}) {
  const Stack = (0, _element.useRef)((0, _stack.createStackNavigator)()).current;
  const navigationContext = (0, _element.useContext)(_bottomSheetNavigationContext.BottomSheetNavigationContext);
  const {
    maxHeight: sheetMaxHeight,
    isMaxHeightSet: isSheetMaxHeightSet
  } = (0, _element.useContext)(_bottomSheetContext.BottomSheetContext);
  const currentHeight = (0, _reactNativeReanimated.useSharedValue)(navigationContext.currentHeight?.value || DEFAULT_HEIGHT);
  const backgroundStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.background, _styles.default.backgroundDark);
  const defaultTheme = (0, _element.useMemo)(() => ({
    ..._native.DefaultTheme,
    colors: {
      ..._native.DefaultTheme.colors,
      background: backgroundStyle.backgroundColor
    }
  }), [backgroundStyle.backgroundColor]);
  const _theme = theme || defaultTheme;
  const setHeight = (0, _element.useCallback)(height => {
    if (height > DEFAULT_HEIGHT && Math.round(height) !== Math.round(currentHeight.value)) {
      // If max height is set in the bottom sheet, we clamp
      // the new height using that value.
      const newHeight = isSheetMaxHeightSet ? Math.min(sheetMaxHeight, height) : height;
      const shouldAnimate = animate && currentHeight.value !== DEFAULT_HEIGHT;
      if (shouldAnimate) {
        currentHeight.value = (0, _reactNativeReanimated.withTiming)(newHeight, {
          duration: HEIGHT_ANIMATION_DURATION,
          easing: _reactNativeReanimated.Easing.out(_reactNativeReanimated.Easing.cubic)
        });
      } else {
        currentHeight.value = newHeight;
      }
    }
  }, [animate, currentHeight, isSheetMaxHeightSet, sheetMaxHeight]);
  const animatedStyles = (0, _reactNativeReanimated.useAnimatedStyle)(() => ({
    height: currentHeight.value
  }));
  const screens = (0, _element.useMemo)(() => {
    return _element.Children.map(children, child => {
      let screen = child;
      const {
        name,
        ...otherProps
      } = child.props;
      if (!main) {
        screen = (0, _element.cloneElement)(child, {
          ...child.props,
          isNested: true
        });
      }
      return (0, _react.createElement)(Stack.Screen, {
        name: name,
        ...otherProps,
        children: () => screen
      });
    });
  }, [children, main]);
  return (0, _element.useMemo)(() => {
    return (0, _react.createElement)(_reactNativeReanimated.default.View, {
      style: [style, animatedStyles],
      testID: testID
    }, (0, _react.createElement)(_bottomSheetNavigationContext.BottomSheetNavigationProvider, {
      value: {
        setHeight,
        currentHeight
      }
    }, main ? (0, _react.createElement)(_native.NavigationContainer, {
      theme: _theme
    }, (0, _react.createElement)(Stack.Navigator, {
      screenOptions: options,
      detachInactiveScreens: false
    }, screens)) : (0, _react.createElement)(Stack.Navigator, {
      screenOptions: options,
      detachInactiveScreens: false
    }, screens)));
  }, [_theme, animatedStyles, currentHeight, main, screens, setHeight, style, testID]);
}
var _default = BottomSheetNavigationContainer;
exports.default = _default;
//# sourceMappingURL=navigation-container.native.js.map