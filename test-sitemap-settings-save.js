/**
 * Script de test spécifique pour la sauvegarde des paramètres de sitemap
 * À exécuter dans la console du navigateur sur la page d'admin WordPress
 */

console.log('🧪 Test de sauvegarde des paramètres de sitemap Boss SEO');
console.log('=======================================================');

// Fonction pour tester la sauvegarde avec différents types de données
async function testSitemapSettingsSave() {
    
    // Test 1: Paramètres minimaux valides
    console.log('\n🔍 Test 1: Paramètres minimaux...');
    const minimalSettings = {
        enabled: true,
        includeImages: false,
        includeLastMod: true,
        enableAutoUpdate: false,
        enableImageSitemap: false,
        enableVideoSitemap: false,
        enableStoriesSitemap: false,
        enableNewsSitemap: false,
        enableCustomSitemap: false,
        enableTaxonomySitemaps: false,
        defaultChangeFreq: 'weekly',
        defaultPriority: 0.7,
        autoUpdateFrequency: 'daily',
        includedPostTypes: ['post', 'page'],
        includedTaxonomies: ['category'],
        newsPostTypes: [],
        enablePostTypeSitemaps: {
            'post': true,
            'page': true
        },
        customUrls: []
    };
    
    await testSaveSettings(minimalSettings, 'Paramètres minimaux');
    
    // Test 2: Paramètres complets
    console.log('\n🔍 Test 2: Paramètres complets...');
    const fullSettings = {
        enabled: true,
        includeImages: true,
        includeLastMod: true,
        enableAutoUpdate: true,
        enableImageSitemap: true,
        enableVideoSitemap: true,
        enableStoriesSitemap: false,
        enableNewsSitemap: false,
        enableCustomSitemap: true,
        enableTaxonomySitemaps: true,
        defaultChangeFreq: 'daily',
        defaultPriority: 0.8,
        autoUpdateFrequency: 'hourly',
        includedPostTypes: ['post', 'page', 'product'],
        includedTaxonomies: ['category', 'post_tag'],
        newsPostTypes: ['post'],
        enablePostTypeSitemaps: {
            'post': true,
            'page': true,
            'product': false
        },
        customUrls: [
            {
                loc: 'https://example.com/custom-page',
                lastmod: '2024-01-01',
                changefreq: 'monthly',
                priority: '0.9'
            }
        ]
    };
    
    await testSaveSettings(fullSettings, 'Paramètres complets');
    
    // Test 3: Paramètres avec valeurs nulles/undefined
    console.log('\n🔍 Test 3: Paramètres avec valeurs nulles...');
    const nullSettings = {
        enabled: true,
        includeImages: null,
        includeLastMod: undefined,
        enableAutoUpdate: false,
        defaultChangeFreq: '',
        defaultPriority: null,
        includedPostTypes: null,
        enablePostTypeSitemaps: null,
        customUrls: null
    };
    
    await testSaveSettings(nullSettings, 'Paramètres avec valeurs nulles');
    
    // Test 4: Paramètres vides
    console.log('\n🔍 Test 4: Paramètres vides...');
    await testSaveSettings({}, 'Paramètres vides');
    
    // Test 5: Données invalides
    console.log('\n🔍 Test 5: Données invalides...');
    await testSaveSettings('invalid_data', 'Données invalides (string)');
    await testSaveSettings(null, 'Données invalides (null)');
    await testSaveSettings(undefined, 'Données invalides (undefined)');
}

// Fonction utilitaire pour tester la sauvegarde
async function testSaveSettings(settings, testName) {
    try {
        console.log(`📡 Test: ${testName}`);
        console.log('📤 Données envoyées:', settings);
        
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': wpApiSettings.nonce
            },
            body: JSON.stringify({ settings: settings })
        });
        
        console.log(`📊 Status: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Succès:', result);
            
            // Vérifier que les paramètres ont été sauvegardés en les récupérant
            const getResponse = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings');
            if (getResponse.ok) {
                const savedSettings = await getResponse.json();
                console.log('📥 Paramètres récupérés:', savedSettings);
            }
            
            return { success: true, data: result };
        } else {
            const errorText = await response.text();
            console.log('❌ Erreur HTTP:', errorText);
            return { success: false, error: errorText };
        }
    } catch (error) {
        console.log('💥 Exception:', error);
        return { success: false, error: error.message };
    }
}

// Test de récupération des paramètres actuels
async function getCurrentSettings() {
    console.log('\n📥 Récupération des paramètres actuels...');
    try {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings');
        if (response.ok) {
            const settings = await response.json();
            console.log('✅ Paramètres actuels:', settings);
            return settings;
        } else {
            console.log('❌ Erreur récupération:', response.status, response.statusText);
            return null;
        }
    } catch (error) {
        console.log('💥 Exception récupération:', error);
        return null;
    }
}

// Test de validation des données
function validateSettingsStructure(settings) {
    console.log('\n🔍 Validation de la structure des paramètres...');
    
    const requiredFields = [
        'enabled', 'includeImages', 'includeLastMod', 'enableAutoUpdate',
        'defaultChangeFreq', 'defaultPriority', 'includedPostTypes',
        'enablePostTypeSitemaps', 'customUrls'
    ];
    
    const missing = [];
    const invalid = [];
    
    for (const field of requiredFields) {
        if (!(field in settings)) {
            missing.push(field);
        } else {
            // Validation basique du type
            switch (field) {
                case 'enabled':
                case 'includeImages':
                case 'includeLastMod':
                case 'enableAutoUpdate':
                    if (typeof settings[field] !== 'boolean') {
                        invalid.push(`${field} (doit être boolean, reçu ${typeof settings[field]})`);
                    }
                    break;
                case 'defaultPriority':
                    if (typeof settings[field] !== 'number') {
                        invalid.push(`${field} (doit être number, reçu ${typeof settings[field]})`);
                    }
                    break;
                case 'includedPostTypes':
                case 'customUrls':
                    if (!Array.isArray(settings[field])) {
                        invalid.push(`${field} (doit être array, reçu ${typeof settings[field]})`);
                    }
                    break;
                case 'enablePostTypeSitemaps':
                    if (typeof settings[field] !== 'object' || Array.isArray(settings[field])) {
                        invalid.push(`${field} (doit être object, reçu ${typeof settings[field]})`);
                    }
                    break;
            }
        }
    }
    
    if (missing.length > 0) {
        console.log('❌ Champs manquants:', missing);
    }
    
    if (invalid.length > 0) {
        console.log('❌ Types invalides:', invalid);
    }
    
    if (missing.length === 0 && invalid.length === 0) {
        console.log('✅ Structure valide');
        return true;
    }
    
    return false;
}

// Lancer les tests
console.log('🚀 Lancement des tests de sauvegarde...\n');

getCurrentSettings().then(currentSettings => {
    if (currentSettings) {
        validateSettingsStructure(currentSettings);
    }
    
    return testSitemapSettingsSave();
}).then(() => {
    console.log('\n✅ Tous les tests de sauvegarde terminés!');
    console.log('Vérifiez les logs PHP pour plus de détails sur les erreurs.');
    console.log('Logs disponibles dans wp-content/debug.log si WP_DEBUG_LOG est activé.');
}).catch(error => {
    console.error('💥 Erreur lors des tests:', error);
});

// Fonctions utilitaires disponibles globalement
window.testSitemapSettings = {
    save: testSaveSettings,
    getCurrent: getCurrentSettings,
    validate: validateSettingsStructure,
    runAllTests: testSitemapSettingsSave
};

console.log('\n🛠️ Fonctions utilitaires disponibles:');
console.log('- testSitemapSettings.save(settings, name) - Tester sauvegarde');
console.log('- testSitemapSettings.getCurrent() - Récupérer paramètres actuels');
console.log('- testSitemapSettings.validate(settings) - Valider structure');
console.log('- testSitemapSettings.runAllTests() - Relancer tous les tests');
