"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Picker = void 0;
var _react = require("react");
var _reactColorful = require("react-colorful");
var _colord = require("colord");
var _element = require("@wordpress/element");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Track the start and the end of drag pointer events related to controlling
 * the picker's saturation / hue / alpha, and fire the corresponding callbacks.
 * This is particularly useful to implement synergies like the one with the
 * `Popover` component, where a pointer events "trap" is rendered while
 * the user is dragging the pointer to avoid potential interference with iframe
 * elements.
 *
 * @param props
 * @param props.containerEl
 * @param props.onDragStart
 * @param props.onDragEnd
 */
const useOnPickerDrag = ({
  containerEl,
  onDragStart,
  onDragEnd
}) => {
  const isDragging = (0, _element.useRef)(false);
  const leftWhileDragging = (0, _element.useRef)(false);
  (0, _element.useEffect)(() => {
    if (!containerEl || !onDragStart && !onDragEnd) {
      return;
    }
    const interactiveElements = [containerEl.querySelector('.react-colorful__saturation'), containerEl.querySelector('.react-colorful__hue'), containerEl.querySelector('.react-colorful__alpha')].filter(el => !!el);
    if (interactiveElements.length === 0) {
      return;
    }
    const doc = containerEl.ownerDocument;
    const onPointerUp = event => {
      isDragging.current = false;
      leftWhileDragging.current = false;
      onDragEnd?.(event);
    };
    const onPointerDown = event => {
      isDragging.current = true;
      onDragStart?.(event);
    };
    const onPointerLeave = () => {
      leftWhileDragging.current = isDragging.current;
    };

    // Try to detect if the user released the pointer while away from the
    // current window. If the check is successfull, the dragEnd callback will
    // called as soon as the pointer re-enters the window (better late than never)
    const onPointerEnter = event => {
      const noPointerButtonsArePressed = event.buttons === 0;
      if (leftWhileDragging.current && noPointerButtonsArePressed) {
        onPointerUp(event);
      }
    };

    // The pointerdown event is added on the interactive elements,
    // while the remaining events are added on the document object since
    // the pointer wouldn't necessarily be hovering the initial interactive
    // element at that point.
    interactiveElements.forEach(el => el.addEventListener('pointerdown', onPointerDown));
    doc.addEventListener('pointerup', onPointerUp);
    doc.addEventListener('pointerenter', onPointerEnter);
    doc.addEventListener('pointerleave', onPointerLeave);
    return () => {
      interactiveElements.forEach(el => el.removeEventListener('pointerdown', onPointerDown));
      doc.removeEventListener('pointerup', onPointerUp);
      doc.removeEventListener('pointerenter', onPointerEnter);
      doc.removeEventListener('pointerleave', onPointerUp);
    };
  }, [onDragStart, onDragEnd, containerEl]);
};
const Picker = ({
  color,
  enableAlpha,
  onChange,
  onDragStart,
  onDragEnd,
  containerEl
}) => {
  const Component = enableAlpha ? _reactColorful.RgbaStringColorPicker : _reactColorful.RgbStringColorPicker;
  const rgbColor = (0, _element.useMemo)(() => color.toRgbString(), [color]);
  useOnPickerDrag({
    containerEl,
    onDragStart,
    onDragEnd
  });
  return (0, _react.createElement)(Component, {
    color: rgbColor,
    onChange: nextColor => {
      onChange((0, _colord.colord)(nextColor));
    }
  });
};
exports.Picker = Picker;
//# sourceMappingURL=picker.js.map