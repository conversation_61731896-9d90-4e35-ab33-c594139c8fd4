"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _borderBoxControlVisualizer = _interopRequireDefault(require("../border-box-control-visualizer"));
var _borderControl = require("../../border-control");
var _grid = require("../../grid");
var _context = require("../../context");
var _hook = require("./hook");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BorderBoxControlSplitControls = (props, forwardedRef) => {
  const {
    centeredClassName,
    colors,
    disableCustomColors,
    enableAlpha,
    enableStyle,
    onChange,
    popoverPlacement,
    popoverOffset,
    rightAlignedClassName,
    size = 'default',
    value,
    __experimentalIsRenderedInSidebar,
    ...otherProps
  } = (0, _hook.useBorderBoxControlSplitControls)(props);

  // Use internal state instead of a ref to make sure that the component
  // re-renders when the popover's anchor updates.
  const [popoverAnchor, setPopoverAnchor] = (0, _element.useState)(null);

  // Memoize popoverProps to avoid returning a new object every time.
  const popoverProps = (0, _element.useMemo)(() => popoverPlacement ? {
    placement: popoverPlacement,
    offset: popoverOffset,
    anchor: popoverAnchor,
    shift: true
  } : undefined, [popoverPlacement, popoverOffset, popoverAnchor]);
  const sharedBorderControlProps = {
    colors,
    disableCustomColors,
    enableAlpha,
    enableStyle,
    isCompact: true,
    __experimentalIsRenderedInSidebar,
    size
  };
  const mergedRef = (0, _compose.useMergeRefs)([setPopoverAnchor, forwardedRef]);
  return (0, _react.createElement)(_grid.Grid, {
    ...otherProps,
    ref: mergedRef,
    gap: 4
  }, (0, _react.createElement)(_borderBoxControlVisualizer.default, {
    value: value,
    size: size
  }), (0, _react.createElement)(_borderControl.BorderControl, {
    className: centeredClassName,
    hideLabelFromVision: true,
    label: (0, _i18n.__)('Top border'),
    onChange: newBorder => onChange(newBorder, 'top'),
    __unstablePopoverProps: popoverProps,
    value: value?.top,
    ...sharedBorderControlProps
  }), (0, _react.createElement)(_borderControl.BorderControl, {
    hideLabelFromVision: true,
    label: (0, _i18n.__)('Left border'),
    onChange: newBorder => onChange(newBorder, 'left'),
    __unstablePopoverProps: popoverProps,
    value: value?.left,
    ...sharedBorderControlProps
  }), (0, _react.createElement)(_borderControl.BorderControl, {
    className: rightAlignedClassName,
    hideLabelFromVision: true,
    label: (0, _i18n.__)('Right border'),
    onChange: newBorder => onChange(newBorder, 'right'),
    __unstablePopoverProps: popoverProps,
    value: value?.right,
    ...sharedBorderControlProps
  }), (0, _react.createElement)(_borderControl.BorderControl, {
    className: centeredClassName,
    hideLabelFromVision: true,
    label: (0, _i18n.__)('Bottom border'),
    onChange: newBorder => onChange(newBorder, 'bottom'),
    __unstablePopoverProps: popoverProps,
    value: value?.bottom,
    ...sharedBorderControlProps
  }));
};
const ConnectedBorderBoxControlSplitControls = (0, _context.contextConnect)(BorderBoxControlSplitControls, 'BorderBoxControlSplitControls');
var _default = ConnectedBorderBoxControlSplitControls;
exports.default = _default;
//# sourceMappingURL=component.js.map