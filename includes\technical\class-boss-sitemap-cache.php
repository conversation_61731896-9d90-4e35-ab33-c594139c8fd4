<?php
/**
 * Classe pour le cache avancé et la compression des sitemaps
 *
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 * @since      1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Boss_Sitemap_Cache
 *
 * Gère le cache avancé, la compression et l'optimisation des sitemaps
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */
class Boss_Sitemap_Cache {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Configuration du cache.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $cache_config    Configuration du cache.
     */
    private $cache_config;

    /**
     * Répertoire de cache.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $cache_dir    Répertoire de cache.
     */
    private $cache_dir;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        $this->cache_config = get_option( 'boss_seo_cache_config', array(
            'enabled' => true,
            'compression' => true,
            'gzip_level' => 6,
            'cache_duration' => 3600, // 1 heure
            'max_cache_size' => 50, // MB
            'preload_enabled' => true,
            'mobile_cache' => true,
            'cdn_enabled' => false,
            'cdn_url' => ''
        ) );
        
        $this->cache_dir = WP_CONTENT_DIR . '/cache/boss-seo-sitemaps/';
        $this->ensure_cache_directory();
    }

    /**
     * S'assure que le répertoire de cache existe.
     *
     * @since    1.2.0
     */
    private function ensure_cache_directory() {
        if ( ! file_exists( $this->cache_dir ) ) {
            wp_mkdir_p( $this->cache_dir );
            
            // Créer un fichier .htaccess pour la sécurité
            $htaccess_content = "# Boss SEO Sitemap Cache\n";
            $htaccess_content .= "Options -Indexes\n";
            $htaccess_content .= "<Files *.xml>\n";
            $htaccess_content .= "    Header set Content-Type \"application/xml; charset=UTF-8\"\n";
            $htaccess_content .= "    Header set Cache-Control \"public, max-age=3600\"\n";
            $htaccess_content .= "</Files>\n";
            
            file_put_contents( $this->cache_dir . '.htaccess', $htaccess_content );
        }
    }

    /**
     * Met en cache un sitemap.
     *
     * @since    1.2.0
     * @param    string    $sitemap_type    Le type de sitemap.
     * @param    string    $content         Le contenu XML.
     * @param    array     $metadata        Les métadonnées.
     * @return   bool                       Succès de la mise en cache.
     */
    public function cache_sitemap( $sitemap_type, $content, $metadata = array() ) {
        if ( ! $this->cache_config['enabled'] ) {
            return false;
        }

        try {
            $cache_key = $this->generate_cache_key( $sitemap_type );
            $cache_file = $this->cache_dir . $cache_key . '.xml';
            $meta_file = $this->cache_dir . $cache_key . '.meta';
            
            // Préparer les métadonnées
            $meta_data = array_merge( array(
                'type' => $sitemap_type,
                'generated_at' => current_time( 'mysql' ),
                'expires_at' => date( 'Y-m-d H:i:s', time() + $this->cache_config['cache_duration'] ),
                'size' => strlen( $content ),
                'urls_count' => $this->count_urls_in_sitemap( $content ),
                'version' => $this->version
            ), $metadata );
            
            // Compresser le contenu si activé
            if ( $this->cache_config['compression'] ) {
                $compressed_content = gzencode( $content, $this->cache_config['gzip_level'] );
                $cache_file .= '.gz';
                $meta_data['compressed'] = true;
                $meta_data['compression_ratio'] = round( ( 1 - strlen( $compressed_content ) / strlen( $content ) ) * 100, 2 );
                $content = $compressed_content;
            }
            
            // Écrire les fichiers
            $write_success = file_put_contents( $cache_file, $content ) !== false;
            $meta_success = file_put_contents( $meta_file, json_encode( $meta_data ) ) !== false;
            
            if ( $write_success && $meta_success ) {
                // Nettoyer le cache si nécessaire
                $this->cleanup_cache_if_needed();
                
                return true;
            }
            
            return false;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur mise en cache sitemap - ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Récupère un sitemap depuis le cache.
     *
     * @since    1.2.0
     * @param    string    $sitemap_type    Le type de sitemap.
     * @return   array|false                Le contenu et métadonnées ou false.
     */
    public function get_cached_sitemap( $sitemap_type ) {
        if ( ! $this->cache_config['enabled'] ) {
            return false;
        }

        try {
            $cache_key = $this->generate_cache_key( $sitemap_type );
            $cache_file = $this->cache_dir . $cache_key . '.xml';
            $cache_file_gz = $cache_file . '.gz';
            $meta_file = $this->cache_dir . $cache_key . '.meta';
            
            // Vérifier si les fichiers existent
            $use_compressed = file_exists( $cache_file_gz );
            $cache_file_to_use = $use_compressed ? $cache_file_gz : $cache_file;
            
            if ( ! file_exists( $cache_file_to_use ) || ! file_exists( $meta_file ) ) {
                return false;
            }
            
            // Lire les métadonnées
            $meta_content = file_get_contents( $meta_file );
            $metadata = json_decode( $meta_content, true );
            
            if ( ! $metadata ) {
                return false;
            }
            
            // Vérifier l'expiration
            if ( strtotime( $metadata['expires_at'] ) < time() ) {
                $this->delete_cached_sitemap( $sitemap_type );
                return false;
            }
            
            // Lire le contenu
            $content = file_get_contents( $cache_file_to_use );
            
            if ( $content === false ) {
                return false;
            }
            
            // Décompresser si nécessaire
            if ( $use_compressed || ( isset( $metadata['compressed'] ) && $metadata['compressed'] ) ) {
                $content = gzdecode( $content );
                if ( $content === false ) {
                    return false;
                }
            }
            
            return array(
                'content' => $content,
                'metadata' => $metadata,
                'cache_hit' => true
            );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération cache sitemap - ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Supprime un sitemap du cache.
     *
     * @since    1.2.0
     * @param    string    $sitemap_type    Le type de sitemap.
     * @return   bool                       Succès de la suppression.
     */
    public function delete_cached_sitemap( $sitemap_type ) {
        try {
            $cache_key = $this->generate_cache_key( $sitemap_type );
            $files_to_delete = array(
                $this->cache_dir . $cache_key . '.xml',
                $this->cache_dir . $cache_key . '.xml.gz',
                $this->cache_dir . $cache_key . '.meta'
            );
            
            $deleted = false;
            foreach ( $files_to_delete as $file ) {
                if ( file_exists( $file ) ) {
                    unlink( $file );
                    $deleted = true;
                }
            }
            
            return $deleted;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur suppression cache sitemap - ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Vide tout le cache.
     *
     * @since    1.2.0
     * @return   bool    Succès du vidage.
     */
    public function clear_all_cache() {
        try {
            $files = glob( $this->cache_dir . '*' );
            $deleted_count = 0;
            
            foreach ( $files as $file ) {
                if ( is_file( $file ) && basename( $file ) !== '.htaccess' ) {
                    if ( unlink( $file ) ) {
                        $deleted_count++;
                    }
                }
            }
            
            return $deleted_count > 0;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur vidage cache - ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Génère une clé de cache pour un type de sitemap.
     *
     * @since    1.2.0
     * @param    string    $sitemap_type    Le type de sitemap.
     * @return   string                     La clé de cache.
     */
    private function generate_cache_key( $sitemap_type ) {
        $factors = array(
            $sitemap_type,
            get_site_url(),
            $this->version
        );
        
        // Ajouter des facteurs mobiles si activé
        if ( $this->cache_config['mobile_cache'] && wp_is_mobile() ) {
            $factors[] = 'mobile';
        }
        
        return md5( implode( '|', $factors ) );
    }

    /**
     * Compte les URLs dans un sitemap XML.
     *
     * @since    1.2.0
     * @param    string    $xml_content    Le contenu XML.
     * @return   int                       Le nombre d'URLs.
     */
    private function count_urls_in_sitemap( $xml_content ) {
        return substr_count( $xml_content, '<url>' ) + substr_count( $xml_content, '<sitemap>' );
    }

    /**
     * Nettoie le cache si nécessaire.
     *
     * @since    1.2.0
     */
    private function cleanup_cache_if_needed() {
        $cache_size = $this->get_cache_size();
        $max_size = $this->cache_config['max_cache_size'] * 1024 * 1024; // Convertir en bytes
        
        if ( $cache_size > $max_size ) {
            $this->cleanup_old_cache_files();
        }
    }

    /**
     * Récupère la taille du cache.
     *
     * @since    1.2.0
     * @return   int    La taille en bytes.
     */
    public function get_cache_size() {
        $size = 0;
        $files = glob( $this->cache_dir . '*' );
        
        foreach ( $files as $file ) {
            if ( is_file( $file ) ) {
                $size += filesize( $file );
            }
        }
        
        return $size;
    }

    /**
     * Nettoie les anciens fichiers de cache.
     *
     * @since    1.2.0
     */
    private function cleanup_old_cache_files() {
        $files = glob( $this->cache_dir . '*.meta' );
        $files_with_time = array();
        
        foreach ( $files as $meta_file ) {
            $meta_content = file_get_contents( $meta_file );
            $metadata = json_decode( $meta_content, true );
            
            if ( $metadata && isset( $metadata['generated_at'] ) ) {
                $files_with_time[] = array(
                    'file' => $meta_file,
                    'time' => strtotime( $metadata['generated_at'] ),
                    'type' => $metadata['type'] ?? 'unknown'
                );
            }
        }
        
        // Trier par date (plus ancien en premier)
        usort( $files_with_time, function( $a, $b ) {
            return $a['time'] - $b['time'];
        } );
        
        // Supprimer les plus anciens jusqu'à atteindre 80% de la taille max
        $target_size = $this->cache_config['max_cache_size'] * 1024 * 1024 * 0.8;
        $current_size = $this->get_cache_size();
        
        foreach ( $files_with_time as $file_info ) {
            if ( $current_size <= $target_size ) {
                break;
            }
            
            $this->delete_cached_sitemap( $file_info['type'] );
            $current_size = $this->get_cache_size();
        }
    }

    /**
     * Récupère les statistiques du cache.
     *
     * @since    1.2.0
     * @return   array    Les statistiques.
     */
    public function get_cache_stats() {
        $files = glob( $this->cache_dir . '*.meta' );
        $stats = array(
            'total_files' => count( $files ),
            'total_size' => $this->get_cache_size(),
            'total_size_formatted' => size_format( $this->get_cache_size() ),
            'max_size_formatted' => size_format( $this->cache_config['max_cache_size'] * 1024 * 1024 ),
            'usage_percentage' => round( ( $this->get_cache_size() / ( $this->cache_config['max_cache_size'] * 1024 * 1024 ) ) * 100, 2 ),
            'sitemaps' => array(),
            'compression_enabled' => $this->cache_config['compression'],
            'cache_enabled' => $this->cache_config['enabled']
        );
        
        foreach ( $files as $meta_file ) {
            $meta_content = file_get_contents( $meta_file );
            $metadata = json_decode( $meta_content, true );
            
            if ( $metadata ) {
                $stats['sitemaps'][] = array(
                    'type' => $metadata['type'] ?? 'unknown',
                    'size' => $metadata['size'] ?? 0,
                    'size_formatted' => size_format( $metadata['size'] ?? 0 ),
                    'urls_count' => $metadata['urls_count'] ?? 0,
                    'generated_at' => $metadata['generated_at'] ?? '',
                    'expires_at' => $metadata['expires_at'] ?? '',
                    'compressed' => $metadata['compressed'] ?? false,
                    'compression_ratio' => $metadata['compression_ratio'] ?? 0
                );
            }
        }
        
        return $stats;
    }

    /**
     * Configure le cache.
     *
     * @since    1.2.0
     * @param    array    $config    La nouvelle configuration.
     * @return   bool                Succès de la configuration.
     */
    public function configure_cache( $config ) {
        try {
            $this->cache_config = array_merge( $this->cache_config, $config );
            
            // Valider la configuration
            if ( $this->cache_config['cache_duration'] < 300 ) {
                $this->cache_config['cache_duration'] = 300; // Minimum 5 minutes
            }
            
            if ( $this->cache_config['max_cache_size'] < 10 ) {
                $this->cache_config['max_cache_size'] = 10; // Minimum 10 MB
            }
            
            if ( $this->cache_config['gzip_level'] < 1 || $this->cache_config['gzip_level'] > 9 ) {
                $this->cache_config['gzip_level'] = 6; // Niveau par défaut
            }
            
            // Sauvegarder la configuration
            update_option( 'boss_seo_cache_config', $this->cache_config );
            
            // Si le cache est désactivé, le vider
            if ( ! $this->cache_config['enabled'] ) {
                $this->clear_all_cache();
            }
            
            return true;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur configuration cache - ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Récupère la configuration du cache.
     *
     * @since    1.2.0
     * @return   array    La configuration.
     */
    public function get_cache_config() {
        return $this->cache_config;
    }

    /**
     * Précharge les sitemaps populaires.
     *
     * @since    1.2.0
     * @return   array    Le résultat du préchargement.
     */
    public function preload_popular_sitemaps() {
        if ( ! $this->cache_config['preload_enabled'] ) {
            return array(
                'success' => false,
                'message' => __( 'Préchargement désactivé', 'boss-seo' )
            );
        }

        $popular_sitemaps = array( 'main', 'post', 'page', 'image' );
        $preloaded = 0;
        $errors = 0;
        
        foreach ( $popular_sitemaps as $sitemap_type ) {
            // Simuler la génération et mise en cache
            $content = $this->generate_sample_sitemap( $sitemap_type );
            
            if ( $this->cache_sitemap( $sitemap_type, $content ) ) {
                $preloaded++;
            } else {
                $errors++;
            }
        }
        
        return array(
            'success' => $errors === 0,
            'preloaded' => $preloaded,
            'errors' => $errors,
            'message' => sprintf( __( '%d sitemaps préchargés, %d erreurs', 'boss-seo' ), $preloaded, $errors )
        );
    }

    /**
     * Génère un sitemap d'exemple pour le préchargement.
     *
     * @since    1.2.0
     * @param    string    $type    Le type de sitemap.
     * @return   string             Le contenu XML.
     */
    private function generate_sample_sitemap( $type ) {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // Ajouter quelques URLs d'exemple
        for ( $i = 1; $i <= 10; $i++ ) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>" . get_site_url() . "/sample-{$type}-{$i}/</loc>\n";
            $xml .= "    <lastmod>" . date( 'Y-m-d\TH:i:s+00:00' ) . "</lastmod>\n";
            $xml .= "    <changefreq>weekly</changefreq>\n";
            $xml .= "    <priority>0.7</priority>\n";
            $xml .= "  </url>\n";
        }
        
        $xml .= '</urlset>';
        
        return $xml;
    }
}
?>
