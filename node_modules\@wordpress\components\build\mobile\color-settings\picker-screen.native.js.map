{"version": 3, "names": ["_native", "require", "_element", "_components", "_colorPicker", "PickerScreen", "route", "useRoute", "navigation", "useNavigation", "onShouldEnableInnerHandling", "shouldEnableBottomSheetMaxHeight", "onHandleClosingBottomSheet", "isBottomSheetContentScrolling", "shouldEnableBottomSheetScroll", "onHandleHardwareButtonPress", "useContext", "BottomSheetContext", "setColor", "currentValue", "isGradientColor", "params", "useMemo", "_react", "createElement", "ColorPicker", "activeColor", "onNavigationBack", "goBack", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/color-settings/picker-screen.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { useRoute, useNavigation } from '@react-navigation/native';\n\n/**\n * WordPress dependencies\n */\nimport { useContext, useMemo } from '@wordpress/element';\nimport { BottomSheetContext } from '@wordpress/components';\n\n/**\n * Internal dependencies\n */\nimport { ColorPicker } from '../../color-picker';\n\nconst PickerScreen = () => {\n\tconst route = useRoute();\n\tconst navigation = useNavigation();\n\tconst {\n\t\tonShouldEnableInnerHandling,\n\t\tshouldEnableBottomSheetMaxHeight,\n\t\tonHandleClosingBottomSheet,\n\t\tisBottomSheetContentScrolling,\n\t\tshouldEnableBottomSheetScroll,\n\t\tonHandleHardwareButtonPress,\n\t} = useContext( BottomSheetContext );\n\tconst { setColor, currentValue, isGradientColor } = route.params;\n\treturn useMemo( () => {\n\t\treturn (\n\t\t\t<ColorPicker\n\t\t\t\tonShouldEnableInnerHandling={ onShouldEnableInnerHandling }\n\t\t\t\tshouldEnableBottomSheetMaxHeight={\n\t\t\t\t\tshouldEnableBottomSheetMaxHeight\n\t\t\t\t}\n\t\t\t\tsetColor={ setColor }\n\t\t\t\tactiveColor={ currentValue }\n\t\t\t\tisGradientColor={ isGradientColor }\n\t\t\t\tonNavigationBack={ navigation.goBack }\n\t\t\t\tonHandleClosingBottomSheet={ onHandleClosingBottomSheet }\n\t\t\t\tisBottomSheetContentScrolling={ isBottomSheetContentScrolling }\n\t\t\t\tshouldEnableBottomSheetScroll={ shouldEnableBottomSheetScroll }\n\t\t\t\tonHandleHardwareButtonPress={ onHandleHardwareButtonPress }\n\t\t\t/>\n\t\t);\n\t\t// Disable reason: deferring this refactor to the native team.\n\t\t// see https://github.com/WordPress/gutenberg/pull/41166\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [\n\t\tsetColor,\n\t\tcurrentValue,\n\t\tisGradientColor,\n\t\tonShouldEnableInnerHandling,\n\t\tshouldEnableBottomSheetMaxHeight,\n\t\tonHandleClosingBottomSheet,\n\t\tisBottomSheetContentScrolling,\n\t\tshouldEnableBottomSheetScroll,\n\t\tonHandleHardwareButtonPress,\n\t] );\n};\n\nexport default PickerScreen;\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AAKA,IAAAG,YAAA,GAAAH,OAAA;AAdA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAGA,MAAMI,YAAY,GAAGA,CAAA,KAAM;EAC1B,MAAMC,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAClC,MAAM;IACLC,2BAA2B;IAC3BC,gCAAgC;IAChCC,0BAA0B;IAC1BC,6BAA6B;IAC7BC,6BAA6B;IAC7BC;EACD,CAAC,GAAG,IAAAC,mBAAU,EAAEC,8BAAmB,CAAC;EACpC,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGd,KAAK,CAACe,MAAM;EAChE,OAAO,IAAAC,gBAAO,EAAE,MAAM;IACrB,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACpB,YAAA,CAAAqB,WAAW;MACXf,2BAA2B,EAAGA,2BAA6B;MAC3DC,gCAAgC,EAC/BA,gCACA;MACDO,QAAQ,EAAGA,QAAU;MACrBQ,WAAW,EAAGP,YAAc;MAC5BC,eAAe,EAAGA,eAAiB;MACnCO,gBAAgB,EAAGnB,UAAU,CAACoB,MAAQ;MACtChB,0BAA0B,EAAGA,0BAA4B;MACzDC,6BAA6B,EAAGA,6BAA+B;MAC/DC,6BAA6B,EAAGA,6BAA+B;MAC/DC,2BAA2B,EAAGA;IAA6B,CAC3D,CAAC;IAEH;IACA;IACA;EACD,CAAC,EAAE,CACFG,QAAQ,EACRC,YAAY,EACZC,eAAe,EACfV,2BAA2B,EAC3BC,gCAAgC,EAChCC,0BAA0B,EAC1BC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,2BAA2B,CAC1B,CAAC;AACJ,CAAC;AAAC,IAAAc,QAAA,GAEaxB,YAAY;AAAAyB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}