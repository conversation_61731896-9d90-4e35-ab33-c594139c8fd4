{"version": 3, "names": ["_reactNative", "require", "_compose", "_styles", "_interopRequireDefault", "Heading", "children", "headingStyle", "usePreferredColorSchemeStyle", "styles", "heading", "_react", "createElement", "Text", "accessibilityRole", "style", "maxFontSizeMultiplier", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/nav-bar/heading.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Text } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\n\nfunction Heading( { children } ) {\n\tconst headingStyle = usePreferredColorSchemeStyle(\n\t\tstyles.heading,\n\t\tstyles[ 'heading-dark' ]\n\t);\n\n\treturn (\n\t\t<Text\n\t\t\taccessibilityRole=\"header\"\n\t\t\tstyle={ headingStyle }\n\t\t\tmaxFontSizeMultiplier={ 3 }\n\t\t>\n\t\t\t{ children }\n\t\t</Text>\n\t);\n}\n\nexport default Heading;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AAbA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAGA,SAASI,OAAOA,CAAE;EAAEC;AAAS,CAAC,EAAG;EAChC,MAAMC,YAAY,GAAG,IAAAC,qCAA4B,EAChDC,eAAM,CAACC,OAAO,EACdD,eAAM,CAAE,cAAc,CACvB,CAAC;EAED,OACC,IAAAE,MAAA,CAAAC,aAAA,EAACZ,YAAA,CAAAa,IAAI;IACJC,iBAAiB,EAAC,QAAQ;IAC1BC,KAAK,EAAGR,YAAc;IACtBS,qBAAqB,EAAG;EAAG,GAEzBV,QACG,CAAC;AAET;AAAC,IAAAW,QAAA,GAEcZ,OAAO;AAAAa,OAAA,CAAAC,OAAA,GAAAF,QAAA"}