import { __ } from '@wordpress/i18n';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardBody, 
  CardHeader, 
  CardFooter, 
  Dashicon,
  Panel,
  PanelBody,
  PanelRow,
  TabPanel
} from '@wordpress/components';
import { useState } from '@wordpress/element';

/**
 * Composant pour afficher un problème individuel
 */
const IssueItem = ({ issue, isExpanded, onToggleExpand, onMarkResolved }) => {
  // Fonction pour obtenir l'icône en fonction de la sévérité
  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return 'warning';
      case 'error':
        return 'dismiss';
      case 'warning':
        return 'flag';
      case 'improvement':
        return 'lightbulb';
      case 'success':
        return 'yes-alt';
      default:
        return 'info';
    }
  };

  // Fonction pour obtenir la classe de couleur en fonction de la sévérité
  const getSeverityColorClass = (severity) => {
    switch (severity) {
      case 'critical':
        return 'boss-text-red-600';
      case 'error':
        return 'boss-text-orange-600';
      case 'warning':
        return 'boss-text-yellow-600';
      case 'improvement':
        return 'boss-text-blue-600';
      case 'success':
        return 'boss-text-green-600';
      default:
        return 'boss-text-gray-600';
    }
  };

  // Fonction pour obtenir la classe de fond en fonction de la sévérité
  const getSeverityBgClass = (severity) => {
    switch (severity) {
      case 'critical':
        return 'boss-bg-red-50';
      case 'error':
        return 'boss-bg-orange-50';
      case 'warning':
        return 'boss-bg-yellow-50';
      case 'improvement':
        return 'boss-bg-blue-50';
      case 'success':
        return 'boss-bg-green-50';
      default:
        return 'boss-bg-gray-50';
    }
  };

  // Fonction pour obtenir le libellé de la sévérité
  const getSeverityLabel = (severity) => {
    switch (severity) {
      case 'critical':
        return __('Critique', 'boss-seo');
      case 'error':
        return __('Erreur', 'boss-seo');
      case 'warning':
        return __('Avertissement', 'boss-seo');
      case 'improvement':
        return __('Amélioration', 'boss-seo');
      case 'success':
        return __('Réussite', 'boss-seo');
      default:
        return severity;
    }
  };

  // Fonction pour obtenir le libellé de la catégorie
  const getCategoryLabel = (category) => {
    switch (category) {
      case 'performance':
        return __('Performance', 'boss-seo');
      case 'seo':
        return __('SEO', 'boss-seo');
      case 'accessibility':
        return __('Accessibilité', 'boss-seo');
      case 'bestPractices':
        return __('Bonnes pratiques', 'boss-seo');
      case 'security':
        return __('Sécurité', 'boss-seo');
      default:
        return category;
    }
  };

  return (
    <div 
      id={`issue-${issue.id}`}
      className={`boss-border boss-rounded-lg boss-mb-4 boss-overflow-hidden ${
        issue.status === 'resolved' 
          ? 'boss-border-green-200 boss-bg-green-50 boss-opacity-70' 
          : 'boss-border-gray-200'
      }`}
    >
      {/* En-tête du problème (toujours visible) */}
      <div 
        className={`boss-p-4 boss-flex boss-justify-between boss-items-center boss-cursor-pointer ${
          isExpanded ? `${getSeverityBgClass(issue.severity)} boss-border-b boss-border-gray-200` : ''
        }`}
        onClick={() => onToggleExpand(issue.id)}
      >
        <div className="boss-flex boss-items-center boss-flex-1">
          <div className={`boss-w-8 boss-h-8 boss-rounded-full boss-flex boss-items-center boss-justify-center boss-mr-3 ${
            issue.severity === 'critical' ? 'boss-bg-red-100' :
            issue.severity === 'error' ? 'boss-bg-orange-100' :
            issue.severity === 'warning' ? 'boss-bg-yellow-100' :
            issue.severity === 'improvement' ? 'boss-bg-blue-100' :
            'boss-bg-green-100'
          }`}>
            <Dashicon 
              icon={getSeverityIcon(issue.severity)} 
              className={getSeverityColorClass(issue.severity)} 
            />
          </div>
          <div className="boss-flex-1">
            <h4 className={`boss-font-medium ${
              issue.status === 'resolved' ? 'boss-line-through boss-text-boss-gray' : 'boss-text-boss-dark'
            }`}>
              {issue.title}
            </h4>
            <div className="boss-flex boss-items-center boss-mt-1">
              <span className={`boss-text-xs boss-font-medium boss-px-2 boss-py-0.5 boss-rounded-full boss-mr-2 ${
                getSeverityBgClass(issue.severity)} ${getSeverityColorClass(issue.severity)
              }`}>
                {getSeverityLabel(issue.severity)}
              </span>
              {issue.category && (
                <span className="boss-text-xs boss-text-boss-gray">
                  {getCategoryLabel(issue.category)}
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="boss-flex boss-items-center">
          {issue.status === 'resolved' ? (
            <span className="boss-text-xs boss-font-medium boss-text-green-600 boss-bg-green-100 boss-px-2 boss-py-0.5 boss-rounded-full boss-mr-2">
              {__('Résolu', 'boss-seo')}
            </span>
          ) : (
            <Button
              isSmall
              isSecondary
              className="boss-mr-2"
              onClick={(e) => {
                e.stopPropagation();
                onMarkResolved(issue.id, issue.severity === 'success' ? 'successes' : 
                  issue.severity === 'critical' ? 'critical' :
                  issue.severity === 'error' ? 'errors' :
                  issue.severity === 'warning' ? 'warnings' :
                  'improvements'
                );
              }}
            >
              {__('Marquer comme résolu', 'boss-seo')}
            </Button>
          )}
          <Dashicon 
            icon={isExpanded ? 'arrow-up-alt2' : 'arrow-down-alt2'} 
            className="boss-text-boss-gray" 
          />
        </div>
      </div>
      
      {/* Contenu détaillé (visible uniquement si développé) */}
      {isExpanded && (
        <div className="boss-p-4">
          {/* Description */}
          <div className="boss-mb-4">
            <h5 className="boss-font-medium boss-text-boss-dark boss-mb-2">
              {__('Description', 'boss-seo')}
            </h5>
            <p className="boss-text-boss-gray">
              {issue.description}
            </p>
          </div>
          
          {/* Impact SEO */}
          {issue.impact && (
            <div className="boss-mb-4">
              <h5 className="boss-font-medium boss-text-boss-dark boss-mb-2">
                {__('Impact SEO', 'boss-seo')}
              </h5>
              <p className="boss-text-boss-gray">
                {issue.impact}
              </p>
            </div>
          )}
          
          {/* Solution */}
          {issue.solution && (
            <div className="boss-mb-4">
              <h5 className="boss-font-medium boss-text-boss-dark boss-mb-2">
                {__('Comment corriger', 'boss-seo')}
              </h5>
              <div className="boss-bg-gray-50 boss-p-3 boss-rounded-lg boss-text-boss-gray boss-text-sm">
                {issue.solution}
              </div>
            </div>
          )}
          
          {/* Actions */}
          <div className="boss-flex boss-justify-between boss-mt-4">
            <div>
              {issue.url && (
                <Button
                  isSecondary
                  isSmall
                  href={issue.url}
                  target="_blank"
                  className="boss-mr-2"
                >
                  <Dashicon icon="external" className="boss-mr-1" />
                  {__('En savoir plus', 'boss-seo')}
                </Button>
              )}
            </div>
            
            {issue.status !== 'resolved' && (
              <Button
                isPrimary
                isSmall
                onClick={() => onMarkResolved(issue.id, issue.severity === 'success' ? 'successes' : 
                  issue.severity === 'critical' ? 'critical' :
                  issue.severity === 'error' ? 'errors' :
                  issue.severity === 'warning' ? 'warnings' :
                  'improvements'
                )}
              >
                <Dashicon icon="yes-alt" className="boss-mr-1" />
                {__('Marquer comme résolu', 'boss-seo')}
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Composant pour la section des problèmes détectés
 */
const IssuesSection = ({ issues, expandedIssues, onToggleExpand, onMarkResolved }) => {
  const [activeCategory, setActiveCategory] = useState('all');
  
  // Filtrer les problèmes en fonction de la catégorie active
  const getFilteredIssues = (issueType) => {
    if (!issues || !issues[issueType]) return [];
    
    if (activeCategory === 'all') {
      return issues[issueType];
    }
    
    return issues[issueType].filter(issue => issue.category === activeCategory);
  };
  
  // Compter le nombre total de problèmes
  const getTotalIssuesCount = () => {
    return (
      issues.critical.length +
      issues.errors.length +
      issues.warnings.length +
      issues.improvements.length +
      issues.successes.length
    );
  };
  
  // Compter le nombre de problèmes résolus
  const getResolvedIssuesCount = () => {
    return (
      issues.critical.filter(issue => issue.status === 'resolved').length +
      issues.errors.filter(issue => issue.status === 'resolved').length +
      issues.warnings.filter(issue => issue.status === 'resolved').length +
      issues.improvements.filter(issue => issue.status === 'resolved').length
    );
  };

  return (
    <div>
      {/* Résumé des problèmes */}
      <Card className="boss-mb-6">
        <CardBody>
          <div className="boss-flex boss-flex-wrap boss-justify-between boss-items-center boss-gap-4">
            <div>
              <h3 className="boss-text-xl boss-font-bold boss-text-boss-dark boss-mb-1">
                {__('Problèmes détectés', 'boss-seo')}
              </h3>
              <p className="boss-text-boss-gray">
                {getResolvedIssuesCount()} {__('sur', 'boss-seo')} {getTotalIssuesCount()} {__('problèmes résolus', 'boss-seo')}
              </p>
            </div>
            
            <div className="boss-flex boss-flex-wrap boss-gap-2">
              <Button
                isSecondary
                className={`boss-flex boss-items-center ${activeCategory === 'all' ? 'boss-bg-gray-100' : ''}`}
                onClick={() => setActiveCategory('all')}
              >
                <Dashicon icon="list-view" className="boss-mr-1" />
                {__('Tous', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                className={`boss-flex boss-items-center ${activeCategory === 'performance' ? 'boss-bg-gray-100' : ''}`}
                onClick={() => setActiveCategory('performance')}
              >
                <Dashicon icon="dashboard" className="boss-mr-1" />
                {__('Performance', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                className={`boss-flex boss-items-center ${activeCategory === 'seo' ? 'boss-bg-gray-100' : ''}`}
                onClick={() => setActiveCategory('seo')}
              >
                <Dashicon icon="search" className="boss-mr-1" />
                {__('SEO', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                className={`boss-flex boss-items-center ${activeCategory === 'accessibility' ? 'boss-bg-gray-100' : ''}`}
                onClick={() => setActiveCategory('accessibility')}
              >
                <Dashicon icon="universal-access" className="boss-mr-1" />
                {__('Accessibilité', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                className={`boss-flex boss-items-center ${activeCategory === 'security' ? 'boss-bg-gray-100' : ''}`}
                onClick={() => setActiveCategory('security')}
              >
                <Dashicon icon="shield" className="boss-mr-1" />
                {__('Sécurité', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
      
      {/* Problèmes critiques */}
      {getFilteredIssues('critical').length > 0 && (
        <div className="boss-mb-8">
          <div className="boss-flex boss-items-center boss-mb-4">
            <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-red-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
              <Dashicon icon="warning" className="boss-text-red-600" />
            </div>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Problèmes critiques', 'boss-seo')} ({getFilteredIssues('critical').length})
            </h3>
          </div>
          
          {getFilteredIssues('critical').map(issue => (
            <IssueItem
              key={issue.id}
              issue={issue}
              isExpanded={expandedIssues[issue.id] || false}
              onToggleExpand={onToggleExpand}
              onMarkResolved={onMarkResolved}
            />
          ))}
        </div>
      )}
      
      {/* Erreurs */}
      {getFilteredIssues('errors').length > 0 && (
        <div className="boss-mb-8">
          <div className="boss-flex boss-items-center boss-mb-4">
            <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-orange-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
              <Dashicon icon="dismiss" className="boss-text-orange-600" />
            </div>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Erreurs', 'boss-seo')} ({getFilteredIssues('errors').length})
            </h3>
          </div>
          
          {getFilteredIssues('errors').map(issue => (
            <IssueItem
              key={issue.id}
              issue={issue}
              isExpanded={expandedIssues[issue.id] || false}
              onToggleExpand={onToggleExpand}
              onMarkResolved={onMarkResolved}
            />
          ))}
        </div>
      )}
      
      {/* Avertissements */}
      {getFilteredIssues('warnings').length > 0 && (
        <div className="boss-mb-8">
          <div className="boss-flex boss-items-center boss-mb-4">
            <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-yellow-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
              <Dashicon icon="flag" className="boss-text-yellow-600" />
            </div>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Avertissements', 'boss-seo')} ({getFilteredIssues('warnings').length})
            </h3>
          </div>
          
          {getFilteredIssues('warnings').map(issue => (
            <IssueItem
              key={issue.id}
              issue={issue}
              isExpanded={expandedIssues[issue.id] || false}
              onToggleExpand={onToggleExpand}
              onMarkResolved={onMarkResolved}
            />
          ))}
        </div>
      )}
      
      {/* Améliorations */}
      {getFilteredIssues('improvements').length > 0 && (
        <div className="boss-mb-8">
          <div className="boss-flex boss-items-center boss-mb-4">
            <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-blue-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
              <Dashicon icon="lightbulb" className="boss-text-blue-600" />
            </div>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Améliorations', 'boss-seo')} ({getFilteredIssues('improvements').length})
            </h3>
          </div>
          
          {getFilteredIssues('improvements').map(issue => (
            <IssueItem
              key={issue.id}
              issue={issue}
              isExpanded={expandedIssues[issue.id] || false}
              onToggleExpand={onToggleExpand}
              onMarkResolved={onMarkResolved}
            />
          ))}
        </div>
      )}
      
      {/* Réussites */}
      {getFilteredIssues('successes').length > 0 && (
        <div className="boss-mb-8">
          <div className="boss-flex boss-items-center boss-mb-4">
            <div className="boss-w-8 boss-h-8 boss-rounded-full boss-bg-green-100 boss-flex boss-items-center boss-justify-center boss-mr-3">
              <Dashicon icon="yes-alt" className="boss-text-green-600" />
            </div>
            <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Réussites', 'boss-seo')} ({getFilteredIssues('successes').length})
            </h3>
          </div>
          
          {getFilteredIssues('successes').map(issue => (
            <IssueItem
              key={issue.id}
              issue={issue}
              isExpanded={expandedIssues[issue.id] || false}
              onToggleExpand={onToggleExpand}
              onMarkResolved={onMarkResolved}
            />
          ))}
        </div>
      )}
      
      {/* Message si aucun problème n'est trouvé */}
      {activeCategory !== 'all' && 
       getFilteredIssues('critical').length === 0 && 
       getFilteredIssues('errors').length === 0 && 
       getFilteredIssues('warnings').length === 0 && 
       getFilteredIssues('improvements').length === 0 && 
       getFilteredIssues('successes').length === 0 && (
        <div className="boss-text-center boss-p-8 boss-bg-gray-50 boss-rounded-lg boss-text-boss-gray">
          <Dashicon icon="info" className="boss-text-3xl boss-mb-2" />
          <p>
            {__('Aucun problème trouvé dans cette catégorie.', 'boss-seo')}
          </p>
        </div>
      )}
    </div>
  );
};

export default IssuesSection;
