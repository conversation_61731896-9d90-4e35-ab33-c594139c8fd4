{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_reactNative", "_components", "_compose", "_icons", "_button", "_dropdown", "mergeProps", "defaultProps", "props", "mergedProps", "className", "classnames", "isFunction", "maybeFunc", "DropdownMenu", "children", "controls", "icon", "menu", "label", "popoverProps", "toggleProps", "length", "controlSets", "Array", "isArray", "mergedPopoverProps", "_react", "createElement", "default", "renderToggle", "isOpen", "onToggle", "mergedToggleProps", "onClick", "event", "renderContent", "onClose", "BottomSheet", "<PERSON><PERSON>ead<PERSON>", "isVisible", "PanelBody", "title", "style", "paddingLeft", "paddingRight", "flatMap", "controlSet", "indexOfSet", "map", "control", "indexOfControl", "Cell", "key", "join", "onPress", "editable", "leftAlign", "isSelected", "isActive", "separatorType", "Platform", "OS", "_default", "withPreferredColorScheme", "exports"], "sources": ["@wordpress/components/src/dropdown-menu/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport { Platform } from 'react-native';\n/**\n * WordPress dependencies\n */\nimport { BottomSheet, PanelBody } from '@wordpress/components';\nimport { withPreferredColorScheme } from '@wordpress/compose';\nimport { menu } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport Button from '../button';\nimport Dropdown from '../dropdown';\n\nfunction mergeProps( defaultProps = {}, props = {} ) {\n\tconst mergedProps = {\n\t\t...defaultProps,\n\t\t...props,\n\t};\n\n\tif ( props.className && defaultProps.className ) {\n\t\tmergedProps.className = classnames(\n\t\t\tprops.className,\n\t\t\tdefaultProps.className\n\t\t);\n\t}\n\n\treturn mergedProps;\n}\n\n/**\n * Whether the argument is a function.\n *\n * @param {*} maybeFunc The argument to check.\n * @return {boolean} True if the argument is a function, false otherwise.\n */\nfunction isFunction( maybeFunc ) {\n\treturn typeof maybeFunc === 'function';\n}\n\nfunction DropdownMenu( {\n\tchildren,\n\tclassName,\n\tcontrols,\n\ticon = menu,\n\tlabel,\n\tpopoverProps,\n\ttoggleProps,\n} ) {\n\tif ( ! controls?.length && ! isFunction( children ) ) {\n\t\treturn null;\n\t}\n\n\t// Normalize controls to nested array of objects (sets of controls)\n\tlet controlSets;\n\tif ( controls?.length ) {\n\t\tcontrolSets = controls;\n\t\tif ( ! Array.isArray( controlSets[ 0 ] ) ) {\n\t\t\tcontrolSets = [ controlSets ];\n\t\t}\n\t}\n\tconst mergedPopoverProps = mergeProps(\n\t\t{\n\t\t\tclassName: 'components-dropdown-menu__popover',\n\t\t},\n\t\tpopoverProps\n\t);\n\n\treturn (\n\t\t<Dropdown\n\t\t\tclassName={ classnames( 'components-dropdown-menu', className ) }\n\t\t\tpopoverProps={ mergedPopoverProps }\n\t\t\trenderToggle={ ( { isOpen, onToggle } ) => {\n\t\t\t\tconst mergedToggleProps = mergeProps(\n\t\t\t\t\t{\n\t\t\t\t\t\tclassName: classnames(\n\t\t\t\t\t\t\t'components-dropdown-menu__toggle',\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t'is-opened': isOpen,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t),\n\t\t\t\t\t},\n\t\t\t\t\ttoggleProps\n\t\t\t\t);\n\n\t\t\t\treturn (\n\t\t\t\t\t<Button\n\t\t\t\t\t\t{ ...mergedToggleProps }\n\t\t\t\t\t\ticon={ icon }\n\t\t\t\t\t\tonClick={ ( event ) => {\n\t\t\t\t\t\t\tonToggle( event );\n\t\t\t\t\t\t\tif ( mergedToggleProps.onClick ) {\n\t\t\t\t\t\t\t\tmergedToggleProps.onClick( event );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} }\n\t\t\t\t\t\taria-haspopup=\"true\"\n\t\t\t\t\t\taria-expanded={ isOpen }\n\t\t\t\t\t\tlabel={ label }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ mergedToggleProps.children }\n\t\t\t\t\t</Button>\n\t\t\t\t);\n\t\t\t} }\n\t\t\trenderContent={ ( { isOpen, onClose, ...props } ) => {\n\t\t\t\treturn (\n\t\t\t\t\t<BottomSheet\n\t\t\t\t\t\thideHeader={ true }\n\t\t\t\t\t\tisVisible={ isOpen }\n\t\t\t\t\t\tonClose={ onClose }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ isFunction( children ) ? children( props ) : null }\n\t\t\t\t\t\t<PanelBody\n\t\t\t\t\t\t\ttitle={ label }\n\t\t\t\t\t\t\tstyle={ { paddingLeft: 0, paddingRight: 0 } }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ controlSets?.flatMap(\n\t\t\t\t\t\t\t\t( controlSet, indexOfSet ) =>\n\t\t\t\t\t\t\t\t\tcontrolSet.map(\n\t\t\t\t\t\t\t\t\t\t( control, indexOfControl ) => (\n\t\t\t\t\t\t\t\t\t\t\t<BottomSheet.Cell\n\t\t\t\t\t\t\t\t\t\t\t\tkey={ [\n\t\t\t\t\t\t\t\t\t\t\t\t\tindexOfSet,\n\t\t\t\t\t\t\t\t\t\t\t\t\tindexOfControl,\n\t\t\t\t\t\t\t\t\t\t\t\t].join() }\n\t\t\t\t\t\t\t\t\t\t\t\tlabel={ control.title }\n\t\t\t\t\t\t\t\t\t\t\t\tonPress={ () => {\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClose();\n\t\t\t\t\t\t\t\t\t\t\t\t\tif ( control.onClick ) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcontrol.onClick();\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\t\teditable={ false }\n\t\t\t\t\t\t\t\t\t\t\t\ticon={ control.icon }\n\t\t\t\t\t\t\t\t\t\t\t\tleftAlign={ true }\n\t\t\t\t\t\t\t\t\t\t\t\tisSelected={ control.isActive }\n\t\t\t\t\t\t\t\t\t\t\t\tseparatorType={\n\t\t\t\t\t\t\t\t\t\t\t\t\tPlatform.OS === 'android'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? 'none'\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: 'leftMargin'\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</PanelBody>\n\t\t\t\t\t</BottomSheet>\n\t\t\t\t);\n\t\t\t} }\n\t\t/>\n\t);\n}\n\nexport default withPreferredColorScheme( DropdownMenu );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAIA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAKA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,SAAA,GAAAP,sBAAA,CAAAC,OAAA;AAhBA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;;AAIA,SAASO,UAAUA,CAAEC,YAAY,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,CAAC,EAAG;EACpD,MAAMC,WAAW,GAAG;IACnB,GAAGF,YAAY;IACf,GAAGC;EACJ,CAAC;EAED,IAAKA,KAAK,CAACE,SAAS,IAAIH,YAAY,CAACG,SAAS,EAAG;IAChDD,WAAW,CAACC,SAAS,GAAG,IAAAC,mBAAU,EACjCH,KAAK,CAACE,SAAS,EACfH,YAAY,CAACG,SACd,CAAC;EACF;EAEA,OAAOD,WAAW;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAAEC,SAAS,EAAG;EAChC,OAAO,OAAOA,SAAS,KAAK,UAAU;AACvC;AAEA,SAASC,YAAYA,CAAE;EACtBC,QAAQ;EACRL,SAAS;EACTM,QAAQ;EACRC,IAAI,GAAGC,WAAI;EACXC,KAAK;EACLC,YAAY;EACZC;AACD,CAAC,EAAG;EACH,IAAK,CAAEL,QAAQ,EAAEM,MAAM,IAAI,CAAEV,UAAU,CAAEG,QAAS,CAAC,EAAG;IACrD,OAAO,IAAI;EACZ;;EAEA;EACA,IAAIQ,WAAW;EACf,IAAKP,QAAQ,EAAEM,MAAM,EAAG;IACvBC,WAAW,GAAGP,QAAQ;IACtB,IAAK,CAAEQ,KAAK,CAACC,OAAO,CAAEF,WAAW,CAAE,CAAC,CAAG,CAAC,EAAG;MAC1CA,WAAW,GAAG,CAAEA,WAAW,CAAE;IAC9B;EACD;EACA,MAAMG,kBAAkB,GAAGpB,UAAU,CACpC;IACCI,SAAS,EAAE;EACZ,CAAC,EACDU,YACD,CAAC;EAED,OACC,IAAAO,MAAA,CAAAC,aAAA,EAACvB,SAAA,CAAAwB,OAAQ;IACRnB,SAAS,EAAG,IAAAC,mBAAU,EAAE,0BAA0B,EAAED,SAAU,CAAG;IACjEU,YAAY,EAAGM,kBAAoB;IACnCI,YAAY,EAAGA,CAAE;MAAEC,MAAM;MAAEC;IAAS,CAAC,KAAM;MAC1C,MAAMC,iBAAiB,GAAG3B,UAAU,CACnC;QACCI,SAAS,EAAE,IAAAC,mBAAU,EACpB,kCAAkC,EAClC;UACC,WAAW,EAAEoB;QACd,CACD;MACD,CAAC,EACDV,WACD,CAAC;MAED,OACC,IAAAM,MAAA,CAAAC,aAAA,EAACxB,OAAA,CAAAyB,OAAM;QAAA,GACDI,iBAAiB;QACtBhB,IAAI,EAAGA,IAAM;QACbiB,OAAO,EAAKC,KAAK,IAAM;UACtBH,QAAQ,CAAEG,KAAM,CAAC;UACjB,IAAKF,iBAAiB,CAACC,OAAO,EAAG;YAChCD,iBAAiB,CAACC,OAAO,CAAEC,KAAM,CAAC;UACnC;QACD,CAAG;QACH,iBAAc,MAAM;QACpB,iBAAgBJ,MAAQ;QACxBZ,KAAK,EAAGA;MAAO,GAEbc,iBAAiB,CAAClB,QACb,CAAC;IAEX,CAAG;IACHqB,aAAa,EAAGA,CAAE;MAAEL,MAAM;MAAEM,OAAO;MAAE,GAAG7B;IAAM,CAAC,KAAM;MACpD,OACC,IAAAmB,MAAA,CAAAC,aAAA,EAAC3B,WAAA,CAAAqC,WAAW;QACXC,UAAU,EAAG,IAAM;QACnBC,SAAS,EAAGT,MAAQ;QACpBM,OAAO,EAAGA;MAAS,GAEjBzB,UAAU,CAAEG,QAAS,CAAC,GAAGA,QAAQ,CAAEP,KAAM,CAAC,GAAG,IAAI,EACnD,IAAAmB,MAAA,CAAAC,aAAA,EAAC3B,WAAA,CAAAwC,SAAS;QACTC,KAAK,EAAGvB,KAAO;QACfwB,KAAK,EAAG;UAAEC,WAAW,EAAE,CAAC;UAAEC,YAAY,EAAE;QAAE;MAAG,GAE3CtB,WAAW,EAAEuB,OAAO,CACrB,CAAEC,UAAU,EAAEC,UAAU,KACvBD,UAAU,CAACE,GAAG,CACb,CAAEC,OAAO,EAAEC,cAAc,KACxB,IAAAxB,MAAA,CAAAC,aAAA,EAAC3B,WAAA,CAAAqC,WAAW,CAACc,IAAI;QAChBC,GAAG,EAAG,CACLL,UAAU,EACVG,cAAc,CACd,CAACG,IAAI,CAAC,CAAG;QACVnC,KAAK,EAAG+B,OAAO,CAACR,KAAO;QACvBa,OAAO,EAAGA,CAAA,KAAM;UACflB,OAAO,CAAC,CAAC;UACT,IAAKa,OAAO,CAAChB,OAAO,EAAG;YACtBgB,OAAO,CAAChB,OAAO,CAAC,CAAC;UAClB;QACD,CAAG;QACHsB,QAAQ,EAAG,KAAO;QAClBvC,IAAI,EAAGiC,OAAO,CAACjC,IAAM;QACrBwC,SAAS,EAAG,IAAM;QAClBC,UAAU,EAAGR,OAAO,CAACS,QAAU;QAC/BC,aAAa,EACZC,qBAAQ,CAACC,EAAE,KAAK,SAAS,GACtB,MAAM,GACN;MACH,CACD,CAEH,CACF,CACU,CACC,CAAC;IAEhB;EAAG,CACH,CAAC;AAEJ;AAAC,IAAAC,QAAA,GAEc,IAAAC,iCAAwB,EAAElD,YAAa,CAAC;AAAAmD,OAAA,CAAApC,OAAA,GAAAkC,QAAA"}