"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "HStack", {
  enumerable: true,
  get: function () {
    return _component.default;
  }
});
Object.defineProperty(exports, "useHStack", {
  enumerable: true,
  get: function () {
    return _hook.useHStack;
  }
});
var _component = _interopRequireDefault(require("./component"));
var _hook = require("./hook");
//# sourceMappingURL=index.js.map