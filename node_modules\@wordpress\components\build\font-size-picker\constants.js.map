{"version": 3, "names": ["_i18n", "require", "T_SHIRT_ABBREVIATIONS", "__", "exports", "T_SHIRT_NAMES"], "sources": ["@wordpress/components/src/font-size-picker/constants.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * List of T-shirt abbreviations.\n *\n * When there are 5 font sizes or fewer, we assume that the font sizes are\n * ordered by size and show T-shirt labels.\n */\nexport const T_SHIRT_ABBREVIATIONS = [\n\t/* translators: S stands for 'small' and is a size label. */\n\t__( 'S' ),\n\t/* translators: M stands for 'medium' and is a size label. */\n\t__( 'M' ),\n\t/* translators: L stands for 'large' and is a size label. */\n\t__( 'L' ),\n\t/* translators: XL stands for 'extra large' and is a size label. */\n\t__( 'XL' ),\n\t/* translators: XXL stands for 'extra extra large' and is a size label. */\n\t__( 'XXL' ),\n];\n\n/**\n * List of T-shirt names.\n *\n * When there are 5 font sizes or fewer, we assume that the font sizes are\n * ordered by size and show T-shirt labels.\n */\nexport const T_SHIRT_NAMES = [\n\t__( 'Small' ),\n\t__( 'Medium' ),\n\t__( 'Large' ),\n\t__( 'Extra Large' ),\n\t__( 'Extra Extra Large' ),\n];\n"], "mappings": ";;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,qBAAqB,GAAG,CACpC;AACA,IAAAC,QAAE,EAAE,GAAI,CAAC,EACT;AACA,IAAAA,QAAE,EAAE,GAAI,CAAC,EACT;AACA,IAAAA,QAAE,EAAE,GAAI,CAAC,EACT;AACA,IAAAA,QAAE,EAAE,IAAK,CAAC,EACV;AACA,IAAAA,QAAE,EAAE,KAAM,CAAC,CACX;;AAED;AACA;AACA;AACA;AACA;AACA;AALAC,OAAA,CAAAF,qBAAA,GAAAA,qBAAA;AAMO,MAAMG,aAAa,GAAG,CAC5B,IAAAF,QAAE,EAAE,OAAQ,CAAC,EACb,IAAAA,QAAE,EAAE,QAAS,CAAC,EACd,IAAAA,QAAE,EAAE,OAAQ,CAAC,EACb,IAAAA,QAAE,EAAE,aAAc,CAAC,EACnB,IAAAA,QAAE,EAAE,mBAAoB,CAAC,CACzB;AAACC,OAAA,CAAAC,aAAA,GAAAA,aAAA"}