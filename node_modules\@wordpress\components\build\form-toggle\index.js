"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FormToggle = FormToggle;
exports.noop = exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
/**
 * External dependencies
 */

const noop = () => {};

/**
 * FormToggle switches a single setting on or off.
 *
 * ```jsx
 * import { FormToggle } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyFormToggle = () => {
 *   const [ isChecked, setChecked ] = useState( true );
 *
 *   return (
 *     <FormToggle
 *       checked={ isChecked }
 *       onChange={ () => setChecked( ( state ) => ! state ) }
 *     />
 *   );
 * };
 * ```
 */
exports.noop = noop;
function FormToggle(props) {
  const {
    className,
    checked,
    id,
    disabled,
    onChange = noop,
    ...additionalProps
  } = props;
  const wrapperClasses = (0, _classnames.default)('components-form-toggle', className, {
    'is-checked': checked,
    'is-disabled': disabled
  });
  return (0, _react.createElement)("span", {
    className: wrapperClasses
  }, (0, _react.createElement)("input", {
    className: "components-form-toggle__input",
    id: id,
    type: "checkbox",
    checked: checked,
    onChange: onChange,
    disabled: disabled,
    ...additionalProps
  }), (0, _react.createElement)("span", {
    className: "components-form-toggle__track"
  }), (0, _react.createElement)("span", {
    className: "components-form-toggle__thumb"
  }));
}
var _default = FormToggle;
exports.default = _default;
//# sourceMappingURL=index.js.map