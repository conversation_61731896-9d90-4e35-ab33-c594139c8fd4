{"version": 3, "names": ["_i18n", "require", "_icons", "_components", "_cell", "_interopRequireDefault", "_styles", "BottomSheetColorCell", "props", "color", "withColorIndicator", "disabled", "cellProps", "_react", "createElement", "default", "accessibilityRole", "accessibilityHint", "__", "editable", "value", "ColorIndicator", "style", "styles", "colorCircle", "Icon", "icon", "chevronRight"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/color-cell.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { Icon, chevronRight } from '@wordpress/icons';\nimport { ColorIndicator } from '@wordpress/components';\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport styles from './styles.scss';\n\nexport default function BottomSheetColorCell( props ) {\n\tconst { color, withColorIndicator = true, disabled, ...cellProps } = props;\n\n\treturn (\n\t\t<Cell\n\t\t\t{ ...cellProps }\n\t\t\taccessibilityRole={ 'button' }\n\t\t\taccessibilityHint={\n\t\t\t\t/* translators: accessibility text (hint for moving to color settings) */\n\t\t\t\t__( 'Double tap to go to color settings' )\n\t\t\t}\n\t\t\teditable={ false }\n\t\t\tdisabled={ disabled }\n\t\t\tvalue={ withColorIndicator && ! color && __( 'Default' ) }\n\t\t>\n\t\t\t{ withColorIndicator && color && (\n\t\t\t\t<ColorIndicator color={ color } style={ styles.colorCircle } />\n\t\t\t) }\n\t\t\t{ disabled ? null : <Icon icon={ chevronRight }></Icon> }\n\t\t</Cell>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AAIA,IAAAG,KAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAD,sBAAA,CAAAJ,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAIe,SAASM,oBAAoBA,CAAEC,KAAK,EAAG;EACrD,MAAM;IAAEC,KAAK;IAAEC,kBAAkB,GAAG,IAAI;IAAEC,QAAQ;IAAE,GAAGC;EAAU,CAAC,GAAGJ,KAAK;EAE1E,OACC,IAAAK,MAAA,CAAAC,aAAA,EAACV,KAAA,CAAAW,OAAI;IAAA,GACCH,SAAS;IACdI,iBAAiB,EAAG,QAAU;IAC9BC,iBAAiB,EAChB;IACA,IAAAC,QAAE,EAAE,oCAAqC,CACzC;IACDC,QAAQ,EAAG,KAAO;IAClBR,QAAQ,EAAGA,QAAU;IACrBS,KAAK,EAAGV,kBAAkB,IAAI,CAAED,KAAK,IAAI,IAAAS,QAAE,EAAE,SAAU;EAAG,GAExDR,kBAAkB,IAAID,KAAK,IAC5B,IAAAI,MAAA,CAAAC,aAAA,EAACX,WAAA,CAAAkB,cAAc;IAACZ,KAAK,EAAGA,KAAO;IAACa,KAAK,EAAGC,eAAM,CAACC;EAAa,CAAE,CAC9D,EACCb,QAAQ,GAAG,IAAI,GAAG,IAAAE,MAAA,CAAAC,aAAA,EAACZ,MAAA,CAAAuB,IAAI;IAACC,IAAI,EAAGC;EAAc,CAAO,CACjD,CAAC;AAET"}