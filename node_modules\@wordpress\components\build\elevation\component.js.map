{"version": 3, "names": ["_context", "require", "_view", "_hook", "UnconnectedElevation", "props", "forwardedRef", "elevationProps", "useElevation", "_react", "createElement", "View", "ref", "Elevation", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/elevation/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnect } from '../context';\nimport { View } from '../view';\nimport { useElevation } from './hook';\nimport type { ElevationProps } from './types';\n\nfunction UnconnectedElevation(\n\tprops: WordPressComponentProps< ElevationProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst elevationProps = useElevation( props );\n\n\treturn <View { ...elevationProps } ref={ forwardedRef } />;\n}\n\n/**\n * `Elevation` is a core component that renders shadow, using the component\n * system's shadow system.\n *\n * The shadow effect is generated using the `value` prop.\n *\n * ```jsx\n * import {\n *\t__experimentalElevation as Elevation,\n *\t__experimentalSurface as Surface,\n *\t__experimentalText as Text,\n * } from '@wordpress/components';\n *\n * function Example() {\n *   return (\n *     <Surface>\n *       <Text>Code is Poetry</Text>\n *       <Elevation value={ 5 } />\n *     </Surface>\n *   );\n * }\n * ```\n */\nexport const Elevation = contextConnect( UnconnectedElevation, 'Elevation' );\n\nexport default Elevation;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,oBAAoBA,CAC5BC,KAAuD,EACvDC,YAAiC,EAChC;EACD,MAAMC,cAAc,GAAG,IAAAC,kBAAY,EAAEH,KAAM,CAAC;EAE5C,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,cAAc;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,SAAS,GAAG,IAAAC,uBAAc,EAAEV,oBAAoB,EAAE,WAAY,CAAC;AAACW,OAAA,CAAAF,SAAA,GAAAA,SAAA;AAAA,IAAAG,QAAA,GAE9DH,SAAS;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}