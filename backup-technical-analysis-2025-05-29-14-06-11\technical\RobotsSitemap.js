import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TabPanel,
  TextareaControl,
  ToggleControl,
  SelectControl,
  CheckboxControl,
  RangeControl,
  Notice,
  Spinner
} from '@wordpress/components';

// Importer le service
import RobotsSitemapService from '../../services/RobotsSitemapService';

// Composant pour l'éditeur de robots.txt
const RobotsEditor = ({ robotsContent, setRobotsContent, onSave, isSaving }) => {
  const [validationError, setValidationError] = useState(null);

  // Fonction pour valider le contenu du robots.txt
  const validateRobots = () => {
    // Vérifier si le contenu contient des directives de base
    if (!robotsContent.includes('User-agent:')) {
      setValidationError(__('Le fichier robots.txt doit contenir au moins une directive User-agent.', 'boss-seo'));
      return false;
    }

    // Vérifier la syntaxe de base
    const lines = robotsContent.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line && !line.startsWith('#') && !line.includes(':')) {
        setValidationError(__(`Erreur de syntaxe à la ligne ${i + 1}: "${line}"`, 'boss-seo'));
        return false;
      }
    }

    setValidationError(null);
    return true;
  };

  // Fonction pour gérer la sauvegarde
  const handleSave = () => {
    if (validateRobots()) {
      onSave(robotsContent);
    }
  };

  // Fonction pour ajouter des règles communes
  const addCommonRules = (type) => {
    let newContent = robotsContent;

    switch (type) {
      case 'wordpress-core':
        newContent += `\n\n# WordPress core files
User-agent: *
Disallow: /wp-admin/
Disallow: /wp-includes/
Disallow: /wp-content/plugins/
Disallow: /wp-content/themes/
Disallow: /wp-login.php
Disallow: /xmlrpc.php`;
        break;
      case 'media':
        newContent += `\n\n# Media files
User-agent: *
Allow: /wp-content/uploads/*.jpg
Allow: /wp-content/uploads/*.jpeg
Allow: /wp-content/uploads/*.png
Allow: /wp-content/uploads/*.gif
Allow: /wp-content/uploads/*.webp
Allow: /wp-content/uploads/*.svg`;
        break;
      case 'sitemap':
        newContent += `\n\n# Sitemap
Sitemap: ${window.location.origin}/sitemap.xml`;
        break;
      default:
        break;
    }

    setRobotsContent(newContent);
  };

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Éditeur robots.txt', 'boss-seo')}
          </h2>
          <div className="boss-flex boss-space-x-2">
            <Button
              isSecondary
              onClick={() => addCommonRules('wordpress-core')}
            >
              {__('+ WordPress Core', 'boss-seo')}
            </Button>
            <Button
              isSecondary
              onClick={() => addCommonRules('media')}
            >
              {__('+ Médias', 'boss-seo')}
            </Button>
            <Button
              isSecondary
              onClick={() => addCommonRules('sitemap')}
            >
              {__('+ Sitemap', 'boss-seo')}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <TextareaControl
          value={robotsContent}
          onChange={setRobotsContent}
          className="boss-font-mono boss-text-sm"
          rows={15}
        />

        {validationError && (
          <Notice status="error" isDismissible={false} className="boss-mt-4">
            {validationError}
          </Notice>
        )}

        <div className="boss-mt-4 boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-text-sm boss-text-boss-gray">
          <h3 className="boss-font-medium boss-text-boss-dark boss-mb-2">
            {__('Conseils pour robots.txt', 'boss-seo')}
          </h3>
          <ul className="boss-list-disc boss-pl-5 boss-space-y-1">
            <li>{__('Utilisez "User-agent: *" pour cibler tous les robots.', 'boss-seo')}</li>
            <li>{__('Utilisez "Disallow: /chemin/" pour bloquer l\'accès à un répertoire.', 'boss-seo')}</li>
            <li>{__('Utilisez "Allow: /chemin/" pour autoriser explicitement l\'accès.', 'boss-seo')}</li>
            <li>{__('Ajoutez "Sitemap: URL" pour indiquer l\'emplacement de votre sitemap.', 'boss-seo')}</li>
            <li>{__('Les commentaires commencent par "#".', 'boss-seo')}</li>
          </ul>
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-end">
          <Button
            isPrimary
            onClick={handleSave}
            isBusy={isSaving}
            disabled={isSaving}
          >
            {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer robots.txt', 'boss-seo')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

// Composant pour la configuration du sitemap
const SitemapConfig = ({
  sitemapSettings,
  setSitemapSettings,
  onSave,
  isSaving,
  contentTypes,
  taxonomies
}) => {
  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Configuration du sitemap', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-space-y-6">
          <div>
            <ToggleControl
              label={__('Activer le sitemap XML', 'boss-seo')}
              checked={sitemapSettings.enabled}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, enabled: value })}
              className="boss-mb-4"
            />

            {sitemapSettings.enabled && (
              <>
                <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg boss-mb-4">
                  <div className="boss-flex boss-items-start">
                    <div className="boss-flex-shrink-0 boss-mr-3">
                      <span className="dashicons dashicons-info boss-text-blue-600"></span>
                    </div>
                    <div>
                      <p className="boss-text-sm boss-text-boss-dark">
                        {__('Votre sitemap XML est disponible à l\'adresse:', 'boss-seo')}
                      </p>
                      <a
                        href={`${window.location.origin}/sitemap.xml`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="boss-text-sm boss-text-blue-600 boss-font-medium boss-underline"
                      >
                        {`${window.location.origin}/sitemap.xml`}
                      </a>
                    </div>
                  </div>
                </div>

                <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
                  <div>
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                      {__('Types de contenu', 'boss-seo')}
                    </h3>

                    <div className="boss-space-y-3">
                      {contentTypes.map((type) => (
                        <CheckboxControl
                          key={type.value}
                          label={type.label}
                          checked={sitemapSettings.includedPostTypes.includes(type.value)}
                          onChange={(checked) => {
                            const updatedTypes = checked
                              ? [...sitemapSettings.includedPostTypes, type.value]
                              : sitemapSettings.includedPostTypes.filter(t => t !== type.value);

                            setSitemapSettings({
                              ...sitemapSettings,
                              includedPostTypes: updatedTypes
                            });
                          }}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                      {__('Taxonomies', 'boss-seo')}
                    </h3>

                    <div className="boss-space-y-3">
                      {taxonomies.map((tax) => (
                        <CheckboxControl
                          key={tax.value}
                          label={tax.label}
                          checked={sitemapSettings.includedTaxonomies.includes(tax.value)}
                          onChange={(checked) => {
                            const updatedTaxonomies = checked
                              ? [...sitemapSettings.includedTaxonomies, tax.value]
                              : sitemapSettings.includedTaxonomies.filter(t => t !== tax.value);

                            setSitemapSettings({
                              ...sitemapSettings,
                              includedTaxonomies: updatedTaxonomies
                            });
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                <div className="boss-mt-6">
                  <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                    {__('Options avancées', 'boss-seo')}
                  </h3>

                  <div className="boss-space-y-4">
                    <SelectControl
                      label={__('Fréquence de mise à jour par défaut', 'boss-seo')}
                      value={sitemapSettings.defaultChangeFreq}
                      options={[
                        { label: __('Toujours', 'boss-seo'), value: 'always' },
                        { label: __('Horaire', 'boss-seo'), value: 'hourly' },
                        { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                        { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                        { label: __('Mensuelle', 'boss-seo'), value: 'monthly' },
                        { label: __('Annuelle', 'boss-seo'), value: 'yearly' },
                        { label: __('Jamais', 'boss-seo'), value: 'never' }
                      ]}
                      onChange={(value) => setSitemapSettings({ ...sitemapSettings, defaultChangeFreq: value })}
                    />

                    <RangeControl
                      label={__('Priorité par défaut', 'boss-seo')}
                      value={sitemapSettings.defaultPriority}
                      onChange={(value) => setSitemapSettings({ ...sitemapSettings, defaultPriority: value })}
                      min={0.1}
                      max={1.0}
                      step={0.1}
                    />

                    <ToggleControl
                      label={__('Inclure les images', 'boss-seo')}
                      checked={sitemapSettings.includeImages}
                      onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeImages: value })}
                    />

                    <ToggleControl
                      label={__('Inclure la dernière date de modification', 'boss-seo')}
                      checked={sitemapSettings.includeLastMod}
                      onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeLastMod: value })}
                    />

                    <TextareaControl
                      label={__('URLs à exclure', 'boss-seo')}
                      help={__('Une URL par ligne. Utilisez des chemins relatifs (ex: /page-a-exclure/).', 'boss-seo')}
                      value={sitemapSettings.excludedUrls}
                      onChange={(value) => setSitemapSettings({ ...sitemapSettings, excludedUrls: value })}
                      rows={4}
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-end">
          <Button
            isPrimary
            onClick={() => onSave(sitemapSettings)}
            isBusy={isSaving}
            disabled={isSaving}
          >
            {isSaving ? __('Enregistrement...', 'boss-seo') : __('Enregistrer la configuration', 'boss-seo')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

// Composant pour les statistiques d'indexation
const IndexationStats = ({ stats, isLoading }) => {
  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Statistiques d\'indexation', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        {isLoading ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : (
          <div>
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-3 boss-gap-6 boss-mb-6">
              <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-center">
                <div className="boss-text-3xl boss-font-bold boss-text-boss-primary boss-mb-1">
                  {stats.indexedPages}
                </div>
                <div className="boss-text-sm boss-text-boss-gray">
                  {__('Pages indexées', 'boss-seo')}
                </div>
              </div>

              <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-center">
                <div className="boss-text-3xl boss-font-bold boss-text-boss-success boss-mb-1">
                  {stats.crawlRate}
                </div>
                <div className="boss-text-sm boss-text-boss-gray">
                  {__('Pages/jour', 'boss-seo')}
                </div>
              </div>

              <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-center">
                <div className="boss-text-3xl boss-font-bold boss-text-boss-dark boss-mb-1">
                  {stats.lastCrawl}
                </div>
                <div className="boss-text-sm boss-text-boss-gray">
                  {__('Dernière exploration', 'boss-seo')}
                </div>
              </div>
            </div>

            <div className="boss-mb-6">
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                {__('Problèmes d\'indexation', 'boss-seo')}
              </h3>

              {stats.indexationIssues.length === 0 ? (
                <div className="boss-p-4 boss-bg-green-50 boss-rounded-lg boss-text-green-800">
                  {__('Aucun problème d\'indexation détecté.', 'boss-seo')}
                </div>
              ) : (
                <div className="boss-space-y-3">
                  {stats.indexationIssues.map((issue, index) => (
                    <div key={index} className="boss-p-4 boss-bg-yellow-50 boss-rounded-lg">
                      <div className="boss-flex boss-items-start">
                        <div className="boss-flex-shrink-0 boss-mr-3">
                          <span className="dashicons dashicons-warning boss-text-yellow-600"></span>
                        </div>
                        <div>
                          <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">{issue.title}</h4>
                          <p className="boss-text-sm boss-text-boss-gray">{issue.description}</p>
                          {issue.affectedUrls && (
                            <div className="boss-mt-2">
                              <p className="boss-text-sm boss-font-medium boss-text-boss-dark">{__('URLs affectées:', 'boss-seo')}</p>
                              <ul className="boss-list-disc boss-pl-5 boss-text-sm boss-text-boss-gray">
                                {issue.affectedUrls.map((url, urlIndex) => (
                                  <li key={urlIndex}>{url}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div>
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                {__('Conseils d\'optimisation', 'boss-seo')}
              </h3>

              <div className="boss-space-y-3">
                <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg">
                  <div className="boss-flex boss-items-start">
                    <div className="boss-flex-shrink-0 boss-mr-3">
                      <span className="dashicons dashicons-info boss-text-blue-600"></span>
                    </div>
                    <div>
                      <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Optimisez votre sitemap', 'boss-seo')}
                      </h4>
                      <p className="boss-text-sm boss-text-boss-gray">
                        {__('Assurez-vous que votre sitemap inclut toutes vos pages importantes et exclut le contenu de faible valeur.', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg">
                  <div className="boss-flex boss-items-start">
                    <div className="boss-flex-shrink-0 boss-mr-3">
                      <span className="dashicons dashicons-info boss-text-blue-600"></span>
                    </div>
                    <div>
                      <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Utilisez robots.txt efficacement', 'boss-seo')}
                      </h4>
                      <p className="boss-text-sm boss-text-boss-gray">
                        {__('Bloquez l\'accès aux pages administratives et aux contenus en double, mais assurez-vous de ne pas bloquer accidentellement des ressources importantes.', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="boss-p-4 boss-bg-blue-50 boss-rounded-lg">
                  <div className="boss-flex boss-items-start">
                    <div className="boss-flex-shrink-0 boss-mr-3">
                      <span className="dashicons dashicons-info boss-text-blue-600"></span>
                    </div>
                    <div>
                      <h4 className="boss-font-medium boss-text-boss-dark boss-mb-1">
                        {__('Soumettez votre sitemap', 'boss-seo')}
                      </h4>
                      <p className="boss-text-sm boss-text-boss-gray">
                        {__('Soumettez votre sitemap à Google Search Console et Bing Webmaster Tools pour accélérer l\'indexation.', 'boss-seo')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

// Composant principal
const RobotsSitemap = () => {
  // États
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [robotsContent, setRobotsContent] = useState('');
  const [sitemapSettings, setSitemapSettings] = useState({
    enabled: true,
    includedPostTypes: [],
    includedTaxonomies: [],
    defaultChangeFreq: 'weekly',
    defaultPriority: 0.7,
    includeImages: true,
    includeLastMod: true,
    excludedUrls: ''
  });
  const [indexationStats, setIndexationStats] = useState({
    indexedPages: 0,
    crawlRate: 0,
    lastCrawl: '',
    indexationIssues: []
  });
  const [contentTypes, setContentTypes] = useState([]);
  const [taxonomies, setTaxonomies] = useState([]);

  // Charger les données
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Charger le contenu du robots.txt
        const robotsData = await RobotsSitemapService.getRobotsContent();
        setRobotsContent(robotsData.content || '');

        // Charger les types de contenu
        const contentTypesData = await RobotsSitemapService.getContentTypes();
        setContentTypes(contentTypesData.contentTypes || []);

        // Charger les taxonomies
        const taxonomiesData = await RobotsSitemapService.getTaxonomies();
        setTaxonomies(taxonomiesData.taxonomies || []);

        // Charger les paramètres du sitemap
        const sitemapSettingsData = await RobotsSitemapService.getSitemapSettings();
        setSitemapSettings(sitemapSettingsData.settings || {
          enabled: true,
          includedPostTypes: [],
          includedTaxonomies: [],
          defaultChangeFreq: 'weekly',
          defaultPriority: 0.7,
          includeImages: true,
          includeLastMod: true,
          excludedUrls: ''
        });

        // Charger les statistiques d'indexation
        const indexationStatsData = await RobotsSitemapService.getIndexationStats();
        setIndexationStats(indexationStatsData.stats || {
          indexedPages: 0,
          crawlRate: 0,
          lastCrawl: '',
          indexationIssues: []
        });

        setIsLoading(false);
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Fonction pour enregistrer le contenu du robots.txt
  const saveRobotsContent = async (content) => {
    setIsSaving(true);

    try {
      // Appeler le service pour enregistrer le contenu du robots.txt
      const result = await RobotsSitemapService.saveRobotsContent(content);

      setRobotsContent(content);
      setIsSaving(false);
      setSuccessMessage(result.message || __('Le fichier robots.txt a été enregistré avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du robots.txt:', error);
      setIsSaving(false);
      setSuccessMessage(__('Erreur lors de l\'enregistrement du robots.txt.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }
  };

  // Fonction pour enregistrer les paramètres du sitemap
  const saveSitemapSettings = async (settings) => {
    setIsSaving(true);

    try {
      // Appeler le service pour enregistrer les paramètres du sitemap
      const result = await RobotsSitemapService.saveSitemapSettings(settings);

      setSitemapSettings(settings);
      setIsSaving(false);
      setSuccessMessage(result.message || __('Les paramètres du sitemap ont été enregistrés avec succès !', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du sitemap:', error);
      setIsSaving(false);
      setSuccessMessage(__('Erreur lors de l\'enregistrement des paramètres du sitemap.', 'boss-seo'));
      setShowSuccess(true);

      // Masquer le message de succès après 3 secondes
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }
  };

  return (
    <div>
      {isLoading ? (
        <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
          <Spinner />
        </div>
      ) : (
        <div>
          {showSuccess && (
            <Notice status="success" isDismissible={false} className="boss-mb-6">
              {successMessage}
            </Notice>
          )}

          <TabPanel
            className="boss-mb-6"
            activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
            tabs={[
              {
                name: 'robots',
                title: __('Robots.txt', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'sitemap',
                title: __('Sitemap', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              },
              {
                name: 'stats',
                title: __('Statistiques d\'indexation', 'boss-seo'),
                className: 'boss-font-medium boss-px-4 boss-py-2'
              }
            ]}
          >
            {(tab) => {
              if (tab.name === 'robots') {
                return (
                  <RobotsEditor
                    robotsContent={robotsContent}
                    setRobotsContent={setRobotsContent}
                    onSave={saveRobotsContent}
                    isSaving={isSaving}
                  />
                );
              } else if (tab.name === 'sitemap') {
                return (
                  <SitemapConfig
                    sitemapSettings={sitemapSettings}
                    setSitemapSettings={setSitemapSettings}
                    onSave={saveSitemapSettings}
                    isSaving={isSaving}
                    contentTypes={contentTypes}
                    taxonomies={taxonomies}
                  />
                );
              } else if (tab.name === 'stats') {
                return (
                  <IndexationStats
                    stats={indexationStats}
                    isLoading={isLoading}
                  />
                );
              }
            }}
          </TabPanel>
        </div>
      )}
    </div>
  );
};

export default RobotsSitemap;
