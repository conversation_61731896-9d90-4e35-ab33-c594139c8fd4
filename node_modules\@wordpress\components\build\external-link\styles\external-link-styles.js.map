{"version": 3, "names": ["_icons", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "StyledIcon", "_base", "default", "Icon", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports"], "sources": ["@wordpress/components/src/external-link/styles/external-link-styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n\n/**\n * WordPress dependencies\n */\nimport { Icon } from '@wordpress/icons';\n\nexport const StyledIcon = styled( Icon )`\n\twidth: 1em;\n\theight: 1em;\n\tmargin: 0;\n\tvertical-align: middle;\n\tfill: currentColor;\n`;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,MAAA,GAAAC,OAAA;AAAwC,SAAAC,iCAAA;AAEjC,MAAMC,UAAU,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,WAAI,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAMvC;AAACc,OAAA,CAAAb,UAAA,GAAAA,UAAA"}