"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";









var _KBNYGXWIcjs = require('./KBNYGXWI.cjs');



var _AV6KTKLEcjs = require('./AV6KTKLE.cjs');

// src/utils/store.ts
function getInternal(store, key) {
  const internals = store.__unstableInternals;
  _KBNYGXWIcjs.invariant.call(void 0, internals, "Invalid store");
  return internals[key];
}
function createStore(initialState, ...stores) {
  let state = initialState;
  let prevStateBatch = state;
  let lastUpdate = Symbol();
  let destroy = _KBNYGXWIcjs.noop;
  const instances = /* @__PURE__ */ new Set();
  const updatedKeys = /* @__PURE__ */ new Set();
  const setups = /* @__PURE__ */ new Set();
  const listeners = /* @__PURE__ */ new Set();
  const batchListeners = /* @__PURE__ */ new Set();
  const disposables = /* @__PURE__ */ new WeakMap();
  const listenerKeys = /* @__PURE__ */ new WeakMap();
  const storeSetup = (callback) => {
    setups.add(callback);
    return () => setups.delete(callback);
  };
  const storeInit = () => {
    const initialized = instances.size;
    const instance = Symbol();
    instances.add(instance);
    const maybeDestroy = () => {
      instances.delete(instance);
      if (instances.size)
        return;
      destroy();
    };
    if (initialized)
      return maybeDestroy;
    const desyncs = _KBNYGXWIcjs.getKeys.call(void 0, state).map(
      (key) => _KBNYGXWIcjs.chain.call(void 0, 
        ...stores.map((store) => {
          var _a;
          const storeState = (_a = store == null ? void 0 : store.getState) == null ? void 0 : _a.call(store);
          if (!storeState)
            return;
          if (!_KBNYGXWIcjs.hasOwnProperty.call(void 0, storeState, key))
            return;
          return sync(store, [key], (state2) => {
            setState(
              key,
              state2[key],
              // @ts-expect-error - Not public API. This is just to prevent
              // infinite loops.
              true
            );
          });
        })
      )
    );
    const teardowns = [];
    setups.forEach((setup2) => teardowns.push(setup2()));
    const cleanups = stores.map(init);
    destroy = _KBNYGXWIcjs.chain.call(void 0, ...desyncs, ...teardowns, ...cleanups);
    return maybeDestroy;
  };
  const sub = (keys, listener, set = listeners) => {
    set.add(listener);
    listenerKeys.set(listener, keys);
    return () => {
      var _a;
      (_a = disposables.get(listener)) == null ? void 0 : _a();
      disposables.delete(listener);
      listenerKeys.delete(listener);
      set.delete(listener);
    };
  };
  const storeSubscribe = (keys, listener) => sub(keys, listener);
  const storeSync = (keys, listener) => {
    disposables.set(listener, listener(state, state));
    return sub(keys, listener);
  };
  const storeBatch = (keys, listener) => {
    disposables.set(listener, listener(state, prevStateBatch));
    return sub(keys, listener, batchListeners);
  };
  const storePick = (keys) => createStore(_KBNYGXWIcjs.pick.call(void 0, state, keys), finalStore);
  const storeOmit = (keys) => createStore(_KBNYGXWIcjs.omit.call(void 0, state, keys), finalStore);
  const getState = () => state;
  const setState = (key, value, fromStores = false) => {
    if (!_KBNYGXWIcjs.hasOwnProperty.call(void 0, state, key))
      return;
    const nextValue = _KBNYGXWIcjs.applyState.call(void 0, value, state[key]);
    if (nextValue === state[key])
      return;
    if (!fromStores) {
      stores.forEach((store) => {
        var _a;
        (_a = store == null ? void 0 : store.setState) == null ? void 0 : _a.call(store, key, nextValue);
      });
    }
    const prevState = state;
    state = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, state), { [key]: nextValue });
    const thisUpdate = Symbol();
    lastUpdate = thisUpdate;
    updatedKeys.add(key);
    const run = (listener, prev, uKeys) => {
      var _a;
      const keys = listenerKeys.get(listener);
      const updated = (k) => uKeys ? uKeys.has(k) : k === key;
      if (!keys || keys.some(updated)) {
        (_a = disposables.get(listener)) == null ? void 0 : _a();
        disposables.set(listener, listener(state, prev));
      }
    };
    listeners.forEach((listener) => {
      run(listener, prevState);
    });
    queueMicrotask(() => {
      if (lastUpdate !== thisUpdate)
        return;
      const snapshot = state;
      batchListeners.forEach((listener) => {
        run(listener, prevStateBatch, updatedKeys);
      });
      prevStateBatch = snapshot;
      updatedKeys.clear();
    });
  };
  const finalStore = {
    getState,
    setState,
    __unstableInternals: {
      setup: storeSetup,
      init: storeInit,
      subscribe: storeSubscribe,
      sync: storeSync,
      batch: storeBatch,
      pick: storePick,
      omit: storeOmit
    }
  };
  return finalStore;
}
function setup(store, ...args) {
  if (!store)
    return;
  return getInternal(store, "setup")(...args);
}
function init(store, ...args) {
  if (!store)
    return;
  return getInternal(store, "init")(...args);
}
function subscribe(store, ...args) {
  if (!store)
    return;
  return getInternal(store, "subscribe")(...args);
}
function sync(store, ...args) {
  if (!store)
    return;
  return getInternal(store, "sync")(...args);
}
function batch(store, ...args) {
  if (!store)
    return;
  return getInternal(store, "batch")(...args);
}
function omit2(store, ...args) {
  if (!store)
    return;
  return getInternal(store, "omit")(...args);
}
function pick2(store, ...args) {
  if (!store)
    return;
  return getInternal(store, "pick")(...args);
}
function mergeStore(...stores) {
  const initialState = stores.reduce((state, store2) => {
    var _a;
    const nextState = (_a = store2 == null ? void 0 : store2.getState) == null ? void 0 : _a.call(store2);
    if (!nextState)
      return state;
    return _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, state), nextState);
  }, {});
  const store = createStore(initialState, ...stores);
  return store;
}
function throwOnConflictingProps(props, store) {
  if (process.env.NODE_ENV === "production")
    return;
  if (!store)
    return;
  const defaultKeys = Object.entries(props).filter(([key, value]) => key.startsWith("default") && value !== void 0).map(([key]) => {
    var _a;
    const stateKey = key.replace("default", "");
    return `${((_a = stateKey[0]) == null ? void 0 : _a.toLowerCase()) || ""}${stateKey.slice(1)}`;
  });
  if (!defaultKeys.length)
    return;
  const storeState = store.getState();
  const conflictingProps = defaultKeys.filter(
    (key) => _KBNYGXWIcjs.hasOwnProperty.call(void 0, storeState, key)
  );
  if (!conflictingProps.length)
    return;
  throw new Error(
    `Passing a store prop in conjunction with a default state is not supported.

const store = useSelectStore();
<SelectProvider store={store} defaultValue="Apple" />
                ^             ^

Instead, pass the default state to the topmost store:

const store = useSelectStore({ defaultValue: "Apple" });
<SelectProvider store={store} />

See https://github.com/ariakit/ariakit/pull/2745 for more details.

If there's a particular need for this, please submit a feature request at https://github.com/ariakit/ariakit
`
  );
}












exports.createStore = createStore; exports.setup = setup; exports.init = init; exports.subscribe = subscribe; exports.sync = sync; exports.batch = batch; exports.omit = omit2; exports.pick = pick2; exports.mergeStore = mergeStore; exports.throwOnConflictingProps = throwOnConflictingProps;
