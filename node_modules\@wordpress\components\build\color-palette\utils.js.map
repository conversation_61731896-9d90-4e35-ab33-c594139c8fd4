{"version": 3, "names": ["_colord", "require", "_names", "_interopRequireDefault", "_a11y", "_i18n", "extend", "namesPlugin", "a11yPlugin", "extractColorNameFromCurrentValue", "currentValue", "colors", "showMultiplePalettes", "currentValueIsCssVariable", "test", "normalizedCurrentValue", "colord", "toHex", "colorPalettes", "paletteColors", "name", "colorName", "color", "colorValue", "normalizedColorValue", "__", "exports", "isMultiplePaletteObject", "obj", "Array", "isArray", "isMultiplePaletteArray", "arr", "length", "every", "colorObj", "normalizeColorValue", "value", "element", "ownerDocument", "defaultView", "computedBackgroundColor", "getComputedStyle", "backgroundColor"], "sources": ["@wordpress/components/src/color-palette/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { colord, extend } from 'colord';\nimport namesPlugin from 'colord/plugins/names';\nimport a11yPlugin from 'colord/plugins/a11y';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport type { ColorObject, ColorPaletteProps, PaletteObject } from './types';\n\nextend( [ namesPlugin, a11yPlugin ] );\n\nexport const extractColorNameFromCurrentValue = (\n\tcurrentValue?: ColorPaletteProps[ 'value' ],\n\tcolors: ColorPaletteProps[ 'colors' ] = [],\n\tshowMultiplePalettes: boolean = false\n) => {\n\tif ( ! currentValue ) {\n\t\treturn '';\n\t}\n\n\tconst currentValueIsCssVariable = /^var\\(/.test( currentValue );\n\tconst normalizedCurrentValue = currentValueIsCssVariable\n\t\t? currentValue\n\t\t: colord( currentValue ).toHex();\n\n\t// Normalize format of `colors` to simplify the following loop\n\ttype normalizedPaletteObject = { colors: ColorObject[] };\n\tconst colorPalettes: normalizedPaletteObject[] = showMultiplePalettes\n\t\t? ( colors as PaletteObject[] )\n\t\t: [ { colors: colors as ColorObject[] } ];\n\tfor ( const { colors: paletteColors } of colorPalettes ) {\n\t\tfor ( const { name: colorName, color: colorValue } of paletteColors ) {\n\t\t\tconst normalizedColorValue = currentValueIsCssVariable\n\t\t\t\t? colorValue\n\t\t\t\t: colord( colorValue ).toHex();\n\n\t\t\tif ( normalizedCurrentValue === normalizedColorValue ) {\n\t\t\t\treturn colorName;\n\t\t\t}\n\t\t}\n\t}\n\n\t// translators: shown when the user has picked a custom color (i.e not in the palette of colors).\n\treturn __( 'Custom' );\n};\n\n// The PaletteObject type has a `colors` property (an array of ColorObject),\n// while the ColorObject type has a `color` property (the CSS color value).\nexport const isMultiplePaletteObject = (\n\tobj: PaletteObject | ColorObject\n): obj is PaletteObject =>\n\tArray.isArray( ( obj as PaletteObject ).colors ) && ! ( 'color' in obj );\n\nexport const isMultiplePaletteArray = (\n\tarr: ( PaletteObject | ColorObject )[]\n): arr is PaletteObject[] => {\n\treturn (\n\t\tarr.length > 0 &&\n\t\tarr.every( ( colorObj ) => isMultiplePaletteObject( colorObj ) )\n\t);\n};\n\n/**\n * Transform a CSS variable used as background color into the color value itself.\n *\n * @param value   The color value that may be a CSS variable.\n * @param element The element for which to get the computed style.\n * @return The background color value computed from a element.\n */\nexport const normalizeColorValue = (\n\tvalue: string | undefined,\n\telement: HTMLElement | null\n) => {\n\tconst currentValueIsCssVariable = /^var\\(/.test( value ?? '' );\n\n\tif ( ! currentValueIsCssVariable || element === null ) {\n\t\treturn value;\n\t}\n\n\tconst { ownerDocument } = element;\n\tconst { defaultView } = ownerDocument;\n\tconst computedBackgroundColor =\n\t\tdefaultView?.getComputedStyle( element ).backgroundColor;\n\n\treturn computedBackgroundColor\n\t\t? colord( computedBackgroundColor ).toHex()\n\t\t: value;\n};\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AAKA,IAAAI,KAAA,GAAAJ,OAAA;AAVA;AACA;AACA;;AAKA;AACA;AACA;;AAQA,IAAAK,cAAM,EAAE,CAAEC,cAAW,EAAEC,aAAU,CAAG,CAAC;AAE9B,MAAMC,gCAAgC,GAAGA,CAC/CC,YAA2C,EAC3CC,MAAqC,GAAG,EAAE,EAC1CC,oBAA6B,GAAG,KAAK,KACjC;EACJ,IAAK,CAAEF,YAAY,EAAG;IACrB,OAAO,EAAE;EACV;EAEA,MAAMG,yBAAyB,GAAG,QAAQ,CAACC,IAAI,CAAEJ,YAAa,CAAC;EAC/D,MAAMK,sBAAsB,GAAGF,yBAAyB,GACrDH,YAAY,GACZ,IAAAM,cAAM,EAAEN,YAAa,CAAC,CAACO,KAAK,CAAC,CAAC;;EAEjC;;EAEA,MAAMC,aAAwC,GAAGN,oBAAoB,GAChED,MAAM,GACR,CAAE;IAAEA,MAAM,EAAEA;EAAwB,CAAC,CAAE;EAC1C,KAAM,MAAM;IAAEA,MAAM,EAAEQ;EAAc,CAAC,IAAID,aAAa,EAAG;IACxD,KAAM,MAAM;MAAEE,IAAI,EAAEC,SAAS;MAAEC,KAAK,EAAEC;IAAW,CAAC,IAAIJ,aAAa,EAAG;MACrE,MAAMK,oBAAoB,GAAGX,yBAAyB,GACnDU,UAAU,GACV,IAAAP,cAAM,EAAEO,UAAW,CAAC,CAACN,KAAK,CAAC,CAAC;MAE/B,IAAKF,sBAAsB,KAAKS,oBAAoB,EAAG;QACtD,OAAOH,SAAS;MACjB;IACD;EACD;;EAEA;EACA,OAAO,IAAAI,QAAE,EAAE,QAAS,CAAC;AACtB,CAAC;;AAED;AACA;AAAAC,OAAA,CAAAjB,gCAAA,GAAAA,gCAAA;AACO,MAAMkB,uBAAuB,GACnCC,GAAgC,IAEhCC,KAAK,CAACC,OAAO,CAAIF,GAAG,CAAoBjB,MAAO,CAAC,IAAI,EAAI,OAAO,IAAIiB,GAAG,CAAE;AAACF,OAAA,CAAAC,uBAAA,GAAAA,uBAAA;AAEnE,MAAMI,sBAAsB,GAClCC,GAAsC,IACV;EAC5B,OACCA,GAAG,CAACC,MAAM,GAAG,CAAC,IACdD,GAAG,CAACE,KAAK,CAAIC,QAAQ,IAAMR,uBAAuB,CAAEQ,QAAS,CAAE,CAAC;AAElE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANAT,OAAA,CAAAK,sBAAA,GAAAA,sBAAA;AAOO,MAAMK,mBAAmB,GAAGA,CAClCC,KAAyB,EACzBC,OAA2B,KACvB;EACJ,MAAMzB,yBAAyB,GAAG,QAAQ,CAACC,IAAI,CAAEuB,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAG,CAAC;EAE9D,IAAK,CAAExB,yBAAyB,IAAIyB,OAAO,KAAK,IAAI,EAAG;IACtD,OAAOD,KAAK;EACb;EAEA,MAAM;IAAEE;EAAc,CAAC,GAAGD,OAAO;EACjC,MAAM;IAAEE;EAAY,CAAC,GAAGD,aAAa;EACrC,MAAME,uBAAuB,GAC5BD,WAAW,EAAEE,gBAAgB,CAAEJ,OAAQ,CAAC,CAACK,eAAe;EAEzD,OAAOF,uBAAuB,GAC3B,IAAAzB,cAAM,EAAEyB,uBAAwB,CAAC,CAACxB,KAAK,CAAC,CAAC,GACzCoB,KAAK;AACT,CAAC;AAACX,OAAA,CAAAU,mBAAA,GAAAA,mBAAA"}