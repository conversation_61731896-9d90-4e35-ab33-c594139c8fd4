{"version": 3, "names": ["_compose", "require", "_cell", "_interopRequireDefault", "_styles", "FooterMessageCell", "textAlign", "props", "_react", "createElement", "default", "editable", "value", "accessibilityRole", "labelStyle", "styles", "footerMessageCell", "_default", "withPreferredColorScheme", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/footer-message-cell.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { withPreferredColorScheme } from '@wordpress/compose';\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport styles from './styles.scss';\n\nfunction FooterMessageCell( { textAlign = 'left', ...props } ) {\n\treturn (\n\t\t<Cell\n\t\t\t{ ...props }\n\t\t\teditable={ false }\n\t\t\tvalue={ '' }\n\t\t\taccessibilityRole={ 'text' }\n\t\t\tlabelStyle={ [ styles.footerMessageCell, { textAlign } ] }\n\t\t/>\n\t);\n}\n\nexport default withPreferredColorScheme( FooterMessageCell );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAIA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAD,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAEA;AACA;AACA;;AAIA,SAASI,iBAAiBA,CAAE;EAAEC,SAAS,GAAG,MAAM;EAAE,GAAGC;AAAM,CAAC,EAAG;EAC9D,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACP,KAAA,CAAAQ,OAAI;IAAA,GACCH,KAAK;IACVI,QAAQ,EAAG,KAAO;IAClBC,KAAK,EAAG,EAAI;IACZC,iBAAiB,EAAG,MAAQ;IAC5BC,UAAU,EAAG,CAAEC,eAAM,CAACC,iBAAiB,EAAE;MAAEV;IAAU,CAAC;EAAI,CAC1D,CAAC;AAEJ;AAAC,IAAAW,QAAA,GAEc,IAAAC,iCAAwB,EAAEb,iBAAkB,CAAC;AAAAc,OAAA,CAAAT,OAAA,GAAAO,QAAA"}