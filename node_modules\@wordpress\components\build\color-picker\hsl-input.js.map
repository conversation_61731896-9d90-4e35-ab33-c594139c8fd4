{"version": 3, "names": ["_colord", "require", "_element", "_inputWithSlider", "HslInput", "color", "onChange", "enableAlpha", "colorPropHSLA", "useMemo", "toHsl", "internalHSLA", "setInternalHSLA", "useState", "isInternalColorSameAsReceivedColor", "isEqual", "colord", "useEffect", "colorValue", "updateHSLAValue", "partialNewValue", "nextOnChangeValue", "prevHSLA", "_react", "createElement", "Fragment", "InputWithSlider", "min", "max", "label", "abbreviation", "value", "h", "nextH", "s", "nextS", "l", "nextL", "Math", "trunc", "a", "nextA", "exports"], "sources": ["@wordpress/components/src/color-picker/hsl-input.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { colord } from 'colord';\n\n/**\n * WordPress dependencies\n */\nimport { useState, useEffect, useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport { InputWithSlider } from './input-with-slider';\nimport type { HslInputProps } from './types';\n\nexport const HslInput = ( { color, onChange, enableAlpha }: HslInputProps ) => {\n\tconst colorPropHSLA = useMemo( () => color.toHsl(), [ color ] );\n\n\tconst [ internalHSLA, setInternalHSLA ] = useState( { ...colorPropHSLA } );\n\n\tconst isInternalColorSameAsReceivedColor = color.isEqual(\n\t\tcolord( internalHSLA )\n\t);\n\n\tuseEffect( () => {\n\t\tif ( ! isInternalColorSameAsReceivedColor ) {\n\t\t\t// Keep internal HSLA color up to date with the received color prop\n\t\t\tsetInternalHSLA( colorPropHSLA );\n\t\t}\n\t}, [ colorPropHSLA, isInternalColorSameAsReceivedColor ] );\n\n\t// If the internal color is equal to the received color prop, we can use the\n\t// HSLA values from the local state which, compared to the received color prop,\n\t// retain more details about the actual H and S values that the user selected,\n\t// and thus allow for better UX when interacting with the H and S sliders.\n\tconst colorValue = isInternalColorSameAsReceivedColor\n\t\t? internalHSLA\n\t\t: colorPropHSLA;\n\n\tconst updateHSLAValue = (\n\t\tpartialNewValue: Partial< typeof colorPropHSLA >\n\t) => {\n\t\tconst nextOnChangeValue = colord( {\n\t\t\t...colorValue,\n\t\t\t...partialNewValue,\n\t\t} );\n\n\t\t// Fire `onChange` only if the resulting color is different from the\n\t\t// current one.\n\t\t// Otherwise, update the internal HSLA color to cause a re-render.\n\t\tif ( ! color.isEqual( nextOnChangeValue ) ) {\n\t\t\tonChange( nextOnChangeValue );\n\t\t} else {\n\t\t\tsetInternalHSLA( ( prevHSLA ) => ( {\n\t\t\t\t...prevHSLA,\n\t\t\t\t...partialNewValue,\n\t\t\t} ) );\n\t\t}\n\t};\n\n\treturn (\n\t\t<>\n\t\t\t<InputWithSlider\n\t\t\t\tmin={ 0 }\n\t\t\t\tmax={ 359 }\n\t\t\t\tlabel=\"Hue\"\n\t\t\t\tabbreviation=\"H\"\n\t\t\t\tvalue={ colorValue.h }\n\t\t\t\tonChange={ ( nextH: number ) => {\n\t\t\t\t\tupdateHSLAValue( { h: nextH } );\n\t\t\t\t} }\n\t\t\t/>\n\t\t\t<InputWithSlider\n\t\t\t\tmin={ 0 }\n\t\t\t\tmax={ 100 }\n\t\t\t\tlabel=\"Saturation\"\n\t\t\t\tabbreviation=\"S\"\n\t\t\t\tvalue={ colorValue.s }\n\t\t\t\tonChange={ ( nextS: number ) => {\n\t\t\t\t\tupdateHSLAValue( { s: nextS } );\n\t\t\t\t} }\n\t\t\t/>\n\t\t\t<InputWithSlider\n\t\t\t\tmin={ 0 }\n\t\t\t\tmax={ 100 }\n\t\t\t\tlabel=\"Lightness\"\n\t\t\t\tabbreviation=\"L\"\n\t\t\t\tvalue={ colorValue.l }\n\t\t\t\tonChange={ ( nextL: number ) => {\n\t\t\t\t\tupdateHSLAValue( { l: nextL } );\n\t\t\t\t} }\n\t\t\t/>\n\t\t\t{ enableAlpha && (\n\t\t\t\t<InputWithSlider\n\t\t\t\t\tmin={ 0 }\n\t\t\t\t\tmax={ 100 }\n\t\t\t\t\tlabel=\"Alpha\"\n\t\t\t\t\tabbreviation=\"A\"\n\t\t\t\t\tvalue={ Math.trunc( 100 * colorValue.a ) }\n\t\t\t\t\tonChange={ ( nextA: number ) => {\n\t\t\t\t\t\tupdateHSLAValue( { a: nextA / 100 } );\n\t\t\t\t\t} }\n\t\t\t\t/>\n\t\t\t) }\n\t\t</>\n\t);\n};\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,gBAAA,GAAAF,OAAA;AAbA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAIO,MAAMG,QAAQ,GAAGA,CAAE;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAA2B,CAAC,KAAM;EAC9E,MAAMC,aAAa,GAAG,IAAAC,gBAAO,EAAE,MAAMJ,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,CAAEL,KAAK,CAAG,CAAC;EAE/D,MAAM,CAAEM,YAAY,EAAEC,eAAe,CAAE,GAAG,IAAAC,iBAAQ,EAAE;IAAE,GAAGL;EAAc,CAAE,CAAC;EAE1E,MAAMM,kCAAkC,GAAGT,KAAK,CAACU,OAAO,CACvD,IAAAC,cAAM,EAAEL,YAAa,CACtB,CAAC;EAED,IAAAM,kBAAS,EAAE,MAAM;IAChB,IAAK,CAAEH,kCAAkC,EAAG;MAC3C;MACAF,eAAe,CAAEJ,aAAc,CAAC;IACjC;EACD,CAAC,EAAE,CAAEA,aAAa,EAAEM,kCAAkC,CAAG,CAAC;;EAE1D;EACA;EACA;EACA;EACA,MAAMI,UAAU,GAAGJ,kCAAkC,GAClDH,YAAY,GACZH,aAAa;EAEhB,MAAMW,eAAe,GACpBC,eAAgD,IAC5C;IACJ,MAAMC,iBAAiB,GAAG,IAAAL,cAAM,EAAE;MACjC,GAAGE,UAAU;MACb,GAAGE;IACJ,CAAE,CAAC;;IAEH;IACA;IACA;IACA,IAAK,CAAEf,KAAK,CAACU,OAAO,CAAEM,iBAAkB,CAAC,EAAG;MAC3Cf,QAAQ,CAAEe,iBAAkB,CAAC;IAC9B,CAAC,MAAM;MACNT,eAAe,CAAIU,QAAQ,KAAQ;QAClC,GAAGA,QAAQ;QACX,GAAGF;MACJ,CAAC,CAAG,CAAC;IACN;EACD,CAAC;EAED,OACC,IAAAG,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAACrB,gBAAA,CAAAuB,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,KAAK;IACXC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGb,UAAU,CAACc,CAAG;IACtB1B,QAAQ,EAAK2B,KAAa,IAAM;MAC/Bd,eAAe,CAAE;QAAEa,CAAC,EAAEC;MAAM,CAAE,CAAC;IAChC;EAAG,CACH,CAAC,EACF,IAAAV,MAAA,CAAAC,aAAA,EAACrB,gBAAA,CAAAuB,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,YAAY;IAClBC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGb,UAAU,CAACgB,CAAG;IACtB5B,QAAQ,EAAK6B,KAAa,IAAM;MAC/BhB,eAAe,CAAE;QAAEe,CAAC,EAAEC;MAAM,CAAE,CAAC;IAChC;EAAG,CACH,CAAC,EACF,IAAAZ,MAAA,CAAAC,aAAA,EAACrB,gBAAA,CAAAuB,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,WAAW;IACjBC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGb,UAAU,CAACkB,CAAG;IACtB9B,QAAQ,EAAK+B,KAAa,IAAM;MAC/BlB,eAAe,CAAE;QAAEiB,CAAC,EAAEC;MAAM,CAAE,CAAC;IAChC;EAAG,CACH,CAAC,EACA9B,WAAW,IACZ,IAAAgB,MAAA,CAAAC,aAAA,EAACrB,gBAAA,CAAAuB,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,OAAO;IACbC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGO,IAAI,CAACC,KAAK,CAAE,GAAG,GAAGrB,UAAU,CAACsB,CAAE,CAAG;IAC1ClC,QAAQ,EAAKmC,KAAa,IAAM;MAC/BtB,eAAe,CAAE;QAAEqB,CAAC,EAAEC,KAAK,GAAG;MAAI,CAAE,CAAC;IACtC;EAAG,CACH,CAED,CAAC;AAEL,CAAC;AAACC,OAAA,CAAAtC,QAAA,GAAAA,QAAA"}