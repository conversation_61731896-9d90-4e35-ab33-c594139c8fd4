{"version": 3, "names": ["_i18n", "require", "_customSelectControl", "_interopRequireDefault", "_unitControl", "_utils", "DEFAULT_OPTION", "key", "name", "__", "value", "undefined", "CUSTOM_OPTION", "FontSizePickerSelect", "props", "_options$find", "__next40pxDefaultSize", "fontSizes", "disableCustomFontSizes", "size", "onChange", "onSelectCustom", "areAllSizesSameUnit", "getCommonSizeUnit", "options", "map", "fontSize", "hint", "quantity", "parseQuantityAndUnitFromRawValue", "String", "isSimpleCssValue", "slug", "__experimentalHint", "selectedOption", "find", "option", "_react", "createElement", "default", "__nextUnconstrainedWidth", "className", "label", "hideLabelFromVision", "describedBy", "sprintf", "__experimentalShowSelectedHint", "selectedItem", "_default", "exports"], "sources": ["@wordpress/components/src/font-size-picker/font-size-picker-select.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __, sprintf } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport CustomSelectControl from '../custom-select-control';\nimport { parseQuantityAndUnitFromRawValue } from '../unit-control';\nimport type {\n\tFontSizePickerSelectProps,\n\tFontSizePickerSelectOption,\n} from './types';\nimport { getCommonSizeUnit, isSimpleCssValue } from './utils';\n\nconst DEFAULT_OPTION: FontSizePickerSelectOption = {\n\tkey: 'default',\n\tname: __( 'Default' ),\n\tvalue: undefined,\n};\n\nconst CUSTOM_OPTION: FontSizePickerSelectOption = {\n\tkey: 'custom',\n\tname: __( 'Custom' ),\n};\n\nconst FontSizePickerSelect = ( props: FontSizePickerSelectProps ) => {\n\tconst {\n\t\t__next40pxDefaultSize,\n\t\tfontSizes,\n\t\tvalue,\n\t\tdisableCustomFontSizes,\n\t\tsize,\n\t\tonChange,\n\t\tonSelectCustom,\n\t} = props;\n\n\tconst areAllSizesSameUnit = !! getCommonSizeUnit( fontSizes );\n\n\tconst options: FontSizePickerSelectOption[] = [\n\t\tDEFAULT_OPTION,\n\t\t...fontSizes.map( ( fontSize ) => {\n\t\t\tlet hint;\n\t\t\tif ( areAllSizesSameUnit ) {\n\t\t\t\tconst [ quantity ] = parseQuantityAndUnitFromRawValue(\n\t\t\t\t\tfontSize.size\n\t\t\t\t);\n\t\t\t\tif ( quantity !== undefined ) {\n\t\t\t\t\thint = String( quantity );\n\t\t\t\t}\n\t\t\t} else if ( isSimpleCssValue( fontSize.size ) ) {\n\t\t\t\thint = String( fontSize.size );\n\t\t\t}\n\t\t\treturn {\n\t\t\t\tkey: fontSize.slug,\n\t\t\t\tname: fontSize.name || fontSize.slug,\n\t\t\t\tvalue: fontSize.size,\n\t\t\t\t__experimentalHint: hint,\n\t\t\t};\n\t\t} ),\n\t\t...( disableCustomFontSizes ? [] : [ CUSTOM_OPTION ] ),\n\t];\n\n\tconst selectedOption = value\n\t\t? options.find( ( option ) => option.value === value ) ?? CUSTOM_OPTION\n\t\t: DEFAULT_OPTION;\n\n\treturn (\n\t\t<CustomSelectControl\n\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t__nextUnconstrainedWidth\n\t\t\tclassName=\"components-font-size-picker__select\"\n\t\t\tlabel={ __( 'Font size' ) }\n\t\t\thideLabelFromVision\n\t\t\tdescribedBy={ sprintf(\n\t\t\t\t// translators: %s: Currently selected font size.\n\t\t\t\t__( 'Currently selected font size: %s' ),\n\t\t\t\tselectedOption.name\n\t\t\t) }\n\t\t\toptions={ options }\n\t\t\tvalue={ selectedOption }\n\t\t\t__experimentalShowSelectedHint\n\t\t\tonChange={ ( {\n\t\t\t\tselectedItem,\n\t\t\t}: {\n\t\t\t\tselectedItem: FontSizePickerSelectOption;\n\t\t\t} ) => {\n\t\t\t\tif ( selectedItem === CUSTOM_OPTION ) {\n\t\t\t\t\tonSelectCustom();\n\t\t\t\t} else {\n\t\t\t\t\tonChange( selectedItem.value );\n\t\t\t\t}\n\t\t\t} }\n\t\t\tsize={ size }\n\t\t/>\n\t);\n};\n\nexport default FontSizePickerSelect;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,oBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAKA,IAAAI,MAAA,GAAAJ,OAAA;AAdA;AACA;AACA;;AAGA;AACA;AACA;;AASA,MAAMK,cAA0C,GAAG;EAClDC,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,IAAAC,QAAE,EAAE,SAAU,CAAC;EACrBC,KAAK,EAAEC;AACR,CAAC;AAED,MAAMC,aAAyC,GAAG;EACjDL,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,IAAAC,QAAE,EAAE,QAAS;AACpB,CAAC;AAED,MAAMI,oBAAoB,GAAKC,KAAgC,IAAM;EAAA,IAAAC,aAAA;EACpE,MAAM;IACLC,qBAAqB;IACrBC,SAAS;IACTP,KAAK;IACLQ,sBAAsB;IACtBC,IAAI;IACJC,QAAQ;IACRC;EACD,CAAC,GAAGP,KAAK;EAET,MAAMQ,mBAAmB,GAAG,CAAC,CAAE,IAAAC,wBAAiB,EAAEN,SAAU,CAAC;EAE7D,MAAMO,OAAqC,GAAG,CAC7ClB,cAAc,EACd,GAAGW,SAAS,CAACQ,GAAG,CAAIC,QAAQ,IAAM;IACjC,IAAIC,IAAI;IACR,IAAKL,mBAAmB,EAAG;MAC1B,MAAM,CAAEM,QAAQ,CAAE,GAAG,IAAAC,6CAAgC,EACpDH,QAAQ,CAACP,IACV,CAAC;MACD,IAAKS,QAAQ,KAAKjB,SAAS,EAAG;QAC7BgB,IAAI,GAAGG,MAAM,CAAEF,QAAS,CAAC;MAC1B;IACD,CAAC,MAAM,IAAK,IAAAG,uBAAgB,EAAEL,QAAQ,CAACP,IAAK,CAAC,EAAG;MAC/CQ,IAAI,GAAGG,MAAM,CAAEJ,QAAQ,CAACP,IAAK,CAAC;IAC/B;IACA,OAAO;MACNZ,GAAG,EAAEmB,QAAQ,CAACM,IAAI;MAClBxB,IAAI,EAAEkB,QAAQ,CAAClB,IAAI,IAAIkB,QAAQ,CAACM,IAAI;MACpCtB,KAAK,EAAEgB,QAAQ,CAACP,IAAI;MACpBc,kBAAkB,EAAEN;IACrB,CAAC;EACF,CAAE,CAAC,EACH,IAAKT,sBAAsB,GAAG,EAAE,GAAG,CAAEN,aAAa,CAAE,CAAE,CACtD;EAED,MAAMsB,cAAc,GAAGxB,KAAK,IAAAK,aAAA,GACzBS,OAAO,CAACW,IAAI,CAAIC,MAAM,IAAMA,MAAM,CAAC1B,KAAK,KAAKA,KAAM,CAAC,cAAAK,aAAA,cAAAA,aAAA,GAAIH,aAAa,GACrEN,cAAc;EAEjB,OACC,IAAA+B,MAAA,CAAAC,aAAA,EAACpC,oBAAA,CAAAqC,OAAmB;IACnBvB,qBAAqB,EAAGA,qBAAuB;IAC/CwB,wBAAwB;IACxBC,SAAS,EAAC,qCAAqC;IAC/CC,KAAK,EAAG,IAAAjC,QAAE,EAAE,WAAY,CAAG;IAC3BkC,mBAAmB;IACnBC,WAAW,EAAG,IAAAC,aAAO;IACpB;IACA,IAAApC,QAAE,EAAE,kCAAmC,CAAC,EACxCyB,cAAc,CAAC1B,IAChB,CAAG;IACHgB,OAAO,EAAGA,OAAS;IACnBd,KAAK,EAAGwB,cAAgB;IACxBY,8BAA8B;IAC9B1B,QAAQ,EAAGA,CAAE;MACZ2B;IAGD,CAAC,KAAM;MACN,IAAKA,YAAY,KAAKnC,aAAa,EAAG;QACrCS,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM;QACND,QAAQ,CAAE2B,YAAY,CAACrC,KAAM,CAAC;MAC/B;IACD,CAAG;IACHS,IAAI,EAAGA;EAAM,CACb,CAAC;AAEJ,CAAC;AAAC,IAAA6B,QAAA,GAEanC,oBAAoB;AAAAoC,OAAA,CAAAV,OAAA,GAAAS,QAAA"}