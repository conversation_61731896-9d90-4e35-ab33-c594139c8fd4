"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = Token;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _compose = require("@wordpress/compose");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _button = _interopRequireDefault(require("../button"));
var _visuallyHidden = require("../visually-hidden");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const noop = () => {};
function Token({
  value,
  status,
  title,
  displayTransform,
  isBorderless = false,
  disabled = false,
  onClickRemove = noop,
  onMouseEnter,
  onMouseLeave,
  messages,
  termPosition,
  termsCount
}) {
  const instanceId = (0, _compose.useInstanceId)(Token);
  const tokenClasses = (0, _classnames.default)('components-form-token-field__token', {
    'is-error': 'error' === status,
    'is-success': 'success' === status,
    'is-validating': 'validating' === status,
    'is-borderless': isBorderless,
    'is-disabled': disabled
  });
  const onClick = () => onClickRemove({
    value
  });
  const transformedValue = displayTransform(value);
  const termPositionAndCount = (0, _i18n.sprintf)( /* translators: 1: term name, 2: term position in a set of terms, 3: total term set count. */
  (0, _i18n.__)('%1$s (%2$s of %3$s)'), transformedValue, termPosition, termsCount);
  return (0, _react.createElement)("span", {
    className: tokenClasses,
    onMouseEnter: onMouseEnter,
    onMouseLeave: onMouseLeave,
    title: title
  }, (0, _react.createElement)("span", {
    className: "components-form-token-field__token-text",
    id: `components-form-token-field__token-text-${instanceId}`
  }, (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    as: "span"
  }, termPositionAndCount), (0, _react.createElement)("span", {
    "aria-hidden": "true"
  }, transformedValue)), (0, _react.createElement)(_button.default, {
    className: "components-form-token-field__remove-token",
    icon: _icons.closeSmall,
    onClick: !disabled ? onClick : undefined,
    disabled: disabled,
    label: messages.remove,
    "aria-describedby": `components-form-token-field__token-text-${instanceId}`
  }));
}
//# sourceMappingURL=token.js.map