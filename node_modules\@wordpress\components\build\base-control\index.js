"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.VisualLabel = exports.BaseControl = void 0;
Object.defineProperty(exports, "useBaseControlProps", {
  enumerable: true,
  get: function () {
    return _hooks.useBaseControlProps;
  }
});
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _visuallyHidden = require("../visually-hidden");
var _baseControlStyles = require("./styles/base-control-styles");
var _context = require("../context");
var _hooks = require("./hooks");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

/**
 * `BaseControl` is a component used to generate labels and help text for components handling user inputs.
 *
 * ```jsx
 * import { BaseControl, useBaseControlProps } from '@wordpress/components';
 *
 * // Render a `BaseControl` for a textarea input
 * const MyCustomTextareaControl = ({ children, ...baseProps }) => (
 * 	// `useBaseControlProps` is a convenience hook to get the props for the `BaseControl`
 * 	// and the inner control itself. Namely, it takes care of generating a unique `id`,
 * 	// properly associating it with the `label` and `help` elements.
 * 	const { baseControlProps, controlProps } = useBaseControlProps( baseProps );
 *
 * 	return (
 * 		<BaseControl { ...baseControlProps } __nextHasNoMarginBottom={ true }>
 * 			<textarea { ...controlProps }>
 * 			  { children }
 * 			</textarea>
 * 		</BaseControl>
 * 	);
 * );
 * ```
 */
const UnconnectedBaseControl = props => {
  const {
    __nextHasNoMarginBottom = false,
    id,
    label,
    hideLabelFromVision = false,
    help,
    className,
    children
  } = (0, _context.useContextSystem)(props, 'BaseControl');
  return (0, _react.createElement)(_baseControlStyles.Wrapper, {
    className: className
  }, (0, _react.createElement)(_baseControlStyles.StyledField, {
    className: "components-base-control__field"
    // TODO: Official deprecation for this should start after all internal usages have been migrated
    ,
    __nextHasNoMarginBottom: __nextHasNoMarginBottom
  }, label && id && (hideLabelFromVision ? (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    as: "label",
    htmlFor: id
  }, label) : (0, _react.createElement)(_baseControlStyles.StyledLabel, {
    className: "components-base-control__label",
    htmlFor: id
  }, label)), label && !id && (hideLabelFromVision ? (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    as: "label"
  }, label) : (0, _react.createElement)(VisualLabel, null, label)), children), !!help && (0, _react.createElement)(_baseControlStyles.StyledHelp, {
    id: id ? id + '__help' : undefined,
    className: "components-base-control__help",
    __nextHasNoMarginBottom: __nextHasNoMarginBottom
  }, help));
};

/**
 * `BaseControl.VisualLabel` is used to render a purely visual label inside a `BaseControl` component.
 *
 * It should only be used in cases where the children being rendered inside `BaseControl` are already accessibly labeled,
 * e.g., a button, but we want an additional visual label for that section equivalent to the labels `BaseControl` would
 * otherwise use if the `label` prop was passed.
 *
 * @example
 * import { BaseControl } from '@wordpress/components';
 *
 * const MyBaseControl = () => (
 * 	<BaseControl help="This button is already accessibly labeled.">
 * 		<BaseControl.VisualLabel>Author</BaseControl.VisualLabel>
 * 		<Button>Select an author</Button>
 * 	</BaseControl>
 * );
 */
const VisualLabel = ({
  className,
  children,
  ...props
}) => {
  return (0, _react.createElement)(_baseControlStyles.StyledVisualLabel, {
    ...props,
    className: (0, _classnames.default)('components-base-control__label', className)
  }, children);
};
exports.VisualLabel = VisualLabel;
const BaseControl = Object.assign((0, _context.contextConnectWithoutRef)(UnconnectedBaseControl, 'BaseControl'), {
  VisualLabel
});
exports.BaseControl = BaseControl;
var _default = BaseControl;
exports.default = _default;
//# sourceMappingURL=index.js.map