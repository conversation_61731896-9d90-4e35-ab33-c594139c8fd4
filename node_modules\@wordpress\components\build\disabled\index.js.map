{"version": 3, "names": ["_element", "require", "_disabledStyles", "_utils", "Context", "createContext", "Consumer", "Provider", "Disabled", "className", "children", "isDisabled", "props", "cx", "useCx", "_react", "createElement", "value", "inert", "undefined", "disabledStyles", "_default", "exports", "default"], "sources": ["@wordpress/components/src/disabled/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { createContext } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport { disabledStyles } from './styles/disabled-styles';\nimport type { DisabledProps } from './types';\nimport type { WordPressComponentProps } from '../context';\nimport { useCx } from '../utils';\n\nconst Context = createContext< boolean >( false );\nconst { Consumer, Provider } = Context;\n\n/**\n * `Disabled` is a component which disables descendant tabbable elements and\n * prevents pointer interaction.\n *\n * _Note: this component may not behave as expected in browsers that don't\n * support the `inert` HTML attribute. We recommend adding the official WICG\n * polyfill when using this component in your project._\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/inert\n *\n * ```jsx\n * import { Button, Disabled, TextControl } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyDisabled = () => {\n * \tconst [ isDisabled, setIsDisabled ] = useState( true );\n *\n * \tlet input = <TextControl label=\"Input\" onChange={ () => {} } />;\n * \tif ( isDisabled ) {\n * \t\tinput = <Disabled>{ input }</Disabled>;\n * \t}\n *\n * \tconst toggleDisabled = () => {\n * \t\tsetIsDisabled( ( state ) => ! state );\n * \t};\n *\n * \treturn (\n * \t\t<div>\n * \t\t\t{ input }\n * \t\t\t<Button variant=\"primary\" onClick={ toggleDisabled }>\n * \t\t\t\tToggle Disabled\n * \t\t\t</Button>\n * \t\t</div>\n * \t);\n * };\n * ```\n */\nfunction Disabled( {\n\tclassName,\n\tchildren,\n\tisDisabled = true,\n\t...props\n}: WordPressComponentProps< DisabledProps, 'div' > ) {\n\tconst cx = useCx();\n\n\treturn (\n\t\t<Provider value={ isDisabled }>\n\t\t\t<div\n\t\t\t\t// @ts-ignore Reason: inert is a recent HTML attribute\n\t\t\t\tinert={ isDisabled ? 'true' : undefined }\n\t\t\t\tclassName={\n\t\t\t\t\tisDisabled\n\t\t\t\t\t\t? cx( disabledStyles, className, 'components-disabled' )\n\t\t\t\t\t\t: undefined\n\t\t\t\t}\n\t\t\t\t{ ...props }\n\t\t\t>\n\t\t\t\t{ children }\n\t\t\t</div>\n\t\t</Provider>\n\t);\n}\n\nDisabled.Context = Context;\nDisabled.Consumer = Consumer;\n\nexport default Disabled;\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,eAAA,GAAAD,OAAA;AAGA,IAAAE,MAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAMA,MAAMG,OAAO,GAAG,IAAAC,sBAAa,EAAa,KAAM,CAAC;AACjD,MAAM;EAAEC,QAAQ;EAAEC;AAAS,CAAC,GAAGH,OAAO;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAAE;EAClBC,SAAS;EACTC,QAAQ;EACRC,UAAU,GAAG,IAAI;EACjB,GAAGC;AAC6C,CAAC,EAAG;EACpD,MAAMC,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACT,QAAQ;IAACU,KAAK,EAAGN;EAAY,GAC7B,IAAAI,MAAA,CAAAC,aAAA;IACC;IACAE,KAAK,EAAGP,UAAU,GAAG,MAAM,GAAGQ,SAAW;IACzCV,SAAS,EACRE,UAAU,GACPE,EAAE,CAAEO,8BAAc,EAAEX,SAAS,EAAE,qBAAsB,CAAC,GACtDU,SACH;IAAA,GACIP;EAAK,GAERF,QACE,CACI,CAAC;AAEb;AAEAF,QAAQ,CAACJ,OAAO,GAAGA,OAAO;AAC1BI,QAAQ,CAACF,QAAQ,GAAGA,QAAQ;AAAC,IAAAe,QAAA,GAEdb,QAAQ;AAAAc,OAAA,CAAAC,OAAA,GAAAF,QAAA"}