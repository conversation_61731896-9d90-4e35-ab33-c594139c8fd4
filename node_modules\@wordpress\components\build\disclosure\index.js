"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.DisclosureContent = void 0;
var _react = require("react");
var Ariakit = _interopRequireWildcard(require("@ariakit/react"));
var _element = require("@wordpress/element");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */
// eslint-disable-next-line no-restricted-imports

/**
 * WordPress dependencies
 */

/**
 * Accessible Disclosure component that controls visibility of a section of
 * content. It follows the WAI-ARIA Disclosure Pattern.
 */
const UnforwardedDisclosureContent = ({
  visible,
  children,
  ...props
}, ref) => {
  const disclosure = Ariakit.useDisclosureStore({
    open: visible
  });
  return (0, _react.createElement)(Ariakit.DisclosureContent, {
    store: disclosure,
    ref: ref,
    ...props
  }, children);
};
const DisclosureContent = (0, _element.forwardRef)(UnforwardedDisclosureContent);
exports.DisclosureContent = DisclosureContent;
var _default = DisclosureContent;
exports.default = _default;
//# sourceMappingURL=index.js.map