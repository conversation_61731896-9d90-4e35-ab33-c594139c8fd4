{"version": 3, "names": ["_element", "require", "_compose", "_deprecated", "_interopRequireDefault", "isComponentLike", "object", "Component", "_default", "createHigherOrderComponent", "options", "HoC", "onFocusReturn", "WrappedComponent", "WithFocusReturn", "props", "ref", "useFocusReturn", "_react", "createElement", "exports", "default", "Provider", "children", "deprecated", "since", "hint"], "sources": ["@wordpress/components/src/higher-order/with-focus-return/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { Component } from '@wordpress/element';\nimport { createHigherOrderComponent, useFocusReturn } from '@wordpress/compose';\nimport deprecated from '@wordpress/deprecated';\n\n/**\n * Returns true if the given object is component-like. An object is component-\n * like if it is an instance of wp.element.Component, or is a function.\n *\n * @param object Object to test.\n *\n * @return Whether object is component-like.\n */\nfunction isComponentLike( object: any ): object is React.ComponentType {\n\treturn object instanceof Component || typeof object === 'function';\n}\n\ntype Props = {\n\tonFocusReturn?: () => void;\n};\n\n/**\n * Higher Order Component used to be used to wrap disposable elements like\n * sidebars, modals, dropdowns. When mounting the wrapped component, we track a\n * reference to the current active element so we know where to restore focus\n * when the component is unmounted.\n *\n * @param options The component to be enhanced with\n *                focus return behavior, or an object\n *                describing the component and the\n *                focus return characteristics.\n *\n * @return Higher Order Component with the focus restauration behaviour.\n */\nexport default createHigherOrderComponent(\n\t// @ts-expect-error TODO: Reconcile with intended `createHigherOrderComponent` types\n\t( options: React.ComponentType | Record< string, unknown > ) => {\n\t\tconst HoC =\n\t\t\t( { onFocusReturn }: Props = {} ) =>\n\t\t\t( WrappedComponent: React.ComponentType ) => {\n\t\t\t\tconst WithFocusReturn = (\n\t\t\t\t\tprops: Record< string, unknown >\n\t\t\t\t) => {\n\t\t\t\t\tconst ref = useFocusReturn( onFocusReturn );\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<div ref={ ref }>\n\t\t\t\t\t\t\t<WrappedComponent { ...props } />\n\t\t\t\t\t\t</div>\n\t\t\t\t\t);\n\t\t\t\t};\n\n\t\t\t\treturn WithFocusReturn;\n\t\t\t};\n\n\t\tif ( isComponentLike( options ) ) {\n\t\t\tconst WrappedComponent = options;\n\t\t\treturn HoC()( WrappedComponent );\n\t\t}\n\n\t\treturn HoC( options );\n\t},\n\t'withFocusReturn'\n);\n\nexport const Provider = ( { children }: { children: React.ReactNode } ) => {\n\tdeprecated( 'wp.components.FocusReturnProvider component', {\n\t\tsince: '5.7',\n\t\thint: 'This provider is not used anymore. You can just remove it from your codebase',\n\t} );\n\n\treturn children;\n};\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AALA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAAEC,MAAW,EAAkC;EACtE,OAAOA,MAAM,YAAYC,kBAAS,IAAI,OAAOD,MAAM,KAAK,UAAU;AACnE;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,IAAAE,QAAA,GAae,IAAAC,mCAA0B;AACxC;AACEC,OAAwD,IAAM;EAC/D,MAAMC,GAAG,GACRA,CAAE;IAAEC;EAAqB,CAAC,GAAG,CAAC,CAAC,KAC7BC,gBAAqC,IAAM;IAC5C,MAAMC,eAAe,GACpBC,KAAgC,IAC5B;MACJ,MAAMC,GAAG,GAAG,IAAAC,uBAAc,EAAEL,aAAc,CAAC;MAC3C,OACC,IAAAM,MAAA,CAAAC,aAAA;QAAKH,GAAG,EAAGA;MAAK,GACf,IAAAE,MAAA,CAAAC,aAAA,EAACN,gBAAgB;QAAA,GAAME;MAAK,CAAI,CAC5B,CAAC;IAER,CAAC;IAED,OAAOD,eAAe;EACvB,CAAC;EAEF,IAAKT,eAAe,CAAEK,OAAQ,CAAC,EAAG;IACjC,MAAMG,gBAAgB,GAAGH,OAAO;IAChC,OAAOC,GAAG,CAAC,CAAC,CAAEE,gBAAiB,CAAC;EACjC;EAEA,OAAOF,GAAG,CAAED,OAAQ,CAAC;AACtB,CAAC,EACD,iBACD,CAAC;AAAAU,OAAA,CAAAC,OAAA,GAAAb,QAAA;AAEM,MAAMc,QAAQ,GAAGA,CAAE;EAAEC;AAAwC,CAAC,KAAM;EAC1E,IAAAC,mBAAU,EAAE,6CAA6C,EAAE;IAC1DC,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACP,CAAE,CAAC;EAEH,OAAOH,QAAQ;AAChB,CAAC;AAACH,OAAA,CAAAE,QAAA,GAAAA,QAAA"}