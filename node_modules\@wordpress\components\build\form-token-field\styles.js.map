{"version": 3, "names": ["_react", "require", "_flex", "_space", "_utils", "deprecatedPaddings", "__next40pxDefaultSize", "hasTokens", "css", "space", "process", "env", "NODE_ENV", "TokensAndInputWrapperFlex", "_base", "default", "Flex", "target", "label", "boxSizingReset", "exports"], "sources": ["@wordpress/components/src/form-token-field/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { Flex } from '../flex';\nimport { space } from '../utils/space';\nimport { boxSizingReset } from '../utils';\n\ntype TokensAndInputWrapperProps = {\n\t__next40pxDefaultSize: boolean;\n\thasTokens: boolean;\n};\n\nconst deprecatedPaddings = ( {\n\t__next40pxDefaultSize,\n\thasTokens,\n}: TokensAndInputWrapperProps ) =>\n\t! __next40pxDefaultSize &&\n\tcss`\n\t\tpadding-top: ${ space( hasTokens ? 1 : 0.5 ) };\n\t\tpadding-bottom: ${ space( hasTokens ? 1 : 0.5 ) };\n\t`;\n\nexport const TokensAndInputWrapperFlex = styled( Flex )`\n\tpadding: 7px;\n\t${ boxSizingReset }\n\n\t${ deprecatedPaddings }\n`;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAXA;AACA;AACA;;AAIA;AACA;AACA;;AAUA,MAAMI,kBAAkB,GAAGA,CAAE;EAC5BC,qBAAqB;EACrBC;AAC2B,CAAC,KAC5B,CAAED,qBAAqB,qBACvBE,UAAG,kBACc,IAAAC,YAAK,EAAEF,SAAS,GAAG,CAAC,GAAG,GAAI,CAAC,sBACzB,IAAAE,YAAK,EAAEF,SAAS,GAAG,CAAC,GAAG,GAAI,CAAC,SAAAG,OAAA,CAAAC,GAAA,CAAAC,QAAA,wDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,m2CAC/C;AAEK,MAAMC,yBAAyB,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,UAAI,EAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,iBAEnDC,qBAAc,OAEdd,kBAAkB,SAAAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,o2CACrB;AAACQ,OAAA,CAAAP,yBAAA,GAAAA,yBAAA"}