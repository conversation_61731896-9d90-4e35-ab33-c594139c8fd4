{"version": 3, "names": ["_i18n", "require", "_icons", "_cell", "_interopRequireDefault", "_styles", "placeholderColor", "styles", "LinkCell", "value", "valueMask", "onPress", "showIcon", "_react", "createElement", "default", "icon", "link", "label", "__", "valueStyle", "undefined", "Icon", "chevronRight"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/link-cell.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { link, Icon, chevronRight } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport styles from './styles.scss';\n\nconst { placeholderColor } = styles;\n\nexport default function LinkCell( {\n\tvalue,\n\tvalueMask,\n\tonPress,\n\tshowIcon = true,\n} ) {\n\treturn (\n\t\t<Cell\n\t\t\ticon={ showIcon && link }\n\t\t\tlabel={ __( 'Link to' ) }\n\t\t\t// Since this is not actually editable, we treat value as a placeholder.\n\t\t\tvalue={ valueMask || value || __( 'Search or type URL' ) }\n\t\t\tvalueStyle={\n\t\t\t\t!! ( value || valueMask ) ? undefined : placeholderColor\n\t\t\t}\n\t\t\tonPress={ onPress }\n\t\t>\n\t\t\t<Icon icon={ chevronRight }></Icon>\n\t\t</Cell>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAKA,IAAAE,KAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,OAAA,GAAAD,sBAAA,CAAAH,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAIA,MAAM;EAAEK;AAAiB,CAAC,GAAGC,eAAM;AAEpB,SAASC,QAAQA,CAAE;EACjCC,KAAK;EACLC,SAAS;EACTC,OAAO;EACPC,QAAQ,GAAG;AACZ,CAAC,EAAG;EACH,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACX,KAAA,CAAAY,OAAI;IACJC,IAAI,EAAGJ,QAAQ,IAAIK,WAAM;IACzBC,KAAK,EAAG,IAAAC,QAAE,EAAE,SAAU;IACtB;IAAA;IACAV,KAAK,EAAGC,SAAS,IAAID,KAAK,IAAI,IAAAU,QAAE,EAAE,oBAAqB,CAAG;IAC1DC,UAAU,EACT,CAAC,EAAIX,KAAK,IAAIC,SAAS,CAAE,GAAGW,SAAS,GAAGf,gBACxC;IACDK,OAAO,EAAGA;EAAS,GAEnB,IAAAE,MAAA,CAAAC,aAAA,EAACZ,MAAA,CAAAoB,IAAI;IAACN,IAAI,EAAGO;EAAc,CAAO,CAC7B,CAAC;AAET"}