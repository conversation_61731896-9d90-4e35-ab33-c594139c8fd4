"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _E53JW5BDcjs = require('./E53JW5BD.cjs');


var _F6HPKLO2cjs = require('./F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('./KBNYGXWI.cjs');



var _AV6KTKLEcjs = require('./AV6KTKLE.cjs');

// src/hovercard/hovercard-store.ts
function createHovercardStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const popover = _E53JW5BDcjs.createPopoverStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    placement: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.placement,
      syncState == null ? void 0 : syncState.placement,
      "bottom"
    )
  }));
  const timeout = _KBNYGXWIcjs.defaultValue.call(void 0, props.timeout, syncState == null ? void 0 : syncState.timeout, 500);
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, popover.getState()), {
    timeout,
    showTimeout: _KBNYGXWIcjs.defaultValue.call(void 0, props.showTimeout, syncState == null ? void 0 : syncState.showTimeout),
    hideTimeout: _KBNYGXWIcjs.defaultValue.call(void 0, props.hideTimeout, syncState == null ? void 0 : syncState.hideTimeout),
    autoFocusOnShow: _KBNYGXWIcjs.defaultValue.call(void 0, syncState == null ? void 0 : syncState.autoFocusOnShow, false)
  });
  const hovercard = _F6HPKLO2cjs.createStore.call(void 0, initialState, popover, props.store);
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, popover), hovercard), {
    setAutoFocusOnShow: (value) => hovercard.setState("autoFocusOnShow", value)
  });
}



exports.createHovercardStore = createHovercardStore;
