"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _colord = require("colord");
var _compose = require("@wordpress/compose");
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _button = _interopRequireDefault(require("../../button"));
var _hStack = require("../../h-stack");
var _colorPicker = require("../../color-picker");
var _visuallyHidden = require("../../visually-hidden");
var _colorPalette = require("../../color-palette");
var _utils = require("./utils");
var _constants = require("./constants");
var _dropdownContentWrapper = _interopRequireDefault(require("../../dropdown/dropdown-content-wrapper"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function ControlPointButton({
  isOpen,
  position,
  color,
  ...additionalProps
}) {
  const instanceId = (0, _compose.useInstanceId)(ControlPointButton);
  const descriptionId = `components-custom-gradient-picker__control-point-button-description-${instanceId}`;
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_button.default, {
    "aria-label": (0, _i18n.sprintf)(
    // translators: %1$s: gradient position e.g: 70, %2$s: gradient color code e.g: rgb(52,121,151).
    (0, _i18n.__)('Gradient control point at position %1$s%% with color code %2$s.'), position, color),
    "aria-describedby": descriptionId,
    "aria-haspopup": "true",
    "aria-expanded": isOpen,
    className: (0, _classnames.default)('components-custom-gradient-picker__control-point-button', {
      'is-active': isOpen
    }),
    ...additionalProps
  }), (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    id: descriptionId
  }, (0, _i18n.__)('Use your left or right arrow keys or drag and drop with the mouse to change the gradient position. Press the button to change the color or remove the control point.')));
}
function GradientColorPickerDropdown({
  isRenderedInSidebar,
  className,
  ...props
}) {
  // Open the popover below the gradient control/insertion point
  const popoverProps = (0, _element.useMemo)(() => ({
    placement: 'bottom',
    offset: 8,
    // Disabling resize as it would otherwise cause the popover to show
    // scrollbars while dragging the color picker's handle close to the
    // popover edge.
    resize: false
  }), []);
  const mergedClassName = (0, _classnames.default)('components-custom-gradient-picker__control-point-dropdown', className);
  return (0, _react.createElement)(_colorPalette.CustomColorPickerDropdown, {
    isRenderedInSidebar: isRenderedInSidebar,
    popoverProps: popoverProps,
    className: mergedClassName,
    ...props
  });
}
function ControlPoints({
  disableRemove,
  disableAlpha,
  gradientPickerDomRef,
  ignoreMarkerPosition,
  value: controlPoints,
  onChange,
  onStartControlPointChange,
  onStopControlPointChange,
  __experimentalIsRenderedInSidebar
}) {
  const controlPointMoveState = (0, _element.useRef)();
  const onMouseMove = event => {
    if (controlPointMoveState.current === undefined || gradientPickerDomRef.current === null) {
      return;
    }
    const relativePosition = (0, _utils.getHorizontalRelativeGradientPosition)(event.clientX, gradientPickerDomRef.current);
    const {
      initialPosition,
      index,
      significantMoveHappened
    } = controlPointMoveState.current;
    if (!significantMoveHappened && Math.abs(initialPosition - relativePosition) >= _constants.MINIMUM_SIGNIFICANT_MOVE) {
      controlPointMoveState.current.significantMoveHappened = true;
    }
    onChange((0, _utils.updateControlPointPosition)(controlPoints, index, relativePosition));
  };
  const cleanEventListeners = () => {
    if (window && window.removeEventListener && controlPointMoveState.current && controlPointMoveState.current.listenersActivated) {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', cleanEventListeners);
      onStopControlPointChange();
      controlPointMoveState.current.listenersActivated = false;
    }
  };

  // Adding `cleanEventListeners` to the dependency array below requires the function itself to be wrapped in a `useCallback`
  // This memoization would prevent the event listeners from being properly cleaned.
  // Instead, we'll pass a ref to the function in our `useEffect` so `cleanEventListeners` itself is no longer a dependency.
  const cleanEventListenersRef = (0, _element.useRef)();
  cleanEventListenersRef.current = cleanEventListeners;
  (0, _element.useEffect)(() => {
    return () => {
      cleanEventListenersRef.current?.();
    };
  }, []);
  return (0, _react.createElement)(_react.Fragment, null, controlPoints.map((point, index) => {
    const initialPosition = point?.position;
    return ignoreMarkerPosition !== initialPosition && (0, _react.createElement)(GradientColorPickerDropdown, {
      isRenderedInSidebar: __experimentalIsRenderedInSidebar,
      key: index,
      onClose: onStopControlPointChange,
      renderToggle: ({
        isOpen,
        onToggle
      }) => (0, _react.createElement)(ControlPointButton, {
        key: index,
        onClick: () => {
          if (controlPointMoveState.current && controlPointMoveState.current.significantMoveHappened) {
            return;
          }
          if (isOpen) {
            onStopControlPointChange();
          } else {
            onStartControlPointChange();
          }
          onToggle();
        },
        onMouseDown: () => {
          if (window && window.addEventListener) {
            controlPointMoveState.current = {
              initialPosition,
              index,
              significantMoveHappened: false,
              listenersActivated: true
            };
            onStartControlPointChange();
            window.addEventListener('mousemove', onMouseMove);
            window.addEventListener('mouseup', cleanEventListeners);
          }
        },
        onKeyDown: event => {
          if (event.code === 'ArrowLeft') {
            // Stop propagation of the key press event to avoid focus moving
            // to another editor area.
            event.stopPropagation();
            onChange((0, _utils.updateControlPointPosition)(controlPoints, index, (0, _utils.clampPercent)(point.position - _constants.KEYBOARD_CONTROL_POINT_VARIATION)));
          } else if (event.code === 'ArrowRight') {
            // Stop propagation of the key press event to avoid focus moving
            // to another editor area.
            event.stopPropagation();
            onChange((0, _utils.updateControlPointPosition)(controlPoints, index, (0, _utils.clampPercent)(point.position + _constants.KEYBOARD_CONTROL_POINT_VARIATION)));
          }
        },
        isOpen: isOpen,
        position: point.position,
        color: point.color
      }),
      renderContent: ({
        onClose
      }) => (0, _react.createElement)(_dropdownContentWrapper.default, {
        paddingSize: "none"
      }, (0, _react.createElement)(_colorPicker.ColorPicker, {
        enableAlpha: !disableAlpha,
        color: point.color,
        onChange: color => {
          onChange((0, _utils.updateControlPointColor)(controlPoints, index, (0, _colord.colord)(color).toRgbString()));
        }
      }), !disableRemove && controlPoints.length > 2 && (0, _react.createElement)(_hStack.HStack, {
        className: "components-custom-gradient-picker__remove-control-point-wrapper",
        alignment: "center"
      }, (0, _react.createElement)(_button.default, {
        onClick: () => {
          onChange((0, _utils.removeControlPoint)(controlPoints, index));
          onClose();
        },
        variant: "link"
      }, (0, _i18n.__)('Remove Control Point')))),
      style: {
        left: `${point.position}%`,
        transform: 'translateX( -50% )'
      }
    });
  }));
}
function InsertPoint({
  value: controlPoints,
  onChange,
  onOpenInserter,
  onCloseInserter,
  insertPosition,
  disableAlpha,
  __experimentalIsRenderedInSidebar
}) {
  const [alreadyInsertedPoint, setAlreadyInsertedPoint] = (0, _element.useState)(false);
  return (0, _react.createElement)(GradientColorPickerDropdown, {
    isRenderedInSidebar: __experimentalIsRenderedInSidebar,
    className: "components-custom-gradient-picker__inserter",
    onClose: () => {
      onCloseInserter();
    },
    renderToggle: ({
      isOpen,
      onToggle
    }) => (0, _react.createElement)(_button.default, {
      "aria-expanded": isOpen,
      "aria-haspopup": "true",
      onClick: () => {
        if (isOpen) {
          onCloseInserter();
        } else {
          setAlreadyInsertedPoint(false);
          onOpenInserter();
        }
        onToggle();
      },
      className: "components-custom-gradient-picker__insert-point-dropdown",
      icon: _icons.plus
    }),
    renderContent: () => (0, _react.createElement)(_dropdownContentWrapper.default, {
      paddingSize: "none"
    }, (0, _react.createElement)(_colorPicker.ColorPicker, {
      enableAlpha: !disableAlpha,
      onChange: color => {
        if (!alreadyInsertedPoint) {
          onChange((0, _utils.addControlPoint)(controlPoints, insertPosition, (0, _colord.colord)(color).toRgbString()));
          setAlreadyInsertedPoint(true);
        } else {
          onChange((0, _utils.updateControlPointColorByPosition)(controlPoints, insertPosition, (0, _colord.colord)(color).toRgbString()));
        }
      }
    })),
    style: insertPosition !== null ? {
      left: `${insertPosition}%`,
      transform: 'translateX( -50% )'
    } : undefined
  });
}
ControlPoints.InsertPoint = InsertPoint;
var _default = ControlPoints;
exports.default = _default;
//# sourceMappingURL=control-points.js.map