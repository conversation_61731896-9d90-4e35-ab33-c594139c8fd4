{"version": 3, "names": ["_i18n", "require", "_button", "_interopRequireDefault", "_icons", "PageControl", "currentPage", "numberOfPages", "setCurrentPage", "_react", "createElement", "className", "__", "Array", "from", "length", "map", "_", "page", "key", "undefined", "default", "icon", "PageControlIcon", "sprintf", "onClick"], "sources": ["@wordpress/components/src/guide/page-control.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __, sprintf } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport Button from '../button';\nimport { PageControlIcon } from './icons';\nimport type { PageControlProps } from './types';\n\nexport default function PageControl( {\n\tcurrentPage,\n\tnumberOfPages,\n\tsetCurrentPage,\n}: PageControlProps ) {\n\treturn (\n\t\t<ul\n\t\t\tclassName=\"components-guide__page-control\"\n\t\t\taria-label={ __( 'Guide controls' ) }\n\t\t>\n\t\t\t{ Array.from( { length: numberOfPages } ).map( ( _, page ) => (\n\t\t\t\t<li\n\t\t\t\t\tkey={ page }\n\t\t\t\t\t// Set aria-current=\"step\" on the active page, see https://www.w3.org/TR/wai-aria-1.1/#aria-current\n\t\t\t\t\taria-current={ page === currentPage ? 'step' : undefined }\n\t\t\t\t>\n\t\t\t\t\t<Button\n\t\t\t\t\t\tkey={ page }\n\t\t\t\t\t\ticon={ <PageControlIcon /> }\n\t\t\t\t\t\taria-label={ sprintf(\n\t\t\t\t\t\t\t/* translators: 1: current page number 2: total number of pages */\n\t\t\t\t\t\t\t__( 'Page %1$d of %2$d' ),\n\t\t\t\t\t\t\tpage + 1,\n\t\t\t\t\t\t\tnumberOfPages\n\t\t\t\t\t\t) }\n\t\t\t\t\t\tonClick={ () => setCurrentPage( page ) }\n\t\t\t\t\t/>\n\t\t\t\t</li>\n\t\t\t) ) }\n\t\t</ul>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AAKe,SAASI,WAAWA,CAAE;EACpCC,WAAW;EACXC,aAAa;EACbC;AACiB,CAAC,EAAG;EACrB,OACC,IAAAC,MAAA,CAAAC,aAAA;IACCC,SAAS,EAAC,gCAAgC;IAC1C,cAAa,IAAAC,QAAE,EAAE,gBAAiB;EAAG,GAEnCC,KAAK,CAACC,IAAI,CAAE;IAAEC,MAAM,EAAER;EAAc,CAAE,CAAC,CAACS,GAAG,CAAE,CAAEC,CAAC,EAAEC,IAAI,KACvD,IAAAT,MAAA,CAAAC,aAAA;IACCS,GAAG,EAAGD;IACN;IAAA;IACA,gBAAeA,IAAI,KAAKZ,WAAW,GAAG,MAAM,GAAGc;EAAW,GAE1D,IAAAX,MAAA,CAAAC,aAAA,EAACR,OAAA,CAAAmB,OAAM;IACNF,GAAG,EAAGD,IAAM;IACZI,IAAI,EAAG,IAAAb,MAAA,CAAAC,aAAA,EAACN,MAAA,CAAAmB,eAAe,MAAE,CAAG;IAC5B,cAAa,IAAAC,aAAO,GACnB;IACA,IAAAZ,QAAE,EAAE,mBAAoB,CAAC,EACzBM,IAAI,GAAG,CAAC,EACRX,aACD,CAAG;IACHkB,OAAO,EAAGA,CAAA,KAAMjB,cAAc,CAAEU,IAAK;EAAG,CACxC,CACE,CACH,CACC,CAAC;AAEP"}