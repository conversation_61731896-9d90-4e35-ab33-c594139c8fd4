"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = FocalPointPickerGrid;
var _react = require("react");
var _focalPointPickerStyle = require("./styles/focal-point-picker-style");
/**
 * Internal dependencies
 */

function FocalPointPickerGrid({
  bounds,
  ...props
}) {
  return (0, _react.createElement)(_focalPointPickerStyle.GridView, {
    ...props,
    className: "components-focal-point-picker__grid",
    style: {
      width: bounds.width,
      height: bounds.height
    }
  }, (0, _react.createElement)(_focalPointPickerStyle.GridLineX, {
    style: {
      top: '33%'
    }
  }), (0, _react.createElement)(_focalPointPickerStyle.GridLineX, {
    style: {
      top: '66%'
    }
  }), (0, _react.createElement)(_focalPointPickerStyle.GridLineY, {
    style: {
      left: '33%'
    }
  }), (0, _react.createElement)(_focalPointPickerStyle.GridLineY, {
    style: {
      left: '66%'
    }
  }));
}
//# sourceMappingURL=grid.js.map