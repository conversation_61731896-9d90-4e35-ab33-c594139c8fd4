!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("valtio/vanilla")):"function"==typeof define&&define.amd?define(["exports","valtio/vanilla"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).valtioUtils={},t.valtioVanilla)}(this,(function(t,e){"use strict";var n;var r=Symbol();var i=new WeakMap,a=new WeakMap,o=function t(e,n){var r=i.get(e);r&&(r[0].forEach((function(n){var r=n.d;e!==r&&t(r)})),++r[2],n&&r[3].add(n))},u=function t(e){var n=i.get(e);n&&(--n[2],n[2]||(n[3].forEach((function(t){return t()})),n[3].clear()),n[0].forEach((function(n){var r=n.d;e!==r&&t(r)})))},s=function(t){var n=t.s,r=t.d,s=a.get(r);s||(s=[new Set],a.set(t.d,s)),s[0].add(t);var c=i.get(n);if(!c){var f=new Set,l=e.subscribe(n,(function(t){f.forEach((function(e){var r=e.d,i=e.c,a=e.n,s=e.i;n===r&&t.every((function(t){return 1===t[1].length&&s.includes(t[1][0])}))||e.p||(o(n,i),a?u(n):e.p=Promise.resolve().then((function(){delete e.p,u(n)})))}))}),!0);c=[f,l,0,new Set],i.set(n,c)}c[0].add(t)},c=function(t){var e=t.s,n=t.d,r=a.get(n);null==r||r[0].delete(t),0===(null==r?void 0:r[0].size)&&a.delete(n);var o=i.get(e);if(o){var u=o[0],s=o[1];u.delete(t),u.size||(s(),i.delete(e))}},f=function(t){var e=a.get(t);return e?Array.from(e[0]):[]},l={add:s,remove:c,list:f};function d(t,n){var r=(null==n?void 0:n.proxy)||e.proxy({}),a=!(null==n||!n.sync),o=Object.keys(t);return o.forEach((function(n){if(Object.getOwnPropertyDescriptor(r,n))throw new Error("object property already defined");var u=t[n],f=null;!function t(){if(f){if(Array.from(f).map((function(e){var n,r,a,o=e[0];return n=o,r=t,!(null==(a=i.get(n))||!a[2]||(a[3].add(r),0))})).some((function(t){return t})))return;if(Array.from(f).every((function(t){var n=t[0],r=t[1];return e.getVersion(n)===r.v})))return}var l=new Map,d=u((function(t){return l.set(t,{v:e.getVersion(t)}),t})),p=function(){var e;l.forEach((function(e,i){var u,c,l=null==(u=f)||null==(c=u.get(i))?void 0:c.s;if(l)e.s=l;else{var d={s:i,d:r,k:n,c:t,n:a,i:o};s(d),e.s=d}})),null==(e=f)||e.forEach((function(t,e){!l.has(e)&&t.s&&c(t.s)})),f=l};d instanceof Promise?d.finally(p):p(),r[n]=d}()})),r}var p=function t(e){if("object"!=typeof(n=e)||null===n)return e;var n,r=Array.isArray(e)?[]:Object.create(Object.getPrototypeOf(e));return Reflect.ownKeys(e).forEach((function(n){r[n]=t(e[n])})),r};function h(t,e){for(var n in e){(a=e[n]).configurable=a.enumerable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,n,a)}if(Object.getOwnPropertySymbols)for(var r=Object.getOwnPropertySymbols(e),i=0;i<r.length;i++){var a,o=r[i];(a=e[o]).configurable=a.enumerable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,o,a)}return t}t.addComputed=function(t,e,n){void 0===n&&(n=t),console.warn("addComputed is deprecated. Please consider using `derive` or `proxyWithComputed` instead. Falling back to emulation with derive. https://github.com/pmndrs/valtio/pull/201");var r={};return Object.keys(e).forEach((function(n){r[n]=function(r){return e[n](r(t))}})),d(r,{proxy:n})},t.derive=d,t.devtools=function(t,n){"string"==typeof n&&(console.warn("string name option is deprecated, use { name }. https://github.com/pmndrs/valtio/pull/400"),n={name:n});var i,a=n||{},o=a.enabled,u=a.name,s=void 0===u?"":u;try{i=null!=o&&o&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(t){}if(i){var c=!1,f=i.connect({name:s}),l=e.subscribe(t,(function(n){var i=n.filter((function(t){return t[0],t[1][0]!==r})).map((function(t){return t[0]+":"+t[1].map(String).join(".")})).join(", ");if(i)if(c)c=!1;else{var a=Object.assign({},e.snapshot(t));delete a[r],f.send({type:i,updatedAt:(new Date).toLocaleString()},a)}})),d=f.subscribe((function(n){var i,a;if("ACTION"===n.type&&n.payload)try{Object.assign(t,JSON.parse(n.payload))}catch(t){console.error("please dispatch a serializable value that JSON.parse() and proxy() support\n",t)}if("DISPATCH"===n.type&&n.state){var o,u;if("JUMP_TO_ACTION"===(null==(o=n.payload)?void 0:o.type)||"JUMP_TO_STATE"===(null==(u=n.payload)?void 0:u.type)){c=!0;var s=JSON.parse(n.state);Object.assign(t,s)}t[r]=n}else if("DISPATCH"===n.type&&"COMMIT"===(null==(i=n.payload)?void 0:i.type))f.init(e.snapshot(t));else if("DISPATCH"===n.type&&"IMPORT_STATE"===(null==(a=n.payload)?void 0:a.type)){var l,d,p=null==(l=n.payload.nextLiftedState)?void 0:l.actionsById,h=(null==(d=n.payload.nextLiftedState)?void 0:d.computedStates)||[];c=!0,h.forEach((function(n,r){var i=n.state,a=p[r]||"No action found";Object.assign(t,i),0===r?f.init(e.snapshot(t)):f.send(a,e.snapshot(t))}))}}));return f.init(e.snapshot(t)),function(){l(),null==d||d()}}},t.proxyMap=function(t){var n,r,i,a=e.proxy((r={data:Array.from(t||[]),has:function(t){return this.data.some((function(e){return e[0]===t}))},set:function(t,e){var n=this.data.find((function(e){return e[0]===t}));return n?n[1]=e:this.data.push([t,e]),this},get:function(t){var e;return null==(e=this.data.find((function(e){return e[0]===t})))?void 0:e[1]},delete:function(t){var e=this.data.findIndex((function(e){return e[0]===t}));return-1!==e&&(this.data.splice(e,1),!0)},clear:function(){this.data.splice(0)},get size(){return this.data.length},toJSON:function(){return{}},forEach:function(t){var e=this;this.data.forEach((function(n){t(n[1],n[0],e)}))},keys:function(){return this.data.map((function(t){return t[0]})).values()},values:function(){return this.data.map((function(t){return t[1]})).values()},entries:function(){return new Map(this.data).entries()}},(i={})[n=Symbol.toStringTag]=i[n]||{},i[n].get=function(){return"Map"},r[Symbol.iterator]=function(){return this.entries()},h(r,i),r));return Object.defineProperties(a,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(a),a},t.proxySet=function(t){var n,r,i,a=e.proxy((r={data:Array.from(new Set(t)),has:function(t){return-1!==this.data.indexOf(t)},add:function(t){var n=!1;return"object"==typeof t&&null!==t&&(n=-1!==this.data.indexOf(e.proxy(t))),-1!==this.data.indexOf(t)||n||this.data.push(t),this},delete:function(t){var e=this.data.indexOf(t);return-1!==e&&(this.data.splice(e,1),!0)},clear:function(){this.data.splice(0)},get size(){return this.data.length},forEach:function(t){var e=this;this.data.forEach((function(n){t(n,n,e)}))}},(i={})[n=Symbol.toStringTag]=i[n]||{},i[n].get=function(){return"Set"},r.toJSON=function(){return{}},r[Symbol.iterator]=function(){return this.data[Symbol.iterator]()},r.values=function(){return this.data.values()},r.keys=function(){return this.data.values()},r.entries=function(){return new Set(this.data).entries()},h(r,i),r));return Object.defineProperties(a,{data:{enumerable:!1},size:{enumerable:!1},toJSON:{enumerable:!1}}),Object.seal(a),a},t.proxyWithComputed=function(t,n){Object.keys(n).forEach((function(i){if(Object.getOwnPropertyDescriptor(t,i))throw new Error("object property already defined");var a=n[i],o="function"==typeof a?{get:a}:a,u=o.get,s=o.set,c={get:function(){return u(e.snapshot(r))}};s&&(c.set=function(t){return s(r,t)}),Object.defineProperty(t,i,c)}));var r=e.proxy(t);return r},t.proxyWithHistory=function(t,n){void 0===n&&(n=!1);var r=e.proxy({value:t,history:e.ref({wip:void 0,snapshots:[],index:-1}),canUndo:function(){return r.history.index>0},undo:function(){r.canUndo()&&(r.value=r.history.wip=p(r.history.snapshots[--r.history.index]))},canRedo:function(){return r.history.index<r.history.snapshots.length-1},redo:function(){r.canRedo()&&(r.value=r.history.wip=p(r.history.snapshots[++r.history.index]))},saveHistory:function(){r.history.snapshots.splice(r.history.index+1),r.history.snapshots.push(e.snapshot(r).value),++r.history.index},subscribe:function(){return e.subscribe(r,(function(t){t.every((function(t){return"value"===t[1][0]&&("set"!==t[0]||t[2]!==r.history.wip)}))&&r.saveHistory()}))}});return r.saveHistory(),n||r.subscribe(),r},t.subscribeKey=function(t,n,r,i){return e.subscribe(t,(function(e){e.some((function(t){return t[1][0]===n}))&&r(t[n])}),i)},t.underive=function(t,e){var n=null!=e&&e.delete?new Set:null;f(t).forEach((function(t){var r=t.k;null!=e&&e.keys&&!e.keys.includes(r)||(c(t),n&&n.add(r))})),n&&n.forEach((function(e){delete t[e]}))},t.unstable_deriveSubscriptions=l,t.watch=function(t,r){var i=!0,a=new Set,o=new Map,u=function(){i&&(i=!1,a.forEach((function(t){return t()})),a.clear(),o.forEach((function(t){return t()})),o.clear())};return n&&n.add(u),function u(){if(i){a.forEach((function(t){return t()})),a.clear();var s=new Set,c=n;n=a;try{var f=t((function(t){return s.add(t),t}));f&&a.add(f)}finally{n=c}o.forEach((function(t,e){s.has(e)?s.delete(e):(o.delete(e),t())})),s.forEach((function(t){var n=e.subscribe(t,u,null==r?void 0:r.sync);o.set(t,n)}))}}(),u},Object.defineProperty(t,"__esModule",{value:!0})}));
