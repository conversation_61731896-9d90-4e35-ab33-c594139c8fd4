"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  Elevation: true
};
Object.defineProperty(exports, "Elevation", {
  enumerable: true,
  get: function () {
    return _component.default;
  }
});
var _component = _interopRequireDefault(require("./component"));
var _hook = require("./hook");
Object.keys(_hook).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _hook[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _hook[key];
    }
  });
});
//# sourceMappingURL=index.js.map