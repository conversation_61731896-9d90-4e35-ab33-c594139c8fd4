{"version": 3, "names": ["_reactNative", "require", "_reactNativeLinearGradient", "_interopRequireDefault", "_colord", "_element", "_interopRequireWildcard", "_styleNative", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "SaturationValuePicker", "Component", "constructor", "props", "panResponder", "PanResponder", "create", "onStartShouldSetPanResponder", "onStartShouldSetPanResponderCapture", "onMoveShouldSetPanResponder", "onMoveShouldSetPanResponderCapture", "onPanResponderGrant", "evt", "gestureState", "onPress", "saturation", "value", "computeSatValPress", "dragStartValue", "nativeEvent", "fireDragEvent", "onPanResponderMove", "onPanResponderTerminationRequest", "onPanResponderRelease", "onPanResponderTerminate", "onShouldBlockNativeResponder", "normalizeValue", "computeSatValDrag", "dx", "dy", "size", "diffx", "width", "diffy", "height", "event", "locationX", "locationY", "eventName", "render", "sliderSize", "hue", "containerStyle", "borderRadius", "currentColor", "_react", "createElement", "View", "style", "styles", "panHandlers", "colors", "colord", "h", "s", "l", "toHex", "start", "x", "y", "end", "pointerEvents", "borderWidth", "backgroundColor", "transform", "translateX", "translateY", "exports"], "sources": ["@wordpress/components/src/color-picker/saturation-picker.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, PanResponder } from 'react-native';\nimport LinearGradient from 'react-native-linear-gradient';\nimport { colord } from 'colord';\n\n/**\n * WordPress dependencies\n */\nimport React, { Component } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.native.scss';\n\nexport default class SaturationValuePicker extends Component {\n\tconstructor( props ) {\n\t\tsuper( props );\n\n\t\tthis.panResponder = PanResponder.create( {\n\t\t\tonStartShouldSetPanResponder: () => true,\n\t\t\tonStartShouldSetPanResponderCapture: () => true,\n\t\t\tonMoveShouldSetPanResponder: () => true,\n\t\t\tonMoveShouldSetPanResponderCapture: () => true,\n\t\t\tonPanResponderGrant: ( evt, gestureState ) => {\n\t\t\t\tconst { onPress } = this.props;\n\t\t\t\tconst { saturation, value } = this.computeSatValPress( evt );\n\t\t\t\tthis.dragStartValue = {\n\t\t\t\t\tsaturation,\n\t\t\t\t\tvalue,\n\t\t\t\t};\n\n\t\t\t\tif ( onPress ) {\n\t\t\t\t\tonPress( {\n\t\t\t\t\t\t...this.computeSatValPress( evt ),\n\t\t\t\t\t\tnativeEvent: evt.nativeEvent,\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\tthis.fireDragEvent( 'onDragStart', gestureState );\n\t\t\t},\n\t\t\tonPanResponderMove: ( evt, gestureState ) => {\n\t\t\t\tthis.fireDragEvent( 'onDragMove', gestureState );\n\t\t\t},\n\t\t\tonPanResponderTerminationRequest: () => true,\n\t\t\tonPanResponderRelease: ( evt, gestureState ) => {\n\t\t\t\tthis.fireDragEvent( 'onDragEnd', gestureState );\n\t\t\t},\n\t\t\tonPanResponderTerminate: ( evt, gestureState ) => {\n\t\t\t\tthis.fireDragEvent( 'onDragTerminate', gestureState );\n\t\t\t},\n\t\t\tonShouldBlockNativeResponder: () => true,\n\t\t} );\n\t}\n\n\tnormalizeValue( value ) {\n\t\tif ( value < 0 ) return 0;\n\t\tif ( value > 1 ) return 1;\n\t\treturn value;\n\t}\n\n\tcomputeSatValDrag( gestureState ) {\n\t\tconst { dx, dy } = gestureState;\n\t\tconst { size } = this.props;\n\t\tconst { saturation, value } = this.dragStartValue;\n\t\tconst diffx = dx / size.width;\n\t\tconst diffy = dy / size.height;\n\t\treturn {\n\t\t\tsaturation: this.normalizeValue( saturation + diffx ),\n\t\t\tvalue: this.normalizeValue( value - diffy ),\n\t\t};\n\t}\n\n\tcomputeSatValPress( event ) {\n\t\tconst { nativeEvent } = event;\n\t\tconst { locationX, locationY } = nativeEvent;\n\t\tconst { size } = this.props;\n\t\treturn {\n\t\t\tsaturation: this.normalizeValue( locationX / size.width ),\n\t\t\tvalue: 1 - this.normalizeValue( locationY / size.height ),\n\t\t};\n\t}\n\n\tfireDragEvent( eventName, gestureState ) {\n\t\tconst { [ eventName ]: event } = this.props;\n\t\tif ( event ) {\n\t\t\tevent( {\n\t\t\t\t...this.computeSatValDrag( gestureState ),\n\t\t\t\tgestureState,\n\t\t\t} );\n\t\t}\n\t}\n\n\trender() {\n\t\tconst {\n\t\t\tsize,\n\t\t\tsliderSize = 24,\n\t\t\thue = 0,\n\t\t\tvalue = 1,\n\t\t\tsaturation = 1,\n\t\t\tcontainerStyle = {},\n\t\t\tborderRadius = 0,\n\t\t\tcurrentColor,\n\t\t} = this.props;\n\n\t\treturn (\n\t\t\t<View\n\t\t\t\tstyle={ [\n\t\t\t\t\tstyles[ 'hsv-container' ],\n\t\t\t\t\tcontainerStyle,\n\t\t\t\t\t{\n\t\t\t\t\t\theight: size.height + sliderSize,\n\t\t\t\t\t\twidth: size.width + sliderSize,\n\t\t\t\t\t},\n\t\t\t\t] }\n\t\t\t\t{ ...this.panResponder.panHandlers }\n\t\t\t>\n\t\t\t\t<LinearGradient\n\t\t\t\t\tstyle={ [\n\t\t\t\t\t\tstyles[ 'gradient-container' ],\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tborderRadius,\n\t\t\t\t\t\t},\n\t\t\t\t\t] }\n\t\t\t\t\tcolors={ [\n\t\t\t\t\t\t'#fff',\n\t\t\t\t\t\tcolord( { h: hue, s: 100, l: 50 } ).toHex(),\n\t\t\t\t\t] }\n\t\t\t\t\tstart={ { x: 0, y: 0.5 } }\n\t\t\t\t\tend={ { x: 1, y: 0.5 } }\n\t\t\t\t>\n\t\t\t\t\t<LinearGradient colors={ [ 'rgba(0, 0, 0, 0)', '#000' ] }>\n\t\t\t\t\t\t<View\n\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\theight: size.height,\n\t\t\t\t\t\t\t\twidth: size.width,\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t/>\n\t\t\t\t\t</LinearGradient>\n\t\t\t\t</LinearGradient>\n\t\t\t\t<View\n\t\t\t\t\tpointerEvents=\"none\"\n\t\t\t\t\tstyle={ [\n\t\t\t\t\t\tstyles[ 'saturation-slider' ],\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\twidth: sliderSize,\n\t\t\t\t\t\t\theight: sliderSize,\n\t\t\t\t\t\t\tborderRadius: sliderSize / 2,\n\t\t\t\t\t\t\tborderWidth: sliderSize / 10,\n\t\t\t\t\t\t\tbackgroundColor: currentColor,\n\t\t\t\t\t\t\ttransform: [\n\t\t\t\t\t\t\t\t{ translateX: size.width * saturation },\n\t\t\t\t\t\t\t\t{ translateY: size.height * ( 1 - value ) },\n\t\t\t\t\t\t\t],\n\t\t\t\t\t\t},\n\t\t\t\t\t] }\n\t\t\t\t/>\n\t\t\t</View>\n\t\t);\n\t}\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,0BAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAKA,IAAAI,QAAA,GAAAC,uBAAA,CAAAL,OAAA;AAKA,IAAAM,YAAA,GAAAJ,sBAAA,CAAAF,OAAA;AAAyC,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAfzC;AACA;AACA;;AAKA;AACA;AACA;;AAGA;AACA;AACA;;AAGe,MAAMW,qBAAqB,SAASC,kBAAS,CAAC;EAC5DC,WAAWA,CAAEC,KAAK,EAAG;IACpB,KAAK,CAAEA,KAAM,CAAC;IAEd,IAAI,CAACC,YAAY,GAAGC,yBAAY,CAACC,MAAM,CAAE;MACxCC,4BAA4B,EAAEA,CAAA,KAAM,IAAI;MACxCC,mCAAmC,EAAEA,CAAA,KAAM,IAAI;MAC/CC,2BAA2B,EAAEA,CAAA,KAAM,IAAI;MACvCC,kCAAkC,EAAEA,CAAA,KAAM,IAAI;MAC9CC,mBAAmB,EAAEA,CAAEC,GAAG,EAAEC,YAAY,KAAM;QAC7C,MAAM;UAAEC;QAAQ,CAAC,GAAG,IAAI,CAACX,KAAK;QAC9B,MAAM;UAAEY,UAAU;UAAEC;QAAM,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAAEL,GAAI,CAAC;QAC5D,IAAI,CAACM,cAAc,GAAG;UACrBH,UAAU;UACVC;QACD,CAAC;QAED,IAAKF,OAAO,EAAG;UACdA,OAAO,CAAE;YACR,GAAG,IAAI,CAACG,kBAAkB,CAAEL,GAAI,CAAC;YACjCO,WAAW,EAAEP,GAAG,CAACO;UAClB,CAAE,CAAC;QACJ;QAEA,IAAI,CAACC,aAAa,CAAE,aAAa,EAAEP,YAAa,CAAC;MAClD,CAAC;MACDQ,kBAAkB,EAAEA,CAAET,GAAG,EAAEC,YAAY,KAAM;QAC5C,IAAI,CAACO,aAAa,CAAE,YAAY,EAAEP,YAAa,CAAC;MACjD,CAAC;MACDS,gCAAgC,EAAEA,CAAA,KAAM,IAAI;MAC5CC,qBAAqB,EAAEA,CAAEX,GAAG,EAAEC,YAAY,KAAM;QAC/C,IAAI,CAACO,aAAa,CAAE,WAAW,EAAEP,YAAa,CAAC;MAChD,CAAC;MACDW,uBAAuB,EAAEA,CAAEZ,GAAG,EAAEC,YAAY,KAAM;QACjD,IAAI,CAACO,aAAa,CAAE,iBAAiB,EAAEP,YAAa,CAAC;MACtD,CAAC;MACDY,4BAA4B,EAAEA,CAAA,KAAM;IACrC,CAAE,CAAC;EACJ;EAEAC,cAAcA,CAAEV,KAAK,EAAG;IACvB,IAAKA,KAAK,GAAG,CAAC,EAAG,OAAO,CAAC;IACzB,IAAKA,KAAK,GAAG,CAAC,EAAG,OAAO,CAAC;IACzB,OAAOA,KAAK;EACb;EAEAW,iBAAiBA,CAAEd,YAAY,EAAG;IACjC,MAAM;MAAEe,EAAE;MAAEC;IAAG,CAAC,GAAGhB,YAAY;IAC/B,MAAM;MAAEiB;IAAK,CAAC,GAAG,IAAI,CAAC3B,KAAK;IAC3B,MAAM;MAAEY,UAAU;MAAEC;IAAM,CAAC,GAAG,IAAI,CAACE,cAAc;IACjD,MAAMa,KAAK,GAAGH,EAAE,GAAGE,IAAI,CAACE,KAAK;IAC7B,MAAMC,KAAK,GAAGJ,EAAE,GAAGC,IAAI,CAACI,MAAM;IAC9B,OAAO;MACNnB,UAAU,EAAE,IAAI,CAACW,cAAc,CAAEX,UAAU,GAAGgB,KAAM,CAAC;MACrDf,KAAK,EAAE,IAAI,CAACU,cAAc,CAAEV,KAAK,GAAGiB,KAAM;IAC3C,CAAC;EACF;EAEAhB,kBAAkBA,CAAEkB,KAAK,EAAG;IAC3B,MAAM;MAAEhB;IAAY,CAAC,GAAGgB,KAAK;IAC7B,MAAM;MAAEC,SAAS;MAAEC;IAAU,CAAC,GAAGlB,WAAW;IAC5C,MAAM;MAAEW;IAAK,CAAC,GAAG,IAAI,CAAC3B,KAAK;IAC3B,OAAO;MACNY,UAAU,EAAE,IAAI,CAACW,cAAc,CAAEU,SAAS,GAAGN,IAAI,CAACE,KAAM,CAAC;MACzDhB,KAAK,EAAE,CAAC,GAAG,IAAI,CAACU,cAAc,CAAEW,SAAS,GAAGP,IAAI,CAACI,MAAO;IACzD,CAAC;EACF;EAEAd,aAAaA,CAAEkB,SAAS,EAAEzB,YAAY,EAAG;IACxC,MAAM;MAAE,CAAEyB,SAAS,GAAIH;IAAM,CAAC,GAAG,IAAI,CAAChC,KAAK;IAC3C,IAAKgC,KAAK,EAAG;MACZA,KAAK,CAAE;QACN,GAAG,IAAI,CAACR,iBAAiB,CAAEd,YAAa,CAAC;QACzCA;MACD,CAAE,CAAC;IACJ;EACD;EAEA0B,MAAMA,CAAA,EAAG;IACR,MAAM;MACLT,IAAI;MACJU,UAAU,GAAG,EAAE;MACfC,GAAG,GAAG,CAAC;MACPzB,KAAK,GAAG,CAAC;MACTD,UAAU,GAAG,CAAC;MACd2B,cAAc,GAAG,CAAC,CAAC;MACnBC,YAAY,GAAG,CAAC;MAChBC;IACD,CAAC,GAAG,IAAI,CAACzC,KAAK;IAEd,OACC,IAAA0C,MAAA,CAAAC,aAAA,EAAC5E,YAAA,CAAA6E,IAAI;MACJC,KAAK,EAAG,CACPC,oBAAM,CAAE,eAAe,CAAE,EACzBP,cAAc,EACd;QACCR,MAAM,EAAEJ,IAAI,CAACI,MAAM,GAAGM,UAAU;QAChCR,KAAK,EAAEF,IAAI,CAACE,KAAK,GAAGQ;MACrB,CAAC,CACC;MAAA,GACE,IAAI,CAACpC,YAAY,CAAC8C;IAAW,GAElC,IAAAL,MAAA,CAAAC,aAAA,EAAC1E,0BAAA,CAAAa,OAAc;MACd+D,KAAK,EAAG,CACPC,oBAAM,CAAE,oBAAoB,CAAE,EAC9B;QACCN;MACD,CAAC,CACC;MACHQ,MAAM,EAAG,CACR,MAAM,EACN,IAAAC,cAAM,EAAE;QAAEC,CAAC,EAAEZ,GAAG;QAAEa,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAG,CAAE,CAAC,CAACC,KAAK,CAAC,CAAC,CACzC;MACHC,KAAK,EAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAI,CAAG;MAC1BC,GAAG,EAAG;QAAEF,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAI;IAAG,GAExB,IAAAd,MAAA,CAAAC,aAAA,EAAC1E,0BAAA,CAAAa,OAAc;MAACkE,MAAM,EAAG,CAAE,kBAAkB,EAAE,MAAM;IAAI,GACxD,IAAAN,MAAA,CAAAC,aAAA,EAAC5E,YAAA,CAAA6E,IAAI;MACJC,KAAK,EAAG;QACPd,MAAM,EAAEJ,IAAI,CAACI,MAAM;QACnBF,KAAK,EAAEF,IAAI,CAACE;MACb;IAAG,CACH,CACc,CACD,CAAC,EACjB,IAAAa,MAAA,CAAAC,aAAA,EAAC5E,YAAA,CAAA6E,IAAI;MACJc,aAAa,EAAC,MAAM;MACpBb,KAAK,EAAG,CACPC,oBAAM,CAAE,mBAAmB,CAAE,EAC7B;QACCjB,KAAK,EAAEQ,UAAU;QACjBN,MAAM,EAAEM,UAAU;QAClBG,YAAY,EAAEH,UAAU,GAAG,CAAC;QAC5BsB,WAAW,EAAEtB,UAAU,GAAG,EAAE;QAC5BuB,eAAe,EAAEnB,YAAY;QAC7BoB,SAAS,EAAE,CACV;UAAEC,UAAU,EAAEnC,IAAI,CAACE,KAAK,GAAGjB;QAAW,CAAC,EACvC;UAAEmD,UAAU,EAAEpC,IAAI,CAACI,MAAM,IAAK,CAAC,GAAGlB,KAAK;QAAG,CAAC;MAE7C,CAAC;IACC,CACH,CACI,CAAC;EAET;AACD;AAACmD,OAAA,CAAAlF,OAAA,GAAAe,qBAAA"}