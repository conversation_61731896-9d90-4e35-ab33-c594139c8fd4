{"version": 3, "names": ["_colord", "require", "_memize", "_interopRequireDefault", "_element", "isLegacyProps", "props", "onChangeComplete", "disable<PERSON><PERSON>pha", "color", "hex", "getColorFromLegacyProps", "undefined", "transformColorStringToLegacyColor", "memoize", "colordColor", "colord", "toHex", "rgb", "toRgb", "hsv", "toHsv", "hsl", "toHsl", "source", "oldHue", "h", "useDeprecatedProps", "legacyChangeHandler", "useCallback", "enableAlpha", "onChange"], "sources": ["@wordpress/components/src/color-picker/use-deprecated-props.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { colord } from 'colord';\nimport memoize from 'memize';\n\n/**\n * WordPress dependencies\n */\nimport { useCallback } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { ColorPickerProps, LegacyColor, LegacyProps } from './types';\n\nfunction isLegacyProps( props: any ): props is LegacyProps {\n\treturn (\n\t\ttypeof props.onChangeComplete !== 'undefined' ||\n\t\ttypeof props.disableAlpha !== 'undefined' ||\n\t\ttypeof props.color?.hex === 'string'\n\t);\n}\n\nfunction getColorFromLegacyProps(\n\tcolor: LegacyProps[ 'color' ]\n): string | undefined {\n\tif ( color === undefined ) return;\n\n\tif ( typeof color === 'string' ) return color;\n\n\tif ( color.hex ) return color.hex;\n\n\treturn undefined;\n}\n\nconst transformColorStringToLegacyColor = memoize(\n\t( color: string ): LegacyColor => {\n\t\tconst colordColor = colord( color );\n\t\tconst hex = colordColor.toHex();\n\t\tconst rgb = colordColor.toRgb();\n\t\tconst hsv = colordColor.toHsv();\n\t\tconst hsl = colordColor.toHsl();\n\n\t\treturn {\n\t\t\thex,\n\t\t\trgb,\n\t\t\thsv,\n\t\t\thsl,\n\t\t\tsource: 'hex',\n\t\t\toldHue: hsl.h,\n\t\t};\n\t}\n);\n\nexport function useDeprecatedProps(\n\tprops: LegacyProps | ColorPickerProps\n): ColorPickerProps {\n\tconst { onChangeComplete } = props as LegacyProps;\n\tconst legacyChangeHandler = useCallback(\n\t\t( color: string ) => {\n\t\t\tonChangeComplete( transformColorStringToLegacyColor( color ) );\n\t\t},\n\t\t[ onChangeComplete ]\n\t);\n\tif ( isLegacyProps( props ) ) {\n\t\treturn {\n\t\t\tcolor: getColorFromLegacyProps( props.color ),\n\t\t\tenableAlpha: ! props.disableAlpha,\n\t\t\tonChange: legacyChangeHandler,\n\t\t};\n\t}\n\treturn {\n\t\t...props,\n\t\tcolor: props.color as ColorPickerProps[ 'color' ],\n\t\tenableAlpha: ( props as ColorPickerProps ).enableAlpha,\n\t\tonChange: ( props as ColorPickerProps ).onChange,\n\t};\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,QAAA,GAAAH,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AAQA,SAASI,aAAaA,CAAEC,KAAU,EAAyB;EAC1D,OACC,OAAOA,KAAK,CAACC,gBAAgB,KAAK,WAAW,IAC7C,OAAOD,KAAK,CAACE,YAAY,KAAK,WAAW,IACzC,OAAOF,KAAK,CAACG,KAAK,EAAEC,GAAG,KAAK,QAAQ;AAEtC;AAEA,SAASC,uBAAuBA,CAC/BF,KAA6B,EACR;EACrB,IAAKA,KAAK,KAAKG,SAAS,EAAG;EAE3B,IAAK,OAAOH,KAAK,KAAK,QAAQ,EAAG,OAAOA,KAAK;EAE7C,IAAKA,KAAK,CAACC,GAAG,EAAG,OAAOD,KAAK,CAACC,GAAG;EAEjC,OAAOE,SAAS;AACjB;AAEA,MAAMC,iCAAiC,GAAG,IAAAC,eAAO,EAC9CL,KAAa,IAAmB;EACjC,MAAMM,WAAW,GAAG,IAAAC,cAAM,EAAEP,KAAM,CAAC;EACnC,MAAMC,GAAG,GAAGK,WAAW,CAACE,KAAK,CAAC,CAAC;EAC/B,MAAMC,GAAG,GAAGH,WAAW,CAACI,KAAK,CAAC,CAAC;EAC/B,MAAMC,GAAG,GAAGL,WAAW,CAACM,KAAK,CAAC,CAAC;EAC/B,MAAMC,GAAG,GAAGP,WAAW,CAACQ,KAAK,CAAC,CAAC;EAE/B,OAAO;IACNb,GAAG;IACHQ,GAAG;IACHE,GAAG;IACHE,GAAG;IACHE,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH,GAAG,CAACI;EACb,CAAC;AACF,CACD,CAAC;AAEM,SAASC,kBAAkBA,CACjCrB,KAAqC,EAClB;EACnB,MAAM;IAAEC;EAAiB,CAAC,GAAGD,KAAoB;EACjD,MAAMsB,mBAAmB,GAAG,IAAAC,oBAAW,EACpCpB,KAAa,IAAM;IACpBF,gBAAgB,CAAEM,iCAAiC,CAAEJ,KAAM,CAAE,CAAC;EAC/D,CAAC,EACD,CAAEF,gBAAgB,CACnB,CAAC;EACD,IAAKF,aAAa,CAAEC,KAAM,CAAC,EAAG;IAC7B,OAAO;MACNG,KAAK,EAAEE,uBAAuB,CAAEL,KAAK,CAACG,KAAM,CAAC;MAC7CqB,WAAW,EAAE,CAAExB,KAAK,CAACE,YAAY;MACjCuB,QAAQ,EAAEH;IACX,CAAC;EACF;EACA,OAAO;IACN,GAAGtB,KAAK;IACRG,KAAK,EAAEH,KAAK,CAACG,KAAoC;IACjDqB,WAAW,EAAIxB,KAAK,CAAuBwB,WAAW;IACtDC,QAAQ,EAAIzB,KAAK,CAAuByB;EACzC,CAAC;AACF"}