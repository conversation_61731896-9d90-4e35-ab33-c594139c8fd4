"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _reactNativeVideo = _interopRequireDefault(require("react-native-video"));
var _primitives = require("@wordpress/primitives");
var _components = require("@wordpress/components");
var _compose = require("@wordpress/compose");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _reactNativeBridge = require("@wordpress/react-native-bridge");
var _url = require("@wordpress/url");
var _element = require("@wordpress/element");
var _styles = _interopRequireDefault(require("./styles.scss"));
var _audioUrlParser = require("./audio-url-parser.native");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const isIOS = _reactNative.Platform.OS === 'ios';
function Player({
  isUploadInProgress,
  isUploadFailed,
  attributes,
  isSelected
}) {
  const {
    id,
    src
  } = attributes;
  const [paused, setPaused] = (0, _element.useState)(true);
  const onPressListen = () => {
    if (src) {
      if (isIOS && this.player) {
        this.player.presentFullscreenPlayer();
        return;
      }
      _reactNative.Linking.canOpenURL(src).then(supported => {
        if (!supported) {
          _reactNative.Alert.alert((0, _i18n.__)('Problem opening the audio'), (0, _i18n.__)('No application can handle this request.'));
        } else {
          return _reactNative.Linking.openURL(src);
        }
      }).catch(() => {
        _reactNative.Alert.alert((0, _i18n.__)('Problem opening the audio'), (0, _i18n.__)('An unknown error occurred. Please try again.'));
      });
    }
  };
  const containerStyle = (0, _components.useEditorColorScheme)(_styles.default.container, _styles.default.containerDark);
  const iconStyle = (0, _components.useEditorColorScheme)(_styles.default.icon, _styles.default.iconDark);
  const iconDisabledStyle = (0, _components.useEditorColorScheme)(_styles.default.iconDisabled, _styles.default.iconDisabledDark);
  const isDisabled = isUploadFailed || isUploadInProgress;
  const finalIconStyle = {
    ...iconStyle,
    ...(isDisabled && iconDisabledStyle)
  };
  const iconContainerStyle = (0, _components.useEditorColorScheme)(_styles.default.iconContainer, _styles.default.iconContainerDark);
  const titleContainerStyle = {
    ..._styles.default.titleContainer,
    ...(isIOS ? _styles.default.titleContainerIOS : _styles.default.titleContainerAndroid)
  };
  const titleStyle = (0, _components.useEditorColorScheme)(_styles.default.title, _styles.default.titleDark);
  const uploadFailedStyle = (0, _components.useEditorColorScheme)(_styles.default.uploadFailed, _styles.default.uploadFailedDark);
  const subtitleStyle = (0, _components.useEditorColorScheme)(_styles.default.subtitle, _styles.default.subtitleDark);
  const finalSubtitleStyle = {
    ...subtitleStyle,
    ...(isUploadFailed && uploadFailedStyle)
  };
  const buttonBackgroundStyle = (0, _components.useEditorColorScheme)(_styles.default.buttonBackground, _styles.default.buttonBackgroundDark);
  let title = '';
  let extension = '';
  if (src) {
    const result = (0, _audioUrlParser.parseAudioUrl)(src);
    extension = result.extension;
    title = result.title;
  }
  const getSubtitleValue = () => {
    if (isUploadInProgress) {
      return (0, _i18n.__)('Uploading…');
    }
    if (isUploadFailed) {
      return (0, _i18n.__)('Failed to insert audio file. Please tap for options.');
    }
    return extension +
    // translators: displays audio file extension. e.g. MP3 audio file
    (0, _i18n.__)('audio file');
  };
  function onAudioUploadCancelDialog() {
    if (isUploadInProgress) {
      (0, _reactNativeBridge.requestImageUploadCancelDialog)(id);
    } else if (id && (0, _url.getProtocol)(src) === 'file:') {
      (0, _reactNativeBridge.requestImageFailedRetryDialog)(id);
    }
  }
  return (0, _react.createElement)(_reactNative.TouchableWithoutFeedback, {
    accessible: !isSelected,
    disabled: !isSelected,
    onPress: onAudioUploadCancelDialog
  }, (0, _react.createElement)(_primitives.View, {
    style: containerStyle
  }, (0, _react.createElement)(_primitives.View, {
    style: iconContainerStyle
  }, (0, _react.createElement)(_components.Icon, {
    icon: _icons.audio,
    style: finalIconStyle,
    size: 24
  })), (0, _react.createElement)(_primitives.View, {
    style: titleContainerStyle
  }, (0, _react.createElement)(_reactNative.Text, {
    style: titleStyle
  }, title), (0, _react.createElement)(_primitives.View, {
    style: _styles.default.subtitleContainer
  }, isUploadFailed && (0, _react.createElement)(_components.Icon, {
    icon: _icons.warning,
    style: {
      ..._styles.default.errorIcon,
      ...uploadFailedStyle
    },
    size: 16
  }), (0, _react.createElement)(_reactNative.Text, {
    style: finalSubtitleStyle
  }, getSubtitleValue()))), !isDisabled && (0, _react.createElement)(_reactNative.TouchableWithoutFeedback, {
    accessibilityLabel: (0, _i18n.__)('Audio Player'),
    accessibilityRole: 'button',
    accessibilityHint: (0, _i18n.__)('Double tap to listen the audio file'),
    onPress: onPressListen
  }, (0, _react.createElement)(_primitives.View, {
    style: buttonBackgroundStyle
  }, (0, _react.createElement)(_reactNative.Text, {
    style: _styles.default.buttonText
  }, (0, _i18n.__)('OPEN')))), isIOS && (0, _react.createElement)(_reactNativeVideo.default, {
    source: {
      uri: src
    },
    paused: paused,
    ref: ref => {
      this.player = ref;
    },
    controls: false,
    ignoreSilentSwitch: 'ignore',
    onFullscreenPlayerWillPresent: () => {
      setPaused(false);
    },
    onFullscreenPlayerDidDismiss: () => {
      setPaused(true);
    }
  })));
}
var _default = (0, _compose.withPreferredColorScheme)(Player);
exports.default = _default;
//# sourceMappingURL=index.native.js.map