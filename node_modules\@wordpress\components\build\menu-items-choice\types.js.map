{"version": 3, "names": [], "sources": ["@wordpress/components/src/menu-items-choice/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { ShortcutProps } from '../shortcut/types';\nimport type { ButtonAsButtonProps } from '../button/types';\n\nexport type MenuItemsChoiceProps = {\n\t/**\n\t * Array of choices.\n\t *\n\t * @default []\n\t */\n\tchoices: readonly MenuItemChoice[];\n\t/**\n\t * Value of currently selected choice (should match a `value` property\n\t * from a choice in `choices`).\n\t */\n\tvalue: string;\n\t/**\n\t * Callback function to be called with the selected choice when user\n\t * selects a new choice.\n\t */\n\tonSelect( value: string ): void;\n\t/**\n\t * Callback function to be called with a choice when user\n\t * hovers over a new choice (will be empty on mouse leave).\n\t *\n\t * @default noop\n\t */\n\tonHover: ( value: string | null ) => void;\n};\n\nexport type MenuItemChoice = {\n\t/**\n\t * Human-readable label for choice.\n\t */\n\tlabel: string;\n\t/**\n\t * Unique value for choice.\n\t */\n\tvalue: string;\n\t/**\n\t * Whether the menu item is disabled.\n\t */\n\tdisabled?: ButtonAsButtonProps[ 'disabled' ];\n\t/**\n\t * Additional information which will be rendered below the given label.\n\t */\n\tinfo?: string;\n\t/**\n\t * Optional keyboard sequence to trigger choice with keyboard shortcut\n\t * (e.g. `ctrl+s`).\n\t */\n\tshortcut?: ShortcutProps[ 'shortcut' ];\n\t/**\n\t * Aria compliant label.\n\t */\n\t[ 'aria-label' ]?: string;\n};\n"], "mappings": ""}