"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _button = _interopRequireDefault(require("../../button"));
var _tooltip = _interopRequireDefault(require("../../tooltip"));
var _view = require("../../view");
var _context = require("../../context");
var _hook = require("./hook");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BorderBoxControlLinkedButton = (props, forwardedRef) => {
  const {
    className,
    isLinked,
    ...buttonProps
  } = (0, _hook.useBorderBoxControlLinkedButton)(props);
  const label = isLinked ? (0, _i18n.__)('Unlink sides') : (0, _i18n.__)('Link sides');
  return (0, _react.createElement)(_tooltip.default, {
    text: label
  }, (0, _react.createElement)(_view.View, {
    className: className
  }, (0, _react.createElement)(_button.default, {
    ...buttonProps,
    size: "small",
    icon: isLinked ? _icons.link : _icons.linkOff,
    iconSize: 24,
    "aria-label": label,
    ref: forwardedRef
  })));
};
const ConnectedBorderBoxControlLinkedButton = (0, _context.contextConnect)(BorderBoxControlLinkedButton, 'BorderBoxControlLinkedButton');
var _default = ConnectedBorderBoxControlLinkedButton;
exports.default = _default;
//# sourceMappingURL=component.js.map