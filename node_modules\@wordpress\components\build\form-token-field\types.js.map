{"version": 3, "names": [], "sources": ["@wordpress/components/src/form-token-field/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type {\n\tComponentPropsWithRef,\n\tMouseEventHandler,\n\tReactNode,\n\tFocusEvent,\n} from 'react';\n\ntype Messages = {\n\t/**\n\t * The user added a new token.\n\t */\n\tadded: string;\n\t/**\n\t * The user removed an existing token.\n\t */\n\tremoved: string;\n\t/**\n\t * The user focused the button to remove the token.\n\t */\n\tremove: string;\n\t/**\n\t * The user tried to add a token that didn't pass the validation.\n\t */\n\t__experimentalInvalid: string;\n};\n\nexport interface TokenItem {\n\t/**\n\t *  The value of the token.\n\t */\n\tvalue: string;\n\t/**\n\t * One of 'error', 'validating', or 'success'. Applies styles to token.\n\t */\n\tstatus?: 'error' | 'success' | 'validating';\n\t/**\n\t * If not falsey, will add a title to the token.\n\t */\n\ttitle?: string;\n\t/**\n\t * When true, renders tokens as without a background.\n\t */\n\tisBorderless?: boolean;\n\t/**\n\t * Function to call when onMouseEnter event triggered on token.\n\t */\n\tonMouseEnter?: MouseEventHandler< HTMLSpanElement >;\n\t/**\n\t *  Function to call when onMouseLeave is triggered on token.\n\t */\n\tonMouseLeave?: MouseEventHandler< HTMLSpanElement >;\n}\n\nexport interface FormTokenFieldProps\n\textends Pick<\n\t\tComponentPropsWithRef< 'input' >,\n\t\t'autoCapitalize' | 'autoComplete' | 'className'\n\t> {\n\tlabel?: string;\n\t/**\n\t * An array of strings to present to the user as suggested tokens.\n\t */\n\tsuggestions?: string[];\n\t/**\n\t * The maximum number of suggestions to display at a time.\n\t *\n\t * @default 100\n\t */\n\tmaxSuggestions?: number;\n\t/**\n\t * An array of strings or objects to display as tokens in the field.\n\t * If objects are present in the array, they **must** have a property of `value`.\n\t */\n\tvalue?: ( string | TokenItem )[];\n\t/**\n\t * Function to call to transform tokens for display.\n\t * (In the editor, this is needed to decode HTML entities embedded in tags\n\t * - otherwise entities like `&` in tag names are double-encoded like `&amp;`,\n\t * once by the REST API and once by React).\n\t */\n\tdisplayTransform?: ( token: string ) => string;\n\t/**\n\t * Function to call to transform tokens for saving. The default is to trim the token value.\n\t * This function is also applied when matching suggestions against the current value\n\t * so that matching works correctly with leading or trailing spaces. (In the editor,\n\t * this is needed to remove leading and trailing spaces from tag names, like wp-admin does.\n\t * Otherwise the REST API won't save them.)\n\t *\n\t * @default ( token: string ) => token.trim()\n\t */\n\tsaveTransform?: ( token: string ) => string;\n\t/**\n\t * Function to call when the tokens have changed. An array of new tokens is passed to the callback.\n\t *\n\t */\n\tonChange?: ( tokens: ( string | TokenItem )[] ) => void;\n\t/**\n\t * Function to call when the users types in the input field. It can be used to trigger autocomplete requests.\n\t *\n\t */\n\tonInputChange?: ( input: string ) => void;\n\t/**\n\t * Function to call when the TokenField has been focused on. The event is passed to the callback. Useful for analytics.\n\t *\n\t */\n\tonFocus?: ( event: FocusEvent ) => void;\n\t/**\n\t *  When true, renders tokens as without a background.\n\t */\n\tisBorderless?: boolean;\n\t/**\n\t * If passed, `TokenField` will disable ability to add new tokens once number of tokens is greater than or equal to `maxLength`.\n\t */\n\tmaxLength?: number;\n\t/**\n\t * When true, tokens are not able to be added or removed.\n\t */\n\tdisabled?: boolean;\n\t/**\n\t * If passed, the `TokenField` input will show a placeholder string if no value tokens are present.\n\t */\n\tplaceholder?: string;\n\t/**\n\t * If true, will add a token when `TokenField` is focused and `space` is pressed.\n\t */\n\ttokenizeOnSpace?: boolean;\n\t/**\n\t * Allows customizing the messages presented by screen readers in different occasions:\n\t *\n\t * -   `added`: The user added a new token.\n\t * -   `removed`: The user removed an existing token.\n\t * -   `remove` : The user focused the button to remove the token.\n\t * -   `__experimentalInvalid`: The user tried to add a token that didn't pass the validation.\n\t */\n\tmessages?: Messages;\n\t/**\n\t * If true, the suggestions list will be always expanded when the input field has the focus.\n\t */\n\t__experimentalExpandOnFocus?: boolean;\n\t/**\n\t * If passed, all introduced values will be validated before being added as tokens.\n\t *\n\t * @default () => true\n\t */\n\t__experimentalValidateInput?: ( token: string ) => boolean;\n\t/**\n\t * If false, the text on how to use the select (ie: _Separate with commas or the Enter key._) will be hidden.\n\t *\n\t * @default true\n\t */\n\t__experimentalShowHowTo?: boolean;\n\t/**\n\t * Deprecated. Use `__next40pxDefaultSize` instead.\n\t *\n\t * @default false\n\t * @deprecated\n\t */\n\t__next36pxDefaultSize?: boolean;\n\t/**\n\t * Start opting into the larger default height that will become the\n\t * default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n\t/**\n\t * If true, the select the first matching suggestion when the user presses\n\t * the Enter key (or space when tokenizeOnSpace is true).\n\t *\n\t * @default false\n\t */\n\t__experimentalAutoSelectFirstMatch?: boolean;\n\t/**\n\t * Custom renderer for suggestions.\n\t */\n\t__experimentalRenderItem?: ( args: { item: string } ) => ReactNode;\n\t/**\n\t * Start opting into the new margin-free styles that will become the default in a future version.\n\t *\n\t * @default false\n\t */\n\t__nextHasNoMarginBottom?: boolean;\n\t/**\n\t * If true, add any incompleteTokenValue as a new token when the field loses focus.\n\t *\n\t * @default false\n\t */\n\ttokenizeOnBlur?: boolean;\n}\n\n/**\n * `T` can be either a `string` or an object which must have a `value` prop as a string.\n */\nexport interface SuggestionsListProps<\n\tT = string | ( Record< string, unknown > & { value: string } ),\n> {\n\tselectedIndex: number;\n\tscrollIntoView: boolean;\n\tmatch: T;\n\tonHover: ( suggestion: T ) => void;\n\tonSelect: ( suggestion: T ) => void;\n\tsuggestions: T[];\n\tdisplayTransform: ( value: T ) => string;\n\tinstanceId: string | number;\n\t__experimentalRenderItem?: ( args: { item: T } ) => ReactNode;\n}\n\nexport interface TokenProps extends TokenItem {\n\tdisplayTransform: ( value: string ) => string;\n\tdisabled: boolean;\n\tonClickRemove: ( { value }: { value: string } ) => void;\n\tmessages: Messages;\n\ttermPosition: number;\n\ttermsCount: number;\n}\n\nexport interface TokenInputProps {\n\tisExpanded: boolean;\n\tinstanceId: string | number;\n\tselectedSuggestionIndex: number;\n\tonChange?: ( { value }: { value: string } ) => void;\n\tvalue: string;\n}\n"], "mappings": ""}