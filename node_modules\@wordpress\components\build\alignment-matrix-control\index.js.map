{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_i18n", "_compose", "_cell", "_v", "_alignmentMatrixControlStyles", "_icon", "_utils", "AlignmentMatrixControl", "className", "id", "label", "__", "defaultValue", "value", "onChange", "width", "props", "baseId", "useInstanceId", "compositeStore", "useCompositeStore", "defaultActiveId", "getItemId", "activeId", "setActiveId", "nextActiveId", "nextValue", "getItemValue", "rtl", "isRTL", "useState", "classes", "classnames", "_react", "createElement", "Composite", "store", "render", "Root", "role", "size", "GRID", "map", "cells", "index", "CompositeRow", "Row", "key", "cell", "cellId", "isActive", "default", "Icon", "AlignmentMatrixControlIcon", "_default", "exports"], "sources": ["@wordpress/components/src/alignment-matrix-control/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { __, isRTL } from '@wordpress/i18n';\nimport { useInstanceId } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport { Composite, CompositeRow, useCompositeStore } from '../composite/v2';\nimport { Root, Row } from './styles/alignment-matrix-control-styles';\nimport AlignmentMatrixControlIcon from './icon';\nimport { GRID, getItemId, getItemValue } from './utils';\nimport type { WordPressComponentProps } from '../context';\nimport type { AlignmentMatrixControlProps } from './types';\n\n/**\n *\n * AlignmentMatrixControl components enable adjustments to horizontal and vertical alignments for UI.\n *\n * ```jsx\n * import { __experimentalAlignmentMatrixControl as AlignmentMatrixControl } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const Example = () => {\n * \tconst [ alignment, setAlignment ] = useState( 'center center' );\n *\n * \treturn (\n * \t\t<AlignmentMatrixControl\n * \t\t\tvalue={ alignment }\n * \t\t\tonChange={ setAlignment }\n * \t\t/>\n * \t);\n * };\n * ```\n */\nexport function AlignmentMatrixControl( {\n\tclassName,\n\tid,\n\tlabel = __( 'Alignment Matrix Control' ),\n\tdefaultValue = 'center center',\n\tvalue,\n\tonChange,\n\twidth = 92,\n\t...props\n}: WordPressComponentProps< AlignmentMatrixControlProps, 'div', false > ) {\n\tconst baseId = useInstanceId(\n\t\tAlignmentMatrixControl,\n\t\t'alignment-matrix-control',\n\t\tid\n\t);\n\n\tconst compositeStore = useCompositeStore( {\n\t\tdefaultActiveId: getItemId( baseId, defaultValue ),\n\t\tactiveId: getItemId( baseId, value ),\n\t\tsetActiveId: ( nextActiveId ) => {\n\t\t\tconst nextValue = getItemValue( baseId, nextActiveId );\n\t\t\tif ( nextValue ) onChange?.( nextValue );\n\t\t},\n\t\trtl: isRTL(),\n\t} );\n\n\tconst activeId = compositeStore.useState( 'activeId' );\n\n\tconst classes = classnames(\n\t\t'component-alignment-matrix-control',\n\t\tclassName\n\t);\n\n\treturn (\n\t\t<Composite\n\t\t\tstore={ compositeStore }\n\t\t\trender={\n\t\t\t\t<Root\n\t\t\t\t\t{ ...props }\n\t\t\t\t\taria-label={ label }\n\t\t\t\t\tclassName={ classes }\n\t\t\t\t\tid={ baseId }\n\t\t\t\t\trole=\"grid\"\n\t\t\t\t\tsize={ width }\n\t\t\t\t/>\n\t\t\t}\n\t\t>\n\t\t\t{ GRID.map( ( cells, index ) => (\n\t\t\t\t<CompositeRow render={ <Row role=\"row\" /> } key={ index }>\n\t\t\t\t\t{ cells.map( ( cell ) => {\n\t\t\t\t\t\tconst cellId = getItemId( baseId, cell );\n\t\t\t\t\t\tconst isActive = cellId === activeId;\n\n\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t<Cell\n\t\t\t\t\t\t\t\tid={ cellId }\n\t\t\t\t\t\t\t\tisActive={ isActive }\n\t\t\t\t\t\t\t\tkey={ cell }\n\t\t\t\t\t\t\t\tvalue={ cell }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t);\n\t\t\t\t\t} ) }\n\t\t\t\t</CompositeRow>\n\t\t\t) ) }\n\t\t</Composite>\n\t);\n}\n\nAlignmentMatrixControl.Icon = AlignmentMatrixControlIcon;\n\nexport default AlignmentMatrixControl;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AACA,IAAAK,6BAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AAlBA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASQ,sBAAsBA,CAAE;EACvCC,SAAS;EACTC,EAAE;EACFC,KAAK,GAAG,IAAAC,QAAE,EAAE,0BAA2B,CAAC;EACxCC,YAAY,GAAG,eAAe;EAC9BC,KAAK;EACLC,QAAQ;EACRC,KAAK,GAAG,EAAE;EACV,GAAGC;AACkE,CAAC,EAAG;EACzE,MAAMC,MAAM,GAAG,IAAAC,sBAAa,EAC3BX,sBAAsB,EACtB,0BAA0B,EAC1BE,EACD,CAAC;EAED,MAAMU,cAAc,GAAG,IAAAC,oBAAiB,EAAE;IACzCC,eAAe,EAAE,IAAAC,gBAAS,EAAEL,MAAM,EAAEL,YAAa,CAAC;IAClDW,QAAQ,EAAE,IAAAD,gBAAS,EAAEL,MAAM,EAAEJ,KAAM,CAAC;IACpCW,WAAW,EAAIC,YAAY,IAAM;MAChC,MAAMC,SAAS,GAAG,IAAAC,mBAAY,EAAEV,MAAM,EAAEQ,YAAa,CAAC;MACtD,IAAKC,SAAS,EAAGZ,QAAQ,GAAIY,SAAU,CAAC;IACzC,CAAC;IACDE,GAAG,EAAE,IAAAC,WAAK,EAAC;EACZ,CAAE,CAAC;EAEH,MAAMN,QAAQ,GAAGJ,cAAc,CAACW,QAAQ,CAAE,UAAW,CAAC;EAEtD,MAAMC,OAAO,GAAG,IAAAC,mBAAU,EACzB,oCAAoC,EACpCxB,SACD,CAAC;EAED,OACC,IAAAyB,MAAA,CAAAC,aAAA,EAAC/B,EAAA,CAAAgC,SAAS;IACTC,KAAK,EAAGjB,cAAgB;IACxBkB,MAAM,EACL,IAAAJ,MAAA,CAAAC,aAAA,EAAC9B,6BAAA,CAAAkC,IAAI;MAAA,GACCtB,KAAK;MACV,cAAaN,KAAO;MACpBF,SAAS,EAAGuB,OAAS;MACrBtB,EAAE,EAAGQ,MAAQ;MACbsB,IAAI,EAAC,MAAM;MACXC,IAAI,EAAGzB;IAAO,CACd;EACD,GAEC0B,WAAI,CAACC,GAAG,CAAE,CAAEC,KAAK,EAAEC,KAAK,KACzB,IAAAX,MAAA,CAAAC,aAAA,EAAC/B,EAAA,CAAA0C,YAAY;IAACR,MAAM,EAAG,IAAAJ,MAAA,CAAAC,aAAA,EAAC9B,6BAAA,CAAA0C,GAAG;MAACP,IAAI,EAAC;IAAK,CAAE,CAAG;IAACQ,GAAG,EAAGH;EAAO,GACtDD,KAAK,CAACD,GAAG,CAAIM,IAAI,IAAM;IACxB,MAAMC,MAAM,GAAG,IAAA3B,gBAAS,EAAEL,MAAM,EAAE+B,IAAK,CAAC;IACxC,MAAME,QAAQ,GAAGD,MAAM,KAAK1B,QAAQ;IAEpC,OACC,IAAAU,MAAA,CAAAC,aAAA,EAAChC,KAAA,CAAAiD,OAAI;MACJ1C,EAAE,EAAGwC,MAAQ;MACbC,QAAQ,EAAGA,QAAU;MACrBH,GAAG,EAAGC,IAAM;MACZnC,KAAK,EAAGmC;IAAM,CACd,CAAC;EAEJ,CAAE,CACW,CACb,CACQ,CAAC;AAEd;AAEAzC,sBAAsB,CAAC6C,IAAI,GAAGC,aAA0B;AAAC,IAAAC,QAAA,GAE1C/C,sBAAsB;AAAAgD,OAAA,CAAAJ,OAAA,GAAAG,QAAA"}