"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useBorderBoxControl = useBorderBoxControl;
var _element = require("@wordpress/element");
var styles = _interopRequireWildcard(require("../styles"));
var _utils = require("../utils");
var _context = require("../../context");
var _useCx = require("../../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function useBorderBoxControl(props) {
  const {
    className,
    colors = [],
    onChange,
    enableAlpha = false,
    enableStyle = true,
    size = 'default',
    value,
    __experimentalIsRenderedInSidebar = false,
    __next40pxDefaultSize,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'BorderBoxControl');
  const computedSize = size === 'default' && __next40pxDefaultSize ? '__unstable-large' : size;
  const mixedBorders = (0, _utils.hasMixedBorders)(value);
  const splitBorders = (0, _utils.hasSplitBorders)(value);
  const linkedValue = splitBorders ? (0, _utils.getCommonBorder)(value) : value;
  const splitValue = splitBorders ? value : (0, _utils.getSplitBorders)(value);

  // If no numeric width value is set, the unit select will be disabled.
  const hasWidthValue = !isNaN(parseFloat(`${linkedValue?.width}`));
  const [isLinked, setIsLinked] = (0, _element.useState)(!mixedBorders);
  const toggleLinked = () => setIsLinked(!isLinked);
  const onLinkedChange = newBorder => {
    if (!newBorder) {
      return onChange(undefined);
    }

    // If we have all props defined on the new border apply it.
    if (!mixedBorders || (0, _utils.isCompleteBorder)(newBorder)) {
      return onChange((0, _utils.isEmptyBorder)(newBorder) ? undefined : newBorder);
    }

    // If we had mixed borders we might have had some shared border props
    // that we need to maintain. For example; we could have mixed borders
    // with all the same color but different widths. Then from the linked
    // control we change the color. We should keep the separate  widths.
    const changes = (0, _utils.getBorderDiff)(linkedValue, newBorder);
    const updatedBorders = {
      top: {
        ...value?.top,
        ...changes
      },
      right: {
        ...value?.right,
        ...changes
      },
      bottom: {
        ...value?.bottom,
        ...changes
      },
      left: {
        ...value?.left,
        ...changes
      }
    };
    if ((0, _utils.hasMixedBorders)(updatedBorders)) {
      return onChange(updatedBorders);
    }
    const filteredResult = (0, _utils.isEmptyBorder)(updatedBorders.top) ? undefined : updatedBorders.top;
    onChange(filteredResult);
  };
  const onSplitChange = (newBorder, side) => {
    const updatedBorders = {
      ...splitValue,
      [side]: newBorder
    };
    if ((0, _utils.hasMixedBorders)(updatedBorders)) {
      onChange(updatedBorders);
    } else {
      onChange(newBorder);
    }
  };
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => {
    return cx(styles.borderBoxControl, className);
  }, [cx, className]);
  const linkedControlClassName = (0, _element.useMemo)(() => {
    return cx(styles.linkedBorderControl());
  }, [cx]);
  const wrapperClassName = (0, _element.useMemo)(() => {
    return cx(styles.wrapper);
  }, [cx]);
  return {
    ...otherProps,
    className: classes,
    colors,
    disableUnits: mixedBorders && !hasWidthValue,
    enableAlpha,
    enableStyle,
    hasMixedBorders: mixedBorders,
    isLinked,
    linkedControlClassName,
    onLinkedChange,
    onSplitChange,
    toggleLinked,
    linkedValue,
    size: computedSize,
    splitValue,
    wrapperClassName,
    __experimentalIsRenderedInSidebar
  };
}
//# sourceMappingURL=hook.js.map