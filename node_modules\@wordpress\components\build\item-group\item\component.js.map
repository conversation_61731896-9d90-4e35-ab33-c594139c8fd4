{"version": 3, "names": ["_hook", "require", "_context", "_view", "UnconnectedItem", "props", "forwardedRef", "role", "wrapperClassName", "otherProps", "useItem", "_react", "createElement", "className", "View", "ref", "<PERSON><PERSON>", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/item-group/item/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { ItemProps } from '../types';\nimport { useItem } from './hook';\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { View } from '../../view';\n\nfunction UnconnectedItem(\n\tprops: WordPressComponentProps< ItemProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst { role, wrapperClassName, ...otherProps } = useItem( props );\n\n\treturn (\n\t\t<div role={ role } className={ wrapperClassName }>\n\t\t\t<View { ...otherProps } ref={ forwardedRef } />\n\t\t</div>\n\t);\n}\n\n/**\n * `Item` is used in combination with `ItemGroup` to display a list of items\n * grouped and styled together.\n *\n * ```jsx\n * import {\n *   __experimentalItemGroup as ItemGroup,\n *   __experimentalItem as Item,\n * } from '@wordpress/components';\n *\n * function Example() {\n *   return (\n *     <ItemGroup>\n *       <Item>Code</Item>\n *       <Item>is</Item>\n *       <Item>Poetry</Item>\n *     </ItemGroup>\n *   );\n * }\n * ```\n */\nexport const Item = contextConnect( UnconnectedItem, 'Item' );\n\nexport default Item;\n"], "mappings": ";;;;;;;AASA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,eAAeA,CACvBC,KAAkD,EAClDC,YAAiC,EAChC;EACD,MAAM;IAAEC,IAAI;IAAEC,gBAAgB;IAAE,GAAGC;EAAW,CAAC,GAAG,IAAAC,aAAO,EAAEL,KAAM,CAAC;EAElE,OACC,IAAAM,MAAA,CAAAC,aAAA;IAAKL,IAAI,EAAGA,IAAM;IAACM,SAAS,EAAGL;EAAkB,GAChD,IAAAG,MAAA,CAAAC,aAAA,EAACT,KAAA,CAAAW,IAAI;IAAA,GAAML,UAAU;IAAGM,GAAG,EAAGT;EAAc,CAAE,CAC1C,CAAC;AAER;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMU,IAAI,GAAG,IAAAC,uBAAc,EAAEb,eAAe,EAAE,MAAO,CAAC;AAACc,OAAA,CAAAF,IAAA,GAAAA,IAAA;AAAA,IAAAG,QAAA,GAE/CH,IAAI;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}