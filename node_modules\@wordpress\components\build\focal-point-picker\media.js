"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = Media;
var _react = require("react");
var _focalPointPickerStyle = require("./styles/focal-point-picker-style");
var _utils = require("./utils");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function Media({
  alt,
  autoPlay,
  src,
  onLoad,
  mediaRef,
  // Exposing muted prop for test rendering purposes
  // https://github.com/testing-library/react-testing-library/issues/470
  muted = true,
  ...props
}) {
  if (!src) {
    return (0, _react.createElement)(_focalPointPickerStyle.MediaPlaceholder, {
      className: "components-focal-point-picker__media components-focal-point-picker__media--placeholder",
      ref: mediaRef,
      ...props
    });
  }
  const isVideo = (0, _utils.isVideoType)(src);
  return isVideo ? (0, _react.createElement)("video", {
    ...props,
    autoPlay: autoPlay,
    className: "components-focal-point-picker__media components-focal-point-picker__media--video",
    loop: true,
    muted: muted,
    onLoadedData: onLoad,
    ref: mediaRef,
    src: src
  }) : (0, _react.createElement)("img", {
    ...props,
    alt: alt,
    className: "components-focal-point-picker__media components-focal-point-picker__media--image",
    onLoad: onLoad,
    ref: mediaRef,
    src: src
  });
}
//# sourceMappingURL=media.js.map