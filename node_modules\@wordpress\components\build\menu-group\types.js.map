{"version": 3, "names": [], "sources": ["@wordpress/components/src/menu-group/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\nexport type MenuGroupProps = {\n\t/**\n\t * A CSS `class` to give to the container element.\n\t */\n\tclassName?: string;\n\t/**\n\t * Hide the top border on the container.\n\t */\n\thideSeparator?: boolean;\n\t/**\n\t * Text to be displayed as the menu group header.\n\t */\n\tlabel?: string;\n\t/**\n\t * The children elements.\n\t */\n\tchildren?: ReactNode;\n};\n"], "mappings": ""}