{"version": 3, "names": ["_deepmerge", "_interopRequireDefault", "require", "_es", "_isPlainObject", "_element", "_warning", "_utils", "ComponentsContext", "createContext", "exports", "useComponentsContext", "useContext", "useContextSystemBridge", "value", "parentContext", "valueRef", "useRef", "useUpdateEffect", "fastDeepEqual", "current", "SCRIPT_DEBUG", "warn", "JSON", "stringify", "config", "useMemo", "deepmerge", "isMergeableObject", "isPlainObject", "BaseContextSystemProvider", "children", "contextValue", "_react", "createElement", "Provider", "ContextSystemProvider", "memo"], "sources": ["@wordpress/components/src/context/context-system-provider.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport deepmerge from 'deepmerge';\nimport fastDeepEqual from 'fast-deep-equal/es6';\nimport { isPlainObject } from 'is-plain-object';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tcreateContext,\n\tuseContext,\n\tuseRef,\n\tuseMemo,\n\tmemo,\n} from '@wordpress/element';\nimport warn from '@wordpress/warning';\n\n/**\n * Internal dependencies\n */\nimport { useUpdateEffect } from '../utils';\n\nexport const ComponentsContext = createContext(\n\t/** @type {Record<string, any>} */ ( {} )\n);\nexport const useComponentsContext = () => useContext( ComponentsContext );\n\n/**\n * Consolidates incoming ContextSystem values with a (potential) parent ContextSystem value.\n *\n * Note: This function will warn if it detects an un-memoized `value`\n *\n * @param {Object}              props\n * @param {Record<string, any>} props.value\n * @return {Record<string, any>} The consolidated value.\n */\nfunction useContextSystemBridge( { value } ) {\n\tconst parentContext = useComponentsContext();\n\n\tconst valueRef = useRef( value );\n\n\tuseUpdateEffect( () => {\n\t\tif (\n\t\t\t// Objects are equivalent.\n\t\t\tfastDeepEqual( valueRef.current, value ) &&\n\t\t\t// But not the same reference.\n\t\t\tvalueRef.current !== value\n\t\t) {\n\t\t\twarn( `Please memoize your context: ${ JSON.stringify( value ) }` );\n\t\t}\n\t}, [ value ] );\n\n\t// `parentContext` will always be memoized (i.e., the result of this hook itself)\n\t// or the default value from when the `ComponentsContext` was originally\n\t// initialized (which will never change, it's a static variable)\n\t// so this memoization will prevent `deepmerge()` from rerunning unless\n\t// the references to `value` change OR the `parentContext` has an actual material change\n\t// (because again, it's guaranteed to be memoized or a static reference to the empty object\n\t// so we know that the only changes for `parentContext` are material ones... i.e., why we\n\t// don't have to warn in the `useUpdateEffect` hook above for `parentContext` and we only\n\t// need to bother with the `value`). The `useUpdateEffect` above will ensure that we are\n\t// correctly warning when the `value` isn't being properly memoized. All of that to say\n\t// that this should be super safe to assume that `useMemo` will only run on actual\n\t// changes to the two dependencies, therefore saving us calls to `deepmerge()`!\n\tconst config = useMemo( () => {\n\t\t// Deep clone `parentContext` to avoid mutating it later.\n\t\treturn deepmerge( parentContext ?? {}, value ?? {}, {\n\t\t\tisMergeableObject: isPlainObject,\n\t\t} );\n\t}, [ parentContext, value ] );\n\n\treturn config;\n}\n\n/**\n * A Provider component that can modify props for connected components within\n * the Context system.\n *\n * @example\n * ```jsx\n * <ContextSystemProvider value={{ Button: { size: 'small' }}}>\n *   <Button>...</Button>\n * </ContextSystemProvider>\n * ```\n *\n * @template {Record<string, any>} T\n * @param {Object}                    options\n * @param {import('react').ReactNode} options.children Children to render.\n * @param {T}                         options.value    Props to render into connected components.\n * @return {JSX.Element} A Provider wrapped component.\n */\nconst BaseContextSystemProvider = ( { children, value } ) => {\n\tconst contextValue = useContextSystemBridge( { value } );\n\n\treturn (\n\t\t<ComponentsContext.Provider value={ contextValue }>\n\t\t\t{ children }\n\t\t</ComponentsContext.Provider>\n\t);\n};\n\nexport const ContextSystemProvider = memo( BaseContextSystemProvider );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,GAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAKA,IAAAG,QAAA,GAAAH,OAAA;AAOA,IAAAI,QAAA,GAAAL,sBAAA,CAAAC,OAAA;AAKA,IAAAK,MAAA,GAAAL,OAAA;AAtBA;AACA;AACA;;AAKA;AACA;AACA;;AAUA;AACA;AACA;;AAGO,MAAMM,iBAAiB,GAAG,IAAAC,sBAAa,GAC7C,kCAAqC,CAAC,CACvC,CAAC;AAACC,OAAA,CAAAF,iBAAA,GAAAA,iBAAA;AACK,MAAMG,oBAAoB,GAAGA,CAAA,KAAM,IAAAC,mBAAU,EAAEJ,iBAAkB,CAAC;;AAEzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARAE,OAAA,CAAAC,oBAAA,GAAAA,oBAAA;AASA,SAASE,sBAAsBA,CAAE;EAAEC;AAAM,CAAC,EAAG;EAC5C,MAAMC,aAAa,GAAGJ,oBAAoB,CAAC,CAAC;EAE5C,MAAMK,QAAQ,GAAG,IAAAC,eAAM,EAAEH,KAAM,CAAC;EAEhC,IAAAI,sBAAe,EAAE,MAAM;IACtB;IACC;IACA,IAAAC,WAAa,EAAEH,QAAQ,CAACI,OAAO,EAAEN,KAAM,CAAC;IACxC;IACAE,QAAQ,CAACI,OAAO,KAAKN,KAAK,EACzB;MACD,OAAAO,YAAA,oBAAAA,YAAA,gBAAAC,gBAAI,EAAG,gCAAgCC,IAAI,CAACC,SAAS,CAAEV,KAAM,CAAG,EAAE,CAAC;IACpE;EACD,CAAC,EAAE,CAAEA,KAAK,CAAG,CAAC;;EAEd;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMW,MAAM,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC7B;IACA,OAAO,IAAAC,kBAAS,EAAEZ,aAAa,aAAbA,aAAa,cAAbA,aAAa,GAAI,CAAC,CAAC,EAAED,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,CAAC,CAAC,EAAE;MACnDc,iBAAiB,EAAEC;IACpB,CAAE,CAAC;EACJ,CAAC,EAAE,CAAEd,aAAa,EAAED,KAAK,CAAG,CAAC;EAE7B,OAAOW,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,yBAAyB,GAAGA,CAAE;EAAEC,QAAQ;EAAEjB;AAAM,CAAC,KAAM;EAC5D,MAAMkB,YAAY,GAAGnB,sBAAsB,CAAE;IAAEC;EAAM,CAAE,CAAC;EAExD,OACC,IAAAmB,MAAA,CAAAC,aAAA,EAAC1B,iBAAiB,CAAC2B,QAAQ;IAACrB,KAAK,EAAGkB;EAAc,GAC/CD,QACyB,CAAC;AAE/B,CAAC;AAEM,MAAMK,qBAAqB,GAAG,IAAAC,aAAI,EAAEP,yBAA0B,CAAC;AAACpB,OAAA,CAAA0B,qBAAA,GAAAA,qBAAA"}