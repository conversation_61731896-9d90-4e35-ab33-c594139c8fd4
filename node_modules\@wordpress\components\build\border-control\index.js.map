{"version": 3, "names": ["_component", "_interopRequireDefault", "require", "_hook"], "sources": ["@wordpress/components/src/border-control/index.ts"], "sourcesContent": ["export { default as BorderControl } from './border-control/component';\nexport { useBorderControl } from './border-control/hook';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA"}