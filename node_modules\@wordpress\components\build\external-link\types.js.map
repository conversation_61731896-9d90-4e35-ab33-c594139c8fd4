{"version": 3, "names": [], "sources": ["@wordpress/components/src/external-link/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\nexport type ExternalLinkProps = {\n\t/**\n\t * The content to be displayed within the link.\n\t */\n\tchildren: ReactNode;\n\t/**\n\t * The URL of the external resource.\n\t */\n\thref: string;\n};\n"], "mappings": ""}