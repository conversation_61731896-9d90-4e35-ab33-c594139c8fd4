{"name": "ignore-walk", "version": "4.0.1", "description": "Nested/recursive `.gitignore`/`.npmignore` parsing and filtering.", "main": "index.js", "devDependencies": {"@npmcli/lint": "^1.0.2", "mkdirp": "^0.5.1", "mutate-fs": "^1.1.0", "rimraf": "^2.6.1", "tap": "^15.0.6"}, "scripts": {"test": "tap", "posttest": "npm run lint --", "lint": "npm run npmclilint -- \"*.*js\" \"test/**/*.*js\"", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "npmclilint": "npmcli-lint", "postsnap": "npm run lintfix --"}, "keywords": ["ignorefile", "ignore", "file", ".giti<PERSON>re", ".n<PERSON><PERSON><PERSON>", "glob"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/ignore-walk.git"}, "files": ["index.js"], "dependencies": {"minimatch": "^3.0.4"}, "tap": {"test-env": "LC_ALL=sk", "before": "test/00-setup.js", "after": "test/zz-cleanup.js", "jobs": 1}, "engines": {"node": ">=10"}}