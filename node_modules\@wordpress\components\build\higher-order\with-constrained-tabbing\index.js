"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _compose = require("@wordpress/compose");
/**
 * WordPress dependencies
 */

/**
 * `withConstrainedTabbing` is a React [higher-order component](https://facebook.github.io/react/docs/higher-order-components.html)
 * adding the ability to constrain keyboard navigation with the Tab key within a component.
 * For accessibility reasons, some UI components need to constrain Tab navigation, for example
 * modal dialogs or similar UI. Use of this component is recommended only in cases where a way to
 * navigate away from the wrapped component is implemented by other means, usually by pressing
 * the Escape key or using a specific UI control, e.g. a "Close" button.
 */
const withConstrainedTabbing = (0, _compose.createHigherOrderComponent)(WrappedComponent => function ComponentWithConstrainedTabbing(props) {
  const ref = (0, _compose.useConstrainedTabbing)();
  return (0, _react.createElement)("div", {
    ref: ref,
    tabIndex: -1
  }, (0, _react.createElement)(WrappedComponent, {
    ...props
  }));
}, 'withConstrainedTabbing');
var _default = withConstrainedTabbing;
exports.default = _default;
//# sourceMappingURL=index.js.map