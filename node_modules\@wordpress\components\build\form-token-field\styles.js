"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.TokensAndInputWrapperFlex = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react = require("@emotion/react");
var _flex = require("../flex");
var _space = require("../utils/space");
var _utils = require("../utils");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const deprecatedPaddings = ({
  __next40pxDefaultSize,
  hasTokens
}) => !__next40pxDefaultSize && /*#__PURE__*/(0, _react.css)("padding-top:", (0, _space.space)(hasTokens ? 1 : 0.5), ";padding-bottom:", (0, _space.space)(hasTokens ? 1 : 0.5), ";" + (process.env.NODE_ENV === "production" ? "" : ";label:deprecatedPaddings;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9ybS10b2tlbi1maWVsZC9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBdUJJIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9ybS10b2tlbi1maWVsZC9zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5cbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IEZsZXggfSBmcm9tICcuLi9mbGV4JztcbmltcG9ydCB7IHNwYWNlIH0gZnJvbSAnLi4vdXRpbHMvc3BhY2UnO1xuaW1wb3J0IHsgYm94U2l6aW5nUmVzZXQgfSBmcm9tICcuLi91dGlscyc7XG5cbnR5cGUgVG9rZW5zQW5kSW5wdXRXcmFwcGVyUHJvcHMgPSB7XG5cdF9fbmV4dDQwcHhEZWZhdWx0U2l6ZTogYm9vbGVhbjtcblx0aGFzVG9rZW5zOiBib29sZWFuO1xufTtcblxuY29uc3QgZGVwcmVjYXRlZFBhZGRpbmdzID0gKCB7XG5cdF9fbmV4dDQwcHhEZWZhdWx0U2l6ZSxcblx0aGFzVG9rZW5zLFxufTogVG9rZW5zQW5kSW5wdXRXcmFwcGVyUHJvcHMgKSA9PlxuXHQhIF9fbmV4dDQwcHhEZWZhdWx0U2l6ZSAmJlxuXHRjc3NgXG5cdFx0cGFkZGluZy10b3A6ICR7IHNwYWNlKCBoYXNUb2tlbnMgPyAxIDogMC41ICkgfTtcblx0XHRwYWRkaW5nLWJvdHRvbTogJHsgc3BhY2UoIGhhc1Rva2VucyA/IDEgOiAwLjUgKSB9O1xuXHRgO1xuXG5leHBvcnQgY29uc3QgVG9rZW5zQW5kSW5wdXRXcmFwcGVyRmxleCA9IHN0eWxlZCggRmxleCApYFxuXHRwYWRkaW5nOiA3cHg7XG5cdCR7IGJveFNpemluZ1Jlc2V0IH1cblxuXHQkeyBkZXByZWNhdGVkUGFkZGluZ3MgfVxuYDtcbiJdfQ== */");
const TokensAndInputWrapperFlex = ( /*#__PURE__*/0, _base.default)(_flex.Flex, process.env.NODE_ENV === "production" ? {
  target: "ehq8nmi0"
} : {
  target: "ehq8nmi0",
  label: "TokensAndInputWrapperFlex"
})("padding:7px;", _utils.boxSizingReset, " ", deprecatedPaddings, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9ybS10b2tlbi1maWVsZC9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBNEJ1RCIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2Zvcm0tdG9rZW4tZmllbGQvc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuaW1wb3J0IHsgY3NzIH0gZnJvbSAnQGVtb3Rpb24vcmVhY3QnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBGbGV4IH0gZnJvbSAnLi4vZmxleCc7XG5pbXBvcnQgeyBzcGFjZSB9IGZyb20gJy4uL3V0aWxzL3NwYWNlJztcbmltcG9ydCB7IGJveFNpemluZ1Jlc2V0IH0gZnJvbSAnLi4vdXRpbHMnO1xuXG50eXBlIFRva2Vuc0FuZElucHV0V3JhcHBlclByb3BzID0ge1xuXHRfX25leHQ0MHB4RGVmYXVsdFNpemU6IGJvb2xlYW47XG5cdGhhc1Rva2VuczogYm9vbGVhbjtcbn07XG5cbmNvbnN0IGRlcHJlY2F0ZWRQYWRkaW5ncyA9ICgge1xuXHRfX25leHQ0MHB4RGVmYXVsdFNpemUsXG5cdGhhc1Rva2Vucyxcbn06IFRva2Vuc0FuZElucHV0V3JhcHBlclByb3BzICkgPT5cblx0ISBfX25leHQ0MHB4RGVmYXVsdFNpemUgJiZcblx0Y3NzYFxuXHRcdHBhZGRpbmctdG9wOiAkeyBzcGFjZSggaGFzVG9rZW5zID8gMSA6IDAuNSApIH07XG5cdFx0cGFkZGluZy1ib3R0b206ICR7IHNwYWNlKCBoYXNUb2tlbnMgPyAxIDogMC41ICkgfTtcblx0YDtcblxuZXhwb3J0IGNvbnN0IFRva2Vuc0FuZElucHV0V3JhcHBlckZsZXggPSBzdHlsZWQoIEZsZXggKWBcblx0cGFkZGluZzogN3B4O1xuXHQkeyBib3hTaXppbmdSZXNldCB9XG5cblx0JHsgZGVwcmVjYXRlZFBhZGRpbmdzIH1cbmA7XG4iXX0= */"));
exports.TokensAndInputWrapperFlex = TokensAndInputWrapperFlex;
//# sourceMappingURL=styles.js.map