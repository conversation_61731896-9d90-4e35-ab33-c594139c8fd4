{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_visually<PERSON><PERSON>den", "_baseControlStyles", "_context", "_hooks", "UnconnectedBaseControl", "props", "__nextHasNoMarginBottom", "id", "label", "hideLabelFromVision", "help", "className", "children", "useContextSystem", "_react", "createElement", "Wrapper", "StyledField", "VisuallyHidden", "as", "htmlFor", "StyledLabel", "VisualLabel", "StyledHelp", "undefined", "StyledVisualLabel", "classnames", "exports", "BaseControl", "Object", "assign", "contextConnectWithoutRef", "_default", "default"], "sources": ["@wordpress/components/src/base-control/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * Internal dependencies\n */\nimport { VisuallyHidden } from '../visually-hidden';\nimport type { BaseControlProps, BaseControlVisualLabelProps } from './types';\nimport {\n\t<PERSON><PERSON><PERSON>,\n\t<PERSON>d<PERSON><PERSON>,\n\tStyled<PERSON><PERSON><PERSON>,\n\t<PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tStyledVisualLabel,\n} from './styles/base-control-styles';\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnectWithoutRef, useContextSystem } from '../context';\n\nexport { useBaseControlProps } from './hooks';\n\n/**\n * `BaseControl` is a component used to generate labels and help text for components handling user inputs.\n *\n * ```jsx\n * import { BaseControl, useBaseControlProps } from '@wordpress/components';\n *\n * // Render a `BaseControl` for a textarea input\n * const MyCustomTextareaControl = ({ children, ...baseProps }) => (\n * \t// `useBaseControlProps` is a convenience hook to get the props for the `BaseControl`\n * \t// and the inner control itself. Namely, it takes care of generating a unique `id`,\n * \t// properly associating it with the `label` and `help` elements.\n * \tconst { baseControlProps, controlProps } = useBaseControlProps( baseProps );\n *\n * \treturn (\n * \t\t<BaseControl { ...baseControlProps } __nextHasNoMarginBottom={ true }>\n * \t\t\t<textarea { ...controlProps }>\n * \t\t\t  { children }\n * \t\t\t</textarea>\n * \t\t</BaseControl>\n * \t);\n * );\n * ```\n */\nconst UnconnectedBaseControl = (\n\tprops: WordPressComponentProps< BaseControlProps, null >\n) => {\n\tconst {\n\t\t__nextHasNoMarginBottom = false,\n\t\tid,\n\t\tlabel,\n\t\thideLabelFromVision = false,\n\t\thelp,\n\t\tclassName,\n\t\tchildren,\n\t} = useContextSystem( props, 'BaseControl' );\n\n\treturn (\n\t\t<Wrapper className={ className }>\n\t\t\t<StyledField\n\t\t\t\tclassName=\"components-base-control__field\"\n\t\t\t\t// TODO: Official deprecation for this should start after all internal usages have been migrated\n\t\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t>\n\t\t\t\t{ label &&\n\t\t\t\t\tid &&\n\t\t\t\t\t( hideLabelFromVision ? (\n\t\t\t\t\t\t<VisuallyHidden as=\"label\" htmlFor={ id }>\n\t\t\t\t\t\t\t{ label }\n\t\t\t\t\t\t</VisuallyHidden>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<StyledLabel\n\t\t\t\t\t\t\tclassName=\"components-base-control__label\"\n\t\t\t\t\t\t\thtmlFor={ id }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ label }\n\t\t\t\t\t\t</StyledLabel>\n\t\t\t\t\t) ) }\n\t\t\t\t{ label &&\n\t\t\t\t\t! id &&\n\t\t\t\t\t( hideLabelFromVision ? (\n\t\t\t\t\t\t<VisuallyHidden as=\"label\">{ label }</VisuallyHidden>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<VisualLabel>{ label }</VisualLabel>\n\t\t\t\t\t) ) }\n\t\t\t\t{ children }\n\t\t\t</StyledField>\n\t\t\t{ !! help && (\n\t\t\t\t<StyledHelp\n\t\t\t\t\tid={ id ? id + '__help' : undefined }\n\t\t\t\t\tclassName=\"components-base-control__help\"\n\t\t\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t\t>\n\t\t\t\t\t{ help }\n\t\t\t\t</StyledHelp>\n\t\t\t) }\n\t\t</Wrapper>\n\t);\n};\n\n/**\n * `BaseControl.VisualLabel` is used to render a purely visual label inside a `BaseControl` component.\n *\n * It should only be used in cases where the children being rendered inside `BaseControl` are already accessibly labeled,\n * e.g., a button, but we want an additional visual label for that section equivalent to the labels `BaseControl` would\n * otherwise use if the `label` prop was passed.\n *\n * @example\n * import { BaseControl } from '@wordpress/components';\n *\n * const MyBaseControl = () => (\n * \t<BaseControl help=\"This button is already accessibly labeled.\">\n * \t\t<BaseControl.VisualLabel>Author</BaseControl.VisualLabel>\n * \t\t<Button>Select an author</Button>\n * \t</BaseControl>\n * );\n */\nexport const VisualLabel = ( {\n\tclassName,\n\tchildren,\n\t...props\n}: WordPressComponentProps< BaseControlVisualLabelProps, 'span' > ) => {\n\treturn (\n\t\t<StyledVisualLabel\n\t\t\t{ ...props }\n\t\t\tclassName={ classnames(\n\t\t\t\t'components-base-control__label',\n\t\t\t\tclassName\n\t\t\t) }\n\t\t>\n\t\t\t{ children }\n\t\t</StyledVisualLabel>\n\t);\n};\n\nexport const BaseControl = Object.assign(\n\tcontextConnectWithoutRef( UnconnectedBaseControl, 'BaseControl' ),\n\t{ VisualLabel }\n);\n\nexport default BaseControl;\n"], "mappings": ";;;;;;;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,eAAA,GAAAD,OAAA;AAEA,IAAAE,kBAAA,GAAAF,OAAA;AAQA,IAAAG,QAAA,GAAAH,OAAA;AAEA,IAAAI,MAAA,GAAAJ,OAAA;AApBA;AACA;AACA;;AAGA;AACA;AACA;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,sBAAsB,GAC3BC,KAAwD,IACpD;EACJ,MAAM;IACLC,uBAAuB,GAAG,KAAK;IAC/BC,EAAE;IACFC,KAAK;IACLC,mBAAmB,GAAG,KAAK;IAC3BC,IAAI;IACJC,SAAS;IACTC;EACD,CAAC,GAAG,IAAAC,yBAAgB,EAAER,KAAK,EAAE,aAAc,CAAC;EAE5C,OACC,IAAAS,MAAA,CAAAC,aAAA,EAACd,kBAAA,CAAAe,OAAO;IAACL,SAAS,EAAGA;EAAW,GAC/B,IAAAG,MAAA,CAAAC,aAAA,EAACd,kBAAA,CAAAgB,WAAW;IACXN,SAAS,EAAC;IACV;IAAA;IACAL,uBAAuB,EAAGA;EAAyB,GAEjDE,KAAK,IACND,EAAE,KACAE,mBAAmB,GACpB,IAAAK,MAAA,CAAAC,aAAA,EAACf,eAAA,CAAAkB,cAAc;IAACC,EAAE,EAAC,OAAO;IAACC,OAAO,EAAGb;EAAI,GACtCC,KACa,CAAC,GAEjB,IAAAM,MAAA,CAAAC,aAAA,EAACd,kBAAA,CAAAoB,WAAW;IACXV,SAAS,EAAC,gCAAgC;IAC1CS,OAAO,EAAGb;EAAI,GAEZC,KACU,CACb,CAAE,EACFA,KAAK,IACN,CAAED,EAAE,KACFE,mBAAmB,GACpB,IAAAK,MAAA,CAAAC,aAAA,EAACf,eAAA,CAAAkB,cAAc;IAACC,EAAE,EAAC;EAAO,GAAGX,KAAuB,CAAC,GAErD,IAAAM,MAAA,CAAAC,aAAA,EAACO,WAAW,QAAGd,KAAoB,CACnC,CAAE,EACFI,QACU,CAAC,EACZ,CAAC,CAAEF,IAAI,IACR,IAAAI,MAAA,CAAAC,aAAA,EAACd,kBAAA,CAAAsB,UAAU;IACVhB,EAAE,EAAGA,EAAE,GAAGA,EAAE,GAAG,QAAQ,GAAGiB,SAAW;IACrCb,SAAS,EAAC,+BAA+B;IACzCL,uBAAuB,EAAGA;EAAyB,GAEjDI,IACS,CAEL,CAAC;AAEZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMY,WAAW,GAAGA,CAAE;EAC5BX,SAAS;EACTC,QAAQ;EACR,GAAGP;AAC4D,CAAC,KAAM;EACtE,OACC,IAAAS,MAAA,CAAAC,aAAA,EAACd,kBAAA,CAAAwB,iBAAiB;IAAA,GACZpB,KAAK;IACVM,SAAS,EAAG,IAAAe,mBAAU,EACrB,gCAAgC,EAChCf,SACD;EAAG,GAEDC,QACgB,CAAC;AAEtB,CAAC;AAACe,OAAA,CAAAL,WAAA,GAAAA,WAAA;AAEK,MAAMM,WAAW,GAAGC,MAAM,CAACC,MAAM,CACvC,IAAAC,iCAAwB,EAAE3B,sBAAsB,EAAE,aAAc,CAAC,EACjE;EAAEkB;AAAY,CACf,CAAC;AAACK,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAAA,IAAAI,QAAA,GAEaJ,WAAW;AAAAD,OAAA,CAAAM,OAAA,GAAAD,QAAA"}