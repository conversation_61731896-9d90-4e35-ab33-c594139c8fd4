{"version": 3, "names": ["_react", "require", "_utils", "_space", "_EMOTION_STRINGIFIED_CSS_ERROR__", "Wrapper", "_base", "default", "process", "env", "NODE_ENV", "target", "label", "font", "boxSizingReset", "exports", "deprecatedMarginField", "__nextHasNoMarginBottom", "css", "space", "StyledField", "labelStyles", "baseLabelTypography", "StyledLabel", "_ref", "name", "styles", "map", "toString", "deprecatedMarginHelp", "StyledHelp", "COLORS", "gray", "StyledVisualLabel"], "sources": ["@wordpress/components/src/base-control/styles/base-control-styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { baseLabelTypography, boxSizingReset, font, COLORS } from '../../utils';\nimport { space } from '../../utils/space';\n\nexport const Wrapper = styled.div`\n\tfont-family: ${ font( 'default.fontFamily' ) };\n\tfont-size: ${ font( 'default.fontSize' ) };\n\n\t${ boxSizingReset }\n`;\n\nconst deprecatedMarginField = ( { __nextHasNoMarginBottom = false } ) => {\n\treturn (\n\t\t! __nextHasNoMarginBottom &&\n\t\tcss`\n\t\t\tmargin-bottom: ${ space( 2 ) };\n\t\t`\n\t);\n};\n\nexport const StyledField = styled.div`\n\t${ deprecatedMarginField }\n\n\t.components-panel__row & {\n\t\tmargin-bottom: inherit;\n\t}\n`;\n\nconst labelStyles = css`\n\t${ baseLabelTypography };\n\n\tdisplay: inline-block;\n\tmargin-bottom: ${ space( 2 ) };\n\t/**\n\t * Removes Chrome/Safari/Firefox user agent stylesheet padding from\n\t * StyledLabel when it is rendered as a legend.\n\t */\n\tpadding: 0;\n`;\n\nexport const StyledLabel = styled.label`\n\t${ labelStyles }\n`;\n\nconst deprecatedMarginHelp = ( { __nextHasNoMarginBottom = false } ) => {\n\treturn (\n\t\t! __nextHasNoMarginBottom &&\n\t\tcss`\n\t\t\tmargin-bottom: revert;\n\t\t`\n\t);\n};\n\nexport const StyledHelp = styled.p`\n\tmargin-top: ${ space( 2 ) };\n\tmargin-bottom: 0;\n\tfont-size: ${ font( 'helpText.fontSize' ) };\n\tfont-style: normal;\n\tcolor: ${ COLORS.gray[ 700 ] };\n\n\t${ deprecatedMarginHelp }\n`;\n\nexport const StyledVisualLabel = styled.span`\n\t${ labelStyles }\n`;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAA0C,SAAAG,iCAAA;AAEnC,MAAMC,OAAO,OAAAC,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,mBACH,IAAAC,WAAI,EAAE,oBAAqB,CAAC,iBAC9B,IAAAA,WAAI,EAAE,kBAAmB,CAAC,OAErCC,qBAAc,SAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,w+EACjB;AAACK,OAAA,CAAAV,OAAA,GAAAA,OAAA;AAEF,MAAMW,qBAAqB,GAAGA,CAAE;EAAEC,uBAAuB,GAAG;AAAM,CAAC,KAAM;EACxE,OACC,CAAEA,uBAAuB,qBACzBC,UAAG,oBACgB,IAAAC,YAAK,EAAE,CAAE,CAAC,SAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,2DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,u+EAC5B;AAEH,CAAC;AAEM,MAAMU,WAAW,OAAAd,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GACpBI,qBAAqB,yDAAAR,OAAA,CAAAC,GAAA,CAAAC,QAAA,4+EAKxB;AAACK,OAAA,CAAAK,WAAA,GAAAA,WAAA;AAEF,MAAMC,WAAW,oBAAGH,UAAG,EACnBI,0BAAmB,0CAGJ,IAAAH,YAAK,EAAE,CAAE,CAAC,mBAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,iDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,2+EAM5B;AAEM,MAAMa,WAAW,OAAAjB,KAAA,CAAAC,OAAA,WAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GACpBS,WAAW,SAAAb,OAAA,CAAAC,GAAA,CAAAC,QAAA,4+EACd;AAACK,OAAA,CAAAQ,WAAA,GAAAA,WAAA;AAAA,IAAAC,IAAA,GAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAe,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAxB;AAAA;AAEF,MAAMyB,oBAAoB,GAAGA,CAAE;EAAEZ,uBAAuB,GAAG;AAAM,CAAC,KAAM;EACvE,OACC,CAAEA,uBAAuB,IAAAO,IAGxB;AAEH,CAAC;AAEM,MAAMM,UAAU,OAAAxB,KAAA,CAAAC,OAAA,OAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,kBACP,IAAAO,YAAK,EAAE,CAAE,CAAC,iCAEX,IAAAN,WAAI,EAAE,mBAAoB,CAAC,+BAE/BkB,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,OAEzBH,oBAAoB,SAAArB,OAAA,CAAAC,GAAA,CAAAC,QAAA,4+EACvB;AAACK,OAAA,CAAAe,UAAA,GAAAA,UAAA;AAEK,MAAMG,iBAAiB,OAAA3B,KAAA,CAAAC,OAAA,UAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAC1BS,WAAW,SAAAb,OAAA,CAAAC,GAAA,CAAAC,QAAA,4+EACd;AAACK,OAAA,CAAAkB,iBAAA,GAAAA,iBAAA"}