{"version": 3, "names": ["_element", "require", "_context", "styles", "_interopRequireWildcard", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useCardDivider", "props", "className", "otherProps", "useContextSystem", "cx", "useCx", "classes", "useMemo", "Divider", "borderColor"], "sources": ["@wordpress/components/src/card/card-divider/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport * as styles from '../styles';\nimport { useCx } from '../../utils/hooks/use-cx';\nimport type { DividerProps } from '../../divider';\n\nexport function useCardDivider(\n\tprops: WordPressComponentProps< DividerProps, 'hr', false >\n) {\n\tconst { className, ...otherProps } = useContextSystem(\n\t\tprops,\n\t\t'CardDivider'\n\t);\n\n\tconst cx = useCx();\n\n\tconst classes = useMemo(\n\t\t() =>\n\t\t\tcx(\n\t\t\t\tstyles.Divider,\n\t\t\t\tstyles.borderColor,\n\t\t\t\t// This classname is added for legacy compatibility reasons.\n\t\t\t\t'components-card__divider',\n\t\t\t\tclassName\n\t\t\t),\n\t\t[ className, cx ]\n\t);\n\n\treturn {\n\t\t...otherProps,\n\t\tclassName: classes,\n\t};\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAiD,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAXjD;AACA;AACA;;AAYO,SAASW,cAAcA,CAC7BC,KAA2D,EAC1D;EACD,MAAM;IAAEC,SAAS;IAAE,GAAGC;EAAW,CAAC,GAAG,IAAAC,yBAAgB,EACpDH,KAAK,EACL,aACD,CAAC;EAED,MAAMI,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EACtB,MACCH,EAAE,CACD9B,MAAM,CAACkC,OAAO,EACdlC,MAAM,CAACmC,WAAW;EAClB;EACA,0BAA0B,EAC1BR,SACD,CAAC,EACF,CAAEA,SAAS,EAAEG,EAAE,CAChB,CAAC;EAED,OAAO;IACN,GAAGF,UAAU;IACbD,SAAS,EAAEK;EACZ,CAAC;AACF"}