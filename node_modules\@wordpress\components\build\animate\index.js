"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Animate = Animate;
exports.default = void 0;
exports.getAnimateClassName = getAnimateClassName;
var _classnames = _interopRequireDefault(require("classnames"));
/**
 * External dependencies
 */

/**
 * @param type The animation type
 * @return Default origin
 */
function getDefaultOrigin(type) {
  return type === 'appear' ? 'top' : 'left';
}

/**
 * @param options
 *
 * @return ClassName that applies the animations
 */
function getAnimateClassName(options) {
  if (options.type === 'loading') {
    return (0, _classnames.default)('components-animate__loading');
  }
  const {
    type,
    origin = getDefaultOrigin(type)
  } = options;
  if (type === 'appear') {
    const [yAxis, xAxis = 'center'] = origin.split(' ');
    return (0, _classnames.default)('components-animate__appear', {
      ['is-from-' + xAxis]: xAxis !== 'center',
      ['is-from-' + yAxis]: yAxis !== 'middle'
    });
  }
  if (type === 'slide-in') {
    return (0, _classnames.default)('components-animate__slide-in', 'is-from-' + origin);
  }
  return undefined;
}

/**
 * Simple interface to introduce animations to components.
 *
 * ```jsx
 * import { Animate, Notice } from '@wordpress/components';
 *
 * const MyAnimatedNotice = () => (
 * 	<Animate type="slide-in" options={ { origin: 'top' } }>
 * 		{ ( { className } ) => (
 * 			<Notice className={ className } status="success">
 * 				<p>Animation finished.</p>
 * 			</Notice>
 * 		) }
 * 	</Animate>
 * );
 * ```
 */
function Animate({
  type,
  options = {},
  children
}) {
  return children({
    className: getAnimateClassName({
      type,
      ...options
    })
  });
}
var _default = Animate;
exports.default = _default;
//# sourceMappingURL=index.js.map