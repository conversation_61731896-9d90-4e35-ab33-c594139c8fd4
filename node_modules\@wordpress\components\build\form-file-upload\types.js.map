{"version": 3, "names": [], "sources": ["@wordpress/components/src/form-file-upload/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ComponentProps, InputHTMLAttributes, ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type Icon from '../icon';\n\n// TODO: Replace `children` and `icon` types with props from <PERSON><PERSON> once <PERSON><PERSON> is typed.\nexport type FormFileUploadProps = {\n\t/**\n\t * A string passed to `input` element that tells the browser which file types can be\n\t * upload to the upload by the user use. e.g: `image/*,video/*`.\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#Unique_file_type_specifiers.\n\t */\n\taccept?: InputHTMLAttributes< HTMLInputElement >[ 'accept' ];\n\t/**\n\t * Children are passed as children of `<PERSON><PERSON>`.\n\t */\n\tchildren?: ReactNode;\n\t/**\n\t * The icon to render in the `Button`.\n\t */\n\ticon?: ComponentProps< typeof Icon >[ 'icon' ];\n\t/**\n\t * Whether to allow multiple selection of files or not.\n\t */\n\tmultiple?: InputHTMLAttributes< HTMLInputElement >[ 'multiple' ];\n\t/**\n\t * Callback function passed directly to the `input` file element.\n\t *\n\t * Select files will be available in `event.currentTarget.files`.\n\t */\n\tonChange: InputHTMLAttributes< HTMLInputElement >[ 'onChange' ];\n\t/**\n\t * Callback function passed directly to the `input` file element.\n\t *\n\t * This can be useful when you want to force a `change` event to fire when\n\t * the user chooses the same file again. To do this, set the target value to\n\t * an empty string in the `onClick` function.\n\t *\n\t * ```jsx\n\t * <FormFileUpload\n\t * \tonClick={ ( event ) => ( event.target.value = '' ) }\n\t * \tonChange={ onChange }\n\t * >\n\t * \tUpload\n\t * </FormFileUpload>\n\t * ```\n\t */\n\tonClick?: InputHTMLAttributes< HTMLInputElement >[ 'onClick' ];\n\t/**\n\t * Optional callback function used to render the UI.\n\t *\n\t * If passed, the component does not render the default UI (a button) and\n\t * calls this function to render it. The function receives an object with\n\t * property `openFileDialog`, a function that, when called, opens the browser\n\t * native file upload modal window.\n\t */\n\trender?: ( arg: { openFileDialog: () => void } ) => ReactNode;\n};\n"], "mappings": ""}