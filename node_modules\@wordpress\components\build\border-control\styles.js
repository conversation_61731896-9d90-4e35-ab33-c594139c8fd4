"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.wrapperWidth = exports.wrapperHeight = exports.resetButton = exports.innerWrapper = exports.colorIndicatorWrapper = exports.colorIndicatorBorder = exports.borderSlider = exports.borderControlPopoverControls = exports.borderControlPopoverContent = exports.borderControlDropdown = exports.borderControl = exports.borderColorIndicator = void 0;
var _react = require("@emotion/react");
var _utils = require("../utils");
var _space = require("../utils/space");
var _baseControlStyles = require("../base-control/styles/base-control-styles");
var _unitControlStyles = require("../unit-control/styles/unit-control-styles");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const labelStyles = process.env.NODE_ENV === "production" ? {
  name: "f3vz0n",
  styles: "font-weight:500"
} : {
  name: "rst95r-labelStyles",
  styles: "font-weight:500;label:labelStyles;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
const focusBoxShadow = /*#__PURE__*/(0, _react.css)("box-shadow:inset ", _utils.CONFIG.controlBoxShadowFocus, ";" + (process.env.NODE_ENV === "production" ? "" : ";label:focusBoxShadow;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
const borderControl = /*#__PURE__*/(0, _react.css)("border:0;padding:0;margin:0;", _utils.boxSizingReset, ";" + (process.env.NODE_ENV === "production" ? "" : ";label:borderControl;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderControl = borderControl;
const innerWrapper = () => /*#__PURE__*/(0, _react.css)(_unitControlStyles.ValueInput, "{flex:1 1 40%;}&& ", _unitControlStyles.UnitSelect, "{min-height:0;}" + (process.env.NODE_ENV === "production" ? "" : ";label:innerWrapper;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");

/*
 * This style is only applied to the UnitControl wrapper when the border width
 * field should be a set width. Omitting this allows the UnitControl &
 * RangeControl to share the available width in a 40/60 split respectively.
 */
exports.innerWrapper = innerWrapper;
const wrapperWidth = /*#__PURE__*/(0, _react.css)(_unitControlStyles.ValueInput, "{flex:0 0 auto;}" + (process.env.NODE_ENV === "production" ? "" : ";label:wrapperWidth;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.wrapperWidth = wrapperWidth;
const wrapperHeight = size => {
  return /*#__PURE__*/(0, _react.css)("height:", size === '__unstable-large' ? '40px' : '30px', ";" + (process.env.NODE_ENV === "production" ? "" : ";label:wrapperHeight;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm9yZGVyLWNvbnRyb2wvc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQXdEVyIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2JvcmRlci1jb250cm9sL3N0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IGNzcyB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcblxuLyoqXG4gKiBJbnRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHsgQ09MT1JTLCBDT05GSUcsIGJveFNpemluZ1Jlc2V0LCBydGwgfSBmcm9tICcuLi91dGlscyc7XG5pbXBvcnQgeyBzcGFjZSB9IGZyb20gJy4uL3V0aWxzL3NwYWNlJztcbmltcG9ydCB7IFN0eWxlZExhYmVsIH0gZnJvbSAnLi4vYmFzZS1jb250cm9sL3N0eWxlcy9iYXNlLWNvbnRyb2wtc3R5bGVzJztcbmltcG9ydCB7XG5cdFZhbHVlSW5wdXQgYXMgVW5pdENvbnRyb2xXcmFwcGVyLFxuXHRVbml0U2VsZWN0LFxufSBmcm9tICcuLi91bml0LWNvbnRyb2wvc3R5bGVzL3VuaXQtY29udHJvbC1zdHlsZXMnO1xuXG5pbXBvcnQgdHlwZSB7IEJvcmRlciB9IGZyb20gJy4vdHlwZXMnO1xuXG5jb25zdCBsYWJlbFN0eWxlcyA9IGNzc2Bcblx0Zm9udC13ZWlnaHQ6IDUwMDtcbmA7XG5cbmNvbnN0IGZvY3VzQm94U2hhZG93ID0gY3NzYFxuXHRib3gtc2hhZG93OiBpbnNldCAkeyBDT05GSUcuY29udHJvbEJveFNoYWRvd0ZvY3VzIH07XG5gO1xuXG5leHBvcnQgY29uc3QgYm9yZGVyQ29udHJvbCA9IGNzc2Bcblx0Ym9yZGVyOiAwO1xuXHRwYWRkaW5nOiAwO1xuXHRtYXJnaW46IDA7XG5cdCR7IGJveFNpemluZ1Jlc2V0IH1cbmA7XG5cbmV4cG9ydCBjb25zdCBpbm5lcldyYXBwZXIgPSAoKSA9PiBjc3NgXG5cdCR7IFVuaXRDb250cm9sV3JhcHBlciB9IHtcblx0XHRmbGV4OiAxIDEgNDAlO1xuXHR9XG5cdCYmICR7IFVuaXRTZWxlY3QgfSB7XG5cdFx0LyogUHJldmVudCB1bml0IHNlbGVjdCBmb3JjaW5nIG1pbiBoZWlnaHQgbGFyZ2VyIHRoYW4gaXRzIFVuaXRDb250cm9sICovXG5cdFx0bWluLWhlaWdodDogMDtcblx0fVxuYDtcblxuLypcbiAqIFRoaXMgc3R5bGUgaXMgb25seSBhcHBsaWVkIHRvIHRoZSBVbml0Q29udHJvbCB3cmFwcGVyIHdoZW4gdGhlIGJvcmRlciB3aWR0aFxuICogZmllbGQgc2hvdWxkIGJlIGEgc2V0IHdpZHRoLiBPbWl0dGluZyB0aGlzIGFsbG93cyB0aGUgVW5pdENvbnRyb2wgJlxuICogUmFuZ2VDb250cm9sIHRvIHNoYXJlIHRoZSBhdmFpbGFibGUgd2lkdGggaW4gYSA0MC82MCBzcGxpdCByZXNwZWN0aXZlbHkuXG4gKi9cbmV4cG9ydCBjb25zdCB3cmFwcGVyV2lkdGggPSBjc3NgXG5cdCR7IFVuaXRDb250cm9sV3JhcHBlciB9IHtcblx0XHQvKiBGb3JjZSB0aGUgVW5pdENvbnRyb2wncyBzZXQgd2lkdGguICovXG5cdFx0ZmxleDogMCAwIGF1dG87XG5cdH1cbmA7XG5cbmV4cG9ydCBjb25zdCB3cmFwcGVySGVpZ2h0ID0gKCBzaXplPzogJ2RlZmF1bHQnIHwgJ19fdW5zdGFibGUtbGFyZ2UnICkgPT4ge1xuXHRyZXR1cm4gY3NzYFxuXHRcdGhlaWdodDogJHsgc2l6ZSA9PT0gJ19fdW5zdGFibGUtbGFyZ2UnID8gJzQwcHgnIDogJzMwcHgnIH07XG5cdGA7XG59O1xuXG5leHBvcnQgY29uc3QgYm9yZGVyQ29udHJvbERyb3Bkb3duID0gY3NzYFxuXHRiYWNrZ3JvdW5kOiAjZmZmO1xuXG5cdCYmID4gYnV0dG9uIHtcblx0XHRhc3BlY3QtcmF0aW86IDE7XG5cdFx0cGFkZGluZzogMDtcblx0XHRkaXNwbGF5OiBmbGV4O1xuXHRcdGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cdFx0anVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cdFx0JHsgcnRsKFxuXHRcdFx0eyBib3JkZXJSYWRpdXM6IGAycHggMCAwIDJweGAgfSxcblx0XHRcdHsgYm9yZGVyUmFkaXVzOiBgMCAycHggMnB4IDBgIH1cblx0XHQpKCkgfVxuXHRcdGJvcmRlcjogJHsgQ09ORklHLmJvcmRlcldpZHRoIH0gc29saWQgJHsgQ09MT1JTLnVpLmJvcmRlciB9O1xuXG5cdFx0Jjpmb2N1cyxcblx0XHQmOmhvdmVyOm5vdCggOmRpc2FibGVkICkge1xuXHRcdFx0JHsgZm9jdXNCb3hTaGFkb3cgfVxuXHRcdFx0Ym9yZGVyLWNvbG9yOiAkeyBDT0xPUlMudWkuYm9yZGVyRm9jdXMgfTtcblx0XHRcdHotaW5kZXg6IDE7XG5cdFx0XHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdFx0fVxuXHR9XG5gO1xuXG5leHBvcnQgY29uc3QgY29sb3JJbmRpY2F0b3JCb3JkZXIgPSAoIGJvcmRlcj86IEJvcmRlciApID0+IHtcblx0Y29uc3QgeyBjb2xvciwgc3R5bGUgfSA9IGJvcmRlciB8fCB7fTtcblxuXHRjb25zdCBmYWxsYmFja0NvbG9yID1cblx0XHQhISBzdHlsZSAmJiBzdHlsZSAhPT0gJ25vbmUnID8gQ09MT1JTLmdyYXlbIDMwMCBdIDogdW5kZWZpbmVkO1xuXG5cdHJldHVybiBjc3NgXG5cdFx0Ym9yZGVyLXN0eWxlOiAkeyBzdHlsZSA9PT0gJ25vbmUnID8gJ3NvbGlkJyA6IHN0eWxlIH07XG5cdFx0Ym9yZGVyLWNvbG9yOiAkeyBjb2xvciB8fCBmYWxsYmFja0NvbG9yIH07XG5cdGA7XG59O1xuXG5leHBvcnQgY29uc3QgY29sb3JJbmRpY2F0b3JXcmFwcGVyID0gKFxuXHRib3JkZXI/OiBCb3JkZXIsXG5cdHNpemU/OiAnZGVmYXVsdCcgfCAnX191bnN0YWJsZS1sYXJnZSdcbikgPT4ge1xuXHRjb25zdCB7IHN0eWxlIH0gPSBib3JkZXIgfHwge307XG5cblx0cmV0dXJuIGNzc2Bcblx0XHRib3JkZXItcmFkaXVzOiA5OTk5cHg7XG5cdFx0Ym9yZGVyOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XG5cdFx0JHsgc3R5bGUgPyBjb2xvckluZGljYXRvckJvcmRlciggYm9yZGVyICkgOiB1bmRlZmluZWQgfVxuXHRcdHdpZHRoOiAkeyBzaXplID09PSAnX191bnN0YWJsZS1sYXJnZScgPyAnMjRweCcgOiAnMjJweCcgfTtcblx0XHRoZWlnaHQ6ICR7IHNpemUgPT09ICdfX3Vuc3RhYmxlLWxhcmdlJyA/ICcyNHB4JyA6ICcyMnB4JyB9O1xuXHRcdHBhZGRpbmc6ICR7IHNpemUgPT09ICdfX3Vuc3RhYmxlLWxhcmdlJyA/ICcycHgnIDogJzFweCcgfTtcblxuXHRcdC8qXG5cdFx0ICogQ29sb3JJbmRpY2F0b3Jcblx0XHQgKlxuXHRcdCAqIFRoZSB0cmFuc3BhcmVudCBjb2xvcnMgdXNlZCBoZXJlIGVuc3VyZSB2aXNpYmlsaXR5IG9mIHRoZSBpbmRpY2F0b3Jcblx0XHQgKiBvdmVyIHRoZSBhY3RpdmUgc3RhdGUgb2YgdGhlIGJvcmRlciBjb250cm9sIGRyb3Bkb3duJ3MgdG9nZ2xlIGJ1dHRvbi5cblx0XHQgKi9cblx0XHQmID4gc3BhbiB7XG5cdFx0XHRoZWlnaHQ6ICR7IHNwYWNlKCA0ICkgfTtcblx0XHRcdHdpZHRoOiAkeyBzcGFjZSggNCApIH07XG5cdFx0XHRiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoXG5cdFx0XHRcdC00NWRlZyxcblx0XHRcdFx0dHJhbnNwYXJlbnQgNDglLFxuXHRcdFx0XHRyZ2IoIDAgMCAwIC8gMjAlICkgNDglLFxuXHRcdFx0XHRyZ2IoIDAgMCAwIC8gMjAlICkgNTIlLFxuXHRcdFx0XHR0cmFuc3BhcmVudCA1MiVcblx0XHRcdCk7XG5cdFx0fVxuXHRgO1xufTtcblxuLy8gTXVzdCBlcXVhbCAkY29sb3ItcGFsZXR0ZS1jaXJjbGUtc2l6ZSBmcm9tOlxuLy8gQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9jaXJjdWxhci1vcHRpb24tcGlja2VyL3N0eWxlLnNjc3NcbmNvbnN0IHN3YXRjaFNpemUgPSAyODtcbmNvbnN0IHN3YXRjaEdhcCA9IDEyO1xuXG5leHBvcnQgY29uc3QgYm9yZGVyQ29udHJvbFBvcG92ZXJDb250cm9scyA9IGNzc2Bcblx0d2lkdGg6ICR7IHN3YXRjaFNpemUgKiA2ICsgc3dhdGNoR2FwICogNSB9cHg7XG5cblx0PiBkaXY6Zmlyc3Qtb2YtdHlwZSA+ICR7IFN0eWxlZExhYmVsIH0ge1xuXHRcdG1hcmdpbi1ib3R0b206IDA7XG5cdFx0JHsgbGFiZWxTdHlsZXMgfVxuXHR9XG5cblx0JiYgJHsgU3R5bGVkTGFiZWwgfSArIGJ1dHRvbjpub3QoIC5oYXMtdGV4dCApIHtcblx0XHRtaW4td2lkdGg6IDI0cHg7XG5cdFx0cGFkZGluZzogMDtcblx0fVxuYDtcblxuZXhwb3J0IGNvbnN0IGJvcmRlckNvbnRyb2xQb3BvdmVyQ29udGVudCA9IGNzc2BgO1xuZXhwb3J0IGNvbnN0IGJvcmRlckNvbG9ySW5kaWNhdG9yID0gY3NzYGA7XG5cbmV4cG9ydCBjb25zdCByZXNldEJ1dHRvbiA9IGNzc2Bcblx0anVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cdHdpZHRoOiAxMDAlO1xuXG5cdC8qIE92ZXJyaWRlIGJ1dHRvbiBjb21wb25lbnQgc3R5bGluZyAqL1xuXHQmJiB7XG5cdFx0Ym9yZGVyLXRvcDogJHsgQ09ORklHLmJvcmRlcldpZHRoIH0gc29saWQgJHsgQ09MT1JTLmdyYXlbIDQwMCBdIH07XG5cdFx0Ym9yZGVyLXRvcC1sZWZ0LXJhZGl1czogMDtcblx0XHRib3JkZXItdG9wLXJpZ2h0LXJhZGl1czogMDtcblx0XHRoZWlnaHQ6IDQwcHg7XG5cdH1cbmA7XG5cbmV4cG9ydCBjb25zdCBib3JkZXJTbGlkZXIgPSAoKSA9PiBjc3NgXG5cdGZsZXg6IDEgMSA2MCU7XG5cdCR7IHJ0bCggeyBtYXJnaW5SaWdodDogc3BhY2UoIDMgKSB9ICkoKSB9XG5gO1xuIl19 */");
};
exports.wrapperHeight = wrapperHeight;
const borderControlDropdown = /*#__PURE__*/(0, _react.css)("background:#fff;&&>button{aspect-ratio:1;padding:0;display:flex;align-items:center;justify-content:center;", (0, _utils.rtl)({
  borderRadius: `2px 0 0 2px`
}, {
  borderRadius: `0 2px 2px 0`
})(), " border:", _utils.CONFIG.borderWidth, " solid ", _utils.COLORS.ui.border, ";&:focus,&:hover:not( :disabled ){", focusBoxShadow, " border-color:", _utils.COLORS.ui.borderFocus, ";z-index:1;position:relative;}}" + (process.env.NODE_ENV === "production" ? "" : ";label:borderControlDropdown;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderControlDropdown = borderControlDropdown;
const colorIndicatorBorder = border => {
  const {
    color,
    style
  } = border || {};
  const fallbackColor = !!style && style !== 'none' ? _utils.COLORS.gray[300] : undefined;
  return /*#__PURE__*/(0, _react.css)("border-style:", style === 'none' ? 'solid' : style, ";border-color:", color || fallbackColor, ";" + (process.env.NODE_ENV === "production" ? "" : ";label:colorIndicatorBorder;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
exports.colorIndicatorBorder = colorIndicatorBorder;
const colorIndicatorWrapper = (border, size) => {
  const {
    style
  } = border || {};
  return /*#__PURE__*/(0, _react.css)("border-radius:9999px;border:2px solid transparent;", style ? colorIndicatorBorder(border) : undefined, " width:", size === '__unstable-large' ? '24px' : '22px', ";height:", size === '__unstable-large' ? '24px' : '22px', ";padding:", size === '__unstable-large' ? '2px' : '1px', ";&>span{height:", (0, _space.space)(4), ";width:", (0, _space.space)(4), ";background:linear-gradient(\n\t\t\t\t-45deg,\n\t\t\t\ttransparent 48%,\n\t\t\t\trgb( 0 0 0 / 20% ) 48%,\n\t\t\t\trgb( 0 0 0 / 20% ) 52%,\n\t\t\t\ttransparent 52%\n\t\t\t);}" + (process.env.NODE_ENV === "production" ? "" : ";label:colorIndicatorWrapper;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};

// Must equal $color-palette-circle-size from:
// @wordpress/components/src/circular-option-picker/style.scss
exports.colorIndicatorWrapper = colorIndicatorWrapper;
const swatchSize = 28;
const swatchGap = 12;
const borderControlPopoverControls = /*#__PURE__*/(0, _react.css)("width:", swatchSize * 6 + swatchGap * 5, "px;>div:first-of-type>", _baseControlStyles.StyledLabel, "{margin-bottom:0;", labelStyles, ";}&& ", _baseControlStyles.StyledLabel, "+button:not( .has-text ){min-width:24px;padding:0;}" + (process.env.NODE_ENV === "production" ? "" : ";label:borderControlPopoverControls;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderControlPopoverControls = borderControlPopoverControls;
const borderControlPopoverContent = /*#__PURE__*/(0, _react.css)(process.env.NODE_ENV === "production" ? "" : ";label:borderControlPopoverContent;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderControlPopoverContent = borderControlPopoverContent;
const borderColorIndicator = /*#__PURE__*/(0, _react.css)(process.env.NODE_ENV === "production" ? "" : ";label:borderColorIndicator;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderColorIndicator = borderColorIndicator;
const resetButton = /*#__PURE__*/(0, _react.css)("justify-content:center;width:100%;&&{border-top:", _utils.CONFIG.borderWidth, " solid ", _utils.COLORS.gray[400], ";border-top-left-radius:0;border-top-right-radius:0;height:40px;}" + (process.env.NODE_ENV === "production" ? "" : ";label:resetButton;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.resetButton = resetButton;
const borderSlider = () => /*#__PURE__*/(0, _react.css)("flex:1 1 60%;", (0, _utils.rtl)({
  marginRight: (0, _space.space)(3)
})(), ";" + (process.env.NODE_ENV === "production" ? "" : ";label:borderSlider;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderSlider = borderSlider;
//# sourceMappingURL=styles.js.map