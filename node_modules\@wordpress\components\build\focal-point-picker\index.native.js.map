{"version": 3, "names": ["_reactNative", "require", "_reactNativeVideo", "_interopRequireDefault", "_reactNativeBridge", "_i18n", "_components", "_element", "_compose", "_focalPoint", "_tooltip", "_style", "_utils", "_math", "MIN_POSITION_VALUE", "MAX_POSITION_VALUE", "FOCAL_POINT_UNITS", "default", "label", "value", "FocalPointPicker", "props", "focalPoint", "onChange", "shouldEnableBottomSheetScroll", "url", "isVideo", "isVideoType", "containerSize", "setContainerSize", "useState", "slider<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayPlaceholder", "setDisplayPlaceholder", "videoNaturalSize", "setVideoNaturalSize", "tooltipVisible", "setTooltipVisible", "locationPageOffsetX", "useRef", "locationPageOffsetY", "videoRef", "useEffect", "requestFocalPointPickerTooltipShown", "tooltipShown", "setFocalPointPickerTooltipShown", "pan", "Animated", "ValueXY", "current", "setValue", "x", "width", "y", "height", "panResponder", "useMemo", "PanResponder", "create", "onStartShouldSetPanResponder", "onStartShouldSetPanResponderCapture", "onMoveShouldSetPanResponder", "onMoveShouldSetPanResponderCapture", "onPanResponderGrant", "event", "locationX", "locationY", "pageX", "pageY", "nativeEvent", "extractOffset", "onPanResponderMove", "dx", "dy", "useNativeDriver", "onPanResponderRelease", "flattenOffset", "clamp", "toFixed", "prevState", "mediaBackground", "usePreferredColorSchemeStyle", "styles", "mediaBackgroundDark", "imagePreviewStyles", "mediaPlaceholder", "image", "videoPreviewStyles", "aspectRatio", "opacity", "video", "focalPointGroupStyles", "focalPointGroup", "transform", "translateX", "interpolate", "inputRange", "outputRange", "extrapolate", "translateY", "FOCAL_POINT_SIZE", "focalPointStyles", "StyleSheet", "flatten", "marginLeft", "marginTop", "onTooltipPress", "onMediaLayout", "layout", "onImageDataLoad", "onVideoLoad", "naturalSize", "seek", "onXCoordinateChange", "onYCoordinateChange", "_react", "createElement", "View", "style", "container", "onPress", "visible", "media", "panHandlers", "onLayout", "mediaContainer", "Image", "edit<PERSON><PERSON><PERSON>", "highlightSelected", "isSelected", "muted", "paused", "disable<PERSON><PERSON><PERSON>", "onLoad", "ref", "resizeMode", "source", "uri", "pointerEvents", "Label", "text", "__", "yOffset", "testID", "UnitControl", "key", "max", "min", "unit", "units", "Math", "round", "_default", "exports"], "sources": ["@wordpress/components/src/focal-point-picker/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Animated, PanResponder, StyleSheet, View } from 'react-native';\nimport Video from 'react-native-video';\n\n/**\n * WordPress dependencies\n */\nimport {\n\trequestFocalPointPickerTooltipShown,\n\tsetFocalPointPickerTooltipShown,\n} from '@wordpress/react-native-bridge';\nimport { __ } from '@wordpress/i18n';\nimport { Image, UnitControl } from '@wordpress/components';\nimport { useRef, useState, useMemo, useEffect } from '@wordpress/element';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport FocalPoint from './focal-point';\nimport Tooltip from './tooltip';\nimport styles from './style.scss';\nimport { isVideoType } from './utils';\nimport { clamp } from '../utils/math';\n\nconst MIN_POSITION_VALUE = 0;\nconst MAX_POSITION_VALUE = 100;\nconst FOCAL_POINT_UNITS = [ { default: 50, label: '%', value: '%' } ];\n\nfunction FocalPointPicker( props ) {\n\tconst { focalPoint, onChange, shouldEnableBottomSheetScroll, url } = props;\n\n\tconst isVideo = isVideoType( url );\n\n\tconst [ containerSize, setContainerSize ] = useState( null );\n\tconst [ sliderKey, setSliderKey ] = useState( 0 );\n\tconst [ displayPlaceholder, setDisplayPlaceholder ] = useState( true );\n\tconst [ videoNaturalSize, setVideoNaturalSize ] = useState( null );\n\tconst [ tooltipVisible, setTooltipVisible ] = useState( false );\n\n\tconst locationPageOffsetX = useRef();\n\tconst locationPageOffsetY = useRef();\n\tconst videoRef = useRef( null );\n\n\tuseEffect( () => {\n\t\trequestFocalPointPickerTooltipShown( ( tooltipShown ) => {\n\t\t\tif ( ! tooltipShown ) {\n\t\t\t\tsetTooltipVisible( true );\n\t\t\t\tsetFocalPointPickerTooltipShown( true );\n\t\t\t}\n\t\t} );\n\t}, [] );\n\n\t// Animated coordinates for drag handle.\n\tconst pan = useRef( new Animated.ValueXY() ).current;\n\n\t/**\n\t * Set drag handle position anytime focal point coordinates change.\n\t * E.g. initial render, dragging range sliders.\n\t */\n\tuseEffect( () => {\n\t\tif ( containerSize ) {\n\t\t\tpan.setValue( {\n\t\t\t\tx: focalPoint.x * containerSize.width,\n\t\t\t\ty: focalPoint.y * containerSize.height,\n\t\t\t} );\n\t\t}\n\t}, [ focalPoint, containerSize, pan ] );\n\n\t// Pan responder to manage drag handle interactivity.\n\tconst panResponder = useMemo(\n\t\t() =>\n\t\t\tPanResponder.create( {\n\t\t\t\tonStartShouldSetPanResponder: () => true,\n\t\t\t\tonStartShouldSetPanResponderCapture: () => true,\n\t\t\t\tonMoveShouldSetPanResponder: () => true,\n\t\t\t\tonMoveShouldSetPanResponderCapture: () => true,\n\n\t\t\t\tonPanResponderGrant: ( event ) => {\n\t\t\t\t\tshouldEnableBottomSheetScroll( false );\n\t\t\t\t\tconst {\n\t\t\t\t\t\tlocationX: x,\n\t\t\t\t\t\tlocationY: y,\n\t\t\t\t\t\tpageX,\n\t\t\t\t\t\tpageY,\n\t\t\t\t\t} = event.nativeEvent;\n\t\t\t\t\tlocationPageOffsetX.current = pageX - x;\n\t\t\t\t\tlocationPageOffsetY.current = pageY - y;\n\t\t\t\t\tpan.setValue( { x, y } ); // Set cursor to tap location.\n\t\t\t\t\tpan.extractOffset(); // Set offset to current value.\n\t\t\t\t},\n\t\t\t\t// Move cursor to match delta drag.\n\t\t\t\tonPanResponderMove: Animated.event(\n\t\t\t\t\t[ null, { dx: pan.x, dy: pan.y } ],\n\t\t\t\t\t{ useNativeDriver: false }\n\t\t\t\t),\n\t\t\t\tonPanResponderRelease: ( event ) => {\n\t\t\t\t\tshouldEnableBottomSheetScroll( true );\n\t\t\t\t\tpan.flattenOffset(); // Flatten offset into value.\n\t\t\t\t\tconst { pageX, pageY } = event.nativeEvent;\n\t\t\t\t\t// Ideally, x and y below are merely locationX and locationY from the\n\t\t\t\t\t// nativeEvent. However, we are required to compute these relative\n\t\t\t\t\t// coordinates to workaround a bug affecting Android's PanResponder.\n\t\t\t\t\t// Specifically, dragging the handle outside the bounds of the image\n\t\t\t\t\t// results in inaccurate locationX and locationY coordinates to be\n\t\t\t\t\t// reported. https://github.com/facebook/react-native/issues/15290#issuecomment-435494944\n\t\t\t\t\tconst x = pageX - locationPageOffsetX.current;\n\t\t\t\t\tconst y = pageY - locationPageOffsetY.current;\n\t\t\t\t\tonChange( {\n\t\t\t\t\t\tx: clamp( x / containerSize?.width, 0, 1 ).toFixed( 2 ),\n\t\t\t\t\t\ty: clamp( y / containerSize?.height, 0, 1 ).toFixed(\n\t\t\t\t\t\t\t2\n\t\t\t\t\t\t),\n\t\t\t\t\t} );\n\t\t\t\t\t// Slider (child of RangeCell) is uncontrolled, so we must increment a\n\t\t\t\t\t// key to re-mount and sync the pan gesture values to the sliders\n\t\t\t\t\t// https://github.com/callstack/react-native-slider/tree/v3.0.3#value\n\t\t\t\t\tsetSliderKey( ( prevState ) => prevState + 1 );\n\t\t\t\t},\n\t\t\t} ),\n\t\t[ containerSize, pan, onChange, shouldEnableBottomSheetScroll ]\n\t);\n\n\tconst mediaBackground = usePreferredColorSchemeStyle(\n\t\tstyles.mediaBackground,\n\t\tstyles.mediaBackgroundDark\n\t);\n\tconst imagePreviewStyles = [\n\t\tdisplayPlaceholder && styles.mediaPlaceholder,\n\t\tstyles.image,\n\t];\n\tconst videoPreviewStyles = [\n\t\t{\n\t\t\taspectRatio:\n\t\t\t\tvideoNaturalSize &&\n\t\t\t\tvideoNaturalSize.width / videoNaturalSize.height,\n\t\t\t// Hide Video component since it has black background while loading the source\n\t\t\topacity: displayPlaceholder ? 0 : 1,\n\t\t},\n\t\tstyles.video,\n\t\tdisplayPlaceholder && styles.mediaPlaceholder,\n\t];\n\tconst focalPointGroupStyles = [\n\t\tstyles.focalPointGroup,\n\t\t{\n\t\t\ttransform: [\n\t\t\t\t{\n\t\t\t\t\ttranslateX: pan.x.interpolate( {\n\t\t\t\t\t\tinputRange: [ 0, containerSize?.width || 0 ],\n\t\t\t\t\t\toutputRange: [ 0, containerSize?.width || 0 ],\n\t\t\t\t\t\textrapolate: 'clamp',\n\t\t\t\t\t} ),\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\ttranslateY: pan.y.interpolate( {\n\t\t\t\t\t\tinputRange: [ 0, containerSize?.height || 0 ],\n\t\t\t\t\t\toutputRange: [ 0, containerSize?.height || 0 ],\n\t\t\t\t\t\textrapolate: 'clamp',\n\t\t\t\t\t} ),\n\t\t\t\t},\n\t\t\t],\n\t\t},\n\t];\n\tconst FOCAL_POINT_SIZE = 50;\n\tconst focalPointStyles = StyleSheet.flatten( [\n\t\tstyles.focalPoint,\n\t\t{\n\t\t\theight: FOCAL_POINT_SIZE,\n\t\t\tmarginLeft: -( FOCAL_POINT_SIZE / 2 ),\n\t\t\tmarginTop: -( FOCAL_POINT_SIZE / 2 ),\n\t\t\twidth: FOCAL_POINT_SIZE,\n\t\t},\n\t] );\n\n\tconst onTooltipPress = () => setTooltipVisible( false );\n\tconst onMediaLayout = ( event ) => {\n\t\tconst { height, width } = event.nativeEvent.layout;\n\n\t\tif (\n\t\t\twidth !== 0 &&\n\t\t\theight !== 0 &&\n\t\t\t( containerSize?.width !== width ||\n\t\t\t\tcontainerSize?.height !== height )\n\t\t) {\n\t\t\tsetContainerSize( { width, height } );\n\t\t}\n\t};\n\tconst onImageDataLoad = () => setDisplayPlaceholder( false );\n\tconst onVideoLoad = ( event ) => {\n\t\tconst { height, width } = event.naturalSize;\n\t\tsetVideoNaturalSize( { height, width } );\n\t\tsetDisplayPlaceholder( false );\n\t\t// Avoid invisible, paused video on Android, presumably related to\n\t\t// https://github.com/react-native-video/react-native-video/issues/1979\n\t\tvideoRef?.current.seek( 0 );\n\t};\n\tconst onXCoordinateChange = ( x ) =>\n\t\tonChange( { x: ( x / 100 ).toFixed( 2 ) } );\n\tconst onYCoordinateChange = ( y ) =>\n\t\tonChange( { y: ( y / 100 ).toFixed( 2 ) } );\n\n\treturn (\n\t\t<View style={ styles.container }>\n\t\t\t<Tooltip onPress={ onTooltipPress } visible={ tooltipVisible }>\n\t\t\t\t<View style={ [ styles.media, mediaBackground ] }>\n\t\t\t\t\t<View\n\t\t\t\t\t\t{ ...panResponder.panHandlers }\n\t\t\t\t\t\tonLayout={ onMediaLayout }\n\t\t\t\t\t\tstyle={ styles.mediaContainer }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ ! isVideo && (\n\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\teditButton={ false }\n\t\t\t\t\t\t\t\thighlightSelected={ false }\n\t\t\t\t\t\t\t\tisSelected={ ! displayPlaceholder }\n\t\t\t\t\t\t\t\theight=\"100%\"\n\t\t\t\t\t\t\t\turl={ url }\n\t\t\t\t\t\t\t\tstyle={ imagePreviewStyles }\n\t\t\t\t\t\t\t\tonImageDataLoad={ onImageDataLoad }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t) }\n\t\t\t\t\t\t{ isVideo && (\n\t\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\t\tmuted\n\t\t\t\t\t\t\t\tpaused\n\t\t\t\t\t\t\t\tdisableFocus\n\t\t\t\t\t\t\t\tonLoad={ onVideoLoad }\n\t\t\t\t\t\t\t\tref={ videoRef }\n\t\t\t\t\t\t\t\tresizeMode=\"contain\"\n\t\t\t\t\t\t\t\tsource={ { uri: url } }\n\t\t\t\t\t\t\t\tstyle={ videoPreviewStyles }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t) }\n\t\t\t\t\t\t{ ! displayPlaceholder && (\n\t\t\t\t\t\t\t<Animated.View\n\t\t\t\t\t\t\t\tpointerEvents=\"none\"\n\t\t\t\t\t\t\t\tstyle={ focalPointGroupStyles }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<Tooltip.Label\n\t\t\t\t\t\t\t\t\ttext={ __( 'Drag to adjust focal point' ) }\n\t\t\t\t\t\t\t\t\tyOffset={ -( FOCAL_POINT_SIZE / 2 ) }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<FocalPoint\n\t\t\t\t\t\t\t\t\theight={ focalPointStyles.height }\n\t\t\t\t\t\t\t\t\tstyle={ focalPointStyles }\n\t\t\t\t\t\t\t\t\ttestID=\"focal-point-picker-handle\"\n\t\t\t\t\t\t\t\t\twidth={ focalPointStyles.width }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</Animated.View>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</View>\n\t\t\t\t</View>\n\t\t\t\t<UnitControl\n\t\t\t\t\tkey={ `xAxis-${ sliderKey }` }\n\t\t\t\t\tlabel={ __( 'X-Axis Position' ) }\n\t\t\t\t\tmax={ MAX_POSITION_VALUE }\n\t\t\t\t\tmin={ MIN_POSITION_VALUE }\n\t\t\t\t\tonChange={ onXCoordinateChange }\n\t\t\t\t\tunit=\"%\"\n\t\t\t\t\tunits={ FOCAL_POINT_UNITS }\n\t\t\t\t\tvalue={ Math.round( focalPoint.x * 100 ) }\n\t\t\t\t/>\n\t\t\t\t<UnitControl\n\t\t\t\t\tkey={ `yAxis-${ sliderKey }` }\n\t\t\t\t\tlabel={ __( 'Y-Axis Position' ) }\n\t\t\t\t\tmax={ MAX_POSITION_VALUE }\n\t\t\t\t\tmin={ MIN_POSITION_VALUE }\n\t\t\t\t\tonChange={ onYCoordinateChange }\n\t\t\t\t\tunit=\"%\"\n\t\t\t\t\tunits={ FOCAL_POINT_UNITS }\n\t\t\t\t\tvalue={ Math.round( focalPoint.y * 100 ) }\n\t\t\t\t/>\n\t\t\t</Tooltip>\n\t\t</View>\n\t);\n}\n\nexport default FocalPointPicker;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,kBAAA,GAAAH,OAAA;AAIA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AAKA,IAAAQ,WAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,QAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,KAAA,GAAAZ,OAAA;AAzBA;AACA;AACA;;AAIA;AACA;AACA;;AAUA;AACA;AACA;;AAOA,MAAMa,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,kBAAkB,GAAG,GAAG;AAC9B,MAAMC,iBAAiB,GAAG,CAAE;EAAEC,OAAO,EAAE,EAAE;EAAEC,KAAK,EAAE,GAAG;EAAEC,KAAK,EAAE;AAAI,CAAC,CAAE;AAErE,SAASC,gBAAgBA,CAAEC,KAAK,EAAG;EAClC,MAAM;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,6BAA6B;IAAEC;EAAI,CAAC,GAAGJ,KAAK;EAE1E,MAAMK,OAAO,GAAG,IAAAC,kBAAW,EAAEF,GAAI,CAAC;EAElC,MAAM,CAAEG,aAAa,EAAEC,gBAAgB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,IAAK,CAAC;EAC5D,MAAM,CAAEC,SAAS,EAAEC,YAAY,CAAE,GAAG,IAAAF,iBAAQ,EAAE,CAAE,CAAC;EACjD,MAAM,CAAEG,kBAAkB,EAAEC,qBAAqB,CAAE,GAAG,IAAAJ,iBAAQ,EAAE,IAAK,CAAC;EACtE,MAAM,CAAEK,gBAAgB,EAAEC,mBAAmB,CAAE,GAAG,IAAAN,iBAAQ,EAAE,IAAK,CAAC;EAClE,MAAM,CAAEO,cAAc,EAAEC,iBAAiB,CAAE,GAAG,IAAAR,iBAAQ,EAAE,KAAM,CAAC;EAE/D,MAAMS,mBAAmB,GAAG,IAAAC,eAAM,EAAC,CAAC;EACpC,MAAMC,mBAAmB,GAAG,IAAAD,eAAM,EAAC,CAAC;EACpC,MAAME,QAAQ,GAAG,IAAAF,eAAM,EAAE,IAAK,CAAC;EAE/B,IAAAG,kBAAS,EAAE,MAAM;IAChB,IAAAC,sDAAmC,EAAIC,YAAY,IAAM;MACxD,IAAK,CAAEA,YAAY,EAAG;QACrBP,iBAAiB,CAAE,IAAK,CAAC;QACzB,IAAAQ,kDAA+B,EAAE,IAAK,CAAC;MACxC;IACD,CAAE,CAAC;EACJ,CAAC,EAAE,EAAG,CAAC;;EAEP;EACA,MAAMC,GAAG,GAAG,IAAAP,eAAM,EAAE,IAAIQ,qBAAQ,CAACC,OAAO,CAAC,CAAE,CAAC,CAACC,OAAO;;EAEpD;AACD;AACA;AACA;EACC,IAAAP,kBAAS,EAAE,MAAM;IAChB,IAAKf,aAAa,EAAG;MACpBmB,GAAG,CAACI,QAAQ,CAAE;QACbC,CAAC,EAAE9B,UAAU,CAAC8B,CAAC,GAAGxB,aAAa,CAACyB,KAAK;QACrCC,CAAC,EAAEhC,UAAU,CAACgC,CAAC,GAAG1B,aAAa,CAAC2B;MACjC,CAAE,CAAC;IACJ;EACD,CAAC,EAAE,CAAEjC,UAAU,EAAEM,aAAa,EAAEmB,GAAG,CAAG,CAAC;;EAEvC;EACA,MAAMS,YAAY,GAAG,IAAAC,gBAAO,EAC3B,MACCC,yBAAY,CAACC,MAAM,CAAE;IACpBC,4BAA4B,EAAEA,CAAA,KAAM,IAAI;IACxCC,mCAAmC,EAAEA,CAAA,KAAM,IAAI;IAC/CC,2BAA2B,EAAEA,CAAA,KAAM,IAAI;IACvCC,kCAAkC,EAAEA,CAAA,KAAM,IAAI;IAE9CC,mBAAmB,EAAIC,KAAK,IAAM;MACjCzC,6BAA6B,CAAE,KAAM,CAAC;MACtC,MAAM;QACL0C,SAAS,EAAEd,CAAC;QACZe,SAAS,EAAEb,CAAC;QACZc,KAAK;QACLC;MACD,CAAC,GAAGJ,KAAK,CAACK,WAAW;MACrB/B,mBAAmB,CAACW,OAAO,GAAGkB,KAAK,GAAGhB,CAAC;MACvCX,mBAAmB,CAACS,OAAO,GAAGmB,KAAK,GAAGf,CAAC;MACvCP,GAAG,CAACI,QAAQ,CAAE;QAAEC,CAAC;QAAEE;MAAE,CAAE,CAAC,CAAC,CAAC;MAC1BP,GAAG,CAACwB,aAAa,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;;IACD;IACAC,kBAAkB,EAAExB,qBAAQ,CAACiB,KAAK,CACjC,CAAE,IAAI,EAAE;MAAEQ,EAAE,EAAE1B,GAAG,CAACK,CAAC;MAAEsB,EAAE,EAAE3B,GAAG,CAACO;IAAE,CAAC,CAAE,EAClC;MAAEqB,eAAe,EAAE;IAAM,CAC1B,CAAC;IACDC,qBAAqB,EAAIX,KAAK,IAAM;MACnCzC,6BAA6B,CAAE,IAAK,CAAC;MACrCuB,GAAG,CAAC8B,aAAa,CAAC,CAAC,CAAC,CAAC;MACrB,MAAM;QAAET,KAAK;QAAEC;MAAM,CAAC,GAAGJ,KAAK,CAACK,WAAW;MAC1C;MACA;MACA;MACA;MACA;MACA;MACA,MAAMlB,CAAC,GAAGgB,KAAK,GAAG7B,mBAAmB,CAACW,OAAO;MAC7C,MAAMI,CAAC,GAAGe,KAAK,GAAG5B,mBAAmB,CAACS,OAAO;MAC7C3B,QAAQ,CAAE;QACT6B,CAAC,EAAE,IAAA0B,WAAK,EAAE1B,CAAC,GAAGxB,aAAa,EAAEyB,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC0B,OAAO,CAAE,CAAE,CAAC;QACvDzB,CAAC,EAAE,IAAAwB,WAAK,EAAExB,CAAC,GAAG1B,aAAa,EAAE2B,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC,CAACwB,OAAO,CAClD,CACD;MACD,CAAE,CAAC;MACH;MACA;MACA;MACA/C,YAAY,CAAIgD,SAAS,IAAMA,SAAS,GAAG,CAAE,CAAC;IAC/C;EACD,CAAE,CAAC,EACJ,CAAEpD,aAAa,EAAEmB,GAAG,EAAExB,QAAQ,EAAEC,6BAA6B,CAC9D,CAAC;EAED,MAAMyD,eAAe,GAAG,IAAAC,qCAA4B,EACnDC,cAAM,CAACF,eAAe,EACtBE,cAAM,CAACC,mBACR,CAAC;EACD,MAAMC,kBAAkB,GAAG,CAC1BpD,kBAAkB,IAAIkD,cAAM,CAACG,gBAAgB,EAC7CH,cAAM,CAACI,KAAK,CACZ;EACD,MAAMC,kBAAkB,GAAG,CAC1B;IACCC,WAAW,EACVtD,gBAAgB,IAChBA,gBAAgB,CAACkB,KAAK,GAAGlB,gBAAgB,CAACoB,MAAM;IACjD;IACAmC,OAAO,EAAEzD,kBAAkB,GAAG,CAAC,GAAG;EACnC,CAAC,EACDkD,cAAM,CAACQ,KAAK,EACZ1D,kBAAkB,IAAIkD,cAAM,CAACG,gBAAgB,CAC7C;EACD,MAAMM,qBAAqB,GAAG,CAC7BT,cAAM,CAACU,eAAe,EACtB;IACCC,SAAS,EAAE,CACV;MACCC,UAAU,EAAEhD,GAAG,CAACK,CAAC,CAAC4C,WAAW,CAAE;QAC9BC,UAAU,EAAE,CAAE,CAAC,EAAErE,aAAa,EAAEyB,KAAK,IAAI,CAAC,CAAE;QAC5C6C,WAAW,EAAE,CAAE,CAAC,EAAEtE,aAAa,EAAEyB,KAAK,IAAI,CAAC,CAAE;QAC7C8C,WAAW,EAAE;MACd,CAAE;IACH,CAAC,EACD;MACCC,UAAU,EAAErD,GAAG,CAACO,CAAC,CAAC0C,WAAW,CAAE;QAC9BC,UAAU,EAAE,CAAE,CAAC,EAAErE,aAAa,EAAE2B,MAAM,IAAI,CAAC,CAAE;QAC7C2C,WAAW,EAAE,CAAE,CAAC,EAAEtE,aAAa,EAAE2B,MAAM,IAAI,CAAC,CAAE;QAC9C4C,WAAW,EAAE;MACd,CAAE;IACH,CAAC;EAEH,CAAC,CACD;EACD,MAAME,gBAAgB,GAAG,EAAE;EAC3B,MAAMC,gBAAgB,GAAGC,uBAAU,CAACC,OAAO,CAAE,CAC5CrB,cAAM,CAAC7D,UAAU,EACjB;IACCiC,MAAM,EAAE8C,gBAAgB;IACxBI,UAAU,EAAE,EAAGJ,gBAAgB,GAAG,CAAC,CAAE;IACrCK,SAAS,EAAE,EAAGL,gBAAgB,GAAG,CAAC,CAAE;IACpChD,KAAK,EAAEgD;EACR,CAAC,CACA,CAAC;EAEH,MAAMM,cAAc,GAAGA,CAAA,KAAMrE,iBAAiB,CAAE,KAAM,CAAC;EACvD,MAAMsE,aAAa,GAAK3C,KAAK,IAAM;IAClC,MAAM;MAAEV,MAAM;MAAEF;IAAM,CAAC,GAAGY,KAAK,CAACK,WAAW,CAACuC,MAAM;IAElD,IACCxD,KAAK,KAAK,CAAC,IACXE,MAAM,KAAK,CAAC,KACV3B,aAAa,EAAEyB,KAAK,KAAKA,KAAK,IAC/BzB,aAAa,EAAE2B,MAAM,KAAKA,MAAM,CAAE,EAClC;MACD1B,gBAAgB,CAAE;QAAEwB,KAAK;QAAEE;MAAO,CAAE,CAAC;IACtC;EACD,CAAC;EACD,MAAMuD,eAAe,GAAGA,CAAA,KAAM5E,qBAAqB,CAAE,KAAM,CAAC;EAC5D,MAAM6E,WAAW,GAAK9C,KAAK,IAAM;IAChC,MAAM;MAAEV,MAAM;MAAEF;IAAM,CAAC,GAAGY,KAAK,CAAC+C,WAAW;IAC3C5E,mBAAmB,CAAE;MAAEmB,MAAM;MAAEF;IAAM,CAAE,CAAC;IACxCnB,qBAAqB,CAAE,KAAM,CAAC;IAC9B;IACA;IACAQ,QAAQ,EAAEQ,OAAO,CAAC+D,IAAI,CAAE,CAAE,CAAC;EAC5B,CAAC;EACD,MAAMC,mBAAmB,GAAK9D,CAAC,IAC9B7B,QAAQ,CAAE;IAAE6B,CAAC,EAAE,CAAEA,CAAC,GAAG,GAAG,EAAG2B,OAAO,CAAE,CAAE;EAAE,CAAE,CAAC;EAC5C,MAAMoC,mBAAmB,GAAK7D,CAAC,IAC9B/B,QAAQ,CAAE;IAAE+B,CAAC,EAAE,CAAEA,CAAC,GAAG,GAAG,EAAGyB,OAAO,CAAE,CAAE;EAAE,CAAE,CAAC;EAE5C,OACC,IAAAqC,MAAA,CAAAC,aAAA,EAACrH,YAAA,CAAAsH,IAAI;IAACC,KAAK,EAAGpC,cAAM,CAACqC;EAAW,GAC/B,IAAAJ,MAAA,CAAAC,aAAA,EAAC3G,QAAA,CAAAO,OAAO;IAACwG,OAAO,EAAGd,cAAgB;IAACe,OAAO,EAAGrF;EAAgB,GAC7D,IAAA+E,MAAA,CAAAC,aAAA,EAACrH,YAAA,CAAAsH,IAAI;IAACC,KAAK,EAAG,CAAEpC,cAAM,CAACwC,KAAK,EAAE1C,eAAe;EAAI,GAChD,IAAAmC,MAAA,CAAAC,aAAA,EAACrH,YAAA,CAAAsH,IAAI;IAAA,GACC9D,YAAY,CAACoE,WAAW;IAC7BC,QAAQ,EAAGjB,aAAe;IAC1BW,KAAK,EAAGpC,cAAM,CAAC2C;EAAgB,GAE7B,CAAEpG,OAAO,IACV,IAAA0F,MAAA,CAAAC,aAAA,EAAC/G,WAAA,CAAAyH,KAAK;IACLC,UAAU,EAAG,KAAO;IACpBC,iBAAiB,EAAG,KAAO;IAC3BC,UAAU,EAAG,CAAEjG,kBAAoB;IACnCsB,MAAM,EAAC,MAAM;IACb9B,GAAG,EAAGA,GAAK;IACX8F,KAAK,EAAGlC,kBAAoB;IAC5ByB,eAAe,EAAGA;EAAiB,CACnC,CACD,EACCpF,OAAO,IACR,IAAA0F,MAAA,CAAAC,aAAA,EAACnH,iBAAA,CAAAe,OAAK;IACLkH,KAAK;IACLC,MAAM;IACNC,YAAY;IACZC,MAAM,EAAGvB,WAAa;IACtBwB,GAAG,EAAG7F,QAAU;IAChB8F,UAAU,EAAC,SAAS;IACpBC,MAAM,EAAG;MAAEC,GAAG,EAAEjH;IAAI,CAAG;IACvB8F,KAAK,EAAG/B;EAAoB,CAC5B,CACD,EACC,CAAEvD,kBAAkB,IACrB,IAAAmF,MAAA,CAAAC,aAAA,EAACrH,YAAA,CAAAgD,QAAQ,CAACsE,IAAI;IACbqB,aAAa,EAAC,MAAM;IACpBpB,KAAK,EAAG3B;EAAuB,GAE/B,IAAAwB,MAAA,CAAAC,aAAA,EAAC3G,QAAA,CAAAO,OAAO,CAAC2H,KAAK;IACbC,IAAI,EAAG,IAAAC,QAAE,EAAE,4BAA6B,CAAG;IAC3CC,OAAO,EAAG,EAAG1C,gBAAgB,GAAG,CAAC;EAAI,CACrC,CAAC,EACF,IAAAe,MAAA,CAAAC,aAAA,EAAC5G,WAAA,CAAAQ,OAAU;IACVsC,MAAM,EAAG+C,gBAAgB,CAAC/C,MAAQ;IAClCgE,KAAK,EAAGjB,gBAAkB;IAC1B0C,MAAM,EAAC,2BAA2B;IAClC3F,KAAK,EAAGiD,gBAAgB,CAACjD;EAAO,CAChC,CACa,CAEX,CACD,CAAC,EACP,IAAA+D,MAAA,CAAAC,aAAA,EAAC/G,WAAA,CAAA2I,WAAW;IACXC,GAAG,EAAI,SAASnH,SAAW,EAAG;IAC9Bb,KAAK,EAAG,IAAA4H,QAAE,EAAE,iBAAkB,CAAG;IACjCK,GAAG,EAAGpI,kBAAoB;IAC1BqI,GAAG,EAAGtI,kBAAoB;IAC1BS,QAAQ,EAAG2F,mBAAqB;IAChCmC,IAAI,EAAC,GAAG;IACRC,KAAK,EAAGtI,iBAAmB;IAC3BG,KAAK,EAAGoI,IAAI,CAACC,KAAK,CAAElI,UAAU,CAAC8B,CAAC,GAAG,GAAI;EAAG,CAC1C,CAAC,EACF,IAAAgE,MAAA,CAAAC,aAAA,EAAC/G,WAAA,CAAA2I,WAAW;IACXC,GAAG,EAAI,SAASnH,SAAW,EAAG;IAC9Bb,KAAK,EAAG,IAAA4H,QAAE,EAAE,iBAAkB,CAAG;IACjCK,GAAG,EAAGpI,kBAAoB;IAC1BqI,GAAG,EAAGtI,kBAAoB;IAC1BS,QAAQ,EAAG4F,mBAAqB;IAChCkC,IAAI,EAAC,GAAG;IACRC,KAAK,EAAGtI,iBAAmB;IAC3BG,KAAK,EAAGoI,IAAI,CAACC,KAAK,CAAElI,UAAU,CAACgC,CAAC,GAAG,GAAI;EAAG,CAC1C,CACO,CACJ,CAAC;AAET;AAAC,IAAAmG,QAAA,GAEcrI,gBAAgB;AAAAsI,OAAA,CAAAzI,OAAA,GAAAwI,QAAA"}