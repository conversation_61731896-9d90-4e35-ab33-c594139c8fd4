{"version": 3, "names": ["_visually<PERSON><PERSON>den", "require", "_inputControlStyles", "Label", "children", "hideLabelFromVision", "htmlFor", "props", "_react", "createElement", "VisuallyHidden", "as", "LabelWrapper"], "sources": ["@wordpress/components/src/input-control/label.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport { VisuallyHidden } from '../visually-hidden';\nimport {\n\tLabel as BaseLabel,\n\tLabelWrapper,\n} from './styles/input-control-styles';\nimport type { WordPressComponentProps } from '../context';\nimport type { InputControlLabelProps } from './types';\n\nexport default function Label( {\n\tchildren,\n\thideLabelFromVision,\n\thtmlFor,\n\t...props\n}: WordPressComponentProps< InputControlLabelProps, 'label', false > ) {\n\tif ( ! children ) return null;\n\n\tif ( hideLabelFromVision ) {\n\t\treturn (\n\t\t\t<VisuallyHidden as=\"label\" htmlFor={ htmlFor }>\n\t\t\t\t{ children }\n\t\t\t</VisuallyHidden>\n\t\t);\n\t}\n\n\treturn (\n\t\t<LabelWrapper>\n\t\t\t<BaseLabel htmlFor={ htmlFor } { ...props }>\n\t\t\t\t{ children }\n\t\t\t</BaseLabel>\n\t\t</LabelWrapper>\n\t);\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AAJA;AACA;AACA;;AASe,SAASE,KAAKA,CAAE;EAC9BC,QAAQ;EACRC,mBAAmB;EACnBC,OAAO;EACP,GAAGC;AAC+D,CAAC,EAAG;EACtE,IAAK,CAAEH,QAAQ,EAAG,OAAO,IAAI;EAE7B,IAAKC,mBAAmB,EAAG;IAC1B,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACT,eAAA,CAAAU,cAAc;MAACC,EAAE,EAAC,OAAO;MAACL,OAAO,EAAGA;IAAS,GAC3CF,QACa,CAAC;EAEnB;EAEA,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACP,mBAAA,CAAAU,YAAY,QACZ,IAAAJ,MAAA,CAAAC,aAAA,EAACP,mBAAA,CAAAC,KAAS;IAACG,OAAO,EAAGA,OAAS;IAAA,GAAMC;EAAK,GACtCH,QACQ,CACE,CAAC;AAEjB"}