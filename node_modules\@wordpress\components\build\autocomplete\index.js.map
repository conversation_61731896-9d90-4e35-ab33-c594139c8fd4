{"version": 3, "names": ["_removeAccents", "_interopRequireDefault", "require", "_element", "_compose", "_richText", "_a11y", "_keycodes", "_autocompleterUi", "_strings", "getNodeText", "node", "toString", "Array", "map", "join", "props", "children", "EMPTY_FILTERED_OPTIONS", "useAutocomplete", "record", "onChange", "onReplace", "completers", "contentRef", "instanceId", "useInstanceId", "selectedIndex", "setSelectedIndex", "useState", "filteredOptions", "setFilteredOptions", "filterValue", "setFilterValue", "autocompleter", "setAutocompleter", "AutocompleterUI", "setAutocompleterUI", "backspacing", "useRef", "insertCompletion", "replacement", "end", "start", "triggerPrefix", "length", "toInsert", "create", "html", "renderToString", "insert", "select", "option", "getOptionCompletion", "isDisabled", "completion", "value", "isCompletionObject", "obj", "action", "undefined", "completionObject", "reset", "onChangeOptions", "options", "handleKeyDown", "event", "current", "key", "defaultPrevented", "isComposing", "keyCode", "newIndex", "isAppleOS", "speak", "label", "preventDefault", "textContent", "useMemo", "isCollapsed", "getTextContent", "slice", "useEffect", "completer", "reduce", "lastTrigger", "currentCompleter", "triggerIndex", "lastIndexOf", "lastTriggerIndex", "allowContext", "textWithoutTrigger", "tooDistantFromTrigger", "mismatch", "wordsFromTrigger", "split", "hasOneTriggerWord", "matchingWhileBackspacing", "textAfterSelection", "test", "safeTrigger", "escapeRegExp", "text", "removeAccents", "match", "RegExp", "query", "getAutoCompleterUI", "<PERSON><PERSON><PERSON>", "className", "isExpanded", "listBoxId", "activeId", "hasSelection", "onKeyDown", "popover", "_react", "createElement", "onSelect", "useLastDifferentValue", "history", "Set", "add", "size", "delete", "from", "useAutocompleteProps", "ref", "onKeyDownRef", "previousRecord", "mergedRefs", "useMergeRefs", "useRefEffect", "element", "_onKeyDown", "addEventListener", "removeEventListener", "didUserInput", "Autocomplete", "isSelected", "Fragment"], "sources": ["@wordpress/components/src/autocomplete/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport removeAccents from 'remove-accents';\n\n/**\n * WordPress dependencies\n */\nimport {\n\trenderToString,\n\tuseEffect,\n\tuseState,\n\tuseRef,\n\tuseMemo,\n} from '@wordpress/element';\nimport { __, _n } from '@wordpress/i18n';\nimport { useInstanceId, useMergeRefs, useRefEffect } from '@wordpress/compose';\nimport {\n\tcreate,\n\tslice,\n\tinsert,\n\tisCollapsed,\n\tgetTextContent,\n} from '@wordpress/rich-text';\nimport { speak } from '@wordpress/a11y';\nimport { isAppleOS } from '@wordpress/keycodes';\n\n/**\n * Internal dependencies\n */\nimport { getAutoCompleterUI } from './autocompleter-ui';\nimport { escapeRegExp } from '../utils/strings';\nimport type {\n\tAutocompleteProps,\n\tAutocompleterUIProps,\n\tInsertOption,\n\tKeyedOption,\n\tOptionCompletion,\n\tReplaceOption,\n\tUseAutocompleteProps,\n\tWPCompleter,\n} from './types';\n\nconst getNodeText = ( node: React.ReactNode ): string => {\n\tif ( node === null ) {\n\t\treturn '';\n\t}\n\n\tswitch ( typeof node ) {\n\t\tcase 'string':\n\t\tcase 'number':\n\t\t\treturn node.toString();\n\t\t\tbreak;\n\t\tcase 'boolean':\n\t\t\treturn '';\n\t\t\tbreak;\n\t\tcase 'object': {\n\t\t\tif ( node instanceof Array ) {\n\t\t\t\treturn node.map( getNodeText ).join( '' );\n\t\t\t}\n\t\t\tif ( 'props' in node ) {\n\t\t\t\treturn getNodeText( node.props.children );\n\t\t\t}\n\t\t\tbreak;\n\t\t}\n\t\tdefault:\n\t\t\treturn '';\n\t}\n\n\treturn '';\n};\n\nconst EMPTY_FILTERED_OPTIONS: KeyedOption[] = [];\n\nexport function useAutocomplete( {\n\trecord,\n\tonChange,\n\tonReplace,\n\tcompleters,\n\tcontentRef,\n}: UseAutocompleteProps ) {\n\tconst instanceId = useInstanceId( useAutocomplete );\n\tconst [ selectedIndex, setSelectedIndex ] = useState( 0 );\n\n\tconst [ filteredOptions, setFilteredOptions ] = useState<\n\t\tArray< KeyedOption >\n\t>( EMPTY_FILTERED_OPTIONS );\n\tconst [ filterValue, setFilterValue ] =\n\t\tuseState< AutocompleterUIProps[ 'filterValue' ] >( '' );\n\tconst [ autocompleter, setAutocompleter ] = useState< WPCompleter | null >(\n\t\tnull\n\t);\n\tconst [ AutocompleterUI, setAutocompleterUI ] = useState<\n\t\t( ( props: AutocompleterUIProps ) => JSX.Element | null ) | null\n\t>( null );\n\n\tconst backspacing = useRef( false );\n\n\tfunction insertCompletion( replacement: React.ReactNode ) {\n\t\tif ( autocompleter === null ) {\n\t\t\treturn;\n\t\t}\n\t\tconst end = record.start;\n\t\tconst start =\n\t\t\tend - autocompleter.triggerPrefix.length - filterValue.length;\n\t\tconst toInsert = create( { html: renderToString( replacement ) } );\n\n\t\tonChange( insert( record, toInsert, start, end ) );\n\t}\n\n\tfunction select( option: KeyedOption ) {\n\t\tconst { getOptionCompletion } = autocompleter || {};\n\n\t\tif ( option.isDisabled ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( getOptionCompletion ) {\n\t\t\tconst completion = getOptionCompletion( option.value, filterValue );\n\n\t\t\tconst isCompletionObject = (\n\t\t\t\tobj: OptionCompletion\n\t\t\t): obj is InsertOption | ReplaceOption => {\n\t\t\t\treturn (\n\t\t\t\t\tobj !== null &&\n\t\t\t\t\ttypeof obj === 'object' &&\n\t\t\t\t\t'action' in obj &&\n\t\t\t\t\tobj.action !== undefined &&\n\t\t\t\t\t'value' in obj &&\n\t\t\t\t\tobj.value !== undefined\n\t\t\t\t);\n\t\t\t};\n\n\t\t\tconst completionObject = isCompletionObject( completion )\n\t\t\t\t? completion\n\t\t\t\t: ( {\n\t\t\t\t\t\taction: 'insert-at-caret',\n\t\t\t\t\t\tvalue: completion,\n\t\t\t\t  } as InsertOption );\n\n\t\t\tif ( 'replace' === completionObject.action ) {\n\t\t\t\tonReplace( [ completionObject.value ] );\n\t\t\t\t// When replacing, the component will unmount, so don't reset\n\t\t\t\t// state (below) on an unmounted component.\n\t\t\t\treturn;\n\t\t\t} else if ( 'insert-at-caret' === completionObject.action ) {\n\t\t\t\tinsertCompletion( completionObject.value );\n\t\t\t}\n\t\t}\n\n\t\t// Reset autocomplete state after insertion rather than before\n\t\t// so insertion events don't cause the completion menu to redisplay.\n\t\treset();\n\t}\n\n\tfunction reset() {\n\t\tsetSelectedIndex( 0 );\n\t\tsetFilteredOptions( EMPTY_FILTERED_OPTIONS );\n\t\tsetFilterValue( '' );\n\t\tsetAutocompleter( null );\n\t\tsetAutocompleterUI( null );\n\t}\n\n\t/**\n\t * Load options for an autocompleter.\n\t *\n\t * @param {Array} options\n\t */\n\tfunction onChangeOptions( options: Array< KeyedOption > ) {\n\t\tsetSelectedIndex(\n\t\t\toptions.length === filteredOptions.length ? selectedIndex : 0\n\t\t);\n\t\tsetFilteredOptions( options );\n\t}\n\n\tfunction handleKeyDown( event: KeyboardEvent ) {\n\t\tbackspacing.current = event.key === 'Backspace';\n\n\t\tif ( ! autocompleter ) {\n\t\t\treturn;\n\t\t}\n\t\tif ( filteredOptions.length === 0 ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (\n\t\t\tevent.defaultPrevented ||\n\t\t\t// Ignore keydowns from IMEs\n\t\t\tevent.isComposing ||\n\t\t\t// Workaround for Mac Safari where the final Enter/Backspace of an IME composition\n\t\t\t// is `isComposing=false`, even though it's technically still part of the composition.\n\t\t\t// These can only be detected by keyCode.\n\t\t\tevent.keyCode === 229\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tswitch ( event.key ) {\n\t\t\tcase 'ArrowUp': {\n\t\t\t\tconst newIndex =\n\t\t\t\t\t( selectedIndex === 0\n\t\t\t\t\t\t? filteredOptions.length\n\t\t\t\t\t\t: selectedIndex ) - 1;\n\t\t\t\tsetSelectedIndex( newIndex );\n\t\t\t\t// See the related PR as to why this is necessary: https://github.com/WordPress/gutenberg/pull/54902.\n\t\t\t\tif ( isAppleOS() ) {\n\t\t\t\t\tspeak(\n\t\t\t\t\t\tgetNodeText( filteredOptions[ newIndex ].label ),\n\t\t\t\t\t\t'assertive'\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tcase 'ArrowDown': {\n\t\t\t\tconst newIndex = ( selectedIndex + 1 ) % filteredOptions.length;\n\t\t\t\tsetSelectedIndex( newIndex );\n\t\t\t\tif ( isAppleOS() ) {\n\t\t\t\t\tspeak(\n\t\t\t\t\t\tgetNodeText( filteredOptions[ newIndex ].label ),\n\t\t\t\t\t\t'assertive'\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tcase 'Escape':\n\t\t\t\tsetAutocompleter( null );\n\t\t\t\tsetAutocompleterUI( null );\n\t\t\t\tevent.preventDefault();\n\t\t\t\tbreak;\n\n\t\t\tcase 'Enter':\n\t\t\t\tselect( filteredOptions[ selectedIndex ] );\n\t\t\t\tbreak;\n\n\t\t\tcase 'ArrowLeft':\n\t\t\tcase 'ArrowRight':\n\t\t\t\treset();\n\t\t\t\treturn;\n\n\t\t\tdefault:\n\t\t\t\treturn;\n\t\t}\n\n\t\t// Any handled key should prevent original behavior. This relies on\n\t\t// the early return in the default case.\n\t\tevent.preventDefault();\n\t}\n\n\t// textContent is a primitive (string), memoizing is not strictly necessary\n\t// but this is a preemptive performance improvement, since the autocompleter\n\t// is a potential bottleneck for the editor type metric.\n\tconst textContent = useMemo( () => {\n\t\tif ( isCollapsed( record ) ) {\n\t\t\treturn getTextContent( slice( record, 0 ) );\n\t\t}\n\t\treturn '';\n\t}, [ record ] );\n\n\tuseEffect( () => {\n\t\tif ( ! textContent ) {\n\t\t\tif ( autocompleter ) reset();\n\t\t\treturn;\n\t\t}\n\n\t\t// Find the completer with the highest triggerPrefix index in the\n\t\t// textContent.\n\t\tconst completer = completers.reduce< WPCompleter | null >(\n\t\t\t( lastTrigger, currentCompleter ) => {\n\t\t\t\tconst triggerIndex = textContent.lastIndexOf(\n\t\t\t\t\tcurrentCompleter.triggerPrefix\n\t\t\t\t);\n\t\t\t\tconst lastTriggerIndex =\n\t\t\t\t\tlastTrigger !== null\n\t\t\t\t\t\t? textContent.lastIndexOf( lastTrigger.triggerPrefix )\n\t\t\t\t\t\t: -1;\n\n\t\t\t\treturn triggerIndex > lastTriggerIndex\n\t\t\t\t\t? currentCompleter\n\t\t\t\t\t: lastTrigger;\n\t\t\t},\n\t\t\tnull\n\t\t);\n\n\t\tif ( ! completer ) {\n\t\t\tif ( autocompleter ) reset();\n\t\t\treturn;\n\t\t}\n\n\t\tconst { allowContext, triggerPrefix } = completer;\n\t\tconst triggerIndex = textContent.lastIndexOf( triggerPrefix );\n\t\tconst textWithoutTrigger = textContent.slice(\n\t\t\ttriggerIndex + triggerPrefix.length\n\t\t);\n\n\t\tconst tooDistantFromTrigger = textWithoutTrigger.length > 50; // 50 chars seems to be a good limit.\n\t\t// This is a final barrier to prevent the effect from completing with\n\t\t// an extremely long string, which causes the editor to slow-down\n\t\t// significantly. This could happen, for example, if `matchingWhileBackspacing`\n\t\t// is true and one of the \"words\" end up being too long. If that's the case,\n\t\t// it will be caught by this guard.\n\t\tif ( tooDistantFromTrigger ) return;\n\n\t\tconst mismatch = filteredOptions.length === 0;\n\t\tconst wordsFromTrigger = textWithoutTrigger.split( /\\s/ );\n\t\t// We need to allow the effect to run when not backspacing and if there\n\t\t// was a mismatch. i.e when typing a trigger + the match string or when\n\t\t// clicking in an existing trigger word on the page. We do that if we\n\t\t// detect that we have one word from trigger in the current textual context.\n\t\t//\n\t\t// Ex.: \"Some text @a\" <-- \"@a\" will be detected as the trigger word and\n\t\t// allow the effect to run. It will run until there's a mismatch.\n\t\tconst hasOneTriggerWord = wordsFromTrigger.length === 1;\n\t\t// This is used to allow the effect to run when backspacing and if\n\t\t// \"touching\" a word that \"belongs\" to a trigger. We consider a \"trigger\n\t\t// word\" any word up to the limit of 3 from the trigger character.\n\t\t// Anything beyond that is ignored if there's a mismatch. This allows\n\t\t// us to \"escape\" a mismatch when backspacing, but still imposing some\n\t\t// sane limits.\n\t\t//\n\t\t// Ex: \"Some text @marcelo sekkkk\" <--- \"kkkk\" caused a mismatch, but\n\t\t// if the user presses backspace here, it will show the completion popup again.\n\t\tconst matchingWhileBackspacing =\n\t\t\tbackspacing.current && wordsFromTrigger.length <= 3;\n\n\t\tif ( mismatch && ! ( matchingWhileBackspacing || hasOneTriggerWord ) ) {\n\t\t\tif ( autocompleter ) reset();\n\t\t\treturn;\n\t\t}\n\n\t\tconst textAfterSelection = getTextContent(\n\t\t\tslice( record, undefined, getTextContent( record ).length )\n\t\t);\n\n\t\tif (\n\t\t\tallowContext &&\n\t\t\t! allowContext(\n\t\t\t\ttextContent.slice( 0, triggerIndex ),\n\t\t\t\ttextAfterSelection\n\t\t\t)\n\t\t) {\n\t\t\tif ( autocompleter ) reset();\n\t\t\treturn;\n\t\t}\n\n\t\tif (\n\t\t\t/^\\s/.test( textWithoutTrigger ) ||\n\t\t\t/\\s\\s+$/.test( textWithoutTrigger )\n\t\t) {\n\t\t\tif ( autocompleter ) reset();\n\t\t\treturn;\n\t\t}\n\n\t\tif ( ! /[\\u0000-\\uFFFF]*$/.test( textWithoutTrigger ) ) {\n\t\t\tif ( autocompleter ) reset();\n\t\t\treturn;\n\t\t}\n\n\t\tconst safeTrigger = escapeRegExp( completer.triggerPrefix );\n\t\tconst text = removeAccents( textContent );\n\t\tconst match = text\n\t\t\t.slice( text.lastIndexOf( completer.triggerPrefix ) )\n\t\t\t.match( new RegExp( `${ safeTrigger }([\\u0000-\\uFFFF]*)$` ) );\n\t\tconst query = match && match[ 1 ];\n\n\t\tsetAutocompleter( completer );\n\t\tsetAutocompleterUI( () =>\n\t\t\tcompleter !== autocompleter\n\t\t\t\t? getAutoCompleterUI( completer )\n\t\t\t\t: AutocompleterUI\n\t\t);\n\t\tsetFilterValue( query === null ? '' : query );\n\t\t// Temporarily disabling exhaustive-deps to avoid introducing unexpected side effecst.\n\t\t// See https://github.com/WordPress/gutenberg/pull/41820\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [ textContent ] );\n\n\tconst { key: selectedKey = '' } = filteredOptions[ selectedIndex ] || {};\n\tconst { className } = autocompleter || {};\n\tconst isExpanded = !! autocompleter && filteredOptions.length > 0;\n\tconst listBoxId = isExpanded\n\t\t? `components-autocomplete-listbox-${ instanceId }`\n\t\t: undefined;\n\tconst activeId = isExpanded\n\t\t? `components-autocomplete-item-${ instanceId }-${ selectedKey }`\n\t\t: null;\n\tconst hasSelection = record.start !== undefined;\n\n\treturn {\n\t\tlistBoxId,\n\t\tactiveId,\n\t\tonKeyDown: handleKeyDown,\n\t\tpopover: hasSelection && AutocompleterUI && (\n\t\t\t<AutocompleterUI\n\t\t\t\tclassName={ className }\n\t\t\t\tfilterValue={ filterValue }\n\t\t\t\tinstanceId={ instanceId }\n\t\t\t\tlistBoxId={ listBoxId }\n\t\t\t\tselectedIndex={ selectedIndex }\n\t\t\t\tonChangeOptions={ onChangeOptions }\n\t\t\t\tonSelect={ select }\n\t\t\t\tvalue={ record }\n\t\t\t\tcontentRef={ contentRef }\n\t\t\t\treset={ reset }\n\t\t\t/>\n\t\t),\n\t};\n}\n\nfunction useLastDifferentValue( value: UseAutocompleteProps[ 'record' ] ) {\n\tconst history = useRef< Set< typeof value > >( new Set() );\n\n\thistory.current.add( value );\n\n\t// Keep the history size to 2.\n\tif ( history.current.size > 2 ) {\n\t\thistory.current.delete( Array.from( history.current )[ 0 ] );\n\t}\n\n\treturn Array.from( history.current )[ 0 ];\n}\n\nexport function useAutocompleteProps( options: UseAutocompleteProps ) {\n\tconst ref = useRef< HTMLElement >( null );\n\tconst onKeyDownRef = useRef< ( event: KeyboardEvent ) => void >();\n\tconst { record } = options;\n\tconst previousRecord = useLastDifferentValue( record );\n\tconst { popover, listBoxId, activeId, onKeyDown } = useAutocomplete( {\n\t\t...options,\n\t\tcontentRef: ref,\n\t} );\n\tonKeyDownRef.current = onKeyDown;\n\n\tconst mergedRefs = useMergeRefs( [\n\t\tref,\n\t\tuseRefEffect( ( element: HTMLElement ) => {\n\t\t\tfunction _onKeyDown( event: KeyboardEvent ) {\n\t\t\t\tonKeyDownRef.current?.( event );\n\t\t\t}\n\t\t\telement.addEventListener( 'keydown', _onKeyDown );\n\t\t\treturn () => {\n\t\t\t\telement.removeEventListener( 'keydown', _onKeyDown );\n\t\t\t};\n\t\t}, [] ),\n\t] );\n\n\t// We only want to show the popover if the user has typed something.\n\tconst didUserInput = record.text !== previousRecord?.text;\n\n\tif ( ! didUserInput ) {\n\t\treturn { ref: mergedRefs };\n\t}\n\n\treturn {\n\t\tref: mergedRefs,\n\t\tchildren: popover,\n\t\t'aria-autocomplete': listBoxId ? 'list' : undefined,\n\t\t'aria-owns': listBoxId,\n\t\t'aria-activedescendant': activeId,\n\t};\n}\n\nexport default function Autocomplete( {\n\tchildren,\n\tisSelected,\n\t...options\n}: AutocompleteProps ) {\n\tconst { popover, ...props } = useAutocomplete( options );\n\treturn (\n\t\t<>\n\t\t\t{ children( props ) }\n\t\t\t{ isSelected && popover }\n\t\t</>\n\t);\n}\n"], "mappings": ";;;;;;;;;;AAGA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAQA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AAOA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AAKA,IAAAM,gBAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AA/BA;AACA;AACA;;AAGA;AACA;AACA;;AAoBA;AACA;AACA;;AAcA,MAAMQ,WAAW,GAAKC,IAAqB,IAAc;EACxD,IAAKA,IAAI,KAAK,IAAI,EAAG;IACpB,OAAO,EAAE;EACV;EAEA,QAAS,OAAOA,IAAI;IACnB,KAAK,QAAQ;IACb,KAAK,QAAQ;MACZ,OAAOA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACtB;IACD,KAAK,SAAS;MACb,OAAO,EAAE;MACT;IACD,KAAK,QAAQ;MAAE;QACd,IAAKD,IAAI,YAAYE,KAAK,EAAG;UAC5B,OAAOF,IAAI,CAACG,GAAG,CAAEJ,WAAY,CAAC,CAACK,IAAI,CAAE,EAAG,CAAC;QAC1C;QACA,IAAK,OAAO,IAAIJ,IAAI,EAAG;UACtB,OAAOD,WAAW,CAAEC,IAAI,CAACK,KAAK,CAACC,QAAS,CAAC;QAC1C;QACA;MACD;IACA;MACC,OAAO,EAAE;EACX;EAEA,OAAO,EAAE;AACV,CAAC;AAED,MAAMC,sBAAqC,GAAG,EAAE;AAEzC,SAASC,eAAeA,CAAE;EAChCC,MAAM;EACNC,QAAQ;EACRC,SAAS;EACTC,UAAU;EACVC;AACqB,CAAC,EAAG;EACzB,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEP,eAAgB,CAAC;EACnD,MAAM,CAAEQ,aAAa,EAAEC,gBAAgB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,CAAE,CAAC;EAEzD,MAAM,CAAEC,eAAe,EAAEC,kBAAkB,CAAE,GAAG,IAAAF,iBAAQ,EAErDX,sBAAuB,CAAC;EAC3B,MAAM,CAAEc,WAAW,EAAEC,cAAc,CAAE,GACpC,IAAAJ,iBAAQ,EAA2C,EAAG,CAAC;EACxD,MAAM,CAAEK,aAAa,EAAEC,gBAAgB,CAAE,GAAG,IAAAN,iBAAQ,EACnD,IACD,CAAC;EACD,MAAM,CAAEO,eAAe,EAAEC,kBAAkB,CAAE,GAAG,IAAAR,iBAAQ,EAErD,IAAK,CAAC;EAET,MAAMS,WAAW,GAAG,IAAAC,eAAM,EAAE,KAAM,CAAC;EAEnC,SAASC,gBAAgBA,CAAEC,WAA4B,EAAG;IACzD,IAAKP,aAAa,KAAK,IAAI,EAAG;MAC7B;IACD;IACA,MAAMQ,GAAG,GAAGtB,MAAM,CAACuB,KAAK;IACxB,MAAMA,KAAK,GACVD,GAAG,GAAGR,aAAa,CAACU,aAAa,CAACC,MAAM,GAAGb,WAAW,CAACa,MAAM;IAC9D,MAAMC,QAAQ,GAAG,IAAAC,gBAAM,EAAE;MAAEC,IAAI,EAAE,IAAAC,uBAAc,EAAER,WAAY;IAAE,CAAE,CAAC;IAElEpB,QAAQ,CAAE,IAAA6B,gBAAM,EAAE9B,MAAM,EAAE0B,QAAQ,EAAEH,KAAK,EAAED,GAAI,CAAE,CAAC;EACnD;EAEA,SAASS,MAAMA,CAAEC,MAAmB,EAAG;IACtC,MAAM;MAAEC;IAAoB,CAAC,GAAGnB,aAAa,IAAI,CAAC,CAAC;IAEnD,IAAKkB,MAAM,CAACE,UAAU,EAAG;MACxB;IACD;IAEA,IAAKD,mBAAmB,EAAG;MAC1B,MAAME,UAAU,GAAGF,mBAAmB,CAAED,MAAM,CAACI,KAAK,EAAExB,WAAY,CAAC;MAEnE,MAAMyB,kBAAkB,GACvBC,GAAqB,IACoB;QACzC,OACCA,GAAG,KAAK,IAAI,IACZ,OAAOA,GAAG,KAAK,QAAQ,IACvB,QAAQ,IAAIA,GAAG,IACfA,GAAG,CAACC,MAAM,KAAKC,SAAS,IACxB,OAAO,IAAIF,GAAG,IACdA,GAAG,CAACF,KAAK,KAAKI,SAAS;MAEzB,CAAC;MAED,MAAMC,gBAAgB,GAAGJ,kBAAkB,CAAEF,UAAW,CAAC,GACtDA,UAAU,GACR;QACFI,MAAM,EAAE,iBAAiB;QACzBH,KAAK,EAAED;MACP,CAAmB;MAEtB,IAAK,SAAS,KAAKM,gBAAgB,CAACF,MAAM,EAAG;QAC5CrC,SAAS,CAAE,CAAEuC,gBAAgB,CAACL,KAAK,CAAG,CAAC;QACvC;QACA;QACA;MACD,CAAC,MAAM,IAAK,iBAAiB,KAAKK,gBAAgB,CAACF,MAAM,EAAG;QAC3DnB,gBAAgB,CAAEqB,gBAAgB,CAACL,KAAM,CAAC;MAC3C;IACD;;IAEA;IACA;IACAM,KAAK,CAAC,CAAC;EACR;EAEA,SAASA,KAAKA,CAAA,EAAG;IAChBlC,gBAAgB,CAAE,CAAE,CAAC;IACrBG,kBAAkB,CAAEb,sBAAuB,CAAC;IAC5Ce,cAAc,CAAE,EAAG,CAAC;IACpBE,gBAAgB,CAAE,IAAK,CAAC;IACxBE,kBAAkB,CAAE,IAAK,CAAC;EAC3B;;EAEA;AACD;AACA;AACA;AACA;EACC,SAAS0B,eAAeA,CAAEC,OAA6B,EAAG;IACzDpC,gBAAgB,CACfoC,OAAO,CAACnB,MAAM,KAAKf,eAAe,CAACe,MAAM,GAAGlB,aAAa,GAAG,CAC7D,CAAC;IACDI,kBAAkB,CAAEiC,OAAQ,CAAC;EAC9B;EAEA,SAASC,aAAaA,CAAEC,KAAoB,EAAG;IAC9C5B,WAAW,CAAC6B,OAAO,GAAGD,KAAK,CAACE,GAAG,KAAK,WAAW;IAE/C,IAAK,CAAElC,aAAa,EAAG;MACtB;IACD;IACA,IAAKJ,eAAe,CAACe,MAAM,KAAK,CAAC,EAAG;MACnC;IACD;IAEA,IACCqB,KAAK,CAACG,gBAAgB;IACtB;IACAH,KAAK,CAACI,WAAW;IACjB;IACA;IACA;IACAJ,KAAK,CAACK,OAAO,KAAK,GAAG,EACpB;MACD;IACD;IAEA,QAASL,KAAK,CAACE,GAAG;MACjB,KAAK,SAAS;QAAE;UACf,MAAMI,QAAQ,GACb,CAAE7C,aAAa,KAAK,CAAC,GAClBG,eAAe,CAACe,MAAM,GACtBlB,aAAa,IAAK,CAAC;UACvBC,gBAAgB,CAAE4C,QAAS,CAAC;UAC5B;UACA,IAAK,IAAAC,mBAAS,EAAC,CAAC,EAAG;YAClB,IAAAC,WAAK,EACJhE,WAAW,CAAEoB,eAAe,CAAE0C,QAAQ,CAAE,CAACG,KAAM,CAAC,EAChD,WACD,CAAC;UACF;UACA;QACD;MAEA,KAAK,WAAW;QAAE;UACjB,MAAMH,QAAQ,GAAG,CAAE7C,aAAa,GAAG,CAAC,IAAKG,eAAe,CAACe,MAAM;UAC/DjB,gBAAgB,CAAE4C,QAAS,CAAC;UAC5B,IAAK,IAAAC,mBAAS,EAAC,CAAC,EAAG;YAClB,IAAAC,WAAK,EACJhE,WAAW,CAAEoB,eAAe,CAAE0C,QAAQ,CAAE,CAACG,KAAM,CAAC,EAChD,WACD,CAAC;UACF;UACA;QACD;MAEA,KAAK,QAAQ;QACZxC,gBAAgB,CAAE,IAAK,CAAC;QACxBE,kBAAkB,CAAE,IAAK,CAAC;QAC1B6B,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MAED,KAAK,OAAO;QACXzB,MAAM,CAAErB,eAAe,CAAEH,aAAa,CAAG,CAAC;QAC1C;MAED,KAAK,WAAW;MAChB,KAAK,YAAY;QAChBmC,KAAK,CAAC,CAAC;QACP;MAED;QACC;IACF;;IAEA;IACA;IACAI,KAAK,CAACU,cAAc,CAAC,CAAC;EACvB;;EAEA;EACA;EACA;EACA,MAAMC,WAAW,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAClC,IAAK,IAAAC,qBAAW,EAAE3D,MAAO,CAAC,EAAG;MAC5B,OAAO,IAAA4D,wBAAc,EAAE,IAAAC,eAAK,EAAE7D,MAAM,EAAE,CAAE,CAAE,CAAC;IAC5C;IACA,OAAO,EAAE;EACV,CAAC,EAAE,CAAEA,MAAM,CAAG,CAAC;EAEf,IAAA8D,kBAAS,EAAE,MAAM;IAChB,IAAK,CAAEL,WAAW,EAAG;MACpB,IAAK3C,aAAa,EAAG4B,KAAK,CAAC,CAAC;MAC5B;IACD;;IAEA;IACA;IACA,MAAMqB,SAAS,GAAG5D,UAAU,CAAC6D,MAAM,CAClC,CAAEC,WAAW,EAAEC,gBAAgB,KAAM;MACpC,MAAMC,YAAY,GAAGV,WAAW,CAACW,WAAW,CAC3CF,gBAAgB,CAAC1C,aAClB,CAAC;MACD,MAAM6C,gBAAgB,GACrBJ,WAAW,KAAK,IAAI,GACjBR,WAAW,CAACW,WAAW,CAAEH,WAAW,CAACzC,aAAc,CAAC,GACpD,CAAC,CAAC;MAEN,OAAO2C,YAAY,GAAGE,gBAAgB,GACnCH,gBAAgB,GAChBD,WAAW;IACf,CAAC,EACD,IACD,CAAC;IAED,IAAK,CAAEF,SAAS,EAAG;MAClB,IAAKjD,aAAa,EAAG4B,KAAK,CAAC,CAAC;MAC5B;IACD;IAEA,MAAM;MAAE4B,YAAY;MAAE9C;IAAc,CAAC,GAAGuC,SAAS;IACjD,MAAMI,YAAY,GAAGV,WAAW,CAACW,WAAW,CAAE5C,aAAc,CAAC;IAC7D,MAAM+C,kBAAkB,GAAGd,WAAW,CAACI,KAAK,CAC3CM,YAAY,GAAG3C,aAAa,CAACC,MAC9B,CAAC;IAED,MAAM+C,qBAAqB,GAAGD,kBAAkB,CAAC9C,MAAM,GAAG,EAAE,CAAC,CAAC;IAC9D;IACA;IACA;IACA;IACA;IACA,IAAK+C,qBAAqB,EAAG;IAE7B,MAAMC,QAAQ,GAAG/D,eAAe,CAACe,MAAM,KAAK,CAAC;IAC7C,MAAMiD,gBAAgB,GAAGH,kBAAkB,CAACI,KAAK,CAAE,IAAK,CAAC;IACzD;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,iBAAiB,GAAGF,gBAAgB,CAACjD,MAAM,KAAK,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMoD,wBAAwB,GAC7B3D,WAAW,CAAC6B,OAAO,IAAI2B,gBAAgB,CAACjD,MAAM,IAAI,CAAC;IAEpD,IAAKgD,QAAQ,IAAI,EAAII,wBAAwB,IAAID,iBAAiB,CAAE,EAAG;MACtE,IAAK9D,aAAa,EAAG4B,KAAK,CAAC,CAAC;MAC5B;IACD;IAEA,MAAMoC,kBAAkB,GAAG,IAAAlB,wBAAc,EACxC,IAAAC,eAAK,EAAE7D,MAAM,EAAEwC,SAAS,EAAE,IAAAoB,wBAAc,EAAE5D,MAAO,CAAC,CAACyB,MAAO,CAC3D,CAAC;IAED,IACC6C,YAAY,IACZ,CAAEA,YAAY,CACbb,WAAW,CAACI,KAAK,CAAE,CAAC,EAAEM,YAAa,CAAC,EACpCW,kBACD,CAAC,EACA;MACD,IAAKhE,aAAa,EAAG4B,KAAK,CAAC,CAAC;MAC5B;IACD;IAEA,IACC,KAAK,CAACqC,IAAI,CAAER,kBAAmB,CAAC,IAChC,QAAQ,CAACQ,IAAI,CAAER,kBAAmB,CAAC,EAClC;MACD,IAAKzD,aAAa,EAAG4B,KAAK,CAAC,CAAC;MAC5B;IACD;IAEA,IAAK,CAAE,mBAAmB,CAACqC,IAAI,CAAER,kBAAmB,CAAC,EAAG;MACvD,IAAKzD,aAAa,EAAG4B,KAAK,CAAC,CAAC;MAC5B;IACD;IAEA,MAAMsC,WAAW,GAAG,IAAAC,qBAAY,EAAElB,SAAS,CAACvC,aAAc,CAAC;IAC3D,MAAM0D,IAAI,GAAG,IAAAC,sBAAa,EAAE1B,WAAY,CAAC;IACzC,MAAM2B,KAAK,GAAGF,IAAI,CAChBrB,KAAK,CAAEqB,IAAI,CAACd,WAAW,CAAEL,SAAS,CAACvC,aAAc,CAAE,CAAC,CACpD4D,KAAK,CAAE,IAAIC,MAAM,CAAG,GAAGL,WAAa,qBAAqB,CAAE,CAAC;IAC9D,MAAMM,KAAK,GAAGF,KAAK,IAAIA,KAAK,CAAE,CAAC,CAAE;IAEjCrE,gBAAgB,CAAEgD,SAAU,CAAC;IAC7B9C,kBAAkB,CAAE,MACnB8C,SAAS,KAAKjD,aAAa,GACxB,IAAAyE,mCAAkB,EAAExB,SAAU,CAAC,GAC/B/C,eACJ,CAAC;IACDH,cAAc,CAAEyE,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAM,CAAC;IAC7C;IACA;IACA;EACD,CAAC,EAAE,CAAE7B,WAAW,CAAG,CAAC;EAEpB,MAAM;IAAET,GAAG,EAAEwC,WAAW,GAAG;EAAG,CAAC,GAAG9E,eAAe,CAAEH,aAAa,CAAE,IAAI,CAAC,CAAC;EACxE,MAAM;IAAEkF;EAAU,CAAC,GAAG3E,aAAa,IAAI,CAAC,CAAC;EACzC,MAAM4E,UAAU,GAAG,CAAC,CAAE5E,aAAa,IAAIJ,eAAe,CAACe,MAAM,GAAG,CAAC;EACjE,MAAMkE,SAAS,GAAGD,UAAU,GACxB,mCAAmCrF,UAAY,EAAC,GACjDmC,SAAS;EACZ,MAAMoD,QAAQ,GAAGF,UAAU,GACvB,gCAAgCrF,UAAY,IAAImF,WAAa,EAAC,GAC/D,IAAI;EACP,MAAMK,YAAY,GAAG7F,MAAM,CAACuB,KAAK,KAAKiB,SAAS;EAE/C,OAAO;IACNmD,SAAS;IACTC,QAAQ;IACRE,SAAS,EAAEjD,aAAa;IACxBkD,OAAO,EAAEF,YAAY,IAAI7E,eAAe,IACvC,IAAAgF,MAAA,CAAAC,aAAA,EAACjF,eAAe;MACfyE,SAAS,EAAGA,SAAW;MACvB7E,WAAW,EAAGA,WAAa;MAC3BP,UAAU,EAAGA,UAAY;MACzBsF,SAAS,EAAGA,SAAW;MACvBpF,aAAa,EAAGA,aAAe;MAC/BoC,eAAe,EAAGA,eAAiB;MACnCuD,QAAQ,EAAGnE,MAAQ;MACnBK,KAAK,EAAGpC,MAAQ;MAChBI,UAAU,EAAGA,UAAY;MACzBsC,KAAK,EAAGA;IAAO,CACf;EAEH,CAAC;AACF;AAEA,SAASyD,qBAAqBA,CAAE/D,KAAuC,EAAG;EACzE,MAAMgE,OAAO,GAAG,IAAAjF,eAAM,EAAyB,IAAIkF,GAAG,CAAC,CAAE,CAAC;EAE1DD,OAAO,CAACrD,OAAO,CAACuD,GAAG,CAAElE,KAAM,CAAC;;EAE5B;EACA,IAAKgE,OAAO,CAACrD,OAAO,CAACwD,IAAI,GAAG,CAAC,EAAG;IAC/BH,OAAO,CAACrD,OAAO,CAACyD,MAAM,CAAE/G,KAAK,CAACgH,IAAI,CAAEL,OAAO,CAACrD,OAAQ,CAAC,CAAE,CAAC,CAAG,CAAC;EAC7D;EAEA,OAAOtD,KAAK,CAACgH,IAAI,CAAEL,OAAO,CAACrD,OAAQ,CAAC,CAAE,CAAC,CAAE;AAC1C;AAEO,SAAS2D,oBAAoBA,CAAE9D,OAA6B,EAAG;EACrE,MAAM+D,GAAG,GAAG,IAAAxF,eAAM,EAAiB,IAAK,CAAC;EACzC,MAAMyF,YAAY,GAAG,IAAAzF,eAAM,EAAqC,CAAC;EACjE,MAAM;IAAEnB;EAAO,CAAC,GAAG4C,OAAO;EAC1B,MAAMiE,cAAc,GAAGV,qBAAqB,CAAEnG,MAAO,CAAC;EACtD,MAAM;IAAE+F,OAAO;IAAEJ,SAAS;IAAEC,QAAQ;IAAEE;EAAU,CAAC,GAAG/F,eAAe,CAAE;IACpE,GAAG6C,OAAO;IACVxC,UAAU,EAAEuG;EACb,CAAE,CAAC;EACHC,YAAY,CAAC7D,OAAO,GAAG+C,SAAS;EAEhC,MAAMgB,UAAU,GAAG,IAAAC,qBAAY,EAAE,CAChCJ,GAAG,EACH,IAAAK,qBAAY,EAAIC,OAAoB,IAAM;IACzC,SAASC,UAAUA,CAAEpE,KAAoB,EAAG;MAC3C8D,YAAY,CAAC7D,OAAO,GAAID,KAAM,CAAC;IAChC;IACAmE,OAAO,CAACE,gBAAgB,CAAE,SAAS,EAAED,UAAW,CAAC;IACjD,OAAO,MAAM;MACZD,OAAO,CAACG,mBAAmB,CAAE,SAAS,EAAEF,UAAW,CAAC;IACrD,CAAC;EACF,CAAC,EAAE,EAAG,CAAC,CACN,CAAC;;EAEH;EACA,MAAMG,YAAY,GAAGrH,MAAM,CAACkF,IAAI,KAAK2B,cAAc,EAAE3B,IAAI;EAEzD,IAAK,CAAEmC,YAAY,EAAG;IACrB,OAAO;MAAEV,GAAG,EAAEG;IAAW,CAAC;EAC3B;EAEA,OAAO;IACNH,GAAG,EAAEG,UAAU;IACfjH,QAAQ,EAAEkG,OAAO;IACjB,mBAAmB,EAAEJ,SAAS,GAAG,MAAM,GAAGnD,SAAS;IACnD,WAAW,EAAEmD,SAAS;IACtB,uBAAuB,EAAEC;EAC1B,CAAC;AACF;AAEe,SAAS0B,YAAYA,CAAE;EACrCzH,QAAQ;EACR0H,UAAU;EACV,GAAG3E;AACe,CAAC,EAAG;EACtB,MAAM;IAAEmD,OAAO;IAAE,GAAGnG;EAAM,CAAC,GAAGG,eAAe,CAAE6C,OAAQ,CAAC;EACxD,OACC,IAAAoD,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAwB,QAAA,QACG3H,QAAQ,CAAED,KAAM,CAAC,EACjB2H,UAAU,IAAIxB,OACf,CAAC;AAEL"}