{"version": 3, "names": ["initialStateReducer", "state", "exports", "initialInputControlState", "error", "initialValue", "isDirty", "isDragEnabled", "isDragging", "isPressEnterToChange", "value"], "sources": ["@wordpress/components/src/input-control/reducer/state.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { Reducer, SyntheticEvent } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { Action, InputAction } from './actions';\n\nexport interface InputState {\n\t_event?: SyntheticEvent;\n\terror: unknown;\n\tinitialValue?: string;\n\tisDirty: boolean;\n\tisDragEnabled: boolean;\n\tisDragging: boolean;\n\tisPressEnterToChange: boolean;\n\tvalue?: string;\n}\n\nexport type StateReducer< SpecializedAction = {} > = Reducer<\n\tInputState,\n\tSpecializedAction extends Action\n\t\t? InputAction | SpecializedAction\n\t\t: InputAction\n>;\n\nexport const initialStateReducer: StateReducer = ( state: InputState ) => state;\n\nexport const initialInputControlState: InputState = {\n\terror: null,\n\tinitialValue: '',\n\tisDirty: false,\n\tisDragEnabled: false,\n\tisDragging: false,\n\tisPressEnterToChange: false,\n\tvalue: '',\n};\n"], "mappings": ";;;;;;AAAA;AACA;AACA;;AAGA;AACA;AACA;;AAqBO,MAAMA,mBAAiC,GAAKC,KAAiB,IAAMA,KAAK;AAACC,OAAA,CAAAF,mBAAA,GAAAA,mBAAA;AAEzE,MAAMG,wBAAoC,GAAG;EACnDC,KAAK,EAAE,IAAI;EACXC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,KAAK;EACdC,aAAa,EAAE,KAAK;EACpBC,UAAU,EAAE,KAAK;EACjBC,oBAAoB,EAAE,KAAK;EAC3BC,KAAK,EAAE;AACR,CAAC;AAACR,OAAA,CAAAC,wBAAA,GAAAA,wBAAA"}