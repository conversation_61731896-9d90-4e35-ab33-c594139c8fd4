"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.FontSizePicker = void 0;
var _react = require("react");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _element = require("@wordpress/element");
var _button = require("../button");
var _rangeControl = _interopRequireDefault(require("../range-control"));
var _flex = require("../flex");
var _unitControl = _interopRequireWildcard(require("../unit-control"));
var _visuallyHidden = require("../visually-hidden");
var _utils = require("./utils");
var _styles = require("./styles");
var _spacer = require("../spacer");
var _fontSizePickerSelect = _interopRequireDefault(require("./font-size-picker-select"));
var _fontSizePickerToggleGroup = _interopRequireDefault(require("./font-size-picker-toggle-group"));
var _constants = require("./constants");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const UnforwardedFontSizePicker = (props, ref) => {
  const {
    /** Start opting into the new margin-free styles that will become the default in a future version. */
    __nextHasNoMarginBottom = false,
    __next40pxDefaultSize = false,
    fallbackFontSize,
    fontSizes = [],
    disableCustomFontSizes = false,
    onChange,
    size = 'default',
    units: unitsProp,
    value,
    withSlider = false,
    withReset = true
  } = props;
  if (!__nextHasNoMarginBottom) {
    (0, _deprecated.default)('Bottom margin styles for wp.components.FontSizePicker', {
      since: '6.1',
      version: '6.4',
      hint: 'Set the `__nextHasNoMarginBottom` prop to true to start opting into the new styles, which will become the default in a future version.'
    });
  }
  const units = (0, _unitControl.useCustomUnits)({
    availableUnits: unitsProp || ['px', 'em', 'rem']
  });
  const shouldUseSelectControl = fontSizes.length > 5;
  const selectedFontSize = fontSizes.find(fontSize => fontSize.size === value);
  const isCustomValue = !!value && !selectedFontSize;
  const [showCustomValueControl, setShowCustomValueControl] = (0, _element.useState)(!disableCustomFontSizes && isCustomValue);
  const headerHint = (0, _element.useMemo)(() => {
    if (showCustomValueControl) {
      return (0, _i18n.__)('Custom');
    }
    if (!shouldUseSelectControl) {
      if (selectedFontSize) {
        return selectedFontSize.name || _constants.T_SHIRT_NAMES[fontSizes.indexOf(selectedFontSize)];
      }
      return '';
    }
    const commonUnit = (0, _utils.getCommonSizeUnit)(fontSizes);
    if (commonUnit) {
      return `(${commonUnit})`;
    }
    return '';
  }, [showCustomValueControl, shouldUseSelectControl, selectedFontSize, fontSizes]);
  if (fontSizes.length === 0 && disableCustomFontSizes) {
    return null;
  }

  // If neither the value or first font size is a string, then FontSizePicker
  // operates in a legacy "unitless" mode where UnitControl can only be used
  // to select px values and onChange() is always called with number values.
  const hasUnits = typeof value === 'string' || typeof fontSizes[0]?.size === 'string';
  const [valueQuantity, valueUnit] = (0, _unitControl.parseQuantityAndUnitFromRawValue)(value, units);
  const isValueUnitRelative = !!valueUnit && ['em', 'rem'].includes(valueUnit);
  const isDisabled = value === undefined;
  return (0, _react.createElement)(_styles.Container, {
    ref: ref,
    className: "components-font-size-picker"
  }, (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    as: "legend"
  }, (0, _i18n.__)('Font size')), (0, _react.createElement)(_spacer.Spacer, null, (0, _react.createElement)(_styles.Header, {
    className: "components-font-size-picker__header"
  }, (0, _react.createElement)(_styles.HeaderLabel, {
    "aria-label": `${(0, _i18n.__)('Size')} ${headerHint || ''}`
  }, (0, _i18n.__)('Size'), headerHint && (0, _react.createElement)(_styles.HeaderHint, {
    className: "components-font-size-picker__header__hint"
  }, headerHint)), !disableCustomFontSizes && (0, _react.createElement)(_styles.HeaderToggle, {
    label: showCustomValueControl ? (0, _i18n.__)('Use size preset') : (0, _i18n.__)('Set custom size'),
    icon: _icons.settings,
    onClick: () => {
      setShowCustomValueControl(!showCustomValueControl);
    },
    isPressed: showCustomValueControl,
    size: "small"
  }))), (0, _react.createElement)(_styles.Controls, {
    className: "components-font-size-picker__controls",
    __nextHasNoMarginBottom: __nextHasNoMarginBottom
  }, !!fontSizes.length && shouldUseSelectControl && !showCustomValueControl && (0, _react.createElement)(_fontSizePickerSelect.default, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    fontSizes: fontSizes,
    value: value,
    disableCustomFontSizes: disableCustomFontSizes,
    size: size,
    onChange: newValue => {
      if (newValue === undefined) {
        onChange?.(undefined);
      } else {
        onChange?.(hasUnits ? newValue : Number(newValue), fontSizes.find(fontSize => fontSize.size === newValue));
      }
    },
    onSelectCustom: () => setShowCustomValueControl(true)
  }), !shouldUseSelectControl && !showCustomValueControl && (0, _react.createElement)(_fontSizePickerToggleGroup.default, {
    fontSizes: fontSizes,
    value: value,
    __nextHasNoMarginBottom: __nextHasNoMarginBottom,
    __next40pxDefaultSize: __next40pxDefaultSize,
    size: size,
    onChange: newValue => {
      if (newValue === undefined) {
        onChange?.(undefined);
      } else {
        onChange?.(hasUnits ? newValue : Number(newValue), fontSizes.find(fontSize => fontSize.size === newValue));
      }
    }
  }), !disableCustomFontSizes && showCustomValueControl && (0, _react.createElement)(_flex.Flex, {
    className: "components-font-size-picker__custom-size-control"
  }, (0, _react.createElement)(_flex.FlexItem, {
    isBlock: true
  }, (0, _react.createElement)(_unitControl.default, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    label: (0, _i18n.__)('Custom'),
    labelPosition: "top",
    hideLabelFromVision: true,
    value: value,
    onChange: newValue => {
      if (newValue === undefined) {
        onChange?.(undefined);
      } else {
        onChange?.(hasUnits ? newValue : parseInt(newValue, 10));
      }
    },
    size: size,
    units: hasUnits ? units : [],
    min: 0
  })), withSlider && (0, _react.createElement)(_flex.FlexItem, {
    isBlock: true
  }, (0, _react.createElement)(_spacer.Spacer, {
    marginX: 2,
    marginBottom: 0
  }, (0, _react.createElement)(_rangeControl.default, {
    __nextHasNoMarginBottom: __nextHasNoMarginBottom,
    __next40pxDefaultSize: __next40pxDefaultSize,
    className: "components-font-size-picker__custom-input",
    label: (0, _i18n.__)('Custom Size'),
    hideLabelFromVision: true,
    value: valueQuantity,
    initialPosition: fallbackFontSize,
    withInputField: false,
    onChange: newValue => {
      if (newValue === undefined) {
        onChange?.(undefined);
      } else if (hasUnits) {
        onChange?.(newValue + (valueUnit !== null && valueUnit !== void 0 ? valueUnit : 'px'));
      } else {
        onChange?.(newValue);
      }
    },
    min: 0,
    max: isValueUnitRelative ? 10 : 100,
    step: isValueUnitRelative ? 0.1 : 1
  }))), withReset && (0, _react.createElement)(_flex.FlexItem, null, (0, _react.createElement)(_button.Button, {
    disabled: isDisabled,
    __experimentalIsFocusable: true,
    onClick: () => {
      onChange?.(undefined);
    },
    variant: "secondary",
    __next40pxDefaultSize: true,
    size: size === '__unstable-large' || props.__next40pxDefaultSize ? 'default' : 'small'
  }, (0, _i18n.__)('Reset'))))));
};
const FontSizePicker = (0, _element.forwardRef)(UnforwardedFontSizePicker);
exports.FontSizePicker = FontSizePicker;
var _default = FontSizePicker;
exports.default = _default;
//# sourceMappingURL=index.js.map