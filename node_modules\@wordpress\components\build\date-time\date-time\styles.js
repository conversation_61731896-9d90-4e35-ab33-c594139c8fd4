"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Wrapper = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _vStack = require("../../v-stack");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const Wrapper = ( /*#__PURE__*/0, _base.default)(_vStack.VStack, process.env.NODE_ENV === "production" ? {
  target: "e1p5onf00"
} : {
  target: "e1p5onf00",
  label: "Wrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "1khn195",
  styles: "box-sizing:border-box"
} : {
  name: "1khn195",
  styles: "box-sizing:border-box",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZGF0ZS10aW1lL2RhdGUtdGltZS9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBVXVDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZGF0ZS10aW1lL2RhdGUtdGltZS9zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IFZTdGFjayB9IGZyb20gJy4uLy4uL3Ytc3RhY2snO1xuXG5leHBvcnQgY29uc3QgV3JhcHBlciA9IHN0eWxlZCggVlN0YWNrIClgXG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5gO1xuIl19 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.Wrapper = Wrapper;
//# sourceMappingURL=styles.js.map