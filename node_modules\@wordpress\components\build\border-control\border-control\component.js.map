{"version": 3, "names": ["_i18n", "require", "_borderControlDropdown", "_interopRequireDefault", "_unitControl", "_rangeControl", "_hStack", "_baseControlStyles", "_view", "_visually<PERSON><PERSON>den", "_context", "_hook", "BorderLabel", "props", "label", "hideLabelFromVision", "_react", "createElement", "VisuallyHidden", "as", "StyledLabel", "UnconnectedBorderControl", "forwardedRef", "__next40pxDefaultSize", "colors", "disableCustomColors", "disableUnits", "enableAlpha", "enableStyle", "innerWrapperClassName", "inputWidth", "isStyleSettable", "onBorderChange", "onSliderChange", "onWidthChange", "placeholder", "__unstablePopoverProps", "previousStyleSelection", "showDropdownHeader", "size", "sliderClassName", "value", "border", "widthUnit", "widthValue", "<PERSON><PERSON><PERSON><PERSON>", "__experimentalIsRenderedInSidebar", "otherProps", "useBorderControl", "View", "ref", "HStack", "spacing", "className", "default", "prefix", "onChange", "__", "min", "width", "__unstableInputWidth", "__nextHasNoMarginBottom", "initialPosition", "max", "step", "includes", "undefined", "withInputField", "BorderControl", "contextConnect", "exports", "_default"], "sources": ["@wordpress/components/src/border-control/border-control/component.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport BorderControlDropdown from '../border-control-dropdown';\nimport UnitControl from '../../unit-control';\nimport RangeControl from '../../range-control';\nimport { HStack } from '../../h-stack';\nimport { StyledLabel } from '../../base-control/styles/base-control-styles';\nimport { View } from '../../view';\nimport { VisuallyHidden } from '../../visually-hidden';\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useBorderControl } from './hook';\n\nimport type { BorderControlProps, LabelProps } from '../types';\n\nconst BorderLabel = ( props: LabelProps ) => {\n\tconst { label, hideLabelFromVision } = props;\n\n\tif ( ! label ) {\n\t\treturn null;\n\t}\n\n\treturn hideLabelFromVision ? (\n\t\t<VisuallyHidden as=\"legend\">{ label }</VisuallyHidden>\n\t) : (\n\t\t<StyledLabel as=\"legend\">{ label }</StyledLabel>\n\t);\n};\n\nconst UnconnectedBorderControl = (\n\tprops: WordPressComponentProps< BorderControlProps, 'div', false >,\n\tforwardedRef: React.ForwardedRef< any >\n) => {\n\tconst {\n\t\t__next40pxDefaultSize = false,\n\t\tcolors,\n\t\tdisableCustomColors,\n\t\tdisableUnits,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\thideLabelFromVision,\n\t\tinnerWrapperClassName,\n\t\tinputWidth,\n\t\tisStyleSettable,\n\t\tlabel,\n\t\tonBorderChange,\n\t\tonSliderChange,\n\t\tonWidthChange,\n\t\tplaceholder,\n\t\t__unstablePopoverProps,\n\t\tpreviousStyleSelection,\n\t\tshowDropdownHeader,\n\t\tsize,\n\t\tsliderClassName,\n\t\tvalue: border,\n\t\twidthUnit,\n\t\twidthValue,\n\t\twithSlider,\n\t\t__experimentalIsRenderedInSidebar,\n\t\t...otherProps\n\t} = useBorderControl( props );\n\n\treturn (\n\t\t<View as=\"fieldset\" { ...otherProps } ref={ forwardedRef }>\n\t\t\t<BorderLabel\n\t\t\t\tlabel={ label }\n\t\t\t\thideLabelFromVision={ hideLabelFromVision }\n\t\t\t/>\n\t\t\t<HStack spacing={ 4 } className={ innerWrapperClassName }>\n\t\t\t\t<UnitControl\n\t\t\t\t\tprefix={\n\t\t\t\t\t\t<BorderControlDropdown\n\t\t\t\t\t\t\tborder={ border }\n\t\t\t\t\t\t\tcolors={ colors }\n\t\t\t\t\t\t\t__unstablePopoverProps={ __unstablePopoverProps }\n\t\t\t\t\t\t\tdisableCustomColors={ disableCustomColors }\n\t\t\t\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\t\t\t\tenableStyle={ enableStyle }\n\t\t\t\t\t\t\tisStyleSettable={ isStyleSettable }\n\t\t\t\t\t\t\tonChange={ onBorderChange }\n\t\t\t\t\t\t\tpreviousStyleSelection={ previousStyleSelection }\n\t\t\t\t\t\t\tshowDropdownHeader={ showDropdownHeader }\n\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tsize={ size }\n\t\t\t\t\t\t/>\n\t\t\t\t\t}\n\t\t\t\t\tlabel={ __( 'Border width' ) }\n\t\t\t\t\thideLabelFromVision\n\t\t\t\t\tmin={ 0 }\n\t\t\t\t\tonChange={ onWidthChange }\n\t\t\t\t\tvalue={ border?.width || '' }\n\t\t\t\t\tplaceholder={ placeholder }\n\t\t\t\t\tdisableUnits={ disableUnits }\n\t\t\t\t\t__unstableInputWidth={ inputWidth }\n\t\t\t\t\tsize={ size }\n\t\t\t\t/>\n\t\t\t\t{ withSlider && (\n\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t\t\tlabel={ __( 'Border width' ) }\n\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t\tclassName={ sliderClassName }\n\t\t\t\t\t\tinitialPosition={ 0 }\n\t\t\t\t\t\tmax={ 100 }\n\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\tonChange={ onSliderChange }\n\t\t\t\t\t\tstep={ [ 'px', '%' ].includes( widthUnit ) ? 1 : 0.1 }\n\t\t\t\t\t\tvalue={ widthValue || undefined }\n\t\t\t\t\t\twithInputField={ false }\n\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t</HStack>\n\t\t</View>\n\t);\n};\n\n/**\n * The `BorderControl` brings together internal sub-components which allow users to\n * set the various properties of a border. The first sub-component, a\n * `BorderDropdown` contains options representing border color and style. The\n * border width is controlled via a `UnitControl` and an optional `RangeControl`.\n *\n * Border radius is not covered by this control as it may be desired separate to\n * color, style, and width. For example, the border radius may be absorbed under\n * a \"shape\" abstraction.\n *\n * ```jsx\n * import { __experimentalBorderControl as BorderControl } from '@wordpress/components';\n * import { __ } from '@wordpress/i18n';\n *\n * const colors = [\n * \t{ name: 'Blue 20', color: '#72aee6' },\n * \t// ...\n * ];\n *\n * const MyBorderControl = () => {\n * \tconst [ border, setBorder ] = useState();\n * \tconst onChange = ( newBorder ) => setBorder( newBorder );\n *\n * \treturn (\n * \t\t<BorderControl\n * \t\t\tcolors={ colors }\n * \t\t\tlabel={ __( 'Border' ) }\n * \t\t\tonChange={ onChange }\n * \t\t\tvalue={ border }\n * \t\t/>\n * \t);\n * };\n * ```\n */\nexport const BorderControl = contextConnect(\n\tUnconnectedBorderControl,\n\t'BorderControl'\n);\n\nexport default BorderControl;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,sBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,aAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,kBAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,eAAA,GAAAR,OAAA;AAEA,IAAAS,QAAA,GAAAT,OAAA;AACA,IAAAU,KAAA,GAAAV,OAAA;AAjBA;AACA;AACA;;AAGA;AACA;AACA;;AAcA,MAAMW,WAAW,GAAKC,KAAiB,IAAM;EAC5C,MAAM;IAAEC,KAAK;IAAEC;EAAoB,CAAC,GAAGF,KAAK;EAE5C,IAAK,CAAEC,KAAK,EAAG;IACd,OAAO,IAAI;EACZ;EAEA,OAAOC,mBAAmB,GACzB,IAAAC,MAAA,CAAAC,aAAA,EAACR,eAAA,CAAAS,cAAc;IAACC,EAAE,EAAC;EAAQ,GAAGL,KAAuB,CAAC,GAEtD,IAAAE,MAAA,CAAAC,aAAA,EAACV,kBAAA,CAAAa,WAAW;IAACD,EAAE,EAAC;EAAQ,GAAGL,KAAoB,CAC/C;AACF,CAAC;AAED,MAAMO,wBAAwB,GAAGA,CAChCR,KAAkE,EAClES,YAAuC,KACnC;EACJ,MAAM;IACLC,qBAAqB,GAAG,KAAK;IAC7BC,MAAM;IACNC,mBAAmB;IACnBC,YAAY;IACZC,WAAW;IACXC,WAAW;IACXb,mBAAmB;IACnBc,qBAAqB;IACrBC,UAAU;IACVC,eAAe;IACfjB,KAAK;IACLkB,cAAc;IACdC,cAAc;IACdC,aAAa;IACbC,WAAW;IACXC,sBAAsB;IACtBC,sBAAsB;IACtBC,kBAAkB;IAClBC,IAAI;IACJC,eAAe;IACfC,KAAK,EAAEC,MAAM;IACbC,SAAS;IACTC,UAAU;IACVC,UAAU;IACVC,iCAAiC;IACjC,GAAGC;EACJ,CAAC,GAAG,IAAAC,sBAAgB,EAAEnC,KAAM,CAAC;EAE7B,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACT,KAAA,CAAAyC,IAAI;IAAC9B,EAAE,EAAC,UAAU;IAAA,GAAM4B,UAAU;IAAGG,GAAG,EAAG5B;EAAc,GACzD,IAAAN,MAAA,CAAAC,aAAA,EAACL,WAAW;IACXE,KAAK,EAAGA,KAAO;IACfC,mBAAmB,EAAGA;EAAqB,CAC3C,CAAC,EACF,IAAAC,MAAA,CAAAC,aAAA,EAACX,OAAA,CAAA6C,MAAM;IAACC,OAAO,EAAG,CAAG;IAACC,SAAS,EAAGxB;EAAuB,GACxD,IAAAb,MAAA,CAAAC,aAAA,EAACb,YAAA,CAAAkD,OAAW;IACXC,MAAM,EACL,IAAAvC,MAAA,CAAAC,aAAA,EAACf,sBAAA,CAAAoD,OAAqB;MACrBZ,MAAM,EAAGA,MAAQ;MACjBlB,MAAM,EAAGA,MAAQ;MACjBY,sBAAsB,EAAGA,sBAAwB;MACjDX,mBAAmB,EAAGA,mBAAqB;MAC3CE,WAAW,EAAGA,WAAa;MAC3BC,WAAW,EAAGA,WAAa;MAC3BG,eAAe,EAAGA,eAAiB;MACnCyB,QAAQ,EAAGxB,cAAgB;MAC3BK,sBAAsB,EAAGA,sBAAwB;MACjDC,kBAAkB,EAAGA,kBAAoB;MACzCQ,iCAAiC,EAChCA,iCACA;MACDP,IAAI,EAAGA;IAAM,CACb,CACD;IACDzB,KAAK,EAAG,IAAA2C,QAAE,EAAE,cAAe,CAAG;IAC9B1C,mBAAmB;IACnB2C,GAAG,EAAG,CAAG;IACTF,QAAQ,EAAGtB,aAAe;IAC1BO,KAAK,EAAGC,MAAM,EAAEiB,KAAK,IAAI,EAAI;IAC7BxB,WAAW,EAAGA,WAAa;IAC3BT,YAAY,EAAGA,YAAc;IAC7BkC,oBAAoB,EAAG9B,UAAY;IACnCS,IAAI,EAAGA;EAAM,CACb,CAAC,EACAM,UAAU,IACX,IAAA7B,MAAA,CAAAC,aAAA,EAACZ,aAAA,CAAAiD,OAAY;IACZO,uBAAuB;IACvB/C,KAAK,EAAG,IAAA2C,QAAE,EAAE,cAAe,CAAG;IAC9B1C,mBAAmB;IACnBsC,SAAS,EAAGb,eAAiB;IAC7BsB,eAAe,EAAG,CAAG;IACrBC,GAAG,EAAG,GAAK;IACXL,GAAG,EAAG,CAAG;IACTF,QAAQ,EAAGvB,cAAgB;IAC3B+B,IAAI,EAAG,CAAE,IAAI,EAAE,GAAG,CAAE,CAACC,QAAQ,CAAEtB,SAAU,CAAC,GAAG,CAAC,GAAG,GAAK;IACtDF,KAAK,EAAGG,UAAU,IAAIsB,SAAW;IACjCC,cAAc,EAAG,KAAO;IACxB5C,qBAAqB,EAAGA;EAAuB,CAC/C,CAEK,CACH,CAAC;AAET,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM6C,aAAa,GAAG,IAAAC,uBAAc,EAC1ChD,wBAAwB,EACxB,eACD,CAAC;AAACiD,OAAA,CAAAF,aAAA,GAAAA,aAAA;AAAA,IAAAG,QAAA,GAEaH,aAAa;AAAAE,OAAA,CAAAhB,OAAA,GAAAiB,QAAA"}