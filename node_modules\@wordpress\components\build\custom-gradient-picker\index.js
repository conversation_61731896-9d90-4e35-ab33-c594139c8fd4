"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CustomGradientPicker = CustomGradientPicker;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _i18n = require("@wordpress/i18n");
var _anglePickerControl = _interopRequireDefault(require("../angle-picker-control"));
var _gradientBar = _interopRequireDefault(require("./gradient-bar"));
var _flex = require("../flex");
var _selectControl = _interopRequireDefault(require("../select-control"));
var _vStack = require("../v-stack");
var _utils = require("./utils");
var _serializer = require("./serializer");
var _constants = require("./constants");
var _customGradientPickerStyles = require("./styles/custom-gradient-picker-styles");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const GradientAnglePicker = ({
  gradientAST,
  hasGradient,
  onChange
}) => {
  var _gradientAST$orientat;
  const angle = (_gradientAST$orientat = gradientAST?.orientation?.value) !== null && _gradientAST$orientat !== void 0 ? _gradientAST$orientat : _constants.DEFAULT_LINEAR_GRADIENT_ANGLE;
  const onAngleChange = newAngle => {
    onChange((0, _serializer.serializeGradient)({
      ...gradientAST,
      orientation: {
        type: 'angular',
        value: `${newAngle}`
      }
    }));
  };
  return (0, _react.createElement)(_anglePickerControl.default, {
    __nextHasNoMarginBottom: true,
    onChange: onAngleChange,
    value: hasGradient ? angle : ''
  });
};
const GradientTypePicker = ({
  gradientAST,
  hasGradient,
  onChange
}) => {
  const {
    type
  } = gradientAST;
  const onSetLinearGradient = () => {
    onChange((0, _serializer.serializeGradient)({
      ...gradientAST,
      orientation: gradientAST.orientation ? undefined : _constants.HORIZONTAL_GRADIENT_ORIENTATION,
      type: 'linear-gradient'
    }));
  };
  const onSetRadialGradient = () => {
    const {
      orientation,
      ...restGradientAST
    } = gradientAST;
    onChange((0, _serializer.serializeGradient)({
      ...restGradientAST,
      type: 'radial-gradient'
    }));
  };
  const handleOnChange = next => {
    if (next === 'linear-gradient') {
      onSetLinearGradient();
    }
    if (next === 'radial-gradient') {
      onSetRadialGradient();
    }
  };
  return (0, _react.createElement)(_selectControl.default, {
    __nextHasNoMarginBottom: true,
    className: "components-custom-gradient-picker__type-picker",
    label: (0, _i18n.__)('Type'),
    labelPosition: "top",
    onChange: handleOnChange,
    options: _constants.GRADIENT_OPTIONS,
    size: "__unstable-large",
    value: hasGradient ? type : undefined
  });
};

/**
 * CustomGradientPicker is a React component that renders a UI for specifying
 * linear or radial gradients. Radial gradients are displayed in the picker as
 * a slice of the gradient from the center to the outside.
 *
 * ```jsx
 * import { CustomGradientPicker } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyCustomGradientPicker = () => {
 *   const [ gradient, setGradient ] = useState();
 *
 *   return (
 *     <CustomGradientPicker
 *			value={ gradient }
 *			onChange={ setGradient }
 *     />
 *   );
 * };
 * ```
 */
function CustomGradientPicker({
  /** Start opting into the new margin-free styles that will become the default in a future version. */
  __nextHasNoMargin = false,
  value,
  onChange,
  __experimentalIsRenderedInSidebar = false
}) {
  const {
    gradientAST,
    hasGradient
  } = (0, _utils.getGradientAstWithDefault)(value);

  // On radial gradients the bar should display a linear gradient.
  // On radial gradients the bar represents a slice of the gradient from the center until the outside.
  // On liner gradients the bar represents the color stops from left to right independently of the angle.
  const background = (0, _utils.getLinearGradientRepresentation)(gradientAST);

  // Control points color option may be hex from presets, custom colors will be rgb.
  // The position should always be a percentage.
  const controlPoints = gradientAST.colorStops.map(colorStop => {
    return {
      color: (0, _utils.getStopCssColor)(colorStop),
      // Although it's already been checked by `hasUnsupportedLength` in `getGradientAstWithDefault`,
      // TypeScript doesn't know that `colorStop.length` is not undefined here.
      // @ts-expect-error
      position: parseInt(colorStop.length.value)
    };
  });
  if (!__nextHasNoMargin) {
    (0, _deprecated.default)('Outer margin styles for wp.components.CustomGradientPicker', {
      since: '6.1',
      version: '6.4',
      hint: 'Set the `__nextHasNoMargin` prop to true to start opting into the new styles, which will become the default in a future version'
    });
  }
  return (0, _react.createElement)(_vStack.VStack, {
    spacing: 4,
    className: (0, _classnames.default)('components-custom-gradient-picker', {
      'is-next-has-no-margin': __nextHasNoMargin
    })
  }, (0, _react.createElement)(_gradientBar.default, {
    __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
    background: background,
    hasGradient: hasGradient,
    value: controlPoints,
    onChange: newControlPoints => {
      onChange((0, _serializer.serializeGradient)((0, _utils.getGradientAstWithControlPoints)(gradientAST, newControlPoints)));
    }
  }), (0, _react.createElement)(_flex.Flex, {
    gap: 3,
    className: "components-custom-gradient-picker__ui-line"
  }, (0, _react.createElement)(_customGradientPickerStyles.SelectWrapper, null, (0, _react.createElement)(GradientTypePicker, {
    gradientAST: gradientAST,
    hasGradient: hasGradient,
    onChange: onChange
  })), (0, _react.createElement)(_customGradientPickerStyles.AccessoryWrapper, null, gradientAST.type === 'linear-gradient' && (0, _react.createElement)(GradientAnglePicker, {
    gradientAST: gradientAST,
    hasGradient: hasGradient,
    onChange: onChange
  }))));
}
var _default = CustomGradientPicker;
exports.default = _default;
//# sourceMappingURL=index.js.map