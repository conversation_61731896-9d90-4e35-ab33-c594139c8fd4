{"version": 3, "names": ["_reactNative", "require", "_colord", "_names", "_interopRequireDefault", "_element", "_i18n", "_components", "_compose", "_icons", "_style", "_hsvColorPickerNative", "extend", "namesPlugin", "ColorPicker", "shouldEnableBottomSheetScroll", "shouldEnableBottomSheetMaxHeight", "isBottomSheetContentScrolling", "setColor", "activeColor", "isGradientColor", "onNavigationBack", "onHandleClosingBottomSheet", "onBottomSheetClosed", "onHandleHardwareButtonPress", "bottomLabelText", "isIOS", "Platform", "OS", "hitSlop", "top", "bottom", "left", "right", "h", "initH", "s", "initS", "v", "initV", "colord", "toHsv", "hue", "setHue", "useState", "sat", "setSaturation", "val", "setValue", "savedColor", "paddingLeft", "spacing", "height", "pickerHeight", "borderRadius", "styles", "picker", "pickerPointerSize", "pickerPointer", "picker<PERSON><PERSON><PERSON>", "BottomSheet", "getWidth", "applyButtonStyle", "usePreferredColorSchemeStyle", "applyButton", "applyButtonDark", "cancelButtonStyle", "cancelButton", "cancelButtonDark", "colorTextStyle", "colorText", "colorTextDark", "selectColorTextStyle", "selectColorText", "selectColorTextDark", "footerStyle", "footer", "footerDark", "combineToHex", "toHex", "currentColor", "updateColor", "saturation", "value", "undefined", "useEffect", "onButtonPress", "action", "_react", "createElement", "Fragment", "default", "huePickerHue", "onHuePickerDragMove", "onHuePickerPress", "satValPickerHue", "satValPickerSaturation", "satValPickerValue", "onSatValPickerDragMove", "onSatValPickerPress", "onSatValPickerDragStart", "onSatValPickerDragEnd", "onHuePickerDragStart", "onHuePickerDragEnd", "huePickerBarWidth", "huePickerBarHeight", "satValPickerSize", "width", "satValPickerSliderSize", "satValPickerBorderRadius", "huePickerBorderRadius", "View", "style", "TouchableWithoutFeedback", "onPress", "Text", "__", "Icon", "icon", "close", "size", "selectable", "toUpperCase", "check"], "sources": ["@wordpress/components/src/color-picker/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, Text, TouchableWithoutFeedback, Platform } from 'react-native';\nimport { colord, extend } from 'colord';\nimport namesPlugin from 'colord/plugins/names';\n/**\n * WordPress dependencies\n */\nimport { useState, useEffect } from '@wordpress/element';\nimport { __ } from '@wordpress/i18n';\nimport { BottomSheet } from '@wordpress/components';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\nimport { Icon, check, close } from '@wordpress/icons';\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\nimport HsvColorPicker from './hsv-color-picker.native.js';\n\nextend( [ namesPlugin ] );\n\nfunction ColorPicker( {\n\tshouldEnableBottomSheetScroll,\n\tshouldEnableBottomSheetMaxHeight,\n\tisBottomSheetContentScrolling,\n\tsetColor,\n\tactiveColor,\n\tisGradientColor,\n\tonNavigationBack,\n\tonHandleClosingBottomSheet,\n\tonBottomSheetClosed,\n\tonHandleHardwareButtonPress,\n\tbottomLabelText,\n} ) {\n\tconst isIOS = Platform.OS === 'ios';\n\tconst hitSlop = { top: 22, bottom: 22, left: 22, right: 22 };\n\tconst {\n\t\th: initH,\n\t\ts: initS,\n\t\tv: initV,\n\t} = ! isGradientColor && activeColor\n\t\t? colord( activeColor ).toHsv()\n\t\t: { h: 0, s: 50, v: 50 };\n\tconst [ hue, setHue ] = useState( initH );\n\tconst [ sat, setSaturation ] = useState( initS / 100 );\n\tconst [ val, setValue ] = useState( initV / 100 );\n\tconst [ savedColor ] = useState( activeColor );\n\n\tconst {\n\t\tpaddingLeft: spacing,\n\t\theight: pickerHeight,\n\t\tborderRadius,\n\t} = styles.picker;\n\tconst { height: pickerPointerSize } = styles.pickerPointer;\n\tconst pickerWidth = BottomSheet.getWidth() - 2 * spacing;\n\n\tconst applyButtonStyle = usePreferredColorSchemeStyle(\n\t\tstyles.applyButton,\n\t\tstyles.applyButtonDark\n\t);\n\tconst cancelButtonStyle = usePreferredColorSchemeStyle(\n\t\tstyles.cancelButton,\n\t\tstyles.cancelButtonDark\n\t);\n\tconst colorTextStyle = usePreferredColorSchemeStyle(\n\t\tstyles.colorText,\n\t\tstyles.colorTextDark\n\t);\n\tconst selectColorTextStyle = usePreferredColorSchemeStyle(\n\t\tstyles.selectColorText,\n\t\tstyles.selectColorTextDark\n\t);\n\tconst footerStyle = usePreferredColorSchemeStyle(\n\t\tstyles.footer,\n\t\tstyles.footerDark\n\t);\n\n\tconst combineToHex = ( h = hue, s = sat, v = val ) =>\n\t\tcolord( { h, s: s * 100, v: v * 100 } ).toHex();\n\n\tconst currentColor = combineToHex();\n\n\tconst updateColor = ( { hue: h, saturation: s, value: v } ) => {\n\t\tif ( h !== undefined ) setHue( h );\n\t\tif ( s !== undefined ) setSaturation( s );\n\t\tif ( v !== undefined ) setValue( v );\n\t\tsetColor( combineToHex( h, s, v ) );\n\t};\n\n\tuseEffect( () => {\n\t\tshouldEnableBottomSheetMaxHeight( false );\n\t\tonHandleClosingBottomSheet( () => {\n\t\t\tif ( savedColor ) {\n\t\t\t\tsetColor( savedColor );\n\t\t\t}\n\t\t\tif ( onBottomSheetClosed ) {\n\t\t\t\tonBottomSheetClosed();\n\t\t\t}\n\t\t} );\n\t\tif ( onHandleHardwareButtonPress ) {\n\t\t\tonHandleHardwareButtonPress( onButtonPress );\n\t\t}\n\t\t// TODO: Revisit this to discover if there's a good reason for omitting\n\t\t// the hook’s dependencies and running it a single time. Ideally there\n\t\t// may be a way to refactor and obviate the disabled lint rule. If not,\n\t\t// this comment should be replaced by one that explains the reasoning.\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [] );\n\n\tfunction onButtonPress( action ) {\n\t\tonNavigationBack();\n\t\tonHandleClosingBottomSheet( null );\n\t\tshouldEnableBottomSheetMaxHeight( true );\n\t\tsetColor( action === 'apply' ? currentColor : savedColor );\n\t\tif ( onBottomSheetClosed ) {\n\t\t\tonBottomSheetClosed();\n\t\t}\n\t}\n\n\treturn (\n\t\t<>\n\t\t\t<HsvColorPicker\n\t\t\t\thuePickerHue={ hue }\n\t\t\t\tcurrentColor={ currentColor }\n\t\t\t\tonHuePickerDragMove={ updateColor }\n\t\t\t\tonHuePickerPress={\n\t\t\t\t\t! isBottomSheetContentScrolling && updateColor\n\t\t\t\t}\n\t\t\t\tsatValPickerHue={ hue }\n\t\t\t\tsatValPickerSaturation={ sat }\n\t\t\t\tsatValPickerValue={ val }\n\t\t\t\tonSatValPickerDragMove={ updateColor }\n\t\t\t\tonSatValPickerPress={\n\t\t\t\t\t! isBottomSheetContentScrolling && updateColor\n\t\t\t\t}\n\t\t\t\tonSatValPickerDragStart={ () => {\n\t\t\t\t\tshouldEnableBottomSheetScroll( false );\n\t\t\t\t} }\n\t\t\t\tonSatValPickerDragEnd={ () =>\n\t\t\t\t\tshouldEnableBottomSheetScroll( true )\n\t\t\t\t}\n\t\t\t\tonHuePickerDragStart={ () =>\n\t\t\t\t\tshouldEnableBottomSheetScroll( false )\n\t\t\t\t}\n\t\t\t\tonHuePickerDragEnd={ () =>\n\t\t\t\t\tshouldEnableBottomSheetScroll( true )\n\t\t\t\t}\n\t\t\t\thuePickerBarWidth={ pickerWidth }\n\t\t\t\thuePickerBarHeight={ pickerPointerSize / 2 }\n\t\t\t\tsatValPickerSize={ {\n\t\t\t\t\twidth: pickerWidth,\n\t\t\t\t\theight: pickerHeight,\n\t\t\t\t} }\n\t\t\t\tsatValPickerSliderSize={ pickerPointerSize * 2 }\n\t\t\t\tsatValPickerBorderRadius={ borderRadius }\n\t\t\t\thuePickerBorderRadius={ borderRadius }\n\t\t\t/>\n\t\t\t<View style={ footerStyle }>\n\t\t\t\t<TouchableWithoutFeedback\n\t\t\t\t\tonPress={ () => onButtonPress( 'cancel' ) }\n\t\t\t\t\thitSlop={ hitSlop }\n\t\t\t\t>\n\t\t\t\t\t<View>\n\t\t\t\t\t\t{ isIOS ? (\n\t\t\t\t\t\t\t<Text style={ cancelButtonStyle }>\n\t\t\t\t\t\t\t\t{ __( 'Cancel' ) }\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t<Icon\n\t\t\t\t\t\t\t\ticon={ close }\n\t\t\t\t\t\t\t\tsize={ 24 }\n\t\t\t\t\t\t\t\tstyle={ cancelButtonStyle }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</View>\n\t\t\t\t</TouchableWithoutFeedback>\n\t\t\t\t{ bottomLabelText ? (\n\t\t\t\t\t<Text style={ selectColorTextStyle }>\n\t\t\t\t\t\t{ bottomLabelText }\n\t\t\t\t\t</Text>\n\t\t\t\t) : (\n\t\t\t\t\t<Text style={ colorTextStyle } selectable>\n\t\t\t\t\t\t{ currentColor.toUpperCase() }\n\t\t\t\t\t</Text>\n\t\t\t\t) }\n\t\t\t\t<TouchableWithoutFeedback\n\t\t\t\t\tonPress={ () => onButtonPress( 'apply' ) }\n\t\t\t\t\thitSlop={ hitSlop }\n\t\t\t\t>\n\t\t\t\t\t<View>\n\t\t\t\t\t\t{ isIOS ? (\n\t\t\t\t\t\t\t<Text style={ applyButtonStyle }>\n\t\t\t\t\t\t\t\t{ __( 'Apply' ) }\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t<Icon\n\t\t\t\t\t\t\t\ticon={ check }\n\t\t\t\t\t\t\t\tsize={ 24 }\n\t\t\t\t\t\t\t\tstyle={ applyButtonStyle }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</View>\n\t\t\t\t</TouchableWithoutFeedback>\n\t\t\t</View>\n\t\t</>\n\t);\n}\n\nexport { ColorPicker };\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAIA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,WAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AACA,IAAAQ,MAAA,GAAAR,OAAA;AAIA,IAAAS,MAAA,GAAAN,sBAAA,CAAAH,OAAA;AACA,IAAAU,qBAAA,GAAAP,sBAAA,CAAAH,OAAA;AAlBA;AACA;AACA;;AAIA;AACA;AACA;;AAMA;AACA;AACA;;AAIA,IAAAW,cAAM,EAAE,CAAEC,cAAW,CAAG,CAAC;AAEzB,SAASC,WAAWA,CAAE;EACrBC,6BAA6B;EAC7BC,gCAAgC;EAChCC,6BAA6B;EAC7BC,QAAQ;EACRC,WAAW;EACXC,eAAe;EACfC,gBAAgB;EAChBC,0BAA0B;EAC1BC,mBAAmB;EACnBC,2BAA2B;EAC3BC;AACD,CAAC,EAAG;EACH,MAAMC,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;EACnC,MAAMC,OAAO,GAAG;IAAEC,GAAG,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAG,CAAC;EAC5D,MAAM;IACLC,CAAC,EAAEC,KAAK;IACRC,CAAC,EAAEC,KAAK;IACRC,CAAC,EAAEC;EACJ,CAAC,GAAG,CAAEnB,eAAe,IAAID,WAAW,GACjC,IAAAqB,cAAM,EAAErB,WAAY,CAAC,CAACsB,KAAK,CAAC,CAAC,GAC7B;IAAEP,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAE,EAAE;IAAEE,CAAC,EAAE;EAAG,CAAC;EACzB,MAAM,CAAEI,GAAG,EAAEC,MAAM,CAAE,GAAG,IAAAC,iBAAQ,EAAET,KAAM,CAAC;EACzC,MAAM,CAAEU,GAAG,EAAEC,aAAa,CAAE,GAAG,IAAAF,iBAAQ,EAAEP,KAAK,GAAG,GAAI,CAAC;EACtD,MAAM,CAAEU,GAAG,EAAEC,QAAQ,CAAE,GAAG,IAAAJ,iBAAQ,EAAEL,KAAK,GAAG,GAAI,CAAC;EACjD,MAAM,CAAEU,UAAU,CAAE,GAAG,IAAAL,iBAAQ,EAAEzB,WAAY,CAAC;EAE9C,MAAM;IACL+B,WAAW,EAAEC,OAAO;IACpBC,MAAM,EAAEC,YAAY;IACpBC;EACD,CAAC,GAAGC,cAAM,CAACC,MAAM;EACjB,MAAM;IAAEJ,MAAM,EAAEK;EAAkB,CAAC,GAAGF,cAAM,CAACG,aAAa;EAC1D,MAAMC,WAAW,GAAGC,uBAAW,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAGV,OAAO;EAExD,MAAMW,gBAAgB,GAAG,IAAAC,qCAA4B,EACpDR,cAAM,CAACS,WAAW,EAClBT,cAAM,CAACU,eACR,CAAC;EACD,MAAMC,iBAAiB,GAAG,IAAAH,qCAA4B,EACrDR,cAAM,CAACY,YAAY,EACnBZ,cAAM,CAACa,gBACR,CAAC;EACD,MAAMC,cAAc,GAAG,IAAAN,qCAA4B,EAClDR,cAAM,CAACe,SAAS,EAChBf,cAAM,CAACgB,aACR,CAAC;EACD,MAAMC,oBAAoB,GAAG,IAAAT,qCAA4B,EACxDR,cAAM,CAACkB,eAAe,EACtBlB,cAAM,CAACmB,mBACR,CAAC;EACD,MAAMC,WAAW,GAAG,IAAAZ,qCAA4B,EAC/CR,cAAM,CAACqB,MAAM,EACbrB,cAAM,CAACsB,UACR,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAE5C,CAAC,GAAGQ,GAAG,EAAEN,CAAC,GAAGS,GAAG,EAAEP,CAAC,GAAGS,GAAG,KAC/C,IAAAP,cAAM,EAAE;IAAEN,CAAC;IAAEE,CAAC,EAAEA,CAAC,GAAG,GAAG;IAAEE,CAAC,EAAEA,CAAC,GAAG;EAAI,CAAE,CAAC,CAACyC,KAAK,CAAC,CAAC;EAEhD,MAAMC,YAAY,GAAGF,YAAY,CAAC,CAAC;EAEnC,MAAMG,WAAW,GAAGA,CAAE;IAAEvC,GAAG,EAAER,CAAC;IAAEgD,UAAU,EAAE9C,CAAC;IAAE+C,KAAK,EAAE7C;EAAE,CAAC,KAAM;IAC9D,IAAKJ,CAAC,KAAKkD,SAAS,EAAGzC,MAAM,CAAET,CAAE,CAAC;IAClC,IAAKE,CAAC,KAAKgD,SAAS,EAAGtC,aAAa,CAAEV,CAAE,CAAC;IACzC,IAAKE,CAAC,KAAK8C,SAAS,EAAGpC,QAAQ,CAAEV,CAAE,CAAC;IACpCpB,QAAQ,CAAE4D,YAAY,CAAE5C,CAAC,EAAEE,CAAC,EAAEE,CAAE,CAAE,CAAC;EACpC,CAAC;EAED,IAAA+C,kBAAS,EAAE,MAAM;IAChBrE,gCAAgC,CAAE,KAAM,CAAC;IACzCM,0BAA0B,CAAE,MAAM;MACjC,IAAK2B,UAAU,EAAG;QACjB/B,QAAQ,CAAE+B,UAAW,CAAC;MACvB;MACA,IAAK1B,mBAAmB,EAAG;QAC1BA,mBAAmB,CAAC,CAAC;MACtB;IACD,CAAE,CAAC;IACH,IAAKC,2BAA2B,EAAG;MAClCA,2BAA2B,CAAE8D,aAAc,CAAC;IAC7C;IACA;IACA;IACA;IACA;IACA;EACD,CAAC,EAAE,EAAG,CAAC;EAEP,SAASA,aAAaA,CAAEC,MAAM,EAAG;IAChClE,gBAAgB,CAAC,CAAC;IAClBC,0BAA0B,CAAE,IAAK,CAAC;IAClCN,gCAAgC,CAAE,IAAK,CAAC;IACxCE,QAAQ,CAAEqE,MAAM,KAAK,OAAO,GAAGP,YAAY,GAAG/B,UAAW,CAAC;IAC1D,IAAK1B,mBAAmB,EAAG;MAC1BA,mBAAmB,CAAC,CAAC;IACtB;EACD;EAEA,OACC,IAAAiE,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAAC9E,qBAAA,CAAAgF,OAAc;IACdC,YAAY,EAAGlD,GAAK;IACpBsC,YAAY,EAAGA,YAAc;IAC7Ba,mBAAmB,EAAGZ,WAAa;IACnCa,gBAAgB,EACf,CAAE7E,6BAA6B,IAAIgE,WACnC;IACDc,eAAe,EAAGrD,GAAK;IACvBsD,sBAAsB,EAAGnD,GAAK;IAC9BoD,iBAAiB,EAAGlD,GAAK;IACzBmD,sBAAsB,EAAGjB,WAAa;IACtCkB,mBAAmB,EAClB,CAAElF,6BAA6B,IAAIgE,WACnC;IACDmB,uBAAuB,EAAGA,CAAA,KAAM;MAC/BrF,6BAA6B,CAAE,KAAM,CAAC;IACvC,CAAG;IACHsF,qBAAqB,EAAGA,CAAA,KACvBtF,6BAA6B,CAAE,IAAK,CACpC;IACDuF,oBAAoB,EAAGA,CAAA,KACtBvF,6BAA6B,CAAE,KAAM,CACrC;IACDwF,kBAAkB,EAAGA,CAAA,KACpBxF,6BAA6B,CAAE,IAAK,CACpC;IACDyF,iBAAiB,EAAG7C,WAAa;IACjC8C,kBAAkB,EAAGhD,iBAAiB,GAAG,CAAG;IAC5CiD,gBAAgB,EAAG;MAClBC,KAAK,EAAEhD,WAAW;MAClBP,MAAM,EAAEC;IACT,CAAG;IACHuD,sBAAsB,EAAGnD,iBAAiB,GAAG,CAAG;IAChDoD,wBAAwB,EAAGvD,YAAc;IACzCwD,qBAAqB,EAAGxD;EAAc,CACtC,CAAC,EACF,IAAAkC,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAA+G,IAAI;IAACC,KAAK,EAAGrC;EAAa,GAC1B,IAAAa,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAAiH,wBAAwB;IACxBC,OAAO,EAAGA,CAAA,KAAM5B,aAAa,CAAE,QAAS,CAAG;IAC3CzD,OAAO,EAAGA;EAAS,GAEnB,IAAA2D,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAA+G,IAAI,QACFrF,KAAK,GACN,IAAA8D,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAAmH,IAAI;IAACH,KAAK,EAAG9C;EAAmB,GAC9B,IAAAkD,QAAE,EAAE,QAAS,CACV,CAAC,GAEP,IAAA5B,MAAA,CAAAC,aAAA,EAAChF,MAAA,CAAA4G,IAAI;IACJC,IAAI,EAAGC,YAAO;IACdC,IAAI,EAAG,EAAI;IACXR,KAAK,EAAG9C;EAAmB,CAC3B,CAEG,CACmB,CAAC,EACzBzC,eAAe,GAChB,IAAA+D,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAAmH,IAAI;IAACH,KAAK,EAAGxC;EAAsB,GACjC/C,eACG,CAAC,GAEP,IAAA+D,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAAmH,IAAI;IAACH,KAAK,EAAG3C,cAAgB;IAACoD,UAAU;EAAA,GACtCzC,YAAY,CAAC0C,WAAW,CAAC,CACtB,CACN,EACD,IAAAlC,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAAiH,wBAAwB;IACxBC,OAAO,EAAGA,CAAA,KAAM5B,aAAa,CAAE,OAAQ,CAAG;IAC1CzD,OAAO,EAAGA;EAAS,GAEnB,IAAA2D,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAA+G,IAAI,QACFrF,KAAK,GACN,IAAA8D,MAAA,CAAAC,aAAA,EAACzF,YAAA,CAAAmH,IAAI;IAACH,KAAK,EAAGlD;EAAkB,GAC7B,IAAAsD,QAAE,EAAE,OAAQ,CACT,CAAC,GAEP,IAAA5B,MAAA,CAAAC,aAAA,EAAChF,MAAA,CAAA4G,IAAI;IACJC,IAAI,EAAGK,YAAO;IACdH,IAAI,EAAG,EAAI;IACXR,KAAK,EAAGlD;EAAkB,CAC1B,CAEG,CACmB,CACrB,CACL,CAAC;AAEL"}