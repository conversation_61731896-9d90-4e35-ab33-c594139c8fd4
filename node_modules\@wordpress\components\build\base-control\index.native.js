"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BaseControl;
var _react = require("react");
var _reactNative = require("react-native");
/**
 * External dependencies
 */

function BaseControl({
  label,
  help,
  children
}) {
  return (0, _react.createElement)(_reactNative.View, {
    accessible: true,
    accessibilityLabel: label
  }, label && (0, _react.createElement)(_reactNative.Text, null, label), children, help && (0, _react.createElement)(_reactNative.Text, null, help));
}
//# sourceMappingURL=index.native.js.map