{"version": 3, "names": ["PointerCircle", "_base", "default", "process", "env", "NODE_ENV", "target", "label", "isDragging", "exports"], "sources": ["@wordpress/components/src/focal-point-picker/styles/focal-point-style.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n\nexport const PointerCircle = styled.div`\n\tbackground-color: transparent;\n\tcursor: grab;\n\theight: 48px;\n\tmargin: -24px 0 0 -24px;\n\tposition: absolute;\n\tuser-select: none;\n\twidth: 48px;\n\twill-change: transform;\n\tz-index: 10000;\n\tbackground: rgba( 255, 255, 255, 0.6 );\n\tborder-radius: 50%;\n\tbackdrop-filter: blur( 4px );\n\tbox-shadow: rgb( 0 0 0 / 20% ) 0px 0px 10px;\n\n\t${ ( { isDragging }: { isDragging: boolean } ) =>\n\t\tisDragging && 'cursor: grabbing;' }\n`;\n"], "mappings": ";;;;;;;;AAAA;AACA;AACA;;AAGO,MAAMA,aAAa,OAAAC,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,oSAetB,CAAE;EAAEC;AAAoC,CAAC,KAC3CA,UAAU,IAAI,mBAAmB,SAAAL,OAAA,CAAAC,GAAA,CAAAC,QAAA,gpCAClC;AAACI,OAAA,CAAAT,aAAA,GAAAA,aAAA"}