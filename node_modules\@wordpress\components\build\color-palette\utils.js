"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.normalizeColorValue = exports.isMultiplePaletteObject = exports.isMultiplePaletteArray = exports.extractColorNameFromCurrentValue = void 0;
var _colord = require("colord");
var _names = _interopRequireDefault(require("colord/plugins/names"));
var _a11y = _interopRequireDefault(require("colord/plugins/a11y"));
var _i18n = require("@wordpress/i18n");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

(0, _colord.extend)([_names.default, _a11y.default]);
const extractColorNameFromCurrentValue = (currentValue, colors = [], showMultiplePalettes = false) => {
  if (!currentValue) {
    return '';
  }
  const currentValueIsCssVariable = /^var\(/.test(currentValue);
  const normalizedCurrentValue = currentValueIsCssVariable ? currentValue : (0, _colord.colord)(currentValue).toHex();

  // Normalize format of `colors` to simplify the following loop

  const colorPalettes = showMultiplePalettes ? colors : [{
    colors: colors
  }];
  for (const {
    colors: paletteColors
  } of colorPalettes) {
    for (const {
      name: colorName,
      color: colorValue
    } of paletteColors) {
      const normalizedColorValue = currentValueIsCssVariable ? colorValue : (0, _colord.colord)(colorValue).toHex();
      if (normalizedCurrentValue === normalizedColorValue) {
        return colorName;
      }
    }
  }

  // translators: shown when the user has picked a custom color (i.e not in the palette of colors).
  return (0, _i18n.__)('Custom');
};

// The PaletteObject type has a `colors` property (an array of ColorObject),
// while the ColorObject type has a `color` property (the CSS color value).
exports.extractColorNameFromCurrentValue = extractColorNameFromCurrentValue;
const isMultiplePaletteObject = obj => Array.isArray(obj.colors) && !('color' in obj);
exports.isMultiplePaletteObject = isMultiplePaletteObject;
const isMultiplePaletteArray = arr => {
  return arr.length > 0 && arr.every(colorObj => isMultiplePaletteObject(colorObj));
};

/**
 * Transform a CSS variable used as background color into the color value itself.
 *
 * @param value   The color value that may be a CSS variable.
 * @param element The element for which to get the computed style.
 * @return The background color value computed from a element.
 */
exports.isMultiplePaletteArray = isMultiplePaletteArray;
const normalizeColorValue = (value, element) => {
  const currentValueIsCssVariable = /^var\(/.test(value !== null && value !== void 0 ? value : '');
  if (!currentValueIsCssVariable || element === null) {
    return value;
  }
  const {
    ownerDocument
  } = element;
  const {
    defaultView
  } = ownerDocument;
  const computedBackgroundColor = defaultView?.getComputedStyle(element).backgroundColor;
  return computedBackgroundColor ? (0, _colord.colord)(computedBackgroundColor).toHex() : value;
};
exports.normalizeColorValue = normalizeColorValue;
//# sourceMappingURL=utils.js.map