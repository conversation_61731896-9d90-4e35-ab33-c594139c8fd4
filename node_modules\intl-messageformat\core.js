"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var __extends=function(){var n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};return function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}}(),Compiler=function(){function t(t,e,r){this.locales=[],this.formats={number:{},date:{},time:{}},this.pluralNumberFormat=null,this.currentPlural=null,this.pluralStack=[],this.locales=t,this.formats=e,this.formatters=r}return t.prototype.compile=function(t){return this.pluralStack=[],this.currentPlural=null,this.pluralNumberFormat=null,this.compileMessage(t)},t.prototype.compileMessage=function(t){var e=this;if(!t||"messageFormatPattern"!==t.type)throw new Error('Message AST is not of type: "messageFormatPattern"');var r=t.elements,n=r.filter(function(t){return"messageTextElement"===t.type||"argumentElement"===t.type}).map(function(t){return"messageTextElement"===t.type?e.compileMessageText(t):e.compileArgument(t)});if(n.length!==r.length)throw new Error("Message element does not have a valid type");return n},t.prototype.compileMessageText=function(t){return this.currentPlural&&/(^|[^\\])#/g.test(t.value)?(this.pluralNumberFormat||(this.pluralNumberFormat=new Intl.NumberFormat(this.locales)),new PluralOffsetString(this.currentPlural.id,this.currentPlural.format.offset,this.pluralNumberFormat,t.value)):t.value.replace(/\\#/g,"#")},t.prototype.compileArgument=function(t){var e=t.format,r=t.id,n=this.formatters;if(!e)return new StringFormat(r);var o=this.formats,a=this.locales;switch(e.type){case"numberFormat":return{id:r,format:n.getNumberFormat(a,o.number[e.style]).format};case"dateFormat":return{id:r,format:n.getDateTimeFormat(a,o.date[e.style]).format};case"timeFormat":return{id:r,format:n.getDateTimeFormat(a,o.time[e.style]).format};case"pluralFormat":return new PluralFormat(r,e.offset,this.compileOptions(t),n.getPluralRules(a,{type:e.ordinal?"ordinal":"cardinal"}));case"selectFormat":return new SelectFormat(r,this.compileOptions(t));default:throw new Error("Message element does not have a valid format type")}},t.prototype.compileOptions=function(t){var r=this,e=t.format,n=e.options;this.pluralStack.push(this.currentPlural),this.currentPlural="pluralFormat"===e.type?t:null;var o=n.reduce(function(t,e){return t[e.selector]=r.compileMessage(e.value),t},{});return this.currentPlural=this.pluralStack.pop(),o},t}(),Formatter=function(t){this.id=t},StringFormat=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return __extends(e,t),e.prototype.format=function(t){return t||"number"==typeof t?"string"==typeof t?t:String(t):""},e}(Formatter),PluralFormat=function(){function t(t,e,r,n){this.id=t,this.offset=e,this.options=r,this.pluralRules=n}return t.prototype.getOption=function(t){var e=this.options;return e["="+t]||e[this.pluralRules.select(t-this.offset)]||e.other},t}(),PluralOffsetString=function(a){function t(t,e,r,n){var o=a.call(this,t)||this;return o.offset=e,o.numberFormat=r,o.string=n,o}return __extends(t,a),t.prototype.format=function(t){var e=this.numberFormat.format(t-this.offset);return this.string.replace(/(^|[^\\])#/g,"$1"+e).replace(/\\#/g,"#")},t}(Formatter),SelectFormat=function(){function t(t,e){this.id=t,this.options=e}return t.prototype.getOption=function(t){var e=this.options;return e[t]||e.other},t}();function isSelectOrPluralFormat(t){return!!t.options}var __extends$1=function(){var n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};return function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}}(),__assign=function(){return(__assign=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function resolveLocale(t){"string"==typeof t&&(t=[t]);try{return Intl.NumberFormat.supportedLocalesOf(t,{localeMatcher:"best fit"})[0]}catch(t){return IntlMessageFormat.defaultLocale}}function formatPatterns(t,e){for(var r="",n=0,o=t;n<o.length;n++){var a=o[n];if("string"!=typeof a){var i=a.id;if(!(e&&i in e))throw new FormatError("A value must be provided for: "+i,i);var s=e[i];isSelectOrPluralFormat(a)?r+=formatPatterns(a.getOption(s),e):r+=a.format(s)}else r+=a}return r}function mergeConfig(r,n){return n?__assign({},r||{},n||{},Object.keys(r).reduce(function(t,e){return t[e]=__assign({},r[e],n[e]||{}),t},{})):r}function mergeConfigs(r,n){return n?Object.keys(r).reduce(function(t,e){return t[e]=mergeConfig(r[e],n[e]),t},__assign({},r)):r}var FormatError=function(n){function t(t,e){var r=n.call(this,t)||this;return r.variableId=e,r}return __extends$1(t,n),t}(Error);function createDefaultFormatters(){return{getNumberFormat:function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return new((t=Intl.NumberFormat).bind.apply(t,[void 0].concat(e)))},getDateTimeFormat:function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return new((t=Intl.DateTimeFormat).bind.apply(t,[void 0].concat(e)))},getPluralRules:function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return new((t=Intl.PluralRules).bind.apply(t,[void 0].concat(e)))}}}var IntlMessageFormat=function(){function s(t,e,r,n){var o=this;if(void 0===e&&(e=s.defaultLocale),this.format=function(t){try{return formatPatterns(o.pattern,t)}catch(t){throw t.variableId?new Error("The intl string context variable '"+t.variableId+"' was not provided to the string '"+o.message+"'"):t}},"string"==typeof t){if(!s.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");this.ast=s.__parse(t)}else this.ast=t;if(this.message=t,!this.ast||"messageFormatPattern"!==this.ast.type)throw new TypeError("A message must be provided as a String or AST.");var a=mergeConfigs(s.formats,r);this.locale=resolveLocale(e||[]);var i=n&&n.formatters||createDefaultFormatters();this.pattern=new Compiler(e,a,i).compile(this.ast)}return s.prototype.resolvedOptions=function(){return{locale:this.locale}},s.prototype.getAst=function(){return this.ast},s.defaultLocale="en",s.__parse=void 0,s.formats={number:{currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},s}();exports.IntlMessageFormat=IntlMessageFormat,exports.createDefaultFormatters=createDefaultFormatters,exports.default=IntlMessageFormat;
