{"version": 3, "names": ["_deprecated", "_interopRequireDefault", "require", "DropZoneProvider", "children", "deprecated", "since", "hint"], "sources": ["@wordpress/components/src/drop-zone/provider.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport deprecated from '@wordpress/deprecated';\n\nexport default function DropZoneProvider( {\n\tchildren,\n}: {\n\tchildren: React.ReactNode;\n} ) {\n\tdeprecated( 'wp.components.DropZoneProvider', {\n\t\tsince: '5.8',\n\t\thint: 'wp.component.DropZone no longer needs a provider. wp.components.DropZoneProvider is safe to remove from your code.',\n\t} );\n\treturn children;\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAHA;AACA;AACA;;AAGe,SAASC,gBAAgBA,CAAE;EACzCC;AAGD,CAAC,EAAG;EACH,IAAAC,mBAAU,EAAE,gCAAgC,EAAE;IAC7CC,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE;EACP,CAAE,CAAC;EACH,OAAOH,QAAQ;AAChB"}