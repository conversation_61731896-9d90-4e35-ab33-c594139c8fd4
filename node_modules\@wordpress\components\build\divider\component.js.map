{"version": 3, "names": ["Ariakit", "_interopRequireWildcard", "require", "_context", "_styles", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "UnconnectedDivider", "props", "forwardedRef", "contextProps", "useContextSystem", "_react", "createElement", "Separator", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "Divider", "contextConnect", "exports", "_default"], "sources": ["@wordpress/components/src/divider/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport * as Ariakit from '@ariakit/react';\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnect, useContextSystem } from '../context';\nimport { DividerView } from './styles';\nimport type { DividerProps } from './types';\n\nfunction UnconnectedDivider(\n\tprops: WordPressComponentProps< DividerProps, 'hr', false >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst contextProps = useContextSystem( props, 'Divider' );\n\n\treturn (\n\t\t<Ariakit.Separator\n\t\t\trender={ <DividerView /> }\n\t\t\t{ ...contextProps }\n\t\t\tref={ forwardedRef }\n\t\t/>\n\t);\n}\n\n/**\n * `Divider` is a layout component that separates groups of related content.\n *\n * ```js\n * import {\n * \t\t__experimentalDivider as Divider,\n * \t\t__experimentalText as Text,\n * \t\t__experimentalVStack as VStack,\n * } from `@wordpress/components`;\n *\n * function Example() {\n * \treturn (\n * \t\t<VStack spacing={4}>\n * \t\t\t<Text>Some text here</Text>\n * \t\t\t<Divider />\n * \t\t\t<Text>Some more text here</Text>\n * \t\t</VStack>\n * \t);\n * }\n * ```\n */\nexport const Divider = contextConnect( UnconnectedDivider, 'Divider' );\n\nexport default Divider;\n"], "mappings": ";;;;;;;AAIA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAOA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAAuC,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAZvC;AACA;AACA;AACA;;AAYA,SAASW,kBAAkBA,CAC1BC,KAA2D,EAC3DC,YAAiC,EAChC;EACD,MAAMC,YAAY,GAAG,IAAAC,yBAAgB,EAAEH,KAAK,EAAE,SAAU,CAAC;EAEzD,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACjC,OAAO,CAACkC,SAAS;IACjBC,MAAM,EAAG,IAAAH,MAAA,CAAAC,aAAA,EAAC7B,OAAA,CAAAgC,WAAW,MAAE,CAAG;IAAA,GACrBN,YAAY;IACjBO,GAAG,EAAGR;EAAc,CACpB,CAAC;AAEJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMS,OAAO,GAAG,IAAAC,uBAAc,EAAEZ,kBAAkB,EAAE,SAAU,CAAC;AAACa,OAAA,CAAAF,OAAA,GAAAA,OAAA;AAAA,IAAAG,QAAA,GAExDH,OAAO;AAAAE,OAAA,CAAA5B,OAAA,GAAA6B,QAAA"}