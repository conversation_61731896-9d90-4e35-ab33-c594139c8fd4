"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _native = require("@react-navigation/native");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _components = require("@wordpress/components");
var _navBar = _interopRequireDefault(require("../bottom-sheet/nav-bar"));
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const FocalPointSettingsPanelMemo = (0, _element.memo)(({
  focalPoint,
  onFocalPointChange,
  shouldEnableBottomSheetScroll,
  url
}) => {
  const navigation = (0, _native.useNavigation)();
  function onButtonPress(action) {
    navigation.goBack();
    if (action === 'apply') {
      onFocalPointChange(draftFocalPoint);
    }
  }
  const [draftFocalPoint, setDraftFocalPoint] = (0, _element.useState)(focalPoint);
  function setPosition(coordinates) {
    setDraftFocalPoint(prevState => ({
      ...prevState,
      ...coordinates
    }));
  }
  return (0, _react.createElement)(_reactNative.SafeAreaView, {
    style: _styles.default.safearea
  }, (0, _react.createElement)(_navBar.default, null, (0, _react.createElement)(_navBar.default.DismissButton, {
    onPress: () => onButtonPress('cancel')
  }), (0, _react.createElement)(_navBar.default.Heading, null, (0, _i18n.__)('Edit focal point')), (0, _react.createElement)(_navBar.default.ApplyButton, {
    onPress: () => onButtonPress('apply')
  })), (0, _react.createElement)(_components.FocalPointPicker, {
    focalPoint: draftFocalPoint,
    onChange: (0, _element.useCallback)(setPosition, []),
    shouldEnableBottomSheetScroll: shouldEnableBottomSheetScroll,
    url: url
  }));
});
function FocalPointSettingsPanel(props) {
  const route = (0, _native.useRoute)();
  const {
    shouldEnableBottomSheetScroll
  } = (0, _element.useContext)(_components.BottomSheetContext);
  return (0, _react.createElement)(FocalPointSettingsPanelMemo, {
    shouldEnableBottomSheetScroll: shouldEnableBottomSheetScroll,
    ...props,
    ...route.params
  });
}
var _default = FocalPointSettingsPanel;
exports.default = _default;
//# sourceMappingURL=index.native.js.map