{"version": 3, "names": ["_element", "require", "styles", "_interopRequireWildcard", "_utils", "_context", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "isValidBorder", "border", "<PERSON><PERSON><PERSON><PERSON>", "width", "undefined", "hasColor", "color", "useBorderControl", "props", "className", "colors", "isCompact", "onChange", "enableAlpha", "enableStyle", "shouldSanitizeBorder", "size", "value", "__experimentalIsRenderedInSidebar", "__next40pxDefaultSize", "otherProps", "useContextSystem", "computedSize", "widthValue", "originalWidthUnit", "parseQuantityAndUnitFromRawValue", "widthUnit", "had<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorSelection", "setColorSelection", "useState", "styleSelection", "setStyleSelection", "isStyleSettable", "onBorderChange", "useCallback", "newBorder", "onWidthChange", "newWidth", "newWidthValue", "parsedValue", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedBorder", "style", "onSliderChange", "cx", "useCx", "classes", "useMemo", "borderControl", "wrapperWidth", "innerWrapperClassName", "widthStyle", "heightStyle", "wrapperHeight", "innerWrapper", "sliderClassName", "borderSlider", "inputWidth", "previousStyleSelection"], "sources": ["@wordpress/components/src/border-control/border-control/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useCallback, useMemo, useState } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport * as styles from '../styles';\nimport { parseQuantityAndUnitFromRawValue } from '../../unit-control/utils';\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useCx } from '../../utils/hooks/use-cx';\n\nimport type { Border, BorderControlProps } from '../types';\n\n// If either width or color are defined, the border is considered valid\n// and a border style can be set as well.\nconst isValidBorder = ( border?: Border ) => {\n\tconst hasWidth = border?.width !== undefined && border.width !== '';\n\tconst hasColor = border?.color !== undefined;\n\treturn hasWidth || hasColor;\n};\n\nexport function useBorderControl(\n\tprops: WordPressComponentProps< BorderControlProps, 'div' >\n) {\n\tconst {\n\t\tclassName,\n\t\tcolors = [],\n\t\tisCompact,\n\t\tonChange,\n\t\tenableAlpha = true,\n\t\tenableStyle = true,\n\t\tshouldSanitizeBorder = true,\n\t\tsize = 'default',\n\t\tvalue: border,\n\t\twidth,\n\t\t__experimentalIsRenderedInSidebar = false,\n\t\t__next40pxDefaultSize,\n\t\t...otherProps\n\t} = useContextSystem( props, 'BorderControl' );\n\n\tconst computedSize =\n\t\tsize === 'default' && __next40pxDefaultSize ? '__unstable-large' : size;\n\n\tconst [ widthValue, originalWidthUnit ] = parseQuantityAndUnitFromRawValue(\n\t\tborder?.width\n\t);\n\tconst widthUnit = originalWidthUnit || 'px';\n\tconst hadPreviousZeroWidth = widthValue === 0;\n\n\tconst [ colorSelection, setColorSelection ] = useState< string >();\n\tconst [ styleSelection, setStyleSelection ] = useState< string >();\n\n\tconst isStyleSettable = shouldSanitizeBorder\n\t\t? isValidBorder( border )\n\t\t: true;\n\n\tconst onBorderChange = useCallback(\n\t\t( newBorder?: Border ) => {\n\t\t\tif ( shouldSanitizeBorder && ! isValidBorder( newBorder ) ) {\n\t\t\t\tonChange( undefined );\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tonChange( newBorder );\n\t\t},\n\t\t[ onChange, shouldSanitizeBorder ]\n\t);\n\n\tconst onWidthChange = useCallback(\n\t\t( newWidth?: string ) => {\n\t\t\tconst newWidthValue = newWidth === '' ? undefined : newWidth;\n\t\t\tconst [ parsedValue ] =\n\t\t\t\tparseQuantityAndUnitFromRawValue( newWidth );\n\t\t\tconst hasZeroWidth = parsedValue === 0;\n\t\t\tconst updatedBorder = { ...border, width: newWidthValue };\n\n\t\t\t// Setting the border width explicitly to zero will also set the\n\t\t\t// border style to `none` and clear the border color.\n\t\t\tif ( hasZeroWidth && ! hadPreviousZeroWidth ) {\n\t\t\t\t// Before clearing the color and style selections, keep track of\n\t\t\t\t// the current selections so they can be restored when the width\n\t\t\t\t// changes to a non-zero value.\n\t\t\t\tsetColorSelection( border?.color );\n\t\t\t\tsetStyleSelection( border?.style );\n\n\t\t\t\t// Clear the color and style border properties.\n\t\t\t\tupdatedBorder.color = undefined;\n\t\t\t\tupdatedBorder.style = 'none';\n\t\t\t}\n\n\t\t\t// Selection has changed from zero border width to non-zero width.\n\t\t\tif ( ! hasZeroWidth && hadPreviousZeroWidth ) {\n\t\t\t\t// Restore previous border color and style selections if width\n\t\t\t\t// is now not zero.\n\t\t\t\tif ( updatedBorder.color === undefined ) {\n\t\t\t\t\tupdatedBorder.color = colorSelection;\n\t\t\t\t}\n\t\t\t\tif ( updatedBorder.style === 'none' ) {\n\t\t\t\t\tupdatedBorder.style = styleSelection;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tonBorderChange( updatedBorder );\n\t\t},\n\t\t[\n\t\t\tborder,\n\t\t\thadPreviousZeroWidth,\n\t\t\tcolorSelection,\n\t\t\tstyleSelection,\n\t\t\tonBorderChange,\n\t\t]\n\t);\n\n\tconst onSliderChange = useCallback(\n\t\t( value?: number ) => {\n\t\t\tonWidthChange( `${ value }${ widthUnit }` );\n\t\t},\n\t\t[ onWidthChange, widthUnit ]\n\t);\n\n\t// Generate class names.\n\tconst cx = useCx();\n\tconst classes = useMemo( () => {\n\t\treturn cx( styles.borderControl, className );\n\t}, [ className, cx ] );\n\n\tlet wrapperWidth = width;\n\tif ( isCompact ) {\n\t\t// Widths below represent the minimum usable width for compact controls.\n\t\t// Taller controls contain greater internal padding, thus greater width.\n\t\twrapperWidth = size === '__unstable-large' ? '116px' : '90px';\n\t}\n\tconst innerWrapperClassName = useMemo( () => {\n\t\tconst widthStyle = !! wrapperWidth && styles.wrapperWidth;\n\t\tconst heightStyle = styles.wrapperHeight( computedSize );\n\n\t\treturn cx( styles.innerWrapper(), widthStyle, heightStyle );\n\t}, [ wrapperWidth, cx, computedSize ] );\n\n\tconst sliderClassName = useMemo( () => {\n\t\treturn cx( styles.borderSlider() );\n\t}, [ cx ] );\n\n\treturn {\n\t\t...otherProps,\n\t\tclassName: classes,\n\t\tcolors,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\tinnerWrapperClassName,\n\t\tinputWidth: wrapperWidth,\n\t\tisStyleSettable,\n\t\tonBorderChange,\n\t\tonSliderChange,\n\t\tonWidthChange,\n\t\tpreviousStyleSelection: styleSelection,\n\t\tsliderClassName,\n\t\tvalue: border,\n\t\twidthUnit,\n\t\twidthValue,\n\t\tsize: computedSize,\n\t\t__experimentalIsRenderedInSidebar,\n\t\t__next40pxDefaultSize,\n\t};\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAAiD,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAZjD;AACA;AACA;;AAGA;AACA;AACA;;AASA;AACA;AACA,MAAMW,aAAa,GAAKC,MAAe,IAAM;EAC5C,MAAMC,QAAQ,GAAGD,MAAM,EAAEE,KAAK,KAAKC,SAAS,IAAIH,MAAM,CAACE,KAAK,KAAK,EAAE;EACnE,MAAME,QAAQ,GAAGJ,MAAM,EAAEK,KAAK,KAAKF,SAAS;EAC5C,OAAOF,QAAQ,IAAIG,QAAQ;AAC5B,CAAC;AAEM,SAASE,gBAAgBA,CAC/BC,KAA2D,EAC1D;EACD,MAAM;IACLC,SAAS;IACTC,MAAM,GAAG,EAAE;IACXC,SAAS;IACTC,QAAQ;IACRC,WAAW,GAAG,IAAI;IAClBC,WAAW,GAAG,IAAI;IAClBC,oBAAoB,GAAG,IAAI;IAC3BC,IAAI,GAAG,SAAS;IAChBC,KAAK,EAAEhB,MAAM;IACbE,KAAK;IACLe,iCAAiC,GAAG,KAAK;IACzCC,qBAAqB;IACrB,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEb,KAAK,EAAE,eAAgB,CAAC;EAE9C,MAAMc,YAAY,GACjBN,IAAI,KAAK,SAAS,IAAIG,qBAAqB,GAAG,kBAAkB,GAAGH,IAAI;EAExE,MAAM,CAAEO,UAAU,EAAEC,iBAAiB,CAAE,GAAG,IAAAC,uCAAgC,EACzExB,MAAM,EAAEE,KACT,CAAC;EACD,MAAMuB,SAAS,GAAGF,iBAAiB,IAAI,IAAI;EAC3C,MAAMG,oBAAoB,GAAGJ,UAAU,KAAK,CAAC;EAE7C,MAAM,CAAEK,cAAc,EAAEC,iBAAiB,CAAE,GAAG,IAAAC,iBAAQ,EAAW,CAAC;EAClE,MAAM,CAAEC,cAAc,EAAEC,iBAAiB,CAAE,GAAG,IAAAF,iBAAQ,EAAW,CAAC;EAElE,MAAMG,eAAe,GAAGlB,oBAAoB,GACzCf,aAAa,CAAEC,MAAO,CAAC,GACvB,IAAI;EAEP,MAAMiC,cAAc,GAAG,IAAAC,oBAAW,EAC/BC,SAAkB,IAAM;IACzB,IAAKrB,oBAAoB,IAAI,CAAEf,aAAa,CAAEoC,SAAU,CAAC,EAAG;MAC3DxB,QAAQ,CAAER,SAAU,CAAC;MACrB;IACD;IACAQ,QAAQ,CAAEwB,SAAU,CAAC;EACtB,CAAC,EACD,CAAExB,QAAQ,EAAEG,oBAAoB,CACjC,CAAC;EAED,MAAMsB,aAAa,GAAG,IAAAF,oBAAW,EAC9BG,QAAiB,IAAM;IACxB,MAAMC,aAAa,GAAGD,QAAQ,KAAK,EAAE,GAAGlC,SAAS,GAAGkC,QAAQ;IAC5D,MAAM,CAAEE,WAAW,CAAE,GACpB,IAAAf,uCAAgC,EAAEa,QAAS,CAAC;IAC7C,MAAMG,YAAY,GAAGD,WAAW,KAAK,CAAC;IACtC,MAAME,aAAa,GAAG;MAAE,GAAGzC,MAAM;MAAEE,KAAK,EAAEoC;IAAc,CAAC;;IAEzD;IACA;IACA,IAAKE,YAAY,IAAI,CAAEd,oBAAoB,EAAG;MAC7C;MACA;MACA;MACAE,iBAAiB,CAAE5B,MAAM,EAAEK,KAAM,CAAC;MAClC0B,iBAAiB,CAAE/B,MAAM,EAAE0C,KAAM,CAAC;;MAElC;MACAD,aAAa,CAACpC,KAAK,GAAGF,SAAS;MAC/BsC,aAAa,CAACC,KAAK,GAAG,MAAM;IAC7B;;IAEA;IACA,IAAK,CAAEF,YAAY,IAAId,oBAAoB,EAAG;MAC7C;MACA;MACA,IAAKe,aAAa,CAACpC,KAAK,KAAKF,SAAS,EAAG;QACxCsC,aAAa,CAACpC,KAAK,GAAGsB,cAAc;MACrC;MACA,IAAKc,aAAa,CAACC,KAAK,KAAK,MAAM,EAAG;QACrCD,aAAa,CAACC,KAAK,GAAGZ,cAAc;MACrC;IACD;IAEAG,cAAc,CAAEQ,aAAc,CAAC;EAChC,CAAC,EACD,CACCzC,MAAM,EACN0B,oBAAoB,EACpBC,cAAc,EACdG,cAAc,EACdG,cAAc,CAEhB,CAAC;EAED,MAAMU,cAAc,GAAG,IAAAT,oBAAW,EAC/BlB,KAAc,IAAM;IACrBoB,aAAa,CAAG,GAAGpB,KAAO,GAAGS,SAAW,EAAE,CAAC;EAC5C,CAAC,EACD,CAAEW,aAAa,EAAEX,SAAS,CAC3B,CAAC;;EAED;EACA,MAAMmB,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,OAAOH,EAAE,CAAExE,MAAM,CAAC4E,aAAa,EAAExC,SAAU,CAAC;EAC7C,CAAC,EAAE,CAAEA,SAAS,EAAEoC,EAAE,CAAG,CAAC;EAEtB,IAAIK,YAAY,GAAG/C,KAAK;EACxB,IAAKQ,SAAS,EAAG;IAChB;IACA;IACAuC,YAAY,GAAGlC,IAAI,KAAK,kBAAkB,GAAG,OAAO,GAAG,MAAM;EAC9D;EACA,MAAMmC,qBAAqB,GAAG,IAAAH,gBAAO,EAAE,MAAM;IAC5C,MAAMI,UAAU,GAAG,CAAC,CAAEF,YAAY,IAAI7E,MAAM,CAAC6E,YAAY;IACzD,MAAMG,WAAW,GAAGhF,MAAM,CAACiF,aAAa,CAAEhC,YAAa,CAAC;IAExD,OAAOuB,EAAE,CAAExE,MAAM,CAACkF,YAAY,CAAC,CAAC,EAAEH,UAAU,EAAEC,WAAY,CAAC;EAC5D,CAAC,EAAE,CAAEH,YAAY,EAAEL,EAAE,EAAEvB,YAAY,CAAG,CAAC;EAEvC,MAAMkC,eAAe,GAAG,IAAAR,gBAAO,EAAE,MAAM;IACtC,OAAOH,EAAE,CAAExE,MAAM,CAACoF,YAAY,CAAC,CAAE,CAAC;EACnC,CAAC,EAAE,CAAEZ,EAAE,CAAG,CAAC;EAEX,OAAO;IACN,GAAGzB,UAAU;IACbX,SAAS,EAAEsC,OAAO;IAClBrC,MAAM;IACNG,WAAW;IACXC,WAAW;IACXqC,qBAAqB;IACrBO,UAAU,EAAER,YAAY;IACxBjB,eAAe;IACfC,cAAc;IACdU,cAAc;IACdP,aAAa;IACbsB,sBAAsB,EAAE5B,cAAc;IACtCyB,eAAe;IACfvC,KAAK,EAAEhB,MAAM;IACbyB,SAAS;IACTH,UAAU;IACVP,IAAI,EAAEM,YAAY;IAClBJ,iCAAiC;IACjCC;EACD,CAAC;AACF"}