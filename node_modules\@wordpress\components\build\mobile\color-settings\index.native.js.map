{"version": 3, "names": ["_native", "require", "_element", "_components", "_pickerScreen", "_interopRequireDefault", "_gradientPickerScreen", "_palette", "_utils", "ColorSettingsMemo", "memo", "defaultSettings", "onHandleClosingBottomSheet", "shouldEnableBottomSheetMaxHeight", "onColorChange", "colorValue", "gradientValue", "onGradientChange", "onColorCleared", "label", "hideNavigation", "useEffect", "_react", "createElement", "BottomSheet", "NavigationContainer", "NavigationScreen", "name", "colorsUtils", "screens", "palette", "initialParams", "default", "picker", "gradientPicker", "ColorSettings", "props", "route", "useRoute", "useContext", "BottomSheetContext", "params", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/color-settings/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { useRoute } from '@react-navigation/native';\n\n/**\n * WordPress dependencies\n */\nimport { memo, useEffect, useContext } from '@wordpress/element';\nimport { BottomSheetContext, BottomSheet } from '@wordpress/components';\n\n/**\n * Internal dependencies\n */\nimport PickerScreen from './picker-screen';\nimport GradientPickerScreen from './gradient-picker-screen';\nimport PaletteScreen from './palette.screen';\n\nimport { colorsUtils } from './utils';\n\nconst ColorSettingsMemo = memo(\n\t( {\n\t\tdefaultSettings,\n\t\tonHandleClosingBottomSheet,\n\t\tshouldEnableBottomSheetMaxHeight,\n\t\tonColorChange,\n\t\tcolorValue,\n\t\tgradientValue,\n\t\tonGradientChange,\n\t\tonColorCleared,\n\t\tlabel,\n\t\thideNavigation,\n\t} ) => {\n\t\tuseEffect( () => {\n\t\t\tshouldEnableBottomSheetMaxHeight( true );\n\t\t\tonHandleClosingBottomSheet( null );\n\t\t\t// Disable reason: deferring this refactor to the native team.\n\t\t\t// see https://github.com/WordPress/gutenberg/pull/41166\n\t\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t\t}, [] );\n\t\treturn (\n\t\t\t<BottomSheet.NavigationContainer>\n\t\t\t\t<BottomSheet.NavigationScreen\n\t\t\t\t\tname={ colorsUtils.screens.palette }\n\t\t\t\t\tinitialParams={ {\n\t\t\t\t\t\tdefaultSettings,\n\t\t\t\t\t\tonColorChange,\n\t\t\t\t\t\tcolorValue,\n\t\t\t\t\t\tgradientValue,\n\t\t\t\t\t\tonGradientChange,\n\t\t\t\t\t\tonColorCleared,\n\t\t\t\t\t\tlabel,\n\t\t\t\t\t\thideNavigation,\n\t\t\t\t\t} }\n\t\t\t\t>\n\t\t\t\t\t<PaletteScreen />\n\t\t\t\t</BottomSheet.NavigationScreen>\n\t\t\t\t<BottomSheet.NavigationScreen\n\t\t\t\t\tname={ colorsUtils.screens.picker }\n\t\t\t\t>\n\t\t\t\t\t<PickerScreen />\n\t\t\t\t</BottomSheet.NavigationScreen>\n\t\t\t\t<BottomSheet.NavigationScreen\n\t\t\t\t\tname={ colorsUtils.screens.gradientPicker }\n\t\t\t\t>\n\t\t\t\t\t<GradientPickerScreen />\n\t\t\t\t</BottomSheet.NavigationScreen>\n\t\t\t</BottomSheet.NavigationContainer>\n\t\t);\n\t}\n);\nfunction ColorSettings( props ) {\n\tconst route = useRoute();\n\tconst { onHandleClosingBottomSheet, shouldEnableBottomSheetMaxHeight } =\n\t\tuseContext( BottomSheetContext );\n\n\treturn (\n\t\t<ColorSettingsMemo\n\t\t\tonHandleClosingBottomSheet={ onHandleClosingBottomSheet }\n\t\t\tshouldEnableBottomSheetMaxHeight={\n\t\t\t\tshouldEnableBottomSheetMaxHeight\n\t\t\t}\n\t\t\t{ ...props }\n\t\t\t{ ...route.params }\n\t\t/>\n\t);\n}\n\nexport default ColorSettings;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AAKA,IAAAG,aAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,qBAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,QAAA,GAAAF,sBAAA,CAAAJ,OAAA;AAEA,IAAAO,MAAA,GAAAP,OAAA;AAlBA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAOA,MAAMQ,iBAAiB,GAAG,IAAAC,aAAI,EAC7B,CAAE;EACDC,eAAe;EACfC,0BAA0B;EAC1BC,gCAAgC;EAChCC,aAAa;EACbC,UAAU;EACVC,aAAa;EACbC,gBAAgB;EAChBC,cAAc;EACdC,KAAK;EACLC;AACD,CAAC,KAAM;EACN,IAAAC,kBAAS,EAAE,MAAM;IAChBR,gCAAgC,CAAE,IAAK,CAAC;IACxCD,0BAA0B,CAAE,IAAK,CAAC;IAClC;IACA;IACA;EACD,CAAC,EAAE,EAAG,CAAC;EACP,OACC,IAAAU,MAAA,CAAAC,aAAA,EAACpB,WAAA,CAAAqB,WAAW,CAACC,mBAAmB,QAC/B,IAAAH,MAAA,CAAAC,aAAA,EAACpB,WAAA,CAAAqB,WAAW,CAACE,gBAAgB;IAC5BC,IAAI,EAAGC,kBAAW,CAACC,OAAO,CAACC,OAAS;IACpCC,aAAa,EAAG;MACfpB,eAAe;MACfG,aAAa;MACbC,UAAU;MACVC,aAAa;MACbC,gBAAgB;MAChBC,cAAc;MACdC,KAAK;MACLC;IACD;EAAG,GAEH,IAAAE,MAAA,CAAAC,aAAA,EAAChB,QAAA,CAAAyB,OAAa,MAAE,CACa,CAAC,EAC/B,IAAAV,MAAA,CAAAC,aAAA,EAACpB,WAAA,CAAAqB,WAAW,CAACE,gBAAgB;IAC5BC,IAAI,EAAGC,kBAAW,CAACC,OAAO,CAACI;EAAQ,GAEnC,IAAAX,MAAA,CAAAC,aAAA,EAACnB,aAAA,CAAA4B,OAAY,MAAE,CACc,CAAC,EAC/B,IAAAV,MAAA,CAAAC,aAAA,EAACpB,WAAA,CAAAqB,WAAW,CAACE,gBAAgB;IAC5BC,IAAI,EAAGC,kBAAW,CAACC,OAAO,CAACK;EAAgB,GAE3C,IAAAZ,MAAA,CAAAC,aAAA,EAACjB,qBAAA,CAAA0B,OAAoB,MAAE,CACM,CACE,CAAC;AAEpC,CACD,CAAC;AACD,SAASG,aAAaA,CAAEC,KAAK,EAAG;EAC/B,MAAMC,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,MAAM;IAAE1B,0BAA0B;IAAEC;EAAiC,CAAC,GACrE,IAAA0B,mBAAU,EAAEC,8BAAmB,CAAC;EAEjC,OACC,IAAAlB,MAAA,CAAAC,aAAA,EAACd,iBAAiB;IACjBG,0BAA0B,EAAGA,0BAA4B;IACzDC,gCAAgC,EAC/BA,gCACA;IAAA,GACIuB,KAAK;IAAA,GACLC,KAAK,CAACI;EAAM,CACjB,CAAC;AAEJ;AAAC,IAAAC,QAAA,GAEcP,aAAa;AAAAQ,OAAA,CAAAX,OAAA,GAAAU,QAAA"}