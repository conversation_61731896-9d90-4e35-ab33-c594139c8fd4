"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = AxialInputControls;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _utils = require("../unit-control/utils");
var _tooltip = _interopRequireDefault(require("../tooltip"));
var _utils2 = require("./utils");
var _boxControlStyles = require("./styles/box-control-styles");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const groupedSides = ['vertical', 'horizontal'];
function AxialInputControls({
  __next40pxDefaultSize,
  onChange,
  onFocus,
  values,
  selectedUnits,
  setSelectedUnits,
  sides,
  ...props
}) {
  const generatedId = (0, _compose.useInstanceId)(AxialInputControls, `box-control-input`);
  const createHandleOnFocus = side => event => {
    if (!onFocus) {
      return;
    }
    onFocus(event, {
      side
    });
  };
  const handleOnValueChange = (side, next) => {
    if (!onChange) {
      return;
    }
    const nextValues = {
      ...values
    };
    const isNumeric = next !== undefined && !isNaN(parseFloat(next));
    const nextValue = isNumeric ? next : undefined;
    if (side === 'vertical') {
      nextValues.top = nextValue;
      nextValues.bottom = nextValue;
    }
    if (side === 'horizontal') {
      nextValues.left = nextValue;
      nextValues.right = nextValue;
    }
    onChange(nextValues);
  };
  const createHandleOnUnitChange = side => next => {
    const newUnits = {
      ...selectedUnits
    };
    if (side === 'vertical') {
      newUnits.top = next;
      newUnits.bottom = next;
    }
    if (side === 'horizontal') {
      newUnits.left = next;
      newUnits.right = next;
    }
    setSelectedUnits(newUnits);
  };

  // Filter sides if custom configuration provided, maintaining default order.
  const filteredSides = sides?.length ? groupedSides.filter(side => sides.includes(side)) : groupedSides;
  return (0, _react.createElement)(_react.Fragment, null, filteredSides.map(side => {
    var _CUSTOM_VALUE_SETTING, _CUSTOM_VALUE_SETTING2;
    const [parsedQuantity, parsedUnit] = (0, _utils.parseQuantityAndUnitFromRawValue)(side === 'vertical' ? values.top : values.left);
    const selectedUnit = side === 'vertical' ? selectedUnits.top : selectedUnits.left;
    const inputId = [generatedId, side].join('-');
    return (0, _react.createElement)(_boxControlStyles.InputWrapper, {
      key: side
    }, (0, _react.createElement)(_boxControlStyles.FlexedBoxControlIcon, {
      side: side,
      sides: sides
    }), (0, _react.createElement)(_tooltip.default, {
      placement: "top-end",
      text: _utils2.LABELS[side]
    }, (0, _react.createElement)(_boxControlStyles.StyledUnitControl, {
      ...props,
      __next40pxDefaultSize: __next40pxDefaultSize,
      className: "component-box-control__unit-control",
      id: inputId,
      isPressEnterToChange: true,
      value: [parsedQuantity, selectedUnit !== null && selectedUnit !== void 0 ? selectedUnit : parsedUnit].join(''),
      onChange: newValue => handleOnValueChange(side, newValue),
      onUnitChange: createHandleOnUnitChange(side),
      onFocus: createHandleOnFocus(side),
      label: _utils2.LABELS[side],
      hideLabelFromVision: true,
      key: side
    })), (0, _react.createElement)(_boxControlStyles.FlexedRangeControl, {
      __nextHasNoMarginBottom: true,
      __next40pxDefaultSize: __next40pxDefaultSize,
      "aria-controls": inputId,
      label: _utils2.LABELS[side],
      hideLabelFromVision: true,
      onChange: newValue => handleOnValueChange(side, newValue !== undefined ? [newValue, selectedUnit !== null && selectedUnit !== void 0 ? selectedUnit : parsedUnit].join('') : undefined),
      min: 0,
      max: (_CUSTOM_VALUE_SETTING = _utils2.CUSTOM_VALUE_SETTINGS[selectedUnit !== null && selectedUnit !== void 0 ? selectedUnit : 'px']?.max) !== null && _CUSTOM_VALUE_SETTING !== void 0 ? _CUSTOM_VALUE_SETTING : 10,
      step: (_CUSTOM_VALUE_SETTING2 = _utils2.CUSTOM_VALUE_SETTINGS[selectedUnit !== null && selectedUnit !== void 0 ? selectedUnit : 'px']?.step) !== null && _CUSTOM_VALUE_SETTING2 !== void 0 ? _CUSTOM_VALUE_SETTING2 : 0.1,
      value: parsedQuantity !== null && parsedQuantity !== void 0 ? parsedQuantity : 0,
      withInputField: false
    }));
  }));
}
//# sourceMappingURL=axial-input-controls.js.map