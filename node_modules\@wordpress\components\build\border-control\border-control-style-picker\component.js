"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _context = require("../../context");
var _toggleGroupControl = require("../../toggle-group-control");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BORDER_STYLES = [{
  label: (0, _i18n.__)('Solid'),
  icon: _icons.lineSolid,
  value: 'solid'
}, {
  label: (0, _i18n.__)('Dashed'),
  icon: _icons.lineDashed,
  value: 'dashed'
}, {
  label: (0, _i18n.__)('Dotted'),
  icon: _icons.lineDotted,
  value: 'dotted'
}];
function UnconnectedBorderControlStylePicker({
  onChange,
  ...restProps
}, forwardedRef) {
  return (0, _react.createElement)(_toggleGroupControl.ToggleGroupControl, {
    __nextHasNoMarginBottom: true,
    __next40pxDefaultSize: true,
    ref: forwardedRef,
    isDeselectable: true,
    onChange: value => {
      onChange?.(value);
    },
    ...restProps
  }, BORDER_STYLES.map(borderStyle => (0, _react.createElement)(_toggleGroupControl.ToggleGroupControlOptionIcon, {
    key: borderStyle.value,
    value: borderStyle.value,
    icon: borderStyle.icon,
    label: borderStyle.label
  })));
}
const BorderControlStylePicker = (0, _context.contextConnect)(UnconnectedBorderControlStylePicker, 'BorderControlStylePicker');
var _default = BorderControlStylePicker;
exports.default = _default;
//# sourceMappingURL=component.js.map