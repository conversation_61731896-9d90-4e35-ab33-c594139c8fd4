{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_deprecated", "_i18n", "_modal", "_button", "_pageControl", "Guide", "children", "className", "contentLabel", "finishButtonText", "__", "onFinish", "pages", "ref", "useRef", "currentPage", "setCurrentPage", "useState", "useEffect", "frame", "current", "querySelector", "HTMLElement", "focus", "Children", "count", "deprecated", "since", "alternative", "_Children$map", "map", "child", "content", "canGoBack", "canGoForward", "length", "goBack", "goForward", "_react", "createElement", "default", "classnames", "isDismissible", "onRequestClose", "onKeyDown", "event", "code", "preventDefault", "image", "numberOfPages", "variant", "onClick", "_default", "exports"], "sources": ["@wordpress/components/src/guide/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { useState, useEffect, Children, useRef } from '@wordpress/element';\nimport deprecated from '@wordpress/deprecated';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport Modal from '../modal';\nimport Button from '../button';\nimport PageControl from './page-control';\nimport type { GuideProps } from './types';\n\n/**\n * `Guide` is a React component that renders a _user guide_ in a modal. The guide consists of several pages which the user can step through one by one. The guide is finished when the modal is closed or when the user clicks _Finish_ on the last page of the guide.\n *\n * ```jsx\n * function MyTutorial() {\n * \tconst [ isOpen, setIsOpen ] = useState( true );\n *\n * \tif ( ! isOpen ) {\n * \t\treturn null;\n * \t}\n *\n * \treturn (\n * \t\t<Guide\n * \t\t\tonFinish={ () => setIsOpen( false ) }\n * \t\t\tpages={ [\n * \t\t\t\t{\n * \t\t\t\t\tcontent: <p>Welcome to the ACME Store!</p>,\n * \t\t\t\t},\n * \t\t\t\t{\n * \t\t\t\t\timage: <img src=\"https://acmestore.com/add-to-cart.png\" />,\n * \t\t\t\t\tcontent: (\n * \t\t\t\t\t\t<p>\n * \t\t\t\t\t\t\tClick <i>Add to Cart</i> to buy a product.\n * \t\t\t\t\t\t</p>\n * \t\t\t\t\t),\n * \t\t\t\t},\n * \t\t\t] }\n * \t\t/>\n * \t);\n * }\n * ```\n */\nfunction Guide( {\n\tchildren,\n\tclassName,\n\tcontentLabel,\n\tfinishButtonText = __( 'Finish' ),\n\tonFinish,\n\tpages = [],\n}: GuideProps ) {\n\tconst ref = useRef< HTMLDivElement >( null );\n\tconst [ currentPage, setCurrentPage ] = useState( 0 );\n\n\tuseEffect( () => {\n\t\t// Place focus at the top of the guide on mount and when the page changes.\n\t\tconst frame = ref.current?.querySelector( '.components-guide' );\n\t\tif ( frame instanceof HTMLElement ) {\n\t\t\tframe.focus();\n\t\t}\n\t}, [ currentPage ] );\n\n\tuseEffect( () => {\n\t\tif ( Children.count( children ) ) {\n\t\t\tdeprecated( 'Passing children to <Guide>', {\n\t\t\t\tsince: '5.5',\n\t\t\t\talternative: 'the `pages` prop',\n\t\t\t} );\n\t\t}\n\t}, [ children ] );\n\n\tif ( Children.count( children ) ) {\n\t\tpages =\n\t\t\tChildren.map( children, ( child ) => ( {\n\t\t\t\tcontent: child,\n\t\t\t} ) ) ?? [];\n\t}\n\n\tconst canGoBack = currentPage > 0;\n\tconst canGoForward = currentPage < pages.length - 1;\n\n\tconst goBack = () => {\n\t\tif ( canGoBack ) {\n\t\t\tsetCurrentPage( currentPage - 1 );\n\t\t}\n\t};\n\n\tconst goForward = () => {\n\t\tif ( canGoForward ) {\n\t\t\tsetCurrentPage( currentPage + 1 );\n\t\t}\n\t};\n\n\tif ( pages.length === 0 ) {\n\t\treturn null;\n\t}\n\n\treturn (\n\t\t<Modal\n\t\t\tclassName={ classnames( 'components-guide', className ) }\n\t\t\tcontentLabel={ contentLabel }\n\t\t\tisDismissible={ pages.length > 1 }\n\t\t\tonRequestClose={ onFinish }\n\t\t\tonKeyDown={ ( event ) => {\n\t\t\t\tif ( event.code === 'ArrowLeft' ) {\n\t\t\t\t\tgoBack();\n\t\t\t\t\t// Do not scroll the modal's contents.\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t} else if ( event.code === 'ArrowRight' ) {\n\t\t\t\t\tgoForward();\n\t\t\t\t\t// Do not scroll the modal's contents.\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t}\n\t\t\t} }\n\t\t\tref={ ref }\n\t\t>\n\t\t\t<div className=\"components-guide__container\">\n\t\t\t\t<div className=\"components-guide__page\">\n\t\t\t\t\t{ pages[ currentPage ].image }\n\n\t\t\t\t\t{ pages.length > 1 && (\n\t\t\t\t\t\t<PageControl\n\t\t\t\t\t\t\tcurrentPage={ currentPage }\n\t\t\t\t\t\t\tnumberOfPages={ pages.length }\n\t\t\t\t\t\t\tsetCurrentPage={ setCurrentPage }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\n\t\t\t\t\t{ pages[ currentPage ].content }\n\t\t\t\t</div>\n\n\t\t\t\t<div className=\"components-guide__footer\">\n\t\t\t\t\t{ canGoBack && (\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tclassName=\"components-guide__back-button\"\n\t\t\t\t\t\t\tvariant=\"tertiary\"\n\t\t\t\t\t\t\tonClick={ goBack }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ __( 'Previous' ) }\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t) }\n\t\t\t\t\t{ canGoForward && (\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tclassName=\"components-guide__forward-button\"\n\t\t\t\t\t\t\tvariant=\"primary\"\n\t\t\t\t\t\t\tonClick={ goForward }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ __( 'Next' ) }\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t) }\n\t\t\t\t\t{ ! canGoForward && (\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tclassName=\"components-guide__finish-button\"\n\t\t\t\t\t\t\tvariant=\"primary\"\n\t\t\t\t\t\t\tonClick={ onFinish }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ finishButtonText }\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t) }\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</Modal>\n\t);\n}\n\nexport default Guide;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAKA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,YAAA,GAAAP,sBAAA,CAAAC,OAAA;AAjBA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,KAAKA,CAAE;EACfC,QAAQ;EACRC,SAAS;EACTC,YAAY;EACZC,gBAAgB,GAAG,IAAAC,QAAE,EAAE,QAAS,CAAC;EACjCC,QAAQ;EACRC,KAAK,GAAG;AACG,CAAC,EAAG;EACf,MAAMC,GAAG,GAAG,IAAAC,eAAM,EAAoB,IAAK,CAAC;EAC5C,MAAM,CAAEC,WAAW,EAAEC,cAAc,CAAE,GAAG,IAAAC,iBAAQ,EAAE,CAAE,CAAC;EAErD,IAAAC,kBAAS,EAAE,MAAM;IAChB;IACA,MAAMC,KAAK,GAAGN,GAAG,CAACO,OAAO,EAAEC,aAAa,CAAE,mBAAoB,CAAC;IAC/D,IAAKF,KAAK,YAAYG,WAAW,EAAG;MACnCH,KAAK,CAACI,KAAK,CAAC,CAAC;IACd;EACD,CAAC,EAAE,CAAER,WAAW,CAAG,CAAC;EAEpB,IAAAG,kBAAS,EAAE,MAAM;IAChB,IAAKM,iBAAQ,CAACC,KAAK,CAAEnB,QAAS,CAAC,EAAG;MACjC,IAAAoB,mBAAU,EAAE,6BAA6B,EAAE;QAC1CC,KAAK,EAAE,KAAK;QACZC,WAAW,EAAE;MACd,CAAE,CAAC;IACJ;EACD,CAAC,EAAE,CAAEtB,QAAQ,CAAG,CAAC;EAEjB,IAAKkB,iBAAQ,CAACC,KAAK,CAAEnB,QAAS,CAAC,EAAG;IAAA,IAAAuB,aAAA;IACjCjB,KAAK,IAAAiB,aAAA,GACJL,iBAAQ,CAACM,GAAG,CAAExB,QAAQ,EAAIyB,KAAK,KAAQ;MACtCC,OAAO,EAAED;IACV,CAAC,CAAG,CAAC,cAAAF,aAAA,cAAAA,aAAA,GAAI,EAAE;EACb;EAEA,MAAMI,SAAS,GAAGlB,WAAW,GAAG,CAAC;EACjC,MAAMmB,YAAY,GAAGnB,WAAW,GAAGH,KAAK,CAACuB,MAAM,GAAG,CAAC;EAEnD,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACpB,IAAKH,SAAS,EAAG;MAChBjB,cAAc,CAAED,WAAW,GAAG,CAAE,CAAC;IAClC;EACD,CAAC;EAED,MAAMsB,SAAS,GAAGA,CAAA,KAAM;IACvB,IAAKH,YAAY,EAAG;MACnBlB,cAAc,CAAED,WAAW,GAAG,CAAE,CAAC;IAClC;EACD,CAAC;EAED,IAAKH,KAAK,CAACuB,MAAM,KAAK,CAAC,EAAG;IACzB,OAAO,IAAI;EACZ;EAEA,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACrC,MAAA,CAAAsC,OAAK;IACLjC,SAAS,EAAG,IAAAkC,mBAAU,EAAE,kBAAkB,EAAElC,SAAU,CAAG;IACzDC,YAAY,EAAGA,YAAc;IAC7BkC,aAAa,EAAG9B,KAAK,CAACuB,MAAM,GAAG,CAAG;IAClCQ,cAAc,EAAGhC,QAAU;IAC3BiC,SAAS,EAAKC,KAAK,IAAM;MACxB,IAAKA,KAAK,CAACC,IAAI,KAAK,WAAW,EAAG;QACjCV,MAAM,CAAC,CAAC;QACR;QACAS,KAAK,CAACE,cAAc,CAAC,CAAC;MACvB,CAAC,MAAM,IAAKF,KAAK,CAACC,IAAI,KAAK,YAAY,EAAG;QACzCT,SAAS,CAAC,CAAC;QACX;QACAQ,KAAK,CAACE,cAAc,CAAC,CAAC;MACvB;IACD,CAAG;IACHlC,GAAG,EAAGA;EAAK,GAEX,IAAAyB,MAAA,CAAAC,aAAA;IAAKhC,SAAS,EAAC;EAA6B,GAC3C,IAAA+B,MAAA,CAAAC,aAAA;IAAKhC,SAAS,EAAC;EAAwB,GACpCK,KAAK,CAAEG,WAAW,CAAE,CAACiC,KAAK,EAE1BpC,KAAK,CAACuB,MAAM,GAAG,CAAC,IACjB,IAAAG,MAAA,CAAAC,aAAA,EAACnC,YAAA,CAAAoC,OAAW;IACXzB,WAAW,EAAGA,WAAa;IAC3BkC,aAAa,EAAGrC,KAAK,CAACuB,MAAQ;IAC9BnB,cAAc,EAAGA;EAAgB,CACjC,CACD,EAECJ,KAAK,CAAEG,WAAW,CAAE,CAACiB,OACnB,CAAC,EAEN,IAAAM,MAAA,CAAAC,aAAA;IAAKhC,SAAS,EAAC;EAA0B,GACtC0B,SAAS,IACV,IAAAK,MAAA,CAAAC,aAAA,EAACpC,OAAA,CAAAqC,OAAM;IACNjC,SAAS,EAAC,+BAA+B;IACzC2C,OAAO,EAAC,UAAU;IAClBC,OAAO,EAAGf;EAAQ,GAEhB,IAAA1B,QAAE,EAAE,UAAW,CACV,CACR,EACCwB,YAAY,IACb,IAAAI,MAAA,CAAAC,aAAA,EAACpC,OAAA,CAAAqC,OAAM;IACNjC,SAAS,EAAC,kCAAkC;IAC5C2C,OAAO,EAAC,SAAS;IACjBC,OAAO,EAAGd;EAAW,GAEnB,IAAA3B,QAAE,EAAE,MAAO,CACN,CACR,EACC,CAAEwB,YAAY,IACf,IAAAI,MAAA,CAAAC,aAAA,EAACpC,OAAA,CAAAqC,OAAM;IACNjC,SAAS,EAAC,iCAAiC;IAC3C2C,OAAO,EAAC,SAAS;IACjBC,OAAO,EAAGxC;EAAU,GAElBF,gBACK,CAEL,CACD,CACC,CAAC;AAEV;AAAC,IAAA2C,QAAA,GAEc/C,KAAK;AAAAgD,OAAA,CAAAb,OAAA,GAAAY,QAAA"}