"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.InputControlPrefixWrapper = void 0;
var _react = require("react");
var _spacer = require("../spacer");
var _context = require("../context");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedInputControlPrefixWrapper(props, forwardedRef) {
  const derivedProps = (0, _context.useContextSystem)(props, 'InputControlPrefixWrapper');
  return (0, _react.createElement)(_spacer.Spacer, {
    marginBottom: 0,
    ...derivedProps,
    ref: forwardedRef
  });
}

/**
 * A convenience wrapper for the `prefix` when you want to apply
 * standard padding in accordance with the size variant.
 *
 * ```jsx
 * import {
 *   __experimentalInputControl as InputControl,
 *   __experimentalInputControlPrefixWrapper as InputControlPrefixWrapper,
 * } from '@wordpress/components';
 *
 * <InputControl
 *   prefix={<InputControlPrefixWrapper>@</InputControlPrefixWrapper>}
 * />
 * ```
 */
const InputControlPrefixWrapper = (0, _context.contextConnect)(UnconnectedInputControlPrefixWrapper, 'InputControlPrefixWrapper');
exports.InputControlPrefixWrapper = InputControlPrefixWrapper;
var _default = InputControlPrefixWrapper;
exports.default = _default;
//# sourceMappingURL=input-prefix-wrapper.js.map