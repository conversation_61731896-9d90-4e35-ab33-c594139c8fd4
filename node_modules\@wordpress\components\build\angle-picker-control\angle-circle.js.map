{"version": 3, "names": ["_element", "require", "_compose", "_anglePickerControlStyles", "AngleCircle", "value", "onChange", "props", "angleCircleRef", "useRef", "angleCircleCenter", "previousCursorValue", "setAngleCircleCenter", "current", "rect", "getBoundingClientRect", "x", "width", "y", "height", "changeAngleToPosition", "event", "undefined", "preventDefault", "target", "focus", "centerX", "centerY", "getAngle", "clientX", "clientY", "startDrag", "isDragging", "useDragging", "onDragStart", "onDragMove", "onDragEnd", "useEffect", "document", "body", "style", "cursor", "_react", "createElement", "CircleRoot", "ref", "onMouseDown", "className", "CircleIndicatorWrapper", "transform", "tabIndex", "CircleIndicator", "pointX", "pointY", "angleInRadians", "Math", "atan2", "angleInDeg", "round", "PI", "_default", "exports", "default"], "sources": ["@wordpress/components/src/angle-picker-control/angle-circle.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useEffect, useRef } from '@wordpress/element';\nimport { __experimentalUseDragging as useDragging } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport {\n\tCircleRoot,\n\tCircleIndicatorWrapper,\n\tCircleIndicator,\n} from './styles/angle-picker-control-styles';\n\nimport type { WordPressComponentProps } from '../context';\nimport type { AngleCircleProps } from './types';\n\ntype UseDraggingArgumentType = Parameters< typeof useDragging >[ 0 ];\ntype UseDraggingCallbackEvent =\n\t| Parameters< UseDraggingArgumentType[ 'onDragStart' ] >[ 0 ]\n\t| Parameters< UseDraggingArgumentType[ 'onDragMove' ] >[ 0 ]\n\t| Parameters< UseDraggingArgumentType[ 'onDragEnd' ] >[ 0 ];\n\nfunction AngleCircle( {\n\tvalue,\n\tonChange,\n\t...props\n}: WordPressComponentProps< AngleCircleProps, 'div' > ) {\n\tconst angleCircleRef = useRef< HTMLDivElement | null >( null );\n\tconst angleCircleCenter = useRef< { x: number; y: number } | undefined >();\n\tconst previousCursorValue = useRef< CSSStyleDeclaration[ 'cursor' ] >();\n\n\tconst setAngleCircleCenter = () => {\n\t\tif ( angleCircleRef.current === null ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst rect = angleCircleRef.current.getBoundingClientRect();\n\t\tangleCircleCenter.current = {\n\t\t\tx: rect.x + rect.width / 2,\n\t\t\ty: rect.y + rect.height / 2,\n\t\t};\n\t};\n\n\tconst changeAngleToPosition = ( event: UseDraggingCallbackEvent ) => {\n\t\tif ( event === undefined ) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Prevent (drag) mouse events from selecting and accidentally\n\t\t// triggering actions from other elements.\n\t\tevent.preventDefault();\n\t\t// Input control needs to lose focus and by preventDefault above, it doesn't.\n\t\t( event.target as HTMLDivElement | null )?.focus();\n\n\t\tif (\n\t\t\tangleCircleCenter.current !== undefined &&\n\t\t\tonChange !== undefined\n\t\t) {\n\t\t\tconst { x: centerX, y: centerY } = angleCircleCenter.current;\n\t\t\tonChange(\n\t\t\t\tgetAngle( centerX, centerY, event.clientX, event.clientY )\n\t\t\t);\n\t\t}\n\t};\n\n\tconst { startDrag, isDragging } = useDragging( {\n\t\tonDragStart: ( event ) => {\n\t\t\tsetAngleCircleCenter();\n\t\t\tchangeAngleToPosition( event );\n\t\t},\n\t\tonDragMove: changeAngleToPosition,\n\t\tonDragEnd: changeAngleToPosition,\n\t} );\n\n\tuseEffect( () => {\n\t\tif ( isDragging ) {\n\t\t\tif ( previousCursorValue.current === undefined ) {\n\t\t\t\tpreviousCursorValue.current = document.body.style.cursor;\n\t\t\t}\n\t\t\tdocument.body.style.cursor = 'grabbing';\n\t\t} else {\n\t\t\tdocument.body.style.cursor = previousCursorValue.current || '';\n\t\t\tpreviousCursorValue.current = undefined;\n\t\t}\n\t}, [ isDragging ] );\n\n\treturn (\n\t\t<CircleRoot\n\t\t\tref={ angleCircleRef }\n\t\t\tonMouseDown={ startDrag }\n\t\t\tclassName=\"components-angle-picker-control__angle-circle\"\n\t\t\t{ ...props }\n\t\t>\n\t\t\t<CircleIndicatorWrapper\n\t\t\t\tstyle={\n\t\t\t\t\tvalue ? { transform: `rotate(${ value }deg)` } : undefined\n\t\t\t\t}\n\t\t\t\tclassName=\"components-angle-picker-control__angle-circle-indicator-wrapper\"\n\t\t\t\ttabIndex={ -1 }\n\t\t\t>\n\t\t\t\t<CircleIndicator className=\"components-angle-picker-control__angle-circle-indicator\" />\n\t\t\t</CircleIndicatorWrapper>\n\t\t</CircleRoot>\n\t);\n}\n\nfunction getAngle(\n\tcenterX: number,\n\tcenterY: number,\n\tpointX: number,\n\tpointY: number\n) {\n\tconst y = pointY - centerY;\n\tconst x = pointX - centerX;\n\n\tconst angleInRadians = Math.atan2( y, x );\n\tconst angleInDeg = Math.round( angleInRadians * ( 180 / Math.PI ) ) + 90;\n\tif ( angleInDeg < 0 ) {\n\t\treturn 360 + angleInDeg;\n\t}\n\treturn angleInDeg;\n}\n\nexport default AngleCircle;\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,yBAAA,GAAAF,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AAgBA,SAASG,WAAWA,CAAE;EACrBC,KAAK;EACLC,QAAQ;EACR,GAAGC;AACgD,CAAC,EAAG;EACvD,MAAMC,cAAc,GAAG,IAAAC,eAAM,EAA2B,IAAK,CAAC;EAC9D,MAAMC,iBAAiB,GAAG,IAAAD,eAAM,EAAyC,CAAC;EAC1E,MAAME,mBAAmB,GAAG,IAAAF,eAAM,EAAoC,CAAC;EAEvE,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IAClC,IAAKJ,cAAc,CAACK,OAAO,KAAK,IAAI,EAAG;MACtC;IACD;IAEA,MAAMC,IAAI,GAAGN,cAAc,CAACK,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC3DL,iBAAiB,CAACG,OAAO,GAAG;MAC3BG,CAAC,EAAEF,IAAI,CAACE,CAAC,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC;MAC1BC,CAAC,EAAEJ,IAAI,CAACI,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG;IAC3B,CAAC;EACF,CAAC;EAED,MAAMC,qBAAqB,GAAKC,KAA+B,IAAM;IACpE,IAAKA,KAAK,KAAKC,SAAS,EAAG;MAC1B;IACD;;IAEA;IACA;IACAD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB;IACEF,KAAK,CAACG,MAAM,EAA6BC,KAAK,CAAC,CAAC;IAElD,IACCf,iBAAiB,CAACG,OAAO,KAAKS,SAAS,IACvChB,QAAQ,KAAKgB,SAAS,EACrB;MACD,MAAM;QAAEN,CAAC,EAAEU,OAAO;QAAER,CAAC,EAAES;MAAQ,CAAC,GAAGjB,iBAAiB,CAACG,OAAO;MAC5DP,QAAQ,CACPsB,QAAQ,CAAEF,OAAO,EAAEC,OAAO,EAAEN,KAAK,CAACQ,OAAO,EAAER,KAAK,CAACS,OAAQ,CAC1D,CAAC;IACF;EACD,CAAC;EAED,MAAM;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAG,IAAAC,kCAAW,EAAE;IAC9CC,WAAW,EAAIb,KAAK,IAAM;MACzBT,oBAAoB,CAAC,CAAC;MACtBQ,qBAAqB,CAAEC,KAAM,CAAC;IAC/B,CAAC;IACDc,UAAU,EAAEf,qBAAqB;IACjCgB,SAAS,EAAEhB;EACZ,CAAE,CAAC;EAEH,IAAAiB,kBAAS,EAAE,MAAM;IAChB,IAAKL,UAAU,EAAG;MACjB,IAAKrB,mBAAmB,CAACE,OAAO,KAAKS,SAAS,EAAG;QAChDX,mBAAmB,CAACE,OAAO,GAAGyB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM;MACzD;MACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,UAAU;IACxC,CAAC,MAAM;MACNH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG9B,mBAAmB,CAACE,OAAO,IAAI,EAAE;MAC9DF,mBAAmB,CAACE,OAAO,GAAGS,SAAS;IACxC;EACD,CAAC,EAAE,CAAEU,UAAU,CAAG,CAAC;EAEnB,OACC,IAAAU,MAAA,CAAAC,aAAA,EAACxC,yBAAA,CAAAyC,UAAU;IACVC,GAAG,EAAGrC,cAAgB;IACtBsC,WAAW,EAAGf,SAAW;IACzBgB,SAAS,EAAC,+CAA+C;IAAA,GACpDxC;EAAK,GAEV,IAAAmC,MAAA,CAAAC,aAAA,EAACxC,yBAAA,CAAA6C,sBAAsB;IACtBR,KAAK,EACJnC,KAAK,GAAG;MAAE4C,SAAS,EAAG,UAAU5C,KAAO;IAAM,CAAC,GAAGiB,SACjD;IACDyB,SAAS,EAAC,iEAAiE;IAC3EG,QAAQ,EAAG,CAAC;EAAG,GAEf,IAAAR,MAAA,CAAAC,aAAA,EAACxC,yBAAA,CAAAgD,eAAe;IAACJ,SAAS,EAAC;EAAyD,CAAE,CAC/D,CACb,CAAC;AAEf;AAEA,SAASnB,QAAQA,CAChBF,OAAe,EACfC,OAAe,EACfyB,MAAc,EACdC,MAAc,EACb;EACD,MAAMnC,CAAC,GAAGmC,MAAM,GAAG1B,OAAO;EAC1B,MAAMX,CAAC,GAAGoC,MAAM,GAAG1B,OAAO;EAE1B,MAAM4B,cAAc,GAAGC,IAAI,CAACC,KAAK,CAAEtC,CAAC,EAAEF,CAAE,CAAC;EACzC,MAAMyC,UAAU,GAAGF,IAAI,CAACG,KAAK,CAAEJ,cAAc,IAAK,GAAG,GAAGC,IAAI,CAACI,EAAE,CAAG,CAAC,GAAG,EAAE;EACxE,IAAKF,UAAU,GAAG,CAAC,EAAG;IACrB,OAAO,GAAG,GAAGA,UAAU;EACxB;EACA,OAAOA,UAAU;AAClB;AAAC,IAAAG,QAAA,GAEcxD,WAAW;AAAAyD,OAAA,CAAAC,OAAA,GAAAF,QAAA"}