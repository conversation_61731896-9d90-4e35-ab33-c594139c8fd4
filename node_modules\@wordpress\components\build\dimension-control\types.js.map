{"version": 3, "names": [], "sources": ["@wordpress/components/src/dimension-control/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { IconType } from '../icon';\n\nexport type Size = {\n\t/**\n\t * Human-readable name of the size.\n\t */\n\tname: string;\n\t/**\n\t * Short unique identifying name of the size.\n\t */\n\tslug: string;\n};\n\nexport type DimensionControlProps = {\n\t/**\n\t * Label for the control.\n\t */\n\tlabel: string;\n\t/**\n\t * An array of sizes to choose from.\n\t *\n\t * @default DEFAULT_SIZES\n\t *\n\t * @see packages/components/src/dimension-control/sizes.ts\n\t */\n\tsizes?: Size[];\n\t/**\n\t * Optional icon rendered in front on the label.\n\t */\n\ticon?: IconType;\n\t/**\n\t * Used to externally control the current value of the control.\n\t */\n\tvalue?: string;\n\t/**\n\t * Function called with the control's internal state changes. The `value` property is equal to a given size slug.\n\t */\n\tonChange?: ( value?: string ) => void;\n\t/**\n\t * CSS class applied to `SelectControl`.\n\t *\n\t * @default ''\n\t */\n\tclassName?: string;\n\t/**\n\t * Start opting into the larger default height that will become the default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n};\n"], "mappings": ""}