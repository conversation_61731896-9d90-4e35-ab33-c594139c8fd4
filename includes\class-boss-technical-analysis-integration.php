<?php
/**
 * Intégration du nouveau module d'analyse technique Boss SEO v2.0
 *
 * Cette classe gère l'intégration du nouveau module d'analyse technique
 * et remplace l'ancien système avec données fictives.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Intégration du module d'analyse technique v2.0
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Technical_Analysis_Integration {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance de l'analyseur technique v2.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_Technical_Analyzer_V2    $analyzer    Instance de l'analyseur.
     */
    private $analyzer;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        // Charger les dépendances
        $this->load_dependencies();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.1.0
     */
    private function load_dependencies() {
        // Charger l'analyseur technique v2
        if ( ! class_exists( 'Boss_Technical_Analyzer_V2' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-technical-analyzer-v2.php';
        }
        
        $this->analyzer = new Boss_Technical_Analyzer_V2( $this->plugin_name, $this->version );
    }

    /**
     * Enregistre les hooks pour cette classe.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Enregistrer les hooks de l'analyseur
        $this->analyzer->register_hooks();
        
        // Hook pour remplacer l'ancien module
        add_action( 'admin_init', array( $this, 'replace_old_technical_module' ) );
        
        // Hook pour enregistrer les scripts et styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_assets' ) );
        
        // Hook pour ajouter les données de configuration JavaScript
        add_action( 'admin_footer', array( $this, 'add_admin_js_config' ) );
    }

    /**
     * Remplace l'ancien module d'analyse technique.
     *
     * @since    1.1.0
     */
    public function replace_old_technical_module() {
        // Vérifier si nous sommes sur la page d'analyse technique
        if ( ! $this->is_technical_analysis_page() ) {
            return;
        }

        // Désactiver l'ancien module s'il existe
        $this->disable_old_technical_module();
        
        // Activer le nouveau module
        $this->enable_new_technical_module();
    }

    /**
     * Vérifie si nous sommes sur la page d'analyse technique.
     *
     * @since    1.1.0
     * @return   bool    True si nous sommes sur la page d'analyse technique.
     */
    private function is_technical_analysis_page() {
        global $pagenow;
        
        return (
            $pagenow === 'admin.php' &&
            isset( $_GET['page'] ) &&
            $_GET['page'] === 'boss-seo-technical'
        );
    }

    /**
     * Désactive l'ancien module d'analyse technique.
     *
     * @since    1.1.0
     */
    private function disable_old_technical_module() {
        // Supprimer les hooks de l'ancien module
        remove_all_actions( 'boss_seo_technical_analysis_content' );
        
        // Désactiver les scripts de l'ancien module
        wp_dequeue_script( 'boss-seo-technical-analysis' );
        wp_dequeue_style( 'boss-seo-technical-analysis' );
        
        // Log de la désactivation
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Boss SEO: Ancien module d\'analyse technique désactivé' );
        }
    }

    /**
     * Active le nouveau module d'analyse technique.
     *
     * @since    1.1.0
     */
    private function enable_new_technical_module() {
        // Ajouter le hook pour le nouveau contenu
        add_action( 'boss_seo_technical_analysis_content', array( $this, 'render_new_technical_module' ) );
        
        // Log de l'activation
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Boss SEO: Nouveau module d\'analyse technique v2.0 activé' );
        }
    }

    /**
     * Rend le nouveau module d'analyse technique.
     *
     * @since    1.1.0
     */
    public function render_new_technical_module() {
        ?>
        <div class="wrap">
            <div id="boss-seo-technical-analysis-v2-app"></div>
            <noscript>
                <div class="notice notice-error">
                    <p><?php _e( 'JavaScript est requis pour utiliser le module d\'analyse technique Boss SEO.', 'boss-seo' ); ?></p>
                </div>
            </noscript>
        </div>
        <?php
    }

    /**
     * Enregistre les assets admin pour le nouveau module.
     *
     * @since    1.1.0
     * @param    string    $hook    Hook de la page admin.
     */
    public function enqueue_admin_assets( $hook ) {
        // Vérifier si nous sommes sur la bonne page
        if ( ! $this->is_technical_analysis_page() ) {
            return;
        }

        // Enregistrer et charger le script React du nouveau module
        wp_enqueue_script(
            'boss-seo-technical-analysis-v2',
            plugin_dir_url( dirname( __FILE__ ) ) . 'admin/js/technical-analysis-v2.js',
            array( 'wp-element', 'wp-components', 'wp-i18n', 'wp-api-fetch' ),
            $this->version,
            true
        );

        // Enregistrer et charger les styles du nouveau module
        wp_enqueue_style(
            'boss-seo-technical-analysis-v2',
            plugin_dir_url( dirname( __FILE__ ) ) . 'admin/css/technical-analysis-v2.css',
            array( 'wp-components' ),
            $this->version
        );

        // Localiser le script avec les données nécessaires
        wp_localize_script(
            'boss-seo-technical-analysis-v2',
            'bossSeoTechnicalV2',
            array(
                'apiUrl'       => rest_url( 'boss-seo/v2/' ),
                'nonce'        => wp_create_nonce( 'wp_rest' ),
                'currentUser'  => wp_get_current_user(),
                'siteUrl'      => home_url(),
                'pluginUrl'    => plugin_dir_url( dirname( __FILE__ ) ),
                'version'      => $this->version,
                'isConfigured' => $this->check_api_configuration(),
                'strings'      => array(
                    'analyzing'           => __( 'Analyse en cours...', 'boss-seo' ),
                    'analyzeComplete'     => __( 'Analyse terminée', 'boss-seo' ),
                    'analyzeError'        => __( 'Erreur lors de l\'analyse', 'boss-seo' ),
                    'selectPage'          => __( 'Sélectionnez une page', 'boss-seo' ),
                    'noResults'           => __( 'Aucun résultat disponible', 'boss-seo' ),
                    'configureApi'        => __( 'Configurer l\'API', 'boss-seo' ),
                    'settingsUrl'         => admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ),
                ),
            )
        );
    }

    /**
     * Ajoute la configuration JavaScript pour le nouveau module.
     *
     * @since    1.1.0
     */
    public function add_admin_js_config() {
        if ( ! $this->is_technical_analysis_page() ) {
            return;
        }

        ?>
        <script type="text/javascript">
        // Configuration globale pour le module d'analyse technique v2
        window.bossSeoTechnicalV2Config = {
            moduleVersion: '2.0',
            apiEndpoints: {
                pages: '/boss-seo/v2/technical/pages',
                analyze: '/boss-seo/v2/technical/analyze',
                history: '/boss-seo/v2/technical/history',
                suggestions: '/boss-seo/v2/technical/ai-suggestions'
            },
            features: {
                realTimeAnalysis: true,
                aiSuggestions: <?php echo $this->is_ai_configured() ? 'true' : 'false'; ?>,
                pageSelection: true,
                historyTracking: true
            },
            limits: {
                maxPagesPerAnalysis: 1,
                analysisTimeout: 60000,
                cacheTimeout: 3600000
            }
        };

        // Initialiser le module quand le DOM est prêt
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof window.initBossSeoTechnicalV2 === 'function') {
                window.initBossSeoTechnicalV2();
            }
        });
        </script>
        <?php
    }

    /**
     * Vérifie si l'API PageSpeed est configurée.
     *
     * @since    1.1.0
     * @return   bool    True si l'API est configurée.
     */
    private function check_api_configuration() {
        // Vérifier la configuration de l'API PageSpeed
        $external_services = get_option( 'boss_optimizer_external_services', array() );
        $api_key = isset( $external_services['google_pagespeed']['api_key'] ) ? $external_services['google_pagespeed']['api_key'] : '';
        
        // Fallback vers l'ancienne structure
        if ( empty( $api_key ) ) {
            $api_key = get_option( 'boss_optimizer_pagespeed_api_key', '' );
        }
        
        return ! empty( $api_key );
    }

    /**
     * Vérifie si l'IA est configurée.
     *
     * @since    1.1.0
     * @return   bool    True si l'IA est configurée.
     */
    private function is_ai_configured() {
        // Vérifier la configuration de l'IA
        $ai_settings = get_option( 'boss_seo_ai_settings', array() );
        
        // Vérifier si au moins un service IA est configuré
        $configured_services = array();
        
        if ( ! empty( $ai_settings['openai_api_key'] ) ) {
            $configured_services[] = 'openai';
        }
        
        if ( ! empty( $ai_settings['claude_api_key'] ) ) {
            $configured_services[] = 'claude';
        }
        
        if ( ! empty( $ai_settings['gemini_api_key'] ) ) {
            $configured_services[] = 'gemini';
        }
        
        return ! empty( $configured_services );
    }

    /**
     * Ajoute une notice d'information sur le nouveau module.
     *
     * @since    1.1.0
     */
    public function add_upgrade_notice() {
        if ( ! $this->is_technical_analysis_page() ) {
            return;
        }

        ?>
        <div class="notice notice-info is-dismissible">
            <p>
                <strong><?php _e( 'Nouveau !', 'boss-seo' ); ?></strong>
                <?php _e( 'Le module d\'analyse technique a été complètement reconstruit avec l\'intégration réelle de Google PageSpeed Insights et des suggestions IA intelligentes.', 'boss-seo' ); ?>
            </p>
            <?php if ( ! $this->check_api_configuration() ) : ?>
                <p>
                    <a href="<?php echo admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ); ?>" class="button button-primary">
                        <?php _e( 'Configurer l\'API PageSpeed', 'boss-seo' ); ?>
                    </a>
                </p>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Nettoie les données de l'ancien module.
     *
     * @since    1.1.0
     */
    public function cleanup_old_module_data() {
        // Supprimer les transients de l'ancien module
        global $wpdb;
        
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_boss_technical_analysis_%' 
             OR option_name LIKE '_transient_timeout_boss_technical_analysis_%'"
        );
        
        // Supprimer les options obsolètes
        delete_option( 'boss_seo_technical_analysis_mock_data' );
        delete_option( 'boss_seo_technical_analysis_version' );
        
        // Log du nettoyage
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Boss SEO: Données de l\'ancien module d\'analyse technique nettoyées' );
        }
    }

    /**
     * Vérifie la compatibilité du système.
     *
     * @since    1.1.0
     * @return   array    Résultats de la vérification de compatibilité.
     */
    public function check_system_compatibility() {
        $compatibility = array(
            'php_version'     => version_compare( PHP_VERSION, '7.4', '>=' ),
            'wordpress_version' => version_compare( get_bloginfo( 'version' ), '5.0', '>=' ),
            'curl_available'  => function_exists( 'curl_init' ),
            'json_available'  => function_exists( 'json_decode' ),
            'dom_available'   => class_exists( 'DOMDocument' ),
            'api_configured'  => $this->check_api_configuration(),
            'ai_configured'   => $this->is_ai_configured(),
        );
        
        $compatibility['overall'] = ! in_array( false, array_slice( $compatibility, 0, 5 ) );
        
        return $compatibility;
    }

    /**
     * Génère un rapport de statut du module.
     *
     * @since    1.1.0
     * @return   array    Rapport de statut.
     */
    public function get_module_status() {
        $compatibility = $this->check_system_compatibility();
        
        return array(
            'version'        => '2.0',
            'status'         => $compatibility['overall'] ? 'active' : 'incompatible',
            'compatibility' => $compatibility,
            'features'       => array(
                'real_pagespeed_api' => $compatibility['api_configured'],
                'ai_suggestions'     => $compatibility['ai_configured'],
                'page_selection'     => true,
                'history_tracking'   => true,
            ),
            'last_check'     => current_time( 'mysql' ),
        );
    }
}
