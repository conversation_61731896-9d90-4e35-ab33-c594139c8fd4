{"version": 3, "names": ["_domScrollIntoView", "_interopRequireDefault", "require", "_classnames", "_element", "_compose", "handleMouseDown", "e", "preventDefault", "SuggestionsList", "selectedIndex", "scrollIntoView", "match", "onHover", "onSelect", "suggestions", "displayTransform", "instanceId", "__experimentalRenderItem", "scrollingIntoView", "setScrollingIntoView", "useState", "listRef", "useRefEffect", "listNode", "rafId", "children", "scrollView", "onlyScrollIfNeeded", "requestAnimationFrame", "undefined", "cancelAnimationFrame", "handleHover", "suggestion", "handleClick", "computeSuggestionMatch", "matchText", "toLocaleLowerCase", "length", "transformedSuggestion", "indexOfMatch", "indexOf", "suggestionBeforeMatch", "substring", "suggestionMatch", "suggestionAfterMatch", "_react", "createElement", "ref", "className", "id", "role", "map", "index", "classnames", "output", "item", "key", "value", "onMouseDown", "onClick", "onMouseEnter", "_default", "exports", "default"], "sources": ["@wordpress/components/src/form-token-field/suggestions-list.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport scrollView from 'dom-scroll-into-view';\nimport classnames from 'classnames';\nimport type { MouseEventHandler, ReactNode } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useState } from '@wordpress/element';\nimport { useRefEffect } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport type { SuggestionsListProps } from './types';\n\nconst handleMouseDown: MouseEventHandler = ( e ) => {\n\t// By preventing default here, we will not lose focus of <input> when clicking a suggestion.\n\te.preventDefault();\n};\n\nexport function SuggestionsList< T extends string | { value: string } >( {\n\tselectedIndex,\n\tscrollIntoView,\n\tmatch,\n\tonHover,\n\tonSelect,\n\tsuggestions = [],\n\tdisplayTransform,\n\tinstanceId,\n\t__experimentalRenderItem,\n}: SuggestionsListProps< T > ) {\n\tconst [ scrollingIntoView, setScrollingIntoView ] = useState( false );\n\n\tconst listRef = useRefEffect< HTMLUListElement >(\n\t\t( listNode ) => {\n\t\t\t// only have to worry about scrolling selected suggestion into view\n\t\t\t// when already expanded.\n\t\t\tlet rafId: number | undefined;\n\t\t\tif (\n\t\t\t\tselectedIndex > -1 &&\n\t\t\t\tscrollIntoView &&\n\t\t\t\tlistNode.children[ selectedIndex ]\n\t\t\t) {\n\t\t\t\tsetScrollingIntoView( true );\n\t\t\t\tscrollView(\n\t\t\t\t\tlistNode.children[ selectedIndex ] as HTMLLIElement,\n\t\t\t\t\tlistNode,\n\t\t\t\t\t{\n\t\t\t\t\t\tonlyScrollIfNeeded: true,\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t\trafId = requestAnimationFrame( () => {\n\t\t\t\t\tsetScrollingIntoView( false );\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\treturn () => {\n\t\t\t\tif ( rafId !== undefined ) {\n\t\t\t\t\tcancelAnimationFrame( rafId );\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\t[ selectedIndex, scrollIntoView ]\n\t);\n\n\tconst handleHover = ( suggestion: T ) => {\n\t\treturn () => {\n\t\t\tif ( ! scrollingIntoView ) {\n\t\t\t\tonHover?.( suggestion );\n\t\t\t}\n\t\t};\n\t};\n\n\tconst handleClick = ( suggestion: T ) => {\n\t\treturn () => {\n\t\t\tonSelect?.( suggestion );\n\t\t};\n\t};\n\n\tconst computeSuggestionMatch = ( suggestion: T ) => {\n\t\tconst matchText = displayTransform( match ).toLocaleLowerCase();\n\t\tif ( matchText.length === 0 ) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconst transformedSuggestion = displayTransform( suggestion );\n\t\tconst indexOfMatch = transformedSuggestion\n\t\t\t.toLocaleLowerCase()\n\t\t\t.indexOf( matchText );\n\n\t\treturn {\n\t\t\tsuggestionBeforeMatch: transformedSuggestion.substring(\n\t\t\t\t0,\n\t\t\t\tindexOfMatch\n\t\t\t),\n\t\t\tsuggestionMatch: transformedSuggestion.substring(\n\t\t\t\tindexOfMatch,\n\t\t\t\tindexOfMatch + matchText.length\n\t\t\t),\n\t\t\tsuggestionAfterMatch: transformedSuggestion.substring(\n\t\t\t\tindexOfMatch + matchText.length\n\t\t\t),\n\t\t};\n\t};\n\n\treturn (\n\t\t<ul\n\t\t\tref={ listRef }\n\t\t\tclassName=\"components-form-token-field__suggestions-list\"\n\t\t\tid={ `components-form-token-suggestions-${ instanceId }` }\n\t\t\trole=\"listbox\"\n\t\t>\n\t\t\t{ suggestions.map( ( suggestion, index ) => {\n\t\t\t\tconst matchText = computeSuggestionMatch( suggestion );\n\t\t\t\tconst className = classnames(\n\t\t\t\t\t'components-form-token-field__suggestion',\n\t\t\t\t\t{\n\t\t\t\t\t\t'is-selected': index === selectedIndex,\n\t\t\t\t\t}\n\t\t\t\t);\n\n\t\t\t\tlet output: ReactNode;\n\n\t\t\t\tif ( typeof __experimentalRenderItem === 'function' ) {\n\t\t\t\t\toutput = __experimentalRenderItem( { item: suggestion } );\n\t\t\t\t} else if ( matchText ) {\n\t\t\t\t\toutput = (\n\t\t\t\t\t\t<span aria-label={ displayTransform( suggestion ) }>\n\t\t\t\t\t\t\t{ matchText.suggestionBeforeMatch }\n\t\t\t\t\t\t\t<strong className=\"components-form-token-field__suggestion-match\">\n\t\t\t\t\t\t\t\t{ matchText.suggestionMatch }\n\t\t\t\t\t\t\t</strong>\n\t\t\t\t\t\t\t{ matchText.suggestionAfterMatch }\n\t\t\t\t\t\t</span>\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\toutput = displayTransform( suggestion );\n\t\t\t\t}\n\n\t\t\t\t/* eslint-disable jsx-a11y/click-events-have-key-events */\n\t\t\t\treturn (\n\t\t\t\t\t<li\n\t\t\t\t\t\tid={ `components-form-token-suggestions-${ instanceId }-${ index }` }\n\t\t\t\t\t\trole=\"option\"\n\t\t\t\t\t\tclassName={ className }\n\t\t\t\t\t\tkey={\n\t\t\t\t\t\t\ttypeof suggestion === 'object' &&\n\t\t\t\t\t\t\t'value' in suggestion\n\t\t\t\t\t\t\t\t? suggestion?.value\n\t\t\t\t\t\t\t\t: displayTransform( suggestion )\n\t\t\t\t\t\t}\n\t\t\t\t\t\tonMouseDown={ handleMouseDown }\n\t\t\t\t\t\tonClick={ handleClick( suggestion ) }\n\t\t\t\t\t\tonMouseEnter={ handleHover( suggestion ) }\n\t\t\t\t\t\taria-selected={ index === selectedIndex }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ output }\n\t\t\t\t\t</li>\n\t\t\t\t);\n\t\t\t\t/* eslint-enable jsx-a11y/click-events-have-key-events */\n\t\t\t} ) }\n\t\t</ul>\n\t);\n}\n\nexport default SuggestionsList;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,kBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AAMA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAXA;AACA;AACA;;AAKA;AACA;AACA;;AASA,MAAMI,eAAkC,GAAKC,CAAC,IAAM;EACnD;EACAA,CAAC,CAACC,cAAc,CAAC,CAAC;AACnB,CAAC;AAEM,SAASC,eAAeA,CAA0C;EACxEC,aAAa;EACbC,cAAc;EACdC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,WAAW,GAAG,EAAE;EAChBC,gBAAgB;EAChBC,UAAU;EACVC;AAC0B,CAAC,EAAG;EAC9B,MAAM,CAAEC,iBAAiB,EAAEC,oBAAoB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAErE,MAAMC,OAAO,GAAG,IAAAC,qBAAY,EACzBC,QAAQ,IAAM;IACf;IACA;IACA,IAAIC,KAAyB;IAC7B,IACCf,aAAa,GAAG,CAAC,CAAC,IAClBC,cAAc,IACda,QAAQ,CAACE,QAAQ,CAAEhB,aAAa,CAAE,EACjC;MACDU,oBAAoB,CAAE,IAAK,CAAC;MAC5B,IAAAO,0BAAU,EACTH,QAAQ,CAACE,QAAQ,CAAEhB,aAAa,CAAE,EAClCc,QAAQ,EACR;QACCI,kBAAkB,EAAE;MACrB,CACD,CAAC;MACDH,KAAK,GAAGI,qBAAqB,CAAE,MAAM;QACpCT,oBAAoB,CAAE,KAAM,CAAC;MAC9B,CAAE,CAAC;IACJ;IAEA,OAAO,MAAM;MACZ,IAAKK,KAAK,KAAKK,SAAS,EAAG;QAC1BC,oBAAoB,CAAEN,KAAM,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EACD,CAAEf,aAAa,EAAEC,cAAc,CAChC,CAAC;EAED,MAAMqB,WAAW,GAAKC,UAAa,IAAM;IACxC,OAAO,MAAM;MACZ,IAAK,CAAEd,iBAAiB,EAAG;QAC1BN,OAAO,GAAIoB,UAAW,CAAC;MACxB;IACD,CAAC;EACF,CAAC;EAED,MAAMC,WAAW,GAAKD,UAAa,IAAM;IACxC,OAAO,MAAM;MACZnB,QAAQ,GAAImB,UAAW,CAAC;IACzB,CAAC;EACF,CAAC;EAED,MAAME,sBAAsB,GAAKF,UAAa,IAAM;IACnD,MAAMG,SAAS,GAAGpB,gBAAgB,CAAEJ,KAAM,CAAC,CAACyB,iBAAiB,CAAC,CAAC;IAC/D,IAAKD,SAAS,CAACE,MAAM,KAAK,CAAC,EAAG;MAC7B,OAAO,IAAI;IACZ;IAEA,MAAMC,qBAAqB,GAAGvB,gBAAgB,CAAEiB,UAAW,CAAC;IAC5D,MAAMO,YAAY,GAAGD,qBAAqB,CACxCF,iBAAiB,CAAC,CAAC,CACnBI,OAAO,CAAEL,SAAU,CAAC;IAEtB,OAAO;MACNM,qBAAqB,EAAEH,qBAAqB,CAACI,SAAS,CACrD,CAAC,EACDH,YACD,CAAC;MACDI,eAAe,EAAEL,qBAAqB,CAACI,SAAS,CAC/CH,YAAY,EACZA,YAAY,GAAGJ,SAAS,CAACE,MAC1B,CAAC;MACDO,oBAAoB,EAAEN,qBAAqB,CAACI,SAAS,CACpDH,YAAY,GAAGJ,SAAS,CAACE,MAC1B;IACD,CAAC;EACF,CAAC;EAED,OACC,IAAAQ,MAAA,CAAAC,aAAA;IACCC,GAAG,EAAG1B,OAAS;IACf2B,SAAS,EAAC,+CAA+C;IACzDC,EAAE,EAAI,qCAAqCjC,UAAY,EAAG;IAC1DkC,IAAI,EAAC;EAAS,GAEZpC,WAAW,CAACqC,GAAG,CAAE,CAAEnB,UAAU,EAAEoB,KAAK,KAAM;IAC3C,MAAMjB,SAAS,GAAGD,sBAAsB,CAAEF,UAAW,CAAC;IACtD,MAAMgB,SAAS,GAAG,IAAAK,mBAAU,EAC3B,yCAAyC,EACzC;MACC,aAAa,EAAED,KAAK,KAAK3C;IAC1B,CACD,CAAC;IAED,IAAI6C,MAAiB;IAErB,IAAK,OAAOrC,wBAAwB,KAAK,UAAU,EAAG;MACrDqC,MAAM,GAAGrC,wBAAwB,CAAE;QAAEsC,IAAI,EAAEvB;MAAW,CAAE,CAAC;IAC1D,CAAC,MAAM,IAAKG,SAAS,EAAG;MACvBmB,MAAM,GACL,IAAAT,MAAA,CAAAC,aAAA;QAAM,cAAa/B,gBAAgB,CAAEiB,UAAW;MAAG,GAChDG,SAAS,CAACM,qBAAqB,EACjC,IAAAI,MAAA,CAAAC,aAAA;QAAQE,SAAS,EAAC;MAA+C,GAC9Db,SAAS,CAACQ,eACL,CAAC,EACPR,SAAS,CAACS,oBACP,CACN;IACF,CAAC,MAAM;MACNU,MAAM,GAAGvC,gBAAgB,CAAEiB,UAAW,CAAC;IACxC;;IAEA;IACA,OACC,IAAAa,MAAA,CAAAC,aAAA;MACCG,EAAE,EAAI,qCAAqCjC,UAAY,IAAIoC,KAAO,EAAG;MACrEF,IAAI,EAAC,QAAQ;MACbF,SAAS,EAAGA,SAAW;MACvBQ,GAAG,EACF,OAAOxB,UAAU,KAAK,QAAQ,IAC9B,OAAO,IAAIA,UAAU,GAClBA,UAAU,EAAEyB,KAAK,GACjB1C,gBAAgB,CAAEiB,UAAW,CAChC;MACD0B,WAAW,EAAGrD,eAAiB;MAC/BsD,OAAO,EAAG1B,WAAW,CAAED,UAAW,CAAG;MACrC4B,YAAY,EAAG7B,WAAW,CAAEC,UAAW,CAAG;MAC1C,iBAAgBoB,KAAK,KAAK3C;IAAe,GAEvC6C,MACC,CAAC;IAEN;EACD,CAAE,CACC,CAAC;AAEP;AAAC,IAAAO,QAAA,GAEcrD,eAAe;AAAAsD,OAAA,CAAAC,OAAA,GAAAF,QAAA"}