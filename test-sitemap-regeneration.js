/**
 * Script de test pour la régénération des sitemaps
 * À exécuter dans la console du navigateur sur la page d'admin WordPress
 */

console.log('🧪 Test de régénération des sitemaps Boss SEO');
console.log('===============================================');

// Fonction pour tester une route API
async function testApiRoute(url, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': wpApiSettings.nonce
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        console.log(`📡 Test ${method} ${url}`);
        const response = await fetch(url, options);
        
        console.log(`📊 Status: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Succès:', result);
            return { success: true, data: result };
        } else {
            const error = await response.text();
            console.log('❌ Erreur:', error);
            return { success: false, error: error };
        }
    } catch (error) {
        console.log('💥 Exception:', error);
        return { success: false, error: error.message };
    }
}

// Tests séquentiels
async function runTests() {
    console.log('\n🔍 1. Test récupération paramètres sitemap...');
    await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings');
    
    console.log('\n🔍 2. Test récupération robots.txt...');
    await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/robots');
    
    console.log('\n🔍 3. Test types de contenu...');
    await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/content-types');
    
    console.log('\n🔍 4. Test taxonomies...');
    await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/taxonomies');
    
    console.log('\n🔍 5. Test historique génération...');
    await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/history');
    
    console.log('\n🚀 6. Test RÉGÉNÉRATION SITEMAP (le test principal)...');
    const regenResult = await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate', 'POST');
    
    if (regenResult.success) {
        console.log('🎉 SUCCÈS! La régénération fonctionne maintenant!');
        
        // Tester les URLs de sitemap après régénération
        console.log('\n🔍 7. Test des URLs de sitemap...');
        const siteUrl = window.location.origin;
        const sitemapUrls = [
            `${siteUrl}/sitemap.xml`,
            `${siteUrl}/sitemap-post.xml`,
            `${siteUrl}/sitemap-page.xml`
        ];
        
        for (const url of sitemapUrls) {
            try {
                console.log(`🌐 Test ${url}`);
                const response = await fetch(url);
                if (response.ok) {
                    const text = await response.text();
                    if (text.includes('<?xml') && text.includes('</urlset>') || text.includes('</sitemapindex>')) {
                        console.log(`✅ ${url} - XML valide`);
                    } else {
                        console.log(`⚠️ ${url} - Contenu suspect:`, text.substring(0, 200));
                    }
                } else {
                    console.log(`❌ ${url} - Status: ${response.status}`);
                }
            } catch (error) {
                console.log(`💥 ${url} - Erreur: ${error.message}`);
            }
        }
    } else {
        console.log('💔 ÉCHEC de la régénération. Vérifiez les logs PHP.');
    }
    
    console.log('\n📋 RÉSUMÉ DES TESTS');
    console.log('==================');
    console.log('Si tous les tests passent, le problème est résolu!');
    console.log('Si la régénération échoue encore, vérifiez:');
    console.log('- Les logs d\'erreur PHP');
    console.log('- Les permissions de fichiers');
    console.log('- La configuration du serveur');
}

// Fonction pour tester la sauvegarde
async function testSave() {
    console.log('\n💾 Test de sauvegarde robots.txt...');
    const testContent = `User-agent: *
Disallow: /wp-admin/
Allow: /wp-admin/admin-ajax.php
Disallow: /wp-includes/
Disallow: /wp-content/plugins/

Sitemap: ${window.location.origin}/sitemap.xml`;

    const saveResult = await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/robots', 'POST', {
        content: testContent
    });
    
    if (saveResult.success) {
        console.log('✅ Sauvegarde robots.txt réussie');
        
        // Vérifier que le contenu a été sauvegardé
        const getResult = await testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/robots');
        if (getResult.success && getResult.data.content === testContent) {
            console.log('✅ Contenu robots.txt vérifié');
        } else {
            console.log('⚠️ Contenu robots.txt différent après sauvegarde');
        }
    } else {
        console.log('❌ Échec sauvegarde robots.txt');
    }
}

// Lancer les tests
console.log('🚀 Lancement des tests...\n');
runTests().then(() => {
    console.log('\n🔧 Test de sauvegarde...');
    return testSave();
}).then(() => {
    console.log('\n✅ Tous les tests terminés!');
    console.log('Vérifiez les résultats ci-dessus pour identifier les problèmes restants.');
}).catch(error => {
    console.error('💥 Erreur lors des tests:', error);
});

// Fonction utilitaire pour tester manuellement
window.testBossSeoApi = {
    regenerate: () => testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate', 'POST'),
    getRobots: () => testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/robots'),
    saveRobots: (content) => testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/robots', 'POST', { content }),
    getSettings: () => testApiRoute('/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings'),
    runAllTests: runTests
};

console.log('\n🛠️ Fonctions utilitaires disponibles:');
console.log('- testBossSeoApi.regenerate() - Tester la régénération');
console.log('- testBossSeoApi.getRobots() - Récupérer robots.txt');
console.log('- testBossSeoApi.saveRobots(content) - Sauvegarder robots.txt');
console.log('- testBossSeoApi.getSettings() - Récupérer paramètres');
console.log('- testBossSeoApi.runAllTests() - Relancer tous les tests');
