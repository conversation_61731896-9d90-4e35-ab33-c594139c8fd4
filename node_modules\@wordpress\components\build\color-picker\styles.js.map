{"version": 3, "names": ["_numberControl", "_interopRequireDefault", "require", "_selectControl", "_rangeControl", "_space", "_utils", "_button", "_flex", "_hStack", "_inputControlStyles", "_configValues", "NumberControlWrapper", "_base", "default", "NumberControl", "process", "env", "NODE_ENV", "target", "label", "InputControlContainer", "space", "exports", "SelectControl", "InnerSelectControl", "BackdropUI", "RangeControl", "InnerRangeControl", "interactiveHueStyles", "AuxiliaryColorArtefactWrapper", "AuxiliaryColorArtefactHStackHeader", "HStack", "ColorInputWrapper", "Flex", "ColorfulWrapper", "boxSizingReset", "CONFIG", "borderWidthFocus", "Copy<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "sources": ["@wordpress/components/src/color-picker/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport NumberControl from '../number-control';\nimport InnerSelectControl from '../select-control';\nimport InnerRangeControl from '../range-control';\nimport { space } from '../utils/space';\nimport { boxSizingReset } from '../utils';\nimport Button from '../button';\nimport { Flex } from '../flex';\nimport { HStack } from '../h-stack';\nimport {\n\tBackdropUI,\n\tContainer as InputControlContainer,\n} from '../input-control/styles/input-control-styles';\nimport CONFIG from '../utils/config-values';\n\nexport const NumberControlWrapper = styled( NumberControl )`\n\t${ InputControlContainer } {\n\t\twidth: ${ space( 24 ) };\n\t}\n`;\n\nexport const SelectControl = styled( InnerSelectControl )`\n\tmargin-left: ${ space( -2 ) };\n\twidth: 5em;\n\t/*\n\t * Remove border, but preserve focus styles\n\t * TODO: this override should be removed,\n\t * see https://github.com/WordPress/gutenberg/pull/50609\n\t */\n\tselect:not( :focus ) ~ ${ BackdropUI }${ BackdropUI }${ BackdropUI } {\n\t\tborder-color: transparent;\n\t}\n`;\n\nexport const RangeControl = styled( InnerRangeControl )`\n\tflex: 1;\n\tmargin-right: ${ space( 2 ) };\n`;\n\n// Make the Hue circle picker not go out of the bar.\nconst interactiveHueStyles = `\n.react-colorful__interactive {\n\twidth: calc( 100% - ${ space( 2 ) } );\n\tmargin-left: ${ space( 1 ) };\n}`;\n\nexport const AuxiliaryColorArtefactWrapper = styled.div`\n\tpadding-top: ${ space( 2 ) };\n\tpadding-right: 0;\n\tpadding-left: 0;\n\tpadding-bottom: 0;\n`;\n\nexport const AuxiliaryColorArtefactHStackHeader = styled( HStack )`\n\tpadding-left: ${ space( 4 ) };\n\tpadding-right: ${ space( 4 ) };\n`;\n\nexport const ColorInputWrapper = styled( Flex )`\n\tpadding-top: ${ space( 4 ) };\n\tpadding-left: ${ space( 4 ) };\n\tpadding-right: ${ space( 3 ) };\n\tpadding-bottom: ${ space( 5 ) };\n`;\n\nexport const ColorfulWrapper = styled.div`\n\t${ boxSizingReset };\n\n\twidth: 216px;\n\n\t.react-colorful {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\twidth: 216px;\n\t\theight: auto;\n\t}\n\n\t.react-colorful__saturation {\n\t\twidth: 100%;\n\t\tborder-radius: 0;\n\t\theight: 216px;\n\t\tmargin-bottom: ${ space( 4 ) };\n\t\tborder-bottom: none;\n\t}\n\n\t.react-colorful__hue,\n\t.react-colorful__alpha {\n\t\twidth: 184px;\n\t\theight: 16px;\n\t\tborder-radius: 16px;\n\t\tmargin-bottom: ${ space( 2 ) };\n\t}\n\n\t.react-colorful__pointer {\n\t\theight: 16px;\n\t\twidth: 16px;\n\t\tborder: none;\n\t\tbox-shadow: 0 0 2px 0 rgba( 0, 0, 0, 0.25 );\n\n\t\t// Shown instead of box-shadow to Windows high contrast mode.\n\t\toutline: 2px solid transparent;\n\t}\n\n\t.react-colorful__pointer-fill {\n\t\tbox-shadow: inset 0 0 0 ${ CONFIG.borderWidthFocus } #fff;\n\t}\n\n\t${ interactiveHueStyles }\n`;\n\nexport const CopyButton = styled( Button )`\n\t&&&&& {\n\t\tmin-width: ${ space( 6 ) };\n\t\tpadding: 0;\n\n\t\t> svg {\n\t\t\tmargin-right: 0;\n\t\t}\n\t}\n`;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AACA,IAAAQ,mBAAA,GAAAR,OAAA;AAIA,IAAAS,aAAA,GAAAV,sBAAA,CAAAC,OAAA;AApBA;AACA;AACA;;AAGA;AACA;AACA;;AAeO,MAAMU,oBAAoB,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,sBAAa,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CACvDC,6BAAqB,aACb,IAAAC,YAAK,EAAE,EAAG,CAAC,UAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IAEtB;AAACK,OAAA,CAAAX,oBAAA,GAAAA,oBAAA;AAEK,MAAMY,aAAa,GAAG,kBAAAX,KAAA,CAAAC,OAAA,EAAQW,sBAAkB,EAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,iBACxC,IAAAE,YAAK,EAAE,CAAC,CAAE,CAAC,sCAODI,8BAAU,EAAKA,8BAAU,EAAKA,8BAAU,mCAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IAGlE;AAACK,OAAA,CAAAC,aAAA,GAAAA,aAAA;AAEK,MAAMG,YAAY,GAAG,kBAAAd,KAAA,CAAAC,OAAA,EAAQc,qBAAiB,EAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,yBAErC,IAAAE,YAAK,EAAE,CAAE,CAAC,SAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IAC3B;;AAED;AAAAK,OAAA,CAAAI,YAAA,GAAAA,YAAA;AACA,MAAME,oBAAoB,GAAI;AAC9B;AACA,uBAAwB,IAAAP,YAAK,EAAE,CAAE,CAAG;AACpC,gBAAiB,IAAAA,YAAK,EAAE,CAAE,CAAG;AAC7B,EAAE;AAEK,MAAMQ,6BAA6B,OAAAjB,KAAA,CAAAC,OAAA,SAAAE,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,mBACzB,IAAAE,YAAK,EAAE,CAAE,CAAC,yDAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IAI1B;AAACK,OAAA,CAAAO,6BAAA,GAAAA,6BAAA;AAEK,MAAMC,kCAAkC,GAAG,kBAAAlB,KAAA,CAAAC,OAAA,EAAQkB,cAAM,EAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,kBAChD,IAAAE,YAAK,EAAE,CAAE,CAAC,qBACT,IAAAA,YAAK,EAAE,CAAE,CAAC,SAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IAC5B;AAACK,OAAA,CAAAQ,kCAAA,GAAAA,kCAAA;AAEK,MAAME,iBAAiB,GAAG,kBAAApB,KAAA,CAAAC,OAAA,EAAQoB,UAAI,EAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,iBAC9B,IAAAE,YAAK,EAAE,CAAE,CAAC,oBACT,IAAAA,YAAK,EAAE,CAAE,CAAC,qBACT,IAAAA,YAAK,EAAE,CAAE,CAAC,sBACT,IAAAA,YAAK,EAAE,CAAE,CAAC,SAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IAC7B;AAACK,OAAA,CAAAU,iBAAA,GAAAA,iBAAA;AAEK,MAAME,eAAe,OAAAtB,KAAA,CAAAC,OAAA,SAAAE,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GACxBgB,qBAAc,oMAgBE,IAAAd,YAAK,EAAE,CAAE,CAAC,gIASV,IAAAA,YAAK,EAAE,CAAE,CAAC,mMAcDe,qBAAM,CAACC,gBAAgB,aAGhDT,oBAAoB,SAAAb,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IACvB;AAACK,OAAA,CAAAY,eAAA,GAAAA,eAAA;AAEK,MAAMI,UAAU,GAAG,kBAAA1B,KAAA,CAAAC,OAAA,EAAQ0B,eAAM,EAAAxB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,qBAE1B,IAAAE,YAAK,EAAE,CAAE,CAAC,yCAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,40IAOzB;AAACK,OAAA,CAAAgB,UAAA,GAAAA,UAAA"}