{"version": 3, "names": ["_context", "require", "_hook", "_context2", "_view", "UnconnectedFlex", "props", "forwardedRef", "children", "isColumn", "otherProps", "useFlex", "_react", "createElement", "FlexContext", "Provider", "value", "flexItemDisplay", "undefined", "View", "ref", "Flex", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/flex/flex/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useFlex } from './hook';\nimport { FlexContext } from './../context';\nimport { View } from '../../view';\nimport type { FlexProps } from '../types';\n\nfunction UnconnectedFlex(\n\tprops: WordPressComponentProps< FlexProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst { children, isColumn, ...otherProps } = useFlex( props );\n\n\treturn (\n\t\t<FlexContext.Provider\n\t\t\tvalue={ { flexItemDisplay: isColumn ? 'block' : undefined } }\n\t\t>\n\t\t\t<View { ...otherProps } ref={ forwardedRef }>\n\t\t\t\t{ children }\n\t\t\t</View>\n\t\t</FlexContext.Provider>\n\t);\n}\n\n/**\n * `Flex` is a primitive layout component that adaptively aligns child content\n * horizontally or vertically. `Flex` powers components like `HStack` and\n * `VStack`.\n *\n * `Flex` is used with any of its two sub-components, `FlexItem` and\n * `FlexBlock`.\n *\n * ```jsx\n * import { Flex, FlexBlock, FlexItem } from '@wordpress/components';\n *\n * function Example() {\n *   return (\n *     <Flex>\n *       <FlexItem>\n *         <p>Code</p>\n *       </FlexItem>\n *       <FlexBlock>\n *         <p>Poetry</p>\n *       </FlexBlock>\n *     </Flex>\n *   );\n * }\n * ```\n */\nexport const Flex = contextConnect( UnconnectedFlex, 'Flex' );\n\nexport default Flex;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAQA,SAASI,eAAeA,CACvBC,KAAkD,EAClDC,YAAiC,EAChC;EACD,MAAM;IAAEC,QAAQ;IAAEC,QAAQ;IAAE,GAAGC;EAAW,CAAC,GAAG,IAAAC,aAAO,EAAEL,KAAM,CAAC;EAE9D,OACC,IAAAM,MAAA,CAAAC,aAAA,EAACV,SAAA,CAAAW,WAAW,CAACC,QAAQ;IACpBC,KAAK,EAAG;MAAEC,eAAe,EAAER,QAAQ,GAAG,OAAO,GAAGS;IAAU;EAAG,GAE7D,IAAAN,MAAA,CAAAC,aAAA,EAACT,KAAA,CAAAe,IAAI;IAAA,GAAMT,UAAU;IAAGU,GAAG,EAAGb;EAAc,GACzCC,QACG,CACe,CAAC;AAEzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMa,IAAI,GAAG,IAAAC,uBAAc,EAAEjB,eAAe,EAAE,MAAO,CAAC;AAACkB,OAAA,CAAAF,IAAA,GAAAA,IAAA;AAAA,IAAAG,QAAA,GAE/CH,IAAI;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}