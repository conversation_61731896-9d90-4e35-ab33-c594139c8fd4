{"version": 3, "names": ["_element", "require", "_cyclePickerCell", "_interopRequireDefault", "CycleSelectControl", "help", "instanceId", "label", "multiple", "onChange", "options", "className", "hideLabelFromVision", "props", "id", "_react", "createElement", "default", "onChangeValue", "undefined", "_default", "memo", "exports"], "sources": ["@wordpress/components/src/mobile/cycle-select-control/index.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { memo } from '@wordpress/element';\n/**\n * Internal dependencies\n */\nimport CyclePickerCell from '../bottom-sheet/cycle-picker-cell';\n\nfunction CycleSelectControl( {\n\thelp,\n\tinstanceId,\n\tlabel,\n\tmultiple = false,\n\tonChange,\n\toptions = [],\n\tclassName,\n\thideLabelFromVision,\n\t...props\n} ) {\n\tconst id = `inspector-select-control-${ instanceId }`;\n\n\treturn (\n\t\t<CyclePickerCell\n\t\t\tlabel={ label }\n\t\t\thideLabelFromVision={ hideLabelFromVision }\n\t\t\tid={ id }\n\t\t\thelp={ help }\n\t\t\tclassName={ className }\n\t\t\tonChangeValue={ onChange }\n\t\t\taria-describedby={ !! help ? `${ id }__help` : undefined }\n\t\t\tmultiple={ multiple }\n\t\t\toptions={ options }\n\t\t\t{ ...props }\n\t\t/>\n\t);\n}\n\nexport default memo( CycleSelectControl );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAIA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAPA;AACA;AACA;;AAEA;AACA;AACA;;AAGA,SAASG,kBAAkBA,CAAE;EAC5BC,IAAI;EACJC,UAAU;EACVC,KAAK;EACLC,QAAQ,GAAG,KAAK;EAChBC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,SAAS;EACTC,mBAAmB;EACnB,GAAGC;AACJ,CAAC,EAAG;EACH,MAAMC,EAAE,GAAI,4BAA4BR,UAAY,EAAC;EAErD,OACC,IAAAS,MAAA,CAAAC,aAAA,EAACd,gBAAA,CAAAe,OAAe;IACfV,KAAK,EAAGA,KAAO;IACfK,mBAAmB,EAAGA,mBAAqB;IAC3CE,EAAE,EAAGA,EAAI;IACTT,IAAI,EAAGA,IAAM;IACbM,SAAS,EAAGA,SAAW;IACvBO,aAAa,EAAGT,QAAU;IAC1B,oBAAmB,CAAC,CAAEJ,IAAI,GAAI,GAAGS,EAAI,QAAO,GAAGK,SAAW;IAC1DX,QAAQ,EAAGA,QAAU;IACrBE,OAAO,EAAGA,OAAS;IAAA,GACdG;EAAK,CACV,CAAC;AAEJ;AAAC,IAAAO,QAAA,GAEc,IAAAC,aAAI,EAAEjB,kBAAmB,CAAC;AAAAkB,OAAA,CAAAL,OAAA,GAAAG,QAAA"}