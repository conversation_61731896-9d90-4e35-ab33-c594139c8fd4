{"version": 3, "names": ["_element", "require", "_deprecated", "_interopRequireDefault", "GuidePage", "props", "useEffect", "deprecated", "since", "alternative", "_react", "createElement"], "sources": ["@wordpress/components/src/guide/page.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useEffect } from '@wordpress/element';\nimport deprecated from '@wordpress/deprecated';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\n\nexport default function GuidePage(\n\tprops: WordPressComponentProps< {}, 'div', false >\n) {\n\tuseEffect( () => {\n\t\tdeprecated( '<GuidePage>', {\n\t\t\tsince: '5.5',\n\t\t\talternative: 'the `pages` prop in <Guide>',\n\t\t} );\n\t}, [] );\n\n\treturn <div { ...props } />;\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AAJA;AACA;AACA;;AASe,SAASG,SAASA,CAChCC,KAAkD,EACjD;EACD,IAAAC,kBAAS,EAAE,MAAM;IAChB,IAAAC,mBAAU,EAAE,aAAa,EAAE;MAC1BC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE;IACd,CAAE,CAAC;EACJ,CAAC,EAAE,EAAG,CAAC;EAEP,OAAO,IAAAC,MAAA,CAAAC,aAAA;IAAA,GAAUN;EAAK,CAAI,CAAC;AAC5B"}