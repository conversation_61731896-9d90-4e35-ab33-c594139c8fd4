{"version": 3, "names": ["_unitValues", "require", "sides", "borderProps", "isEmptyBorder", "border", "some", "prop", "undefined", "exports", "isDefinedBorder", "hasSplitBorders", "allSidesEmpty", "every", "side", "isCompleteBorder", "Object", "keys", "indexOf", "hasMixedBorders", "borders", "shorthandBorders", "map", "getShorthandBorderStyle", "getSplitBorders", "top", "right", "bottom", "left", "getBorderDiff", "original", "updated", "diff", "color", "style", "width", "getCommonBorder", "colors", "styles", "widths", "for<PERSON>ach", "push", "allColorsMatch", "value", "allStylesMatch", "allWidthsMatch", "getMostCommonUnit", "fallbackBorder", "fallbackColor", "fallbackStyle", "fallback<PERSON><PERSON><PERSON>", "hasVisibleBorder", "borderStyle", "filter", "Boolean", "join", "values", "units", "parseCSSUnitValue", "filteredUnits", "mode", "length", "maxCount", "currentMode"], "sources": ["@wordpress/components/src/border-box-control/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\n/**\n * Internal dependencies\n */\nimport { parseCSSUnitValue } from '../utils/unit-values';\nimport type { Border } from '../border-control/types';\nimport type { AnyBorder, Borders, BorderProp, BorderSide } from './types';\n\nconst sides: BorderSide[] = [ 'top', 'right', 'bottom', 'left' ];\nconst borderProps: BorderProp[] = [ 'color', 'style', 'width' ];\n\nexport const isEmptyBorder = ( border?: Border ) => {\n\tif ( ! border ) {\n\t\treturn true;\n\t}\n\treturn ! borderProps.some( ( prop ) => border[ prop ] !== undefined );\n};\n\nexport const isDefinedBorder = ( border: AnyBorder ) => {\n\t// No border, no worries :)\n\tif ( ! border ) {\n\t\treturn false;\n\t}\n\n\t// If we have individual borders per side within the border object we\n\t// need to check whether any of those side borders have been set.\n\tif ( hasSplitBorders( border ) ) {\n\t\tconst allSidesEmpty = sides.every( ( side ) =>\n\t\t\tisEmptyBorder( ( border as Borders )[ side ] )\n\t\t);\n\n\t\treturn ! allSidesEmpty;\n\t}\n\n\t// If we have a top-level border only, check if that is empty. e.g.\n\t// { color: undefined, style: undefined, width: undefined }\n\t// Border radius can still be set within the border object as it is\n\t// handled separately.\n\treturn ! isEmptyBorder( border as Border );\n};\n\nexport const isCompleteBorder = ( border?: Border ) => {\n\tif ( ! border ) {\n\t\treturn false;\n\t}\n\n\treturn borderProps.every( ( prop ) => border[ prop ] !== undefined );\n};\n\nexport const hasSplitBorders = ( border: AnyBorder = {} ) => {\n\treturn Object.keys( border ).some(\n\t\t( side ) => sides.indexOf( side as BorderSide ) !== -1\n\t);\n};\n\nexport const hasMixedBorders = ( borders: AnyBorder ) => {\n\tif ( ! hasSplitBorders( borders ) ) {\n\t\treturn false;\n\t}\n\n\tconst shorthandBorders = sides.map( ( side: BorderSide ) =>\n\t\tgetShorthandBorderStyle( ( borders as Borders )?.[ side ] )\n\t);\n\n\treturn ! shorthandBorders.every(\n\t\t( border ) => border === shorthandBorders[ 0 ]\n\t);\n};\n\nexport const getSplitBorders = ( border?: Border ) => {\n\tif ( ! border || isEmptyBorder( border ) ) {\n\t\treturn undefined;\n\t}\n\n\treturn {\n\t\ttop: border,\n\t\tright: border,\n\t\tbottom: border,\n\t\tleft: border,\n\t};\n};\n\nexport const getBorderDiff = ( original: Border, updated: Border ) => {\n\tconst diff: Border = {};\n\n\tif ( original.color !== updated.color ) {\n\t\tdiff.color = updated.color;\n\t}\n\n\tif ( original.style !== updated.style ) {\n\t\tdiff.style = updated.style;\n\t}\n\n\tif ( original.width !== updated.width ) {\n\t\tdiff.width = updated.width;\n\t}\n\n\treturn diff;\n};\n\nexport const getCommonBorder = ( borders?: Borders ) => {\n\tif ( ! borders ) {\n\t\treturn undefined;\n\t}\n\n\tconst colors: ( CSSProperties[ 'borderColor' ] | undefined )[] = [];\n\tconst styles: ( CSSProperties[ 'borderStyle' ] | undefined )[] = [];\n\tconst widths: ( CSSProperties[ 'borderWidth' ] | undefined )[] = [];\n\n\tsides.forEach( ( side ) => {\n\t\tcolors.push( borders[ side ]?.color );\n\t\tstyles.push( borders[ side ]?.style );\n\t\twidths.push( borders[ side ]?.width );\n\t} );\n\n\tconst allColorsMatch = colors.every( ( value ) => value === colors[ 0 ] );\n\tconst allStylesMatch = styles.every( ( value ) => value === styles[ 0 ] );\n\tconst allWidthsMatch = widths.every( ( value ) => value === widths[ 0 ] );\n\n\treturn {\n\t\tcolor: allColorsMatch ? colors[ 0 ] : undefined,\n\t\tstyle: allStylesMatch ? styles[ 0 ] : undefined,\n\t\twidth: allWidthsMatch ? widths[ 0 ] : getMostCommonUnit( widths ),\n\t};\n};\n\nexport const getShorthandBorderStyle = (\n\tborder?: Border,\n\tfallbackBorder?: Border\n) => {\n\tif ( isEmptyBorder( border ) ) {\n\t\treturn fallbackBorder;\n\t}\n\n\tconst {\n\t\tcolor: fallbackColor,\n\t\tstyle: fallbackStyle,\n\t\twidth: fallbackWidth,\n\t} = fallbackBorder || {};\n\n\tconst {\n\t\tcolor = fallbackColor,\n\t\tstyle = fallbackStyle,\n\t\twidth = fallbackWidth,\n\t} = border as Border;\n\n\tconst hasVisibleBorder = ( !! width && width !== '0' ) || !! color;\n\tconst borderStyle = hasVisibleBorder ? style || 'solid' : style;\n\n\treturn [ width, borderStyle, color ].filter( Boolean ).join( ' ' );\n};\n\nexport const getMostCommonUnit = (\n\tvalues: Array< string | number | undefined >\n): string | undefined => {\n\t// Collect all the CSS units.\n\tconst units = values.map( ( value ) =>\n\t\tvalue === undefined ? undefined : parseCSSUnitValue( `${ value }` )[ 1 ]\n\t);\n\n\t// Return the most common unit out of only the defined CSS units.\n\tconst filteredUnits = units.filter( ( value ) => value !== undefined );\n\treturn mode( filteredUnits as string[] );\n};\n\n/**\n * Finds the mode value out of the array passed favouring the first value\n * as a tiebreaker.\n *\n * @param values Values to determine the mode from.\n *\n * @return The mode value.\n */\nfunction mode( values: Array< string > ): string | undefined {\n\tif ( values.length === 0 ) {\n\t\treturn undefined;\n\t}\n\n\tconst map: { [ index: string ]: number } = {};\n\tlet maxCount = 0;\n\tlet currentMode;\n\n\tvalues.forEach( ( value ) => {\n\t\tmap[ value ] = map[ value ] === undefined ? 1 : map[ value ] + 1;\n\n\t\tif ( map[ value ] > maxCount ) {\n\t\t\tcurrentMode = value;\n\t\t\tmaxCount = map[ value ];\n\t\t}\n\t} );\n\n\treturn currentMode;\n}\n"], "mappings": ";;;;;;AAQA,IAAAA,WAAA,GAAAC,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAKA,MAAMC,KAAmB,GAAG,CAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAE;AAChE,MAAMC,WAAyB,GAAG,CAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAE;AAExD,MAAMC,aAAa,GAAKC,MAAe,IAAM;EACnD,IAAK,CAAEA,MAAM,EAAG;IACf,OAAO,IAAI;EACZ;EACA,OAAO,CAAEF,WAAW,CAACG,IAAI,CAAIC,IAAI,IAAMF,MAAM,CAAEE,IAAI,CAAE,KAAKC,SAAU,CAAC;AACtE,CAAC;AAACC,OAAA,CAAAL,aAAA,GAAAA,aAAA;AAEK,MAAMM,eAAe,GAAKL,MAAiB,IAAM;EACvD;EACA,IAAK,CAAEA,MAAM,EAAG;IACf,OAAO,KAAK;EACb;;EAEA;EACA;EACA,IAAKM,eAAe,CAAEN,MAAO,CAAC,EAAG;IAChC,MAAMO,aAAa,GAAGV,KAAK,CAACW,KAAK,CAAIC,IAAI,IACxCV,aAAa,CAAIC,MAAM,CAAeS,IAAI,CAAG,CAC9C,CAAC;IAED,OAAO,CAAEF,aAAa;EACvB;;EAEA;EACA;EACA;EACA;EACA,OAAO,CAAER,aAAa,CAAEC,MAAiB,CAAC;AAC3C,CAAC;AAACI,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAEK,MAAMK,gBAAgB,GAAKV,MAAe,IAAM;EACtD,IAAK,CAAEA,MAAM,EAAG;IACf,OAAO,KAAK;EACb;EAEA,OAAOF,WAAW,CAACU,KAAK,CAAIN,IAAI,IAAMF,MAAM,CAAEE,IAAI,CAAE,KAAKC,SAAU,CAAC;AACrE,CAAC;AAACC,OAAA,CAAAM,gBAAA,GAAAA,gBAAA;AAEK,MAAMJ,eAAe,GAAGA,CAAEN,MAAiB,GAAG,CAAC,CAAC,KAAM;EAC5D,OAAOW,MAAM,CAACC,IAAI,CAAEZ,MAAO,CAAC,CAACC,IAAI,CAC9BQ,IAAI,IAAMZ,KAAK,CAACgB,OAAO,CAAEJ,IAAmB,CAAC,KAAK,CAAC,CACtD,CAAC;AACF,CAAC;AAACL,OAAA,CAAAE,eAAA,GAAAA,eAAA;AAEK,MAAMQ,eAAe,GAAKC,OAAkB,IAAM;EACxD,IAAK,CAAET,eAAe,CAAES,OAAQ,CAAC,EAAG;IACnC,OAAO,KAAK;EACb;EAEA,MAAMC,gBAAgB,GAAGnB,KAAK,CAACoB,GAAG,CAAIR,IAAgB,IACrDS,uBAAuB,CAAIH,OAAO,GAAiBN,IAAI,CAAG,CAC3D,CAAC;EAED,OAAO,CAAEO,gBAAgB,CAACR,KAAK,CAC5BR,MAAM,IAAMA,MAAM,KAAKgB,gBAAgB,CAAE,CAAC,CAC7C,CAAC;AACF,CAAC;AAACZ,OAAA,CAAAU,eAAA,GAAAA,eAAA;AAEK,MAAMK,eAAe,GAAKnB,MAAe,IAAM;EACrD,IAAK,CAAEA,MAAM,IAAID,aAAa,CAAEC,MAAO,CAAC,EAAG;IAC1C,OAAOG,SAAS;EACjB;EAEA,OAAO;IACNiB,GAAG,EAAEpB,MAAM;IACXqB,KAAK,EAAErB,MAAM;IACbsB,MAAM,EAAEtB,MAAM;IACduB,IAAI,EAAEvB;EACP,CAAC;AACF,CAAC;AAACI,OAAA,CAAAe,eAAA,GAAAA,eAAA;AAEK,MAAMK,aAAa,GAAGA,CAAEC,QAAgB,EAAEC,OAAe,KAAM;EACrE,MAAMC,IAAY,GAAG,CAAC,CAAC;EAEvB,IAAKF,QAAQ,CAACG,KAAK,KAAKF,OAAO,CAACE,KAAK,EAAG;IACvCD,IAAI,CAACC,KAAK,GAAGF,OAAO,CAACE,KAAK;EAC3B;EAEA,IAAKH,QAAQ,CAACI,KAAK,KAAKH,OAAO,CAACG,KAAK,EAAG;IACvCF,IAAI,CAACE,KAAK,GAAGH,OAAO,CAACG,KAAK;EAC3B;EAEA,IAAKJ,QAAQ,CAACK,KAAK,KAAKJ,OAAO,CAACI,KAAK,EAAG;IACvCH,IAAI,CAACG,KAAK,GAAGJ,OAAO,CAACI,KAAK;EAC3B;EAEA,OAAOH,IAAI;AACZ,CAAC;AAACvB,OAAA,CAAAoB,aAAA,GAAAA,aAAA;AAEK,MAAMO,eAAe,GAAKhB,OAAiB,IAAM;EACvD,IAAK,CAAEA,OAAO,EAAG;IAChB,OAAOZ,SAAS;EACjB;EAEA,MAAM6B,MAAwD,GAAG,EAAE;EACnE,MAAMC,MAAwD,GAAG,EAAE;EACnE,MAAMC,MAAwD,GAAG,EAAE;EAEnErC,KAAK,CAACsC,OAAO,CAAI1B,IAAI,IAAM;IAC1BuB,MAAM,CAACI,IAAI,CAAErB,OAAO,CAAEN,IAAI,CAAE,EAAEmB,KAAM,CAAC;IACrCK,MAAM,CAACG,IAAI,CAAErB,OAAO,CAAEN,IAAI,CAAE,EAAEoB,KAAM,CAAC;IACrCK,MAAM,CAACE,IAAI,CAAErB,OAAO,CAAEN,IAAI,CAAE,EAAEqB,KAAM,CAAC;EACtC,CAAE,CAAC;EAEH,MAAMO,cAAc,GAAGL,MAAM,CAACxB,KAAK,CAAI8B,KAAK,IAAMA,KAAK,KAAKN,MAAM,CAAE,CAAC,CAAG,CAAC;EACzE,MAAMO,cAAc,GAAGN,MAAM,CAACzB,KAAK,CAAI8B,KAAK,IAAMA,KAAK,KAAKL,MAAM,CAAE,CAAC,CAAG,CAAC;EACzE,MAAMO,cAAc,GAAGN,MAAM,CAAC1B,KAAK,CAAI8B,KAAK,IAAMA,KAAK,KAAKJ,MAAM,CAAE,CAAC,CAAG,CAAC;EAEzE,OAAO;IACNN,KAAK,EAAES,cAAc,GAAGL,MAAM,CAAE,CAAC,CAAE,GAAG7B,SAAS;IAC/C0B,KAAK,EAAEU,cAAc,GAAGN,MAAM,CAAE,CAAC,CAAE,GAAG9B,SAAS;IAC/C2B,KAAK,EAAEU,cAAc,GAAGN,MAAM,CAAE,CAAC,CAAE,GAAGO,iBAAiB,CAAEP,MAAO;EACjE,CAAC;AACF,CAAC;AAAC9B,OAAA,CAAA2B,eAAA,GAAAA,eAAA;AAEK,MAAMb,uBAAuB,GAAGA,CACtClB,MAAe,EACf0C,cAAuB,KACnB;EACJ,IAAK3C,aAAa,CAAEC,MAAO,CAAC,EAAG;IAC9B,OAAO0C,cAAc;EACtB;EAEA,MAAM;IACLd,KAAK,EAAEe,aAAa;IACpBd,KAAK,EAAEe,aAAa;IACpBd,KAAK,EAAEe;EACR,CAAC,GAAGH,cAAc,IAAI,CAAC,CAAC;EAExB,MAAM;IACLd,KAAK,GAAGe,aAAa;IACrBd,KAAK,GAAGe,aAAa;IACrBd,KAAK,GAAGe;EACT,CAAC,GAAG7C,MAAgB;EAEpB,MAAM8C,gBAAgB,GAAK,CAAC,CAAEhB,KAAK,IAAIA,KAAK,KAAK,GAAG,IAAM,CAAC,CAAEF,KAAK;EAClE,MAAMmB,WAAW,GAAGD,gBAAgB,GAAGjB,KAAK,IAAI,OAAO,GAAGA,KAAK;EAE/D,OAAO,CAAEC,KAAK,EAAEiB,WAAW,EAAEnB,KAAK,CAAE,CAACoB,MAAM,CAAEC,OAAQ,CAAC,CAACC,IAAI,CAAE,GAAI,CAAC;AACnE,CAAC;AAAC9C,OAAA,CAAAc,uBAAA,GAAAA,uBAAA;AAEK,MAAMuB,iBAAiB,GAC7BU,MAA4C,IACpB;EACxB;EACA,MAAMC,KAAK,GAAGD,MAAM,CAAClC,GAAG,CAAIqB,KAAK,IAChCA,KAAK,KAAKnC,SAAS,GAAGA,SAAS,GAAG,IAAAkD,6BAAiB,EAAG,GAAGf,KAAO,EAAE,CAAC,CAAE,CAAC,CACvE,CAAC;;EAED;EACA,MAAMgB,aAAa,GAAGF,KAAK,CAACJ,MAAM,CAAIV,KAAK,IAAMA,KAAK,KAAKnC,SAAU,CAAC;EACtE,OAAOoD,IAAI,CAAED,aAA0B,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPAlD,OAAA,CAAAqC,iBAAA,GAAAA,iBAAA;AAQA,SAASc,IAAIA,CAAEJ,MAAuB,EAAuB;EAC5D,IAAKA,MAAM,CAACK,MAAM,KAAK,CAAC,EAAG;IAC1B,OAAOrD,SAAS;EACjB;EAEA,MAAMc,GAAkC,GAAG,CAAC,CAAC;EAC7C,IAAIwC,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW;EAEfP,MAAM,CAAChB,OAAO,CAAIG,KAAK,IAAM;IAC5BrB,GAAG,CAAEqB,KAAK,CAAE,GAAGrB,GAAG,CAAEqB,KAAK,CAAE,KAAKnC,SAAS,GAAG,CAAC,GAAGc,GAAG,CAAEqB,KAAK,CAAE,GAAG,CAAC;IAEhE,IAAKrB,GAAG,CAAEqB,KAAK,CAAE,GAAGmB,QAAQ,EAAG;MAC9BC,WAAW,GAAGpB,KAAK;MACnBmB,QAAQ,GAAGxC,GAAG,CAAEqB,KAAK,CAAE;IACxB;EACD,CAAE,CAAC;EAEH,OAAOoB,WAAW;AACnB"}