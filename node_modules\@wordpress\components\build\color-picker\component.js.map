{"version": 3, "names": ["_colord", "require", "_names", "_interopRequireDefault", "_element", "_compose", "_i18n", "_context", "_styles", "_colorCopyButton", "_colorInput", "_picker", "_hooks", "extend", "namesPlugin", "options", "label", "value", "UnconnectedColorPicker", "props", "forwardedRef", "enableAlpha", "color", "colorProp", "onChange", "defaultValue", "copyFormat", "onPickerDragStart", "onPickerDragEnd", "divProps", "useContextSystem", "containerEl", "setContainerEl", "useState", "containerRef", "node", "setColor", "useControlledValue", "safeColordColor", "useMemo", "colord", "debouncedSetColor", "useDebounce", "handleChange", "useCallback", "nextValue", "toHex", "colorType", "setColorType", "_react", "createElement", "ColorfulWrapper", "ref", "useMergeRefs", "Picker", "onDragStart", "onDragEnd", "AuxiliaryColorArtefactWrapper", "AuxiliaryColorArtefactHStackHeader", "justify", "SelectControl", "__nextHasNoMarginBottom", "nextColorType", "__", "hideLabelFromVision", "ColorCopyButton", "ColorInputWrapper", "direction", "gap", "ColorInput", "ColorPicker", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/color-picker/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\nimport type { Colord } from 'colord';\nimport { colord, extend } from 'colord';\nimport namesPlugin from 'colord/plugins/names';\n\n/**\n * WordPress dependencies\n */\nimport { useCallback, useState, useMemo } from '@wordpress/element';\nimport { useDebounce, useMergeRefs } from '@wordpress/compose';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { useContextSystem, contextConnect } from '../context';\nimport {\n\tColorfulWrapper,\n\tSelectControl,\n\tAuxiliaryColorArtefactWrapper,\n\tAuxiliaryColorArtefactHStackHeader,\n\tColorInputWrapper,\n} from './styles';\nimport { ColorCopyButton } from './color-copy-button';\nimport { ColorInput } from './color-input';\nimport { Picker } from './picker';\nimport { useControlledValue } from '../utils/hooks';\n\nimport type { ColorPickerProps, ColorType } from './types';\n\nextend( [ namesPlugin ] );\n\nconst options = [\n\t{ label: 'RGB', value: 'rgb' as const },\n\t{ label: 'HSL', value: 'hsl' as const },\n\t{ label: 'Hex', value: 'hex' as const },\n];\n\nconst UnconnectedColorPicker = (\n\tprops: ColorPickerProps,\n\tforwardedRef: ForwardedRef< any >\n) => {\n\tconst {\n\t\tenableAlpha = false,\n\t\tcolor: colorProp,\n\t\tonChange,\n\t\tdefaultValue = '#fff',\n\t\tcopyFormat,\n\n\t\t// Context\n\t\tonPickerDragStart,\n\t\tonPickerDragEnd,\n\t\t...divProps\n\t} = useContextSystem<\n\t\tColorPickerProps & {\n\t\t\tonPickerDragStart?: ( event: MouseEvent ) => void;\n\t\t\tonPickerDragEnd?: ( event: MouseEvent ) => void;\n\t\t}\n\t>( props, 'ColorPicker' );\n\n\tconst [ containerEl, setContainerEl ] = useState< HTMLElement | null >(\n\t\tnull\n\t);\n\tconst containerRef = ( node: HTMLElement | null ) => {\n\t\tsetContainerEl( node );\n\t};\n\n\t// Use a safe default value for the color and remove the possibility of `undefined`.\n\tconst [ color, setColor ] = useControlledValue( {\n\t\tonChange,\n\t\tvalue: colorProp,\n\t\tdefaultValue,\n\t} );\n\n\tconst safeColordColor = useMemo( () => {\n\t\treturn colord( color || '' );\n\t}, [ color ] );\n\n\tconst debouncedSetColor = useDebounce( setColor );\n\n\tconst handleChange = useCallback(\n\t\t( nextValue: Colord ) => {\n\t\t\tdebouncedSetColor( nextValue.toHex() );\n\t\t},\n\t\t[ debouncedSetColor ]\n\t);\n\n\tconst [ colorType, setColorType ] = useState< ColorType >(\n\t\tcopyFormat || 'hex'\n\t);\n\n\treturn (\n\t\t<ColorfulWrapper\n\t\t\tref={ useMergeRefs( [ containerRef, forwardedRef ] ) }\n\t\t\t{ ...divProps }\n\t\t>\n\t\t\t<Picker\n\t\t\t\tcontainerEl={ containerEl }\n\t\t\t\tonChange={ handleChange }\n\t\t\t\tcolor={ safeColordColor }\n\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\tonDragStart={ onPickerDragStart }\n\t\t\t\tonDragEnd={ onPickerDragEnd }\n\t\t\t/>\n\t\t\t<AuxiliaryColorArtefactWrapper>\n\t\t\t\t<AuxiliaryColorArtefactHStackHeader justify=\"space-between\">\n\t\t\t\t\t<SelectControl\n\t\t\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t\t\toptions={ options }\n\t\t\t\t\t\tvalue={ colorType }\n\t\t\t\t\t\tonChange={ ( nextColorType ) =>\n\t\t\t\t\t\t\tsetColorType( nextColorType as ColorType )\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlabel={ __( 'Color format' ) }\n\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t/>\n\t\t\t\t\t<ColorCopyButton\n\t\t\t\t\t\tcolor={ safeColordColor }\n\t\t\t\t\t\tcolorType={ copyFormat || colorType }\n\t\t\t\t\t/>\n\t\t\t\t</AuxiliaryColorArtefactHStackHeader>\n\t\t\t\t<ColorInputWrapper direction=\"column\" gap={ 2 }>\n\t\t\t\t\t<ColorInput\n\t\t\t\t\t\tcolorType={ colorType }\n\t\t\t\t\t\tcolor={ safeColordColor }\n\t\t\t\t\t\tonChange={ handleChange }\n\t\t\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\t\t/>\n\t\t\t\t</ColorInputWrapper>\n\t\t\t</AuxiliaryColorArtefactWrapper>\n\t\t</ColorfulWrapper>\n\t);\n};\n\nexport const ColorPicker = contextConnect(\n\tUnconnectedColorPicker,\n\t'ColorPicker'\n);\n\nexport default ColorPicker;\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AAKA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AAOA,IAAAQ,gBAAA,GAAAR,OAAA;AACA,IAAAS,WAAA,GAAAT,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AA7BA;AACA;AACA;;AAMA;AACA;AACA;;AAKA;AACA;AACA;;AAgBA,IAAAY,cAAM,EAAE,CAAEC,cAAW,CAAG,CAAC;AAEzB,MAAMC,OAAO,GAAG,CACf;EAAEC,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAe,CAAC,EACvC;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAe,CAAC,EACvC;EAAED,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAe,CAAC,CACvC;AAED,MAAMC,sBAAsB,GAAGA,CAC9BC,KAAuB,EACvBC,YAAiC,KAC7B;EACJ,MAAM;IACLC,WAAW,GAAG,KAAK;IACnBC,KAAK,EAAEC,SAAS;IAChBC,QAAQ;IACRC,YAAY,GAAG,MAAM;IACrBC,UAAU;IAEV;IACAC,iBAAiB;IACjBC,eAAe;IACf,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAKjBX,KAAK,EAAE,aAAc,CAAC;EAEzB,MAAM,CAAEY,WAAW,EAAEC,cAAc,CAAE,GAAG,IAAAC,iBAAQ,EAC/C,IACD,CAAC;EACD,MAAMC,YAAY,GAAKC,IAAwB,IAAM;IACpDH,cAAc,CAAEG,IAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM,CAAEb,KAAK,EAAEc,QAAQ,CAAE,GAAG,IAAAC,yBAAkB,EAAE;IAC/Cb,QAAQ;IACRP,KAAK,EAAEM,SAAS;IAChBE;EACD,CAAE,CAAC;EAEH,MAAMa,eAAe,GAAG,IAAAC,gBAAO,EAAE,MAAM;IACtC,OAAO,IAAAC,cAAM,EAAElB,KAAK,IAAI,EAAG,CAAC;EAC7B,CAAC,EAAE,CAAEA,KAAK,CAAG,CAAC;EAEd,MAAMmB,iBAAiB,GAAG,IAAAC,oBAAW,EAAEN,QAAS,CAAC;EAEjD,MAAMO,YAAY,GAAG,IAAAC,oBAAW,EAC7BC,SAAiB,IAAM;IACxBJ,iBAAiB,CAAEI,SAAS,CAACC,KAAK,CAAC,CAAE,CAAC;EACvC,CAAC,EACD,CAAEL,iBAAiB,CACpB,CAAC;EAED,MAAM,CAAEM,SAAS,EAAEC,YAAY,CAAE,GAAG,IAAAf,iBAAQ,EAC3CP,UAAU,IAAI,KACf,CAAC;EAED,OACC,IAAAuB,MAAA,CAAAC,aAAA,EAAC1C,OAAA,CAAA2C,eAAe;IACfC,GAAG,EAAG,IAAAC,qBAAY,EAAE,CAAEnB,YAAY,EAAEd,YAAY,CAAG,CAAG;IAAA,GACjDS;EAAQ,GAEb,IAAAoB,MAAA,CAAAC,aAAA,EAACvC,OAAA,CAAA2C,MAAM;IACNvB,WAAW,EAAGA,WAAa;IAC3BP,QAAQ,EAAGmB,YAAc;IACzBrB,KAAK,EAAGgB,eAAiB;IACzBjB,WAAW,EAAGA,WAAa;IAC3BkC,WAAW,EAAG5B,iBAAmB;IACjC6B,SAAS,EAAG5B;EAAiB,CAC7B,CAAC,EACF,IAAAqB,MAAA,CAAAC,aAAA,EAAC1C,OAAA,CAAAiD,6BAA6B,QAC7B,IAAAR,MAAA,CAAAC,aAAA,EAAC1C,OAAA,CAAAkD,kCAAkC;IAACC,OAAO,EAAC;EAAe,GAC1D,IAAAV,MAAA,CAAAC,aAAA,EAAC1C,OAAA,CAAAoD,aAAa;IACbC,uBAAuB;IACvB9C,OAAO,EAAGA,OAAS;IACnBE,KAAK,EAAG8B,SAAW;IACnBvB,QAAQ,EAAKsC,aAAa,IACzBd,YAAY,CAAEc,aAA2B,CACzC;IACD9C,KAAK,EAAG,IAAA+C,QAAE,EAAE,cAAe,CAAG;IAC9BC,mBAAmB;EAAA,CACnB,CAAC,EACF,IAAAf,MAAA,CAAAC,aAAA,EAACzC,gBAAA,CAAAwD,eAAe;IACf3C,KAAK,EAAGgB,eAAiB;IACzBS,SAAS,EAAGrB,UAAU,IAAIqB;EAAW,CACrC,CACkC,CAAC,EACrC,IAAAE,MAAA,CAAAC,aAAA,EAAC1C,OAAA,CAAA0D,iBAAiB;IAACC,SAAS,EAAC,QAAQ;IAACC,GAAG,EAAG;EAAG,GAC9C,IAAAnB,MAAA,CAAAC,aAAA,EAACxC,WAAA,CAAA2D,UAAU;IACVtB,SAAS,EAAGA,SAAW;IACvBzB,KAAK,EAAGgB,eAAiB;IACzBd,QAAQ,EAAGmB,YAAc;IACzBtB,WAAW,EAAGA;EAAa,CAC3B,CACiB,CACW,CACf,CAAC;AAEpB,CAAC;AAEM,MAAMiD,WAAW,GAAG,IAAAC,uBAAc,EACxCrD,sBAAsB,EACtB,aACD,CAAC;AAACsD,OAAA,CAAAF,WAAA,GAAAA,WAAA;AAAA,IAAAG,QAAA,GAEaH,WAAW;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}