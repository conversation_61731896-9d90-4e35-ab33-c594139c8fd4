"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useBorderControl = useBorderControl;
var _element = require("@wordpress/element");
var styles = _interopRequireWildcard(require("../styles"));
var _utils = require("../../unit-control/utils");
var _context = require("../../context");
var _useCx = require("../../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

// If either width or color are defined, the border is considered valid
// and a border style can be set as well.
const isValidBorder = border => {
  const hasWidth = border?.width !== undefined && border.width !== '';
  const hasColor = border?.color !== undefined;
  return hasWidth || hasColor;
};
function useBorderControl(props) {
  const {
    className,
    colors = [],
    isCompact,
    onChange,
    enableAlpha = true,
    enableStyle = true,
    shouldSanitizeBorder = true,
    size = 'default',
    value: border,
    width,
    __experimentalIsRenderedInSidebar = false,
    __next40pxDefaultSize,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'BorderControl');
  const computedSize = size === 'default' && __next40pxDefaultSize ? '__unstable-large' : size;
  const [widthValue, originalWidthUnit] = (0, _utils.parseQuantityAndUnitFromRawValue)(border?.width);
  const widthUnit = originalWidthUnit || 'px';
  const hadPreviousZeroWidth = widthValue === 0;
  const [colorSelection, setColorSelection] = (0, _element.useState)();
  const [styleSelection, setStyleSelection] = (0, _element.useState)();
  const isStyleSettable = shouldSanitizeBorder ? isValidBorder(border) : true;
  const onBorderChange = (0, _element.useCallback)(newBorder => {
    if (shouldSanitizeBorder && !isValidBorder(newBorder)) {
      onChange(undefined);
      return;
    }
    onChange(newBorder);
  }, [onChange, shouldSanitizeBorder]);
  const onWidthChange = (0, _element.useCallback)(newWidth => {
    const newWidthValue = newWidth === '' ? undefined : newWidth;
    const [parsedValue] = (0, _utils.parseQuantityAndUnitFromRawValue)(newWidth);
    const hasZeroWidth = parsedValue === 0;
    const updatedBorder = {
      ...border,
      width: newWidthValue
    };

    // Setting the border width explicitly to zero will also set the
    // border style to `none` and clear the border color.
    if (hasZeroWidth && !hadPreviousZeroWidth) {
      // Before clearing the color and style selections, keep track of
      // the current selections so they can be restored when the width
      // changes to a non-zero value.
      setColorSelection(border?.color);
      setStyleSelection(border?.style);

      // Clear the color and style border properties.
      updatedBorder.color = undefined;
      updatedBorder.style = 'none';
    }

    // Selection has changed from zero border width to non-zero width.
    if (!hasZeroWidth && hadPreviousZeroWidth) {
      // Restore previous border color and style selections if width
      // is now not zero.
      if (updatedBorder.color === undefined) {
        updatedBorder.color = colorSelection;
      }
      if (updatedBorder.style === 'none') {
        updatedBorder.style = styleSelection;
      }
    }
    onBorderChange(updatedBorder);
  }, [border, hadPreviousZeroWidth, colorSelection, styleSelection, onBorderChange]);
  const onSliderChange = (0, _element.useCallback)(value => {
    onWidthChange(`${value}${widthUnit}`);
  }, [onWidthChange, widthUnit]);

  // Generate class names.
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => {
    return cx(styles.borderControl, className);
  }, [className, cx]);
  let wrapperWidth = width;
  if (isCompact) {
    // Widths below represent the minimum usable width for compact controls.
    // Taller controls contain greater internal padding, thus greater width.
    wrapperWidth = size === '__unstable-large' ? '116px' : '90px';
  }
  const innerWrapperClassName = (0, _element.useMemo)(() => {
    const widthStyle = !!wrapperWidth && styles.wrapperWidth;
    const heightStyle = styles.wrapperHeight(computedSize);
    return cx(styles.innerWrapper(), widthStyle, heightStyle);
  }, [wrapperWidth, cx, computedSize]);
  const sliderClassName = (0, _element.useMemo)(() => {
    return cx(styles.borderSlider());
  }, [cx]);
  return {
    ...otherProps,
    className: classes,
    colors,
    enableAlpha,
    enableStyle,
    innerWrapperClassName,
    inputWidth: wrapperWidth,
    isStyleSettable,
    onBorderChange,
    onSliderChange,
    onWidthChange,
    previousStyleSelection: styleSelection,
    sliderClassName,
    value: border,
    widthUnit,
    widthValue,
    size: computedSize,
    __experimentalIsRenderedInSidebar,
    __next40pxDefaultSize
  };
}
//# sourceMappingURL=hook.js.map