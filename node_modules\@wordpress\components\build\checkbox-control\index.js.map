{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_compose", "_deprecated", "_icons", "_baseControl", "CheckboxControl", "props", "__nextHasNoMarginBottom", "label", "className", "heading", "checked", "indeterminate", "help", "id", "idProp", "onChange", "additionalProps", "deprecated", "alternative", "since", "showCheckedIcon", "setShowCheckedIcon", "useState", "showIndeterminateIcon", "setShowIndeterminateIcon", "ref", "useRefEffect", "node", "matches", "useInstanceId", "onChangeValue", "event", "target", "_react", "createElement", "default", "classnames", "type", "value", "undefined", "Icon", "icon", "reset", "role", "check", "htmlFor", "_default", "exports"], "sources": ["@wordpress/components/src/checkbox-control/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { ChangeEvent } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useState } from '@wordpress/element';\nimport { useInstanceId, useRefEffect } from '@wordpress/compose';\nimport deprecated from '@wordpress/deprecated';\nimport { Icon, check, reset } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport BaseControl from '../base-control';\nimport type { CheckboxControlProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\n/**\n * Checkboxes allow the user to select one or more items from a set.\n *\n * ```jsx\n * import { CheckboxControl } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyCheckboxControl = () => {\n *   const [ isChecked, setChecked ] = useState( true );\n *   return (\n *     <CheckboxControl\n *       label=\"Is author\"\n *       help=\"Is the user a author or not?\"\n *       checked={ isChecked }\n *       onChange={ setChecked }\n *     />\n *   );\n * };\n * ```\n */\nexport function CheckboxControl(\n\tprops: WordPressComponentProps< CheckboxControlProps, 'input', false >\n) {\n\tconst {\n\t\t__nextHasNoMarginBottom,\n\t\tlabel,\n\t\tclassName,\n\t\theading,\n\t\tchecked,\n\t\tindeterminate,\n\t\thelp,\n\t\tid: idProp,\n\t\tonChange,\n\t\t...additionalProps\n\t} = props;\n\n\tif ( heading ) {\n\t\tdeprecated( '`heading` prop in `CheckboxControl`', {\n\t\t\talternative: 'a separate element to implement a heading',\n\t\t\tsince: '5.8',\n\t\t} );\n\t}\n\n\tconst [ showCheckedIcon, setShowCheckedIcon ] = useState( false );\n\tconst [ showIndeterminateIcon, setShowIndeterminateIcon ] =\n\t\tuseState( false );\n\n\t// Run the following callback every time the `ref` (and the additional\n\t// dependencies) change.\n\tconst ref = useRefEffect< HTMLInputElement >(\n\t\t( node ) => {\n\t\t\tif ( ! node ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// It cannot be set using an HTML attribute.\n\t\t\tnode.indeterminate = !! indeterminate;\n\n\t\t\tsetShowCheckedIcon( node.matches( ':checked' ) );\n\t\t\tsetShowIndeterminateIcon( node.matches( ':indeterminate' ) );\n\t\t},\n\t\t[ checked, indeterminate ]\n\t);\n\tconst id = useInstanceId(\n\t\tCheckboxControl,\n\t\t'inspector-checkbox-control',\n\t\tidProp\n\t);\n\tconst onChangeValue = ( event: ChangeEvent< HTMLInputElement > ) =>\n\t\tonChange( event.target.checked );\n\n\treturn (\n\t\t<BaseControl\n\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\tlabel={ heading }\n\t\t\tid={ id }\n\t\t\thelp={ help }\n\t\t\tclassName={ classnames( 'components-checkbox-control', className ) }\n\t\t>\n\t\t\t<span className=\"components-checkbox-control__input-container\">\n\t\t\t\t<input\n\t\t\t\t\tref={ ref }\n\t\t\t\t\tid={ id }\n\t\t\t\t\tclassName=\"components-checkbox-control__input\"\n\t\t\t\t\ttype=\"checkbox\"\n\t\t\t\t\tvalue=\"1\"\n\t\t\t\t\tonChange={ onChangeValue }\n\t\t\t\t\tchecked={ checked }\n\t\t\t\t\taria-describedby={ !! help ? id + '__help' : undefined }\n\t\t\t\t\t{ ...additionalProps }\n\t\t\t\t/>\n\t\t\t\t{ showIndeterminateIcon ? (\n\t\t\t\t\t<Icon\n\t\t\t\t\t\ticon={ reset }\n\t\t\t\t\t\tclassName=\"components-checkbox-control__indeterminate\"\n\t\t\t\t\t\trole=\"presentation\"\n\t\t\t\t\t/>\n\t\t\t\t) : null }\n\t\t\t\t{ showCheckedIcon ? (\n\t\t\t\t\t<Icon\n\t\t\t\t\t\ticon={ check }\n\t\t\t\t\t\tclassName=\"components-checkbox-control__checked\"\n\t\t\t\t\t\trole=\"presentation\"\n\t\t\t\t\t/>\n\t\t\t\t) : null }\n\t\t\t</span>\n\t\t\t{ label && (\n\t\t\t\t<label\n\t\t\t\t\tclassName=\"components-checkbox-control__label\"\n\t\t\t\t\thtmlFor={ id }\n\t\t\t\t>\n\t\t\t\t\t{ label }\n\t\t\t\t</label>\n\t\t\t) }\n\t\t</BaseControl>\n\t);\n}\n\nexport default CheckboxControl;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAKA,IAAAK,YAAA,GAAAN,sBAAA,CAAAC,OAAA;AAjBA;AACA;AACA;;AAIA;AACA;AACA;;AAMA;AACA;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASM,eAAeA,CAC9BC,KAAsE,EACrE;EACD,MAAM;IACLC,uBAAuB;IACvBC,KAAK;IACLC,SAAS;IACTC,OAAO;IACPC,OAAO;IACPC,aAAa;IACbC,IAAI;IACJC,EAAE,EAAEC,MAAM;IACVC,QAAQ;IACR,GAAGC;EACJ,CAAC,GAAGX,KAAK;EAET,IAAKI,OAAO,EAAG;IACd,IAAAQ,mBAAU,EAAE,qCAAqC,EAAE;MAClDC,WAAW,EAAE,2CAA2C;MACxDC,KAAK,EAAE;IACR,CAAE,CAAC;EACJ;EAEA,MAAM,CAAEC,eAAe,EAAEC,kBAAkB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EACjE,MAAM,CAAEC,qBAAqB,EAAEC,wBAAwB,CAAE,GACxD,IAAAF,iBAAQ,EAAE,KAAM,CAAC;;EAElB;EACA;EACA,MAAMG,GAAG,GAAG,IAAAC,qBAAY,EACrBC,IAAI,IAAM;IACX,IAAK,CAAEA,IAAI,EAAG;MACb;IACD;;IAEA;IACAA,IAAI,CAAChB,aAAa,GAAG,CAAC,CAAEA,aAAa;IAErCU,kBAAkB,CAAEM,IAAI,CAACC,OAAO,CAAE,UAAW,CAAE,CAAC;IAChDJ,wBAAwB,CAAEG,IAAI,CAACC,OAAO,CAAE,gBAAiB,CAAE,CAAC;EAC7D,CAAC,EACD,CAAElB,OAAO,EAAEC,aAAa,CACzB,CAAC;EACD,MAAME,EAAE,GAAG,IAAAgB,sBAAa,EACvBzB,eAAe,EACf,4BAA4B,EAC5BU,MACD,CAAC;EACD,MAAMgB,aAAa,GAAKC,KAAsC,IAC7DhB,QAAQ,CAAEgB,KAAK,CAACC,MAAM,CAACtB,OAAQ,CAAC;EAEjC,OACC,IAAAuB,MAAA,CAAAC,aAAA,EAAC/B,YAAA,CAAAgC,OAAW;IACX7B,uBAAuB,EAAGA,uBAAyB;IACnDC,KAAK,EAAGE,OAAS;IACjBI,EAAE,EAAGA,EAAI;IACTD,IAAI,EAAGA,IAAM;IACbJ,SAAS,EAAG,IAAA4B,mBAAU,EAAE,6BAA6B,EAAE5B,SAAU;EAAG,GAEpE,IAAAyB,MAAA,CAAAC,aAAA;IAAM1B,SAAS,EAAC;EAA8C,GAC7D,IAAAyB,MAAA,CAAAC,aAAA;IACCT,GAAG,EAAGA,GAAK;IACXZ,EAAE,EAAGA,EAAI;IACTL,SAAS,EAAC,oCAAoC;IAC9C6B,IAAI,EAAC,UAAU;IACfC,KAAK,EAAC,GAAG;IACTvB,QAAQ,EAAGe,aAAe;IAC1BpB,OAAO,EAAGA,OAAS;IACnB,oBAAmB,CAAC,CAAEE,IAAI,GAAGC,EAAE,GAAG,QAAQ,GAAG0B,SAAW;IAAA,GACnDvB;EAAe,CACpB,CAAC,EACAO,qBAAqB,GACtB,IAAAU,MAAA,CAAAC,aAAA,EAAChC,MAAA,CAAAsC,IAAI;IACJC,IAAI,EAAGC,YAAO;IACdlC,SAAS,EAAC,4CAA4C;IACtDmC,IAAI,EAAC;EAAc,CACnB,CAAC,GACC,IAAI,EACNvB,eAAe,GAChB,IAAAa,MAAA,CAAAC,aAAA,EAAChC,MAAA,CAAAsC,IAAI;IACJC,IAAI,EAAGG,YAAO;IACdpC,SAAS,EAAC,sCAAsC;IAChDmC,IAAI,EAAC;EAAc,CACnB,CAAC,GACC,IACC,CAAC,EACLpC,KAAK,IACN,IAAA0B,MAAA,CAAAC,aAAA;IACC1B,SAAS,EAAC,oCAAoC;IAC9CqC,OAAO,EAAGhC;EAAI,GAEZN,KACI,CAEI,CAAC;AAEhB;AAAC,IAAAuC,QAAA,GAEc1C,eAAe;AAAA2C,OAAA,CAAAZ,OAAA,GAAAW,QAAA"}