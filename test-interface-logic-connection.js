/**
 * Script de test pour vérifier la connexion Interface ↔ Logique
 * À exécuter dans la console du navigateur sur la page d'admin WordPress
 */

console.log('🔗 Test de Connexion Interface ↔ Logique');
console.log('==========================================');

// Fonction pour tester la disponibilité d'une route API
async function testAPIRoute(route, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        };
        
        if (data && method !== 'GET') {
            options.headers['Content-Type'] = 'application/json';
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(route, options);
        const result = await response.json();
        
        return {
            available: response.ok,
            status: response.status,
            result: result
        };
    } catch (error) {
        return {
            available: false,
            error: error.message
        };
    }
}

// Test de toutes les routes API Phase 3
async function testAllAPIRoutes() {
    console.log('\n📡 === TEST DES ROUTES API ===');
    
    const routes = [
        // Optimisation Intelligente
        { path: '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/priorities', name: 'Smart Priorities' },
        { path: '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/algorithms', name: 'Algorithms Config' },
        
        // Search Console
        { path: '/wp-json/boss-seo/v1/robots-sitemap/search-console/data', name: 'Search Console Data' },
        
        // Cache
        { path: '/wp-json/boss-seo/v1/robots-sitemap/cache/stats', name: 'Cache Stats' },
        
        // Rapports
        { path: '/wp-json/boss-seo/v1/robots-sitemap/reports/templates', name: 'Report Templates' },
        { path: '/wp-json/boss-seo/v1/robots-sitemap/reports/history', name: 'Reports History' },
        
        // Routes existantes (vérification)
        { path: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats', name: 'Dashboard Stats' },
        { path: '/wp-json/boss-seo/v1/robots-sitemap/specialized/stats', name: 'Specialized Stats' },
        { path: '/wp-json/boss-seo/v1/robots-sitemap/monitoring/indexation-stats', name: 'Monitoring Stats' }
    ];
    
    const results = {};
    
    for (const route of routes) {
        console.log(`🔍 Test: ${route.name}`);
        const result = await testAPIRoute(route.path);
        results[route.name] = result;
        
        if (result.available) {
            console.log(`✅ ${route.name}: Disponible`);
        } else {
            console.log(`❌ ${route.name}: Indisponible (${result.status || 'Erreur'})`);
        }
    }
    
    return results;
}

// Test des composants React
function testReactComponents() {
    console.log('\n⚛️ === TEST DES COMPOSANTS REACT ===');
    
    // Vérifier si les composants sont montés
    const components = [
        { selector: '.boss-advanced-sitemap-config-pro', name: 'Composant Principal' },
        { selector: '[data-tab-name="smart-optimization"]', name: 'Onglet Optimisation IA' },
        { selector: '[data-tab-name="search-console"]', name: 'Onglet Search Console' },
        { selector: '[data-tab-name="cache-performance"]', name: 'Onglet Cache' },
        { selector: '[data-tab-name="reports"]', name: 'Onglet Rapports' }
    ];
    
    const results = {};
    
    components.forEach(component => {
        const element = document.querySelector(component.selector);
        const available = element !== null;
        results[component.name] = available;
        
        if (available) {
            console.log(`✅ ${component.name}: Présent dans le DOM`);
        } else {
            console.log(`❌ ${component.name}: Absent du DOM`);
        }
    });
    
    return results;
}

// Test des services JavaScript
function testJavaScriptServices() {
    console.log('\n🔧 === TEST DES SERVICES JAVASCRIPT ===');
    
    const services = [
        'RobotsSitemapService',
        'wp.apiFetch',
        'wp.components',
        'wp.element'
    ];
    
    const results = {};
    
    services.forEach(service => {
        let available = false;
        
        try {
            if (service === 'RobotsSitemapService') {
                // Ce service est importé dans les composants, pas global
                available = typeof window.wp !== 'undefined';
            } else if (service.startsWith('wp.')) {
                const parts = service.split('.');
                available = window[parts[0]] && window[parts[0]][parts[1]];
            } else {
                available = typeof window[service] !== 'undefined';
            }
        } catch (error) {
            available = false;
        }
        
        results[service] = available;
        
        if (available) {
            console.log(`✅ ${service}: Disponible`);
        } else {
            console.log(`❌ ${service}: Indisponible`);
        }
    });
    
    return results;
}

// Test des classes PHP (via API)
async function testPHPClasses() {
    console.log('\n🐘 === TEST DES CLASSES PHP ===');
    
    const classes = [
        { name: 'Boss_Smart_Optimization', test: '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/priorities' },
        { name: 'Boss_Search_Console', test: '/wp-json/boss-seo/v1/robots-sitemap/search-console/data' },
        { name: 'Boss_Sitemap_Cache', test: '/wp-json/boss-seo/v1/robots-sitemap/cache/stats' },
        { name: 'Boss_PDF_Reports', test: '/wp-json/boss-seo/v1/robots-sitemap/reports/templates' },
        { name: 'Boss_Sitemap_Dashboard', test: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats' }
    ];
    
    const results = {};
    
    for (const phpClass of classes) {
        console.log(`🔍 Test: ${phpClass.name}`);
        const result = await testAPIRoute(phpClass.test);
        results[phpClass.name] = result.available;
        
        if (result.available) {
            console.log(`✅ ${phpClass.name}: Classe chargée et fonctionnelle`);
        } else {
            console.log(`❌ ${phpClass.name}: Classe non disponible ou erreur`);
        }
    }
    
    return results;
}

// Test d'intégration bout en bout
async function testEndToEndIntegration() {
    console.log('\n🔄 === TEST D\'INTÉGRATION BOUT EN BOUT ===');
    
    // Scénario: Cliquer sur un onglet et charger les données
    console.log('📋 Scénario: Navigation et chargement des données');
    
    // 1. Vérifier la présence des onglets
    const tabs = document.querySelectorAll('[role="tab"]');
    console.log(`✅ Onglets trouvés: ${tabs.length}`);
    
    // 2. Tester le chargement des données pour chaque fonctionnalité
    const dataTests = [
        { name: 'Dashboard', api: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats' },
        { name: 'Optimisation IA', api: '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/priorities' },
        { name: 'Search Console', api: '/wp-json/boss-seo/v1/robots-sitemap/search-console/data' },
        { name: 'Cache', api: '/wp-json/boss-seo/v1/robots-sitemap/cache/stats' },
        { name: 'Rapports', api: '/wp-json/boss-seo/v1/robots-sitemap/reports/templates' }
    ];
    
    const results = {};
    
    for (const test of dataTests) {
        console.log(`🔍 Test données: ${test.name}`);
        const result = await testAPIRoute(test.api);
        results[test.name] = {
            api_available: result.available,
            has_data: result.available && result.result && Object.keys(result.result).length > 0
        };
        
        if (results[test.name].api_available && results[test.name].has_data) {
            console.log(`✅ ${test.name}: API disponible avec données`);
        } else if (results[test.name].api_available) {
            console.log(`⚠️ ${test.name}: API disponible mais sans données`);
        } else {
            console.log(`❌ ${test.name}: API indisponible`);
        }
    }
    
    return results;
}

// Test des permissions et sécurité
async function testSecurityAndPermissions() {
    console.log('\n🔒 === TEST SÉCURITÉ ET PERMISSIONS ===');
    
    // Vérifier la présence du nonce
    const nonceAvailable = typeof wpApiSettings !== 'undefined' && wpApiSettings.nonce;
    console.log(`${nonceAvailable ? '✅' : '❌'} Nonce WordPress: ${nonceAvailable ? 'Présent' : 'Absent'}`);
    
    // Tester l'accès sans nonce (doit échouer)
    try {
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats', {
            method: 'GET'
            // Pas de nonce intentionnellement
        });
        
        const unauthorized = response.status === 401 || response.status === 403;
        console.log(`${unauthorized ? '✅' : '❌'} Protection sans nonce: ${unauthorized ? 'Protégé' : 'Non protégé'}`);
        
        return {
            nonce_available: nonceAvailable,
            protected_without_nonce: unauthorized
        };
    } catch (error) {
        console.log('⚠️ Erreur lors du test de sécurité:', error.message);
        return {
            nonce_available: nonceAvailable,
            protected_without_nonce: true // Assume protected if error
        };
    }
}

// Fonction principale pour exécuter tous les tests
async function runConnectionTests() {
    console.log('🚀 Démarrage des tests de connexion Interface ↔ Logique...\n');
    
    const startTime = Date.now();
    
    try {
        // Exécuter tous les tests
        const apiResults = await testAllAPIRoutes();
        const componentResults = testReactComponents();
        const serviceResults = testJavaScriptServices();
        const phpResults = await testPHPClasses();
        const integrationResults = await testEndToEndIntegration();
        const securityResults = await testSecurityAndPermissions();
        
        const endTime = Date.now();
        
        // Calculer les statistiques
        const apiSuccess = Object.values(apiResults).filter(r => r.available).length;
        const apiTotal = Object.keys(apiResults).length;
        const componentSuccess = Object.values(componentResults).filter(r => r).length;
        const componentTotal = Object.keys(componentResults).length;
        const phpSuccess = Object.values(phpResults).filter(r => r).length;
        const phpTotal = Object.keys(phpResults).length;
        
        console.log('\n📊 === RÉSUMÉ DES TESTS DE CONNEXION ===');
        console.log(`⏱️  Durée totale: ${endTime - startTime}ms`);
        console.log(`📡 APIs: ${apiSuccess}/${apiTotal} fonctionnelles`);
        console.log(`⚛️  Composants React: ${componentSuccess}/${componentTotal} présents`);
        console.log(`🐘 Classes PHP: ${phpSuccess}/${phpTotal} chargées`);
        console.log(`🔒 Sécurité: ${securityResults.nonce_available && securityResults.protected_without_nonce ? 'OK' : 'À vérifier'}`);
        
        // Verdict final
        const overallSuccess = (apiSuccess / apiTotal) > 0.8 && (phpSuccess / phpTotal) > 0.8;
        
        console.log('\n🎯 === VERDICT FINAL ===');
        if (overallSuccess) {
            console.log('✅ CONNEXION INTERFACE ↔ LOGIQUE: OPÉRATIONNELLE');
            console.log('🎉 Les interfaces sont bien connectées à la logique backend !');
        } else {
            console.log('❌ CONNEXION INTERFACE ↔ LOGIQUE: PROBLÈMES DÉTECTÉS');
            console.log('🔧 Vérifiez les erreurs ci-dessus pour corriger les problèmes');
        }
        
        return {
            success: overallSuccess,
            api: apiResults,
            components: componentResults,
            services: serviceResults,
            php: phpResults,
            integration: integrationResults,
            security: securityResults
        };
        
    } catch (error) {
        console.log('\n💥 Erreur lors des tests de connexion:', error);
        return { success: false, error: error.message };
    }
}

// Rendre la fonction disponible globalement
window.testConnection = runConnectionTests;

console.log('\n🛠️ Test de connexion disponible:');
console.log('- testConnection() - Exécuter tous les tests de connexion');
console.log('\n🎯 Pour commencer, tapez: testConnection()');
