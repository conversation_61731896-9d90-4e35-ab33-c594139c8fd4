{"version": 3, "names": ["_reactNative", "require", "_native", "_element", "_icons", "_i18n", "_components", "_style", "_interopRequireDefault", "BottomSheetSelectControl", "label", "icon", "options", "items", "onChange", "value", "selected<PERSON><PERSON><PERSON>", "disabled", "showSubSheet", "setShowSubSheet", "useState", "navigation", "useNavigation", "onChangeValue", "goBack", "selectedOption", "find", "option", "openSubSheet", "navigate", "BottomSheet", "SubSheet", "screenName", "_react", "createElement", "navigationButton", "Cell", "separatorType", "onPress", "accessibilityRole", "accessibilityLabel", "sprintf", "__", "accessibilityHint", "Icon", "chevronRight", "showSheet", "Fragment", "NavBar", "BackButton", "Heading", "View", "style", "styles", "selectControl", "map", "item", "index", "customActionButton", "leftAlign", "key", "check", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet-select-control/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { useNavigation } from '@react-navigation/native';\nimport { useState } from '@wordpress/element';\nimport { Icon, chevronRight, check } from '@wordpress/icons';\nimport { __, sprintf } from '@wordpress/i18n';\nimport { BottomSheet } from '@wordpress/components';\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nconst BottomSheetSelectControl = ( {\n\tlabel,\n\ticon,\n\toptions: items,\n\tonChange,\n\tvalue: selectedValue,\n\tdisabled,\n} ) => {\n\tconst [ showSubSheet, setShowSubSheet ] = useState( false );\n\tconst navigation = useNavigation();\n\n\tconst onChangeValue = ( value ) => {\n\t\treturn () => {\n\t\t\tgoBack();\n\t\t\tonChange( value );\n\t\t};\n\t};\n\n\tconst selectedOption = items.find(\n\t\t( option ) => option.value === selectedValue\n\t);\n\n\tconst goBack = () => {\n\t\tsetShowSubSheet( false );\n\t\tnavigation.goBack();\n\t};\n\n\tconst openSubSheet = () => {\n\t\tnavigation.navigate( BottomSheet.SubSheet.screenName );\n\t\tsetShowSubSheet( true );\n\t};\n\n\treturn (\n\t\t<BottomSheet.SubSheet\n\t\t\tnavigationButton={\n\t\t\t\t<BottomSheet.Cell\n\t\t\t\t\tlabel={ label }\n\t\t\t\t\tseparatorType=\"none\"\n\t\t\t\t\ticon={ icon }\n\t\t\t\t\tvalue={ selectedOption.label }\n\t\t\t\t\tonPress={ openSubSheet }\n\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\taccessibilityLabel={ sprintf(\n\t\t\t\t\t\t// translators:  %1$s: Select control button label e.g. \"Button width\". %2$s: Select control option value e.g: \"Auto, 25%\".\n\t\t\t\t\t\t__( '%1$s. Currently selected: %2$s' ),\n\t\t\t\t\t\tlabel,\n\t\t\t\t\t\tselectedOption.label\n\t\t\t\t\t) }\n\t\t\t\t\taccessibilityHint={ sprintf(\n\t\t\t\t\t\t// translators: %s: Select control button label e.g. \"Button width\"\n\t\t\t\t\t\t__( 'Navigates to select %s' ),\n\t\t\t\t\t\tlabel\n\t\t\t\t\t) }\n\t\t\t\t\tdisabled={ disabled }\n\t\t\t\t>\n\t\t\t\t\t{ disabled ? null : <Icon icon={ chevronRight } /> }\n\t\t\t\t</BottomSheet.Cell>\n\t\t\t}\n\t\t\tshowSheet={ showSubSheet }\n\t\t>\n\t\t\t<>\n\t\t\t\t<BottomSheet.NavBar>\n\t\t\t\t\t<BottomSheet.NavBar.BackButton onPress={ goBack } />\n\t\t\t\t\t<BottomSheet.NavBar.Heading>\n\t\t\t\t\t\t{ label }\n\t\t\t\t\t</BottomSheet.NavBar.Heading>\n\t\t\t\t</BottomSheet.NavBar>\n\t\t\t\t<View style={ styles.selectControl }>\n\t\t\t\t\t{ items.map( ( item, index ) => (\n\t\t\t\t\t\t<BottomSheet.Cell\n\t\t\t\t\t\t\tcustomActionButton\n\t\t\t\t\t\t\tseparatorType=\"none\"\n\t\t\t\t\t\t\tlabel={ item.label }\n\t\t\t\t\t\t\ticon={ item.icon }\n\t\t\t\t\t\t\tonPress={ onChangeValue( item.value ) }\n\t\t\t\t\t\t\tleftAlign={ true }\n\t\t\t\t\t\t\tkey={ index }\n\t\t\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\t\t\taccessibilityLabel={\n\t\t\t\t\t\t\t\titem.value === selectedValue\n\t\t\t\t\t\t\t\t\t? sprintf(\n\t\t\t\t\t\t\t\t\t\t\t// translators: %s: Select control option value e.g: \"Auto, 25%\".\n\t\t\t\t\t\t\t\t\t\t\t__( 'Selected: %s' ),\n\t\t\t\t\t\t\t\t\t\t\titem.label\n\t\t\t\t\t\t\t\t\t  )\n\t\t\t\t\t\t\t\t\t: item.label\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\taccessibilityHint={ __( 'Double tap to select' ) }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ item.value === selectedValue && (\n\t\t\t\t\t\t\t\t<Icon icon={ check }></Icon>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</BottomSheet.Cell>\n\t\t\t\t\t) ) }\n\t\t\t\t</View>\n\t\t\t</>\n\t\t</BottomSheet.SubSheet>\n\t);\n};\n\nexport default BottomSheetSelectControl;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AAIA,IAAAM,MAAA,GAAAC,sBAAA,CAAAP,OAAA;AAhBA;AACA;AACA;;AAGA;AACA;AACA;;AAMA;AACA;AACA;;AAGA,MAAMQ,wBAAwB,GAAGA,CAAE;EAClCC,KAAK;EACLC,IAAI;EACJC,OAAO,EAAEC,KAAK;EACdC,QAAQ;EACRC,KAAK,EAAEC,aAAa;EACpBC;AACD,CAAC,KAAM;EACN,MAAM,CAAEC,YAAY,EAAEC,eAAe,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAC3D,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAElC,MAAMC,aAAa,GAAKR,KAAK,IAAM;IAClC,OAAO,MAAM;MACZS,MAAM,CAAC,CAAC;MACRV,QAAQ,CAAEC,KAAM,CAAC;IAClB,CAAC;EACF,CAAC;EAED,MAAMU,cAAc,GAAGZ,KAAK,CAACa,IAAI,CAC9BC,MAAM,IAAMA,MAAM,CAACZ,KAAK,KAAKC,aAChC,CAAC;EAED,MAAMQ,MAAM,GAAGA,CAAA,KAAM;IACpBL,eAAe,CAAE,KAAM,CAAC;IACxBE,UAAU,CAACG,MAAM,CAAC,CAAC;EACpB,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IAC1BP,UAAU,CAACQ,QAAQ,CAAEC,uBAAW,CAACC,QAAQ,CAACC,UAAW,CAAC;IACtDb,eAAe,CAAE,IAAK,CAAC;EACxB,CAAC;EAED,OACC,IAAAc,MAAA,CAAAC,aAAA,EAAC5B,WAAA,CAAAwB,WAAW,CAACC,QAAQ;IACpBI,gBAAgB,EACf,IAAAF,MAAA,CAAAC,aAAA,EAAC5B,WAAA,CAAAwB,WAAW,CAACM,IAAI;MAChB1B,KAAK,EAAGA,KAAO;MACf2B,aAAa,EAAC,MAAM;MACpB1B,IAAI,EAAGA,IAAM;MACbI,KAAK,EAAGU,cAAc,CAACf,KAAO;MAC9B4B,OAAO,EAAGV,YAAc;MACxBW,iBAAiB,EAAG,QAAU;MAC9BC,kBAAkB,EAAG,IAAAC,aAAO;MAC3B;MACA,IAAAC,QAAE,EAAE,gCAAiC,CAAC,EACtChC,KAAK,EACLe,cAAc,CAACf,KAChB,CAAG;MACHiC,iBAAiB,EAAG,IAAAF,aAAO;MAC1B;MACA,IAAAC,QAAE,EAAE,wBAAyB,CAAC,EAC9BhC,KACD,CAAG;MACHO,QAAQ,EAAGA;IAAU,GAEnBA,QAAQ,GAAG,IAAI,GAAG,IAAAgB,MAAA,CAAAC,aAAA,EAAC9B,MAAA,CAAAwC,IAAI;MAACjC,IAAI,EAAGkC;IAAc,CAAE,CAChC,CAClB;IACDC,SAAS,EAAG5B;EAAc,GAE1B,IAAAe,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAc,QAAA,QACC,IAAAd,MAAA,CAAAC,aAAA,EAAC5B,WAAA,CAAAwB,WAAW,CAACkB,MAAM,QAClB,IAAAf,MAAA,CAAAC,aAAA,EAAC5B,WAAA,CAAAwB,WAAW,CAACkB,MAAM,CAACC,UAAU;IAACX,OAAO,EAAGd;EAAQ,CAAE,CAAC,EACpD,IAAAS,MAAA,CAAAC,aAAA,EAAC5B,WAAA,CAAAwB,WAAW,CAACkB,MAAM,CAACE,OAAO,QACxBxC,KACyB,CACT,CAAC,EACrB,IAAAuB,MAAA,CAAAC,aAAA,EAAClC,YAAA,CAAAmD,IAAI;IAACC,KAAK,EAAGC,cAAM,CAACC;EAAe,GACjCzC,KAAK,CAAC0C,GAAG,CAAE,CAAEC,IAAI,EAAEC,KAAK,KACzB,IAAAxB,MAAA,CAAAC,aAAA,EAAC5B,WAAA,CAAAwB,WAAW,CAACM,IAAI;IAChBsB,kBAAkB;IAClBrB,aAAa,EAAC,MAAM;IACpB3B,KAAK,EAAG8C,IAAI,CAAC9C,KAAO;IACpBC,IAAI,EAAG6C,IAAI,CAAC7C,IAAM;IAClB2B,OAAO,EAAGf,aAAa,CAAEiC,IAAI,CAACzC,KAAM,CAAG;IACvC4C,SAAS,EAAG,IAAM;IAClBC,GAAG,EAAGH,KAAO;IACblB,iBAAiB,EAAG,QAAU;IAC9BC,kBAAkB,EACjBgB,IAAI,CAACzC,KAAK,KAAKC,aAAa,GACzB,IAAAyB,aAAO;IACP;IACA,IAAAC,QAAE,EAAE,cAAe,CAAC,EACpBc,IAAI,CAAC9C,KACL,CAAC,GACD8C,IAAI,CAAC9C,KACR;IACDiC,iBAAiB,EAAG,IAAAD,QAAE,EAAE,sBAAuB;EAAG,GAEhDc,IAAI,CAACzC,KAAK,KAAKC,aAAa,IAC7B,IAAAiB,MAAA,CAAAC,aAAA,EAAC9B,MAAA,CAAAwC,IAAI;IAACjC,IAAI,EAAGkD;EAAO,CAAO,CAEX,CACjB,CACG,CACL,CACmB,CAAC;AAEzB,CAAC;AAAC,IAAAC,QAAA,GAEarD,wBAAwB;AAAAsD,OAAA,CAAAC,OAAA,GAAAF,QAAA"}