{"version": 3, "names": ["_element", "require", "_icons", "_i18n", "_button", "_interopRequireDefault", "_colorPalette", "_colorIndicator", "_icon", "_hStack", "_compose", "ColorOption", "label", "value", "colors", "disableCustomColors", "enableAlpha", "onChange", "isOpen", "setIsOpen", "useState", "idRoot", "useInstanceId", "labelId", "contentId", "_react", "createElement", "Fragment", "default", "className", "onClick", "prev", "HStack", "justify", "spacing", "colorValue", "icon", "swatch", "id", "role", "__", "clearable", "ColorListPicker", "labels", "map", "index", "key", "newColor", "newColors", "slice", "_default", "exports"], "sources": ["@wordpress/components/src/duotone-picker/color-list-picker/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useState } from '@wordpress/element';\nimport { swatch } from '@wordpress/icons';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport Button from '../../button';\nimport ColorPalette from '../../color-palette';\nimport ColorIndicator from '../../color-indicator';\nimport Icon from '../../icon';\nimport { HStack } from '../../h-stack';\nimport type { ColorListPickerProps, ColorOptionProps } from './types';\nimport { useInstanceId } from '@wordpress/compose';\n\nfunction ColorOption( {\n\tlabel,\n\tvalue,\n\tcolors,\n\tdisableCustomColors,\n\tenableAlpha,\n\tonChange,\n}: ColorOptionProps ) {\n\tconst [ isOpen, setIsOpen ] = useState( false );\n\tconst idRoot = useInstanceId( ColorOption, 'color-list-picker-option' );\n\tconst labelId = `${ idRoot }__label`;\n\tconst contentId = `${ idRoot }__content`;\n\n\treturn (\n\t\t<>\n\t\t\t<Button\n\t\t\t\tclassName=\"components-color-list-picker__swatch-button\"\n\t\t\t\tonClick={ () => setIsOpen( ( prev ) => ! prev ) }\n\t\t\t\taria-expanded={ isOpen }\n\t\t\t\taria-controls={ contentId }\n\t\t\t>\n\t\t\t\t<HStack justify=\"flex-start\" spacing={ 2 }>\n\t\t\t\t\t{ value ? (\n\t\t\t\t\t\t<ColorIndicator\n\t\t\t\t\t\t\tcolorValue={ value }\n\t\t\t\t\t\t\tclassName=\"components-color-list-picker__swatch-color\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<Icon icon={ swatch } />\n\t\t\t\t\t) }\n\t\t\t\t\t<span id={ labelId }>{ label }</span>\n\t\t\t\t</HStack>\n\t\t\t</Button>\n\t\t\t<div\n\t\t\t\trole=\"group\"\n\t\t\t\tid={ contentId }\n\t\t\t\taria-labelledby={ labelId }\n\t\t\t\taria-hidden={ ! isOpen }\n\t\t\t>\n\t\t\t\t{ isOpen && (\n\t\t\t\t\t<ColorPalette\n\t\t\t\t\t\taria-label={ __( 'Color options' ) }\n\t\t\t\t\t\tclassName=\"components-color-list-picker__color-picker\"\n\t\t\t\t\t\tcolors={ colors }\n\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\tclearable={ false }\n\t\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\t\tdisableCustomColors={ disableCustomColors }\n\t\t\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t</div>\n\t\t</>\n\t);\n}\n\nfunction ColorListPicker( {\n\tcolors,\n\tlabels,\n\tvalue = [],\n\tdisableCustomColors,\n\tenableAlpha,\n\tonChange,\n}: ColorListPickerProps ) {\n\treturn (\n\t\t<div className=\"components-color-list-picker\">\n\t\t\t{ labels.map( ( label, index ) => (\n\t\t\t\t<ColorOption\n\t\t\t\t\tkey={ index }\n\t\t\t\t\tlabel={ label }\n\t\t\t\t\tvalue={ value[ index ] }\n\t\t\t\t\tcolors={ colors }\n\t\t\t\t\tdisableCustomColors={ disableCustomColors }\n\t\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t\t\tonChange={ ( newColor ) => {\n\t\t\t\t\t\tconst newColors: ( string | undefined )[] =\n\t\t\t\t\t\t\tvalue.slice();\n\t\t\t\t\t\tnewColors[ index ] = newColor;\n\t\t\t\t\t\tonChange( newColors );\n\t\t\t\t\t} }\n\t\t\t\t/>\n\t\t\t) ) }\n\t\t</div>\n\t);\n}\n\nexport default ColorListPicker;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAKA,IAAAG,OAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,eAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,KAAA,GAAAH,sBAAA,CAAAJ,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AAEA,IAAAS,QAAA,GAAAT,OAAA;AAhBA;AACA;AACA;;AAKA;AACA;AACA;;AASA,SAASU,WAAWA,CAAE;EACrBC,KAAK;EACLC,KAAK;EACLC,MAAM;EACNC,mBAAmB;EACnBC,WAAW;EACXC;AACiB,CAAC,EAAG;EACrB,MAAM,CAAEC,MAAM,EAAEC,SAAS,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAC/C,MAAMC,MAAM,GAAG,IAAAC,sBAAa,EAAEX,WAAW,EAAE,0BAA2B,CAAC;EACvE,MAAMY,OAAO,GAAI,GAAGF,MAAQ,SAAQ;EACpC,MAAMG,SAAS,GAAI,GAAGH,MAAQ,WAAU;EAExC,OACC,IAAAI,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAACtB,OAAA,CAAAwB,OAAM;IACNC,SAAS,EAAC,6CAA6C;IACvDC,OAAO,EAAGA,CAAA,KAAMX,SAAS,CAAIY,IAAI,IAAM,CAAEA,IAAK,CAAG;IACjD,iBAAgBb,MAAQ;IACxB,iBAAgBM;EAAW,GAE3B,IAAAC,MAAA,CAAAC,aAAA,EAACjB,OAAA,CAAAuB,MAAM;IAACC,OAAO,EAAC,YAAY;IAACC,OAAO,EAAG;EAAG,GACvCrB,KAAK,GACN,IAAAY,MAAA,CAAAC,aAAA,EAACnB,eAAA,CAAAqB,OAAc;IACdO,UAAU,EAAGtB,KAAO;IACpBgB,SAAS,EAAC;EAA4C,CACtD,CAAC,GAEF,IAAAJ,MAAA,CAAAC,aAAA,EAAClB,KAAA,CAAAoB,OAAI;IAACQ,IAAI,EAAGC;EAAQ,CAAE,CACvB,EACD,IAAAZ,MAAA,CAAAC,aAAA;IAAMY,EAAE,EAAGf;EAAS,GAAGX,KAAa,CAC7B,CACD,CAAC,EACT,IAAAa,MAAA,CAAAC,aAAA;IACCa,IAAI,EAAC,OAAO;IACZD,EAAE,EAAGd,SAAW;IAChB,mBAAkBD,OAAS;IAC3B,eAAc,CAAEL;EAAQ,GAEtBA,MAAM,IACP,IAAAO,MAAA,CAAAC,aAAA,EAACpB,aAAA,CAAAsB,OAAY;IACZ,cAAa,IAAAY,QAAE,EAAE,eAAgB,CAAG;IACpCX,SAAS,EAAC,4CAA4C;IACtDf,MAAM,EAAGA,MAAQ;IACjBD,KAAK,EAAGA,KAAO;IACf4B,SAAS,EAAG,KAAO;IACnBxB,QAAQ,EAAGA,QAAU;IACrBF,mBAAmB,EAAGA,mBAAqB;IAC3CC,WAAW,EAAGA;EAAa,CAC3B,CAEE,CACJ,CAAC;AAEL;AAEA,SAAS0B,eAAeA,CAAE;EACzB5B,MAAM;EACN6B,MAAM;EACN9B,KAAK,GAAG,EAAE;EACVE,mBAAmB;EACnBC,WAAW;EACXC;AACqB,CAAC,EAAG;EACzB,OACC,IAAAQ,MAAA,CAAAC,aAAA;IAAKG,SAAS,EAAC;EAA8B,GAC1Cc,MAAM,CAACC,GAAG,CAAE,CAAEhC,KAAK,EAAEiC,KAAK,KAC3B,IAAApB,MAAA,CAAAC,aAAA,EAACf,WAAW;IACXmC,GAAG,EAAGD,KAAO;IACbjC,KAAK,EAAGA,KAAO;IACfC,KAAK,EAAGA,KAAK,CAAEgC,KAAK,CAAI;IACxB/B,MAAM,EAAGA,MAAQ;IACjBC,mBAAmB,EAAGA,mBAAqB;IAC3CC,WAAW,EAAGA,WAAa;IAC3BC,QAAQ,EAAK8B,QAAQ,IAAM;MAC1B,MAAMC,SAAmC,GACxCnC,KAAK,CAACoC,KAAK,CAAC,CAAC;MACdD,SAAS,CAAEH,KAAK,CAAE,GAAGE,QAAQ;MAC7B9B,QAAQ,CAAE+B,SAAU,CAAC;IACtB;EAAG,CACH,CACA,CACE,CAAC;AAER;AAAC,IAAAE,QAAA,GAEcR,eAAe;AAAAS,OAAA,CAAAvB,OAAA,GAAAsB,QAAA"}