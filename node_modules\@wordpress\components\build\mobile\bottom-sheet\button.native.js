"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const BottomSheetButton = ({
  onPress,
  disabled,
  text,
  color
}) => (0, _react.createElement)(_reactNative.TouchableOpacity, {
  accessible: true,
  onPress: onPress,
  disabled: disabled
}, (0, _react.createElement)(_reactNative.View, {
  style: {
    flexDirection: 'row',
    justifyContent: 'center'
  }
}, (0, _react.createElement)(_reactNative.Text, {
  style: {
    ..._styles.default.buttonText,
    color
  }
}, text)));
var _default = BottomSheetButton;
exports.default = _default;
//# sourceMappingURL=button.native.js.map