"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.FlexBlock = void 0;
var _react = require("react");
var _context = require("../../context");
var _view = require("../../view");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedFlexBlock(props, forwardedRef) {
  const flexBlockProps = (0, _hook.useFlexBlock)(props);
  return (0, _react.createElement)(_view.View, {
    ...flexBlockProps,
    ref: forwardedRef
  });
}

/**
 * `FlexBlock` is a primitive layout component that adaptively resizes content
 * within layout containers like `Flex`.
 *
 * ```jsx
 * import { Flex, FlexBlock } from '@wordpress/components';
 *
 * function Example() {
 *   return (
 *     <Flex>
 *       <FlexBlock>...</FlexBlock>
 *     </Flex>
 *   );
 * }
 * ```
 */
const FlexBlock = (0, _context.contextConnect)(UnconnectedFlexBlock, 'FlexBlock');
exports.FlexBlock = FlexBlock;
var _default = FlexBlock;
exports.default = _default;
//# sourceMappingURL=component.js.map