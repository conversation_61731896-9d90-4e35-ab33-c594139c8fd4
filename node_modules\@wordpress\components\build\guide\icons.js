"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PageControlIcon = void 0;
var _react = require("react");
var _primitives = require("@wordpress/primitives");
/**
 * WordPress dependencies
 */

const PageControlIcon = () => (0, _react.createElement)(_primitives.SVG, {
  width: "8",
  height: "8",
  fill: "none",
  xmlns: "http://www.w3.org/2000/svg"
}, (0, _react.createElement)(_primitives.Circle, {
  cx: "4",
  cy: "4",
  r: "4"
}));
exports.PageControlIcon = PageControlIcon;
//# sourceMappingURL=icons.js.map