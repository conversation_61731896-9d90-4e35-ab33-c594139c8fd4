{"version": 3, "names": ["_react", "require", "_space", "_utils", "_EMOTION_STRINGIFIED_CSS_ERROR__", "MARGIN_DIRECTIONS", "vertical", "start", "end", "horizontal", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "margin", "marginStart", "marginEnd", "css", "rtl", "space", "process", "env", "NODE_ENV", "_ref", "name", "styles", "map", "toString", "renderDisplay", "undefined", "renderBorder", "renderSize", "height", "width", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_base", "default", "target", "label", "exports"], "sources": ["@wordpress/components/src/divider/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { space } from '../utils/space';\nimport { rtl } from '../utils';\nimport type { DividerProps } from './types';\n\nconst MARGIN_DIRECTIONS: Record<\n\tNonNullable< DividerProps[ 'orientation' ] >,\n\tRecord< 'start' | 'end', string >\n> = {\n\tvertical: {\n\t\tstart: 'marginLeft',\n\t\tend: 'marginRight',\n\t},\n\thorizontal: {\n\t\tstart: 'marginTop',\n\t\tend: 'marginBottom',\n\t},\n};\n\n// Renders the correct margins given the Divider's `orientation` and the writing direction.\n// When both the generic `margin` and the specific `marginStart|marginEnd` props are defined,\n// the latter will take priority.\nconst renderMargin = ( {\n\t'aria-orientation': orientation = 'horizontal',\n\tmargin,\n\tmarginStart,\n\tmarginEnd,\n}: DividerProps ) =>\n\tcss(\n\t\trtl( {\n\t\t\t[ MARGIN_DIRECTIONS[ orientation ].start ]: space(\n\t\t\t\tmarginStart ?? margin\n\t\t\t),\n\t\t\t[ MARGIN_DIRECTIONS[ orientation ].end ]: space(\n\t\t\t\tmarginEnd ?? margin\n\t\t\t),\n\t\t} )()\n\t);\n\nconst renderDisplay = ( {\n\t'aria-orientation': orientation = 'horizontal',\n}: DividerProps ) => {\n\treturn orientation === 'vertical'\n\t\t? css( { display: 'inline' } )\n\t\t: undefined;\n};\n\nconst renderBorder = ( {\n\t'aria-orientation': orientation = 'horizontal',\n}: DividerProps ) => {\n\treturn css( {\n\t\t[ orientation === 'vertical' ? 'borderRight' : 'borderBottom' ]:\n\t\t\t'1px solid currentColor',\n\t} );\n};\n\nconst renderSize = ( {\n\t'aria-orientation': orientation = 'horizontal',\n}: DividerProps ) =>\n\tcss( {\n\t\theight: orientation === 'vertical' ? 'auto' : 0,\n\t\twidth: orientation === 'vertical' ? 0 : 'auto',\n\t} );\n\nexport const DividerView = styled.hr< DividerProps >`\n\tborder: 0;\n\tmargin: 0;\n\n\t${ renderDisplay }\n\t${ renderBorder }\n\t${ renderSize }\n\t${ renderMargin }\n`;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAA+B,SAAAG,iCAAA;AAG/B,MAAMC,iBAGL,GAAG;EACHC,QAAQ,EAAE;IACTC,KAAK,EAAE,YAAY;IACnBC,GAAG,EAAE;EACN,CAAC;EACDC,UAAU,EAAE;IACXF,KAAK,EAAE,WAAW;IAClBC,GAAG,EAAE;EACN;AACD,CAAC;;AAED;AACA;AACA;AACA,MAAME,YAAY,GAAGA,CAAE;EACtB,kBAAkB,EAAEC,WAAW,GAAG,YAAY;EAC9CC,MAAM;EACNC,WAAW;EACXC;AACa,CAAC,kBACd,IAAAC,UAAG,EACF,IAAAC,UAAG,EAAE;EACJ,CAAEX,iBAAiB,CAAEM,WAAW,CAAE,CAACJ,KAAK,GAAI,IAAAU,YAAK,EAChDJ,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAID,MAChB,CAAC;EACD,CAAEP,iBAAiB,CAAEM,WAAW,CAAE,CAACH,GAAG,GAAI,IAAAS,YAAK,EAC9CH,SAAS,aAATA,SAAS,cAATA,SAAS,GAAIF,MACd;AACD,CAAE,CAAC,CAAC,CAAC,EAAAM,OAAA,CAAAC,GAAA,CAAAC,QAAA,iDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,05FACN,CAAC;AAAC,IAAAC,IAAA,GAAAH,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAE,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAArB;AAAA;AAEH,MAAMsB,aAAa,GAAGA,CAAE;EACvB,kBAAkB,EAAEf,WAAW,GAAG;AACrB,CAAC,KAAM;EACpB,OAAOA,WAAW,KAAK,UAAU,GAAAU,IAAA,GAE9BM,SAAS;AACb,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAE;EACtB,kBAAkB,EAAEjB,WAAW,GAAG;AACrB,CAAC,KAAM;EACpB,oBAAO,IAAAI,UAAG,EAAE;IACX,CAAEJ,WAAW,KAAK,UAAU,GAAG,aAAa,GAAG,cAAc,GAC5D;EACF,CAAC,EAAAO,OAAA,CAAAC,GAAA,CAAAC,QAAA,iDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,05FAAC,CAAC;AACJ,CAAC;AAED,MAAMS,UAAU,GAAGA,CAAE;EACpB,kBAAkB,EAAElB,WAAW,GAAG;AACrB,CAAC,kBACd,IAAAI,UAAG,EAAE;EACJe,MAAM,EAAEnB,WAAW,KAAK,UAAU,GAAG,MAAM,GAAG,CAAC;EAC/CoB,KAAK,EAAEpB,WAAW,KAAK,UAAU,GAAG,CAAC,GAAG;AACzC,CAAC,EAAAO,OAAA,CAAAC,GAAA,CAAAC,QAAA,+CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,05FAAC,CAAC;AAEG,MAAMY,WAAW,OAAAC,KAAA,CAAAC,OAAA,QAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAe,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,yBAIpBV,aAAa,OACbE,YAAY,OACZC,UAAU,OACVnB,YAAY,SAAAQ,OAAA,CAAAC,GAAA,CAAAC,QAAA,45FACf;AAACiB,OAAA,CAAAL,WAAA,GAAAA,WAAA"}