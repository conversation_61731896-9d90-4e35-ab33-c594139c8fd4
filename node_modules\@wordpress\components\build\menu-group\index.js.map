{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_compose", "MenuGroup", "props", "children", "className", "label", "hideSeparator", "instanceId", "useInstanceId", "Children", "count", "labelId", "classNames", "classnames", "_react", "createElement", "id", "role", "undefined", "_default", "exports", "default"], "sources": ["@wordpress/components/src/menu-group/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { Children } from '@wordpress/element';\nimport { useInstanceId } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport type { MenuGroupProps } from './types';\n\n/**\n * `MenuGroup` wraps a series of related `MenuItem` components into a common\n * section.\n *\n * ```jsx\n * import { MenuGroup, MenuItem } from '@wordpress/components';\n *\n * const MyMenuGroup = () => (\n *   <MenuGroup label=\"Settings\">\n *     <MenuItem>Setting 1</MenuItem>\n *     <MenuItem>Setting 2</MenuItem>\n *   </MenuGroup>\n * );\n * ```\n */\nexport function MenuGroup( props: MenuGroupProps ) {\n\tconst { children, className = '', label, hideSeparator } = props;\n\tconst instanceId = useInstanceId( MenuGroup );\n\n\tif ( ! Children.count( children ) ) {\n\t\treturn null;\n\t}\n\n\tconst labelId = `components-menu-group-label-${ instanceId }`;\n\tconst classNames = classnames( className, 'components-menu-group', {\n\t\t'has-hidden-separator': hideSeparator,\n\t} );\n\n\treturn (\n\t\t<div className={ classNames }>\n\t\t\t{ label && (\n\t\t\t\t<div\n\t\t\t\t\tclassName=\"components-menu-group__label\"\n\t\t\t\t\tid={ labelId }\n\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t>\n\t\t\t\t\t{ label }\n\t\t\t\t</div>\n\t\t\t) }\n\t\t\t<div role=\"group\" aria-labelledby={ label ? labelId : undefined }>\n\t\t\t\t{ children }\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nexport default MenuGroup;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,SAASA,CAAEC,KAAqB,EAAG;EAClD,MAAM;IAAEC,QAAQ;IAAEC,SAAS,GAAG,EAAE;IAAEC,KAAK;IAAEC;EAAc,CAAC,GAAGJ,KAAK;EAChE,MAAMK,UAAU,GAAG,IAAAC,sBAAa,EAAEP,SAAU,CAAC;EAE7C,IAAK,CAAEQ,iBAAQ,CAACC,KAAK,CAAEP,QAAS,CAAC,EAAG;IACnC,OAAO,IAAI;EACZ;EAEA,MAAMQ,OAAO,GAAI,+BAA+BJ,UAAY,EAAC;EAC7D,MAAMK,UAAU,GAAG,IAAAC,mBAAU,EAAET,SAAS,EAAE,uBAAuB,EAAE;IAClE,sBAAsB,EAAEE;EACzB,CAAE,CAAC;EAEH,OACC,IAAAQ,MAAA,CAAAC,aAAA;IAAKX,SAAS,EAAGQ;EAAY,GAC1BP,KAAK,IACN,IAAAS,MAAA,CAAAC,aAAA;IACCX,SAAS,EAAC,8BAA8B;IACxCY,EAAE,EAAGL,OAAS;IACd,eAAY;EAAM,GAEhBN,KACE,CACL,EACD,IAAAS,MAAA,CAAAC,aAAA;IAAKE,IAAI,EAAC,OAAO;IAAC,mBAAkBZ,KAAK,GAAGM,OAAO,GAAGO;EAAW,GAC9Df,QACE,CACD,CAAC;AAER;AAAC,IAAAgB,QAAA,GAEclB,SAAS;AAAAmB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}