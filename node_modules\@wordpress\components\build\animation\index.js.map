{"version": 3, "names": ["_framerMotion", "require"], "sources": ["@wordpress/components/src/animation/index.tsx"], "sourcesContent": ["/**\n * Framer Motion is used to create animated, interactive interfaces. The package is roughly ~30kb so\n * this should ideally be loaded once across all Gutenberg packages. To give ourselves more flexibility\n * in trying animation options, we avoid making this public API.\n *\n * @see https://www.framer.com/docs/animation/\n */\n\n// eslint-disable-next-line no-restricted-imports\nexport {\n\tmotion as __unstableMotion,\n\tAnimatePresence as __unstableAnimatePresence,\n\tMotionContext as __unstableMotionContext,\n} from 'framer-motion';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AASA,IAAAA,aAAA,GAAAC,OAAA"}