{"version": 3, "names": ["_i18n", "require", "_date", "_tooltip", "_interopRequireDefault", "_styles", "TimeZone", "timezone", "getDateSettings", "userTimezoneOffset", "Date", "getTimezoneOffset", "Number", "offset", "offsetSymbol", "zoneAbbr", "abbr", "isNaN", "prettyTimezoneString", "string", "replace", "timezoneDetail", "__", "hasNoAdditionalTimezoneDetail", "trim", "length", "_react", "createElement", "className", "default", "placement", "text", "_default", "exports"], "sources": ["@wordpress/components/src/date-time/time/timezone.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { getSettings as getDateSettings } from '@wordpress/date';\n\n/**\n * Internal dependencies\n */\nimport Tooltip from '../../tooltip';\nimport { TimeZone as StyledComponent } from './styles';\n\n/**\n * Displays timezone information when user timezone is different from site\n * timezone.\n */\nconst TimeZone = () => {\n\tconst { timezone } = getDateSettings();\n\n\t// Convert timezone offset to hours.\n\tconst userTimezoneOffset = -1 * ( new Date().getTimezoneOffset() / 60 );\n\n\t// System timezone and user timezone match, nothing needed.\n\t// Compare as numbers because it comes over as string.\n\tif ( Number( timezone.offset ) === userTimezoneOffset ) {\n\t\treturn null;\n\t}\n\n\tconst offsetSymbol = Number( timezone.offset ) >= 0 ? '+' : '';\n\tconst zoneAbbr =\n\t\t'' !== timezone.abbr && isNaN( Number( timezone.abbr ) )\n\t\t\t? timezone.abbr\n\t\t\t: `UTC${ offsetSymbol }${ timezone.offset }`;\n\n\t// Replace underscore with space in strings like `America/Costa_Rica`.\n\tconst prettyTimezoneString = timezone.string.replace( '_', ' ' );\n\n\tconst timezoneDetail =\n\t\t'UTC' === timezone.string\n\t\t\t? __( 'Coordinated Universal Time' )\n\t\t\t: `(${ zoneAbbr }) ${ prettyTimezoneString }`;\n\n\t// When the prettyTimezoneString is empty, there is no additional timezone\n\t// detail information to show in a Tooltip.\n\tconst hasNoAdditionalTimezoneDetail =\n\t\tprettyTimezoneString.trim().length === 0;\n\n\treturn hasNoAdditionalTimezoneDetail ? (\n\t\t<StyledComponent className=\"components-datetime__timezone\">\n\t\t\t{ zoneAbbr }\n\t\t</StyledComponent>\n\t) : (\n\t\t<Tooltip placement=\"top\" text={ timezoneDetail }>\n\t\t\t<StyledComponent className=\"components-datetime__timezone\">\n\t\t\t\t{ zoneAbbr }\n\t\t\t</StyledComponent>\n\t\t</Tooltip>\n\t);\n};\n\nexport default TimeZone;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA,MAAMK,QAAQ,GAAGA,CAAA,KAAM;EACtB,MAAM;IAAEC;EAAS,CAAC,GAAG,IAAAC,iBAAe,EAAC,CAAC;;EAEtC;EACA,MAAMC,kBAAkB,GAAG,CAAC,CAAC,IAAK,IAAIC,IAAI,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAE;;EAEvE;EACA;EACA,IAAKC,MAAM,CAAEL,QAAQ,CAACM,MAAO,CAAC,KAAKJ,kBAAkB,EAAG;IACvD,OAAO,IAAI;EACZ;EAEA,MAAMK,YAAY,GAAGF,MAAM,CAAEL,QAAQ,CAACM,MAAO,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;EAC9D,MAAME,QAAQ,GACb,EAAE,KAAKR,QAAQ,CAACS,IAAI,IAAIC,KAAK,CAAEL,MAAM,CAAEL,QAAQ,CAACS,IAAK,CAAE,CAAC,GACrDT,QAAQ,CAACS,IAAI,GACZ,MAAMF,YAAc,GAAGP,QAAQ,CAACM,MAAQ,EAAC;;EAE9C;EACA,MAAMK,oBAAoB,GAAGX,QAAQ,CAACY,MAAM,CAACC,OAAO,CAAE,GAAG,EAAE,GAAI,CAAC;EAEhE,MAAMC,cAAc,GACnB,KAAK,KAAKd,QAAQ,CAACY,MAAM,GACtB,IAAAG,QAAE,EAAE,4BAA6B,CAAC,GACjC,IAAIP,QAAU,KAAKG,oBAAsB,EAAC;;EAE/C;EACA;EACA,MAAMK,6BAA6B,GAClCL,oBAAoB,CAACM,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC;EAEzC,OAAOF,6BAA6B,GACnC,IAAAG,MAAA,CAAAC,aAAA,EAACtB,OAAA,CAAAC,QAAe;IAACsB,SAAS,EAAC;EAA+B,GACvDb,QACc,CAAC,GAElB,IAAAW,MAAA,CAAAC,aAAA,EAACxB,QAAA,CAAA0B,OAAO;IAACC,SAAS,EAAC,KAAK;IAACC,IAAI,EAAGV;EAAgB,GAC/C,IAAAK,MAAA,CAAAC,aAAA,EAACtB,OAAA,CAAAC,QAAe;IAACsB,SAAS,EAAC;EAA+B,GACvDb,QACc,CACT,CACT;AACF,CAAC;AAAC,IAAAiB,QAAA,GAEa1B,QAAQ;AAAA2B,OAAA,CAAAJ,OAAA,GAAAG,QAAA"}