{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_compose", "_deprecated", "_button", "TIMEOUT", "ClipboardButton", "className", "children", "onCopy", "onFinishCopy", "text", "buttonProps", "deprecated", "since", "alternative", "timeoutId", "useRef", "ref", "useCopyToClipboard", "current", "clearTimeout", "setTimeout", "useEffect", "classes", "classnames", "focusOnCopyEventTarget", "event", "target", "focus", "_react", "createElement", "default"], "sources": ["@wordpress/components/src/clipboard-button/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { useRef, useEffect } from '@wordpress/element';\nimport { useCopyToClipboard } from '@wordpress/compose';\nimport deprecated from '@wordpress/deprecated';\n\n/**\n * Internal dependencies\n */\nimport Button from '../button';\nimport type { ClipboardButtonProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\nconst TIMEOUT = 4000;\n\nexport default function ClipboardButton( {\n\tclassName,\n\tchildren,\n\tonCopy,\n\tonFinishCopy,\n\ttext,\n\t...buttonProps\n}: WordPressComponentProps< ClipboardButtonProps, 'button', false > ) {\n\tdeprecated( 'wp.components.ClipboardButton', {\n\t\tsince: '5.8',\n\t\talternative: 'wp.compose.useCopyToClipboard',\n\t} );\n\n\tconst timeoutId = useRef< NodeJS.Timeout >();\n\tconst ref = useCopyToClipboard( text, () => {\n\t\tonCopy();\n\t\tif ( timeoutId.current ) {\n\t\t\tclearTimeout( timeoutId.current );\n\t\t}\n\n\t\tif ( onFinishCopy ) {\n\t\t\ttimeoutId.current = setTimeout( () => onFinishCopy(), TIMEOUT );\n\t\t}\n\t} );\n\n\tuseEffect( () => {\n\t\tif ( timeoutId.current ) {\n\t\t\tclearTimeout( timeoutId.current );\n\t\t}\n\t}, [] );\n\n\tconst classes = classnames( 'components-clipboard-button', className );\n\n\t// Workaround for inconsistent behavior in Safari, where <textarea> is not\n\t// the document.activeElement at the moment when the copy event fires.\n\t// This causes documentHasSelection() in the copy-handler component to\n\t// mistakenly override the ClipboardButton, and copy a serialized string\n\t// of the current block instead.\n\tconst focusOnCopyEventTarget: React.ClipboardEventHandler = ( event ) => {\n\t\t// @ts-expect-error: Should be currentTarget, but not changing because this component is deprecated.\n\t\tevent.target.focus();\n\t};\n\n\treturn (\n\t\t<Button\n\t\t\t{ ...buttonProps }\n\t\t\tclassName={ classes }\n\t\t\tref={ ref }\n\t\t\tonCopy={ focusOnCopyEventTarget }\n\t\t>\n\t\t\t{ children }\n\t\t</Button>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAKA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AAfA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;;AAKA,MAAMK,OAAO,GAAG,IAAI;AAEL,SAASC,eAAeA,CAAE;EACxCC,SAAS;EACTC,QAAQ;EACRC,MAAM;EACNC,YAAY;EACZC,IAAI;EACJ,GAAGC;AAC8D,CAAC,EAAG;EACrE,IAAAC,mBAAU,EAAE,+BAA+B,EAAE;IAC5CC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE;EACd,CAAE,CAAC;EAEH,MAAMC,SAAS,GAAG,IAAAC,eAAM,EAAmB,CAAC;EAC5C,MAAMC,GAAG,GAAG,IAAAC,2BAAkB,EAAER,IAAI,EAAE,MAAM;IAC3CF,MAAM,CAAC,CAAC;IACR,IAAKO,SAAS,CAACI,OAAO,EAAG;MACxBC,YAAY,CAAEL,SAAS,CAACI,OAAQ,CAAC;IAClC;IAEA,IAAKV,YAAY,EAAG;MACnBM,SAAS,CAACI,OAAO,GAAGE,UAAU,CAAE,MAAMZ,YAAY,CAAC,CAAC,EAAEL,OAAQ,CAAC;IAChE;EACD,CAAE,CAAC;EAEH,IAAAkB,kBAAS,EAAE,MAAM;IAChB,IAAKP,SAAS,CAACI,OAAO,EAAG;MACxBC,YAAY,CAAEL,SAAS,CAACI,OAAQ,CAAC;IAClC;EACD,CAAC,EAAE,EAAG,CAAC;EAEP,MAAMI,OAAO,GAAG,IAAAC,mBAAU,EAAE,6BAA6B,EAAElB,SAAU,CAAC;;EAEtE;EACA;EACA;EACA;EACA;EACA,MAAMmB,sBAAmD,GAAKC,KAAK,IAAM;IACxE;IACAA,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC;EACrB,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC3B,OAAA,CAAA4B,OAAM;IAAA,GACDpB,WAAW;IAChBL,SAAS,EAAGiB,OAAS;IACrBN,GAAG,EAAGA,GAAK;IACXT,MAAM,EAAGiB;EAAwB,GAE/BlB,QACK,CAAC;AAEX"}