{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_compose", "_element", "_icons", "_circularOptionPickerContext", "_button", "_v", "_tooltip", "UnforwardedOptionAsButton", "props", "forwardedRef", "isPressed", "additionalProps", "_react", "createElement", "default", "ref", "OptionAsButton", "forwardRef", "UnforwardedOptionAsOption", "id", "isSelected", "compositeStore", "activeId", "useState", "setActiveId", "CompositeItem", "render", "role", "store", "OptionAsOption", "Option", "className", "selectedIconProps", "tooltipText", "baseId", "useContext", "CircularOptionPickerContext", "useInstanceId", "commonProps", "optionControl", "classnames", "text", "Icon", "icon", "check"], "sources": ["@wordpress/components/src/circular-option-picker/circular-option-picker-option.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { forwardRef, useContext } from '@wordpress/element';\nimport { Icon, check } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport { CircularOptionPickerContext } from './circular-option-picker-context';\nimport Button from '../button';\nimport { CompositeItem } from '../composite/v2';\nimport Tooltip from '../tooltip';\nimport type { OptionProps, CircularOptionPickerCompositeStore } from './types';\n\nfunction UnforwardedOptionAsButton(\n\tprops: {\n\t\tid?: string;\n\t\tclassName?: string;\n\t\tisPressed?: boolean;\n\t},\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst { isPressed, ...additionalProps } = props;\n\treturn (\n\t\t<Button\n\t\t\t{ ...additionalProps }\n\t\t\taria-pressed={ isPressed }\n\t\t\tref={ forwardedRef }\n\t\t/>\n\t);\n}\n\nconst OptionAsButton = forwardRef( UnforwardedOptionAsButton );\n\nfunction UnforwardedOptionAsOption(\n\tprops: {\n\t\tid: string;\n\t\tclassName?: string;\n\t\tisSelected?: boolean;\n\t\tcompositeStore: CircularOptionPickerCompositeStore;\n\t},\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst { id, isSelected, compositeStore, ...additionalProps } = props;\n\tconst activeId = compositeStore.useState( 'activeId' );\n\n\tif ( isSelected && ! activeId ) {\n\t\tcompositeStore.setActiveId( id );\n\t}\n\n\treturn (\n\t\t<CompositeItem\n\t\t\trender={\n\t\t\t\t<Button\n\t\t\t\t\t{ ...additionalProps }\n\t\t\t\t\trole=\"option\"\n\t\t\t\t\taria-selected={ !! isSelected }\n\t\t\t\t\tref={ forwardedRef }\n\t\t\t\t/>\n\t\t\t}\n\t\t\tstore={ compositeStore }\n\t\t\tid={ id }\n\t\t/>\n\t);\n}\n\nconst OptionAsOption = forwardRef( UnforwardedOptionAsOption );\n\nexport function Option( {\n\tclassName,\n\tisSelected,\n\tselectedIconProps = {},\n\ttooltipText,\n\t...additionalProps\n}: OptionProps ) {\n\tconst { baseId, compositeStore } = useContext(\n\t\tCircularOptionPickerContext\n\t);\n\tconst id = useInstanceId(\n\t\tOption,\n\t\tbaseId || 'components-circular-option-picker__option'\n\t);\n\n\tconst commonProps = {\n\t\tid,\n\t\tclassName: 'components-circular-option-picker__option',\n\t\t...additionalProps,\n\t};\n\n\tconst optionControl = compositeStore ? (\n\t\t<OptionAsOption\n\t\t\t{ ...commonProps }\n\t\t\tcompositeStore={ compositeStore }\n\t\t\tisSelected={ isSelected }\n\t\t/>\n\t) : (\n\t\t<OptionAsButton { ...commonProps } isPressed={ isSelected } />\n\t);\n\n\treturn (\n\t\t<div\n\t\t\tclassName={ classnames(\n\t\t\t\tclassName,\n\t\t\t\t'components-circular-option-picker__option-wrapper'\n\t\t\t) }\n\t\t>\n\t\t\t{ tooltipText ? (\n\t\t\t\t<Tooltip text={ tooltipText }>{ optionControl }</Tooltip>\n\t\t\t) : (\n\t\t\t\toptionControl\n\t\t\t) }\n\t\t\t{ isSelected && <Icon icon={ check } { ...selectedIconProps } /> }\n\t\t</div>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAKA,IAAAI,4BAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,EAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAR,sBAAA,CAAAC,OAAA;AAnBA;AACA;AACA;;AAIA;AACA;AACA;;AAKA;AACA;AACA;;AAOA,SAASQ,yBAAyBA,CACjCC,KAIC,EACDC,YAAiC,EAChC;EACD,MAAM;IAAEC,SAAS;IAAE,GAAGC;EAAgB,CAAC,GAAGH,KAAK;EAC/C,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACT,OAAA,CAAAU,OAAM;IAAA,GACDH,eAAe;IACpB,gBAAeD,SAAW;IAC1BK,GAAG,EAAGN;EAAc,CACpB,CAAC;AAEJ;AAEA,MAAMO,cAAc,GAAG,IAAAC,mBAAU,EAAEV,yBAA0B,CAAC;AAE9D,SAASW,yBAAyBA,CACjCV,KAKC,EACDC,YAAiC,EAChC;EACD,MAAM;IAAEU,EAAE;IAAEC,UAAU;IAAEC,cAAc;IAAE,GAAGV;EAAgB,CAAC,GAAGH,KAAK;EACpE,MAAMc,QAAQ,GAAGD,cAAc,CAACE,QAAQ,CAAE,UAAW,CAAC;EAEtD,IAAKH,UAAU,IAAI,CAAEE,QAAQ,EAAG;IAC/BD,cAAc,CAACG,WAAW,CAAEL,EAAG,CAAC;EACjC;EAEA,OACC,IAAAP,MAAA,CAAAC,aAAA,EAACR,EAAA,CAAAoB,aAAa;IACbC,MAAM,EACL,IAAAd,MAAA,CAAAC,aAAA,EAACT,OAAA,CAAAU,OAAM;MAAA,GACDH,eAAe;MACpBgB,IAAI,EAAC,QAAQ;MACb,iBAAgB,CAAC,CAAEP,UAAY;MAC/BL,GAAG,EAAGN;IAAc,CACpB,CACD;IACDmB,KAAK,EAAGP,cAAgB;IACxBF,EAAE,EAAGA;EAAI,CACT,CAAC;AAEJ;AAEA,MAAMU,cAAc,GAAG,IAAAZ,mBAAU,EAAEC,yBAA0B,CAAC;AAEvD,SAASY,MAAMA,CAAE;EACvBC,SAAS;EACTX,UAAU;EACVY,iBAAiB,GAAG,CAAC,CAAC;EACtBC,WAAW;EACX,GAAGtB;AACS,CAAC,EAAG;EAChB,MAAM;IAAEuB,MAAM;IAAEb;EAAe,CAAC,GAAG,IAAAc,mBAAU,EAC5CC,wDACD,CAAC;EACD,MAAMjB,EAAE,GAAG,IAAAkB,sBAAa,EACvBP,MAAM,EACNI,MAAM,IAAI,2CACX,CAAC;EAED,MAAMI,WAAW,GAAG;IACnBnB,EAAE;IACFY,SAAS,EAAE,2CAA2C;IACtD,GAAGpB;EACJ,CAAC;EAED,MAAM4B,aAAa,GAAGlB,cAAc,GACnC,IAAAT,MAAA,CAAAC,aAAA,EAACgB,cAAc;IAAA,GACTS,WAAW;IAChBjB,cAAc,EAAGA,cAAgB;IACjCD,UAAU,EAAGA;EAAY,CACzB,CAAC,GAEF,IAAAR,MAAA,CAAAC,aAAA,EAACG,cAAc;IAAA,GAAMsB,WAAW;IAAG5B,SAAS,EAAGU;EAAY,CAAE,CAC7D;EAED,OACC,IAAAR,MAAA,CAAAC,aAAA;IACCkB,SAAS,EAAG,IAAAS,mBAAU,EACrBT,SAAS,EACT,mDACD;EAAG,GAEDE,WAAW,GACZ,IAAArB,MAAA,CAAAC,aAAA,EAACP,QAAA,CAAAQ,OAAO;IAAC2B,IAAI,EAAGR;EAAa,GAAGM,aAAwB,CAAC,GAEzDA,aACA,EACCnB,UAAU,IAAI,IAAAR,MAAA,CAAAC,aAAA,EAACX,MAAA,CAAAwC,IAAI;IAACC,IAAI,EAAGC,YAAO;IAAA,GAAMZ;EAAiB,CAAI,CAC3D,CAAC;AAER"}