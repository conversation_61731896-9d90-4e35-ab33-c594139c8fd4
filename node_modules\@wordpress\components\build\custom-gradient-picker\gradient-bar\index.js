"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = CustomGradientBar;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
var _controlPoints = _interopRequireDefault(require("./control-points"));
var _utils = require("./utils");
var _constants = require("./constants");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const customGradientBarReducer = (state, action) => {
  switch (action.type) {
    case 'MOVE_INSERTER':
      if (state.id === 'IDLE' || state.id === 'MOVING_INSERTER') {
        return {
          id: 'MOVING_INSERTER',
          insertPosition: action.insertPosition
        };
      }
      break;
    case 'STOP_INSERTER_MOVE':
      if (state.id === 'MOVING_INSERTER') {
        return {
          id: 'IDLE'
        };
      }
      break;
    case 'OPEN_INSERTER':
      if (state.id === 'MOVING_INSERTER') {
        return {
          id: 'INSERTING_CONTROL_POINT',
          insertPosition: state.insertPosition
        };
      }
      break;
    case 'CLOSE_INSERTER':
      if (state.id === 'INSERTING_CONTROL_POINT') {
        return {
          id: 'IDLE'
        };
      }
      break;
    case 'START_CONTROL_CHANGE':
      if (state.id === 'IDLE') {
        return {
          id: 'MOVING_CONTROL_POINT'
        };
      }
      break;
    case 'STOP_CONTROL_CHANGE':
      if (state.id === 'MOVING_CONTROL_POINT') {
        return {
          id: 'IDLE'
        };
      }
      break;
  }
  return state;
};
const customGradientBarReducerInitialState = {
  id: 'IDLE'
};
function CustomGradientBar({
  background,
  hasGradient,
  value: controlPoints,
  onChange,
  disableInserter = false,
  disableAlpha = false,
  __experimentalIsRenderedInSidebar = false
}) {
  const gradientMarkersContainerDomRef = (0, _element.useRef)(null);
  const [gradientBarState, gradientBarStateDispatch] = (0, _element.useReducer)(customGradientBarReducer, customGradientBarReducerInitialState);
  const onMouseEnterAndMove = event => {
    if (!gradientMarkersContainerDomRef.current) {
      return;
    }
    const insertPosition = (0, _utils.getHorizontalRelativeGradientPosition)(event.clientX, gradientMarkersContainerDomRef.current);

    // If the insert point is close to an existing control point don't show it.
    if (controlPoints.some(({
      position
    }) => {
      return Math.abs(insertPosition - position) < _constants.MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT;
    })) {
      if (gradientBarState.id === 'MOVING_INSERTER') {
        gradientBarStateDispatch({
          type: 'STOP_INSERTER_MOVE'
        });
      }
      return;
    }
    gradientBarStateDispatch({
      type: 'MOVE_INSERTER',
      insertPosition
    });
  };
  const onMouseLeave = () => {
    gradientBarStateDispatch({
      type: 'STOP_INSERTER_MOVE'
    });
  };
  const isMovingInserter = gradientBarState.id === 'MOVING_INSERTER';
  const isInsertingControlPoint = gradientBarState.id === 'INSERTING_CONTROL_POINT';
  return (0, _react.createElement)("div", {
    className: (0, _classnames.default)('components-custom-gradient-picker__gradient-bar', {
      'has-gradient': hasGradient
    }),
    onMouseEnter: onMouseEnterAndMove,
    onMouseMove: onMouseEnterAndMove,
    onMouseLeave: onMouseLeave
  }, (0, _react.createElement)("div", {
    className: "components-custom-gradient-picker__gradient-bar-background",
    style: {
      background,
      opacity: hasGradient ? 1 : 0.4
    }
  }), (0, _react.createElement)("div", {
    ref: gradientMarkersContainerDomRef,
    className: "components-custom-gradient-picker__markers-container"
  }, !disableInserter && (isMovingInserter || isInsertingControlPoint) && (0, _react.createElement)(_controlPoints.default.InsertPoint, {
    __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
    disableAlpha: disableAlpha,
    insertPosition: gradientBarState.insertPosition,
    value: controlPoints,
    onChange: onChange,
    onOpenInserter: () => {
      gradientBarStateDispatch({
        type: 'OPEN_INSERTER'
      });
    },
    onCloseInserter: () => {
      gradientBarStateDispatch({
        type: 'CLOSE_INSERTER'
      });
    }
  }), (0, _react.createElement)(_controlPoints.default, {
    __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
    disableAlpha: disableAlpha,
    disableRemove: disableInserter,
    gradientPickerDomRef: gradientMarkersContainerDomRef,
    ignoreMarkerPosition: isInsertingControlPoint ? gradientBarState.insertPosition : undefined,
    value: controlPoints,
    onChange: onChange,
    onStartControlPointChange: () => {
      gradientBarStateDispatch({
        type: 'START_CONTROL_CHANGE'
      });
    },
    onStopControlPointChange: () => {
      gradientBarStateDispatch({
        type: 'STOP_CONTROL_CHANGE'
      });
    }
  })));
}
//# sourceMappingURL=index.js.map