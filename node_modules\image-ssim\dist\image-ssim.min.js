!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var r;r="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,r.ImageSSIM=e()}}(function(){return function e(r,n,t){function o(a,f){if(!n[a]){if(!r[a]){var u="function"==typeof require&&require;if(!f&&u)return u(a,!0);if(i)return i(a,!0);var h=new Error("Cannot find module '"+a+"'");throw h.code="MODULE_NOT_FOUND",h}var s=n[a]={exports:{}};r[a][0].call(s.exports,function(e){var n=r[a][1][e];return o(n?n:e)},s,s.exports,e,r,n,t)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<t.length;a++)o(t[a]);return o}({1:[function(e,r,n){var t;!function(e){"use strict";function r(e,r,t,o,i,a,f){function u(e,r,n,t){var o,i,a;o=i=a=0;for(var f=0;f<e.length;f++)i+=Math.pow(e[f]-n,2),a+=Math.pow(r[f]-t,2),o+=(e[f]-n)*(r[f]-t);var u=e.length-1;i/=u,a/=u,o/=u;var h=(2*n*t+s)*(2*o+c),v=(Math.pow(n,2)+Math.pow(t,2)+s)*(i+a+c);d+=h/v,p+=(2*o+c)/(i+a+c),l++}if(void 0===t&&(t=8),void 0===o&&(o=.01),void 0===i&&(i=.03),void 0===a&&(a=!0),void 0===f&&(f=8),e.width!==r.width||e.height!==r.height)throw new Error("Images have different sizes!");var h=(1<<f)-1,s=Math.pow(o*h,2),c=Math.pow(i*h,2),l=0,d=0,p=0;return n._iterate(e,r,t,a,u),{ssim:d/l,mcs:p/l}}!function(e){e[e.Grey=1]="Grey",e[e.GreyAlpha=2]="GreyAlpha",e[e.RGB=3]="RGB",e[e.RGBAlpha=4]="RGBAlpha"}(e.Channels||(e.Channels={}));e.Channels;e.compare=r;var n;!function(e){function r(e,r,o,i,a){for(var f=e.width,u=e.height,h=0;u>h;h+=o)for(var s=0;f>s;s+=o){var c=Math.min(o,f-s),l=Math.min(o,u-h),d=n(e,s,h,c,l,i),p=n(r,s,h,c,l,i),v=t(d),w=t(p);a(d,p,v,w)}}function n(e,r,n,t,o,i){for(var a=e.data,f=new Float32Array(new ArrayBuffer(t*o*4)),u=0,h=n+o,s=n;h>s;s++){var c=s*e.width,l=(c+r)*e.channels,d=(c+r+t)*e.channels;switch(e.channels){case 1:for(;d>l;)f[u++]=a[l++];break;case 2:for(;d>l;)f[u++]=a[l++]*(a[l++]/255);break;case 3:if(i)for(;d>l;)f[u++]=.212655*a[l++]+.715158*a[l++]+.072187*a[l++];else for(;d>l;)f[u++]=a[l++]+a[l++]+a[l++];break;case 4:if(i)for(;d>l;)f[u++]=(.212655*a[l++]+.715158*a[l++]+.072187*a[l++])*(a[l++]/255);else for(;d>l;)f[u++]=(a[l++]+a[l++]+a[l++])*(a[l++]/255)}}return f}function t(e){for(var r=0,n=0;n<e.length;n++)r+=e[n];return r/e.length}e._iterate=r}(n||(n={}))}(t||(t={})),r.exports=t},{}]},{},[1])(1)});
//# sourceMappingURL=image-ssim.min.js.map