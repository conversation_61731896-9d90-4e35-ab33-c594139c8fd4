"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _compose = require("@wordpress/compose");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BackgroundView = ({
  children
}) => {
  const backgroundStyles = (0, _compose.usePreferredColorSchemeStyle)(_style.default['components-autocomplete__background'], _style.default['components-autocomplete__background-dark']);
  return (0, _react.createElement)(_reactNative.View, {
    style: backgroundStyles
  }, children);
};
var _default = BackgroundView;
exports.default = _default;
//# sourceMappingURL=background-view.android.js.map