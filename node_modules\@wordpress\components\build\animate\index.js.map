{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "getDefault<PERSON><PERSON><PERSON>", "type", "getAnimateClassName", "options", "classnames", "origin", "yAxis", "xAxis", "split", "undefined", "Animate", "children", "className", "_default", "exports", "default"], "sources": ["@wordpress/components/src/animate/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * Internal dependencies\n */\nimport type { AnimateProps, GetAnimateOptions } from './types';\n\n/**\n * @param type The animation type\n * @return Default origin\n */\nfunction getDefaultOrigin( type?: GetAnimateOptions[ 'type' ] ) {\n\treturn type === 'appear' ? 'top' : 'left';\n}\n\n/**\n * @param options\n *\n * @return ClassName that applies the animations\n */\nexport function getAnimateClassName( options: GetAnimateOptions ) {\n\tif ( options.type === 'loading' ) {\n\t\treturn classnames( 'components-animate__loading' );\n\t}\n\n\tconst { type, origin = getDefaultOrigin( type ) } = options;\n\n\tif ( type === 'appear' ) {\n\t\tconst [ yAxis, xAxis = 'center' ] = origin.split( ' ' );\n\t\treturn classnames( 'components-animate__appear', {\n\t\t\t[ 'is-from-' + xAxis ]: xAxis !== 'center',\n\t\t\t[ 'is-from-' + yAxis ]: yAxis !== 'middle',\n\t\t} );\n\t}\n\n\tif ( type === 'slide-in' ) {\n\t\treturn classnames(\n\t\t\t'components-animate__slide-in',\n\t\t\t'is-from-' + origin\n\t\t);\n\t}\n\n\treturn undefined;\n}\n\n/**\n * Simple interface to introduce animations to components.\n *\n * ```jsx\n * import { Animate, Notice } from '@wordpress/components';\n *\n * const MyAnimatedNotice = () => (\n * \t<Animate type=\"slide-in\" options={ { origin: 'top' } }>\n * \t\t{ ( { className } ) => (\n * \t\t\t<Notice className={ className } status=\"success\">\n * \t\t\t\t<p>Animation finished.</p>\n * \t\t\t</Notice>\n * \t\t) }\n * \t</Animate>\n * );\n * ```\n */\nexport function Animate( { type, options = {}, children }: AnimateProps ) {\n\treturn children( {\n\t\tclassName: getAnimateClassName( {\n\t\t\ttype,\n\t\t\t...options,\n\t\t} as GetAnimateOptions ),\n\t} );\n}\n\nexport default Animate;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAHA;AACA;AACA;;AAQA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAAEC,IAAkC,EAAG;EAC/D,OAAOA,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,MAAM;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,mBAAmBA,CAAEC,OAA0B,EAAG;EACjE,IAAKA,OAAO,CAACF,IAAI,KAAK,SAAS,EAAG;IACjC,OAAO,IAAAG,mBAAU,EAAE,6BAA8B,CAAC;EACnD;EAEA,MAAM;IAAEH,IAAI;IAAEI,MAAM,GAAGL,gBAAgB,CAAEC,IAAK;EAAE,CAAC,GAAGE,OAAO;EAE3D,IAAKF,IAAI,KAAK,QAAQ,EAAG;IACxB,MAAM,CAAEK,KAAK,EAAEC,KAAK,GAAG,QAAQ,CAAE,GAAGF,MAAM,CAACG,KAAK,CAAE,GAAI,CAAC;IACvD,OAAO,IAAAJ,mBAAU,EAAE,4BAA4B,EAAE;MAChD,CAAE,UAAU,GAAGG,KAAK,GAAIA,KAAK,KAAK,QAAQ;MAC1C,CAAE,UAAU,GAAGD,KAAK,GAAIA,KAAK,KAAK;IACnC,CAAE,CAAC;EACJ;EAEA,IAAKL,IAAI,KAAK,UAAU,EAAG;IAC1B,OAAO,IAAAG,mBAAU,EAChB,8BAA8B,EAC9B,UAAU,GAAGC,MACd,CAAC;EACF;EAEA,OAAOI,SAAS;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CAAE;EAAET,IAAI;EAAEE,OAAO,GAAG,CAAC,CAAC;EAAEQ;AAAuB,CAAC,EAAG;EACzE,OAAOA,QAAQ,CAAE;IAChBC,SAAS,EAAEV,mBAAmB,CAAE;MAC/BD,IAAI;MACJ,GAAGE;IACJ,CAAuB;EACxB,CAAE,CAAC;AACJ;AAAC,IAAAU,QAAA,GAEcH,OAAO;AAAAI,OAAA,CAAAC,OAAA,GAAAF,QAAA"}