"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

// Action button component is used by both Back and Apply Button componenets.
function ActionButton({
  onPress,
  accessibilityLabel,
  accessibilityHint,
  children
}) {
  return (0, _react.createElement)(_reactNative.TouchableWithoutFeedback, {
    onPress: onPress,
    accessibilityRole: 'button',
    accessibilityLabel: accessibilityLabel,
    accessibilityHint: accessibilityHint
  }, (0, _react.createElement)(_reactNative.View, {
    style: _styles.default['action-button']
  }, children));
}
var _default = ActionButton;
exports.default = _default;
//# sourceMappingURL=action-button.native.js.map