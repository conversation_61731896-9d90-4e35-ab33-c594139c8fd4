"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useHeading = useHeading;
var _context = require("../context");
var _text = require("../text");
var _fontSize = require("../utils/font-size");
var _utils = require("../utils");
/**
 * Internal dependencies
 */

function useHeading(props) {
  const {
    as: asProp,
    level = 2,
    color = _utils.COLORS.gray[900],
    isBlock = true,
    weight = _utils.CONFIG.fontWeightHeading,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'Heading');
  const as = asProp || `h${level}`;
  const a11yProps = {};
  if (typeof as === 'string' && as[0] !== 'h') {
    // If not a semantic `h` element, add a11y props:
    a11yProps.role = 'heading';
    a11yProps['aria-level'] = typeof level === 'string' ? parseInt(level) : level;
  }
  const textProps = (0, _text.useText)({
    color,
    isBlock,
    weight,
    size: (0, _fontSize.getHeadingFontSize)(level),
    ...otherProps
  });
  return {
    ...textProps,
    ...a11yProps,
    as
  };
}
//# sourceMappingURL=hook.js.map