# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [4.4.0](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.4.0) (2019-06-28)

### Features

- **intl-messageformat:** export core entry point ([ca7eeae](https://github.com/formatjs/formatjs/commit/ca7eeae))

# [4.3.0](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.3.0) (2019-06-27)

### Features

- **intl-messageformat:** allow passing in formatters ([#107](https://github.com/formatjs/formatjs/issues/107)) ([3605693](https://github.com/formatjs/formatjs/commit/3605693))

## [4.2.1](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.2.1) (2019-06-26)

**Note:** Version bump only for package intl-messageformat

# [4.2.0](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.2.0) (2019-06-27)

### Features

- **intl-relativetimeformat:** make intl-relativetimeformat test262-compliant ([#95](https://github.com/formatjs/formatjs/issues/95)) ([91669a3](https://github.com/formatjs/formatjs/commit/91669a3))
- **intl-utils:** Add intl-utils ([#98](https://github.com/formatjs/formatjs/issues/98)) ([2329c57](https://github.com/formatjs/formatjs/commit/2329c57))

## [4.1.2](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.1.2) (2019-06-18)

**Note:** Version bump only for package intl-messageformat

## [4.1.1](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.1.1) (2019-06-18)

**Note:** Version bump only for package intl-messageformat

# [4.1.0](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.1.0) (2019-06-18)

### Features

- **intl-format-cache:** fix TS definition ([71ae9eb](https://github.com/formatjs/formatjs/commit/71ae9eb))

## [4.0.1](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat@4.0.1) (2019-06-12)

**Note:** Version bump only for package intl-messageformat

# [4.0.0](https://github.com/formatjs/intl-messageformat/compare/<EMAIL>-messageformat@4.0.0) (2019-06-05)

### Features

- add package intl-relativetimeformat ([#51](https://github.com/formatjs/intl-messageformat/issues/51)) ([48c0f43](https://github.com/formatjs/intl-messageformat/commit/48c0f43))
- **intl-messageformat:** rm bundled intl-pluralrules ([a8526c3](https://github.com/formatjs/intl-messageformat/commit/a8526c3))
- **intl-messageformat:** rm rolluped dist ([a126939](https://github.com/formatjs/intl-messageformat/commit/a126939))

### BREAKING CHANGES

- **intl-messageformat:** Change dist files packaged. Entry point should stay the
  same though.
- **intl-messageformat:** We no longer include intl-pluralrules in our main index
  file. Consumer should polyfill accordingly.

# [3.3.0](https://github.com/formatjs/intl-messageformat/compare/<EMAIL>-messageformat@3.3.0) (2019-06-03)

### Features

- **intl-messageformat:** Add `getAst` method ([3d6c289](https://github.com/formatjs/intl-messageformat/commit/3d6c289))

# [3.2.0](https://github.com/formatjs/intl-messageformat/compare/<EMAIL>-messageformat@3.2.0) (2019-06-03)

### Features

- **intl-messageformat:** Add `getAst` method ([3d6c289](https://github.com/formatjs/intl-messageformat/commit/3d6c289))

## [3.1.4](https://github.com/formatjs/intl-messageformat/compare/<EMAIL>-messageformat@3.1.4) (2019-05-31)

**Note:** Version bump only for package intl-messageformat

## [3.1.3](https://github.com/formatjs/intl-messageformat/compare/<EMAIL>-messageformat@3.1.3) (2019-05-28)

### Bug Fixes

- **intl-messageformat:** fix [#36](https://github.com/formatjs/intl-messageformat/issues/36) and config merging ([#40](https://github.com/formatjs/intl-messageformat/issues/40)) ([4a9969f](https://github.com/formatjs/intl-messageformat/commit/4a9969f))

## [3.1.2](https://github.com/formatjs/intl-messageformat/compare/<EMAIL>-messageformat@3.1.2) (2019-05-28)

### Bug Fixes

- **intl-messageformat:** fix [#36](https://github.com/formatjs/intl-messageformat/issues/36) and config merging ([#40](https://github.com/formatjs/intl-messageformat/issues/40)) ([4a9969f](https://github.com/formatjs/intl-messageformat/commit/4a9969f))
