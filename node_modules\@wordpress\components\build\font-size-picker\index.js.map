{"version": 3, "names": ["_deprecated", "_interopRequireDefault", "require", "_i18n", "_icons", "_element", "_button", "_rangeControl", "_flex", "_unitControl", "_interopRequireWildcard", "_visually<PERSON><PERSON>den", "_utils", "_styles", "_spacer", "_fontSizePickerSelect", "_fontSizePickerToggleGroup", "_constants", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "UnforwardedFontSizePicker", "props", "ref", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "fallbackFontSize", "fontSizes", "disableCustomFontSizes", "onChange", "size", "units", "unitsProp", "value", "<PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON>", "deprecated", "since", "version", "hint", "useCustomUnits", "availableUnits", "shouldUseSelectControl", "length", "selectedFontSize", "find", "fontSize", "isCustomValue", "showCustomValueControl", "setShowCustomValueControl", "useState", "headerHint", "useMemo", "__", "name", "T_SHIRT_NAMES", "indexOf", "commonUnit", "getCommonSizeUnit", "hasUnits", "valueQuantity", "valueUnit", "parseQuantityAndUnitFromRawValue", "isValueUnitRelative", "includes", "isDisabled", "undefined", "_react", "createElement", "Container", "className", "VisuallyHidden", "as", "Spacer", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderHint", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label", "icon", "settings", "onClick", "isPressed", "Controls", "newValue", "Number", "onSelectCustom", "Flex", "FlexItem", "isBlock", "labelPosition", "hideLabelFromVision", "parseInt", "min", "marginX", "marginBottom", "initialPosition", "withInputField", "max", "step", "<PERSON><PERSON>", "disabled", "__experimentalIsFocusable", "variant", "FontSizePicker", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/font-size-picker/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport deprecated from '@wordpress/deprecated';\nimport { __ } from '@wordpress/i18n';\nimport { settings } from '@wordpress/icons';\nimport { useState, useMemo, forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport { Button } from '../button';\nimport RangeControl from '../range-control';\nimport { Flex, FlexItem } from '../flex';\nimport {\n\tdefault as UnitControl,\n\tparseQuantityAndUnitFromRawValue,\n\tuseCustomUnits,\n} from '../unit-control';\nimport { VisuallyHidden } from '../visually-hidden';\nimport { getCommonSizeUnit } from './utils';\nimport type { FontSizePickerProps } from './types';\nimport {\n\tContainer,\n\tHeader,\n\tHeaderHint,\n\tHeaderLabel,\n\tHeaderToggle,\n\tControls,\n} from './styles';\nimport { Spacer } from '../spacer';\nimport FontSizePickerSelect from './font-size-picker-select';\nimport FontSizePickerToggleGroup from './font-size-picker-toggle-group';\nimport { T_SHIRT_NAMES } from './constants';\n\nconst UnforwardedFontSizePicker = (\n\tprops: FontSizePickerProps,\n\tref: ForwardedRef< any >\n) => {\n\tconst {\n\t\t/** Start opting into the new margin-free styles that will become the default in a future version. */\n\t\t__nextHasNoMarginBottom = false,\n\t\t__next40pxDefaultSize = false,\n\t\tfallbackFontSize,\n\t\tfontSizes = [],\n\t\tdisableCustomFontSizes = false,\n\t\tonChange,\n\t\tsize = 'default',\n\t\tunits: unitsProp,\n\t\tvalue,\n\t\twithSlider = false,\n\t\twithReset = true,\n\t} = props;\n\n\tif ( ! __nextHasNoMarginBottom ) {\n\t\tdeprecated( 'Bottom margin styles for wp.components.FontSizePicker', {\n\t\t\tsince: '6.1',\n\t\t\tversion: '6.4',\n\t\t\thint: 'Set the `__nextHasNoMarginBottom` prop to true to start opting into the new styles, which will become the default in a future version.',\n\t\t} );\n\t}\n\n\tconst units = useCustomUnits( {\n\t\tavailableUnits: unitsProp || [ 'px', 'em', 'rem' ],\n\t} );\n\n\tconst shouldUseSelectControl = fontSizes.length > 5;\n\tconst selectedFontSize = fontSizes.find(\n\t\t( fontSize ) => fontSize.size === value\n\t);\n\tconst isCustomValue = !! value && ! selectedFontSize;\n\n\tconst [ showCustomValueControl, setShowCustomValueControl ] = useState(\n\t\t! disableCustomFontSizes && isCustomValue\n\t);\n\n\tconst headerHint = useMemo( () => {\n\t\tif ( showCustomValueControl ) {\n\t\t\treturn __( 'Custom' );\n\t\t}\n\n\t\tif ( ! shouldUseSelectControl ) {\n\t\t\tif ( selectedFontSize ) {\n\t\t\t\treturn (\n\t\t\t\t\tselectedFontSize.name ||\n\t\t\t\t\tT_SHIRT_NAMES[ fontSizes.indexOf( selectedFontSize ) ]\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn '';\n\t\t}\n\n\t\tconst commonUnit = getCommonSizeUnit( fontSizes );\n\t\tif ( commonUnit ) {\n\t\t\treturn `(${ commonUnit })`;\n\t\t}\n\n\t\treturn '';\n\t}, [\n\t\tshowCustomValueControl,\n\t\tshouldUseSelectControl,\n\t\tselectedFontSize,\n\t\tfontSizes,\n\t] );\n\n\tif ( fontSizes.length === 0 && disableCustomFontSizes ) {\n\t\treturn null;\n\t}\n\n\t// If neither the value or first font size is a string, then FontSizePicker\n\t// operates in a legacy \"unitless\" mode where UnitControl can only be used\n\t// to select px values and onChange() is always called with number values.\n\tconst hasUnits =\n\t\ttypeof value === 'string' || typeof fontSizes[ 0 ]?.size === 'string';\n\n\tconst [ valueQuantity, valueUnit ] = parseQuantityAndUnitFromRawValue(\n\t\tvalue,\n\t\tunits\n\t);\n\tconst isValueUnitRelative =\n\t\t!! valueUnit && [ 'em', 'rem' ].includes( valueUnit );\n\tconst isDisabled = value === undefined;\n\n\treturn (\n\t\t<Container ref={ ref } className=\"components-font-size-picker\">\n\t\t\t<VisuallyHidden as=\"legend\">{ __( 'Font size' ) }</VisuallyHidden>\n\t\t\t<Spacer>\n\t\t\t\t<Header className=\"components-font-size-picker__header\">\n\t\t\t\t\t<HeaderLabel\n\t\t\t\t\t\taria-label={ `${ __( 'Size' ) } ${ headerHint || '' }` }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ __( 'Size' ) }\n\t\t\t\t\t\t{ headerHint && (\n\t\t\t\t\t\t\t<HeaderHint className=\"components-font-size-picker__header__hint\">\n\t\t\t\t\t\t\t\t{ headerHint }\n\t\t\t\t\t\t\t</HeaderHint>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</HeaderLabel>\n\t\t\t\t\t{ ! disableCustomFontSizes && (\n\t\t\t\t\t\t<HeaderToggle\n\t\t\t\t\t\t\tlabel={\n\t\t\t\t\t\t\t\tshowCustomValueControl\n\t\t\t\t\t\t\t\t\t? __( 'Use size preset' )\n\t\t\t\t\t\t\t\t\t: __( 'Set custom size' )\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ticon={ settings }\n\t\t\t\t\t\t\tonClick={ () => {\n\t\t\t\t\t\t\t\tsetShowCustomValueControl(\n\t\t\t\t\t\t\t\t\t! showCustomValueControl\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\tisPressed={ showCustomValueControl }\n\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t</Header>\n\t\t\t</Spacer>\n\t\t\t<Controls\n\t\t\t\tclassName=\"components-font-size-picker__controls\"\n\t\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t>\n\t\t\t\t{ !! fontSizes.length &&\n\t\t\t\t\tshouldUseSelectControl &&\n\t\t\t\t\t! showCustomValueControl && (\n\t\t\t\t\t\t<FontSizePickerSelect\n\t\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t\t\tfontSizes={ fontSizes }\n\t\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\t\tdisableCustomFontSizes={ disableCustomFontSizes }\n\t\t\t\t\t\t\tsize={ size }\n\t\t\t\t\t\t\tonChange={ ( newValue ) => {\n\t\t\t\t\t\t\t\tif ( newValue === undefined ) {\n\t\t\t\t\t\t\t\t\tonChange?.( undefined );\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tonChange?.(\n\t\t\t\t\t\t\t\t\t\thasUnits\n\t\t\t\t\t\t\t\t\t\t\t? newValue\n\t\t\t\t\t\t\t\t\t\t\t: Number( newValue ),\n\t\t\t\t\t\t\t\t\t\tfontSizes.find(\n\t\t\t\t\t\t\t\t\t\t\t( fontSize ) =>\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize.size === newValue\n\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\tonSelectCustom={ () =>\n\t\t\t\t\t\t\t\tsetShowCustomValueControl( true )\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t{ ! shouldUseSelectControl && ! showCustomValueControl && (\n\t\t\t\t\t<FontSizePickerToggleGroup\n\t\t\t\t\t\tfontSizes={ fontSizes }\n\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t\tsize={ size }\n\t\t\t\t\t\tonChange={ ( newValue ) => {\n\t\t\t\t\t\t\tif ( newValue === undefined ) {\n\t\t\t\t\t\t\t\tonChange?.( undefined );\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tonChange?.(\n\t\t\t\t\t\t\t\t\thasUnits ? newValue : Number( newValue ),\n\t\t\t\t\t\t\t\t\tfontSizes.find(\n\t\t\t\t\t\t\t\t\t\t( fontSize ) =>\n\t\t\t\t\t\t\t\t\t\t\tfontSize.size === newValue\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t\t{ ! disableCustomFontSizes && showCustomValueControl && (\n\t\t\t\t\t<Flex className=\"components-font-size-picker__custom-size-control\">\n\t\t\t\t\t\t<FlexItem isBlock>\n\t\t\t\t\t\t\t<UnitControl\n\t\t\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t\t\t\tlabel={ __( 'Custom' ) }\n\t\t\t\t\t\t\t\tlabelPosition=\"top\"\n\t\t\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\t\t\tonChange={ ( newValue ) => {\n\t\t\t\t\t\t\t\t\tif ( newValue === undefined ) {\n\t\t\t\t\t\t\t\t\t\tonChange?.( undefined );\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tonChange?.(\n\t\t\t\t\t\t\t\t\t\t\thasUnits\n\t\t\t\t\t\t\t\t\t\t\t\t? newValue\n\t\t\t\t\t\t\t\t\t\t\t\t: parseInt( newValue, 10 )\n\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\tsize={ size }\n\t\t\t\t\t\t\t\tunits={ hasUnits ? units : [] }\n\t\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</FlexItem>\n\t\t\t\t\t\t{ withSlider && (\n\t\t\t\t\t\t\t<FlexItem isBlock>\n\t\t\t\t\t\t\t\t<Spacer marginX={ 2 } marginBottom={ 0 }>\n\t\t\t\t\t\t\t\t\t<RangeControl\n\t\t\t\t\t\t\t\t\t\t__nextHasNoMarginBottom={\n\t\t\t\t\t\t\t\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t__next40pxDefaultSize={\n\t\t\t\t\t\t\t\t\t\t\t__next40pxDefaultSize\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tclassName=\"components-font-size-picker__custom-input\"\n\t\t\t\t\t\t\t\t\t\tlabel={ __( 'Custom Size' ) }\n\t\t\t\t\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t\t\t\t\t\tvalue={ valueQuantity }\n\t\t\t\t\t\t\t\t\t\tinitialPosition={ fallbackFontSize }\n\t\t\t\t\t\t\t\t\t\twithInputField={ false }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( newValue ) => {\n\t\t\t\t\t\t\t\t\t\t\tif ( newValue === undefined ) {\n\t\t\t\t\t\t\t\t\t\t\t\tonChange?.( undefined );\n\t\t\t\t\t\t\t\t\t\t\t} else if ( hasUnits ) {\n\t\t\t\t\t\t\t\t\t\t\t\tonChange?.(\n\t\t\t\t\t\t\t\t\t\t\t\t\tnewValue +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t( valueUnit ?? 'px' )\n\t\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\tonChange?.( newValue );\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\t\t\t\tmax={ isValueUnitRelative ? 10 : 100 }\n\t\t\t\t\t\t\t\t\t\tstep={ isValueUnitRelative ? 0.1 : 1 }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</Spacer>\n\t\t\t\t\t\t\t</FlexItem>\n\t\t\t\t\t\t) }\n\t\t\t\t\t\t{ withReset && (\n\t\t\t\t\t\t\t<FlexItem>\n\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\tdisabled={ isDisabled }\n\t\t\t\t\t\t\t\t\t__experimentalIsFocusable\n\t\t\t\t\t\t\t\t\tonClick={ () => {\n\t\t\t\t\t\t\t\t\t\tonChange?.( undefined );\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\t\t\t\t\t__next40pxDefaultSize\n\t\t\t\t\t\t\t\t\tsize={\n\t\t\t\t\t\t\t\t\t\tsize === '__unstable-large' ||\n\t\t\t\t\t\t\t\t\t\tprops.__next40pxDefaultSize\n\t\t\t\t\t\t\t\t\t\t\t? 'default'\n\t\t\t\t\t\t\t\t\t\t\t: 'small'\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ __( 'Reset' ) }\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</FlexItem>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</Flex>\n\t\t\t\t) }\n\t\t\t</Controls>\n\t\t</Container>\n\t);\n};\n\nexport const FontSizePicker = forwardRef( UnforwardedFontSizePicker );\n\nexport default FontSizePicker;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAC,uBAAA,CAAAR,OAAA;AAKA,IAAAS,eAAA,GAAAT,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AAEA,IAAAW,OAAA,GAAAX,OAAA;AAQA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,qBAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,0BAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,UAAA,GAAAf,OAAA;AAA4C,SAAAgB,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAT,wBAAAa,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAtC5C;AACA;AACA;;AAGA;AACA;AACA;;AAMA;AACA;AACA;;AAyBA,MAAMW,yBAAyB,GAAGA,CACjCC,KAA0B,EAC1BC,GAAwB,KACpB;EACJ,MAAM;IACL;IACAC,uBAAuB,GAAG,KAAK;IAC/BC,qBAAqB,GAAG,KAAK;IAC7BC,gBAAgB;IAChBC,SAAS,GAAG,EAAE;IACdC,sBAAsB,GAAG,KAAK;IAC9BC,QAAQ;IACRC,IAAI,GAAG,SAAS;IAChBC,KAAK,EAAEC,SAAS;IAChBC,KAAK;IACLC,UAAU,GAAG,KAAK;IAClBC,SAAS,GAAG;EACb,CAAC,GAAGb,KAAK;EAET,IAAK,CAAEE,uBAAuB,EAAG;IAChC,IAAAY,mBAAU,EAAE,uDAAuD,EAAE;MACpEC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE;IACP,CAAE,CAAC;EACJ;EAEA,MAAMR,KAAK,GAAG,IAAAS,2BAAc,EAAE;IAC7BC,cAAc,EAAET,SAAS,IAAI,CAAE,IAAI,EAAE,IAAI,EAAE,KAAK;EACjD,CAAE,CAAC;EAEH,MAAMU,sBAAsB,GAAGf,SAAS,CAACgB,MAAM,GAAG,CAAC;EACnD,MAAMC,gBAAgB,GAAGjB,SAAS,CAACkB,IAAI,CACpCC,QAAQ,IAAMA,QAAQ,CAAChB,IAAI,KAAKG,KACnC,CAAC;EACD,MAAMc,aAAa,GAAG,CAAC,CAAEd,KAAK,IAAI,CAAEW,gBAAgB;EAEpD,MAAM,CAAEI,sBAAsB,EAAEC,yBAAyB,CAAE,GAAG,IAAAC,iBAAQ,EACrE,CAAEtB,sBAAsB,IAAImB,aAC7B,CAAC;EAED,MAAMI,UAAU,GAAG,IAAAC,gBAAO,EAAE,MAAM;IACjC,IAAKJ,sBAAsB,EAAG;MAC7B,OAAO,IAAAK,QAAE,EAAE,QAAS,CAAC;IACtB;IAEA,IAAK,CAAEX,sBAAsB,EAAG;MAC/B,IAAKE,gBAAgB,EAAG;QACvB,OACCA,gBAAgB,CAACU,IAAI,IACrBC,wBAAa,CAAE5B,SAAS,CAAC6B,OAAO,CAAEZ,gBAAiB,CAAC,CAAE;MAExD;MACA,OAAO,EAAE;IACV;IAEA,MAAMa,UAAU,GAAG,IAAAC,wBAAiB,EAAE/B,SAAU,CAAC;IACjD,IAAK8B,UAAU,EAAG;MACjB,OAAQ,IAAIA,UAAY,GAAE;IAC3B;IAEA,OAAO,EAAE;EACV,CAAC,EAAE,CACFT,sBAAsB,EACtBN,sBAAsB,EACtBE,gBAAgB,EAChBjB,SAAS,CACR,CAAC;EAEH,IAAKA,SAAS,CAACgB,MAAM,KAAK,CAAC,IAAIf,sBAAsB,EAAG;IACvD,OAAO,IAAI;EACZ;;EAEA;EACA;EACA;EACA,MAAM+B,QAAQ,GACb,OAAO1B,KAAK,KAAK,QAAQ,IAAI,OAAON,SAAS,CAAE,CAAC,CAAE,EAAEG,IAAI,KAAK,QAAQ;EAEtE,MAAM,CAAE8B,aAAa,EAAEC,SAAS,CAAE,GAAG,IAAAC,6CAAgC,EACpE7B,KAAK,EACLF,KACD,CAAC;EACD,MAAMgC,mBAAmB,GACxB,CAAC,CAAEF,SAAS,IAAI,CAAE,IAAI,EAAE,KAAK,CAAE,CAACG,QAAQ,CAAEH,SAAU,CAAC;EACtD,MAAMI,UAAU,GAAGhC,KAAK,KAAKiC,SAAS;EAEtC,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC1E,OAAA,CAAA2E,SAAS;IAAC9C,GAAG,EAAGA,GAAK;IAAC+C,SAAS,EAAC;EAA6B,GAC7D,IAAAH,MAAA,CAAAC,aAAA,EAAC5E,eAAA,CAAA+E,cAAc;IAACC,EAAE,EAAC;EAAQ,GAAG,IAAAnB,QAAE,EAAE,WAAY,CAAmB,CAAC,EAClE,IAAAc,MAAA,CAAAC,aAAA,EAACzE,OAAA,CAAA8E,MAAM,QACN,IAAAN,MAAA,CAAAC,aAAA,EAAC1E,OAAA,CAAAgF,MAAM;IAACJ,SAAS,EAAC;EAAqC,GACtD,IAAAH,MAAA,CAAAC,aAAA,EAAC1E,OAAA,CAAAiF,WAAW;IACX,cAAc,GAAG,IAAAtB,QAAE,EAAE,MAAO,CAAG,IAAIF,UAAU,IAAI,EAAI;EAAG,GAEtD,IAAAE,QAAE,EAAE,MAAO,CAAC,EACZF,UAAU,IACX,IAAAgB,MAAA,CAAAC,aAAA,EAAC1E,OAAA,CAAAkF,UAAU;IAACN,SAAS,EAAC;EAA2C,GAC9DnB,UACS,CAED,CAAC,EACZ,CAAEvB,sBAAsB,IACzB,IAAAuC,MAAA,CAAAC,aAAA,EAAC1E,OAAA,CAAAmF,YAAY;IACZC,KAAK,EACJ9B,sBAAsB,GACnB,IAAAK,QAAE,EAAE,iBAAkB,CAAC,GACvB,IAAAA,QAAE,EAAE,iBAAkB,CACzB;IACD0B,IAAI,EAAGC,eAAU;IACjBC,OAAO,EAAGA,CAAA,KAAM;MACfhC,yBAAyB,CACxB,CAAED,sBACH,CAAC;IACF,CAAG;IACHkC,SAAS,EAAGlC,sBAAwB;IACpClB,IAAI,EAAC;EAAO,CACZ,CAEK,CACD,CAAC,EACT,IAAAqC,MAAA,CAAAC,aAAA,EAAC1E,OAAA,CAAAyF,QAAQ;IACRb,SAAS,EAAC,uCAAuC;IACjD9C,uBAAuB,EAAGA;EAAyB,GAEjD,CAAC,CAAEG,SAAS,CAACgB,MAAM,IACpBD,sBAAsB,IACtB,CAAEM,sBAAsB,IACvB,IAAAmB,MAAA,CAAAC,aAAA,EAACxE,qBAAA,CAAAU,OAAoB;IACpBmB,qBAAqB,EAAGA,qBAAuB;IAC/CE,SAAS,EAAGA,SAAW;IACvBM,KAAK,EAAGA,KAAO;IACfL,sBAAsB,EAAGA,sBAAwB;IACjDE,IAAI,EAAGA,IAAM;IACbD,QAAQ,EAAKuD,QAAQ,IAAM;MAC1B,IAAKA,QAAQ,KAAKlB,SAAS,EAAG;QAC7BrC,QAAQ,GAAIqC,SAAU,CAAC;MACxB,CAAC,MAAM;QACNrC,QAAQ,GACP8B,QAAQ,GACLyB,QAAQ,GACRC,MAAM,CAAED,QAAS,CAAC,EACrBzD,SAAS,CAACkB,IAAI,CACXC,QAAQ,IACTA,QAAQ,CAAChB,IAAI,KAAKsD,QACpB,CACD,CAAC;MACF;IACD,CAAG;IACHE,cAAc,EAAGA,CAAA,KAChBrC,yBAAyB,CAAE,IAAK;EAChC,CACD,CACD,EACA,CAAEP,sBAAsB,IAAI,CAAEM,sBAAsB,IACrD,IAAAmB,MAAA,CAAAC,aAAA,EAACvE,0BAAA,CAAAS,OAAyB;IACzBqB,SAAS,EAAGA,SAAW;IACvBM,KAAK,EAAGA,KAAO;IACfT,uBAAuB,EAAGA,uBAAyB;IACnDC,qBAAqB,EAAGA,qBAAuB;IAC/CK,IAAI,EAAGA,IAAM;IACbD,QAAQ,EAAKuD,QAAQ,IAAM;MAC1B,IAAKA,QAAQ,KAAKlB,SAAS,EAAG;QAC7BrC,QAAQ,GAAIqC,SAAU,CAAC;MACxB,CAAC,MAAM;QACNrC,QAAQ,GACP8B,QAAQ,GAAGyB,QAAQ,GAAGC,MAAM,CAAED,QAAS,CAAC,EACxCzD,SAAS,CAACkB,IAAI,CACXC,QAAQ,IACTA,QAAQ,CAAChB,IAAI,KAAKsD,QACpB,CACD,CAAC;MACF;IACD;EAAG,CACH,CACD,EACC,CAAExD,sBAAsB,IAAIoB,sBAAsB,IACnD,IAAAmB,MAAA,CAAAC,aAAA,EAAC/E,KAAA,CAAAkG,IAAI;IAACjB,SAAS,EAAC;EAAkD,GACjE,IAAAH,MAAA,CAAAC,aAAA,EAAC/E,KAAA,CAAAmG,QAAQ;IAACC,OAAO;EAAA,GAChB,IAAAtB,MAAA,CAAAC,aAAA,EAAC9E,YAAA,CAAAgB,OAAW;IACXmB,qBAAqB,EAAGA,qBAAuB;IAC/CqD,KAAK,EAAG,IAAAzB,QAAE,EAAE,QAAS,CAAG;IACxBqC,aAAa,EAAC,KAAK;IACnBC,mBAAmB;IACnB1D,KAAK,EAAGA,KAAO;IACfJ,QAAQ,EAAKuD,QAAQ,IAAM;MAC1B,IAAKA,QAAQ,KAAKlB,SAAS,EAAG;QAC7BrC,QAAQ,GAAIqC,SAAU,CAAC;MACxB,CAAC,MAAM;QACNrC,QAAQ,GACP8B,QAAQ,GACLyB,QAAQ,GACRQ,QAAQ,CAAER,QAAQ,EAAE,EAAG,CAC3B,CAAC;MACF;IACD,CAAG;IACHtD,IAAI,EAAGA,IAAM;IACbC,KAAK,EAAG4B,QAAQ,GAAG5B,KAAK,GAAG,EAAI;IAC/B8D,GAAG,EAAG;EAAG,CACT,CACQ,CAAC,EACT3D,UAAU,IACX,IAAAiC,MAAA,CAAAC,aAAA,EAAC/E,KAAA,CAAAmG,QAAQ;IAACC,OAAO;EAAA,GAChB,IAAAtB,MAAA,CAAAC,aAAA,EAACzE,OAAA,CAAA8E,MAAM;IAACqB,OAAO,EAAG,CAAG;IAACC,YAAY,EAAG;EAAG,GACvC,IAAA5B,MAAA,CAAAC,aAAA,EAAChF,aAAA,CAAAkB,OAAY;IACZkB,uBAAuB,EACtBA,uBACA;IACDC,qBAAqB,EACpBA,qBACA;IACD6C,SAAS,EAAC,2CAA2C;IACrDQ,KAAK,EAAG,IAAAzB,QAAE,EAAE,aAAc,CAAG;IAC7BsC,mBAAmB;IACnB1D,KAAK,EAAG2B,aAAe;IACvBoC,eAAe,EAAGtE,gBAAkB;IACpCuE,cAAc,EAAG,KAAO;IACxBpE,QAAQ,EAAKuD,QAAQ,IAAM;MAC1B,IAAKA,QAAQ,KAAKlB,SAAS,EAAG;QAC7BrC,QAAQ,GAAIqC,SAAU,CAAC;MACxB,CAAC,MAAM,IAAKP,QAAQ,EAAG;QACtB9B,QAAQ,GACPuD,QAAQ,IACLvB,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI,IAAI,CACrB,CAAC;MACF,CAAC,MAAM;QACNhC,QAAQ,GAAIuD,QAAS,CAAC;MACvB;IACD,CAAG;IACHS,GAAG,EAAG,CAAG;IACTK,GAAG,EAAGnC,mBAAmB,GAAG,EAAE,GAAG,GAAK;IACtCoC,IAAI,EAAGpC,mBAAmB,GAAG,GAAG,GAAG;EAAG,CACtC,CACM,CACC,CACV,EACC5B,SAAS,IACV,IAAAgC,MAAA,CAAAC,aAAA,EAAC/E,KAAA,CAAAmG,QAAQ,QACR,IAAArB,MAAA,CAAAC,aAAA,EAACjF,OAAA,CAAAiH,MAAM;IACNC,QAAQ,EAAGpC,UAAY;IACvBqC,yBAAyB;IACzBrB,OAAO,EAAGA,CAAA,KAAM;MACfpD,QAAQ,GAAIqC,SAAU,CAAC;IACxB,CAAG;IACHqC,OAAO,EAAC,WAAW;IACnB9E,qBAAqB;IACrBK,IAAI,EACHA,IAAI,KAAK,kBAAkB,IAC3BR,KAAK,CAACG,qBAAqB,GACxB,SAAS,GACT;EACH,GAEC,IAAA4B,QAAE,EAAE,OAAQ,CACP,CACC,CAEN,CAEE,CACA,CAAC;AAEd,CAAC;AAEM,MAAMmD,cAAc,GAAG,IAAAC,mBAAU,EAAEpF,yBAA0B,CAAC;AAACqF,OAAA,CAAAF,cAAA,GAAAA,cAAA;AAAA,IAAAG,QAAA,GAEvDH,cAAc;AAAAE,OAAA,CAAApG,OAAA,GAAAqG,QAAA"}