"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useBorderBoxControlSplitControls = useBorderBoxControlSplitControls;
var _element = require("@wordpress/element");
var styles = _interopRequireWildcard(require("../styles"));
var _context = require("../../context");
var _utils = require("../../utils/");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function useBorderBoxControlSplitControls(props) {
  const {
    className,
    colors = [],
    enableAlpha = false,
    enableStyle = true,
    size = 'default',
    __experimentalIsRenderedInSidebar = false,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'BorderBoxControlSplitControls');

  // Generate class names.
  const cx = (0, _utils.useCx)();
  const classes = (0, _element.useMemo)(() => {
    return cx(styles.borderBoxControlSplitControls(size), className);
  }, [cx, className, size]);
  const centeredClassName = (0, _element.useMemo)(() => {
    return cx(styles.centeredBorderControl, className);
  }, [cx, className]);
  const rightAlignedClassName = (0, _element.useMemo)(() => {
    return cx(styles.rightBorderControl(), className);
  }, [cx, className]);
  return {
    ...otherProps,
    centeredClassName,
    className: classes,
    colors,
    enableAlpha,
    enableStyle,
    rightAlignedClassName,
    size,
    __experimentalIsRenderedInSidebar
  };
}
//# sourceMappingURL=hook.js.map