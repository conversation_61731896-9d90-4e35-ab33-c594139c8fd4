<?php
/**
 * Ajoute un bouton de gestion du cache à la barre d'administration WordPress.
 *
 * Cette classe ajoute un menu rapide dans la barre d'administration pour
 * permettre aux administrateurs de vider rapidement le cache.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Ajoute un bouton de gestion du cache à la barre d'administration WordPress.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Cache_Admin_Bar {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance du gestionnaire de cache.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_Cache_Manager    $cache_manager    Instance du gestionnaire de cache.
     */
    private $cache_manager;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->cache_manager = Boss_Cache_Manager::get_instance();
    }

    /**
     * Enregistre les hooks pour cette classe.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Ajouter le menu à la barre d'administration
        add_action( 'admin_bar_menu', array( $this, 'add_cache_menu' ), 100 );
        
        // Traiter les actions de cache
        add_action( 'admin_init', array( $this, 'handle_cache_actions' ) );
        
        // Ajouter les styles CSS pour le menu
        add_action( 'wp_head', array( $this, 'add_admin_bar_styles' ) );
        add_action( 'admin_head', array( $this, 'add_admin_bar_styles' ) );
    }

    /**
     * Ajoute le menu de cache à la barre d'administration.
     *
     * @since    1.1.0
     * @param    WP_Admin_Bar    $wp_admin_bar    Instance de la barre d'administration.
     */
    public function add_cache_menu( $wp_admin_bar ) {
        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            return;
        }

        // Récupérer les statistiques du cache
        $stats = $this->cache_manager->get_cache_stats();
        $debug_mode = $stats['debug_mode'];
        $transients_count = $stats['transients_count'];

        // Icône et titre du menu principal
        $icon = $debug_mode ? '🔧' : '🗂️';
        $title = sprintf(
            '%s Boss SEO Cache %s',
            $icon,
            $debug_mode ? '(Debug)' : "({$transients_count})"
        );

        // Menu principal
        $wp_admin_bar->add_menu( array(
            'id'    => 'boss-seo-cache',
            'title' => $title,
            'href'  => admin_url( 'admin.php?page=boss-seo-settings&tab=cache' ),
            'meta'  => array(
                'title' => __( 'Gestion du cache Boss SEO', 'boss-seo' ),
                'class' => 'boss-seo-cache-menu'
            ),
        ) );

        // Sous-menu : Vider tout le cache
        $wp_admin_bar->add_menu( array(
            'parent' => 'boss-seo-cache',
            'id'     => 'boss-seo-flush-all',
            'title'  => '🗑️ ' . __( 'Vider tout le cache', 'boss-seo' ),
            'href'   => wp_nonce_url( 
                add_query_arg( 'boss_cache_action', 'flush_all' ), 
                'boss_cache_nonce' 
            ),
            'meta'   => array(
                'title' => __( 'Vide tous les types de cache', 'boss-seo' ),
            ),
        ) );

        // Sous-menu : Vider cache assets
        $wp_admin_bar->add_menu( array(
            'parent' => 'boss-seo-cache',
            'id'     => 'boss-seo-flush-assets',
            'title'  => '🎨 ' . __( 'Vider cache CSS/JS', 'boss-seo' ),
            'href'   => wp_nonce_url( 
                add_query_arg( 'boss_cache_action', 'flush_assets' ), 
                'boss_cache_nonce' 
            ),
            'meta'   => array(
                'title' => __( 'Vide seulement le cache des assets', 'boss-seo' ),
            ),
        ) );

        // Sous-menu : Modules populaires
        $popular_modules = array(
            'optimizer' => array( 'icon' => '⚡', 'label' => __( 'Optimizer', 'boss-seo' ) ),
            'analytics' => array( 'icon' => '📊', 'label' => __( 'Analytics', 'boss-seo' ) ),
        );

        foreach ( $popular_modules as $module => $config ) {
            $wp_admin_bar->add_menu( array(
                'parent' => 'boss-seo-cache',
                'id'     => "boss-seo-flush-{$module}",
                'title'  => $config['icon'] . ' ' . $config['label'],
                'href'   => wp_nonce_url( 
                    add_query_arg( array(
                        'boss_cache_action' => 'flush_module',
                        'module' => $module
                    ) ), 
                    'boss_cache_nonce' 
                ),
                'meta'   => array(
                    'title' => sprintf( __( 'Vider le cache du module %s', 'boss-seo' ), $config['label'] ),
                ),
            ) );
        }

        // Séparateur
        $wp_admin_bar->add_menu( array(
            'parent' => 'boss-seo-cache',
            'id'     => 'boss-seo-cache-separator',
            'title'  => '<hr style="margin: 5px 0; border: none; border-top: 1px solid #ddd;">',
            'meta'   => array( 'class' => 'boss-seo-separator' ),
        ) );

        // Lien vers la page de gestion complète
        $wp_admin_bar->add_menu( array(
            'parent' => 'boss-seo-cache',
            'id'     => 'boss-seo-cache-settings',
            'title'  => '⚙️ ' . __( 'Gestion avancée', 'boss-seo' ),
            'href'   => admin_url( 'admin.php?page=boss-seo-settings&tab=cache' ),
            'meta'   => array(
                'title' => __( 'Ouvrir la page de gestion du cache', 'boss-seo' ),
            ),
        ) );

        // Afficher les statistiques si en mode debug
        if ( $debug_mode ) {
            $wp_admin_bar->add_menu( array(
                'parent' => 'boss-seo-cache',
                'id'     => 'boss-seo-cache-debug-info',
                'title'  => sprintf( 
                    '🔍 Debug: %s transients, v%s', 
                    $transients_count, 
                    $stats['assets_version'] 
                ),
                'meta'   => array(
                    'title' => __( 'Informations de debug du cache', 'boss-seo' ),
                    'class' => 'boss-seo-debug-info'
                ),
            ) );
        }
    }

    /**
     * Traite les actions de cache depuis la barre d'administration.
     *
     * @since    1.1.0
     */
    public function handle_cache_actions() {
        // Vérifier si une action de cache est demandée
        if ( ! isset( $_GET['boss_cache_action'] ) ) {
            return;
        }

        // Vérifier le nonce
        if ( ! wp_verify_nonce( $_GET['_wpnonce'], 'boss_cache_nonce' ) ) {
            wp_die( __( 'Action non autorisée.', 'boss-seo' ) );
        }

        // Vérifier les permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Permissions insuffisantes.', 'boss-seo' ) );
        }

        $action = sanitize_text_field( $_GET['boss_cache_action'] );
        $success = false;
        $message = '';

        switch ( $action ) {
            case 'flush_all':
                $results = $this->cache_manager->flush_all_cache();
                $success = ! empty( $results['assets'] );
                $message = $success 
                    ? __( 'Tous les caches ont été vidés avec succès.', 'boss-seo' )
                    : __( 'Erreur lors du vidage du cache.', 'boss-seo' );
                break;

            case 'flush_assets':
                $success = $this->cache_manager->force_assets_refresh();
                $message = $success 
                    ? __( 'Cache des assets vidé avec succès.', 'boss-seo' )
                    : __( 'Erreur lors du vidage du cache des assets.', 'boss-seo' );
                break;

            case 'flush_module':
                $module = sanitize_text_field( $_GET['module'] ?? '' );
                if ( $module ) {
                    $success = $this->cache_manager->flush_module_cache( $module );
                    $message = $success 
                        ? sprintf( __( 'Cache du module %s vidé avec succès.', 'boss-seo' ), $module )
                        : sprintf( __( 'Erreur lors du vidage du cache du module %s.', 'boss-seo' ), $module );
                }
                break;
        }

        // Ajouter une notification
        if ( $message ) {
            $notice_type = $success ? 'success' : 'error';
            add_action( 'admin_notices', function() use ( $message, $notice_type ) {
                printf(
                    '<div class="notice notice-%s is-dismissible"><p>%s</p></div>',
                    esc_attr( $notice_type ),
                    esc_html( $message )
                );
            } );
        }

        // Rediriger pour éviter la re-soumission
        $redirect_url = remove_query_arg( array( 'boss_cache_action', 'module', '_wpnonce' ) );
        wp_safe_redirect( $redirect_url );
        exit;
    }

    /**
     * Ajoute les styles CSS pour le menu de la barre d'administration.
     *
     * @since    1.1.0
     */
    public function add_admin_bar_styles() {
        if ( ! is_admin_bar_showing() || ! current_user_can( 'manage_options' ) ) {
            return;
        }
        ?>
        <style type="text/css">
            #wp-admin-bar-boss-seo-cache .ab-item {
                font-weight: 600;
            }
            
            #wp-admin-bar-boss-seo-cache .ab-submenu .ab-item {
                font-weight: normal;
                padding-left: 30px !important;
            }
            
            .boss-seo-debug-info .ab-item {
                font-size: 11px !important;
                color: #ff6900 !important;
            }
            
            .boss-seo-separator {
                pointer-events: none;
            }
            
            #wp-admin-bar-boss-seo-cache:hover .ab-item {
                background-color: #32373c;
            }
            
            @media screen and (max-width: 782px) {
                #wp-admin-bar-boss-seo-cache .ab-item {
                    font-size: 14px;
                }
            }
        </style>
        <?php
    }
}
