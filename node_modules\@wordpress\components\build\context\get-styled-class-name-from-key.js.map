{"version": 3, "names": ["_changeCase", "require", "_memize", "_interopRequireDefault", "getStyledClassName", "namespace", "kebab", "kebabCase", "getStyledClassNameFromKey", "memoize", "exports"], "sources": ["@wordpress/components/src/context/get-styled-class-name-from-key.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { paramCase as kebabCase } from 'change-case';\nimport memoize from 'memize';\n\n/**\n * Generates the connected component CSS className based on the namespace.\n *\n * @param namespace The name of the connected component.\n * @return The generated CSS className.\n */\nfunction getStyledClassName( namespace: string ): string {\n\tconst kebab = kebabCase( namespace );\n\treturn `components-${ kebab }`;\n}\n\nexport const getStyledClassNameFromKey = memoize( getStyledClassName );\n"], "mappings": ";;;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAJA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAAEC,SAAiB,EAAW;EACxD,MAAMC,KAAK,GAAG,IAAAC,qBAAS,EAAEF,SAAU,CAAC;EACpC,OAAQ,cAAcC,KAAO,EAAC;AAC/B;AAEO,MAAME,yBAAyB,GAAG,IAAAC,eAAO,EAAEL,kBAAmB,CAAC;AAACM,OAAA,CAAAF,yBAAA,GAAAA,yBAAA"}