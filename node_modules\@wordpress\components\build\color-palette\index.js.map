{"version": 3, "names": ["_colord", "require", "_names", "_interopRequireDefault", "_a11y", "_classnames", "_compose", "_i18n", "_element", "_dropdown", "_colorPicker", "_circularOptionPicker", "_vStack", "_truncate", "_styles", "_dropdownContentWrapper", "_utils", "extend", "namesPlugin", "a11yPlugin", "SinglePalette", "className", "clearColor", "colors", "onChange", "value", "additionalProps", "colorOptions", "useMemo", "map", "color", "name", "index", "colordColor", "colord", "isSelected", "_react", "createElement", "default", "Option", "key", "selectedIconProps", "fill", "contrast", "tooltipText", "sprintf", "__", "style", "backgroundColor", "onClick", "OptionGroup", "options", "MultiplePalettes", "headingLevel", "instanceId", "useInstanceId", "length", "VStack", "spacing", "colorPalette", "id", "ColorHeading", "level", "newColor", "CustomColorPickerDropdown", "isRenderedInSidebar", "popoverProps", "receivedPopoverProps", "props", "shift", "resize", "placement", "offset", "contentClassName", "UnforwardedColorPalette", "forwardedRef", "asButtons", "loop", "clearable", "disableCustomColors", "enableAlpha", "__experimentalIsRenderedInSidebar", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "normalizedColorValue", "setNormalizedColorValue", "useState", "useCallback", "undefined", "customColorPaletteCallbackRef", "node", "normalizeColorValue", "hasMultipleColorOrigins", "isMultiplePaletteArray", "buttonLabelName", "extractColorNameFromCurrentValue", "renderCustomColorPicker", "paddingSize", "ColorPicker", "isHex", "startsWith", "displayValue", "replace", "customColorAccessibleLabel", "paletteCommonProps", "actions", "ButtonAction", "metaProps", "_metaProps", "ref", "renderContent", "renderToggle", "isOpen", "onToggle", "background", "type", "Truncate", "classnames", "ColorPalette", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/color-palette/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\nimport { colord, extend } from 'colord';\nimport namesPlugin from 'colord/plugins/names';\nimport a11yPlugin from 'colord/plugins/a11y';\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { __, sprintf } from '@wordpress/i18n';\nimport { useCallback, useMemo, useState, forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport Dropdown from '../dropdown';\nimport { ColorPicker } from '../color-picker';\nimport CircularOptionPicker from '../circular-option-picker';\nimport { VStack } from '../v-stack';\nimport { Truncate } from '../truncate';\nimport { ColorHeading } from './styles';\nimport DropdownContentWrapper from '../dropdown/dropdown-content-wrapper';\nimport type {\n\tColorObject,\n\tColorPaletteProps,\n\tCustomColorPickerDropdownProps,\n\tMultiplePalettesProps,\n\tPaletteObject,\n\tSinglePaletteProps,\n} from './types';\nimport type { WordPressComponentProps } from '../context';\nimport type { DropdownProps } from '../dropdown/types';\nimport {\n\textractColorNameFromCurrentValue,\n\tisMultiplePaletteArray,\n\tnormalizeColorValue,\n} from './utils';\n\nextend( [ namesPlugin, a11yPlugin ] );\n\nfunction SinglePalette( {\n\tclassName,\n\tclearColor,\n\tcolors,\n\tonChange,\n\tvalue,\n\t...additionalProps\n}: SinglePaletteProps ) {\n\tconst colorOptions = useMemo( () => {\n\t\treturn colors.map( ( { color, name }, index ) => {\n\t\t\tconst colordColor = colord( color );\n\t\t\tconst isSelected = value === color;\n\n\t\t\treturn (\n\t\t\t\t<CircularOptionPicker.Option\n\t\t\t\t\tkey={ `${ color }-${ index }` }\n\t\t\t\t\tisSelected={ isSelected }\n\t\t\t\t\tselectedIconProps={\n\t\t\t\t\t\tisSelected\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\tfill:\n\t\t\t\t\t\t\t\t\t\tcolordColor.contrast() >\n\t\t\t\t\t\t\t\t\t\tcolordColor.contrast( '#000' )\n\t\t\t\t\t\t\t\t\t\t\t? '#fff'\n\t\t\t\t\t\t\t\t\t\t\t: '#000',\n\t\t\t\t\t\t\t  }\n\t\t\t\t\t\t\t: {}\n\t\t\t\t\t}\n\t\t\t\t\ttooltipText={\n\t\t\t\t\t\tname ||\n\t\t\t\t\t\t// translators: %s: color hex code e.g: \"#f00\".\n\t\t\t\t\t\tsprintf( __( 'Color code: %s' ), color )\n\t\t\t\t\t}\n\t\t\t\t\tstyle={ { backgroundColor: color, color } }\n\t\t\t\t\tonClick={\n\t\t\t\t\t\tisSelected ? clearColor : () => onChange( color, index )\n\t\t\t\t\t}\n\t\t\t\t\taria-label={\n\t\t\t\t\t\tname\n\t\t\t\t\t\t\t? // translators: %s: The name of the color e.g: \"vivid red\".\n\t\t\t\t\t\t\t  sprintf( __( 'Color: %s' ), name )\n\t\t\t\t\t\t\t: // translators: %s: color hex code e.g: \"#f00\".\n\t\t\t\t\t\t\t  sprintf( __( 'Color code: %s' ), color )\n\t\t\t\t\t}\n\t\t\t\t/>\n\t\t\t);\n\t\t} );\n\t}, [ colors, value, onChange, clearColor ] );\n\n\treturn (\n\t\t<CircularOptionPicker.OptionGroup\n\t\t\tclassName={ className }\n\t\t\toptions={ colorOptions }\n\t\t\t{ ...additionalProps }\n\t\t/>\n\t);\n}\n\nfunction MultiplePalettes( {\n\tclassName,\n\tclearColor,\n\tcolors,\n\tonChange,\n\tvalue,\n\theadingLevel,\n}: MultiplePalettesProps ) {\n\tconst instanceId = useInstanceId( MultiplePalettes, 'color-palette' );\n\n\tif ( colors.length === 0 ) {\n\t\treturn null;\n\t}\n\n\treturn (\n\t\t<VStack spacing={ 3 } className={ className }>\n\t\t\t{ colors.map( ( { name, colors: colorPalette }, index ) => {\n\t\t\t\tconst id = `${ instanceId }-${ index }`;\n\t\t\t\treturn (\n\t\t\t\t\t<VStack spacing={ 2 } key={ index }>\n\t\t\t\t\t\t<ColorHeading id={ id } level={ headingLevel }>\n\t\t\t\t\t\t\t{ name }\n\t\t\t\t\t\t</ColorHeading>\n\t\t\t\t\t\t<SinglePalette\n\t\t\t\t\t\t\tclearColor={ clearColor }\n\t\t\t\t\t\t\tcolors={ colorPalette }\n\t\t\t\t\t\t\tonChange={ ( newColor ) =>\n\t\t\t\t\t\t\t\tonChange( newColor, index )\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\t\taria-labelledby={ id }\n\t\t\t\t\t\t/>\n\t\t\t\t\t</VStack>\n\t\t\t\t);\n\t\t\t} ) }\n\t\t</VStack>\n\t);\n}\n\nexport function CustomColorPickerDropdown( {\n\tisRenderedInSidebar,\n\tpopoverProps: receivedPopoverProps,\n\t...props\n}: CustomColorPickerDropdownProps ) {\n\tconst popoverProps = useMemo< DropdownProps[ 'popoverProps' ] >(\n\t\t() => ( {\n\t\t\tshift: true,\n\t\t\t// Disabling resize as it would otherwise cause the popover to show\n\t\t\t// scrollbars while dragging the color picker's handle close to the\n\t\t\t// popover edge.\n\t\t\tresize: false,\n\t\t\t...( isRenderedInSidebar\n\t\t\t\t? {\n\t\t\t\t\t\t// When in the sidebar: open to the left (stacking),\n\t\t\t\t\t\t// leaving the same gap as the parent popover.\n\t\t\t\t\t\tplacement: 'left-start',\n\t\t\t\t\t\toffset: 34,\n\t\t\t\t  }\n\t\t\t\t: {\n\t\t\t\t\t\t// Default behavior: open below the anchor\n\t\t\t\t\t\tplacement: 'bottom',\n\t\t\t\t\t\toffset: 8,\n\t\t\t\t  } ),\n\t\t\t...receivedPopoverProps,\n\t\t} ),\n\t\t[ isRenderedInSidebar, receivedPopoverProps ]\n\t);\n\n\treturn (\n\t\t<Dropdown\n\t\t\tcontentClassName=\"components-color-palette__custom-color-dropdown-content\"\n\t\t\tpopoverProps={ popoverProps }\n\t\t\t{ ...props }\n\t\t/>\n\t);\n}\n\nfunction UnforwardedColorPalette(\n\tprops: WordPressComponentProps< ColorPaletteProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst {\n\t\tasButtons,\n\t\tloop,\n\t\tclearable = true,\n\t\tcolors = [],\n\t\tdisableCustomColors = false,\n\t\tenableAlpha = false,\n\t\tonChange,\n\t\tvalue,\n\t\t__experimentalIsRenderedInSidebar = false,\n\t\theadingLevel = 2,\n\t\t'aria-label': ariaLabel,\n\t\t'aria-labelledby': ariaLabelledby,\n\t\t...additionalProps\n\t} = props;\n\tconst [ normalizedColorValue, setNormalizedColorValue ] = useState( value );\n\n\tconst clearColor = useCallback( () => onChange( undefined ), [ onChange ] );\n\n\tconst customColorPaletteCallbackRef = useCallback(\n\t\t( node: HTMLElement | null ) => {\n\t\t\tsetNormalizedColorValue( normalizeColorValue( value, node ) );\n\t\t},\n\t\t[ value ]\n\t);\n\n\tconst hasMultipleColorOrigins = isMultiplePaletteArray( colors );\n\tconst buttonLabelName = useMemo(\n\t\t() =>\n\t\t\textractColorNameFromCurrentValue(\n\t\t\t\tvalue,\n\t\t\t\tcolors,\n\t\t\t\thasMultipleColorOrigins\n\t\t\t),\n\t\t[ value, colors, hasMultipleColorOrigins ]\n\t);\n\n\tconst renderCustomColorPicker = () => (\n\t\t<DropdownContentWrapper paddingSize=\"none\">\n\t\t\t<ColorPicker\n\t\t\t\tcolor={ normalizedColorValue }\n\t\t\t\tonChange={ ( color ) => onChange( color ) }\n\t\t\t\tenableAlpha={ enableAlpha }\n\t\t\t/>\n\t\t</DropdownContentWrapper>\n\t);\n\tconst isHex = value?.startsWith( '#' );\n\n\t// Leave hex values as-is. Remove the `var()` wrapper from CSS vars.\n\tconst displayValue = value?.replace( /^var\\((.+)\\)$/, '$1' );\n\tconst customColorAccessibleLabel = !! displayValue\n\t\t? sprintf(\n\t\t\t\t// translators: %1$s: The name of the color e.g: \"vivid red\". %2$s: The color's hex code e.g: \"#f00\".\n\t\t\t\t__(\n\t\t\t\t\t'Custom color picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\".'\n\t\t\t\t),\n\t\t\t\tbuttonLabelName,\n\t\t\t\tdisplayValue\n\t\t  )\n\t\t: __( 'Custom color picker.' );\n\n\tconst paletteCommonProps = {\n\t\tclearColor,\n\t\tonChange,\n\t\tvalue,\n\t};\n\n\tconst actions = !! clearable && (\n\t\t<CircularOptionPicker.ButtonAction onClick={ clearColor }>\n\t\t\t{ __( 'Clear' ) }\n\t\t</CircularOptionPicker.ButtonAction>\n\t);\n\n\tlet metaProps:\n\t\t| { asButtons: false; loop?: boolean; 'aria-label': string }\n\t\t| { asButtons: false; loop?: boolean; 'aria-labelledby': string }\n\t\t| { asButtons: true };\n\n\tif ( asButtons ) {\n\t\tmetaProps = { asButtons: true };\n\t} else {\n\t\tconst _metaProps: { asButtons: false; loop?: boolean } = {\n\t\t\tasButtons: false,\n\t\t\tloop,\n\t\t};\n\n\t\tif ( ariaLabel ) {\n\t\t\tmetaProps = { ..._metaProps, 'aria-label': ariaLabel };\n\t\t} else if ( ariaLabelledby ) {\n\t\t\tmetaProps = {\n\t\t\t\t..._metaProps,\n\t\t\t\t'aria-labelledby': ariaLabelledby,\n\t\t\t};\n\t\t} else {\n\t\t\tmetaProps = {\n\t\t\t\t..._metaProps,\n\t\t\t\t'aria-label': __( 'Custom color picker.' ),\n\t\t\t};\n\t\t}\n\t}\n\n\treturn (\n\t\t<VStack spacing={ 3 } ref={ forwardedRef } { ...additionalProps }>\n\t\t\t{ ! disableCustomColors && (\n\t\t\t\t<CustomColorPickerDropdown\n\t\t\t\t\tisRenderedInSidebar={ __experimentalIsRenderedInSidebar }\n\t\t\t\t\trenderContent={ renderCustomColorPicker }\n\t\t\t\t\trenderToggle={ ( { isOpen, onToggle } ) => (\n\t\t\t\t\t\t<VStack\n\t\t\t\t\t\t\tclassName=\"components-color-palette__custom-color-wrapper\"\n\t\t\t\t\t\t\tspacing={ 0 }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\tref={ customColorPaletteCallbackRef }\n\t\t\t\t\t\t\t\tclassName=\"components-color-palette__custom-color-button\"\n\t\t\t\t\t\t\t\taria-expanded={ isOpen }\n\t\t\t\t\t\t\t\taria-haspopup=\"true\"\n\t\t\t\t\t\t\t\tonClick={ onToggle }\n\t\t\t\t\t\t\t\taria-label={ customColorAccessibleLabel }\n\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\tbackground: value,\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<VStack\n\t\t\t\t\t\t\t\tclassName=\"components-color-palette__custom-color-text-wrapper\"\n\t\t\t\t\t\t\t\tspacing={ 0.5 }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<Truncate className=\"components-color-palette__custom-color-name\">\n\t\t\t\t\t\t\t\t\t{ value\n\t\t\t\t\t\t\t\t\t\t? buttonLabelName\n\t\t\t\t\t\t\t\t\t\t: __( 'No color selected' ) }\n\t\t\t\t\t\t\t\t</Truncate>\n\t\t\t\t\t\t\t\t{ /*\n\t\t\t\t\t\t\t\tThis `Truncate` is always rendered, even if\n\t\t\t\t\t\t\t\tthere is no `displayValue`, to ensure the layout\n\t\t\t\t\t\t\t\tdoes not shift\n\t\t\t\t\t\t\t\t*/ }\n\t\t\t\t\t\t\t\t<Truncate\n\t\t\t\t\t\t\t\t\tclassName={ classnames(\n\t\t\t\t\t\t\t\t\t\t'components-color-palette__custom-color-value',\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t'components-color-palette__custom-color-value--is-hex':\n\t\t\t\t\t\t\t\t\t\t\t\tisHex,\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ displayValue }\n\t\t\t\t\t\t\t\t</Truncate>\n\t\t\t\t\t\t\t</VStack>\n\t\t\t\t\t\t</VStack>\n\t\t\t\t\t) }\n\t\t\t\t/>\n\t\t\t) }\n\t\t\t<CircularOptionPicker\n\t\t\t\t{ ...metaProps }\n\t\t\t\tactions={ actions }\n\t\t\t\toptions={\n\t\t\t\t\thasMultipleColorOrigins ? (\n\t\t\t\t\t\t<MultiplePalettes\n\t\t\t\t\t\t\t{ ...paletteCommonProps }\n\t\t\t\t\t\t\theadingLevel={ headingLevel }\n\t\t\t\t\t\t\tcolors={ colors as PaletteObject[] }\n\t\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<SinglePalette\n\t\t\t\t\t\t\t{ ...paletteCommonProps }\n\t\t\t\t\t\t\tcolors={ colors as ColorObject[] }\n\t\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\t/>\n\t\t\t\t\t)\n\t\t\t\t}\n\t\t\t/>\n\t\t</VStack>\n\t);\n}\n\n/**\n * Allows the user to pick a color from a list of pre-defined color entries.\n *\n * ```jsx\n * import { ColorPalette } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyColorPalette = () => {\n *   const [ color, setColor ] = useState ( '#f00' )\n *   const colors = [\n *     { name: 'red', color: '#f00' },\n *     { name: 'white', color: '#fff' },\n *     { name: 'blue', color: '#00f' },\n *   ];\n *   return (\n *     <ColorPalette\n *       colors={ colors }\n *       value={ color }\n *       onChange={ ( color ) => setColor( color ) }\n *     />\n *   );\n * } );\n * ```\n */\nexport const ColorPalette = forwardRef( UnforwardedColorPalette );\n\nexport default ColorPalette;\n"], "mappings": ";;;;;;;;;;AAIA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AAKA,IAAAK,QAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AAKA,IAAAQ,SAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,YAAA,GAAAT,OAAA;AACA,IAAAU,qBAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AACA,IAAAY,SAAA,GAAAZ,OAAA;AACA,IAAAa,OAAA,GAAAb,OAAA;AACA,IAAAc,uBAAA,GAAAZ,sBAAA,CAAAF,OAAA;AAWA,IAAAe,MAAA,GAAAf,OAAA;AApCA;AACA;AACA;;AAOA;AACA;AACA;;AAKA;AACA;AACA;;AAwBA,IAAAgB,cAAM,EAAE,CAAEC,cAAW,EAAEC,aAAU,CAAG,CAAC;AAErC,SAASC,aAAaA,CAAE;EACvBC,SAAS;EACTC,UAAU;EACVC,MAAM;EACNC,QAAQ;EACRC,KAAK;EACL,GAAGC;AACgB,CAAC,EAAG;EACvB,MAAMC,YAAY,GAAG,IAAAC,gBAAO,EAAE,MAAM;IACnC,OAAOL,MAAM,CAACM,GAAG,CAAE,CAAE;MAAEC,KAAK;MAAEC;IAAK,CAAC,EAAEC,KAAK,KAAM;MAChD,MAAMC,WAAW,GAAG,IAAAC,cAAM,EAAEJ,KAAM,CAAC;MACnC,MAAMK,UAAU,GAAGV,KAAK,KAAKK,KAAK;MAElC,OACC,IAAAM,MAAA,CAAAC,aAAA,EAAC1B,qBAAA,CAAA2B,OAAoB,CAACC,MAAM;QAC3BC,GAAG,EAAI,GAAGV,KAAO,IAAIE,KAAO,EAAG;QAC/BG,UAAU,EAAGA,UAAY;QACzBM,iBAAiB,EAChBN,UAAU,GACP;UACAO,IAAI,EACHT,WAAW,CAACU,QAAQ,CAAC,CAAC,GACtBV,WAAW,CAACU,QAAQ,CAAE,MAAO,CAAC,GAC3B,MAAM,GACN;QACJ,CAAC,GACD,CAAC,CACJ;QACDC,WAAW,EACVb,IAAI;QACJ;QACA,IAAAc,aAAO,EAAE,IAAAC,QAAE,EAAE,gBAAiB,CAAC,EAAEhB,KAAM,CACvC;QACDiB,KAAK,EAAG;UAAEC,eAAe,EAAElB,KAAK;UAAEA;QAAM,CAAG;QAC3CmB,OAAO,EACNd,UAAU,GAAGb,UAAU,GAAG,MAAME,QAAQ,CAAEM,KAAK,EAAEE,KAAM,CACvD;QACD,cACCD,IAAI;QACD;QACA,IAAAc,aAAO,EAAE,IAAAC,QAAE,EAAE,WAAY,CAAC,EAAEf,IAAK,CAAC;QAClC;QACA,IAAAc,aAAO,EAAE,IAAAC,QAAE,EAAE,gBAAiB,CAAC,EAAEhB,KAAM;MAC1C,CACD,CAAC;IAEJ,CAAE,CAAC;EACJ,CAAC,EAAE,CAAEP,MAAM,EAAEE,KAAK,EAAED,QAAQ,EAAEF,UAAU,CAAG,CAAC;EAE5C,OACC,IAAAc,MAAA,CAAAC,aAAA,EAAC1B,qBAAA,CAAA2B,OAAoB,CAACY,WAAW;IAChC7B,SAAS,EAAGA,SAAW;IACvB8B,OAAO,EAAGxB,YAAc;IAAA,GACnBD;EAAe,CACpB,CAAC;AAEJ;AAEA,SAAS0B,gBAAgBA,CAAE;EAC1B/B,SAAS;EACTC,UAAU;EACVC,MAAM;EACNC,QAAQ;EACRC,KAAK;EACL4B;AACsB,CAAC,EAAG;EAC1B,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEH,gBAAgB,EAAE,eAAgB,CAAC;EAErE,IAAK7B,MAAM,CAACiC,MAAM,KAAK,CAAC,EAAG;IAC1B,OAAO,IAAI;EACZ;EAEA,OACC,IAAApB,MAAA,CAAAC,aAAA,EAACzB,OAAA,CAAA6C,MAAM;IAACC,OAAO,EAAG,CAAG;IAACrC,SAAS,EAAGA;EAAW,GAC1CE,MAAM,CAACM,GAAG,CAAE,CAAE;IAAEE,IAAI;IAAER,MAAM,EAAEoC;EAAa,CAAC,EAAE3B,KAAK,KAAM;IAC1D,MAAM4B,EAAE,GAAI,GAAGN,UAAY,IAAItB,KAAO,EAAC;IACvC,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACzB,OAAA,CAAA6C,MAAM;MAACC,OAAO,EAAG,CAAG;MAAClB,GAAG,EAAGR;IAAO,GAClC,IAAAI,MAAA,CAAAC,aAAA,EAACvB,OAAA,CAAA+C,YAAY;MAACD,EAAE,EAAGA,EAAI;MAACE,KAAK,EAAGT;IAAc,GAC3CtB,IACW,CAAC,EACf,IAAAK,MAAA,CAAAC,aAAA,EAACjB,aAAa;MACbE,UAAU,EAAGA,UAAY;MACzBC,MAAM,EAAGoC,YAAc;MACvBnC,QAAQ,EAAKuC,QAAQ,IACpBvC,QAAQ,CAAEuC,QAAQ,EAAE/B,KAAM,CAC1B;MACDP,KAAK,EAAGA,KAAO;MACf,mBAAkBmC;IAAI,CACtB,CACM,CAAC;EAEX,CAAE,CACK,CAAC;AAEX;AAEO,SAASI,yBAAyBA,CAAE;EAC1CC,mBAAmB;EACnBC,YAAY,EAAEC,oBAAoB;EAClC,GAAGC;AAC4B,CAAC,EAAG;EACnC,MAAMF,YAAY,GAAG,IAAAtC,gBAAO,EAC3B,OAAQ;IACPyC,KAAK,EAAE,IAAI;IACX;IACA;IACA;IACAC,MAAM,EAAE,KAAK;IACb,IAAKL,mBAAmB,GACrB;MACA;MACA;MACAM,SAAS,EAAE,YAAY;MACvBC,MAAM,EAAE;IACR,CAAC,GACD;MACA;MACAD,SAAS,EAAE,QAAQ;MACnBC,MAAM,EAAE;IACR,CAAC,CAAE;IACN,GAAGL;EACJ,CAAC,CAAE,EACH,CAAEF,mBAAmB,EAAEE,oBAAoB,CAC5C,CAAC;EAED,OACC,IAAA/B,MAAA,CAAAC,aAAA,EAAC5B,SAAA,CAAA6B,OAAQ;IACRmC,gBAAgB,EAAC,yDAAyD;IAC1EP,YAAY,EAAGA,YAAc;IAAA,GACxBE;EAAK,CACV,CAAC;AAEJ;AAEA,SAASM,uBAAuBA,CAC/BN,KAA0D,EAC1DO,YAAiC,EAChC;EACD,MAAM;IACLC,SAAS;IACTC,IAAI;IACJC,SAAS,GAAG,IAAI;IAChBvD,MAAM,GAAG,EAAE;IACXwD,mBAAmB,GAAG,KAAK;IAC3BC,WAAW,GAAG,KAAK;IACnBxD,QAAQ;IACRC,KAAK;IACLwD,iCAAiC,GAAG,KAAK;IACzC5B,YAAY,GAAG,CAAC;IAChB,YAAY,EAAE6B,SAAS;IACvB,iBAAiB,EAAEC,cAAc;IACjC,GAAGzD;EACJ,CAAC,GAAG0C,KAAK;EACT,MAAM,CAAEgB,oBAAoB,EAAEC,uBAAuB,CAAE,GAAG,IAAAC,iBAAQ,EAAE7D,KAAM,CAAC;EAE3E,MAAMH,UAAU,GAAG,IAAAiE,oBAAW,EAAE,MAAM/D,QAAQ,CAAEgE,SAAU,CAAC,EAAE,CAAEhE,QAAQ,CAAG,CAAC;EAE3E,MAAMiE,6BAA6B,GAAG,IAAAF,oBAAW,EAC9CG,IAAwB,IAAM;IAC/BL,uBAAuB,CAAE,IAAAM,0BAAmB,EAAElE,KAAK,EAAEiE,IAAK,CAAE,CAAC;EAC9D,CAAC,EACD,CAAEjE,KAAK,CACR,CAAC;EAED,MAAMmE,uBAAuB,GAAG,IAAAC,6BAAsB,EAAEtE,MAAO,CAAC;EAChE,MAAMuE,eAAe,GAAG,IAAAlE,gBAAO,EAC9B,MACC,IAAAmE,uCAAgC,EAC/BtE,KAAK,EACLF,MAAM,EACNqE,uBACD,CAAC,EACF,CAAEnE,KAAK,EAAEF,MAAM,EAAEqE,uBAAuB,CACzC,CAAC;EAED,MAAMI,uBAAuB,GAAGA,CAAA,KAC/B,IAAA5D,MAAA,CAAAC,aAAA,EAACtB,uBAAA,CAAAuB,OAAsB;IAAC2D,WAAW,EAAC;EAAM,GACzC,IAAA7D,MAAA,CAAAC,aAAA,EAAC3B,YAAA,CAAAwF,WAAW;IACXpE,KAAK,EAAGsD,oBAAsB;IAC9B5D,QAAQ,EAAKM,KAAK,IAAMN,QAAQ,CAAEM,KAAM,CAAG;IAC3CkD,WAAW,EAAGA;EAAa,CAC3B,CACsB,CACxB;EACD,MAAMmB,KAAK,GAAG1E,KAAK,EAAE2E,UAAU,CAAE,GAAI,CAAC;;EAEtC;EACA,MAAMC,YAAY,GAAG5E,KAAK,EAAE6E,OAAO,CAAE,eAAe,EAAE,IAAK,CAAC;EAC5D,MAAMC,0BAA0B,GAAG,CAAC,CAAEF,YAAY,GAC/C,IAAAxD,aAAO;EACP;EACA,IAAAC,QAAE,EACD,+FACD,CAAC,EACDgD,eAAe,EACfO,YACA,CAAC,GACD,IAAAvD,QAAE,EAAE,sBAAuB,CAAC;EAE/B,MAAM0D,kBAAkB,GAAG;IAC1BlF,UAAU;IACVE,QAAQ;IACRC;EACD,CAAC;EAED,MAAMgF,OAAO,GAAG,CAAC,CAAE3B,SAAS,IAC3B,IAAA1C,MAAA,CAAAC,aAAA,EAAC1B,qBAAA,CAAA2B,OAAoB,CAACoE,YAAY;IAACzD,OAAO,EAAG3B;EAAY,GACtD,IAAAwB,QAAE,EAAE,OAAQ,CACoB,CACnC;EAED,IAAI6D,SAGkB;EAEtB,IAAK/B,SAAS,EAAG;IAChB+B,SAAS,GAAG;MAAE/B,SAAS,EAAE;IAAK,CAAC;EAChC,CAAC,MAAM;IACN,MAAMgC,UAAgD,GAAG;MACxDhC,SAAS,EAAE,KAAK;MAChBC;IACD,CAAC;IAED,IAAKK,SAAS,EAAG;MAChByB,SAAS,GAAG;QAAE,GAAGC,UAAU;QAAE,YAAY,EAAE1B;MAAU,CAAC;IACvD,CAAC,MAAM,IAAKC,cAAc,EAAG;MAC5BwB,SAAS,GAAG;QACX,GAAGC,UAAU;QACb,iBAAiB,EAAEzB;MACpB,CAAC;IACF,CAAC,MAAM;MACNwB,SAAS,GAAG;QACX,GAAGC,UAAU;QACb,YAAY,EAAE,IAAA9D,QAAE,EAAE,sBAAuB;MAC1C,CAAC;IACF;EACD;EAEA,OACC,IAAAV,MAAA,CAAAC,aAAA,EAACzB,OAAA,CAAA6C,MAAM;IAACC,OAAO,EAAG,CAAG;IAACmD,GAAG,EAAGlC,YAAc;IAAA,GAAMjD;EAAe,GAC5D,CAAEqD,mBAAmB,IACtB,IAAA3C,MAAA,CAAAC,aAAA,EAAC2B,yBAAyB;IACzBC,mBAAmB,EAAGgB,iCAAmC;IACzD6B,aAAa,EAAGd,uBAAyB;IACzCe,YAAY,EAAGA,CAAE;MAAEC,MAAM;MAAEC;IAAS,CAAC,KACpC,IAAA7E,MAAA,CAAAC,aAAA,EAACzB,OAAA,CAAA6C,MAAM;MACNpC,SAAS,EAAC,gDAAgD;MAC1DqC,OAAO,EAAG;IAAG,GAEb,IAAAtB,MAAA,CAAAC,aAAA;MACCwE,GAAG,EAAGpB,6BAA+B;MACrCpE,SAAS,EAAC,+CAA+C;MACzD,iBAAgB2F,MAAQ;MACxB,iBAAc,MAAM;MACpB/D,OAAO,EAAGgE,QAAU;MACpB,cAAaV,0BAA4B;MACzCxD,KAAK,EAAG;QACPmE,UAAU,EAAEzF;MACb,CAAG;MACH0F,IAAI,EAAC;IAAQ,CACb,CAAC,EACF,IAAA/E,MAAA,CAAAC,aAAA,EAACzB,OAAA,CAAA6C,MAAM;MACNpC,SAAS,EAAC,qDAAqD;MAC/DqC,OAAO,EAAG;IAAK,GAEf,IAAAtB,MAAA,CAAAC,aAAA,EAACxB,SAAA,CAAAuG,QAAQ;MAAC/F,SAAS,EAAC;IAA6C,GAC9DI,KAAK,GACJqE,eAAe,GACf,IAAAhD,QAAE,EAAE,mBAAoB,CAClB,CAAC,EAMX,IAAAV,MAAA,CAAAC,aAAA,EAACxB,SAAA,CAAAuG,QAAQ;MACR/F,SAAS,EAAG,IAAAgG,mBAAU,EACrB,8CAA8C,EAC9C;QACC,sDAAsD,EACrDlB;MACF,CACD;IAAG,GAEDE,YACO,CACH,CACD;EACN,CACH,CACD,EACD,IAAAjE,MAAA,CAAAC,aAAA,EAAC1B,qBAAA,CAAA2B,OAAoB;IAAA,GACfqE,SAAS;IACdF,OAAO,EAAGA,OAAS;IACnBtD,OAAO,EACNyC,uBAAuB,GACtB,IAAAxD,MAAA,CAAAC,aAAA,EAACe,gBAAgB;MAAA,GACXoD,kBAAkB;MACvBnD,YAAY,EAAGA,YAAc;MAC7B9B,MAAM,EAAGA,MAA2B;MACpCE,KAAK,EAAGA;IAAO,CACf,CAAC,GAEF,IAAAW,MAAA,CAAAC,aAAA,EAACjB,aAAa;MAAA,GACRoF,kBAAkB;MACvBjF,MAAM,EAAGA,MAAyB;MAClCE,KAAK,EAAGA;IAAO,CACf;EAEF,CACD,CACM,CAAC;AAEX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM6F,YAAY,GAAG,IAAAC,mBAAU,EAAE7C,uBAAwB,CAAC;AAAC8C,OAAA,CAAAF,YAAA,GAAAA,YAAA;AAAA,IAAAG,QAAA,GAEnDH,YAAY;AAAAE,OAAA,CAAAlF,OAAA,GAAAmF,QAAA"}