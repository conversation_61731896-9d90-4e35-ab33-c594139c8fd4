{"version": 3, "names": ["_reactNative", "require", "_i18n", "_icons", "_compose", "_cell", "_interopRequireDefault", "_styles", "_linkSuggestionStyles", "_gridicons", "icons", "URL", "globe", "clipboard", "post", "posts", "page", "pages", "getSummaryForType", "type", "__", "LinkSuggestionItemCell", "suggestion", "onLinkPicked", "props", "title", "contentTitle", "url", "isDirectEntry", "summary", "pickLink", "cellTitleStyle", "usePreferredColorSchemeStyle", "cellStyles", "cellLabel", "cellTextDark", "cellSummaryStyle", "cellValue", "titleStyle", "suggestion<PERSON><PERSON>les", "summaryStyle", "_react", "createElement", "default", "icon", "empty", "onPress", "separatorType", "cellContainerStyle", "itemContainerStyle", "labelStyle", "hidden", "valueStyle", "View", "style", "containerStyle", "Text", "numberOfLines", "ellipsizeMode", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/link-suggestion-item-cell.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Text, View } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { globe } from '@wordpress/icons';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport cellStyles from './styles.scss';\nimport suggestionStyles from './link-suggestion-styles.scss';\nimport { posts, pages, empty, clipboard } from '../gridicons';\n\nconst icons = {\n\tURL: globe,\n\tclipboard,\n\tpost: posts,\n\tpage: pages,\n};\n\nconst getSummaryForType = ( type ) => {\n\tswitch ( type ) {\n\t\tcase 'clipboard':\n\t\t\treturn __( 'From clipboard' );\n\t\tcase 'mailto':\n\t\t\treturn __( 'Add this email link' );\n\t\tcase 'tel':\n\t\t\treturn __( 'Add this telephone link' );\n\t\tdefault:\n\t\t\treturn __( 'Add this link' );\n\t}\n};\n\n// We use some Cell styles here with a column flex-direction.\nfunction LinkSuggestionItemCell( { suggestion, onLinkPicked, ...props } ) {\n\tconst { title: contentTitle, url, type, isDirectEntry } = suggestion;\n\tconst title = isDirectEntry ? url : contentTitle;\n\tconst summary = isDirectEntry ? getSummaryForType( type ) : url;\n\n\tconst pickLink = () => onLinkPicked( suggestion );\n\n\tconst cellTitleStyle = usePreferredColorSchemeStyle(\n\t\tcellStyles.cellLabel,\n\t\tcellStyles.cellTextDark\n\t);\n\n\tconst cellSummaryStyle = usePreferredColorSchemeStyle(\n\t\tcellStyles.cellValue,\n\t\tcellStyles.cellTextDark\n\t);\n\n\tconst titleStyle = [ cellTitleStyle, suggestionStyles.titleStyle ];\n\tconst summaryStyle = [ cellSummaryStyle, suggestionStyles.summaryStyle ];\n\n\treturn (\n\t\t<Cell\n\t\t\t{ ...props }\n\t\t\ticon={ icons[ type ] || empty }\n\t\t\tonPress={ pickLink }\n\t\t\tseparatorType={ 'none' }\n\t\t\tcellContainerStyle={ suggestionStyles.itemContainerStyle }\n\t\t\tlabelStyle={ suggestionStyles.hidden }\n\t\t\tvalueStyle={ suggestionStyles.hidden }\n\t\t>\n\t\t\t<View style={ suggestionStyles.containerStyle }>\n\t\t\t\t<Text\n\t\t\t\t\tstyle={ titleStyle }\n\t\t\t\t\tnumberOfLines={ 1 }\n\t\t\t\t\tellipsizeMode={ 'middle' }\n\t\t\t\t>\n\t\t\t\t\t{ title }\n\t\t\t\t</Text>\n\t\t\t\t<Text\n\t\t\t\t\tstyle={ summaryStyle }\n\t\t\t\t\tnumberOfLines={ 1 }\n\t\t\t\t\tellipsizeMode={ 'middle' }\n\t\t\t\t>\n\t\t\t\t\t{ summary }\n\t\t\t\t</Text>\n\t\t\t</View>\n\t\t</Cell>\n\t);\n}\n\nexport default LinkSuggestionItemCell;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,KAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,OAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,qBAAA,GAAAF,sBAAA,CAAAL,OAAA;AACA,IAAAQ,UAAA,GAAAR,OAAA;AAlBA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;;AAMA,MAAMS,KAAK,GAAG;EACbC,GAAG,EAAEC,YAAK;EACVC,SAAS,EAATA,oBAAS;EACTC,IAAI,EAAEC,gBAAK;EACXC,IAAI,EAAEC;AACP,CAAC;AAED,MAAMC,iBAAiB,GAAKC,IAAI,IAAM;EACrC,QAASA,IAAI;IACZ,KAAK,WAAW;MACf,OAAO,IAAAC,QAAE,EAAE,gBAAiB,CAAC;IAC9B,KAAK,QAAQ;MACZ,OAAO,IAAAA,QAAE,EAAE,qBAAsB,CAAC;IACnC,KAAK,KAAK;MACT,OAAO,IAAAA,QAAE,EAAE,yBAA0B,CAAC;IACvC;MACC,OAAO,IAAAA,QAAE,EAAE,eAAgB,CAAC;EAC9B;AACD,CAAC;;AAED;AACA,SAASC,sBAAsBA,CAAE;EAAEC,UAAU;EAAEC,YAAY;EAAE,GAAGC;AAAM,CAAC,EAAG;EACzE,MAAM;IAAEC,KAAK,EAAEC,YAAY;IAAEC,GAAG;IAAER,IAAI;IAAES;EAAc,CAAC,GAAGN,UAAU;EACpE,MAAMG,KAAK,GAAGG,aAAa,GAAGD,GAAG,GAAGD,YAAY;EAChD,MAAMG,OAAO,GAAGD,aAAa,GAAGV,iBAAiB,CAAEC,IAAK,CAAC,GAAGQ,GAAG;EAE/D,MAAMG,QAAQ,GAAGA,CAAA,KAAMP,YAAY,CAAED,UAAW,CAAC;EAEjD,MAAMS,cAAc,GAAG,IAAAC,qCAA4B,EAClDC,eAAU,CAACC,SAAS,EACpBD,eAAU,CAACE,YACZ,CAAC;EAED,MAAMC,gBAAgB,GAAG,IAAAJ,qCAA4B,EACpDC,eAAU,CAACI,SAAS,EACpBJ,eAAU,CAACE,YACZ,CAAC;EAED,MAAMG,UAAU,GAAG,CAAEP,cAAc,EAAEQ,6BAAgB,CAACD,UAAU,CAAE;EAClE,MAAME,YAAY,GAAG,CAAEJ,gBAAgB,EAAEG,6BAAgB,CAACC,YAAY,CAAE;EAExE,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACrC,KAAA,CAAAsC,OAAI;IAAA,GACCnB,KAAK;IACVoB,IAAI,EAAGlC,KAAK,CAAES,IAAI,CAAE,IAAI0B,gBAAO;IAC/BC,OAAO,EAAGhB,QAAU;IACpBiB,aAAa,EAAG,MAAQ;IACxBC,kBAAkB,EAAGT,6BAAgB,CAACU,kBAAoB;IAC1DC,UAAU,EAAGX,6BAAgB,CAACY,MAAQ;IACtCC,UAAU,EAAGb,6BAAgB,CAACY;EAAQ,GAEtC,IAAAV,MAAA,CAAAC,aAAA,EAAC1C,YAAA,CAAAqD,IAAI;IAACC,KAAK,EAAGf,6BAAgB,CAACgB;EAAgB,GAC9C,IAAAd,MAAA,CAAAC,aAAA,EAAC1C,YAAA,CAAAwD,IAAI;IACJF,KAAK,EAAGhB,UAAY;IACpBmB,aAAa,EAAG,CAAG;IACnBC,aAAa,EAAG;EAAU,GAExBjC,KACG,CAAC,EACP,IAAAgB,MAAA,CAAAC,aAAA,EAAC1C,YAAA,CAAAwD,IAAI;IACJF,KAAK,EAAGd,YAAc;IACtBiB,aAAa,EAAG,CAAG;IACnBC,aAAa,EAAG;EAAU,GAExB7B,OACG,CACD,CACD,CAAC;AAET;AAAC,IAAA8B,QAAA,GAEctC,sBAAsB;AAAAuC,OAAA,CAAAjB,OAAA,GAAAgB,QAAA"}