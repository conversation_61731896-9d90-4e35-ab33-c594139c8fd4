{"version": 3, "names": ["_reactNative", "require", "_element", "_i18n", "_components", "_compose", "_backgroundView", "_interopRequireDefault", "_getDefaultUseItems", "_style", "compose", "stylesCompose", "StyleSheet", "getAutoCompleterUI", "autocompleter", "useItems", "getDefaultUseItems", "AutocompleterUI", "filterValue", "selectedIndex", "onChangeOptions", "onSelect", "value", "reset", "items", "filteredItems", "filter", "item", "isDisabled", "scrollViewRef", "useRef", "animationValue", "Animated", "Value", "current", "isVisible", "setIsVisible", "useState", "text", "useEffect", "length", "useLayoutEffect", "scrollTo", "x", "animated", "startAnimation", "activeItemStyles", "usePreferredColorSchemeStyle", "styles", "iconStyles", "activeIconStyles", "textStyles", "activeTextStyles", "useCallback", "show", "timing", "toValue", "duration", "useNativeDriver", "start", "finished", "contentStyles", "transform", "translateY", "interpolate", "inputRange", "outputRange", "height", "_react", "createElement", "__unstableAutocompletionItemsFill", "View", "style", "default", "ScrollView", "testID", "ref", "horizontal", "contentContainerStyle", "showsHorizontalScrollIndicator", "keyboardShouldPersistTaps", "accessibilityLabel", "__", "map", "option", "index", "isActive", "itemStyle", "textStyle", "iconStyle", "iconSource", "icon", "src", "TouchableOpacity", "activeOpacity", "key", "onPress", "sprintf", "title", "Icon", "size", "Text", "_default", "exports"], "sources": ["@wordpress/components/src/autocomplete/autocompleter-ui.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tView,\n\tAnimated,\n\tStyleSheet,\n\tText,\n\tTouchableOpacity,\n\tScrollView,\n} from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tuseLayoutEffect,\n\tuseEffect,\n\tuseRef,\n\tuseState,\n\tuseCallback,\n} from '@wordpress/element';\nimport { __, sprintf } from '@wordpress/i18n';\nimport {\n\tIcon,\n\t__unstableAutocompletionItemsFill as AutocompletionItemsFill,\n} from '@wordpress/components';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport BackgroundView from './background-view';\nimport getDefaultUseItems from './get-default-use-items';\nimport styles from './style.scss';\n\nconst { compose: stylesCompose } = StyleSheet;\n\nexport function getAutoCompleterUI( autocompleter ) {\n\tconst useItems = autocompleter.useItems\n\t\t? autocompleter.useItems\n\t\t: getDefaultUseItems( autocompleter );\n\n\tfunction AutocompleterUI( {\n\t\tfilterValue,\n\t\tselectedIndex,\n\t\tonChangeOptions,\n\t\tonSelect,\n\t\tvalue,\n\t\treset,\n\t} ) {\n\t\tconst [ items ] = useItems( filterValue );\n\t\tconst filteredItems = items.filter( ( item ) => ! item.isDisabled );\n\t\tconst scrollViewRef = useRef();\n\t\tconst animationValue = useRef( new Animated.Value( 0 ) ).current;\n\t\tconst [ isVisible, setIsVisible ] = useState( false );\n\t\tconst { text } = value;\n\n\t\tuseEffect( () => {\n\t\t\tif ( ! isVisible && text.length > 0 ) {\n\t\t\t\tsetIsVisible( true );\n\t\t\t}\n\t\t}, [ isVisible, text ] );\n\n\t\tuseLayoutEffect( () => {\n\t\t\tonChangeOptions( items );\n\t\t\tscrollViewRef.current?.scrollTo( { x: 0, animated: false } );\n\n\t\t\tif ( isVisible && text.length > 0 ) {\n\t\t\t\tstartAnimation( true );\n\t\t\t} else if ( isVisible && text.length === 0 ) {\n\t\t\t\tstartAnimation( false );\n\t\t\t}\n\t\t\t// Temporarily disabling exhaustive-deps to avoid introducing unexpected side effecst.\n\t\t\t// See https://github.com/WordPress/gutenberg/pull/41820\n\t\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t\t}, [ items, isVisible, text ] );\n\n\t\tconst activeItemStyles = usePreferredColorSchemeStyle(\n\t\t\tstyles[ 'components-autocomplete__item-active' ],\n\t\t\tstyles[ 'components-autocomplete__item-active-dark' ]\n\t\t);\n\n\t\tconst iconStyles = usePreferredColorSchemeStyle(\n\t\t\tstyles[ 'components-autocomplete__icon' ],\n\t\t\tstyles[ 'components-autocomplete__icon-active-dark' ]\n\t\t);\n\n\t\tconst activeIconStyles = usePreferredColorSchemeStyle(\n\t\t\tstyles[ 'components-autocomplete__icon-active ' ],\n\t\t\tstyles[ 'components-autocomplete__icon-active-dark' ]\n\t\t);\n\n\t\tconst textStyles = usePreferredColorSchemeStyle(\n\t\t\tstyles[ 'components-autocomplete__text' ],\n\t\t\tstyles[ 'components-autocomplete__text-dark' ]\n\t\t);\n\n\t\tconst activeTextStyles = usePreferredColorSchemeStyle(\n\t\t\tstyles[ 'components-autocomplete__text-active' ],\n\t\t\tstyles[ 'components-autocomplete__text-active-dark' ]\n\t\t);\n\n\t\tconst startAnimation = useCallback(\n\t\t\t( show ) => {\n\t\t\t\tAnimated.timing( animationValue, {\n\t\t\t\t\ttoValue: show ? 1 : 0,\n\t\t\t\t\tduration: show ? 200 : 100,\n\t\t\t\t\tuseNativeDriver: true,\n\t\t\t\t} ).start( ( { finished } ) => {\n\t\t\t\t\tif ( finished && ! show && isVisible ) {\n\t\t\t\t\t\tsetIsVisible( false );\n\t\t\t\t\t\treset();\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t},\n\t\t\t// Temporarily disabling exhaustive-deps to avoid introducing unexpected side effecst.\n\t\t\t// See https://github.com/WordPress/gutenberg/pull/41820\n\t\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t\t\t[ isVisible ]\n\t\t);\n\n\t\tconst contentStyles = {\n\t\t\ttransform: [\n\t\t\t\t{\n\t\t\t\t\ttranslateY: animationValue.interpolate( {\n\t\t\t\t\t\tinputRange: [ 0, 1 ],\n\t\t\t\t\t\toutputRange: [\n\t\t\t\t\t\t\tstyles[ 'components-autocomplete' ].height,\n\t\t\t\t\t\t\t0,\n\t\t\t\t\t\t],\n\t\t\t\t\t} ),\n\t\t\t\t},\n\t\t\t],\n\t\t};\n\n\t\tif ( ! filteredItems.length > 0 || ! isVisible ) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn (\n\t\t\t<AutocompletionItemsFill>\n\t\t\t\t<View style={ styles[ 'components-autocomplete' ] }>\n\t\t\t\t\t<Animated.View style={ contentStyles }>\n\t\t\t\t\t\t<BackgroundView>\n\t\t\t\t\t\t\t<ScrollView\n\t\t\t\t\t\t\t\ttestID=\"autocompleter\"\n\t\t\t\t\t\t\t\tref={ scrollViewRef }\n\t\t\t\t\t\t\t\thorizontal\n\t\t\t\t\t\t\t\tcontentContainerStyle={\n\t\t\t\t\t\t\t\t\tstyles[ 'components-autocomplete__content' ]\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tshowsHorizontalScrollIndicator={ false }\n\t\t\t\t\t\t\t\tkeyboardShouldPersistTaps=\"always\"\n\t\t\t\t\t\t\t\taccessibilityLabel={\n\t\t\t\t\t\t\t\t\t// translators: Slash inserter autocomplete results\n\t\t\t\t\t\t\t\t\t__( 'Slash inserter results' )\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{ filteredItems.map( ( option, index ) => {\n\t\t\t\t\t\t\t\t\tconst isActive = index === selectedIndex;\n\t\t\t\t\t\t\t\t\tconst itemStyle = stylesCompose(\n\t\t\t\t\t\t\t\t\t\tstyles[\n\t\t\t\t\t\t\t\t\t\t\t'components-autocomplete__item'\n\t\t\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\t\t\tisActive && activeItemStyles\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\tconst textStyle = stylesCompose(\n\t\t\t\t\t\t\t\t\t\ttextStyles,\n\t\t\t\t\t\t\t\t\t\tisActive && activeTextStyles\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\tconst iconStyle = stylesCompose(\n\t\t\t\t\t\t\t\t\t\ticonStyles,\n\t\t\t\t\t\t\t\t\t\tisActive && activeIconStyles\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\tconst iconSource =\n\t\t\t\t\t\t\t\t\t\toption?.value?.icon?.src ||\n\t\t\t\t\t\t\t\t\t\toption?.value?.icon;\n\n\t\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\t\t<TouchableOpacity\n\t\t\t\t\t\t\t\t\t\t\tactiveOpacity={ 0.5 }\n\t\t\t\t\t\t\t\t\t\t\tstyle={ itemStyle }\n\t\t\t\t\t\t\t\t\t\t\tkey={ index }\n\t\t\t\t\t\t\t\t\t\t\tonPress={ () => onSelect( option ) }\n\t\t\t\t\t\t\t\t\t\t\taccessibilityLabel={ sprintf(\n\t\t\t\t\t\t\t\t\t\t\t\t// translators: %s: Block name e.g. \"Image block\"\n\t\t\t\t\t\t\t\t\t\t\t\t__( '%s block' ),\n\t\t\t\t\t\t\t\t\t\t\t\toption?.value?.title\n\t\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<View\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyles[\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t'components-autocomplete__icon'\n\t\t\t\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<Icon\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon={ iconSource }\n\t\t\t\t\t\t\t\t\t\t\t\t\tsize={ 24 }\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={ iconStyle }\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</View>\n\t\t\t\t\t\t\t\t\t\t\t<Text style={ textStyle }>\n\t\t\t\t\t\t\t\t\t\t\t\t{ option?.value?.title }\n\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t</TouchableOpacity>\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t} ) }\n\t\t\t\t\t\t\t</ScrollView>\n\t\t\t\t\t\t</BackgroundView>\n\t\t\t\t\t</Animated.View>\n\t\t\t\t</View>\n\t\t\t</AutocompletionItemsFill>\n\t\t);\n\t}\n\n\treturn AutocompleterUI;\n}\n\nexport default getAutoCompleterUI;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAYA,IAAAC,QAAA,GAAAD,OAAA;AAOA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AAIA,IAAAI,QAAA,GAAAJ,OAAA;AAKA,IAAAK,eAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,mBAAA,GAAAD,sBAAA,CAAAN,OAAA;AACA,IAAAQ,MAAA,GAAAF,sBAAA,CAAAN,OAAA;AAlCA;AACA;AACA;;AAUA;AACA;AACA;;AAeA;AACA;AACA;;AAKA,MAAM;EAAES,OAAO,EAAEC;AAAc,CAAC,GAAGC,uBAAU;AAEtC,SAASC,kBAAkBA,CAAEC,aAAa,EAAG;EACnD,MAAMC,QAAQ,GAAGD,aAAa,CAACC,QAAQ,GACpCD,aAAa,CAACC,QAAQ,GACtB,IAAAC,2BAAkB,EAAEF,aAAc,CAAC;EAEtC,SAASG,eAAeA,CAAE;IACzBC,WAAW;IACXC,aAAa;IACbC,eAAe;IACfC,QAAQ;IACRC,KAAK;IACLC;EACD,CAAC,EAAG;IACH,MAAM,CAAEC,KAAK,CAAE,GAAGT,QAAQ,CAAEG,WAAY,CAAC;IACzC,MAAMO,aAAa,GAAGD,KAAK,CAACE,MAAM,CAAIC,IAAI,IAAM,CAAEA,IAAI,CAACC,UAAW,CAAC;IACnE,MAAMC,aAAa,GAAG,IAAAC,eAAM,EAAC,CAAC;IAC9B,MAAMC,cAAc,GAAG,IAAAD,eAAM,EAAE,IAAIE,qBAAQ,CAACC,KAAK,CAAE,CAAE,CAAE,CAAC,CAACC,OAAO;IAChE,MAAM,CAAEC,SAAS,EAAEC,YAAY,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;IACrD,MAAM;MAAEC;IAAK,CAAC,GAAGhB,KAAK;IAEtB,IAAAiB,kBAAS,EAAE,MAAM;MAChB,IAAK,CAAEJ,SAAS,IAAIG,IAAI,CAACE,MAAM,GAAG,CAAC,EAAG;QACrCJ,YAAY,CAAE,IAAK,CAAC;MACrB;IACD,CAAC,EAAE,CAAED,SAAS,EAAEG,IAAI,CAAG,CAAC;IAExB,IAAAG,wBAAe,EAAE,MAAM;MACtBrB,eAAe,CAAEI,KAAM,CAAC;MACxBK,aAAa,CAACK,OAAO,EAAEQ,QAAQ,CAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAM,CAAE,CAAC;MAE5D,IAAKT,SAAS,IAAIG,IAAI,CAACE,MAAM,GAAG,CAAC,EAAG;QACnCK,cAAc,CAAE,IAAK,CAAC;MACvB,CAAC,MAAM,IAAKV,SAAS,IAAIG,IAAI,CAACE,MAAM,KAAK,CAAC,EAAG;QAC5CK,cAAc,CAAE,KAAM,CAAC;MACxB;MACA;MACA;MACA;IACD,CAAC,EAAE,CAAErB,KAAK,EAAEW,SAAS,EAAEG,IAAI,CAAG,CAAC;IAE/B,MAAMQ,gBAAgB,GAAG,IAAAC,qCAA4B,EACpDC,cAAM,CAAE,sCAAsC,CAAE,EAChDA,cAAM,CAAE,2CAA2C,CACpD,CAAC;IAED,MAAMC,UAAU,GAAG,IAAAF,qCAA4B,EAC9CC,cAAM,CAAE,+BAA+B,CAAE,EACzCA,cAAM,CAAE,2CAA2C,CACpD,CAAC;IAED,MAAME,gBAAgB,GAAG,IAAAH,qCAA4B,EACpDC,cAAM,CAAE,uCAAuC,CAAE,EACjDA,cAAM,CAAE,2CAA2C,CACpD,CAAC;IAED,MAAMG,UAAU,GAAG,IAAAJ,qCAA4B,EAC9CC,cAAM,CAAE,+BAA+B,CAAE,EACzCA,cAAM,CAAE,oCAAoC,CAC7C,CAAC;IAED,MAAMI,gBAAgB,GAAG,IAAAL,qCAA4B,EACpDC,cAAM,CAAE,sCAAsC,CAAE,EAChDA,cAAM,CAAE,2CAA2C,CACpD,CAAC;IAED,MAAMH,cAAc,GAAG,IAAAQ,oBAAW,EAC/BC,IAAI,IAAM;MACXtB,qBAAQ,CAACuB,MAAM,CAAExB,cAAc,EAAE;QAChCyB,OAAO,EAAEF,IAAI,GAAG,CAAC,GAAG,CAAC;QACrBG,QAAQ,EAAEH,IAAI,GAAG,GAAG,GAAG,GAAG;QAC1BI,eAAe,EAAE;MAClB,CAAE,CAAC,CAACC,KAAK,CAAE,CAAE;QAAEC;MAAS,CAAC,KAAM;QAC9B,IAAKA,QAAQ,IAAI,CAAEN,IAAI,IAAInB,SAAS,EAAG;UACtCC,YAAY,CAAE,KAAM,CAAC;UACrBb,KAAK,CAAC,CAAC;QACR;MACD,CAAE,CAAC;IACJ,CAAC;IACD;IACA;IACA;IACA,CAAEY,SAAS,CACZ,CAAC;IAED,MAAM0B,aAAa,GAAG;MACrBC,SAAS,EAAE,CACV;QACCC,UAAU,EAAEhC,cAAc,CAACiC,WAAW,CAAE;UACvCC,UAAU,EAAE,CAAE,CAAC,EAAE,CAAC,CAAE;UACpBC,WAAW,EAAE,CACZlB,cAAM,CAAE,yBAAyB,CAAE,CAACmB,MAAM,EAC1C,CAAC;QAEH,CAAE;MACH,CAAC;IAEH,CAAC;IAED,IAAK,CAAE1C,aAAa,CAACe,MAAM,GAAG,CAAC,IAAI,CAAEL,SAAS,EAAG;MAChD,OAAO,IAAI;IACZ;IAEA,OACC,IAAAiC,MAAA,CAAAC,aAAA,EAACjE,WAAA,CAAAkE,iCAAuB,QACvB,IAAAF,MAAA,CAAAC,aAAA,EAACrE,YAAA,CAAAuE,IAAI;MAACC,KAAK,EAAGxB,cAAM,CAAE,yBAAyB;IAAI,GAClD,IAAAoB,MAAA,CAAAC,aAAA,EAACrE,YAAA,CAAAgC,QAAQ,CAACuC,IAAI;MAACC,KAAK,EAAGX;IAAe,GACrC,IAAAO,MAAA,CAAAC,aAAA,EAAC/D,eAAA,CAAAmE,OAAc,QACd,IAAAL,MAAA,CAAAC,aAAA,EAACrE,YAAA,CAAA0E,UAAU;MACVC,MAAM,EAAC,eAAe;MACtBC,GAAG,EAAG/C,aAAe;MACrBgD,UAAU;MACVC,qBAAqB,EACpB9B,cAAM,CAAE,kCAAkC,CAC1C;MACD+B,8BAA8B,EAAG,KAAO;MACxCC,yBAAyB,EAAC,QAAQ;MAClCC,kBAAkB;MACjB;MACA,IAAAC,QAAE,EAAE,wBAAyB;IAC7B,GAECzD,aAAa,CAAC0D,GAAG,CAAE,CAAEC,MAAM,EAAEC,KAAK,KAAM;MACzC,MAAMC,QAAQ,GAAGD,KAAK,KAAKlE,aAAa;MACxC,MAAMoE,SAAS,GAAG5E,aAAa,CAC9BqC,cAAM,CACL,+BAA+B,CAC/B,EACDsC,QAAQ,IAAIxC,gBACb,CAAC;MACD,MAAM0C,SAAS,GAAG7E,aAAa,CAC9BwC,UAAU,EACVmC,QAAQ,IAAIlC,gBACb,CAAC;MACD,MAAMqC,SAAS,GAAG9E,aAAa,CAC9BsC,UAAU,EACVqC,QAAQ,IAAIpC,gBACb,CAAC;MACD,MAAMwC,UAAU,GACfN,MAAM,EAAE9D,KAAK,EAAEqE,IAAI,EAAEC,GAAG,IACxBR,MAAM,EAAE9D,KAAK,EAAEqE,IAAI;MAEpB,OACC,IAAAvB,MAAA,CAAAC,aAAA,EAACrE,YAAA,CAAA6F,gBAAgB;QAChBC,aAAa,EAAG,GAAK;QACrBtB,KAAK,EAAGe,SAAW;QACnBQ,GAAG,EAAGV,KAAO;QACbW,OAAO,EAAGA,CAAA,KAAM3E,QAAQ,CAAE+D,MAAO,CAAG;QACpCH,kBAAkB,EAAG,IAAAgB,aAAO;QAC3B;QACA,IAAAf,QAAE,EAAE,UAAW,CAAC,EAChBE,MAAM,EAAE9D,KAAK,EAAE4E,KAChB;MAAG,GAEH,IAAA9B,MAAA,CAAAC,aAAA,EAACrE,YAAA,CAAAuE,IAAI;QACJC,KAAK,EACJxB,cAAM,CACL,+BAA+B;MAEhC,GAED,IAAAoB,MAAA,CAAAC,aAAA,EAACjE,WAAA,CAAA+F,IAAI;QACJR,IAAI,EAAGD,UAAY;QACnBU,IAAI,EAAG,EAAI;QACX5B,KAAK,EAAGiB;MAAW,CACnB,CACI,CAAC,EACP,IAAArB,MAAA,CAAAC,aAAA,EAACrE,YAAA,CAAAqG,IAAI;QAAC7B,KAAK,EAAGgB;MAAW,GACtBJ,MAAM,EAAE9D,KAAK,EAAE4E,KACZ,CACW,CAAC;IAErB,CAAE,CACS,CACG,CACF,CACV,CACkB,CAAC;EAE5B;EAEA,OAAOjF,eAAe;AACvB;AAAC,IAAAqF,QAAA,GAEczF,kBAAkB;AAAA0F,OAAA,CAAA9B,OAAA,GAAA6B,QAAA"}