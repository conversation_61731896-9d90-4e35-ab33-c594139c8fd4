"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _colorCell = _interopRequireDefault(require("../mobile/bottom-sheet/color-cell"));
/**
 * Internal dependencies
 */

function ColorControl({
  label,
  onPress,
  color,
  withColorIndicator,
  ...props
}) {
  return (0, _react.createElement)(_colorCell.default, {
    label: label,
    onPress: onPress,
    color: color,
    withColorIndicator: withColorIndicator,
    ...props
  });
}
var _default = ColorControl;
exports.default = _default;
//# sourceMappingURL=index.native.js.map