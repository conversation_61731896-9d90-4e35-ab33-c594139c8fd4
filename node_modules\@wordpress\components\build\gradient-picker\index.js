"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GradientPicker = GradientPicker;
exports.default = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _compose = require("@wordpress/compose");
var _element = require("@wordpress/element");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _circularOptionPicker = _interopRequireDefault(require("../circular-option-picker"));
var _customGradientPicker = _interopRequireDefault(require("../custom-gradient-picker"));
var _vStack = require("../v-stack");
var _styles = require("../color-palette/styles");
var _spacer = require("../spacer");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

// The Multiple Origin Gradients have a `gradients` property (an array of
// gradient objects), while Single Origin ones have a `gradient` property.
const isMultipleOriginObject = obj => Array.isArray(obj.gradients) && !('gradient' in obj);
const isMultipleOriginArray = arr => {
  return arr.length > 0 && arr.every(gradientObj => isMultipleOriginObject(gradientObj));
};
function SingleOrigin({
  className,
  clearGradient,
  gradients,
  onChange,
  value,
  ...additionalProps
}) {
  const gradientOptions = (0, _element.useMemo)(() => {
    return gradients.map(({
      gradient,
      name,
      slug
    }, index) => (0, _react.createElement)(_circularOptionPicker.default.Option, {
      key: slug,
      value: gradient,
      isSelected: value === gradient,
      tooltipText: name ||
      // translators: %s: gradient code e.g: "linear-gradient(90deg, rgba(98,16,153,1) 0%, rgba(172,110,22,1) 100%);".
      (0, _i18n.sprintf)((0, _i18n.__)('Gradient code: %s'), gradient),
      style: {
        color: 'rgba( 0,0,0,0 )',
        background: gradient
      },
      onClick: value === gradient ? clearGradient : () => onChange(gradient, index),
      "aria-label": name ?
      // translators: %s: The name of the gradient e.g: "Angular red to blue".
      (0, _i18n.sprintf)((0, _i18n.__)('Gradient: %s'), name) :
      // translators: %s: gradient code e.g: "linear-gradient(90deg, rgba(98,16,153,1) 0%, rgba(172,110,22,1) 100%);".
      (0, _i18n.sprintf)((0, _i18n.__)('Gradient code: %s'), gradient)
    }));
  }, [gradients, value, onChange, clearGradient]);
  return (0, _react.createElement)(_circularOptionPicker.default.OptionGroup, {
    className: className,
    options: gradientOptions,
    ...additionalProps
  });
}
function MultipleOrigin({
  className,
  clearGradient,
  gradients,
  onChange,
  value,
  headingLevel
}) {
  const instanceId = (0, _compose.useInstanceId)(MultipleOrigin);
  return (0, _react.createElement)(_vStack.VStack, {
    spacing: 3,
    className: className
  }, gradients.map(({
    name,
    gradients: gradientSet
  }, index) => {
    const id = `color-palette-${instanceId}-${index}`;
    return (0, _react.createElement)(_vStack.VStack, {
      spacing: 2,
      key: index
    }, (0, _react.createElement)(_styles.ColorHeading, {
      level: headingLevel,
      id: id
    }, name), (0, _react.createElement)(SingleOrigin, {
      clearGradient: clearGradient,
      gradients: gradientSet,
      onChange: gradient => onChange(gradient, index),
      value: value,
      "aria-labelledby": id
    }));
  }));
}
function Component(props) {
  const {
    asButtons,
    loop,
    actions,
    headingLevel,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledby,
    ...additionalProps
  } = props;
  const options = isMultipleOriginArray(props.gradients) ? (0, _react.createElement)(MultipleOrigin, {
    headingLevel: headingLevel,
    ...additionalProps
  }) : (0, _react.createElement)(SingleOrigin, {
    ...additionalProps
  });
  let metaProps;
  if (asButtons) {
    metaProps = {
      asButtons: true
    };
  } else {
    const _metaProps = {
      asButtons: false,
      loop
    };
    if (ariaLabel) {
      metaProps = {
        ..._metaProps,
        'aria-label': ariaLabel
      };
    } else if (ariaLabelledby) {
      metaProps = {
        ..._metaProps,
        'aria-labelledby': ariaLabelledby
      };
    } else {
      metaProps = {
        ..._metaProps,
        'aria-label': (0, _i18n.__)('Custom color picker.')
      };
    }
  }
  return (0, _react.createElement)(_circularOptionPicker.default, {
    ...metaProps,
    actions: actions,
    options: options
  });
}

/**
 *  GradientPicker is a React component that renders a color gradient picker to
 * define a multi step gradient. There's either a _linear_ or a _radial_ type
 * available.
 *
 * ```jsx
 *import { GradientPicker } from '@wordpress/components';
 *import { useState } from '@wordpress/element';
 *
 *const myGradientPicker = () => {
 *	const [ gradient, setGradient ] = useState( null );
 *
 *	return (
 *		<GradientPicker
 *			__nextHasNoMargin
 *			value={ gradient }
 *			onChange={ ( currentGradient ) => setGradient( currentGradient ) }
 *			gradients={ [
 *				{
 *					name: 'JShine',
 *					gradient:
 *						'linear-gradient(135deg,#12c2e9 0%,#c471ed 50%,#f64f59 100%)',
 *					slug: 'jshine',
 *				},
 *				{
 *					name: 'Moonlit Asteroid',
 *					gradient:
 *						'linear-gradient(135deg,#0F2027 0%, #203A43 0%, #2c5364 100%)',
 *					slug: 'moonlit-asteroid',
 *				},
 *				{
 *					name: 'Rastafarie',
 *					gradient:
 *						'linear-gradient(135deg,#1E9600 0%, #FFF200 0%, #FF0000 100%)',
 *					slug: 'rastafari',
 *				},
 *			] }
 *		/>
 *	);
 *};
 *```
 *
 */
function GradientPicker({
  /** Start opting into the new margin-free styles that will become the default in a future version. */
  __nextHasNoMargin = false,
  className,
  gradients = [],
  onChange,
  value,
  clearable = true,
  disableCustomGradients = false,
  __experimentalIsRenderedInSidebar,
  headingLevel = 2,
  ...additionalProps
}) {
  const clearGradient = (0, _element.useCallback)(() => onChange(undefined), [onChange]);
  if (!__nextHasNoMargin) {
    (0, _deprecated.default)('Outer margin styles for wp.components.GradientPicker', {
      since: '6.1',
      version: '6.4',
      hint: 'Set the `__nextHasNoMargin` prop to true to start opting into the new styles, which will become the default in a future version'
    });
  }
  const deprecatedMarginSpacerProps = !__nextHasNoMargin ? {
    marginTop: !gradients.length ? 3 : undefined,
    marginBottom: !clearable ? 6 : 0
  } : {};
  return (
    // Outmost Spacer wrapper can be removed when deprecation period is over
    (0, _react.createElement)(_spacer.Spacer, {
      marginBottom: 0,
      ...deprecatedMarginSpacerProps
    }, (0, _react.createElement)(_vStack.VStack, {
      spacing: gradients.length ? 4 : 0
    }, !disableCustomGradients && (0, _react.createElement)(_customGradientPicker.default, {
      __nextHasNoMargin: true,
      __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
      value: value,
      onChange: onChange
    }), (gradients.length > 0 || clearable) && (0, _react.createElement)(Component, {
      ...additionalProps,
      className: className,
      clearGradient: clearGradient,
      gradients: gradients,
      onChange: onChange,
      value: value,
      actions: clearable && !disableCustomGradients && (0, _react.createElement)(_circularOptionPicker.default.ButtonAction, {
        onClick: clearGradient
      }, (0, _i18n.__)('Clear')),
      headingLevel: headingLevel
    })))
  );
}
var _default = GradientPicker;
exports.default = _default;
//# sourceMappingURL=index.js.map