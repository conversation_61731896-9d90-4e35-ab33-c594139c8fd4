{"version": 3, "names": ["_reactNative", "require", "_compose", "_icons", "_style", "_interopRequireDefault", "Stepper", "getStylesFromColorScheme", "isMaxValue", "isMinValue", "onPressInDecrement", "onPressInIncrement", "onPressOut", "value", "children", "shouldDisplayTextInput", "valueStyle", "styles", "valueTextDark", "buttonStyle", "button", "buttonDark", "_react", "createElement", "View", "style", "container", "Text", "TouchableOpacity", "disabled", "onPressIn", "opacity", "Icon", "icon", "reset", "size", "color", "testID", "plus", "_default", "withPreferredColorScheme", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/stepper-cell/stepper.ios.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Text, TouchableOpacity, View } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { withPreferredColorScheme } from '@wordpress/compose';\nimport { Icon, plus, reset } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nfunction Stepper( {\n\tgetStylesFromColorScheme,\n\tisMaxValue,\n\tisMinValue,\n\tonPressInDecrement,\n\tonPressInIncrement,\n\tonPressOut,\n\tvalue,\n\tchildren,\n\tshouldDisplayTextInput,\n} ) {\n\tconst valueStyle = getStylesFromColorScheme(\n\t\tstyles.value,\n\t\tstyles.valueTextDark\n\t);\n\tconst buttonStyle = getStylesFromColorScheme(\n\t\tstyles.button,\n\t\tstyles.buttonDark\n\t);\n\n\treturn (\n\t\t<View style={ styles.container }>\n\t\t\t{ ! shouldDisplayTextInput && (\n\t\t\t\t<Text style={ valueStyle }>{ value }</Text>\n\t\t\t) }\n\t\t\t{ children }\n\t\t\t<TouchableOpacity\n\t\t\t\tdisabled={ isMinValue }\n\t\t\t\tonPressIn={ onPressInDecrement }\n\t\t\t\tonPressOut={ onPressOut }\n\t\t\t\tstyle={ [ buttonStyle, isMinValue ? { opacity: 0.4 } : null ] }\n\t\t\t>\n\t\t\t\t<Icon icon={ reset } size={ 24 } color={ buttonStyle.color } />\n\t\t\t</TouchableOpacity>\n\t\t\t<TouchableOpacity\n\t\t\t\ttestID={ 'Increment' }\n\t\t\t\tdisabled={ isMaxValue }\n\t\t\t\tonPressIn={ onPressInIncrement }\n\t\t\t\tonPressOut={ onPressOut }\n\t\t\t\tstyle={ [ buttonStyle, isMaxValue ? { opacity: 0.4 } : null ] }\n\t\t\t>\n\t\t\t\t<Icon icon={ plus } size={ 24 } color={ buttonStyle.color } />\n\t\t\t</TouchableOpacity>\n\t\t</View>\n\t);\n}\n\nexport default withPreferredColorScheme( Stepper );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAKA,IAAAG,MAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAdA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAGA,SAASK,OAAOA,CAAE;EACjBC,wBAAwB;EACxBC,UAAU;EACVC,UAAU;EACVC,kBAAkB;EAClBC,kBAAkB;EAClBC,UAAU;EACVC,KAAK;EACLC,QAAQ;EACRC;AACD,CAAC,EAAG;EACH,MAAMC,UAAU,GAAGT,wBAAwB,CAC1CU,cAAM,CAACJ,KAAK,EACZI,cAAM,CAACC,aACR,CAAC;EACD,MAAMC,WAAW,GAAGZ,wBAAwB,CAC3CU,cAAM,CAACG,MAAM,EACbH,cAAM,CAACI,UACR,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAAwB,IAAI;IAACC,KAAK,EAAGR,cAAM,CAACS;EAAW,GAC7B,CAAEX,sBAAsB,IACzB,IAAAO,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAA2B,IAAI;IAACF,KAAK,EAAGT;EAAY,GAAGH,KAAa,CAC1C,EACCC,QAAQ,EACV,IAAAQ,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAA4B,gBAAgB;IAChBC,QAAQ,EAAGpB,UAAY;IACvBqB,SAAS,EAAGpB,kBAAoB;IAChCE,UAAU,EAAGA,UAAY;IACzBa,KAAK,EAAG,CAAEN,WAAW,EAAEV,UAAU,GAAG;MAAEsB,OAAO,EAAE;IAAI,CAAC,GAAG,IAAI;EAAI,GAE/D,IAAAT,MAAA,CAAAC,aAAA,EAACpB,MAAA,CAAA6B,IAAI;IAACC,IAAI,EAAGC,YAAO;IAACC,IAAI,EAAG,EAAI;IAACC,KAAK,EAAGjB,WAAW,CAACiB;EAAO,CAAE,CAC7C,CAAC,EACnB,IAAAd,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAA4B,gBAAgB;IAChBS,MAAM,EAAG,WAAa;IACtBR,QAAQ,EAAGrB,UAAY;IACvBsB,SAAS,EAAGnB,kBAAoB;IAChCC,UAAU,EAAGA,UAAY;IACzBa,KAAK,EAAG,CAAEN,WAAW,EAAEX,UAAU,GAAG;MAAEuB,OAAO,EAAE;IAAI,CAAC,GAAG,IAAI;EAAI,GAE/D,IAAAT,MAAA,CAAAC,aAAA,EAACpB,MAAA,CAAA6B,IAAI;IAACC,IAAI,EAAGK,WAAM;IAACH,IAAI,EAAG,EAAI;IAACC,KAAK,EAAGjB,WAAW,CAACiB;EAAO,CAAE,CAC5C,CACb,CAAC;AAET;AAAC,IAAAG,QAAA,GAEc,IAAAC,iCAAwB,EAAElC,OAAQ,CAAC;AAAAmC,OAAA,CAAAC,OAAA,GAAAH,QAAA"}