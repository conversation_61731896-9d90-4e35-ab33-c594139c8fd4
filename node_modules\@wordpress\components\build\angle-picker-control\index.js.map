{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_deprecated", "_element", "_i18n", "_flex", "_spacer", "_numberControl", "_angleCircle", "_anglePickerControlStyles", "UnforwardedAnglePickerControl", "props", "ref", "__nextHasNoMarginBottom", "className", "label", "__", "onChange", "value", "restProps", "deprecated", "since", "hint", "handleOnNumberChange", "unprocessedValue", "undefined", "inputValue", "parseInt", "classes", "classnames", "unitText", "_react", "createElement", "UnitText", "prefixedUnitText", "suffixedUnitText", "isRTL", "Root", "gap", "FlexBlock", "default", "max", "min", "size", "step", "spinControls", "prefix", "suffix", "Spacer", "marginBottom", "marginTop", "AnglePickerControl", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/angle-picker-control/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport deprecated from '@wordpress/deprecated';\nimport { forwardRef } from '@wordpress/element';\nimport { isRTL, __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { FlexBlock } from '../flex';\nimport { Spacer } from '../spacer';\nimport NumberControl from '../number-control';\nimport AngleCircle from './angle-circle';\nimport { Root, UnitText } from './styles/angle-picker-control-styles';\n\nimport type { WordPressComponentProps } from '../context';\nimport type { AnglePickerControlProps } from './types';\n\nfunction UnforwardedAnglePickerControl(\n\tprops: WordPressComponentProps< AnglePickerControlProps, 'div' >,\n\tref: ForwardedRef< any >\n) {\n\tconst {\n\t\t__nextHasNoMarginBottom = false,\n\t\tclassName,\n\t\tlabel = __( 'Angle' ),\n\t\tonChange,\n\t\tvalue,\n\t\t...restProps\n\t} = props;\n\n\tif ( ! __nextHasNoMarginBottom ) {\n\t\tdeprecated(\n\t\t\t'Bottom margin styles for wp.components.AnglePickerControl',\n\t\t\t{\n\t\t\t\tsince: '6.1',\n\t\t\t\thint: 'Set the `__nextHasNoMarginBottom` prop to true to start opting into the new styles, which will become the default in a future version.',\n\t\t\t}\n\t\t);\n\t}\n\n\tconst handleOnNumberChange = ( unprocessedValue: string | undefined ) => {\n\t\tif ( onChange === undefined ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst inputValue =\n\t\t\tunprocessedValue !== undefined && unprocessedValue !== ''\n\t\t\t\t? parseInt( unprocessedValue, 10 )\n\t\t\t\t: 0;\n\t\tonChange( inputValue );\n\t};\n\n\tconst classes = classnames( 'components-angle-picker-control', className );\n\n\tconst unitText = <UnitText>°</UnitText>;\n\tconst [ prefixedUnitText, suffixedUnitText ] = isRTL()\n\t\t? [ unitText, null ]\n\t\t: [ null, unitText ];\n\n\treturn (\n\t\t<Root\n\t\t\t{ ...restProps }\n\t\t\tref={ ref }\n\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\tclassName={ classes }\n\t\t\tgap={ 2 }\n\t\t>\n\t\t\t<FlexBlock>\n\t\t\t\t<NumberControl\n\t\t\t\t\tlabel={ label }\n\t\t\t\t\tclassName=\"components-angle-picker-control__input-field\"\n\t\t\t\t\tmax={ 360 }\n\t\t\t\t\tmin={ 0 }\n\t\t\t\t\tonChange={ handleOnNumberChange }\n\t\t\t\t\tsize=\"__unstable-large\"\n\t\t\t\t\tstep=\"1\"\n\t\t\t\t\tvalue={ value }\n\t\t\t\t\tspinControls=\"none\"\n\t\t\t\t\tprefix={ prefixedUnitText }\n\t\t\t\t\tsuffix={ suffixedUnitText }\n\t\t\t\t/>\n\t\t\t</FlexBlock>\n\t\t\t<Spacer marginBottom=\"1\" marginTop=\"auto\">\n\t\t\t\t<AngleCircle\n\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\tvalue={ value }\n\t\t\t\t\tonChange={ onChange }\n\t\t\t\t/>\n\t\t\t</Spacer>\n\t\t</Root>\n\t);\n}\n\n/**\n * `AnglePickerControl` is a React component to render a UI that allows users to\n * pick an angle. Users can choose an angle in a visual UI with the mouse by\n * dragging an angle indicator inside a circle or by directly inserting the\n * desired angle in a text field.\n *\n * ```jsx\n * import { useState } from '@wordpress/element';\n * import { AnglePickerControl } from '@wordpress/components';\n *\n * function Example() {\n *   const [ angle, setAngle ] = useState( 0 );\n *   return (\n *     <AnglePickerControl\n *       value={ angle }\n *       onChange={ setAngle }\n *       __nextHasNoMarginBottom\n *     </>\n *   );\n * }\n * ```\n */\nexport const AnglePickerControl = forwardRef( UnforwardedAnglePickerControl );\n\nexport default AnglePickerControl;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAKA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,cAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,YAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,yBAAA,GAAAR,OAAA;AApBA;AACA;AACA;;AAIA;AACA;AACA;;AAKA;AACA;AACA;;AAUA,SAASS,6BAA6BA,CACrCC,KAAgE,EAChEC,GAAwB,EACvB;EACD,MAAM;IACLC,uBAAuB,GAAG,KAAK;IAC/BC,SAAS;IACTC,KAAK,GAAG,IAAAC,QAAE,EAAE,OAAQ,CAAC;IACrBC,QAAQ;IACRC,KAAK;IACL,GAAGC;EACJ,CAAC,GAAGR,KAAK;EAET,IAAK,CAAEE,uBAAuB,EAAG;IAChC,IAAAO,mBAAU,EACT,2DAA2D,EAC3D;MACCC,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE;IACP,CACD,CAAC;EACF;EAEA,MAAMC,oBAAoB,GAAKC,gBAAoC,IAAM;IACxE,IAAKP,QAAQ,KAAKQ,SAAS,EAAG;MAC7B;IACD;IAEA,MAAMC,UAAU,GACfF,gBAAgB,KAAKC,SAAS,IAAID,gBAAgB,KAAK,EAAE,GACtDG,QAAQ,CAAEH,gBAAgB,EAAE,EAAG,CAAC,GAChC,CAAC;IACLP,QAAQ,CAAES,UAAW,CAAC;EACvB,CAAC;EAED,MAAME,OAAO,GAAG,IAAAC,mBAAU,EAAE,iCAAiC,EAAEf,SAAU,CAAC;EAE1E,MAAMgB,QAAQ,GAAG,IAAAC,MAAA,CAAAC,aAAA,EAACvB,yBAAA,CAAAwB,QAAQ,QAAC,MAAW,CAAC;EACvC,MAAM,CAAEC,gBAAgB,EAAEC,gBAAgB,CAAE,GAAG,IAAAC,WAAK,EAAC,CAAC,GACnD,CAAEN,QAAQ,EAAE,IAAI,CAAE,GAClB,CAAE,IAAI,EAAEA,QAAQ,CAAE;EAErB,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACvB,yBAAA,CAAA4B,IAAI;IAAA,GACClB,SAAS;IACdP,GAAG,EAAGA,GAAK;IACXC,uBAAuB,EAAGA,uBAAyB;IACnDC,SAAS,EAAGc,OAAS;IACrBU,GAAG,EAAG;EAAG,GAET,IAAAP,MAAA,CAAAC,aAAA,EAAC3B,KAAA,CAAAkC,SAAS,QACT,IAAAR,MAAA,CAAAC,aAAA,EAACzB,cAAA,CAAAiC,OAAa;IACbzB,KAAK,EAAGA,KAAO;IACfD,SAAS,EAAC,8CAA8C;IACxD2B,GAAG,EAAG,GAAK;IACXC,GAAG,EAAG,CAAG;IACTzB,QAAQ,EAAGM,oBAAsB;IACjCoB,IAAI,EAAC,kBAAkB;IACvBC,IAAI,EAAC,GAAG;IACR1B,KAAK,EAAGA,KAAO;IACf2B,YAAY,EAAC,MAAM;IACnBC,MAAM,EAAGZ,gBAAkB;IAC3Ba,MAAM,EAAGZ;EAAkB,CAC3B,CACS,CAAC,EACZ,IAAAJ,MAAA,CAAAC,aAAA,EAAC1B,OAAA,CAAA0C,MAAM;IAACC,YAAY,EAAC,GAAG;IAACC,SAAS,EAAC;EAAM,GACxC,IAAAnB,MAAA,CAAAC,aAAA,EAACxB,YAAA,CAAAgC,OAAW;IACX,eAAY,MAAM;IAClBtB,KAAK,EAAGA,KAAO;IACfD,QAAQ,EAAGA;EAAU,CACrB,CACM,CACH,CAAC;AAET;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMkC,kBAAkB,GAAG,IAAAC,mBAAU,EAAE1C,6BAA8B,CAAC;AAAC2C,OAAA,CAAAF,kBAAA,GAAAA,kBAAA;AAAA,IAAAG,QAAA,GAE/DH,kBAAkB;AAAAE,OAAA,CAAAb,OAAA,GAAAc,QAAA"}