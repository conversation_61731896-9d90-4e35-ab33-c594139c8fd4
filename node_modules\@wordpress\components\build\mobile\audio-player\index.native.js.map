{"version": 3, "names": ["_reactNative", "require", "_reactNativeVideo", "_interopRequireDefault", "_primitives", "_components", "_compose", "_i18n", "_icons", "_reactNativeBridge", "_url", "_element", "_styles", "_audioUrl<PERSON><PERSON>er", "isIOS", "Platform", "OS", "Player", "isUploadInProgress", "isUploadFailed", "attributes", "isSelected", "id", "src", "paused", "setPaused", "useState", "onPressListen", "player", "presentFullscreenPlayer", "Linking", "canOpenURL", "then", "supported", "<PERSON><PERSON>", "alert", "__", "openURL", "catch", "containerStyle", "useEditorColorScheme", "styles", "container", "containerDark", "iconStyle", "icon", "iconDark", "iconDisabledStyle", "iconDisabled", "iconDisabledDark", "isDisabled", "finalIconStyle", "iconContainerStyle", "iconContainer", "iconContainerDark", "titleContainerStyle", "<PERSON><PERSON><PERSON><PERSON>", "titleContainerIOS", "titleContainerAndroid", "titleStyle", "title", "titleDark", "uploadFailedStyle", "uploadFailed", "uploadFailedDark", "subtitleStyle", "subtitle", "subtitleDark", "finalSubtitleStyle", "buttonBackgroundStyle", "buttonBackground", "buttonBackgroundDark", "extension", "result", "parseAudioUrl", "getSubtitleValue", "onAudioUploadCancelDialog", "requestImageUploadCancelDialog", "getProtocol", "requestImageFailedRetryDialog", "_react", "createElement", "TouchableWithoutFeedback", "accessible", "disabled", "onPress", "View", "style", "Icon", "audio", "size", "Text", "subtitleContainer", "warning", "errorIcon", "accessibilityLabel", "accessibilityRole", "accessibilityHint", "buttonText", "default", "source", "uri", "ref", "controls", "ignoreSilentSwitch", "onFullscreenPlayerWillPresent", "onFullscreenPlayerDidDismiss", "_default", "withPreferredColorScheme", "exports"], "sources": ["@wordpress/components/src/mobile/audio-player/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tText,\n\tTouchableWithoutFeedback,\n\tLinking,\n\tAlert,\n\tPlatform,\n} from 'react-native';\nimport { default as VideoPlayer } from 'react-native-video';\n\n/**\n * WordPress dependencies\n */\nimport { View } from '@wordpress/primitives';\nimport { Icon, useEditorColorScheme } from '@wordpress/components';\nimport { withPreferredColorScheme } from '@wordpress/compose';\nimport { __ } from '@wordpress/i18n';\nimport { audio, warning } from '@wordpress/icons';\nimport {\n\trequestImageFailedRetryDialog,\n\trequestImageUploadCancelDialog,\n} from '@wordpress/react-native-bridge';\nimport { getProtocol } from '@wordpress/url';\nimport { useState } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\nimport { parseAudioUrl } from './audio-url-parser.native';\n\nconst isIOS = Platform.OS === 'ios';\n\nfunction Player( {\n\tisUploadInProgress,\n\tisUploadFailed,\n\tattributes,\n\tisSelected,\n} ) {\n\tconst { id, src } = attributes;\n\tconst [ paused, setPaused ] = useState( true );\n\n\tconst onPressListen = () => {\n\t\tif ( src ) {\n\t\t\tif ( isIOS && this.player ) {\n\t\t\t\tthis.player.presentFullscreenPlayer();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tLinking.canOpenURL( src )\n\t\t\t\t.then( ( supported ) => {\n\t\t\t\t\tif ( ! supported ) {\n\t\t\t\t\t\tAlert.alert(\n\t\t\t\t\t\t\t__( 'Problem opening the audio' ),\n\t\t\t\t\t\t\t__( 'No application can handle this request.' )\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn Linking.openURL( src );\n\t\t\t\t\t}\n\t\t\t\t} )\n\t\t\t\t.catch( () => {\n\t\t\t\t\tAlert.alert(\n\t\t\t\t\t\t__( 'Problem opening the audio' ),\n\t\t\t\t\t\t__( 'An unknown error occurred. Please try again.' )\n\t\t\t\t\t);\n\t\t\t\t} );\n\t\t}\n\t};\n\n\tconst containerStyle = useEditorColorScheme(\n\t\tstyles.container,\n\t\tstyles.containerDark\n\t);\n\n\tconst iconStyle = useEditorColorScheme( styles.icon, styles.iconDark );\n\n\tconst iconDisabledStyle = useEditorColorScheme(\n\t\tstyles.iconDisabled,\n\t\tstyles.iconDisabledDark\n\t);\n\n\tconst isDisabled = isUploadFailed || isUploadInProgress;\n\n\tconst finalIconStyle = {\n\t\t...iconStyle,\n\t\t...( isDisabled && iconDisabledStyle ),\n\t};\n\n\tconst iconContainerStyle = useEditorColorScheme(\n\t\tstyles.iconContainer,\n\t\tstyles.iconContainerDark\n\t);\n\n\tconst titleContainerStyle = {\n\t\t...styles.titleContainer,\n\t\t...( isIOS ? styles.titleContainerIOS : styles.titleContainerAndroid ),\n\t};\n\n\tconst titleStyle = useEditorColorScheme( styles.title, styles.titleDark );\n\n\tconst uploadFailedStyle = useEditorColorScheme(\n\t\tstyles.uploadFailed,\n\t\tstyles.uploadFailedDark\n\t);\n\n\tconst subtitleStyle = useEditorColorScheme(\n\t\tstyles.subtitle,\n\t\tstyles.subtitleDark\n\t);\n\n\tconst finalSubtitleStyle = {\n\t\t...subtitleStyle,\n\t\t...( isUploadFailed && uploadFailedStyle ),\n\t};\n\n\tconst buttonBackgroundStyle = useEditorColorScheme(\n\t\tstyles.buttonBackground,\n\t\tstyles.buttonBackgroundDark\n\t);\n\n\tlet title = '';\n\tlet extension = '';\n\n\tif ( src ) {\n\t\tconst result = parseAudioUrl( src );\n\t\textension = result.extension;\n\t\ttitle = result.title;\n\t}\n\n\tconst getSubtitleValue = () => {\n\t\tif ( isUploadInProgress ) {\n\t\t\treturn __( 'Uploading…' );\n\t\t}\n\t\tif ( isUploadFailed ) {\n\t\t\treturn __( 'Failed to insert audio file. Please tap for options.' );\n\t\t}\n\t\treturn (\n\t\t\textension +\n\t\t\t// translators: displays audio file extension. e.g. MP3 audio file\n\t\t\t__( 'audio file' )\n\t\t);\n\t};\n\n\tfunction onAudioUploadCancelDialog() {\n\t\tif ( isUploadInProgress ) {\n\t\t\trequestImageUploadCancelDialog( id );\n\t\t} else if ( id && getProtocol( src ) === 'file:' ) {\n\t\t\trequestImageFailedRetryDialog( id );\n\t\t}\n\t}\n\n\treturn (\n\t\t<TouchableWithoutFeedback\n\t\t\taccessible={ ! isSelected }\n\t\t\tdisabled={ ! isSelected }\n\t\t\tonPress={ onAudioUploadCancelDialog }\n\t\t>\n\t\t\t<View style={ containerStyle }>\n\t\t\t\t<View style={ iconContainerStyle }>\n\t\t\t\t\t<Icon icon={ audio } style={ finalIconStyle } size={ 24 } />\n\t\t\t\t</View>\n\t\t\t\t<View style={ titleContainerStyle }>\n\t\t\t\t\t<Text style={ titleStyle }>{ title }</Text>\n\t\t\t\t\t<View style={ styles.subtitleContainer }>\n\t\t\t\t\t\t{ isUploadFailed && (\n\t\t\t\t\t\t\t<Icon\n\t\t\t\t\t\t\t\ticon={ warning }\n\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t...styles.errorIcon,\n\t\t\t\t\t\t\t\t\t...uploadFailedStyle,\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\tsize={ 16 }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t) }\n\t\t\t\t\t\t<Text style={ finalSubtitleStyle }>\n\t\t\t\t\t\t\t{ getSubtitleValue() }\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</View>\n\t\t\t\t</View>\n\t\t\t\t{ ! isDisabled && (\n\t\t\t\t\t<TouchableWithoutFeedback\n\t\t\t\t\t\taccessibilityLabel={ __( 'Audio Player' ) }\n\t\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\t\taccessibilityHint={ __(\n\t\t\t\t\t\t\t'Double tap to listen the audio file'\n\t\t\t\t\t\t) }\n\t\t\t\t\t\tonPress={ onPressListen }\n\t\t\t\t\t>\n\t\t\t\t\t\t<View style={ buttonBackgroundStyle }>\n\t\t\t\t\t\t\t<Text style={ styles.buttonText }>\n\t\t\t\t\t\t\t\t{ __( 'OPEN' ) }\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</View>\n\t\t\t\t\t</TouchableWithoutFeedback>\n\t\t\t\t) }\n\t\t\t\t{ isIOS && (\n\t\t\t\t\t<VideoPlayer\n\t\t\t\t\t\tsource={ { uri: src } }\n\t\t\t\t\t\tpaused={ paused }\n\t\t\t\t\t\tref={ ( ref ) => {\n\t\t\t\t\t\t\tthis.player = ref;\n\t\t\t\t\t\t} }\n\t\t\t\t\t\tcontrols={ false }\n\t\t\t\t\t\tignoreSilentSwitch={ 'ignore' }\n\t\t\t\t\t\tonFullscreenPlayerWillPresent={ () => {\n\t\t\t\t\t\t\tsetPaused( false );\n\t\t\t\t\t\t} }\n\t\t\t\t\t\tonFullscreenPlayerDidDismiss={ () => {\n\t\t\t\t\t\t\tsetPaused( true );\n\t\t\t\t\t\t} }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t</View>\n\t\t</TouchableWithoutFeedback>\n\t);\n}\n\nexport default withPreferredColorScheme( Player );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAOA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,kBAAA,GAAAR,OAAA;AAIA,IAAAS,IAAA,GAAAT,OAAA;AACA,IAAAU,QAAA,GAAAV,OAAA;AAKA,IAAAW,OAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,eAAA,GAAAZ,OAAA;AA/BA;AACA;AACA;;AAUA;AACA;AACA;;AAaA;AACA;AACA;;AAIA,MAAMa,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;AAEnC,SAASC,MAAMA,CAAE;EAChBC,kBAAkB;EAClBC,cAAc;EACdC,UAAU;EACVC;AACD,CAAC,EAAG;EACH,MAAM;IAAEC,EAAE;IAAEC;EAAI,CAAC,GAAGH,UAAU;EAC9B,MAAM,CAAEI,MAAM,EAAEC,SAAS,CAAE,GAAG,IAAAC,iBAAQ,EAAE,IAAK,CAAC;EAE9C,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC3B,IAAKJ,GAAG,EAAG;MACV,IAAKT,KAAK,IAAI,IAAI,CAACc,MAAM,EAAG;QAC3B,IAAI,CAACA,MAAM,CAACC,uBAAuB,CAAC,CAAC;QACrC;MACD;MAEAC,oBAAO,CAACC,UAAU,CAAER,GAAI,CAAC,CACvBS,IAAI,CAAIC,SAAS,IAAM;QACvB,IAAK,CAAEA,SAAS,EAAG;UAClBC,kBAAK,CAACC,KAAK,CACV,IAAAC,QAAE,EAAE,2BAA4B,CAAC,EACjC,IAAAA,QAAE,EAAE,yCAA0C,CAC/C,CAAC;QACF,CAAC,MAAM;UACN,OAAON,oBAAO,CAACO,OAAO,CAAEd,GAAI,CAAC;QAC9B;MACD,CAAE,CAAC,CACFe,KAAK,CAAE,MAAM;QACbJ,kBAAK,CAACC,KAAK,CACV,IAAAC,QAAE,EAAE,2BAA4B,CAAC,EACjC,IAAAA,QAAE,EAAE,8CAA+C,CACpD,CAAC;MACF,CAAE,CAAC;IACL;EACD,CAAC;EAED,MAAMG,cAAc,GAAG,IAAAC,gCAAoB,EAC1CC,eAAM,CAACC,SAAS,EAChBD,eAAM,CAACE,aACR,CAAC;EAED,MAAMC,SAAS,GAAG,IAAAJ,gCAAoB,EAAEC,eAAM,CAACI,IAAI,EAAEJ,eAAM,CAACK,QAAS,CAAC;EAEtE,MAAMC,iBAAiB,GAAG,IAAAP,gCAAoB,EAC7CC,eAAM,CAACO,YAAY,EACnBP,eAAM,CAACQ,gBACR,CAAC;EAED,MAAMC,UAAU,GAAG/B,cAAc,IAAID,kBAAkB;EAEvD,MAAMiC,cAAc,GAAG;IACtB,GAAGP,SAAS;IACZ,IAAKM,UAAU,IAAIH,iBAAiB;EACrC,CAAC;EAED,MAAMK,kBAAkB,GAAG,IAAAZ,gCAAoB,EAC9CC,eAAM,CAACY,aAAa,EACpBZ,eAAM,CAACa,iBACR,CAAC;EAED,MAAMC,mBAAmB,GAAG;IAC3B,GAAGd,eAAM,CAACe,cAAc;IACxB,IAAK1C,KAAK,GAAG2B,eAAM,CAACgB,iBAAiB,GAAGhB,eAAM,CAACiB,qBAAqB;EACrE,CAAC;EAED,MAAMC,UAAU,GAAG,IAAAnB,gCAAoB,EAAEC,eAAM,CAACmB,KAAK,EAAEnB,eAAM,CAACoB,SAAU,CAAC;EAEzE,MAAMC,iBAAiB,GAAG,IAAAtB,gCAAoB,EAC7CC,eAAM,CAACsB,YAAY,EACnBtB,eAAM,CAACuB,gBACR,CAAC;EAED,MAAMC,aAAa,GAAG,IAAAzB,gCAAoB,EACzCC,eAAM,CAACyB,QAAQ,EACfzB,eAAM,CAAC0B,YACR,CAAC;EAED,MAAMC,kBAAkB,GAAG;IAC1B,GAAGH,aAAa;IAChB,IAAK9C,cAAc,IAAI2C,iBAAiB;EACzC,CAAC;EAED,MAAMO,qBAAqB,GAAG,IAAA7B,gCAAoB,EACjDC,eAAM,CAAC6B,gBAAgB,EACvB7B,eAAM,CAAC8B,oBACR,CAAC;EAED,IAAIX,KAAK,GAAG,EAAE;EACd,IAAIY,SAAS,GAAG,EAAE;EAElB,IAAKjD,GAAG,EAAG;IACV,MAAMkD,MAAM,GAAG,IAAAC,6BAAa,EAAEnD,GAAI,CAAC;IACnCiD,SAAS,GAAGC,MAAM,CAACD,SAAS;IAC5BZ,KAAK,GAAGa,MAAM,CAACb,KAAK;EACrB;EAEA,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC9B,IAAKzD,kBAAkB,EAAG;MACzB,OAAO,IAAAkB,QAAE,EAAE,YAAa,CAAC;IAC1B;IACA,IAAKjB,cAAc,EAAG;MACrB,OAAO,IAAAiB,QAAE,EAAE,sDAAuD,CAAC;IACpE;IACA,OACCoC,SAAS;IACT;IACA,IAAApC,QAAE,EAAE,YAAa,CAAC;EAEpB,CAAC;EAED,SAASwC,yBAAyBA,CAAA,EAAG;IACpC,IAAK1D,kBAAkB,EAAG;MACzB,IAAA2D,iDAA8B,EAAEvD,EAAG,CAAC;IACrC,CAAC,MAAM,IAAKA,EAAE,IAAI,IAAAwD,gBAAW,EAAEvD,GAAI,CAAC,KAAK,OAAO,EAAG;MAClD,IAAAwD,gDAA6B,EAAEzD,EAAG,CAAC;IACpC;EACD;EAEA,OACC,IAAA0D,MAAA,CAAAC,aAAA,EAACjF,YAAA,CAAAkF,wBAAwB;IACxBC,UAAU,EAAG,CAAE9D,UAAY;IAC3B+D,QAAQ,EAAG,CAAE/D,UAAY;IACzBgE,OAAO,EAAGT;EAA2B,GAErC,IAAAI,MAAA,CAAAC,aAAA,EAAC7E,WAAA,CAAAkF,IAAI;IAACC,KAAK,EAAGhD;EAAgB,GAC7B,IAAAyC,MAAA,CAAAC,aAAA,EAAC7E,WAAA,CAAAkF,IAAI;IAACC,KAAK,EAAGnC;EAAoB,GACjC,IAAA4B,MAAA,CAAAC,aAAA,EAAC5E,WAAA,CAAAmF,IAAI;IAAC3C,IAAI,EAAG4C,YAAO;IAACF,KAAK,EAAGpC,cAAgB;IAACuC,IAAI,EAAG;EAAI,CAAE,CACtD,CAAC,EACP,IAAAV,MAAA,CAAAC,aAAA,EAAC7E,WAAA,CAAAkF,IAAI;IAACC,KAAK,EAAGhC;EAAqB,GAClC,IAAAyB,MAAA,CAAAC,aAAA,EAACjF,YAAA,CAAA2F,IAAI;IAACJ,KAAK,EAAG5B;EAAY,GAAGC,KAAa,CAAC,EAC3C,IAAAoB,MAAA,CAAAC,aAAA,EAAC7E,WAAA,CAAAkF,IAAI;IAACC,KAAK,EAAG9C,eAAM,CAACmD;EAAmB,GACrCzE,cAAc,IACf,IAAA6D,MAAA,CAAAC,aAAA,EAAC5E,WAAA,CAAAmF,IAAI;IACJ3C,IAAI,EAAGgD,cAAS;IAChBN,KAAK,EAAG;MACP,GAAG9C,eAAM,CAACqD,SAAS;MACnB,GAAGhC;IACJ,CAAG;IACH4B,IAAI,EAAG;EAAI,CACX,CACD,EACD,IAAAV,MAAA,CAAAC,aAAA,EAACjF,YAAA,CAAA2F,IAAI;IAACJ,KAAK,EAAGnB;EAAoB,GAC/BO,gBAAgB,CAAC,CACd,CACD,CACD,CAAC,EACL,CAAEzB,UAAU,IACb,IAAA8B,MAAA,CAAAC,aAAA,EAACjF,YAAA,CAAAkF,wBAAwB;IACxBa,kBAAkB,EAAG,IAAA3D,QAAE,EAAE,cAAe,CAAG;IAC3C4D,iBAAiB,EAAG,QAAU;IAC9BC,iBAAiB,EAAG,IAAA7D,QAAE,EACrB,qCACD,CAAG;IACHiD,OAAO,EAAG1D;EAAe,GAEzB,IAAAqD,MAAA,CAAAC,aAAA,EAAC7E,WAAA,CAAAkF,IAAI;IAACC,KAAK,EAAGlB;EAAuB,GACpC,IAAAW,MAAA,CAAAC,aAAA,EAACjF,YAAA,CAAA2F,IAAI;IAACJ,KAAK,EAAG9C,eAAM,CAACyD;EAAY,GAC9B,IAAA9D,QAAE,EAAE,MAAO,CACR,CACD,CACmB,CAC1B,EACCtB,KAAK,IACN,IAAAkE,MAAA,CAAAC,aAAA,EAAC/E,iBAAA,CAAAiG,OAAW;IACXC,MAAM,EAAG;MAAEC,GAAG,EAAE9E;IAAI,CAAG;IACvBC,MAAM,EAAGA,MAAQ;IACjB8E,GAAG,EAAKA,GAAG,IAAM;MAChB,IAAI,CAAC1E,MAAM,GAAG0E,GAAG;IAClB,CAAG;IACHC,QAAQ,EAAG,KAAO;IAClBC,kBAAkB,EAAG,QAAU;IAC/BC,6BAA6B,EAAGA,CAAA,KAAM;MACrChF,SAAS,CAAE,KAAM,CAAC;IACnB,CAAG;IACHiF,4BAA4B,EAAGA,CAAA,KAAM;MACpCjF,SAAS,CAAE,IAAK,CAAC;IAClB;EAAG,CACH,CAEG,CACmB,CAAC;AAE7B;AAAC,IAAAkF,QAAA,GAEc,IAAAC,iCAAwB,EAAE3F,MAAO,CAAC;AAAA4F,OAAA,CAAAV,OAAA,GAAAQ,QAAA"}