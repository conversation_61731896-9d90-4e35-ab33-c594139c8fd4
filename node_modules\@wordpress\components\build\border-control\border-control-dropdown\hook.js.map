{"version": 3, "names": ["_element", "require", "styles", "_interopRequireWildcard", "_utils", "_context", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useBorderControlDropdown", "props", "border", "className", "colors", "enableAlpha", "enableStyle", "onChange", "previousStyleSelection", "size", "__experimentalIsRenderedInSidebar", "otherProps", "useContextSystem", "widthValue", "parseQuantityAndUnitFromRawValue", "width", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "onColorChange", "color", "style", "onStyleChange", "onReset", "undefined", "cx", "useCx", "classes", "useMemo", "borderControlDropdown", "indicatorClassName", "borderColorIndicator", "indicatorWrapperClassName", "colorIndicatorWrapper", "popoverControlsClassName", "borderControlPopoverControls", "popoverContentClassName", "borderControlPopoverContent", "resetButtonClassName", "resetButton"], "sources": ["@wordpress/components/src/border-control/border-control-dropdown/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport * as styles from '../styles';\nimport { parseQuantityAndUnitFromRawValue } from '../../unit-control/utils';\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useCx } from '../../utils/hooks/use-cx';\n\nimport type { DropdownProps } from '../types';\n\nexport function useBorderControlDropdown(\n\tprops: WordPressComponentProps< DropdownProps, 'div' >\n) {\n\tconst {\n\t\tborder,\n\t\tclassName,\n\t\tcolors = [],\n\t\tenableAlpha = false,\n\t\tenableStyle = true,\n\t\tonChange,\n\t\tpreviousStyleSelection,\n\t\tsize = 'default',\n\t\t__experimentalIsRenderedInSidebar = false,\n\t\t...otherProps\n\t} = useContextSystem( props, 'BorderControlDropdown' );\n\n\tconst [ widthValue ] = parseQuantityAndUnitFromRawValue( border?.width );\n\tconst hasZeroWidth = widthValue === 0;\n\n\tconst onColorChange = ( color?: string ) => {\n\t\tconst style =\n\t\t\tborder?.style === 'none' ? previousStyleSelection : border?.style;\n\t\tconst width = hasZeroWidth && !! color ? '1px' : border?.width;\n\n\t\tonChange( { color, style, width } );\n\t};\n\n\tconst onStyleChange = ( style?: string ) => {\n\t\tconst width = hasZeroWidth && !! style ? '1px' : border?.width;\n\t\tonChange( { ...border, style, width } );\n\t};\n\n\tconst onReset = () => {\n\t\tonChange( {\n\t\t\t...border,\n\t\t\tcolor: undefined,\n\t\t\tstyle: undefined,\n\t\t} );\n\t};\n\n\t// Generate class names.\n\tconst cx = useCx();\n\tconst classes = useMemo( () => {\n\t\treturn cx( styles.borderControlDropdown, className );\n\t}, [ className, cx ] );\n\n\tconst indicatorClassName = useMemo( () => {\n\t\treturn cx( styles.borderColorIndicator );\n\t}, [ cx ] );\n\n\tconst indicatorWrapperClassName = useMemo( () => {\n\t\treturn cx( styles.colorIndicatorWrapper( border, size ) );\n\t}, [ border, cx, size ] );\n\n\tconst popoverControlsClassName = useMemo( () => {\n\t\treturn cx( styles.borderControlPopoverControls );\n\t}, [ cx ] );\n\n\tconst popoverContentClassName = useMemo( () => {\n\t\treturn cx( styles.borderControlPopoverContent );\n\t}, [ cx ] );\n\n\tconst resetButtonClassName = useMemo( () => {\n\t\treturn cx( styles.resetButton );\n\t}, [ cx ] );\n\n\treturn {\n\t\t...otherProps,\n\t\tborder,\n\t\tclassName: classes,\n\t\tcolors,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\tindicatorClassName,\n\t\tindicatorWrapperClassName,\n\t\tonColorChange,\n\t\tonStyleChange,\n\t\tonReset,\n\t\tpopoverContentClassName,\n\t\tpopoverControlsClassName,\n\t\tresetButtonClassName,\n\t\tsize,\n\t\t__experimentalIsRenderedInSidebar,\n\t};\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAAiD,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAZjD;AACA;AACA;;AAGA;AACA;AACA;;AASO,SAASW,wBAAwBA,CACvCC,KAAsD,EACrD;EACD,MAAM;IACLC,MAAM;IACNC,SAAS;IACTC,MAAM,GAAG,EAAE;IACXC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAG,IAAI;IAClBC,QAAQ;IACRC,sBAAsB;IACtBC,IAAI,GAAG,SAAS;IAChBC,iCAAiC,GAAG,KAAK;IACzC,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEX,KAAK,EAAE,uBAAwB,CAAC;EAEtD,MAAM,CAAEY,UAAU,CAAE,GAAG,IAAAC,uCAAgC,EAAEZ,MAAM,EAAEa,KAAM,CAAC;EACxE,MAAMC,YAAY,GAAGH,UAAU,KAAK,CAAC;EAErC,MAAMI,aAAa,GAAKC,KAAc,IAAM;IAC3C,MAAMC,KAAK,GACVjB,MAAM,EAAEiB,KAAK,KAAK,MAAM,GAAGX,sBAAsB,GAAGN,MAAM,EAAEiB,KAAK;IAClE,MAAMJ,KAAK,GAAGC,YAAY,IAAI,CAAC,CAAEE,KAAK,GAAG,KAAK,GAAGhB,MAAM,EAAEa,KAAK;IAE9DR,QAAQ,CAAE;MAAEW,KAAK;MAAEC,KAAK;MAAEJ;IAAM,CAAE,CAAC;EACpC,CAAC;EAED,MAAMK,aAAa,GAAKD,KAAc,IAAM;IAC3C,MAAMJ,KAAK,GAAGC,YAAY,IAAI,CAAC,CAAEG,KAAK,GAAG,KAAK,GAAGjB,MAAM,EAAEa,KAAK;IAC9DR,QAAQ,CAAE;MAAE,GAAGL,MAAM;MAAEiB,KAAK;MAAEJ;IAAM,CAAE,CAAC;EACxC,CAAC;EAED,MAAMM,OAAO,GAAGA,CAAA,KAAM;IACrBd,QAAQ,CAAE;MACT,GAAGL,MAAM;MACTgB,KAAK,EAAEI,SAAS;MAChBH,KAAK,EAAEG;IACR,CAAE,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,OAAOH,EAAE,CAAElD,MAAM,CAACsD,qBAAqB,EAAExB,SAAU,CAAC;EACrD,CAAC,EAAE,CAAEA,SAAS,EAAEoB,EAAE,CAAG,CAAC;EAEtB,MAAMK,kBAAkB,GAAG,IAAAF,gBAAO,EAAE,MAAM;IACzC,OAAOH,EAAE,CAAElD,MAAM,CAACwD,oBAAqB,CAAC;EACzC,CAAC,EAAE,CAAEN,EAAE,CAAG,CAAC;EAEX,MAAMO,yBAAyB,GAAG,IAAAJ,gBAAO,EAAE,MAAM;IAChD,OAAOH,EAAE,CAAElD,MAAM,CAAC0D,qBAAqB,CAAE7B,MAAM,EAAEO,IAAK,CAAE,CAAC;EAC1D,CAAC,EAAE,CAAEP,MAAM,EAAEqB,EAAE,EAAEd,IAAI,CAAG,CAAC;EAEzB,MAAMuB,wBAAwB,GAAG,IAAAN,gBAAO,EAAE,MAAM;IAC/C,OAAOH,EAAE,CAAElD,MAAM,CAAC4D,4BAA6B,CAAC;EACjD,CAAC,EAAE,CAAEV,EAAE,CAAG,CAAC;EAEX,MAAMW,uBAAuB,GAAG,IAAAR,gBAAO,EAAE,MAAM;IAC9C,OAAOH,EAAE,CAAElD,MAAM,CAAC8D,2BAA4B,CAAC;EAChD,CAAC,EAAE,CAAEZ,EAAE,CAAG,CAAC;EAEX,MAAMa,oBAAoB,GAAG,IAAAV,gBAAO,EAAE,MAAM;IAC3C,OAAOH,EAAE,CAAElD,MAAM,CAACgE,WAAY,CAAC;EAChC,CAAC,EAAE,CAAEd,EAAE,CAAG,CAAC;EAEX,OAAO;IACN,GAAGZ,UAAU;IACbT,MAAM;IACNC,SAAS,EAAEsB,OAAO;IAClBrB,MAAM;IACNC,WAAW;IACXC,WAAW;IACXsB,kBAAkB;IAClBE,yBAAyB;IACzBb,aAAa;IACbG,aAAa;IACbC,OAAO;IACPa,uBAAuB;IACvBF,wBAAwB;IACxBI,oBAAoB;IACpB3B,IAAI;IACJC;EACD,CAAC;AACF"}