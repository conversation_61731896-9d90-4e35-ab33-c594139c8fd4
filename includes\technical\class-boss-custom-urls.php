<?php
/**
 * Classe pour gérer les URLs personnalisées dans les sitemaps
 *
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 * @since      1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Boss_Custom_Urls
 *
 * Gère les URLs personnalisées pour les sitemaps
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */
class Boss_Custom_Urls {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Option pour stocker les URLs personnalisées.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $custom_urls_option    Le nom de l'option.
     */
    private $custom_urls_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->custom_urls_option = 'boss-seo_custom_sitemap_urls';
    }

    /**
     * Récupère toutes les URLs personnalisées.
     *
     * @since    1.2.0
     * @return   array    Les URLs personnalisées.
     */
    public function get_all_custom_urls() {
        $urls = get_option( $this->custom_urls_option, array() );
        
        // S'assurer que chaque URL a un ID unique
        foreach ( $urls as $index => $url ) {
            if ( ! isset( $url['id'] ) ) {
                $urls[$index]['id'] = $this->generate_url_id();
            }
        }
        
        // Sauvegarder si des IDs ont été ajoutés
        if ( count( array_filter( $urls, function( $url ) { return ! isset( $url['id'] ); } ) ) > 0 ) {
            update_option( $this->custom_urls_option, $urls );
        }
        
        return $urls;
    }

    /**
     * Ajoute une nouvelle URL personnalisée.
     *
     * @since    1.2.0
     * @param    array    $url_data    Les données de l'URL.
     * @return   array                 Résultat de l'ajout.
     */
    public function add_custom_url( $url_data ) {
        try {
            // Valider les données
            $validation = $this->validate_url_data( $url_data );
            if ( ! $validation['valid'] ) {
                return array(
                    'success' => false,
                    'message' => $validation['message'],
                    'errors' => $validation['errors']
                );
            }

            // Préparer les données
            $url_entry = array(
                'id' => $this->generate_url_id(),
                'loc' => esc_url_raw( $url_data['loc'] ),
                'lastmod' => sanitize_text_field( $url_data['lastmod'] ),
                'changefreq' => sanitize_text_field( $url_data['changefreq'] ),
                'priority' => floatval( $url_data['priority'] ),
                'created_at' => current_time( 'mysql' ),
                'updated_at' => current_time( 'mysql' )
            );

            // Récupérer les URLs existantes
            $existing_urls = $this->get_all_custom_urls();
            
            // Vérifier les doublons
            foreach ( $existing_urls as $existing_url ) {
                if ( $existing_url['loc'] === $url_entry['loc'] ) {
                    return array(
                        'success' => false,
                        'message' => __( 'Cette URL existe déjà dans la liste', 'boss-seo' )
                    );
                }
            }

            // Ajouter la nouvelle URL
            $existing_urls[] = $url_entry;
            
            // Sauvegarder
            $saved = update_option( $this->custom_urls_option, $existing_urls );
            
            if ( $saved ) {
                // Invalider le cache des sitemaps
                $this->invalidate_sitemap_cache();
                
                return array(
                    'success' => true,
                    'message' => __( 'URL ajoutée avec succès', 'boss-seo' ),
                    'url' => $url_entry
                );
            } else {
                return array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la sauvegarde', 'boss-seo' )
                );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur ajout URL personnalisée - ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur interne lors de l\'ajout', 'boss-seo' )
            );
        }
    }

    /**
     * Met à jour une URL personnalisée.
     *
     * @since    1.2.0
     * @param    string    $url_id      L'ID de l'URL.
     * @param    array     $url_data    Les nouvelles données.
     * @return   array                  Résultat de la mise à jour.
     */
    public function update_custom_url( $url_id, $url_data ) {
        try {
            // Valider les données
            $validation = $this->validate_url_data( $url_data );
            if ( ! $validation['valid'] ) {
                return array(
                    'success' => false,
                    'message' => $validation['message'],
                    'errors' => $validation['errors']
                );
            }

            // Récupérer les URLs existantes
            $existing_urls = $this->get_all_custom_urls();
            $url_found = false;

            // Chercher et mettre à jour l'URL
            foreach ( $existing_urls as $index => $existing_url ) {
                if ( $existing_url['id'] === $url_id ) {
                    $existing_urls[$index] = array_merge( $existing_url, array(
                        'loc' => esc_url_raw( $url_data['loc'] ),
                        'lastmod' => sanitize_text_field( $url_data['lastmod'] ),
                        'changefreq' => sanitize_text_field( $url_data['changefreq'] ),
                        'priority' => floatval( $url_data['priority'] ),
                        'updated_at' => current_time( 'mysql' )
                    ) );
                    $url_found = true;
                    break;
                }
            }

            if ( ! $url_found ) {
                return array(
                    'success' => false,
                    'message' => __( 'URL non trouvée', 'boss-seo' )
                );
            }

            // Sauvegarder
            $saved = update_option( $this->custom_urls_option, $existing_urls );
            
            if ( $saved ) {
                // Invalider le cache des sitemaps
                $this->invalidate_sitemap_cache();
                
                return array(
                    'success' => true,
                    'message' => __( 'URL mise à jour avec succès', 'boss-seo' ),
                    'url' => $existing_urls[$index]
                );
            } else {
                return array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la sauvegarde', 'boss-seo' )
                );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur mise à jour URL personnalisée - ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur interne lors de la mise à jour', 'boss-seo' )
            );
        }
    }

    /**
     * Supprime une URL personnalisée.
     *
     * @since    1.2.0
     * @param    string    $url_id    L'ID de l'URL.
     * @return   array                Résultat de la suppression.
     */
    public function delete_custom_url( $url_id ) {
        try {
            // Récupérer les URLs existantes
            $existing_urls = $this->get_all_custom_urls();
            $url_found = false;
            $deleted_url = null;

            // Chercher et supprimer l'URL
            foreach ( $existing_urls as $index => $existing_url ) {
                if ( $existing_url['id'] === $url_id ) {
                    $deleted_url = $existing_url;
                    unset( $existing_urls[$index] );
                    $url_found = true;
                    break;
                }
            }

            if ( ! $url_found ) {
                return array(
                    'success' => false,
                    'message' => __( 'URL non trouvée', 'boss-seo' )
                );
            }

            // Réindexer le tableau
            $existing_urls = array_values( $existing_urls );

            // Sauvegarder
            $saved = update_option( $this->custom_urls_option, $existing_urls );
            
            if ( $saved ) {
                // Invalider le cache des sitemaps
                $this->invalidate_sitemap_cache();
                
                return array(
                    'success' => true,
                    'message' => __( 'URL supprimée avec succès', 'boss-seo' ),
                    'deleted_url' => $deleted_url
                );
            } else {
                return array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la sauvegarde', 'boss-seo' )
                );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur suppression URL personnalisée - ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur interne lors de la suppression', 'boss-seo' )
            );
        }
    }

    /**
     * Valide les données d'une URL.
     *
     * @since    1.2.0
     * @param    array    $url_data    Les données à valider.
     * @return   array                 Résultat de la validation.
     */
    private function validate_url_data( $url_data ) {
        $errors = array();

        // Vérifier l'URL
        if ( empty( $url_data['loc'] ) ) {
            $errors['loc'] = __( 'L\'URL est requise', 'boss-seo' );
        } elseif ( ! filter_var( $url_data['loc'], FILTER_VALIDATE_URL ) ) {
            $errors['loc'] = __( 'L\'URL n\'est pas valide', 'boss-seo' );
        } elseif ( strlen( $url_data['loc'] ) > 2048 ) {
            $errors['loc'] = __( 'L\'URL est trop longue (max 2048 caractères)', 'boss-seo' );
        }

        // Vérifier la priorité
        if ( isset( $url_data['priority'] ) ) {
            $priority = floatval( $url_data['priority'] );
            if ( $priority < 0.0 || $priority > 1.0 ) {
                $errors['priority'] = __( 'La priorité doit être entre 0.0 et 1.0', 'boss-seo' );
            }
        }

        // Vérifier la fréquence
        if ( isset( $url_data['changefreq'] ) ) {
            $valid_frequencies = array( 'always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never' );
            if ( ! in_array( $url_data['changefreq'], $valid_frequencies ) ) {
                $errors['changefreq'] = __( 'Fréquence de changement invalide', 'boss-seo' );
            }
        }

        // Vérifier la date
        if ( isset( $url_data['lastmod'] ) && ! empty( $url_data['lastmod'] ) ) {
            if ( ! strtotime( $url_data['lastmod'] ) ) {
                $errors['lastmod'] = __( 'Format de date invalide', 'boss-seo' );
            }
        }

        return array(
            'valid' => empty( $errors ),
            'errors' => $errors,
            'message' => empty( $errors ) ? '' : __( 'Données invalides', 'boss-seo' )
        );
    }

    /**
     * Génère un ID unique pour une URL.
     *
     * @since    1.2.0
     * @return   string    L'ID généré.
     */
    private function generate_url_id() {
        return 'url_' . uniqid() . '_' . time();
    }

    /**
     * Invalide le cache des sitemaps.
     *
     * @since    1.2.0
     */
    private function invalidate_sitemap_cache() {
        // Supprimer les transients liés aux sitemaps
        delete_transient( 'boss_seo_sitemap_cache' );
        delete_transient( 'boss_seo_dashboard_stats' );
        
        // Marquer les sitemaps pour régénération
        update_option( 'boss_seo_sitemap_needs_regeneration', true );
    }

    /**
     * Exporte les URLs personnalisées en CSV.
     *
     * @since    1.2.0
     * @return   string    Le contenu CSV.
     */
    public function export_to_csv() {
        $urls = $this->get_all_custom_urls();
        
        $csv_content = "URL,Priorité,Fréquence,Dernière modification,Date de création\n";
        
        foreach ( $urls as $url ) {
            $csv_content .= sprintf(
                '"%s","%s","%s","%s","%s"' . "\n",
                $url['loc'],
                $url['priority'],
                $url['changefreq'],
                $url['lastmod'],
                isset( $url['created_at'] ) ? $url['created_at'] : ''
            );
        }
        
        return $csv_content;
    }

    /**
     * Importe des URLs depuis un CSV.
     *
     * @since    1.2.0
     * @param    string    $csv_content    Le contenu CSV.
     * @return   array                     Résultat de l'import.
     */
    public function import_from_csv( $csv_content ) {
        try {
            $lines = explode( "\n", $csv_content );
            $imported = 0;
            $errors = array();
            
            // Ignorer la première ligne (en-têtes)
            array_shift( $lines );
            
            foreach ( $lines as $line_number => $line ) {
                $line = trim( $line );
                if ( empty( $line ) ) {
                    continue;
                }
                
                $data = str_getcsv( $line );
                
                if ( count( $data ) < 3 ) {
                    $errors[] = sprintf( __( 'Ligne %d: Format invalide', 'boss-seo' ), $line_number + 2 );
                    continue;
                }
                
                $url_data = array(
                    'loc' => $data[0],
                    'priority' => isset( $data[1] ) ? $data[1] : '0.5',
                    'changefreq' => isset( $data[2] ) ? $data[2] : 'monthly',
                    'lastmod' => isset( $data[3] ) ? $data[3] : date( 'Y-m-d' )
                );
                
                $result = $this->add_custom_url( $url_data );
                
                if ( $result['success'] ) {
                    $imported++;
                } else {
                    $errors[] = sprintf( __( 'Ligne %d: %s', 'boss-seo' ), $line_number + 2, $result['message'] );
                }
            }
            
            return array(
                'success' => true,
                'imported' => $imported,
                'errors' => $errors,
                'message' => sprintf( __( '%d URLs importées avec succès', 'boss-seo' ), $imported )
            );
            
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur import CSV - ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'import CSV', 'boss-seo' )
            );
        }
    }
}
?>
