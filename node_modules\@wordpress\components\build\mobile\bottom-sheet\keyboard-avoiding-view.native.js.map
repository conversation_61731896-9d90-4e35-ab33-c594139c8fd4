{"version": 3, "names": ["_reactNative", "require", "_element", "KeyboardAvoidingView", "Component", "constructor", "arguments", "_onKeyboardChange", "bind", "_subscriptions", "state", "bottom", "_relativeKeyboardHeight", "keyboardFrame", "windowWidth", "Dimensions", "get", "width", "isFloatingKeyboard", "windowHeight", "height", "keyboardY", "screenY", "props", "keyboardVerticalOffset", "final", "Math", "max", "event", "setState", "duration", "easing", "endCoordinates", "LayoutAnimation", "configureNext", "update", "type", "Types", "componentDidMount", "Platform", "OS", "Keyboard", "addListener", "componentWillUnmount", "for<PERSON>ach", "subscription", "remove", "render", "children", "enabled", "style", "finalStyle", "bottomHeight", "StyleSheet", "compose", "paddingBottom", "_react", "createElement", "View", "defaultProps", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/keyboard-avoiding-view.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tKeyboard,\n\tLayoutAnimation,\n\tPlatform,\n\tStyleSheet,\n\tView,\n\tDimensions,\n} from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { Component } from '@wordpress/element';\n\n/**\n * This is a simplified version of Facebook's KeyboardAvoidingView.\n * It's meant to work specifically with BottomSheets.\n * This fixes an issue in the bottom padding calculation, when the\n * BottomSheet was presented on Landscape, with the keyboard already present,\n * and a TextField on Autofocus (situation present on Links UI)\n */\nclass KeyboardAvoidingView extends Component {\n\tconstructor() {\n\t\tsuper( ...arguments );\n\n\t\tthis._onKeyboardChange = this._onKeyboardChange.bind( this );\n\t\tthis._subscriptions = [];\n\t\tthis.state = {\n\t\t\tbottom: 0,\n\t\t};\n\t}\n\n\t_relativeKeyboardHeight( keyboardFrame ) {\n\t\tif ( ! keyboardFrame ) {\n\t\t\treturn 0;\n\t\t}\n\n\t\tconst windowWidth = Dimensions.get( 'window' ).width;\n\t\tconst isFloatingKeyboard = keyboardFrame.width !== windowWidth;\n\t\tif ( isFloatingKeyboard ) {\n\t\t\treturn 0;\n\t\t}\n\n\t\tconst windowHeight = Dimensions.get( 'window' ).height;\n\t\tconst keyboardY =\n\t\t\tkeyboardFrame.screenY - this.props.keyboardVerticalOffset;\n\n\t\tconst final = Math.max( windowHeight - keyboardY, 0 );\n\t\treturn final;\n\t}\n\n\t/**\n\t * @param {Object} event Keyboard event.\n\t */\n\t_onKeyboardChange( event ) {\n\t\tif ( event === null ) {\n\t\t\tthis.setState( { bottom: 0 } );\n\t\t\treturn;\n\t\t}\n\n\t\tconst { duration, easing, endCoordinates } = event;\n\t\tconst height = this._relativeKeyboardHeight( endCoordinates );\n\n\t\tif ( this.state.bottom === height ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( duration && easing ) {\n\t\t\tLayoutAnimation.configureNext( {\n\t\t\t\tduration,\n\t\t\t\tupdate: {\n\t\t\t\t\tduration,\n\t\t\t\t\ttype: LayoutAnimation.Types[ easing ] || 'keyboard',\n\t\t\t\t},\n\t\t\t} );\n\t\t}\n\t\tthis.setState( { bottom: height } );\n\t}\n\n\tcomponentDidMount() {\n\t\tif ( Platform.OS === 'ios' ) {\n\t\t\tthis._subscriptions = [\n\t\t\t\tKeyboard.addListener(\n\t\t\t\t\t'keyboardWillChangeFrame',\n\t\t\t\t\tthis._onKeyboardChange\n\t\t\t\t),\n\t\t\t];\n\t\t}\n\t}\n\n\tcomponentWillUnmount() {\n\t\tthis._subscriptions.forEach( ( subscription ) => {\n\t\t\tsubscription.remove();\n\t\t} );\n\t}\n\n\trender() {\n\t\tconst { children, enabled, keyboardVerticalOffset, style, ...props } =\n\t\t\tthis.props;\n\n\t\tlet finalStyle = style;\n\t\tif ( Platform.OS === 'ios' ) {\n\t\t\tconst bottomHeight = enabled ? this.state.bottom : 0;\n\t\t\tfinalStyle = StyleSheet.compose( style, {\n\t\t\t\tpaddingBottom: bottomHeight,\n\t\t\t} );\n\t\t}\n\n\t\treturn (\n\t\t\t<View style={ finalStyle } { ...props }>\n\t\t\t\t{ children }\n\t\t\t</View>\n\t\t);\n\t}\n}\n\nKeyboardAvoidingView.defaultProps = {\n\tenabled: true,\n\tkeyboardVerticalOffset: 0,\n};\n\nexport default KeyboardAvoidingView;\n"], "mappings": ";;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAYA,IAAAC,QAAA,GAAAD,OAAA;AAfA;AACA;AACA;;AAUA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,oBAAoB,SAASC,kBAAS,CAAC;EAC5CC,WAAWA,CAAA,EAAG;IACb,KAAK,CAAE,GAAGC,SAAU,CAAC;IAErB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACC,IAAI,CAAE,IAAK,CAAC;IAC5D,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,KAAK,GAAG;MACZC,MAAM,EAAE;IACT,CAAC;EACF;EAEAC,uBAAuBA,CAAEC,aAAa,EAAG;IACxC,IAAK,CAAEA,aAAa,EAAG;MACtB,OAAO,CAAC;IACT;IAEA,MAAMC,WAAW,GAAGC,uBAAU,CAACC,GAAG,CAAE,QAAS,CAAC,CAACC,KAAK;IACpD,MAAMC,kBAAkB,GAAGL,aAAa,CAACI,KAAK,KAAKH,WAAW;IAC9D,IAAKI,kBAAkB,EAAG;MACzB,OAAO,CAAC;IACT;IAEA,MAAMC,YAAY,GAAGJ,uBAAU,CAACC,GAAG,CAAE,QAAS,CAAC,CAACI,MAAM;IACtD,MAAMC,SAAS,GACdR,aAAa,CAACS,OAAO,GAAG,IAAI,CAACC,KAAK,CAACC,sBAAsB;IAE1D,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAER,YAAY,GAAGE,SAAS,EAAE,CAAE,CAAC;IACrD,OAAOI,KAAK;EACb;;EAEA;AACD;AACA;EACClB,iBAAiBA,CAAEqB,KAAK,EAAG;IAC1B,IAAKA,KAAK,KAAK,IAAI,EAAG;MACrB,IAAI,CAACC,QAAQ,CAAE;QAAElB,MAAM,EAAE;MAAE,CAAE,CAAC;MAC9B;IACD;IAEA,MAAM;MAAEmB,QAAQ;MAAEC,MAAM;MAAEC;IAAe,CAAC,GAAGJ,KAAK;IAClD,MAAMR,MAAM,GAAG,IAAI,CAACR,uBAAuB,CAAEoB,cAAe,CAAC;IAE7D,IAAK,IAAI,CAACtB,KAAK,CAACC,MAAM,KAAKS,MAAM,EAAG;MACnC;IACD;IAEA,IAAKU,QAAQ,IAAIC,MAAM,EAAG;MACzBE,4BAAe,CAACC,aAAa,CAAE;QAC9BJ,QAAQ;QACRK,MAAM,EAAE;UACPL,QAAQ;UACRM,IAAI,EAAEH,4BAAe,CAACI,KAAK,CAAEN,MAAM,CAAE,IAAI;QAC1C;MACD,CAAE,CAAC;IACJ;IACA,IAAI,CAACF,QAAQ,CAAE;MAAElB,MAAM,EAAES;IAAO,CAAE,CAAC;EACpC;EAEAkB,iBAAiBA,CAAA,EAAG;IACnB,IAAKC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAG;MAC5B,IAAI,CAAC/B,cAAc,GAAG,CACrBgC,qBAAQ,CAACC,WAAW,CACnB,yBAAyB,EACzB,IAAI,CAACnC,iBACN,CAAC,CACD;IACF;EACD;EAEAoC,oBAAoBA,CAAA,EAAG;IACtB,IAAI,CAAClC,cAAc,CAACmC,OAAO,CAAIC,YAAY,IAAM;MAChDA,YAAY,CAACC,MAAM,CAAC,CAAC;IACtB,CAAE,CAAC;EACJ;EAEAC,MAAMA,CAAA,EAAG;IACR,MAAM;MAAEC,QAAQ;MAAEC,OAAO;MAAEzB,sBAAsB;MAAE0B,KAAK;MAAE,GAAG3B;IAAM,CAAC,GACnE,IAAI,CAACA,KAAK;IAEX,IAAI4B,UAAU,GAAGD,KAAK;IACtB,IAAKX,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAG;MAC5B,MAAMY,YAAY,GAAGH,OAAO,GAAG,IAAI,CAACvC,KAAK,CAACC,MAAM,GAAG,CAAC;MACpDwC,UAAU,GAAGE,uBAAU,CAACC,OAAO,CAAEJ,KAAK,EAAE;QACvCK,aAAa,EAAEH;MAChB,CAAE,CAAC;IACJ;IAEA,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACzD,YAAA,CAAA0D,IAAI;MAACR,KAAK,EAAGC,UAAY;MAAA,GAAM5B;IAAK,GAClCyB,QACG,CAAC;EAET;AACD;AAEA7C,oBAAoB,CAACwD,YAAY,GAAG;EACnCV,OAAO,EAAE,IAAI;EACbzB,sBAAsB,EAAE;AACzB,CAAC;AAAC,IAAAoC,QAAA,GAEazD,oBAAoB;AAAA0D,OAAA,CAAAC,OAAA,GAAAF,QAAA"}