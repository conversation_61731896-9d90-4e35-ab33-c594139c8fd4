"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useBorderControlDropdown = useBorderControlDropdown;
var _element = require("@wordpress/element");
var styles = _interopRequireWildcard(require("../styles"));
var _utils = require("../../unit-control/utils");
var _context = require("../../context");
var _useCx = require("../../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function useBorderControlDropdown(props) {
  const {
    border,
    className,
    colors = [],
    enableAlpha = false,
    enableStyle = true,
    onChange,
    previousStyleSelection,
    size = 'default',
    __experimentalIsRenderedInSidebar = false,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'BorderControlDropdown');
  const [widthValue] = (0, _utils.parseQuantityAndUnitFromRawValue)(border?.width);
  const hasZeroWidth = widthValue === 0;
  const onColorChange = color => {
    const style = border?.style === 'none' ? previousStyleSelection : border?.style;
    const width = hasZeroWidth && !!color ? '1px' : border?.width;
    onChange({
      color,
      style,
      width
    });
  };
  const onStyleChange = style => {
    const width = hasZeroWidth && !!style ? '1px' : border?.width;
    onChange({
      ...border,
      style,
      width
    });
  };
  const onReset = () => {
    onChange({
      ...border,
      color: undefined,
      style: undefined
    });
  };

  // Generate class names.
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => {
    return cx(styles.borderControlDropdown, className);
  }, [className, cx]);
  const indicatorClassName = (0, _element.useMemo)(() => {
    return cx(styles.borderColorIndicator);
  }, [cx]);
  const indicatorWrapperClassName = (0, _element.useMemo)(() => {
    return cx(styles.colorIndicatorWrapper(border, size));
  }, [border, cx, size]);
  const popoverControlsClassName = (0, _element.useMemo)(() => {
    return cx(styles.borderControlPopoverControls);
  }, [cx]);
  const popoverContentClassName = (0, _element.useMemo)(() => {
    return cx(styles.borderControlPopoverContent);
  }, [cx]);
  const resetButtonClassName = (0, _element.useMemo)(() => {
    return cx(styles.resetButton);
  }, [cx]);
  return {
    ...otherProps,
    border,
    className: classes,
    colors,
    enableAlpha,
    enableStyle,
    indicatorClassName,
    indicatorWrapperClassName,
    onColorChange,
    onStyleChange,
    onReset,
    popoverContentClassName,
    popoverControlsClassName,
    resetButtonClassName,
    size,
    __experimentalIsRenderedInSidebar
  };
}
//# sourceMappingURL=hook.js.map