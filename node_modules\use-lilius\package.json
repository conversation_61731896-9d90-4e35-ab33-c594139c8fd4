{"name": "use-lilius", "version": "2.0.5", "description": "A headless calendar hook for React.", "keywords": ["react", "calendar", "datepicker", "date-fns"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/its-danny/use-lilius.git"}, "main": "build/index.js", "module": "build/index.es.js", "types": "build/use-lilius.d.ts", "files": ["build"], "packageManager": "yarn@3.2.0", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "scripts": {"lint": "eslint \"{src,examples}/**/*\" --ext .js,.jsx,.ts,.tsx --ignore-pattern \"examples/**/*.mdx\"", "fix": "eslint \"{src,examples}/**/*\" --ext .js,.jsx,.ts,.tsx --ignore-pattern \"examples/**/*.mdx\" --fix", "test": "jest", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "build-docs": "typedoc --plugin typedoc-plugin-markdown --readme none --out docs/ src/use-lilius.ts", "build": "rollup -c", "release": "standard-version", "prepare": "husky install", "prepack": "pinst --disable", "postpack": "pinst --enable"}, "devDependencies": {"@babel/core": "^7.13.8", "@chakra-ui/react": "^1.3.4", "@chakra-ui/storybook-addon": "^3.0.2", "@emotion/react": "^11.1.5", "@emotion/styled": "^11.1.5", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@storybook/addon-essentials": "^6.5.5", "@storybook/react": "^6.5.5", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react-hooks": "^7.0.0", "@types/jest": "^27.0.1", "@types/react": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.0.0", "babel-loader": "^8.2.2", "cz-conventional-changelog": "3.3.0", "eslint": "^8.3.0", "eslint-config-airbnb-typescript": "^12.3.1", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^26.0.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-mdx": "^1.9.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-storybook": "^0.5.12", "framer-motion": "^4.0.3", "husky": "^7.0.1", "jest": "^26.6.3", "pinst": "^3.0.0", "prettier": "^2.4.1", "react": "^17.0.0", "react-dom": "^17.0.0", "react-icons": "^4.2.0", "rollup": "^2.40.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-typescript2": "^0.31.0", "standard-version": "^9.1.1", "ts-jest": "^26.5.3", "tslib": "^2.1.0", "typedoc": "^0.22.1", "typedoc-plugin-markdown": "^3.6.0", "typescript": "^4.2.3"}, "peerDependencies": {"react": "*", "react-dom": "*"}, "dependencies": {"date-fns": "^3.6.0"}}