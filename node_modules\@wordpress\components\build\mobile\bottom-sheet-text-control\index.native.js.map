{"version": 3, "names": ["_reactNative", "require", "_native", "_element", "_icons", "_compose", "_components", "_styles", "_interopRequireDefault", "BottomSheetTextControl", "initialValue", "onChange", "placeholder", "label", "icon", "footerNote", "cellPlaceholder", "disabled", "showSubSheet", "setShowSubSheet", "useState", "navigation", "useNavigation", "goBack", "openSubSheet", "navigate", "BottomSheet", "SubSheet", "screenName", "horizontalBorderStyle", "usePreferredColorSchemeStyle", "styles", "horizontalBorder", "horizontalBorderDark", "textEditorStyle", "textEditor", "textEditorDark", "_react", "createElement", "navigationButton", "Cell", "onPress", "value", "Icon", "chevronRight", "showSheet", "Fragment", "NavBar", "BackButton", "Heading", "PanelBody", "style", "TextInput", "onChangeText", "text", "defaultValue", "multiline", "placeholderTextColor", "textAlignVertical", "textFooternote", "FooterMessageControl", "textAlign", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet-text-control/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { TextInput } from 'react-native';\nimport { useNavigation } from '@react-navigation/native';\n\n/**\n * WordPress dependencies\n */\nimport { useState } from '@wordpress/element';\nimport { Icon, chevronRight } from '@wordpress/icons';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\nimport {\n\tBottomSheet,\n\tPanelBody,\n\tFooterMessageControl,\n} from '@wordpress/components';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\n\nconst BottomSheetTextControl = ( {\n\tinitialValue,\n\tonChange,\n\tplaceholder,\n\tlabel,\n\ticon,\n\tfooterNote,\n\tcellPlaceholder,\n\tdisabled,\n} ) => {\n\tconst [ showSubSheet, setShowSubSheet ] = useState( false );\n\tconst navigation = useNavigation();\n\n\tconst goBack = () => {\n\t\tsetShowSubSheet( false );\n\t\tnavigation.goBack();\n\t};\n\n\tconst openSubSheet = () => {\n\t\tnavigation.navigate( BottomSheet.SubSheet.screenName );\n\t\tsetShowSubSheet( true );\n\t};\n\n\tconst horizontalBorderStyle = usePreferredColorSchemeStyle(\n\t\tstyles.horizontalBorder,\n\t\tstyles.horizontalBorderDark\n\t);\n\n\tconst textEditorStyle = usePreferredColorSchemeStyle(\n\t\tstyles.textEditor,\n\t\tstyles.textEditorDark\n\t);\n\n\treturn (\n\t\t<BottomSheet.SubSheet\n\t\t\tnavigationButton={\n\t\t\t\t<BottomSheet.Cell\n\t\t\t\t\ticon={ icon }\n\t\t\t\t\tlabel={ label }\n\t\t\t\t\tonPress={ openSubSheet }\n\t\t\t\t\tvalue={ initialValue || '' }\n\t\t\t\t\tplaceholder={ cellPlaceholder || placeholder || '' }\n\t\t\t\t\tdisabled={ disabled }\n\t\t\t\t>\n\t\t\t\t\t{ disabled ? null : <Icon icon={ chevronRight } /> }\n\t\t\t\t</BottomSheet.Cell>\n\t\t\t}\n\t\t\tshowSheet={ showSubSheet }\n\t\t>\n\t\t\t<>\n\t\t\t\t<BottomSheet.NavBar>\n\t\t\t\t\t<BottomSheet.NavBar.BackButton onPress={ goBack } />\n\t\t\t\t\t<BottomSheet.NavBar.Heading>\n\t\t\t\t\t\t{ label }\n\t\t\t\t\t</BottomSheet.NavBar.Heading>\n\t\t\t\t</BottomSheet.NavBar>\n\t\t\t\t<PanelBody style={ horizontalBorderStyle }>\n\t\t\t\t\t<TextInput\n\t\t\t\t\t\tlabel={ label }\n\t\t\t\t\t\tonChangeText={ ( text ) => onChange( text ) }\n\t\t\t\t\t\tdefaultValue={ initialValue }\n\t\t\t\t\t\tmultiline={ true }\n\t\t\t\t\t\tplaceholder={ placeholder }\n\t\t\t\t\t\tplaceholderTextColor={ '#87a6bc' }\n\t\t\t\t\t\tstyle={ textEditorStyle }\n\t\t\t\t\t\ttextAlignVertical={ 'top' }\n\t\t\t\t\t/>\n\t\t\t\t</PanelBody>\n\t\t\t</>\n\n\t\t\t{ footerNote && (\n\t\t\t\t<PanelBody style={ styles.textFooternote }>\n\t\t\t\t\t<FooterMessageControl\n\t\t\t\t\t\tlabel={ footerNote }\n\t\t\t\t\t\ttextAlign=\"left\"\n\t\t\t\t\t/>\n\t\t\t\t</PanelBody>\n\t\t\t) }\n\t\t</BottomSheet.SubSheet>\n\t);\n};\n\nexport default BottomSheetTextControl;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AASA,IAAAM,OAAA,GAAAC,sBAAA,CAAAP,OAAA;AArBA;AACA;AACA;;AAIA;AACA;AACA;;AAUA;AACA;AACA;;AAGA,MAAMQ,sBAAsB,GAAGA,CAAE;EAChCC,YAAY;EACZC,QAAQ;EACRC,WAAW;EACXC,KAAK;EACLC,IAAI;EACJC,UAAU;EACVC,eAAe;EACfC;AACD,CAAC,KAAM;EACN,MAAM,CAAEC,YAAY,EAAEC,eAAe,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAC3D,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAElC,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACpBJ,eAAe,CAAE,KAAM,CAAC;IACxBE,UAAU,CAACE,MAAM,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IAC1BH,UAAU,CAACI,QAAQ,CAAEC,uBAAW,CAACC,QAAQ,CAACC,UAAW,CAAC;IACtDT,eAAe,CAAE,IAAK,CAAC;EACxB,CAAC;EAED,MAAMU,qBAAqB,GAAG,IAAAC,qCAA4B,EACzDC,eAAM,CAACC,gBAAgB,EACvBD,eAAM,CAACE,oBACR,CAAC;EAED,MAAMC,eAAe,GAAG,IAAAJ,qCAA4B,EACnDC,eAAM,CAACI,UAAU,EACjBJ,eAAM,CAACK,cACR,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAAoB,WAAW,CAACC,QAAQ;IACpBY,gBAAgB,EACf,IAAAF,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAAoB,WAAW,CAACc,IAAI;MAChB1B,IAAI,EAAGA,IAAM;MACbD,KAAK,EAAGA,KAAO;MACf4B,OAAO,EAAGjB,YAAc;MACxBkB,KAAK,EAAGhC,YAAY,IAAI,EAAI;MAC5BE,WAAW,EAAGI,eAAe,IAAIJ,WAAW,IAAI,EAAI;MACpDK,QAAQ,EAAGA;IAAU,GAEnBA,QAAQ,GAAG,IAAI,GAAG,IAAAoB,MAAA,CAAAC,aAAA,EAAClC,MAAA,CAAAuC,IAAI;MAAC7B,IAAI,EAAG8B;IAAc,CAAE,CAChC,CAClB;IACDC,SAAS,EAAG3B;EAAc,GAE1B,IAAAmB,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAS,QAAA,QACC,IAAAT,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAAoB,WAAW,CAACqB,MAAM,QAClB,IAAAV,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAAoB,WAAW,CAACqB,MAAM,CAACC,UAAU;IAACP,OAAO,EAAGlB;EAAQ,CAAE,CAAC,EACpD,IAAAc,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAAoB,WAAW,CAACqB,MAAM,CAACE,OAAO,QACxBpC,KACyB,CACT,CAAC,EACrB,IAAAwB,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAA4C,SAAS;IAACC,KAAK,EAAGtB;EAAuB,GACzC,IAAAQ,MAAA,CAAAC,aAAA,EAACtC,YAAA,CAAAoD,SAAS;IACTvC,KAAK,EAAGA,KAAO;IACfwC,YAAY,EAAKC,IAAI,IAAM3C,QAAQ,CAAE2C,IAAK,CAAG;IAC7CC,YAAY,EAAG7C,YAAc;IAC7B8C,SAAS,EAAG,IAAM;IAClB5C,WAAW,EAAGA,WAAa;IAC3B6C,oBAAoB,EAAG,SAAW;IAClCN,KAAK,EAAGjB,eAAiB;IACzBwB,iBAAiB,EAAG;EAAO,CAC3B,CACS,CACV,CAAC,EAED3C,UAAU,IACX,IAAAsB,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAA4C,SAAS;IAACC,KAAK,EAAGpB,eAAM,CAAC4B;EAAgB,GACzC,IAAAtB,MAAA,CAAAC,aAAA,EAAChC,WAAA,CAAAsD,oBAAoB;IACpB/C,KAAK,EAAGE,UAAY;IACpB8C,SAAS,EAAC;EAAM,CAChB,CACS,CAES,CAAC;AAEzB,CAAC;AAAC,IAAAC,QAAA,GAEarD,sBAAsB;AAAAsD,OAAA,CAAAC,OAAA,GAAAF,QAAA"}