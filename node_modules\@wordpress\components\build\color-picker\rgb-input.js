"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RgbInput = void 0;
var _react = require("react");
var _colord = require("colord");
var _inputWithSlider = require("./input-with-slider");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const RgbInput = ({
  color,
  onChange,
  enableAlpha
}) => {
  const {
    r,
    g,
    b,
    a
  } = color.toRgb();
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 255,
    label: "Red",
    abbreviation: "R",
    value: r,
    onChange: nextR => onChange((0, _colord.colord)({
      r: nextR,
      g,
      b,
      a
    }))
  }), (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 255,
    label: "Green",
    abbreviation: "G",
    value: g,
    onChange: nextG => onChange((0, _colord.colord)({
      r,
      g: nextG,
      b,
      a
    }))
  }), (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 255,
    label: "Blue",
    abbreviation: "B",
    value: b,
    onChange: nextB => onChange((0, _colord.colord)({
      r,
      g,
      b: nextB,
      a
    }))
  }), enableAlpha && (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 100,
    label: "Alpha",
    abbreviation: "A",
    value: Math.trunc(a * 100),
    onChange: nextA => onChange((0, _colord.colord)({
      r,
      g,
      b,
      a: nextA / 100
    }))
  }));
};
exports.RgbInput = RgbInput;
//# sourceMappingURL=rgb-input.js.map