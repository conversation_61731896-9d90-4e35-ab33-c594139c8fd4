"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _native = require("@react-navigation/native");
var _reactNative = require("react-native");
var _components = require("@wordpress/components");
var _element = require("@wordpress/element");
var _bottomSheetNavigationContext = require("./bottom-sheet-navigation-context");
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BottomSheetNavigationScreen = ({
  children,
  fullScreen,
  isScrollable,
  isNested,
  name
}) => {
  const navigation = (0, _native.useNavigation)();
  const maxHeight = (0, _element.useRef)(0);
  const isFocused = (0, _native.useIsFocused)();
  const {
    onHandleHardwareButtonPress,
    shouldEnableBottomSheetMaxHeight,
    setIsFullScreen,
    listProps,
    safeAreaBottomInset
  } = (0, _element.useContext)(_components.BottomSheetContext);
  const {
    height: windowHeight
  } = (0, _reactNative.useWindowDimensions)();
  const {
    setHeight
  } = (0, _element.useContext)(_bottomSheetNavigationContext.BottomSheetNavigationContext);
  (0, _native.useFocusEffect)((0, _element.useCallback)(() => {
    onHandleHardwareButtonPress(() => {
      if (navigation.canGoBack()) {
        shouldEnableBottomSheetMaxHeight(true);
        navigation.goBack();
        return true;
      }
      onHandleHardwareButtonPress(null);
      return false;
    });
    /**
     * TODO: onHandleHardwareButtonPress stores a single value, which means
     * future invocations from sibling screens can replace the callback for
     * the currently active screen. Currently, the empty dependency array
     * passed to useCallback here is what prevents erroneous callback
     * replacements, but leveraging memoization to achieve this is brittle and
     * explicitly discouraged in the React documentation.
     * https://reactjs.org/docs/hooks-reference.html#usememo
     *
     * Ideally, we refactor onHandleHardwareButtonPress to manage multiple
     * callbacks triggered based upon which screen is currently active.
     *
     * Related: https://github.com/WordPress/gutenberg/pull/36328#discussion_r768897546
     */
    // see https://github.com/WordPress/gutenberg/pull/41166
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []));
  (0, _native.useFocusEffect)((0, _element.useCallback)(() => {
    if (fullScreen) {
      setHeight(windowHeight);
      setIsFullScreen(true);
    } else if (maxHeight.current !== 0) {
      setIsFullScreen(false);
      setHeight(maxHeight.current);
    }
    return () => {};
  }, [fullScreen, setHeight, setIsFullScreen, windowHeight]));
  const onLayout = ({
    nativeEvent
  }) => {
    if (fullScreen) {
      return;
    }
    const {
      height
    } = nativeEvent.layout;
    if (maxHeight.current !== height && isFocused) {
      maxHeight.current = height;
      setHeight(height);
    }
  };
  return (0, _element.useMemo)(() => {
    return isScrollable || isNested ? (0, _react.createElement)(_reactNative.View, {
      onLayout: onLayout,
      testID: `navigation-screen-${name}`
    }, children) : (0, _react.createElement)(_reactNative.ScrollView, {
      ...listProps
    }, (0, _react.createElement)(_reactNative.TouchableHighlight, {
      accessible: false
    }, (0, _react.createElement)(_reactNative.View, {
      onLayout: onLayout,
      testID: `navigation-screen-${name}`
    }, children, !isNested && (0, _react.createElement)(_reactNative.View, {
      style: {
        height: safeAreaBottomInset || _styles.default.scrollableContent.paddingBottom
      }
    }))));
    // Disable reason: deferring this refactor to the native team.
    // see https://github.com/WordPress/gutenberg/pull/41166
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [children, isFocused, safeAreaBottomInset, listProps, name, isScrollable, isNested, onLayout]);
};
var _default = BottomSheetNavigationScreen;
exports.default = _default;
//# sourceMappingURL=navigation-screen.native.js.map