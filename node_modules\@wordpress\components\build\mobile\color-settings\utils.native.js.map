{"version": 3, "names": ["_i18n", "require", "gradients", "linear", "radial", "gradientOptions", "label", "__", "value", "getGradientType", "color", "includes", "colorsUtils", "screens", "gradientPicker", "picker", "palette", "segments", "isGradient", "exports"], "sources": ["@wordpress/components/src/mobile/color-settings/utils.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\nconst gradients = {\n\tlinear: 'linear-gradient',\n\tradial: 'radial-gradient',\n};\n\nconst gradientOptions = [\n\t{ label: __( 'Linear' ), value: gradients.linear },\n\t{ label: __( 'Radial' ), value: gradients.radial },\n];\n\nconst getGradientType = ( color ) => {\n\tif ( color?.includes( gradients.radial ) ) {\n\t\treturn gradients.radial;\n\t} else if ( color?.includes( gradients.linear ) ) {\n\t\treturn gradients.linear;\n\t}\n\treturn false;\n};\n\nexport const colorsUtils = {\n\tscreens: {\n\t\tgradientPicker: 'GradientPicker',\n\t\tpicker: 'Picker',\n\t\tpalette: 'Palette',\n\t},\n\tsegments: [ __( 'Solid' ), __( 'Gradient' ) ],\n\tgradients,\n\tgradientOptions,\n\tgetGradientType,\n\tisGradient: ( color ) => !! getGradientType( color ),\n};\n"], "mappings": ";;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA,MAAMC,SAAS,GAAG;EACjBC,MAAM,EAAE,iBAAiB;EACzBC,MAAM,EAAE;AACT,CAAC;AAED,MAAMC,eAAe,GAAG,CACvB;EAAEC,KAAK,EAAE,IAAAC,QAAE,EAAE,QAAS,CAAC;EAAEC,KAAK,EAAEN,SAAS,CAACC;AAAO,CAAC,EAClD;EAAEG,KAAK,EAAE,IAAAC,QAAE,EAAE,QAAS,CAAC;EAAEC,KAAK,EAAEN,SAAS,CAACE;AAAO,CAAC,CAClD;AAED,MAAMK,eAAe,GAAKC,KAAK,IAAM;EACpC,IAAKA,KAAK,EAAEC,QAAQ,CAAET,SAAS,CAACE,MAAO,CAAC,EAAG;IAC1C,OAAOF,SAAS,CAACE,MAAM;EACxB,CAAC,MAAM,IAAKM,KAAK,EAAEC,QAAQ,CAAET,SAAS,CAACC,MAAO,CAAC,EAAG;IACjD,OAAOD,SAAS,CAACC,MAAM;EACxB;EACA,OAAO,KAAK;AACb,CAAC;AAEM,MAAMS,WAAW,GAAG;EAC1BC,OAAO,EAAE;IACRC,cAAc,EAAE,gBAAgB;IAChCC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE;EACV,CAAC;EACDC,QAAQ,EAAE,CAAE,IAAAV,QAAE,EAAE,OAAQ,CAAC,EAAE,IAAAA,QAAE,EAAE,UAAW,CAAC,CAAE;EAC7CL,SAAS;EACTG,eAAe;EACfI,eAAe;EACfS,UAAU,EAAIR,KAAK,IAAM,CAAC,CAAED,eAAe,CAAEC,KAAM;AACpD,CAAC;AAACS,OAAA,CAAAP,WAAA,GAAAA,WAAA"}