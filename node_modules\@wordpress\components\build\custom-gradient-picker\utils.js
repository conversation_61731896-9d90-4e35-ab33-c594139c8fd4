"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getGradientAstWithControlPoints = getGradientAstWithControlPoints;
exports.getGradientAstWithDefault = getGradientAstWithDefault;
exports.getLinearGradientRepresentation = getLinearGradientRepresentation;
exports.getStopCssColor = getStopCssColor;
var _gradientParser = _interopRequireDefault(require("gradient-parser"));
var _colord = require("colord");
var _names = _interopRequireDefault(require("colord/plugins/names"));
var _constants = require("./constants");
var _serializer = require("./serializer");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

(0, _colord.extend)([_names.default]);
function getLinearGradientRepresentation(gradientAST) {
  return (0, _serializer.serializeGradient)({
    type: 'linear-gradient',
    orientation: _constants.HORIZONTAL_GRADIENT_ORIENTATION,
    colorStops: gradientAST.colorStops
  });
}
function hasUnsupportedLength(item) {
  return item.length === undefined || item.length.type !== '%';
}
function getGradientAstWithDefault(value) {
  // gradientAST will contain the gradient AST as parsed by gradient-parser npm module.
  // More information of its structure available at https://www.npmjs.com/package/gradient-parser#ast.
  let gradientAST;
  let hasGradient = !!value;
  const valueToParse = value !== null && value !== void 0 ? value : _constants.DEFAULT_GRADIENT;
  try {
    gradientAST = _gradientParser.default.parse(valueToParse)[0];
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('wp.components.CustomGradientPicker failed to parse the gradient with error', error);
    gradientAST = _gradientParser.default.parse(_constants.DEFAULT_GRADIENT)[0];
    hasGradient = false;
  }
  if (!Array.isArray(gradientAST.orientation) && gradientAST.orientation?.type === 'directional') {
    gradientAST.orientation = {
      type: 'angular',
      value: _constants.DIRECTIONAL_ORIENTATION_ANGLE_MAP[gradientAST.orientation.value].toString()
    };
  }
  if (gradientAST.colorStops.some(hasUnsupportedLength)) {
    const {
      colorStops
    } = gradientAST;
    const step = 100 / (colorStops.length - 1);
    colorStops.forEach((stop, index) => {
      stop.length = {
        value: `${step * index}`,
        type: '%'
      };
    });
  }
  return {
    gradientAST,
    hasGradient
  };
}
function getGradientAstWithControlPoints(gradientAST, newControlPoints) {
  return {
    ...gradientAST,
    colorStops: newControlPoints.map(({
      position,
      color
    }) => {
      const {
        r,
        g,
        b,
        a
      } = (0, _colord.colord)(color).toRgb();
      return {
        length: {
          type: '%',
          value: position?.toString()
        },
        type: a < 1 ? 'rgba' : 'rgb',
        value: a < 1 ? [`${r}`, `${g}`, `${b}`, `${a}`] : [`${r}`, `${g}`, `${b}`]
      };
    })
  };
}
function getStopCssColor(colorStop) {
  switch (colorStop.type) {
    case 'hex':
      return `#${colorStop.value}`;
    case 'literal':
      return colorStop.value;
    case 'rgb':
    case 'rgba':
      return `${colorStop.type}(${colorStop.value.join(',')})`;
    default:
      // Should be unreachable if passing an AST from gradient-parser.
      // See https://github.com/rafaelcaricio/gradient-parser#ast.
      return 'transparent';
  }
}
//# sourceMappingURL=utils.js.map