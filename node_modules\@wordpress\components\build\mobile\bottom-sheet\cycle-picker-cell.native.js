"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BottomSheetCyclePickerCell;
var _react = require("react");
var _cell = _interopRequireDefault(require("./cell"));
/**
 * Internal dependencies
 */

function BottomSheetCyclePickerCell(props) {
  const {
    value,
    options,
    onChangeValue,
    ...cellProps
  } = props;
  const nextOptionValue = () => {
    return options[(selectedOptionIndex + 1) % options.length].value;
  };
  const selectedOptionIndex = options.findIndex(option => option.value === value);
  const optionsContainsValue = options.length > 0 && selectedOptionIndex !== -1;
  return (0, _react.createElement)(_cell.default, {
    onPress: () => optionsContainsValue && onChangeValue(nextOptionValue()),
    editable: false,
    value: optionsContainsValue && options[selectedOptionIndex].name,
    ...cellProps
  });
}
//# sourceMappingURL=cycle-picker-cell.native.js.map