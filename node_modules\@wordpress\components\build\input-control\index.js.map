{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_compose", "_element", "_inputBase", "_inputField", "_space", "_utils", "_baseControl", "_useDeprecatedProps", "noop", "useUniqueId", "idProp", "instanceId", "useInstanceId", "InputControl", "id", "UnforwardedInputControl", "props", "ref", "__next40pxDefaultSize", "__unstableStateReducer", "stateReducer", "state", "__unstableInputWidth", "className", "disabled", "help", "hideLabelFromVision", "isPressEnterToChange", "label", "labelPosition", "onChange", "onValidate", "onKeyDown", "prefix", "size", "style", "suffix", "value", "restProps", "useDeprecated36pxDefaultSizeProp", "isFocused", "setIsFocused", "useState", "classes", "classNames", "draftHookProps", "useDraft", "onBlur", "helpPropName", "helpProp", "_react", "createElement", "default", "__nextHasNoMarginBottom", "gap", "justify", "paddingInlineStart", "space", "undefined", "paddingInlineEnd", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/input-control/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classNames from 'classnames';\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { useState, forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport InputBase from './input-base';\nimport InputField from './input-field';\nimport type { InputControlProps } from './types';\nimport { space } from '../utils/space';\nimport { useDraft } from './utils';\nimport BaseControl from '../base-control';\nimport { useDeprecated36pxDefaultSizeProp } from '../utils/use-deprecated-props';\n\nconst noop = () => {};\n\nfunction useUniqueId( idProp?: string ) {\n\tconst instanceId = useInstanceId( InputControl );\n\tconst id = `inspector-input-control-${ instanceId }`;\n\n\treturn idProp || id;\n}\n\nexport function UnforwardedInputControl(\n\tprops: InputControlProps,\n\tref: ForwardedRef< HTMLInputElement >\n) {\n\tconst {\n\t\t__next40pxDefaultSize,\n\t\t__unstableStateReducer: stateReducer = ( state ) => state,\n\t\t__unstableInputWidth,\n\t\tclassName,\n\t\tdisabled = false,\n\t\thelp,\n\t\thideLabelFromVision = false,\n\t\tid: idProp,\n\t\tisPressEnterToChange = false,\n\t\tlabel,\n\t\tlabelPosition = 'top',\n\t\tonChange = noop,\n\t\tonValidate = noop,\n\t\tonKeyDown = noop,\n\t\tprefix,\n\t\tsize = 'default',\n\t\tstyle,\n\t\tsuffix,\n\t\tvalue,\n\t\t...restProps\n\t} = useDeprecated36pxDefaultSizeProp< InputControlProps >(\n\t\tprops,\n\t\t'wp.components.InputControl',\n\t\t'6.4'\n\t);\n\n\tconst [ isFocused, setIsFocused ] = useState( false );\n\n\tconst id = useUniqueId( idProp );\n\tconst classes = classNames( 'components-input-control', className );\n\n\tconst draftHookProps = useDraft( {\n\t\tvalue,\n\t\tonBlur: restProps.onBlur,\n\t\tonChange,\n\t} );\n\n\t// ARIA descriptions can only contain plain text, so fall back to aria-details if not.\n\tconst helpPropName =\n\t\ttypeof help === 'string' ? 'aria-describedby' : 'aria-details';\n\tconst helpProp = !! help ? { [ helpPropName ]: `${ id }__help` } : {};\n\n\treturn (\n\t\t<BaseControl\n\t\t\tclassName={ classes }\n\t\t\thelp={ help }\n\t\t\tid={ id }\n\t\t\t__nextHasNoMarginBottom\n\t\t>\n\t\t\t<InputBase\n\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t__unstableInputWidth={ __unstableInputWidth }\n\t\t\t\tdisabled={ disabled }\n\t\t\t\tgap={ 3 }\n\t\t\t\thideLabelFromVision={ hideLabelFromVision }\n\t\t\t\tid={ id }\n\t\t\t\tisFocused={ isFocused }\n\t\t\t\tjustify=\"left\"\n\t\t\t\tlabel={ label }\n\t\t\t\tlabelPosition={ labelPosition }\n\t\t\t\tprefix={ prefix }\n\t\t\t\tsize={ size }\n\t\t\t\tstyle={ style }\n\t\t\t\tsuffix={ suffix }\n\t\t\t>\n\t\t\t\t<InputField\n\t\t\t\t\t{ ...restProps }\n\t\t\t\t\t{ ...helpProp }\n\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\tclassName=\"components-input-control__input\"\n\t\t\t\t\tdisabled={ disabled }\n\t\t\t\t\tid={ id }\n\t\t\t\t\tisFocused={ isFocused }\n\t\t\t\t\tisPressEnterToChange={ isPressEnterToChange }\n\t\t\t\t\tonKeyDown={ onKeyDown }\n\t\t\t\t\tonValidate={ onValidate }\n\t\t\t\t\tpaddingInlineStart={ prefix ? space( 2 ) : undefined }\n\t\t\t\t\tpaddingInlineEnd={ suffix ? space( 2 ) : undefined }\n\t\t\t\t\tref={ ref }\n\t\t\t\t\tsetIsFocused={ setIsFocused }\n\t\t\t\t\tsize={ size }\n\t\t\t\t\tstateReducer={ stateReducer }\n\t\t\t\t\t{ ...draftHookProps }\n\t\t\t\t/>\n\t\t\t</InputBase>\n\t\t</BaseControl>\n\t);\n}\n\n/**\n * InputControl components let users enter and edit text. This is an experimental component\n * intended to (in time) merge with or replace `TextControl`.\n *\n * ```jsx\n * import { __experimentalInputControl as InputControl } from '@wordpress/components';\n * import { useState } from '@wordpress/compose';\n *\n * const Example = () => {\n *   const [ value, setValue ] = useState( '' );\n *\n *   return (\n *  \t<InputControl\n *  \t\tvalue={ value }\n *  \t\tonChange={ ( nextValue ) => setValue( nextValue ?? '' ) }\n *  \t/>\n *   );\n * };\n * ```\n */\nexport const InputControl = forwardRef( UnforwardedInputControl );\n\nexport default InputControl;\n"], "mappings": ";;;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;AAEA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,mBAAA,GAAAR,OAAA;AArBA;AACA;AACA;;AAIA;AACA;AACA;;AAIA;AACA;AACA;;AASA,MAAMS,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAErB,SAASC,WAAWA,CAAEC,MAAe,EAAG;EACvC,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEC,YAAa,CAAC;EAChD,MAAMC,EAAE,GAAI,2BAA2BH,UAAY,EAAC;EAEpD,OAAOD,MAAM,IAAII,EAAE;AACpB;AAEO,SAASC,uBAAuBA,CACtCC,KAAwB,EACxBC,GAAqC,EACpC;EACD,MAAM;IACLC,qBAAqB;IACrBC,sBAAsB,EAAEC,YAAY,GAAKC,KAAK,IAAMA,KAAK;IACzDC,oBAAoB;IACpBC,SAAS;IACTC,QAAQ,GAAG,KAAK;IAChBC,IAAI;IACJC,mBAAmB,GAAG,KAAK;IAC3BZ,EAAE,EAAEJ,MAAM;IACViB,oBAAoB,GAAG,KAAK;IAC5BC,KAAK;IACLC,aAAa,GAAG,KAAK;IACrBC,QAAQ,GAAGtB,IAAI;IACfuB,UAAU,GAAGvB,IAAI;IACjBwB,SAAS,GAAGxB,IAAI;IAChByB,MAAM;IACNC,IAAI,GAAG,SAAS;IAChBC,KAAK;IACLC,MAAM;IACNC,KAAK;IACL,GAAGC;EACJ,CAAC,GAAG,IAAAC,oDAAgC,EACnCvB,KAAK,EACL,4BAA4B,EAC5B,KACD,CAAC;EAED,MAAM,CAAEwB,SAAS,EAAEC,YAAY,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAErD,MAAM5B,EAAE,GAAGL,WAAW,CAAEC,MAAO,CAAC;EAChC,MAAMiC,OAAO,GAAG,IAAAC,mBAAU,EAAE,0BAA0B,EAAErB,SAAU,CAAC;EAEnE,MAAMsB,cAAc,GAAG,IAAAC,eAAQ,EAAE;IAChCT,KAAK;IACLU,MAAM,EAAET,SAAS,CAACS,MAAM;IACxBjB;EACD,CAAE,CAAC;;EAEH;EACA,MAAMkB,YAAY,GACjB,OAAOvB,IAAI,KAAK,QAAQ,GAAG,kBAAkB,GAAG,cAAc;EAC/D,MAAMwB,QAAQ,GAAG,CAAC,CAAExB,IAAI,GAAG;IAAE,CAAEuB,YAAY,GAAK,GAAGlC,EAAI;EAAQ,CAAC,GAAG,CAAC,CAAC;EAErE,OACC,IAAAoC,MAAA,CAAAC,aAAA,EAAC7C,YAAA,CAAA8C,OAAW;IACX7B,SAAS,EAAGoB,OAAS;IACrBlB,IAAI,EAAGA,IAAM;IACbX,EAAE,EAAGA,EAAI;IACTuC,uBAAuB;EAAA,GAEvB,IAAAH,MAAA,CAAAC,aAAA,EAACjD,UAAA,CAAAkD,OAAS;IACTlC,qBAAqB,EAAGA,qBAAuB;IAC/CI,oBAAoB,EAAGA,oBAAsB;IAC7CE,QAAQ,EAAGA,QAAU;IACrB8B,GAAG,EAAG,CAAG;IACT5B,mBAAmB,EAAGA,mBAAqB;IAC3CZ,EAAE,EAAGA,EAAI;IACT0B,SAAS,EAAGA,SAAW;IACvBe,OAAO,EAAC,MAAM;IACd3B,KAAK,EAAGA,KAAO;IACfC,aAAa,EAAGA,aAAe;IAC/BI,MAAM,EAAGA,MAAQ;IACjBC,IAAI,EAAGA,IAAM;IACbC,KAAK,EAAGA,KAAO;IACfC,MAAM,EAAGA;EAAQ,GAEjB,IAAAc,MAAA,CAAAC,aAAA,EAAChD,WAAA,CAAAiD,OAAU;IAAA,GACLd,SAAS;IAAA,GACTW,QAAQ;IACb/B,qBAAqB,EAAGA,qBAAuB;IAC/CK,SAAS,EAAC,iCAAiC;IAC3CC,QAAQ,EAAGA,QAAU;IACrBV,EAAE,EAAGA,EAAI;IACT0B,SAAS,EAAGA,SAAW;IACvBb,oBAAoB,EAAGA,oBAAsB;IAC7CK,SAAS,EAAGA,SAAW;IACvBD,UAAU,EAAGA,UAAY;IACzByB,kBAAkB,EAAGvB,MAAM,GAAG,IAAAwB,YAAK,EAAE,CAAE,CAAC,GAAGC,SAAW;IACtDC,gBAAgB,EAAGvB,MAAM,GAAG,IAAAqB,YAAK,EAAE,CAAE,CAAC,GAAGC,SAAW;IACpDzC,GAAG,EAAGA,GAAK;IACXwB,YAAY,EAAGA,YAAc;IAC7BP,IAAI,EAAGA,IAAM;IACbd,YAAY,EAAGA,YAAc;IAAA,GACxByB;EAAc,CACnB,CACS,CACC,CAAC;AAEhB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMhC,YAAY,GAAG,IAAA+C,mBAAU,EAAE7C,uBAAwB,CAAC;AAAC8C,OAAA,CAAAhD,YAAA,GAAAA,YAAA;AAAA,IAAAiD,QAAA,GAEnDjD,YAAY;AAAAgD,OAAA,CAAAT,OAAA,GAAAU,QAAA"}