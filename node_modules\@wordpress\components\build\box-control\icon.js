"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BoxControlIcon;
var _react = require("react");
var _boxControlIconStyles = require("./styles/box-control-icon-styles");
/**
 * Internal dependencies
 */

const BASE_ICON_SIZE = 24;
function BoxControlIcon({
  size = 24,
  side = 'all',
  sides,
  ...props
}) {
  const isSideDisabled = value => sides?.length && !sides.includes(value);
  const hasSide = value => {
    if (isSideDisabled(value)) {
      return false;
    }
    return side === 'all' || side === value;
  };
  const top = hasSide('top') || hasSide('vertical');
  const right = hasSide('right') || hasSide('horizontal');
  const bottom = hasSide('bottom') || hasSide('vertical');
  const left = hasSide('left') || hasSide('horizontal');

  // Simulates SVG Icon scaling.
  const scale = size / BASE_ICON_SIZE;
  return (0, _react.createElement)(_boxControlIconStyles.Root, {
    style: {
      transform: `scale(${scale})`
    },
    ...props
  }, (0, _react.createElement)(_boxControlIconStyles.Viewbox, null, (0, _react.createElement)(_boxControlIconStyles.TopStroke, {
    isFocused: top
  }), (0, _react.createElement)(_boxControlIconStyles.RightStroke, {
    isFocused: right
  }), (0, _react.createElement)(_boxControlIconStyles.BottomStroke, {
    isFocused: bottom
  }), (0, _react.createElement)(_boxControlIconStyles.LeftStroke, {
    isFocused: left
  })));
}
//# sourceMappingURL=icon.js.map