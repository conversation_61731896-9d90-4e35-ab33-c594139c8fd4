import { markToTrack, getUntracked } from 'proxy-compare';

const isObject = (x) => typeof x === "object" && x !== null;
const refSet = /* @__PURE__ */ new WeakSet();
const VERSION = (import.meta.env && import.meta.env.MODE) !== "production" ? Symbol("VERSION") : Symbol();
const LISTENERS = (import.meta.env && import.meta.env.MODE) !== "production" ? Symbol("LISTENERS") : Symbol();
const SNAPSHOT = (import.meta.env && import.meta.env.MODE) !== "production" ? Symbol("SNAPSHOT") : Symbol();
const buildProxyFunction = (objectIs = Object.is, newProxy = (target, handler) => new Proxy(target, handler), canProxy = (x) => isObject(x) && !refSet.has(x) && (Array.isArray(x) || !(Symbol.iterator in x)) && !(x instanceof WeakMap) && !(x instanceof WeakSet) && !(x instanceof Error) && !(x instanceof Number) && !(x instanceof Date) && !(x instanceof String) && !(x instanceof RegExp) && !(x instanceof ArrayBuffer), PROMISE_RESULT = (import.meta.env && import.meta.env.MODE) !== "production" ? Symbol("PROMISE_RESULT") : Symbol(), PROMISE_ERROR = (import.meta.env && import.meta.env.MODE) !== "production" ? Symbol("PROMISE_ERROR") : Symbol(), snapshotCache = /* @__PURE__ */ new WeakMap(), createSnapshot = (version, target, receiver) => {
  const cache = snapshotCache.get(receiver);
  if ((cache == null ? void 0 : cache[0]) === version) {
    return cache[1];
  }
  const snapshot2 = Array.isArray(target) ? [] : Object.create(Object.getPrototypeOf(target));
  markToTrack(snapshot2, true);
  snapshotCache.set(receiver, [version, snapshot2]);
  Reflect.ownKeys(target).forEach((key) => {
    const value = Reflect.get(target, key, receiver);
    if (refSet.has(value)) {
      markToTrack(value, false);
      snapshot2[key] = value;
    } else if (value instanceof Promise) {
      if (PROMISE_RESULT in value) {
        snapshot2[key] = value[PROMISE_RESULT];
      } else {
        const errorOrPromise = value[PROMISE_ERROR] || value;
        Object.defineProperty(snapshot2, key, {
          get() {
            if (PROMISE_RESULT in value) {
              return value[PROMISE_RESULT];
            }
            throw errorOrPromise;
          }
        });
      }
    } else if (value == null ? void 0 : value[LISTENERS]) {
      snapshot2[key] = value[SNAPSHOT];
    } else {
      snapshot2[key] = value;
    }
  });
  return Object.freeze(snapshot2);
}, proxyCache = /* @__PURE__ */ new WeakMap(), versionHolder = [1], proxyFunction2 = (initialObject) => {
  if (!isObject(initialObject)) {
    throw new Error("object required");
  }
  const found = proxyCache.get(initialObject);
  if (found) {
    return found;
  }
  let version = versionHolder[0];
  const listeners = /* @__PURE__ */ new Set();
  const notifyUpdate = (op, nextVersion = ++versionHolder[0]) => {
    if (version !== nextVersion) {
      version = nextVersion;
      listeners.forEach((listener) => listener(op, nextVersion));
    }
  };
  const propListeners = /* @__PURE__ */ new Map();
  const getPropListener = (prop) => {
    let propListener = propListeners.get(prop);
    if (!propListener) {
      propListener = (op, nextVersion) => {
        const newOp = [...op];
        newOp[1] = [prop, ...newOp[1]];
        notifyUpdate(newOp, nextVersion);
      };
      propListeners.set(prop, propListener);
    }
    return propListener;
  };
  const popPropListener = (prop) => {
    const propListener = propListeners.get(prop);
    propListeners.delete(prop);
    return propListener;
  };
  const baseObject = Array.isArray(initialObject) ? [] : Object.create(Object.getPrototypeOf(initialObject));
  const handler = {
    get(target, prop, receiver) {
      if (prop === VERSION) {
        return version;
      }
      if (prop === LISTENERS) {
        return listeners;
      }
      if (prop === SNAPSHOT) {
        return createSnapshot(version, target, receiver);
      }
      return Reflect.get(target, prop, receiver);
    },
    deleteProperty(target, prop) {
      const prevValue = Reflect.get(target, prop);
      const childListeners = prevValue == null ? void 0 : prevValue[LISTENERS];
      if (childListeners) {
        childListeners.delete(popPropListener(prop));
      }
      const deleted = Reflect.deleteProperty(target, prop);
      if (deleted) {
        notifyUpdate(["delete", [prop], prevValue]);
      }
      return deleted;
    },
    set(target, prop, value, receiver) {
      var _a;
      const hasPrevValue = Reflect.has(target, prop);
      const prevValue = Reflect.get(target, prop, receiver);
      if (hasPrevValue && objectIs(prevValue, value)) {
        return true;
      }
      const childListeners = prevValue == null ? void 0 : prevValue[LISTENERS];
      if (childListeners) {
        childListeners.delete(popPropListener(prop));
      }
      if (isObject(value)) {
        value = getUntracked(value) || value;
      }
      let nextValue;
      if ((_a = Object.getOwnPropertyDescriptor(target, prop)) == null ? void 0 : _a.set) {
        nextValue = value;
      } else if (value instanceof Promise) {
        nextValue = value.then((v) => {
          nextValue[PROMISE_RESULT] = v;
          notifyUpdate(["resolve", [prop], v]);
          return v;
        }).catch((e) => {
          nextValue[PROMISE_ERROR] = e;
          notifyUpdate(["reject", [prop], e]);
        });
      } else if (value == null ? void 0 : value[LISTENERS]) {
        nextValue = value;
        nextValue[LISTENERS].add(getPropListener(prop));
      } else if (canProxy(value)) {
        nextValue = proxy(value);
        nextValue[LISTENERS].add(getPropListener(prop));
      } else {
        nextValue = value;
      }
      Reflect.set(target, prop, nextValue, receiver);
      notifyUpdate(["set", [prop], value, prevValue]);
      return true;
    }
  };
  const proxyObject = newProxy(baseObject, handler);
  proxyCache.set(initialObject, proxyObject);
  Reflect.ownKeys(initialObject).forEach((key) => {
    const desc = Object.getOwnPropertyDescriptor(
      initialObject,
      key
    );
    if (desc.get || desc.set) {
      Object.defineProperty(baseObject, key, desc);
    } else {
      proxyObject[key] = initialObject[key];
    }
  });
  return proxyObject;
}) => [
  proxyFunction2,
  refSet,
  VERSION,
  LISTENERS,
  SNAPSHOT,
  objectIs,
  newProxy,
  canProxy,
  PROMISE_RESULT,
  PROMISE_ERROR,
  snapshotCache,
  createSnapshot,
  proxyCache,
  versionHolder
];
const [proxyFunction] = buildProxyFunction();
function proxy(initialObject = {}) {
  return proxyFunction(initialObject);
}
function getVersion(proxyObject) {
  return isObject(proxyObject) ? proxyObject[VERSION] : void 0;
}
function subscribe(proxyObject, callback, notifyInSync) {
  if ((import.meta.env && import.meta.env.MODE) !== "production" && !(proxyObject == null ? void 0 : proxyObject[LISTENERS])) {
    console.warn("Please use proxy object");
  }
  let promise;
  const ops = [];
  const listener = (op) => {
    ops.push(op);
    if (notifyInSync) {
      callback(ops.splice(0));
      return;
    }
    if (!promise) {
      promise = Promise.resolve().then(() => {
        promise = void 0;
        callback(ops.splice(0));
      });
    }
  };
  proxyObject[LISTENERS].add(listener);
  return () => {
    proxyObject[LISTENERS].delete(listener);
  };
}
function snapshot(proxyObject) {
  if ((import.meta.env && import.meta.env.MODE) !== "production" && !(proxyObject == null ? void 0 : proxyObject[SNAPSHOT])) {
    console.warn("Please use proxy object");
  }
  return proxyObject[SNAPSHOT];
}
function ref(obj) {
  refSet.add(obj);
  return obj;
}
const unstable_buildProxyFunction = buildProxyFunction;

export { getVersion, proxy, ref, snapshot, subscribe, unstable_buildProxyFunction };
