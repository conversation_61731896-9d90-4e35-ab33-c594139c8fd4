{"version": 3, "names": ["_reactNative", "require", "_icons", "_components", "_compose", "_style", "_interopRequireDefault", "_utils", "SelectedIcon", "opacity", "_react", "createElement", "Animated", "View", "style", "styles", "selected", "outline", "selectedOutline", "Icon", "icon", "check", "size", "ColorIndicator", "color", "isSelected", "withCustomPicker", "isGradient", "colorsUtils", "outlineStyle", "usePreferredColorSchemeStyle", "outlineDark", "Gradient", "circleOption", "gradientValue", "map", "absolute", "key", "backgroundColor", "_default", "exports", "default"], "sources": ["@wordpress/components/src/color-indicator/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, Animated } from 'react-native';\n/**\n * WordPress dependencies\n */\nimport { Icon, check } from '@wordpress/icons';\nimport { Gradient } from '@wordpress/components';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\nimport { colorsUtils } from '../mobile/color-settings/utils';\n\nfunction SelectedIcon( { opacity } ) {\n\treturn (\n\t\t<Animated.View style={ [ styles.selected, { opacity } ] }>\n\t\t\t<View style={ [ styles.outline, styles.selectedOutline ] } />\n\t\t\t<Icon icon={ check } style={ styles.icon } size={ 24 } />\n\t\t</Animated.View>\n\t);\n}\n\nfunction ColorIndicator( {\n\tcolor,\n\tisSelected,\n\twithCustomPicker,\n\tstyle,\n\topacity,\n} ) {\n\tconst { isGradient } = colorsUtils;\n\n\tconst outlineStyle = usePreferredColorSchemeStyle(\n\t\tstyles.outline,\n\t\tstyles.outlineDark\n\t);\n\n\tif ( isGradient( color ) ) {\n\t\treturn (\n\t\t\t<Gradient\n\t\t\t\tstyle={ [ styles.circleOption, style ] }\n\t\t\t\tgradientValue={ color }\n\t\t\t>\n\t\t\t\t<View style={ outlineStyle } />\n\t\t\t\t{ isSelected && <SelectedIcon opacity={ opacity } /> }\n\t\t\t</Gradient>\n\t\t);\n\t} else if ( withCustomPicker ) {\n\t\treturn (\n\t\t\t<View style={ [ styles.circleOption, style ] }>\n\t\t\t\t<View style={ outlineStyle } />\n\t\t\t\t{ color.map( ( gradientValue ) => {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<Gradient\n\t\t\t\t\t\t\tgradientValue={ gradientValue }\n\t\t\t\t\t\t\tstyle={ [\n\t\t\t\t\t\t\t\tstyles.circleOption,\n\t\t\t\t\t\t\t\tstyles.absolute,\n\t\t\t\t\t\t\t\tstyle,\n\t\t\t\t\t\t\t] }\n\t\t\t\t\t\t\tkey={ gradientValue }\n\t\t\t\t\t\t/>\n\t\t\t\t\t);\n\t\t\t\t} ) }\n\t\t\t\t{ isSelected && <SelectedIcon /> }\n\t\t\t</View>\n\t\t);\n\t}\n\treturn (\n\t\t<View\n\t\t\tstyle={ [ styles.circleOption, style, { backgroundColor: color } ] }\n\t\t>\n\t\t\t<View style={ outlineStyle } />\n\t\t\t{ isSelected && <SelectedIcon opacity={ opacity } /> }\n\t\t</View>\n\t);\n}\nexport default ColorIndicator;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAIA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAIA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AAdA;AACA;AACA;;AAEA;AACA;AACA;;AAIA;AACA;AACA;;AAIA,SAASO,YAAYA,CAAE;EAAEC;AAAQ,CAAC,EAAG;EACpC,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAY,QAAQ,CAACC,IAAI;IAACC,KAAK,EAAG,CAAEC,cAAM,CAACC,QAAQ,EAAE;MAAEP;IAAQ,CAAC;EAAI,GACxD,IAAAC,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAa,IAAI;IAACC,KAAK,EAAG,CAAEC,cAAM,CAACE,OAAO,EAAEF,cAAM,CAACG,eAAe;EAAI,CAAE,CAAC,EAC7D,IAAAR,MAAA,CAAAC,aAAA,EAACT,MAAA,CAAAiB,IAAI;IAACC,IAAI,EAAGC,YAAO;IAACP,KAAK,EAAGC,cAAM,CAACK,IAAM;IAACE,IAAI,EAAG;EAAI,CAAE,CAC1C,CAAC;AAElB;AAEA,SAASC,cAAcA,CAAE;EACxBC,KAAK;EACLC,UAAU;EACVC,gBAAgB;EAChBZ,KAAK;EACLL;AACD,CAAC,EAAG;EACH,MAAM;IAAEkB;EAAW,CAAC,GAAGC,kBAAW;EAElC,MAAMC,YAAY,GAAG,IAAAC,qCAA4B,EAChDf,cAAM,CAACE,OAAO,EACdF,cAAM,CAACgB,WACR,CAAC;EAED,IAAKJ,UAAU,CAAEH,KAAM,CAAC,EAAG;IAC1B,OACC,IAAAd,MAAA,CAAAC,aAAA,EAACR,WAAA,CAAA6B,QAAQ;MACRlB,KAAK,EAAG,CAAEC,cAAM,CAACkB,YAAY,EAAEnB,KAAK,CAAI;MACxCoB,aAAa,EAAGV;IAAO,GAEvB,IAAAd,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAa,IAAI;MAACC,KAAK,EAAGe;IAAc,CAAE,CAAC,EAC7BJ,UAAU,IAAI,IAAAf,MAAA,CAAAC,aAAA,EAACH,YAAY;MAACC,OAAO,EAAGA;IAAS,CAAE,CAC1C,CAAC;EAEb,CAAC,MAAM,IAAKiB,gBAAgB,EAAG;IAC9B,OACC,IAAAhB,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAa,IAAI;MAACC,KAAK,EAAG,CAAEC,cAAM,CAACkB,YAAY,EAAEnB,KAAK;IAAI,GAC7C,IAAAJ,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAa,IAAI;MAACC,KAAK,EAAGe;IAAc,CAAE,CAAC,EAC7BL,KAAK,CAACW,GAAG,CAAID,aAAa,IAAM;MACjC,OACC,IAAAxB,MAAA,CAAAC,aAAA,EAACR,WAAA,CAAA6B,QAAQ;QACRE,aAAa,EAAGA,aAAe;QAC/BpB,KAAK,EAAG,CACPC,cAAM,CAACkB,YAAY,EACnBlB,cAAM,CAACqB,QAAQ,EACftB,KAAK,CACH;QACHuB,GAAG,EAAGH;MAAe,CACrB,CAAC;IAEJ,CAAE,CAAC,EACDT,UAAU,IAAI,IAAAf,MAAA,CAAAC,aAAA,EAACH,YAAY,MAAE,CAC1B,CAAC;EAET;EACA,OACC,IAAAE,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAa,IAAI;IACJC,KAAK,EAAG,CAAEC,cAAM,CAACkB,YAAY,EAAEnB,KAAK,EAAE;MAAEwB,eAAe,EAAEd;IAAM,CAAC;EAAI,GAEpE,IAAAd,MAAA,CAAAC,aAAA,EAACX,YAAA,CAAAa,IAAI;IAACC,KAAK,EAAGe;EAAc,CAAE,CAAC,EAC7BJ,UAAU,IAAI,IAAAf,MAAA,CAAAC,aAAA,EAACH,YAAY;IAACC,OAAO,EAAGA;EAAS,CAAE,CAC9C,CAAC;AAET;AAAC,IAAA8B,QAAA,GACchB,cAAc;AAAAiB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}