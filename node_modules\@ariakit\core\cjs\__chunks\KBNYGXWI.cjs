"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _AV6KTKLEcjs = require('./AV6KTKLE.cjs');

// src/utils/misc.ts
function noop(..._) {
}
function shallowEqual(a, b) {
  if (a === b)
    return true;
  if (!a)
    return false;
  if (!b)
    return false;
  if (typeof a !== "object")
    return false;
  if (typeof b !== "object")
    return false;
  const aKeys = Object.keys(a);
  const bKeys = Object.keys(b);
  const { length } = aKeys;
  if (bKeys.length !== length)
    return false;
  for (const key of aKeys) {
    if (a[key] !== b[key]) {
      return false;
    }
  }
  return true;
}
function applyState(argument, currentValue) {
  if (isUpdater(argument)) {
    const value = isLazyValue(currentValue) ? currentValue() : currentValue;
    return argument(value);
  }
  return argument;
}
function isUpdater(argument) {
  return typeof argument === "function";
}
function isLazyValue(value) {
  return typeof value === "function";
}
function isObject(arg) {
  return typeof arg === "object" && arg != null;
}
function isEmpty(arg) {
  if (Array.isArray(arg))
    return !arg.length;
  if (isObject(arg))
    return !Object.keys(arg).length;
  if (arg == null)
    return true;
  if (arg === "")
    return true;
  return false;
}
function isInteger(arg) {
  if (typeof arg === "number") {
    return Math.floor(arg) === arg;
  }
  return String(Math.floor(Number(arg))) === arg;
}
function hasOwnProperty(object, prop) {
  if (typeof Object.hasOwn === "function") {
    return Object.hasOwn(object, prop);
  }
  return Object.prototype.hasOwnProperty.call(object, prop);
}
function chain(...fns) {
  return (...args) => {
    for (const fn of fns) {
      if (typeof fn === "function") {
        fn(...args);
      }
    }
  };
}
function cx(...args) {
  return args.filter(Boolean).join(" ") || void 0;
}
function normalizeString(str) {
  return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
}
function omit(object, keys) {
  const result = _AV6KTKLEcjs.__spreadValues.call(void 0, {}, object);
  for (const key of keys) {
    if (hasOwnProperty(result, key)) {
      delete result[key];
    }
  }
  return result;
}
function pick(object, paths) {
  const result = {};
  for (const key of paths) {
    if (hasOwnProperty(object, key)) {
      result[key] = object[key];
    }
  }
  return result;
}
function identity(value) {
  return value;
}
function beforePaint(cb = noop) {
  const raf = requestAnimationFrame(cb);
  return () => cancelAnimationFrame(raf);
}
function afterPaint(cb = noop) {
  let raf = requestAnimationFrame(() => {
    raf = requestAnimationFrame(cb);
  });
  return () => cancelAnimationFrame(raf);
}
function invariant(condition, message) {
  if (condition)
    return;
  if (typeof message !== "string")
    throw new Error("Invariant failed");
  throw new Error(message);
}
function getKeys(obj) {
  return Object.keys(obj);
}
function isFalsyBooleanCallback(booleanOrCallback, ...args) {
  const result = typeof booleanOrCallback === "function" ? booleanOrCallback(...args) : booleanOrCallback;
  if (result == null)
    return false;
  return !result;
}
function disabledFromProps(props) {
  return props.disabled || props["aria-disabled"] === true || props["aria-disabled"] === "true";
}
function defaultValue(...values) {
  for (const value of values) {
    if (value !== void 0)
      return value;
  }
  return void 0;
}






















exports.noop = noop; exports.shallowEqual = shallowEqual; exports.applyState = applyState; exports.isObject = isObject; exports.isEmpty = isEmpty; exports.isInteger = isInteger; exports.hasOwnProperty = hasOwnProperty; exports.chain = chain; exports.cx = cx; exports.normalizeString = normalizeString; exports.omit = omit; exports.pick = pick; exports.identity = identity; exports.beforePaint = beforePaint; exports.afterPaint = afterPaint; exports.invariant = invariant; exports.getKeys = getKeys; exports.isFalsyBooleanCallback = isFalsyBooleanCallback; exports.disabledFromProps = disabledFromProps; exports.defaultValue = defaultValue;
