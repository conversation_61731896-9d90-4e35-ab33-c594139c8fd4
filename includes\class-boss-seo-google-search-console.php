<?php

/**
 * Intégration Google Search Console pour Boss SEO.
 * 
 * Cette classe gère l'intégration avec Google Search Console
 * pour récupérer des données de performance réelles.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Intégration Google Search Console.
 *
 * Fonctionnalités :
 * - Authentification OAuth2
 * - Récupération des données de performance
 * - Analyse des mots-clés
 * - Données de clics et impressions
 * - Cache intelligent des données
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Google_Search_Console {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * Configuration de l'API.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $config    Configuration API.
     */
    private $config;

    /**
     * Token d'accès.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $access_token    Token d'accès.
     */
    private $access_token;

    /**
     * URL de base de l'API.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $api_base_url    URL de base.
     */
    private $api_base_url = 'https://searchconsole.googleapis.com/webmasters/v3';

    /**
     * Durée du cache en secondes (6 heures).
     *
     * @since    1.2.0
     * @access   private
     * @var      int    $cache_duration    Durée du cache.
     */
    private $cache_duration = 21600;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    string    $plugin_name    Le nom du plugin.
     */
    public function __construct( $plugin_name ) {
        $this->plugin_name = $plugin_name;
        $this->load_config();
        $this->load_access_token();
    }

    /**
     * Charge la configuration depuis les paramètres.
     *
     * @since    1.2.0
     */
    private function load_config() {
        $external_services = get_option( 'boss_seo_external_services', array() );
        
        $this->config = array(
            'client_id' => $external_services['google_search_console_client_id'] ?? '',
            'client_secret' => $external_services['google_search_console_client_secret'] ?? '',
            'redirect_uri' => admin_url( 'admin.php?page=boss-seo-settings&tab=external-services' ),
            'site_url' => home_url(),
            'enabled' => ! empty( $external_services['google_search_console_enabled'] )
        );
    }

    /**
     * Charge le token d'accès stocké.
     *
     * @since    1.2.0
     */
    private function load_access_token() {
        $tokens = get_option( 'boss_seo_gsc_tokens', array() );
        $this->access_token = $tokens['access_token'] ?? '';
    }

    /**
     * Vérifie si l'intégration est configurée et active.
     *
     * @since    1.2.0
     * @return   bool    État de la configuration.
     */
    public function is_configured() {
        return $this->config['enabled'] && 
               ! empty( $this->config['client_id'] ) && 
               ! empty( $this->config['client_secret'] ) &&
               ! empty( $this->access_token );
    }

    /**
     * Récupère les données de performance pour une période.
     *
     * @since    1.2.0
     * @param    array    $options    Options de requête.
     * @return   array|WP_Error       Données de performance ou erreur.
     */
    public function get_performance_data( $options = array() ) {
        if ( ! $this->is_configured() ) {
            return new WP_Error( 'gsc_not_configured', 'Google Search Console non configuré' );
        }

        $defaults = array(
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d', strtotime( '-1 day' ) ),
            'dimensions' => array( 'query' ),
            'row_limit' => 100,
            'start_row' => 0
        );

        $options = wp_parse_args( $options, $defaults );
        
        // Vérifier le cache
        $cache_key = 'gsc_performance_' . md5( serialize( $options ) );
        $cached_data = get_transient( $cache_key );
        
        if ( $cached_data !== false ) {
            return $cached_data;
        }

        try {
            $data = $this->make_api_request( 'searchAnalytics/query', $options );
            
            if ( is_wp_error( $data ) ) {
                return $data;
            }

            // Traiter et formater les données
            $formatted_data = $this->format_performance_data( $data, $options );
            
            // Mettre en cache
            set_transient( $cache_key, $formatted_data, $this->cache_duration );
            
            return $formatted_data;

        } catch ( Exception $e ) {
            return new WP_Error( 'gsc_api_error', $e->getMessage() );
        }
    }

    /**
     * Récupère les mots-clés les plus performants.
     *
     * @since    1.2.0
     * @param    int      $limit    Nombre de mots-clés à récupérer.
     * @return   array|WP_Error     Mots-clés ou erreur.
     */
    public function get_top_keywords( $limit = 50 ) {
        $options = array(
            'dimensions' => array( 'query' ),
            'row_limit' => $limit,
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d', strtotime( '-1 day' ) )
        );

        $data = $this->get_performance_data( $options );
        
        if ( is_wp_error( $data ) ) {
            return $data;
        }

        return $data['keywords'] ?? array();
    }

    /**
     * Récupère les pages les plus performantes.
     *
     * @since    1.2.0
     * @param    int      $limit    Nombre de pages à récupérer.
     * @return   array|WP_Error     Pages ou erreur.
     */
    public function get_top_pages( $limit = 50 ) {
        $options = array(
            'dimensions' => array( 'page' ),
            'row_limit' => $limit,
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d', strtotime( '-1 day' ) )
        );

        $data = $this->get_performance_data( $options );
        
        if ( is_wp_error( $data ) ) {
            return $data;
        }

        return $data['pages'] ?? array();
    }

    /**
     * Récupère les données de performance par appareil.
     *
     * @since    1.2.0
     * @return   array|WP_Error    Données par appareil ou erreur.
     */
    public function get_device_performance() {
        $options = array(
            'dimensions' => array( 'device' ),
            'start_date' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'end_date' => date( 'Y-m-d', strtotime( '-1 day' ) )
        );

        return $this->get_performance_data( $options );
    }

    /**
     * Effectue une requête à l'API Google Search Console.
     *
     * @since    1.2.0
     * @param    string    $endpoint    Endpoint de l'API.
     * @param    array     $data        Données de la requête.
     * @return   array|WP_Error         Réponse de l'API ou erreur.
     */
    private function make_api_request( $endpoint, $data = array() ) {
        $url = $this->api_base_url . '/sites/' . urlencode( $this->config['site_url'] ) . '/' . $endpoint;
        
        $request_body = array(
            'startDate' => $data['start_date'],
            'endDate' => $data['end_date'],
            'dimensions' => $data['dimensions'],
            'rowLimit' => $data['row_limit'],
            'startRow' => $data['start_row'] ?? 0
        );

        $response = wp_remote_post( $url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->access_token,
                'Content-Type' => 'application/json'
            ),
            'body' => wp_json_encode( $request_body ),
            'timeout' => 30
        ) );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        $response_body = wp_remote_retrieve_body( $response );

        if ( $response_code !== 200 ) {
            $error_data = json_decode( $response_body, true );
            $error_message = $error_data['error']['message'] ?? 'Erreur API inconnue';
            
            // Gérer l'expiration du token
            if ( $response_code === 401 ) {
                $this->refresh_access_token();
                return new WP_Error( 'gsc_token_expired', 'Token expiré, veuillez vous reconnecter' );
            }
            
            return new WP_Error( 'gsc_api_error', $error_message );
        }

        $data = json_decode( $response_body, true );
        
        if ( json_last_error() !== JSON_ERROR_NONE ) {
            return new WP_Error( 'gsc_json_error', 'Erreur de décodage JSON' );
        }

        return $data;
    }

    /**
     * Formate les données de performance.
     *
     * @since    1.2.0
     * @param    array    $raw_data    Données brutes de l'API.
     * @param    array    $options     Options de la requête.
     * @return   array                 Données formatées.
     */
    private function format_performance_data( $raw_data, $options ) {
        $formatted = array(
            'total_clicks' => 0,
            'total_impressions' => 0,
            'average_ctr' => 0,
            'average_position' => 0,
            'keywords' => array(),
            'pages' => array(),
            'devices' => array(),
            'period' => array(
                'start' => $options['start_date'],
                'end' => $options['end_date']
            )
        );

        if ( ! isset( $raw_data['rows'] ) || empty( $raw_data['rows'] ) ) {
            return $formatted;
        }

        $total_clicks = 0;
        $total_impressions = 0;
        $total_position = 0;
        $row_count = 0;

        foreach ( $raw_data['rows'] as $row ) {
            $clicks = $row['clicks'] ?? 0;
            $impressions = $row['impressions'] ?? 0;
            $ctr = $row['ctr'] ?? 0;
            $position = $row['position'] ?? 0;

            $total_clicks += $clicks;
            $total_impressions += $impressions;
            $total_position += $position;
            $row_count++;

            // Formater selon la dimension
            if ( in_array( 'query', $options['dimensions'] ) ) {
                $formatted['keywords'][] = array(
                    'keyword' => $row['keys'][0] ?? '',
                    'clicks' => $clicks,
                    'impressions' => $impressions,
                    'ctr' => round( $ctr * 100, 2 ),
                    'position' => round( $position, 1 )
                );
            }

            if ( in_array( 'page', $options['dimensions'] ) ) {
                $formatted['pages'][] = array(
                    'page' => $row['keys'][0] ?? '',
                    'clicks' => $clicks,
                    'impressions' => $impressions,
                    'ctr' => round( $ctr * 100, 2 ),
                    'position' => round( $position, 1 )
                );
            }

            if ( in_array( 'device', $options['dimensions'] ) ) {
                $formatted['devices'][] = array(
                    'device' => $row['keys'][0] ?? '',
                    'clicks' => $clicks,
                    'impressions' => $impressions,
                    'ctr' => round( $ctr * 100, 2 ),
                    'position' => round( $position, 1 )
                );
            }
        }

        // Calculer les moyennes
        $formatted['total_clicks'] = $total_clicks;
        $formatted['total_impressions'] = $total_impressions;
        $formatted['average_ctr'] = $total_impressions > 0 ? round( ( $total_clicks / $total_impressions ) * 100, 2 ) : 0;
        $formatted['average_position'] = $row_count > 0 ? round( $total_position / $row_count, 1 ) : 0;

        return $formatted;
    }

    /**
     * Rafraîchit le token d'accès.
     *
     * @since    1.2.0
     * @return   bool    Succès du rafraîchissement.
     */
    private function refresh_access_token() {
        $tokens = get_option( 'boss_seo_gsc_tokens', array() );
        $refresh_token = $tokens['refresh_token'] ?? '';

        if ( empty( $refresh_token ) ) {
            return false;
        }

        // Logique de rafraîchissement du token à implémenter
        // Pour l'instant, retourner false pour forcer une nouvelle authentification
        return false;
    }

    /**
     * Vide le cache des données GSC.
     *
     * @since    1.2.0
     */
    public function clear_cache() {
        global $wpdb;
        
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_gsc_%' 
             OR option_name LIKE '_transient_timeout_gsc_%'"
        );
    }

    /**
     * Obtient l'URL d'authentification OAuth.
     *
     * @since    1.2.0
     * @return   string    URL d'authentification.
     */
    public function get_auth_url() {
        $params = array(
            'client_id' => $this->config['client_id'],
            'redirect_uri' => $this->config['redirect_uri'],
            'scope' => 'https://www.googleapis.com/auth/webmasters.readonly',
            'response_type' => 'code',
            'access_type' => 'offline',
            'prompt' => 'consent'
        );

        return 'https://accounts.google.com/o/oauth2/auth?' . http_build_query( $params );
    }
}
