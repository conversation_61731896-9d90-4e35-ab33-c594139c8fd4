"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _anglePickerControlStyles = require("./styles/angle-picker-control-styles");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function AngleCircle({
  value,
  onChange,
  ...props
}) {
  const angleCircleRef = (0, _element.useRef)(null);
  const angleCircleCenter = (0, _element.useRef)();
  const previousCursorValue = (0, _element.useRef)();
  const setAngleCircleCenter = () => {
    if (angleCircleRef.current === null) {
      return;
    }
    const rect = angleCircleRef.current.getBoundingClientRect();
    angleCircleCenter.current = {
      x: rect.x + rect.width / 2,
      y: rect.y + rect.height / 2
    };
  };
  const changeAngleToPosition = event => {
    if (event === undefined) {
      return;
    }

    // Prevent (drag) mouse events from selecting and accidentally
    // triggering actions from other elements.
    event.preventDefault();
    // Input control needs to lose focus and by preventDefault above, it doesn't.
    event.target?.focus();
    if (angleCircleCenter.current !== undefined && onChange !== undefined) {
      const {
        x: centerX,
        y: centerY
      } = angleCircleCenter.current;
      onChange(getAngle(centerX, centerY, event.clientX, event.clientY));
    }
  };
  const {
    startDrag,
    isDragging
  } = (0, _compose.__experimentalUseDragging)({
    onDragStart: event => {
      setAngleCircleCenter();
      changeAngleToPosition(event);
    },
    onDragMove: changeAngleToPosition,
    onDragEnd: changeAngleToPosition
  });
  (0, _element.useEffect)(() => {
    if (isDragging) {
      if (previousCursorValue.current === undefined) {
        previousCursorValue.current = document.body.style.cursor;
      }
      document.body.style.cursor = 'grabbing';
    } else {
      document.body.style.cursor = previousCursorValue.current || '';
      previousCursorValue.current = undefined;
    }
  }, [isDragging]);
  return (0, _react.createElement)(_anglePickerControlStyles.CircleRoot, {
    ref: angleCircleRef,
    onMouseDown: startDrag,
    className: "components-angle-picker-control__angle-circle",
    ...props
  }, (0, _react.createElement)(_anglePickerControlStyles.CircleIndicatorWrapper, {
    style: value ? {
      transform: `rotate(${value}deg)`
    } : undefined,
    className: "components-angle-picker-control__angle-circle-indicator-wrapper",
    tabIndex: -1
  }, (0, _react.createElement)(_anglePickerControlStyles.CircleIndicator, {
    className: "components-angle-picker-control__angle-circle-indicator"
  })));
}
function getAngle(centerX, centerY, pointX, pointY) {
  const y = pointY - centerY;
  const x = pointX - centerX;
  const angleInRadians = Math.atan2(y, x);
  const angleInDeg = Math.round(angleInRadians * (180 / Math.PI)) + 90;
  if (angleInDeg < 0) {
    return 360 + angleInDeg;
  }
  return angleInDeg;
}
var _default = AngleCircle;
exports.default = _default;
//# sourceMappingURL=angle-circle.js.map