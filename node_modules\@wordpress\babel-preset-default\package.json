{"name": "@wordpress/babel-preset-default", "version": "7.42.0", "description": "Default Babel preset for WordPress development.", "author": "The WordPress Contributors", "license": "GPL-2.0-or-later", "keywords": ["wordpress", "<PERSON><PERSON>", "babel", "preset", "babel-preset"], "homepage": "https://github.com/WordPress/gutenberg/tree/HEAD/packages/babel-preset-default/README.md", "repository": {"type": "git", "url": "https://github.com/WordPress/gutenberg.git", "directory": "packages/babel-preset-default"}, "bugs": {"url": "https://github.com/WordPress/gutenberg/issues"}, "engines": {"node": ">=14"}, "files": ["build", "index.js"], "main": "index.js", "dependencies": {"@babel/core": "^7.16.0", "@babel/plugin-transform-react-jsx": "^7.16.0", "@babel/plugin-transform-runtime": "^7.16.0", "@babel/preset-env": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@babel/runtime": "^7.16.0", "@wordpress/babel-plugin-import-jsx-pragma": "^4.41.0", "@wordpress/browserslist-config": "^5.41.0", "@wordpress/warning": "^2.58.0", "browserslist": "^4.21.10", "core-js": "^3.31.0", "react": "^18.3.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "node ./bin/index.js"}, "gitHead": "42f38f287506a6b3ed8ccba839b18ad066821044"}