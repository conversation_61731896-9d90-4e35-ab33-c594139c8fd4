"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CustomSelect = CustomSelect;
exports.CustomSelectContext = void 0;
exports.CustomSelectItem = CustomSelectItem;
var _react = require("react");
var Ariakit = _interopRequireWildcard(require("@ariakit/react"));
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var Styled = _interopRequireWildcard(require("./styles"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */
// eslint-disable-next-line no-restricted-imports

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const CustomSelectContext = (0, _element.createContext)(undefined);
exports.CustomSelectContext = CustomSelectContext;
function defaultRenderSelectedValue(value) {
  const isValueEmpty = Array.isArray(value) ? value.length === 0 : value === undefined || value === null;
  if (isValueEmpty) {
    return (0, _i18n.__)('Select an item');
  }
  if (Array.isArray(value)) {
    return value.length === 1 ? value[0] :
    // translators: %s: number of items selected (it will always be 2 or more items)
    (0, _i18n.sprintf)((0, _i18n.__)('%s items selected'), value.length);
  }
  return value;
}
function CustomSelect({
  children,
  defaultValue,
  label,
  onChange,
  size = 'default',
  value,
  renderSelectedValue,
  ...props
}) {
  const store = Ariakit.useSelectStore({
    setValue: nextValue => onChange?.(nextValue),
    defaultValue,
    value
  });
  const {
    value: currentValue
  } = store.useState();
  const computedRenderSelectedValue = renderSelectedValue !== null && renderSelectedValue !== void 0 ? renderSelectedValue : defaultRenderSelectedValue;
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(Styled.CustomSelectLabel, {
    store: store
  }, label), (0, _react.createElement)(Styled.CustomSelectButton, {
    ...props,
    size: size,
    hasCustomRenderProp: !!renderSelectedValue,
    store: store
  }, computedRenderSelectedValue(currentValue), (0, _react.createElement)(Ariakit.SelectArrow, null)), (0, _react.createElement)(Styled.CustomSelectPopover, {
    gutter: 12,
    store: store,
    sameWidth: true
  }, (0, _react.createElement)(CustomSelectContext.Provider, {
    value: {
      store
    }
  }, children)));
}
function CustomSelectItem({
  children,
  ...props
}) {
  const customSelectContext = (0, _element.useContext)(CustomSelectContext);
  return (0, _react.createElement)(Styled.CustomSelectItem, {
    store: customSelectContext?.store,
    ...props
  }, children !== null && children !== void 0 ? children : props.value, (0, _react.createElement)(Ariakit.SelectItemCheck, null));
}
//# sourceMappingURL=index.js.map