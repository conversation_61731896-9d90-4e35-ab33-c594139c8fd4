<?php
/**
 * Classe pour la génération de pages locales.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour la génération de pages locales.
 *
 * Cette classe gère toutes les fonctionnalités liées à la génération de pages locales.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Local_Page_Generator {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_local_page_';

    /**
     * Le préfixe pour les métadonnées.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $meta_prefix    Le préfixe pour les métadonnées.
     */
    protected $meta_prefix = 'boss_local_page_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_generate_local_pages', array( $this, 'ajax_generate_local_pages' ) );
        add_action( 'wp_ajax_boss_seo_regenerate_local_page', array( $this, 'ajax_regenerate_local_page' ) );
        add_action( 'wp_ajax_boss_seo_delete_local_page', array( $this, 'ajax_delete_local_page' ) );
        add_action( 'wp_ajax_boss_seo_get_local_pages', array( $this, 'ajax_get_local_pages' ) );
        add_action( 'wp_ajax_boss_seo_get_page_templates', array( $this, 'ajax_get_page_templates' ) );

        // Ajouter les filtres pour les shortcodes
        add_shortcode( 'boss_local_info', array( $this, 'shortcode_local_info' ) );
        add_shortcode( 'boss_local_map', array( $this, 'shortcode_local_map' ) );
        add_shortcode( 'boss_local_hours', array( $this, 'shortcode_local_hours' ) );
        add_shortcode( 'boss_local_schema', array( $this, 'shortcode_local_schema' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Route pour générer une page
        register_rest_route(
            'boss-seo/v1',
            '/local/generate-page',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'generate_single_page' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'page' => array(
                        'required'    => true,
                        'type'        => 'object',
                        'description' => __( 'Les données de la page à générer.', 'boss-seo' ),
                    ),
                ),
            )
        );

        // Route pour récupérer les pages générées
        register_rest_route(
            'boss-seo/v1',
            '/local/generated-pages/(?P<location_id>\d+)',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_generated_pages_by_location' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Routes existantes
        register_rest_route(
            'boss-seo/v1',
            '/local-pages',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_local_pages' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'generate_local_pages' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/local-pages/(?P<id>\d+)',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_local_page' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'regenerate_local_page' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_local_page' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/page-templates',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_page_templates' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les pages locales via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_local_pages( $request ) {
        $local_pages = $this->get_all_local_pages();
        return rest_ensure_response( $local_pages );
    }

    /**
     * Récupère une page locale spécifique via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_local_page( $request ) {
        $page_id = $request['id'];

        // Vérifier si la page existe
        $page = get_post( $page_id );
        if ( ! $page || $page->post_type !== 'page' ) {
            return new WP_Error( 'page_not_found', __( 'Page non trouvée.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Vérifier si c'est une page locale
        $location_id = get_post_meta( $page_id, $this->meta_prefix . 'location_id', true );
        if ( ! $location_id ) {
            return new WP_Error( 'not_local_page', __( 'Cette page n\'est pas une page locale.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les données de la page
        $local_page = $this->prepare_local_page_for_response( $page_id );

        return rest_ensure_response( $local_page );
    }

    /**
     * Génère des pages locales via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_local_pages( $request ) {
        $params = $request->get_params();

        // Vérifier les paramètres requis
        if ( ! isset( $params['location_ids'] ) || ! is_array( $params['location_ids'] ) ) {
            return new WP_Error( 'missing_location_ids', __( 'Les IDs des emplacements sont requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( ! isset( $params['template'] ) || empty( $params['template'] ) ) {
            return new WP_Error( 'missing_template', __( 'Le modèle de page est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les options
        $options = array(
            'template'      => sanitize_text_field( $params['template'] ),
            'url_structure' => isset( $params['url_structure'] ) ? sanitize_text_field( $params['url_structure'] ) : 'city',
            'parent_page'   => isset( $params['parent_page'] ) ? absint( $params['parent_page'] ) : 0,
            'elements'      => isset( $params['elements'] ) && is_array( $params['elements'] ) ? $params['elements'] : array(),
        );

        // Générer les pages
        $location_ids = array_map( 'absint', $params['location_ids'] );
        $results = $this->generate_pages( $location_ids, $options );

        return rest_ensure_response( $results );
    }

    /**
     * Régénère une page locale via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function regenerate_local_page( $request ) {
        $page_id = $request['id'];
        $params = $request->get_params();

        // Vérifier si la page existe
        $page = get_post( $page_id );
        if ( ! $page || $page->post_type !== 'page' ) {
            return new WP_Error( 'page_not_found', __( 'Page non trouvée.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Vérifier si c'est une page locale
        $location_id = get_post_meta( $page_id, $this->meta_prefix . 'location_id', true );
        if ( ! $location_id ) {
            return new WP_Error( 'not_local_page', __( 'Cette page n\'est pas une page locale.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les options
        $options = array(
            'template'      => isset( $params['template'] ) ? sanitize_text_field( $params['template'] ) : get_post_meta( $page_id, $this->meta_prefix . 'template', true ),
            'url_structure' => isset( $params['url_structure'] ) ? sanitize_text_field( $params['url_structure'] ) : get_post_meta( $page_id, $this->meta_prefix . 'url_structure', true ),
            'parent_page'   => isset( $params['parent_page'] ) ? absint( $params['parent_page'] ) : get_post_meta( $page_id, $this->meta_prefix . 'parent_page', true ),
            'elements'      => isset( $params['elements'] ) && is_array( $params['elements'] ) ? $params['elements'] : get_post_meta( $page_id, $this->meta_prefix . 'elements', true ),
        );

        // Régénérer la page
        $result = $this->regenerate_page( $page_id, $location_id, $options );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        // Récupérer les données de la page
        $local_page = $this->prepare_local_page_for_response( $page_id );

        return rest_ensure_response( $local_page );
    }

    /**
     * Supprime une page locale via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_local_page( $request ) {
        $page_id = $request['id'];

        // Vérifier si la page existe
        $page = get_post( $page_id );
        if ( ! $page || $page->post_type !== 'page' ) {
            return new WP_Error( 'page_not_found', __( 'Page non trouvée.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        // Vérifier si c'est une page locale
        $location_id = get_post_meta( $page_id, $this->meta_prefix . 'location_id', true );
        if ( ! $location_id ) {
            return new WP_Error( 'not_local_page', __( 'Cette page n\'est pas une page locale.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Supprimer la page
        $result = wp_delete_post( $page_id, true );

        if ( ! $result ) {
            return new WP_Error( 'delete_failed', __( 'La suppression de la page a échoué.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'deleted'  => true,
            'page_id'  => $page_id,
        ) );
    }

    /**
     * Génère une page unique via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function generate_single_page( $request ) {
        $params = $request->get_params();
        $page_data = isset( $params['page'] ) ? $params['page'] : array();

        // Vérifier les paramètres requis
        if ( ! isset( $page_data['location_id'] ) ) {
            return new WP_Error( 'missing_location_id', __( 'L\'ID de l\'emplacement est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        if ( ! isset( $page_data['template'] ) ) {
            return new WP_Error( 'missing_template', __( 'Le modèle de page est requis.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        $location_id = absint( $page_data['location_id'] );
        $options = array(
            'template'      => sanitize_text_field( $page_data['template'] ),
            'url_structure' => isset( $page_data['url_structure'] ) ? sanitize_text_field( $page_data['url_structure'] ) : 'city',
            'parent_page'   => isset( $page_data['parent_page'] ) ? absint( $page_data['parent_page'] ) : 0,
            'elements'      => isset( $page_data['elements'] ) && is_array( $page_data['elements'] ) ? $page_data['elements'] : array(),
        );

        // Générer la page
        $results = $this->generate_pages( array( $location_id ), $options );

        if ( ! empty( $results['errors'] ) ) {
            return new WP_Error( 'generation_failed', $results['errors'][0]['message'], array( 'status' => 500 ) );
        }

        if ( empty( $results['success'] ) ) {
            return new WP_Error( 'generation_failed', __( 'Erreur lors de la génération de la page.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Page générée avec succès.', 'boss-seo' ),
            'data'    => $results['success'][0]
        ) );
    }

    /**
     * Récupère les pages générées pour un emplacement via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_generated_pages_by_location( $request ) {
        $location_id = absint( $request['location_id'] );

        if ( ! $location_id ) {
            return new WP_Error( 'invalid_location_id', __( 'ID d\'emplacement invalide.', 'boss-seo' ), array( 'status' => 400 ) );
        }

        // Récupérer les pages pour cet emplacement
        $args = array(
            'post_type'      => 'page',
            'post_status'    => array( 'publish', 'draft' ),
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'     => $this->meta_prefix . 'location_id',
                    'value'   => $location_id,
                    'compare' => '=',
                ),
            ),
        );

        $query = new WP_Query( $args );
        $pages = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $pages[] = $this->prepare_local_page_for_response( get_the_ID() );
            }
            wp_reset_postdata();
        }

        return rest_ensure_response( $pages );
    }

    /**
     * Récupère les modèles de page via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_page_templates( $request ) {
        $templates = $this->get_available_templates();
        return rest_ensure_response( $templates );
    }

    /**
     * Gère les requêtes AJAX pour générer des pages locales.
     *
     * @since    1.2.0
     */
    public function ajax_generate_local_pages() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['location_ids'] ) || ! is_array( $_POST['location_ids'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Les IDs des emplacements sont requis.', 'boss-seo' ) ) );
        }

        if ( ! isset( $_POST['template'] ) || empty( $_POST['template'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Le modèle de page est requis.', 'boss-seo' ) ) );
        }

        // Récupérer les options
        $options = array(
            'template'      => sanitize_text_field( $_POST['template'] ),
            'url_structure' => isset( $_POST['url_structure'] ) ? sanitize_text_field( $_POST['url_structure'] ) : 'city',
            'parent_page'   => isset( $_POST['parent_page'] ) ? absint( $_POST['parent_page'] ) : 0,
            'elements'      => isset( $_POST['elements'] ) && is_array( $_POST['elements'] ) ? $_POST['elements'] : array(),
        );

        // Générer les pages
        $location_ids = array_map( 'absint', $_POST['location_ids'] );
        $results = $this->generate_pages( $location_ids, $options );

        wp_send_json_success( $results );
    }

    /**
     * Gère les requêtes AJAX pour régénérer une page locale.
     *
     * @since    1.2.0
     */
    public function ajax_regenerate_local_page() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['page_id'] ) || empty( $_POST['page_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de la page est requis.', 'boss-seo' ) ) );
        }

        $page_id = absint( $_POST['page_id'] );

        // Vérifier si la page existe
        $page = get_post( $page_id );
        if ( ! $page || $page->post_type !== 'page' ) {
            wp_send_json_error( array( 'message' => __( 'Page non trouvée.', 'boss-seo' ) ) );
        }

        // Vérifier si c'est une page locale
        $location_id = get_post_meta( $page_id, $this->meta_prefix . 'location_id', true );
        if ( ! $location_id ) {
            wp_send_json_error( array( 'message' => __( 'Cette page n\'est pas une page locale.', 'boss-seo' ) ) );
        }

        // Récupérer les options
        $options = array(
            'template'      => isset( $_POST['template'] ) ? sanitize_text_field( $_POST['template'] ) : get_post_meta( $page_id, $this->meta_prefix . 'template', true ),
            'url_structure' => isset( $_POST['url_structure'] ) ? sanitize_text_field( $_POST['url_structure'] ) : get_post_meta( $page_id, $this->meta_prefix . 'url_structure', true ),
            'parent_page'   => isset( $_POST['parent_page'] ) ? absint( $_POST['parent_page'] ) : get_post_meta( $page_id, $this->meta_prefix . 'parent_page', true ),
            'elements'      => isset( $_POST['elements'] ) && is_array( $_POST['elements'] ) ? $_POST['elements'] : get_post_meta( $page_id, $this->meta_prefix . 'elements', true ),
        );

        // Régénérer la page
        $result = $this->regenerate_page( $page_id, $location_id, $options );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => $result->get_error_message() ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Page régénérée avec succès.', 'boss-seo' ),
            'page'    => $this->prepare_local_page_for_response( $page_id ),
        ) );
    }

    /**
     * Gère les requêtes AJAX pour supprimer une page locale.
     *
     * @since    1.2.0
     */
    public function ajax_delete_local_page() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier les paramètres requis
        if ( ! isset( $_POST['page_id'] ) || empty( $_POST['page_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'L\'ID de la page est requis.', 'boss-seo' ) ) );
        }

        $page_id = absint( $_POST['page_id'] );

        // Vérifier si la page existe
        $page = get_post( $page_id );
        if ( ! $page || $page->post_type !== 'page' ) {
            wp_send_json_error( array( 'message' => __( 'Page non trouvée.', 'boss-seo' ) ) );
        }

        // Vérifier si c'est une page locale
        $location_id = get_post_meta( $page_id, $this->meta_prefix . 'location_id', true );
        if ( ! $location_id ) {
            wp_send_json_error( array( 'message' => __( 'Cette page n\'est pas une page locale.', 'boss-seo' ) ) );
        }

        // Supprimer la page
        $result = wp_delete_post( $page_id, true );

        if ( ! $result ) {
            wp_send_json_error( array( 'message' => __( 'La suppression de la page a échoué.', 'boss-seo' ) ) );
        }

        wp_send_json_success( array(
            'message' => __( 'Page supprimée avec succès.', 'boss-seo' ),
            'page_id' => $page_id,
        ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les pages locales.
     *
     * @since    1.2.0
     */
    public function ajax_get_local_pages() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les pages locales
        $local_pages = $this->get_all_local_pages();

        wp_send_json_success( $local_pages );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les modèles de page.
     *
     * @since    1.2.0
     */
    public function ajax_get_page_templates() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les modèles de page
        $templates = $this->get_available_templates();

        wp_send_json_success( $templates );
    }

    /**
     * Récupère toutes les pages locales.
     *
     * @since    1.2.0
     * @return   array    Les pages locales.
     */
    public function get_all_local_pages() {
        $args = array(
            'post_type'      => 'page',
            'post_status'    => 'publish',
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'     => $this->meta_prefix . 'location_id',
                    'compare' => 'EXISTS',
                ),
            ),
        );

        $query = new WP_Query( $args );
        $pages = array();

        if ( $query->have_posts() ) {
            while ( $query->have_posts() ) {
                $query->the_post();
                $pages[] = $this->prepare_local_page_for_response( get_the_ID() );
            }
            wp_reset_postdata();
        }

        return $pages;
    }

    /**
     * Récupère une page locale par ID d'emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   int|false                 L'ID de la page ou false si aucune page n'est trouvée.
     */
    public function get_page_by_location_id( $location_id ) {
        $args = array(
            'post_type'      => 'page',
            'post_status'    => 'publish',
            'posts_per_page' => 1,
            'meta_query'     => array(
                array(
                    'key'     => $this->meta_prefix . 'location_id',
                    'value'   => $location_id,
                    'compare' => '=',
                ),
            ),
        );

        $query = new WP_Query( $args );

        if ( $query->have_posts() ) {
            $query->the_post();
            $page_id = get_the_ID();
            wp_reset_postdata();
            return $page_id;
        }

        return false;
    }

    /**
     * Prépare les données d'une page locale pour la réponse API.
     *
     * @since    1.2.0
     * @param    int       $page_id    L'ID de la page.
     * @return   array                 Les données de la page.
     */
    public function prepare_local_page_for_response( $page_id ) {
        $page = get_post( $page_id );

        if ( ! $page || $page->post_type !== 'page' ) {
            return array();
        }

        $location_id = get_post_meta( $page_id, $this->meta_prefix . 'location_id', true );
        $location = get_post( $location_id );

        $page_data = array(
            'id'           => $page_id,
            'title'        => $page->post_title,
            'content'      => $page->post_content,
            'permalink'    => get_permalink( $page_id ),
            'date'         => $page->post_date,
            'modified'     => $page->post_modified,
            'location_id'  => $location_id,
            'location'     => $location ? $location->post_title : '',
            'template'     => get_post_meta( $page_id, $this->meta_prefix . 'template', true ),
            'url_structure' => get_post_meta( $page_id, $this->meta_prefix . 'url_structure', true ),
            'parent_page'  => get_post_meta( $page_id, $this->meta_prefix . 'parent_page', true ),
            'elements'     => get_post_meta( $page_id, $this->meta_prefix . 'elements', true ),
        );

        return $page_data;
    }

    /**
     * Récupère les modèles de page disponibles.
     *
     * @since    1.2.0
     * @return   array    Les modèles de page disponibles.
     */
    public function get_available_templates() {
        $templates = array(
            'default' => array(
                'name'        => __( 'Modèle par défaut', 'boss-seo' ),
                'description' => __( 'Un modèle de page simple avec les informations de base de l\'emplacement.', 'boss-seo' ),
                'preview'     => plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/images/templates/default.jpg',
            ),
            'business' => array(
                'name'        => __( 'Modèle entreprise', 'boss-seo' ),
                'description' => __( 'Un modèle de page pour les entreprises avec une mise en page professionnelle.', 'boss-seo' ),
                'preview'     => plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/images/templates/business.jpg',
            ),
            'store' => array(
                'name'        => __( 'Modèle magasin', 'boss-seo' ),
                'description' => __( 'Un modèle de page pour les magasins avec une mise en avant des produits.', 'boss-seo' ),
                'preview'     => plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/images/templates/store.jpg',
            ),
            'restaurant' => array(
                'name'        => __( 'Modèle restaurant', 'boss-seo' ),
                'description' => __( 'Un modèle de page pour les restaurants avec une mise en avant du menu.', 'boss-seo' ),
                'preview'     => plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/images/templates/restaurant.jpg',
            ),
            'service' => array(
                'name'        => __( 'Modèle service', 'boss-seo' ),
                'description' => __( 'Un modèle de page pour les entreprises de services avec une mise en avant des prestations.', 'boss-seo' ),
                'preview'     => plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/images/templates/service.jpg',
            ),
            'custom' => array(
                'name'        => __( 'Modèle personnalisé', 'boss-seo' ),
                'description' => __( 'Un modèle de page vide que vous pouvez personnaliser selon vos besoins.', 'boss-seo' ),
                'preview'     => plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'admin/images/templates/custom.jpg',
            ),
        );

        return apply_filters( 'boss_seo_local_page_templates', $templates );
    }

    /**
     * Génère des pages locales pour les emplacements spécifiés.
     *
     * @since    1.2.0
     * @param    array    $location_ids    Les IDs des emplacements.
     * @param    array    $options         Les options de génération.
     * @return   array                     Les résultats de la génération.
     */
    public function generate_pages( $location_ids, $options ) {
        $results = array(
            'success' => array(),
            'errors'  => array(),
            'total'   => count( $location_ids ),
        );

        foreach ( $location_ids as $location_id ) {
            $location = get_post( $location_id );

            if ( ! $location || $location->post_type !== 'boss_location' ) {
                $results['errors'][] = array(
                    'location_id' => $location_id,
                    'message'     => __( 'Emplacement non trouvé.', 'boss-seo' ),
                );
                continue;
            }

            // Vérifier si une page existe déjà pour cet emplacement
            $existing_page_id = $this->get_page_by_location_id( $location_id );
            if ( $existing_page_id ) {
                $results['errors'][] = array(
                    'location_id' => $location_id,
                    'message'     => __( 'Une page existe déjà pour cet emplacement.', 'boss-seo' ),
                );
                continue;
            }

            // Récupérer les métadonnées de l'emplacement
            $location_meta = $this->get_location_meta( $location_id );

            // Générer le titre de la page
            $page_title = $this->generate_page_title( $location, $location_meta );

            // Générer le slug de la page
            $page_slug = $this->generate_page_slug( $location, $location_meta, $options['url_structure'] );

            // Générer le contenu de la page
            $page_content = $this->generate_page_content( $location, $location_meta, $options );

            // Créer la page
            $page_data = array(
                'post_title'   => $page_title,
                'post_name'    => $page_slug,
                'post_content' => $page_content,
                'post_status'  => 'publish',
                'post_type'    => 'page',
                'post_parent'  => isset( $options['parent_page'] ) ? absint( $options['parent_page'] ) : 0,
            );

            $page_id = wp_insert_post( $page_data );

            if ( is_wp_error( $page_id ) ) {
                $results['errors'][] = array(
                    'location_id' => $location_id,
                    'message'     => $page_id->get_error_message(),
                );
                continue;
            }

            // Enregistrer les métadonnées de la page
            update_post_meta( $page_id, $this->meta_prefix . 'location_id', $location_id );
            update_post_meta( $page_id, $this->meta_prefix . 'template', $options['template'] );
            update_post_meta( $page_id, $this->meta_prefix . 'url_structure', $options['url_structure'] );
            update_post_meta( $page_id, $this->meta_prefix . 'parent_page', $options['parent_page'] );
            update_post_meta( $page_id, $this->meta_prefix . 'elements', $options['elements'] );
            update_post_meta( $page_id, $this->meta_prefix . 'generated_at', current_time( 'mysql' ) );

            $results['success'][] = array(
                'location_id' => $location_id,
                'page_id'     => $page_id,
                'page_title'  => $page_title,
                'page_url'    => get_permalink( $page_id ),
                'message'     => __( 'Page générée avec succès.', 'boss-seo' ),
            );
        }

        return $results;
    }

    /**
     * Régénère une page locale.
     *
     * @since    1.2.0
     * @param    int      $page_id       L'ID de la page.
     * @param    int      $location_id   L'ID de l'emplacement.
     * @param    array    $options       Les options de génération.
     * @return   bool|WP_Error           True en cas de succès, WP_Error en cas d'erreur.
     */
    public function regenerate_page( $page_id, $location_id, $options ) {
        $location = get_post( $location_id );

        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return new WP_Error( 'location_not_found', __( 'Emplacement non trouvé.', 'boss-seo' ) );
        }

        // Récupérer les métadonnées de l'emplacement
        $location_meta = $this->get_location_meta( $location_id );

        // Générer le nouveau titre de la page
        $page_title = $this->generate_page_title( $location, $location_meta );

        // Générer le nouveau slug de la page
        $page_slug = $this->generate_page_slug( $location, $location_meta, $options['url_structure'] );

        // Générer le nouveau contenu de la page
        $page_content = $this->generate_page_content( $location, $location_meta, $options );

        // Mettre à jour la page
        $page_data = array(
            'ID'           => $page_id,
            'post_title'   => $page_title,
            'post_name'    => $page_slug,
            'post_content' => $page_content,
            'post_parent'  => isset( $options['parent_page'] ) ? absint( $options['parent_page'] ) : 0,
        );

        $result = wp_update_post( $page_data );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        // Mettre à jour les métadonnées de la page
        update_post_meta( $page_id, $this->meta_prefix . 'template', $options['template'] );
        update_post_meta( $page_id, $this->meta_prefix . 'url_structure', $options['url_structure'] );
        update_post_meta( $page_id, $this->meta_prefix . 'parent_page', $options['parent_page'] );
        update_post_meta( $page_id, $this->meta_prefix . 'elements', $options['elements'] );
        update_post_meta( $page_id, $this->meta_prefix . 'regenerated_at', current_time( 'mysql' ) );

        return true;
    }

    /**
     * Récupère les métadonnées d'un emplacement.
     *
     * @since    1.2.0
     * @param    int       $location_id    L'ID de l'emplacement.
     * @return   array                     Les métadonnées de l'emplacement.
     */
    private function get_location_meta( $location_id ) {
        $meta_prefix = 'boss_location_';

        $meta_fields = array(
            // Détails
            'status',
            'primary',
            'short_name',
            'description',

            // Adresse
            'street',
            'street2',
            'city',
            'state',
            'postal_code',
            'country',
            'latitude',
            'longitude',

            // Coordonnées
            'phone',
            'fax',
            'email',
            'website',

            // SEO
            'seo_title',
            'seo_description',
            'seo_keywords',
            'seo_schema_type',
            'seo_hide_in_sitemap',
        );

        $meta_data = array();

        foreach ( $meta_fields as $field ) {
            $meta_key = $meta_prefix . $field;
            $meta_data[$field] = get_post_meta( $location_id, $meta_key, true );
        }

        // Récupérer les horaires d'ouverture
        $meta_data['hours'] = get_post_meta( $location_id, $meta_prefix . 'hours', true );

        return $meta_data;
    }

    /**
     * Génère le titre d'une page locale.
     *
     * @since    1.2.0
     * @param    WP_Post   $location       L'objet emplacement.
     * @param    array     $location_meta  Les métadonnées de l'emplacement.
     * @return   string                    Le titre de la page.
     */
    private function generate_page_title( $location, $location_meta ) {
        $business_name = get_option( 'boss_business_name', get_bloginfo( 'name' ) );
        $city = isset( $location_meta['city'] ) ? $location_meta['city'] : '';

        if ( ! empty( $location_meta['short_name'] ) ) {
            return sprintf( '%s %s', $business_name, $location_meta['short_name'] );
        } elseif ( ! empty( $city ) ) {
            return sprintf( '%s %s', $business_name, $city );
        } else {
            return $location->post_title;
        }
    }

    /**
     * Génère le slug d'une page locale.
     *
     * @since    1.2.0
     * @param    WP_Post   $location       L'objet emplacement.
     * @param    array     $location_meta  Les métadonnées de l'emplacement.
     * @param    string    $url_structure  La structure d'URL à utiliser.
     * @return   string                    Le slug de la page.
     */
    private function generate_page_slug( $location, $location_meta, $url_structure ) {
        $business_name = sanitize_title( get_option( 'boss_business_name', get_bloginfo( 'name' ) ) );
        $city = isset( $location_meta['city'] ) ? sanitize_title( $location_meta['city'] ) : '';
        $state = isset( $location_meta['state'] ) ? sanitize_title( $location_meta['state'] ) : '';
        $postal_code = isset( $location_meta['postal_code'] ) ? sanitize_title( $location_meta['postal_code'] ) : '';

        switch ( $url_structure ) {
            case 'city':
                if ( ! empty( $city ) ) {
                    return $city;
                }
                break;

            case 'city-state':
                if ( ! empty( $city ) && ! empty( $state ) ) {
                    return $city . '-' . $state;
                } elseif ( ! empty( $city ) ) {
                    return $city;
                }
                break;

            case 'postal-code':
                if ( ! empty( $postal_code ) ) {
                    return $postal_code;
                }
                break;

            case 'business-city':
                if ( ! empty( $business_name ) && ! empty( $city ) ) {
                    return $business_name . '-' . $city;
                } elseif ( ! empty( $city ) ) {
                    return $city;
                }
                break;

            case 'location-name':
                return sanitize_title( $location->post_title );

            case 'custom':
                if ( ! empty( $location_meta['short_name'] ) ) {
                    return sanitize_title( $location_meta['short_name'] );
                }
                break;
        }

        // Fallback
        return sanitize_title( $location->post_title );
    }

    /**
     * Génère le contenu d'une page locale.
     *
     * @since    1.2.0
     * @param    WP_Post   $location       L'objet emplacement.
     * @param    array     $location_meta  Les métadonnées de l'emplacement.
     * @param    array     $options        Les options de génération.
     * @return   string                    Le contenu de la page.
     */
    private function generate_page_content( $location, $location_meta, $options ) {
        $template = isset( $options['template'] ) ? $options['template'] : 'default';
        $elements = isset( $options['elements'] ) ? $options['elements'] : array();

        // Récupérer le contenu du modèle
        $template_content = $this->get_template_content( $template );

        // Remplacer les variables
        $template_content = $this->replace_template_variables( $template_content, $location, $location_meta );

        // Ajouter les éléments
        $content = $template_content;

        if ( in_array( 'map', $elements ) ) {
            $content .= "\n\n" . '[boss_local_map location_id="' . $location->ID . '"]';
        }

        if ( in_array( 'hours', $elements ) ) {
            $content .= "\n\n" . '[boss_local_hours location_id="' . $location->ID . '"]';
        }

        if ( in_array( 'schema', $elements ) ) {
            $content .= "\n\n" . '[boss_local_schema location_id="' . $location->ID . '"]';
        }

        if ( in_array( 'info', $elements ) ) {
            $content .= "\n\n" . '[boss_local_info location_id="' . $location->ID . '"]';
        }

        return $content;
    }

    /**
     * Récupère le contenu d'un modèle de page.
     *
     * @since    1.2.0
     * @param    string    $template    Le nom du modèle.
     * @return   string                 Le contenu du modèle.
     */
    private function get_template_content( $template ) {
        $templates = array(
            'default' => "<!-- wp:heading {\"level\":1} -->\n<h1>{{business_name}} {{city}}</h1>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p>{{description}}</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:heading -->\n<h2>Coordonnées</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p><strong>Adresse :</strong> {{address}}<br><strong>Téléphone :</strong> {{phone}}<br><strong>Email :</strong> {{email}}<br><strong>Site web :</strong> {{website}}</p>\n<!-- /wp:paragraph -->",

            'business' => "<!-- wp:group {\"backgroundColor\":\"white\",\"className\":\"boss-local-header\"} -->\n<div class=\"wp-block-group boss-local-header has-white-background-color has-background\"><!-- wp:heading {\"level\":1,\"textAlign\":\"center\"} -->\n<h1 class=\"has-text-align-center\">{{business_name}} {{city}}</h1>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph {\"align\":\"center\"} -->\n<p class=\"has-text-align-center\">{{description}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:group -->\n\n<!-- wp:columns -->\n<div class=\"wp-block-columns\"><!-- wp:column -->\n<div class=\"wp-block-column\"><!-- wp:heading -->\n<h2>À propos de nous</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p>{{content}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:column -->\n\n<!-- wp:column -->\n<div class=\"wp-block-column\"><!-- wp:heading -->\n<h2>Coordonnées</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p><strong>Adresse :</strong> {{address}}<br><strong>Téléphone :</strong> {{phone}}<br><strong>Email :</strong> {{email}}<br><strong>Site web :</strong> {{website}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:column --></div>\n<!-- /wp:columns -->",

            'store' => "<!-- wp:cover {\"url\":\"{{featured_image}}\",\"dimRatio\":70,\"className\":\"boss-local-header\"} -->\n<div class=\"wp-block-cover boss-local-header\"><span aria-hidden=\"true\" class=\"wp-block-cover__background has-background-dim-70 has-background-dim\"></span><img class=\"wp-block-cover__image-background\" alt=\"\" src=\"{{featured_image}}\" data-object-fit=\"cover\"/><div class=\"wp-block-cover__inner-container\"><!-- wp:heading {\"level\":1,\"textAlign\":\"center\",\"textColor\":\"white\"} -->\n<h1 class=\"has-text-align-center has-white-color has-text-color\">{{business_name}} {{city}}</h1>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph {\"align\":\"center\",\"textColor\":\"white\"} -->\n<p class=\"has-text-align-center has-white-color has-text-color\">{{description}}</p>\n<!-- /wp:paragraph --></div></div>\n<!-- /wp:cover -->\n\n<!-- wp:columns -->\n<div class=\"wp-block-columns\"><!-- wp:column {\"width\":\"66.66%\"} -->\n<div class=\"wp-block-column\" style=\"flex-basis:66.66%\"><!-- wp:heading -->\n<h2>Notre magasin</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p>{{content}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:column -->\n\n<!-- wp:column {\"width\":\"33.33%\"} -->\n<div class=\"wp-block-column\" style=\"flex-basis:33.33%\"><!-- wp:heading -->\n<h2>Informations pratiques</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p><strong>Adresse :</strong> {{address}}<br><strong>Téléphone :</strong> {{phone}}<br><strong>Email :</strong> {{email}}<br><strong>Site web :</strong> {{website}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:column --></div>\n<!-- /wp:columns -->",

            'restaurant' => "<!-- wp:cover {\"url\":\"{{featured_image}}\",\"dimRatio\":60,\"className\":\"boss-local-header\"} -->\n<div class=\"wp-block-cover boss-local-header\"><span aria-hidden=\"true\" class=\"wp-block-cover__background has-background-dim-60 has-background-dim\"></span><img class=\"wp-block-cover__image-background\" alt=\"\" src=\"{{featured_image}}\" data-object-fit=\"cover\"/><div class=\"wp-block-cover__inner-container\"><!-- wp:heading {\"level\":1,\"textAlign\":\"center\",\"textColor\":\"white\"} -->\n<h1 class=\"has-text-align-center has-white-color has-text-color\">{{business_name}} {{city}}</h1>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph {\"align\":\"center\",\"textColor\":\"white\"} -->\n<p class=\"has-text-align-center has-white-color has-text-color\">{{description}}</p>\n<!-- /wp:paragraph --></div></div>\n<!-- /wp:cover -->\n\n<!-- wp:columns -->\n<div class=\"wp-block-columns\"><!-- wp:column {\"width\":\"66.66%\"} -->\n<div class=\"wp-block-column\" style=\"flex-basis:66.66%\"><!-- wp:heading -->\n<h2>Notre restaurant</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p>{{content}}</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:heading {\"level\":3} -->\n<h3>Menu</h3>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p>Découvrez notre menu savoureux préparé avec des ingrédients frais et locaux.</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:column -->\n\n<!-- wp:column {\"width\":\"33.33%\"} -->\n<div class=\"wp-block-column\" style=\"flex-basis:33.33%\"><!-- wp:heading -->\n<h2>Informations pratiques</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p><strong>Adresse :</strong> {{address}}<br><strong>Téléphone :</strong> {{phone}}<br><strong>Email :</strong> {{email}}<br><strong>Site web :</strong> {{website}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:column --></div>\n<!-- /wp:columns -->",

            'service' => "<!-- wp:group {\"backgroundColor\":\"white\",\"className\":\"boss-local-header\"} -->\n<div class=\"wp-block-group boss-local-header has-white-background-color has-background\"><!-- wp:heading {\"level\":1,\"textAlign\":\"center\"} -->\n<h1 class=\"has-text-align-center\">{{business_name}} {{city}}</h1>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph {\"align\":\"center\"} -->\n<p class=\"has-text-align-center\">{{description}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:group -->\n\n<!-- wp:columns -->\n<div class=\"wp-block-columns\"><!-- wp:column {\"width\":\"66.66%\"} -->\n<div class=\"wp-block-column\" style=\"flex-basis:66.66%\"><!-- wp:heading -->\n<h2>Nos services</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p>{{content}}</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:heading {\"level\":3} -->\n<h3>Pourquoi nous choisir ?</h3>\n<!-- /wp:heading -->\n\n<!-- wp:list -->\n<ul><li>Service professionnel et de qualité</li><li>Équipe expérimentée</li><li>Satisfaction client garantie</li><li>Prix compétitifs</li></ul>\n<!-- /wp:list --></div>\n<!-- /wp:column -->\n\n<!-- wp:column {\"width\":\"33.33%\"} -->\n<div class=\"wp-block-column\" style=\"flex-basis:33.33%\"><!-- wp:heading -->\n<h2>Contactez-nous</h2>\n<!-- /wp:heading -->\n\n<!-- wp:paragraph -->\n<p><strong>Adresse :</strong> {{address}}<br><strong>Téléphone :</strong> {{phone}}<br><strong>Email :</strong> {{email}}<br><strong>Site web :</strong> {{website}}</p>\n<!-- /wp:paragraph --></div>\n<!-- /wp:column --></div>\n<!-- /wp:columns -->",

            'custom' => "<!-- wp:paragraph -->\n<p>Ceci est un modèle personnalisé. Vous pouvez le modifier selon vos besoins.</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:paragraph -->\n<p>Informations sur l'emplacement :</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:list -->\n<ul><li>Nom : {{business_name}} {{city}}</li><li>Adresse : {{address}}</li><li>Téléphone : {{phone}}</li><li>Email : {{email}}</li><li>Site web : {{website}}</li></ul>\n<!-- /wp:list -->",
        );

        if ( isset( $templates[$template] ) ) {
            return $templates[$template];
        }

        return $templates['default'];
    }

    /**
     * Remplace les variables dans un modèle de page.
     *
     * @since    1.2.0
     * @param    string    $content        Le contenu du modèle.
     * @param    WP_Post   $location       L'objet emplacement.
     * @param    array     $location_meta  Les métadonnées de l'emplacement.
     * @return   string                    Le contenu avec les variables remplacées.
     */
    private function replace_template_variables( $content, $location, $location_meta ) {
        $business_name = get_option( 'boss_business_name', get_bloginfo( 'name' ) );

        // Construire l'adresse formatée
        $address_parts = array();
        if ( ! empty( $location_meta['street'] ) ) $address_parts[] = $location_meta['street'];
        if ( ! empty( $location_meta['street2'] ) ) $address_parts[] = $location_meta['street2'];
        if ( ! empty( $location_meta['city'] ) ) $address_parts[] = $location_meta['city'];
        if ( ! empty( $location_meta['state'] ) ) $address_parts[] = $location_meta['state'];
        if ( ! empty( $location_meta['postal_code'] ) ) $address_parts[] = $location_meta['postal_code'];
        if ( ! empty( $location_meta['country'] ) ) $address_parts[] = $location_meta['country'];
        $address = implode( ', ', $address_parts );

        // Récupérer l'image mise en avant
        $featured_image = '';
        if ( has_post_thumbnail( $location->ID ) ) {
            $featured_image = get_the_post_thumbnail_url( $location->ID, 'full' );
        }

        // Variables à remplacer
        $variables = array(
            '{{business_name}}' => $business_name,
            '{{location_name}}' => $location->post_title,
            '{{short_name}}' => isset( $location_meta['short_name'] ) ? $location_meta['short_name'] : '',
            '{{description}}' => isset( $location_meta['description'] ) ? $location_meta['description'] : '',
            '{{content}}' => $location->post_content,
            '{{street}}' => isset( $location_meta['street'] ) ? $location_meta['street'] : '',
            '{{street2}}' => isset( $location_meta['street2'] ) ? $location_meta['street2'] : '',
            '{{city}}' => isset( $location_meta['city'] ) ? $location_meta['city'] : '',
            '{{state}}' => isset( $location_meta['state'] ) ? $location_meta['state'] : '',
            '{{postal_code}}' => isset( $location_meta['postal_code'] ) ? $location_meta['postal_code'] : '',
            '{{country}}' => isset( $location_meta['country'] ) ? $location_meta['country'] : '',
            '{{address}}' => $address,
            '{{phone}}' => isset( $location_meta['phone'] ) ? $location_meta['phone'] : '',
            '{{fax}}' => isset( $location_meta['fax'] ) ? $location_meta['fax'] : '',
            '{{email}}' => isset( $location_meta['email'] ) ? $location_meta['email'] : '',
            '{{website}}' => isset( $location_meta['website'] ) ? $location_meta['website'] : '',
            '{{featured_image}}' => $featured_image,
        );

        // Remplacer les variables
        foreach ( $variables as $variable => $value ) {
            $content = str_replace( $variable, $value, $content );
        }

        return $content;
    }

    /**
     * Shortcode pour afficher les informations d'un emplacement.
     *
     * @since    1.2.0
     * @param    array     $atts    Les attributs du shortcode.
     * @return   string             Le contenu du shortcode.
     */
    public function shortcode_local_info( $atts ) {
        $atts = shortcode_atts( array(
            'location_id' => 0,
            'show_name' => 'yes',
            'show_address' => 'yes',
            'show_contact' => 'yes',
            'show_description' => 'yes',
            'layout' => 'default',
        ), $atts, 'boss_local_info' );

        $location_id = absint( $atts['location_id'] );

        // Si l'ID de l'emplacement n'est pas spécifié, essayer de le récupérer depuis la page
        if ( ! $location_id && is_page() ) {
            $location_id = get_post_meta( get_the_ID(), $this->meta_prefix . 'location_id', true );
        }

        if ( ! $location_id ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : ID d\'emplacement non spécifié.', 'boss-seo' ) . '</p>';
        }

        $location = get_post( $location_id );

        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : Emplacement non trouvé.', 'boss-seo' ) . '</p>';
        }

        $location_meta = $this->get_location_meta( $location_id );

        // Construire l'adresse formatée
        $address_parts = array();
        if ( ! empty( $location_meta['street'] ) ) $address_parts[] = $location_meta['street'];
        if ( ! empty( $location_meta['street2'] ) ) $address_parts[] = $location_meta['street2'];
        if ( ! empty( $location_meta['city'] ) ) $address_parts[] = $location_meta['city'];
        if ( ! empty( $location_meta['state'] ) ) $address_parts[] = $location_meta['state'];
        if ( ! empty( $location_meta['postal_code'] ) ) $address_parts[] = $location_meta['postal_code'];
        if ( ! empty( $location_meta['country'] ) ) $address_parts[] = $location_meta['country'];
        $address = implode( ', ', $address_parts );

        // Construire le HTML
        $html = '<div class="boss-local-info boss-local-info-' . esc_attr( $atts['layout'] ) . '">';

        if ( $atts['show_name'] === 'yes' ) {
            $html .= '<h3 class="boss-local-info-name">' . esc_html( $location->post_title ) . '</h3>';
        }

        if ( $atts['show_description'] === 'yes' && ! empty( $location_meta['description'] ) ) {
            $html .= '<div class="boss-local-info-description">' . wp_kses_post( $location_meta['description'] ) . '</div>';
        }

        if ( $atts['show_address'] === 'yes' && ! empty( $address ) ) {
            $html .= '<div class="boss-local-info-address">';
            $html .= '<strong>' . __( 'Adresse :', 'boss-seo' ) . '</strong> ' . esc_html( $address );
            $html .= '</div>';
        }

        if ( $atts['show_contact'] === 'yes' ) {
            $html .= '<div class="boss-local-info-contact">';

            if ( ! empty( $location_meta['phone'] ) ) {
                $html .= '<div class="boss-local-info-phone">';
                $html .= '<strong>' . __( 'Téléphone :', 'boss-seo' ) . '</strong> ';
                $html .= '<a href="tel:' . esc_attr( $location_meta['phone'] ) . '">' . esc_html( $location_meta['phone'] ) . '</a>';
                $html .= '</div>';
            }

            if ( ! empty( $location_meta['email'] ) ) {
                $html .= '<div class="boss-local-info-email">';
                $html .= '<strong>' . __( 'Email :', 'boss-seo' ) . '</strong> ';
                $html .= '<a href="mailto:' . esc_attr( $location_meta['email'] ) . '">' . esc_html( $location_meta['email'] ) . '</a>';
                $html .= '</div>';
            }

            if ( ! empty( $location_meta['website'] ) ) {
                $html .= '<div class="boss-local-info-website">';
                $html .= '<strong>' . __( 'Site web :', 'boss-seo' ) . '</strong> ';
                $html .= '<a href="' . esc_url( $location_meta['website'] ) . '" target="_blank">' . esc_html( $location_meta['website'] ) . '</a>';
                $html .= '</div>';
            }

            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Shortcode pour afficher une carte d'un emplacement.
     *
     * @since    1.2.0
     * @param    array     $atts    Les attributs du shortcode.
     * @return   string             Le contenu du shortcode.
     */
    public function shortcode_local_map( $atts ) {
        $atts = shortcode_atts( array(
            'location_id' => 0,
            'width' => '100%',
            'height' => '400px',
            'zoom' => 15,
            'show_marker' => 'yes',
            'marker_title' => '',
        ), $atts, 'boss_local_map' );

        $location_id = absint( $atts['location_id'] );

        // Si l'ID de l'emplacement n'est pas spécifié, essayer de le récupérer depuis la page
        if ( ! $location_id && is_page() ) {
            $location_id = get_post_meta( get_the_ID(), $this->meta_prefix . 'location_id', true );
        }

        if ( ! $location_id ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : ID d\'emplacement non spécifié.', 'boss-seo' ) . '</p>';
        }

        $location = get_post( $location_id );

        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : Emplacement non trouvé.', 'boss-seo' ) . '</p>';
        }

        $location_meta = $this->get_location_meta( $location_id );

        if ( empty( $location_meta['latitude'] ) || empty( $location_meta['longitude'] ) ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : Coordonnées GPS non définies pour cet emplacement.', 'boss-seo' ) . '</p>';
        }

        $latitude = $location_meta['latitude'];
        $longitude = $location_meta['longitude'];
        $zoom = absint( $atts['zoom'] );
        $show_marker = $atts['show_marker'] === 'yes';
        $marker_title = ! empty( $atts['marker_title'] ) ? $atts['marker_title'] : $location->post_title;

        // Générer un ID unique pour la carte
        $map_id = 'boss-local-map-' . uniqid();

        // Enqueue les scripts et styles nécessaires
        wp_enqueue_style( 'leaflet', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css', array(), '1.7.1' );
        wp_enqueue_script( 'leaflet', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js', array(), '1.7.1', true );

        // Construire le HTML
        $html = '<div id="' . esc_attr( $map_id ) . '" class="boss-local-map" style="width: ' . esc_attr( $atts['width'] ) . '; height: ' . esc_attr( $atts['height'] ) . ';"></div>';

        // Ajouter le script pour initialiser la carte
        $html .= '<script>
            jQuery(document).ready(function($) {
                var map = L.map("' . esc_js( $map_id ) . '").setView([' . esc_js( $latitude ) . ', ' . esc_js( $longitude ) . '], ' . esc_js( $zoom ) . ');

                L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
                    attribution: \'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors\'
                }).addTo(map);

                ' . ( $show_marker ? 'L.marker([' . esc_js( $latitude ) . ', ' . esc_js( $longitude ) . ']).addTo(map).bindPopup("' . esc_js( $marker_title ) . '");' : '' ) . '
            });
        </script>';

        return $html;
    }

    /**
     * Shortcode pour afficher les horaires d'ouverture d'un emplacement.
     *
     * @since    1.2.0
     * @param    array     $atts    Les attributs du shortcode.
     * @return   string             Le contenu du shortcode.
     */
    public function shortcode_local_hours( $atts ) {
        $atts = shortcode_atts( array(
            'location_id' => 0,
            'show_title' => 'yes',
            'title' => __( 'Horaires d\'ouverture', 'boss-seo' ),
            'layout' => 'table',
        ), $atts, 'boss_local_hours' );

        $location_id = absint( $atts['location_id'] );

        // Si l'ID de l'emplacement n'est pas spécifié, essayer de le récupérer depuis la page
        if ( ! $location_id && is_page() ) {
            $location_id = get_post_meta( get_the_ID(), $this->meta_prefix . 'location_id', true );
        }

        if ( ! $location_id ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : ID d\'emplacement non spécifié.', 'boss-seo' ) . '</p>';
        }

        $location = get_post( $location_id );

        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : Emplacement non trouvé.', 'boss-seo' ) . '</p>';
        }

        $location_meta = $this->get_location_meta( $location_id );

        if ( empty( $location_meta['hours'] ) || ! is_array( $location_meta['hours'] ) ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : Horaires d\'ouverture non définis pour cet emplacement.', 'boss-seo' ) . '</p>';
        }

        $hours = $location_meta['hours'];

        // Jours de la semaine
        $days = array(
            'monday'    => __( 'Lundi', 'boss-seo' ),
            'tuesday'   => __( 'Mardi', 'boss-seo' ),
            'wednesday' => __( 'Mercredi', 'boss-seo' ),
            'thursday'  => __( 'Jeudi', 'boss-seo' ),
            'friday'    => __( 'Vendredi', 'boss-seo' ),
            'saturday'  => __( 'Samedi', 'boss-seo' ),
            'sunday'    => __( 'Dimanche', 'boss-seo' ),
        );

        // Construire le HTML
        $html = '<div class="boss-local-hours boss-local-hours-' . esc_attr( $atts['layout'] ) . '">';

        if ( $atts['show_title'] === 'yes' && ! empty( $atts['title'] ) ) {
            $html .= '<h3 class="boss-local-hours-title">' . esc_html( $atts['title'] ) . '</h3>';
        }

        if ( $atts['layout'] === 'table' ) {
            $html .= '<table class="boss-local-hours-table">';
            $html .= '<thead><tr><th>' . __( 'Jour', 'boss-seo' ) . '</th><th>' . __( 'Horaires', 'boss-seo' ) . '</th></tr></thead>';
            $html .= '<tbody>';

            foreach ( $days as $day_key => $day_label ) {
                $html .= '<tr>';
                $html .= '<td>' . esc_html( $day_label ) . '</td>';
                $html .= '<td>';

                if ( isset( $hours[$day_key] ) ) {
                    $day_hours = $hours[$day_key];

                    if ( $day_hours['status'] === 'open' && ! empty( $day_hours['hours'] ) ) {
                        $slots = array();

                        foreach ( $day_hours['hours'] as $slot ) {
                            $slots[] = esc_html( $slot['open'] ) . ' - ' . esc_html( $slot['close'] );
                        }

                        $html .= implode( ', ', $slots );
                    } elseif ( $day_hours['status'] === 'closed' ) {
                        $html .= __( 'Fermé', 'boss-seo' );
                    } elseif ( $day_hours['status'] === 'appointment' ) {
                        $html .= __( 'Sur rendez-vous', 'boss-seo' );
                    }
                } else {
                    $html .= __( 'Non défini', 'boss-seo' );
                }

                $html .= '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
        } else {
            $html .= '<ul class="boss-local-hours-list">';

            foreach ( $days as $day_key => $day_label ) {
                $html .= '<li class="boss-local-hours-day">';
                $html .= '<span class="boss-local-hours-day-label">' . esc_html( $day_label ) . '</span>';
                $html .= '<span class="boss-local-hours-day-hours">';

                if ( isset( $hours[$day_key] ) ) {
                    $day_hours = $hours[$day_key];

                    if ( $day_hours['status'] === 'open' && ! empty( $day_hours['hours'] ) ) {
                        $slots = array();

                        foreach ( $day_hours['hours'] as $slot ) {
                            $slots[] = esc_html( $slot['open'] ) . ' - ' . esc_html( $slot['close'] );
                        }

                        $html .= implode( ', ', $slots );
                    } elseif ( $day_hours['status'] === 'closed' ) {
                        $html .= __( 'Fermé', 'boss-seo' );
                    } elseif ( $day_hours['status'] === 'appointment' ) {
                        $html .= __( 'Sur rendez-vous', 'boss-seo' );
                    }
                } else {
                    $html .= __( 'Non défini', 'boss-seo' );
                }

                $html .= '</span>';
                $html .= '</li>';
            }

            $html .= '</ul>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Shortcode pour afficher le schéma structuré d'un emplacement.
     *
     * @since    1.2.0
     * @param    array     $atts    Les attributs du shortcode.
     * @return   string             Le contenu du shortcode.
     */
    public function shortcode_local_schema( $atts ) {
        $atts = shortcode_atts( array(
            'location_id' => 0,
            'show_code' => 'no',
        ), $atts, 'boss_local_schema' );

        $location_id = absint( $atts['location_id'] );

        // Si l'ID de l'emplacement n'est pas spécifié, essayer de le récupérer depuis la page
        if ( ! $location_id && is_page() ) {
            $location_id = get_post_meta( get_the_ID(), $this->meta_prefix . 'location_id', true );
        }

        if ( ! $location_id ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : ID d\'emplacement non spécifié.', 'boss-seo' ) . '</p>';
        }

        $location = get_post( $location_id );

        if ( ! $location || $location->post_type !== 'boss_location' ) {
            return '<p class="boss-seo-error">' . __( 'Erreur : Emplacement non trouvé.', 'boss-seo' ) . '</p>';
        }

        $location_meta = $this->get_location_meta( $location_id );

        // Récupérer le schéma
        $schema = $this->generate_location_schema( $location, $location_meta );

        // Ajouter le schéma au head
        add_action( 'wp_head', function() use ( $schema ) {
            echo '<script type="application/ld+json">' . wp_json_encode( $schema ) . '</script>';
        } );

        // Si on ne veut pas afficher le code, on retourne une chaîne vide
        if ( $atts['show_code'] !== 'yes' ) {
            return '';
        }

        // Sinon, on affiche le code
        $html = '<div class="boss-local-schema">';
        $html .= '<pre>' . esc_html( wp_json_encode( $schema, JSON_PRETTY_PRINT ) ) . '</pre>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Génère le schéma structuré pour un emplacement.
     *
     * @since    1.2.0
     * @param    WP_Post   $location       L'objet emplacement.
     * @param    array     $location_meta  Les métadonnées de l'emplacement.
     * @return   array                     Le schéma structuré.
     */
    private function generate_location_schema( $location, $location_meta ) {
        $business_name = get_option( 'boss_business_name', get_bloginfo( 'name' ) );
        $schema_type = ! empty( $location_meta['seo_schema_type'] ) ? $location_meta['seo_schema_type'] : 'LocalBusiness';

        // Construire l'adresse formatée
        $address_parts = array();
        if ( ! empty( $location_meta['street'] ) ) $address_parts[] = $location_meta['street'];
        if ( ! empty( $location_meta['street2'] ) ) $address_parts[] = $location_meta['street2'];
        if ( ! empty( $location_meta['city'] ) ) $address_parts[] = $location_meta['city'];
        if ( ! empty( $location_meta['state'] ) ) $address_parts[] = $location_meta['state'];
        if ( ! empty( $location_meta['postal_code'] ) ) $address_parts[] = $location_meta['postal_code'];
        if ( ! empty( $location_meta['country'] ) ) $address_parts[] = $location_meta['country'];
        $address = implode( ', ', $address_parts );

        // Récupérer l'image mise en avant
        $image = '';
        if ( has_post_thumbnail( $location->ID ) ) {
            $image = get_the_post_thumbnail_url( $location->ID, 'full' );
        }

        // Construire le schéma
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => $schema_type,
            'name' => $location->post_title,
            'description' => ! empty( $location_meta['description'] ) ? $location_meta['description'] : '',
            'url' => get_permalink( $location->ID ),
            'telephone' => ! empty( $location_meta['phone'] ) ? $location_meta['phone'] : '',
            'email' => ! empty( $location_meta['email'] ) ? $location_meta['email'] : '',
            'address' => array(
                '@type' => 'PostalAddress',
                'streetAddress' => ! empty( $location_meta['street'] ) ? $location_meta['street'] . ( ! empty( $location_meta['street2'] ) ? ', ' . $location_meta['street2'] : '' ) : '',
                'addressLocality' => ! empty( $location_meta['city'] ) ? $location_meta['city'] : '',
                'addressRegion' => ! empty( $location_meta['state'] ) ? $location_meta['state'] : '',
                'postalCode' => ! empty( $location_meta['postal_code'] ) ? $location_meta['postal_code'] : '',
                'addressCountry' => ! empty( $location_meta['country'] ) ? $location_meta['country'] : '',
            ),
        );

        // Ajouter les coordonnées géographiques
        if ( ! empty( $location_meta['latitude'] ) && ! empty( $location_meta['longitude'] ) ) {
            $schema['geo'] = array(
                '@type' => 'GeoCoordinates',
                'latitude' => $location_meta['latitude'],
                'longitude' => $location_meta['longitude'],
            );
        }

        // Ajouter l'image
        if ( ! empty( $image ) ) {
            $schema['image'] = $image;
        }

        // Ajouter les horaires d'ouverture
        if ( ! empty( $location_meta['hours'] ) && is_array( $location_meta['hours'] ) ) {
            $opening_hours = array();
            $days_map = array(
                'monday' => 'Mo',
                'tuesday' => 'Tu',
                'wednesday' => 'We',
                'thursday' => 'Th',
                'friday' => 'Fr',
                'saturday' => 'Sa',
                'sunday' => 'Su',
            );

            foreach ( $location_meta['hours'] as $day_key => $day_hours ) {
                if ( $day_hours['status'] === 'open' && ! empty( $day_hours['hours'] ) ) {
                    foreach ( $day_hours['hours'] as $slot ) {
                        $opening_hours[] = array(
                            '@type' => 'OpeningHoursSpecification',
                            'dayOfWeek' => $days_map[$day_key],
                            'opens' => $slot['open'],
                            'closes' => $slot['close'],
                        );
                    }
                }
            }

            if ( ! empty( $opening_hours ) ) {
                $schema['openingHoursSpecification'] = $opening_hours;
            }
        }

        return $schema;
    }
}