"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";

// src/tooltip.ts
var _tooltipstore = require('@ariakit/react-core/tooltip/tooltip-store');
var _tooltipcontext = require('@ariakit/react-core/tooltip/tooltip-context');
var _tooltip = require('@ariakit/react-core/tooltip/tooltip');
var _tooltipprovider = require('@ariakit/react-core/tooltip/tooltip-provider');
var _tooltipanchor = require('@ariakit/react-core/tooltip/tooltip-anchor');
var _tooltiparrow = require('@ariakit/react-core/tooltip/tooltip-arrow');








exports.useTooltipStore = _tooltipstore.useTooltipStore; exports.useTooltipContext = _tooltipcontext.useTooltipContext; exports.Tooltip = _tooltip.Tooltip; exports.TooltipProvider = _tooltipprovider.TooltipProvider; exports.TooltipAnchor = _tooltipanchor.TooltipAnchor; exports.TooltipArrow = _tooltiparrow.TooltipArrow;
