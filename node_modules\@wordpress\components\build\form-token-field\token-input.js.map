{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "UnForwardedTokenInput", "props", "ref", "value", "isExpanded", "instanceId", "selectedSuggestionIndex", "className", "onChange", "onFocus", "onBlur", "restProps", "hasFocus", "setHasFocus", "useState", "size", "length", "onChangeHandler", "event", "target", "onFocusHandler", "e", "onBlurHandler", "_react", "createElement", "id", "type", "classnames", "autoComplete", "role", "undefined", "TokenInput", "forwardRef", "exports", "_default", "default"], "sources": ["@wordpress/components/src/form-token-field/token-input.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { ChangeEvent, ForwardedRef, FocusEventHandler } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef, useState } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport type { TokenInputProps } from './types';\n\nexport function UnForwardedTokenInput(\n\tprops: WordPressComponentProps< TokenInputProps, 'input', false >,\n\tref: ForwardedRef< HTMLInputElement >\n) {\n\tconst {\n\t\tvalue,\n\t\tisExpanded,\n\t\tinstanceId,\n\t\tselectedSuggestionIndex,\n\t\tclassName,\n\t\tonChange,\n\t\tonFocus,\n\t\tonBlur,\n\t\t...restProps\n\t} = props;\n\n\tconst [ hasFocus, setHasFocus ] = useState( false );\n\n\tconst size = value ? value.length + 1 : 0;\n\n\tconst onChangeHandler = ( event: ChangeEvent< HTMLInputElement > ) => {\n\t\tif ( onChange ) {\n\t\t\tonChange( {\n\t\t\t\tvalue: event.target.value,\n\t\t\t} );\n\t\t}\n\t};\n\n\tconst onFocusHandler: FocusEventHandler< HTMLInputElement > = ( e ) => {\n\t\tsetHasFocus( true );\n\t\tonFocus?.( e );\n\t};\n\n\tconst onBlurHandler: FocusEventHandler< HTMLInputElement > = ( e ) => {\n\t\tsetHasFocus( false );\n\t\tonBlur?.( e );\n\t};\n\n\treturn (\n\t\t<input\n\t\t\tref={ ref }\n\t\t\tid={ `components-form-token-input-${ instanceId }` }\n\t\t\ttype=\"text\"\n\t\t\t{ ...restProps }\n\t\t\tvalue={ value || '' }\n\t\t\tonChange={ onChangeHandler }\n\t\t\tonFocus={ onFocusHandler }\n\t\t\tonBlur={ onBlurHandler }\n\t\t\tsize={ size }\n\t\t\tclassName={ classnames(\n\t\t\t\tclassName,\n\t\t\t\t'components-form-token-field__input'\n\t\t\t) }\n\t\t\tautoComplete=\"off\"\n\t\t\trole=\"combobox\"\n\t\t\taria-expanded={ isExpanded }\n\t\t\taria-autocomplete=\"list\"\n\t\t\taria-owns={\n\t\t\t\tisExpanded\n\t\t\t\t\t? `components-form-token-suggestions-${ instanceId }`\n\t\t\t\t\t: undefined\n\t\t\t}\n\t\t\taria-activedescendant={\n\t\t\t\t// Only add the `aria-activedescendant` attribute when:\n\t\t\t\t// - the user is actively interacting with the input (`hasFocus`)\n\t\t\t\t// - there is a selected suggestion (`selectedSuggestionIndex !== -1`)\n\t\t\t\t// - the list of suggestions are rendered in the DOM (`isExpanded`)\n\t\t\t\thasFocus && selectedSuggestionIndex !== -1 && isExpanded\n\t\t\t\t\t? `components-form-token-suggestions-${ instanceId }-${ selectedSuggestionIndex }`\n\t\t\t\t\t: undefined\n\t\t\t}\n\t\t\taria-describedby={ `components-form-token-suggestions-howto-${ instanceId }` }\n\t\t/>\n\t);\n}\n\nexport const TokenInput = forwardRef( UnForwardedTokenInput );\n\nexport default TokenInput;\n"], "mappings": ";;;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AASO,SAASE,qBAAqBA,CACpCC,KAAiE,EACjEC,GAAqC,EACpC;EACD,MAAM;IACLC,KAAK;IACLC,UAAU;IACVC,UAAU;IACVC,uBAAuB;IACvBC,SAAS;IACTC,QAAQ;IACRC,OAAO;IACPC,MAAM;IACN,GAAGC;EACJ,CAAC,GAAGV,KAAK;EAET,MAAM,CAAEW,QAAQ,EAAEC,WAAW,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAEnD,MAAMC,IAAI,GAAGZ,KAAK,GAAGA,KAAK,CAACa,MAAM,GAAG,CAAC,GAAG,CAAC;EAEzC,MAAMC,eAAe,GAAKC,KAAsC,IAAM;IACrE,IAAKV,QAAQ,EAAG;MACfA,QAAQ,CAAE;QACTL,KAAK,EAAEe,KAAK,CAACC,MAAM,CAAChB;MACrB,CAAE,CAAC;IACJ;EACD,CAAC;EAED,MAAMiB,cAAqD,GAAKC,CAAC,IAAM;IACtER,WAAW,CAAE,IAAK,CAAC;IACnBJ,OAAO,GAAIY,CAAE,CAAC;EACf,CAAC;EAED,MAAMC,aAAoD,GAAKD,CAAC,IAAM;IACrER,WAAW,CAAE,KAAM,CAAC;IACpBH,MAAM,GAAIW,CAAE,CAAC;EACd,CAAC;EAED,OACC,IAAAE,MAAA,CAAAC,aAAA;IACCtB,GAAG,EAAGA,GAAK;IACXuB,EAAE,EAAI,+BAA+BpB,UAAY,EAAG;IACpDqB,IAAI,EAAC,MAAM;IAAA,GACNf,SAAS;IACdR,KAAK,EAAGA,KAAK,IAAI,EAAI;IACrBK,QAAQ,EAAGS,eAAiB;IAC5BR,OAAO,EAAGW,cAAgB;IAC1BV,MAAM,EAAGY,aAAe;IACxBP,IAAI,EAAGA,IAAM;IACbR,SAAS,EAAG,IAAAoB,mBAAU,EACrBpB,SAAS,EACT,oCACD,CAAG;IACHqB,YAAY,EAAC,KAAK;IAClBC,IAAI,EAAC,UAAU;IACf,iBAAgBzB,UAAY;IAC5B,qBAAkB,MAAM;IACxB,aACCA,UAAU,GACN,qCAAqCC,UAAY,EAAC,GACnDyB,SACH;IACD;IACC;IACA;IACA;IACA;IACAlB,QAAQ,IAAIN,uBAAuB,KAAK,CAAC,CAAC,IAAIF,UAAU,GACpD,qCAAqCC,UAAY,IAAIC,uBAAyB,EAAC,GAChFwB,SACH;IACD,oBAAoB,2CAA2CzB,UAAY;EAAG,CAC9E,CAAC;AAEJ;AAEO,MAAM0B,UAAU,GAAG,IAAAC,mBAAU,EAAEhC,qBAAsB,CAAC;AAACiC,OAAA,CAAAF,UAAA,GAAAA,UAAA;AAAA,IAAAG,QAAA,GAE/CH,UAAU;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}