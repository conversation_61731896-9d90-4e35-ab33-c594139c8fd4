import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  CheckboxControl,
  SelectControl,
  TextControl,
  Dashicon,
  Modal
} from '@wordpress/components';

const RedirectionsList = ({ 
  redirections, 
  onEdit, 
  onDelete, 
  onBulkAction,
  searchQuery,
  setSearchQuery,
  filterType,
  setFilterType,
  filterStatus,
  setFilterStatus,
  selectedItems,
  setSelectedItems,
  selectAll,
  setSelectAll
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [redirectionToDelete, setRedirectionToDelete] = useState(null);
  
  // Fonction pour confirmer la suppression
  const confirmDelete = (redirection) => {
    setRedirectionToDelete(redirection);
    setShowDeleteConfirm(true);
  };
  
  // Fonction pour effectuer la suppression
  const handleDelete = () => {
    if (redirectionToDelete) {
      onDelete(redirectionToDelete.id);
      setShowDeleteConfirm(false);
      setRedirectionToDelete(null);
    }
  };
  
  // Fonction pour gérer la sélection d'un élément
  const handleItemSelection = (id) => {
    if (selectedItems.includes(id)) {
      setSelectedItems(selectedItems.filter(itemId => itemId !== id));
    } else {
      setSelectedItems([...selectedItems, id]);
    }
  };
  
  // Fonction pour obtenir le texte du type de redirection
  const getRedirectionTypeText = (type) => {
    switch (type) {
      case '301':
        return __('301 - Permanente', 'boss-seo');
      case '302':
        return __('302 - Temporaire', 'boss-seo');
      case '307':
        return __('307 - Temporaire', 'boss-seo');
      case '410':
        return __('410 - Contenu supprimé', 'boss-seo');
      case '451':
        return __('451 - Indisponible pour raisons légales', 'boss-seo');
      default:
        return type;
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du type
  const getTypeColorClass = (type) => {
    switch (type) {
      case '301':
        return 'boss-bg-green-100 boss-text-green-800';
      case '302':
      case '307':
        return 'boss-bg-blue-100 boss-text-blue-800';
      case '410':
        return 'boss-bg-red-100 boss-text-red-800';
      case '451':
        return 'boss-bg-yellow-100 boss-text-yellow-800';
      default:
        return 'boss-bg-gray-100 boss-text-gray-800';
    }
  };
  
  // Fonction pour obtenir le texte du statut
  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return __('Active', 'boss-seo');
      case 'inactive':
        return __('Inactive', 'boss-seo');
      default:
        return status;
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'active':
        return 'boss-text-green-600';
      case 'inactive':
        return 'boss-text-red-600';
      default:
        return 'boss-text-gray-600';
    }
  };
  
  // Filtrer les redirections
  const filteredRedirections = redirections.filter(redirection => {
    // Filtrer par type
    const matchesType = filterType === 'all' || redirection.type === filterType;
    
    // Filtrer par statut
    const matchesStatus = filterStatus === 'all' || redirection.status === filterStatus;
    
    // Filtrer par recherche
    const matchesSearch = searchQuery === '' || 
      redirection.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
      redirection.target.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesType && matchesStatus && matchesSearch;
  });

  return (
    <>
      <Card className="boss-mb-6">
        <CardHeader className="boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('Liste des redirections', 'boss-seo')}
            </h2>
            <div className="boss-flex boss-space-x-2">
              <Button
                isSecondary
                isSmall
                onClick={() => onBulkAction('activate')}
                disabled={selectedItems.length === 0}
              >
                {__('Activer', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                isSmall
                onClick={() => onBulkAction('deactivate')}
                disabled={selectedItems.length === 0}
              >
                {__('Désactiver', 'boss-seo')}
              </Button>
              <Button
                isDestructive
                isSmall
                onClick={() => onBulkAction('delete')}
                disabled={selectedItems.length === 0}
              >
                {__('Supprimer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody className="boss-p-0">
          <div className="boss-overflow-x-auto">
            <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
              <thead className="boss-bg-gray-50">
                <tr>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider boss-w-10">
                    <CheckboxControl
                      checked={selectAll}
                      onChange={setSelectAll}
                      label=""
                    />
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Source', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Destination', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Type', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Hits', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Statut', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Dernière utilisation', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-right boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Actions', 'boss-seo')}
                  </th>
                </tr>
              </thead>
              <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                {filteredRedirections.length === 0 ? (
                  <tr>
                    <td colSpan="8" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                      {__('Aucune redirection trouvée.', 'boss-seo')}
                    </td>
                  </tr>
                ) : (
                  filteredRedirections.map(redirection => (
                    <tr key={redirection.id} className="boss-hover:boss-bg-gray-50">
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <CheckboxControl
                          checked={selectedItems.includes(redirection.id)}
                          onChange={() => handleItemSelection(redirection.id)}
                          label=""
                        />
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-font-medium boss-text-boss-dark">{redirection.source}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{redirection.target}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${getTypeColorClass(redirection.type)}`}>
                          {getRedirectionTypeText(redirection.type)}
                        </span>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{redirection.hits}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className={`boss-flex boss-items-center ${getStatusColorClass(redirection.status)}`}>
                          <span className={`boss-h-2.5 boss-w-2.5 boss-rounded-full boss-mr-2 ${
                            redirection.status === 'active' ? 'boss-bg-green-600' : 'boss-bg-red-600'
                          }`}></span>
                          {getStatusText(redirection.status)}
                        </div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{redirection.lastUsed || '-'}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap boss-text-right">
                        <div className="boss-flex boss-justify-end boss-space-x-2">
                          <Button
                            isSecondary
                            isSmall
                            onClick={() => onEdit(redirection)}
                          >
                            <Dashicon icon="edit" />
                          </Button>
                          <Button
                            isDestructive
                            isSmall
                            onClick={() => confirmDelete(redirection)}
                          >
                            <Dashicon icon="trash" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardBody>
        <CardFooter className="boss-border-t boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <div className="boss-text-boss-gray boss-text-sm">
              {filteredRedirections.length} {__('redirections trouvées', 'boss-seo')}
            </div>
            <div className="boss-text-boss-gray boss-text-sm">
              {selectedItems.length} {__('redirections sélectionnées', 'boss-seo')}
            </div>
          </div>
        </CardFooter>
      </Card>

      {/* Modal de confirmation de suppression */}
      {showDeleteConfirm && redirectionToDelete && (
        <Modal
          title={__('Confirmer la suppression', 'boss-seo')}
          onRequestClose={() => setShowDeleteConfirm(false)}
        >
          <div className="boss-p-6">
            <p className="boss-mb-6">
              {__('Êtes-vous sûr de vouloir supprimer la redirection de', 'boss-seo')} <strong>{redirectionToDelete.source}</strong> {__('vers', 'boss-seo')} <strong>{redirectionToDelete.target}</strong> ?
              {__('Cette action est irréversible.', 'boss-seo')}
            </p>
            
            <div className="boss-flex boss-justify-end boss-space-x-3">
              <Button
                isSecondary
                onClick={() => setShowDeleteConfirm(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                isDanger
                onClick={handleDelete}
              >
                {__('Supprimer', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default RedirectionsList;
