{"version": 3, "names": ["Ariakit", "_interopRequireWildcard", "require", "_element", "_i18n", "Styled", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "CustomSelectContext", "createContext", "undefined", "exports", "defaultRenderSelectedValue", "value", "isValueEmpty", "Array", "isArray", "length", "__", "sprintf", "CustomSelect", "children", "defaultValue", "label", "onChange", "size", "renderSelectedValue", "props", "store", "useSelectStore", "setValue", "nextValue", "currentValue", "useState", "computedRenderSelectedValue", "_react", "createElement", "Fragment", "CustomSelectLabel", "CustomSelectButton", "hasCustomRenderProp", "SelectArrow", "CustomSelectPopover", "gutter", "sameWidth", "Provider", "CustomSelectItem", "customSelectContext", "useContext", "SelectItemCheck"], "sources": ["@wordpress/components/src/custom-select-control-v2/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport * as Ariakit from '@ariakit/react';\n/**\n * WordPress dependencies\n */\nimport { createContext, useContext } from '@wordpress/element';\nimport { __, sprintf } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport * as Styled from './styles';\nimport type {\n\tCustomSelectProps,\n\tCustomSelectItemProps,\n\tCustomSelectContext as CustomSelectContextType,\n} from './types';\nimport type { WordPressComponentProps } from '../context';\n\nexport const CustomSelectContext =\n\tcreateContext< CustomSelectContextType >( undefined );\n\nfunction defaultRenderSelectedValue( value: CustomSelectProps[ 'value' ] ) {\n\tconst isValueEmpty = Array.isArray( value )\n\t\t? value.length === 0\n\t\t: value === undefined || value === null;\n\n\tif ( isValueEmpty ) {\n\t\treturn __( 'Select an item' );\n\t}\n\n\tif ( Array.isArray( value ) ) {\n\t\treturn value.length === 1\n\t\t\t? value[ 0 ]\n\t\t\t: // translators: %s: number of items selected (it will always be 2 or more items)\n\t\t\t  sprintf( __( '%s items selected' ), value.length );\n\t}\n\n\treturn value;\n}\n\nexport function CustomSelect( {\n\tchildren,\n\tdefaultValue,\n\tlabel,\n\tonChange,\n\tsize = 'default',\n\tvalue,\n\trenderSelectedValue,\n\t...props\n}: WordPressComponentProps< CustomSelectProps, 'button', false > ) {\n\tconst store = Ariakit.useSelectStore( {\n\t\tsetValue: ( nextValue ) => onChange?.( nextValue ),\n\t\tdefaultValue,\n\t\tvalue,\n\t} );\n\n\tconst { value: currentValue } = store.useState();\n\n\tconst computedRenderSelectedValue =\n\t\trenderSelectedValue ?? defaultRenderSelectedValue;\n\n\treturn (\n\t\t<>\n\t\t\t<Styled.CustomSelectLabel store={ store }>\n\t\t\t\t{ label }\n\t\t\t</Styled.CustomSelectLabel>\n\t\t\t<Styled.CustomSelectButton\n\t\t\t\t{ ...props }\n\t\t\t\tsize={ size }\n\t\t\t\thasCustomRenderProp={ !! renderSelectedValue }\n\t\t\t\tstore={ store }\n\t\t\t>\n\t\t\t\t{ computedRenderSelectedValue( currentValue ) }\n\t\t\t\t<Ariakit.SelectArrow />\n\t\t\t</Styled.CustomSelectButton>\n\t\t\t<Styled.CustomSelectPopover gutter={ 12 } store={ store } sameWidth>\n\t\t\t\t<CustomSelectContext.Provider value={ { store } }>\n\t\t\t\t\t{ children }\n\t\t\t\t</CustomSelectContext.Provider>\n\t\t\t</Styled.CustomSelectPopover>\n\t\t</>\n\t);\n}\n\nexport function CustomSelectItem( {\n\tchildren,\n\t...props\n}: WordPressComponentProps< CustomSelectItemProps, 'div', false > ) {\n\tconst customSelectContext = useContext( CustomSelectContext );\n\treturn (\n\t\t<Styled.CustomSelectItem\n\t\t\tstore={ customSelectContext?.store }\n\t\t\t{ ...props }\n\t\t>\n\t\t\t{ children ?? props.value }\n\t\t\t<Ariakit.SelectItemCheck />\n\t\t</Styled.CustomSelectItem>\n\t);\n}\n"], "mappings": ";;;;;;;;;AAIA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAIA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAKA,IAAAG,MAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAmC,SAAAI,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAN,wBAAAU,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAdnC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAIA;AACA;AACA;;AASO,MAAMW,mBAAmB,GAC/B,IAAAC,sBAAa,EAA6BC,SAAU,CAAC;AAACC,OAAA,CAAAH,mBAAA,GAAAA,mBAAA;AAEvD,SAASI,0BAA0BA,CAAEC,KAAmC,EAAG;EAC1E,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAEH,KAAM,CAAC,GACxCA,KAAK,CAACI,MAAM,KAAK,CAAC,GAClBJ,KAAK,KAAKH,SAAS,IAAIG,KAAK,KAAK,IAAI;EAExC,IAAKC,YAAY,EAAG;IACnB,OAAO,IAAAI,QAAE,EAAE,gBAAiB,CAAC;EAC9B;EAEA,IAAKH,KAAK,CAACC,OAAO,CAAEH,KAAM,CAAC,EAAG;IAC7B,OAAOA,KAAK,CAACI,MAAM,KAAK,CAAC,GACtBJ,KAAK,CAAE,CAAC,CAAE;IACV;IACA,IAAAM,aAAO,EAAE,IAAAD,QAAE,EAAE,mBAAoB,CAAC,EAAEL,KAAK,CAACI,MAAO,CAAC;EACtD;EAEA,OAAOJ,KAAK;AACb;AAEO,SAASO,YAAYA,CAAE;EAC7BC,QAAQ;EACRC,YAAY;EACZC,KAAK;EACLC,QAAQ;EACRC,IAAI,GAAG,SAAS;EAChBZ,KAAK;EACLa,mBAAmB;EACnB,GAAGC;AAC2D,CAAC,EAAG;EAClE,MAAMC,KAAK,GAAGhD,OAAO,CAACiD,cAAc,CAAE;IACrCC,QAAQ,EAAIC,SAAS,IAAMP,QAAQ,GAAIO,SAAU,CAAC;IAClDT,YAAY;IACZT;EACD,CAAE,CAAC;EAEH,MAAM;IAAEA,KAAK,EAAEmB;EAAa,CAAC,GAAGJ,KAAK,CAACK,QAAQ,CAAC,CAAC;EAEhD,MAAMC,2BAA2B,GAChCR,mBAAmB,aAAnBA,mBAAmB,cAAnBA,mBAAmB,GAAId,0BAA0B;EAElD,OACC,IAAAuB,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAACnD,MAAM,CAACqD,iBAAiB;IAACV,KAAK,EAAGA;EAAO,GACtCL,KACuB,CAAC,EAC3B,IAAAY,MAAA,CAAAC,aAAA,EAACnD,MAAM,CAACsD,kBAAkB;IAAA,GACpBZ,KAAK;IACVF,IAAI,EAAGA,IAAM;IACbe,mBAAmB,EAAG,CAAC,CAAEd,mBAAqB;IAC9CE,KAAK,EAAGA;EAAO,GAEbM,2BAA2B,CAAEF,YAAa,CAAC,EAC7C,IAAAG,MAAA,CAAAC,aAAA,EAACxD,OAAO,CAAC6D,WAAW,MAAE,CACI,CAAC,EAC5B,IAAAN,MAAA,CAAAC,aAAA,EAACnD,MAAM,CAACyD,mBAAmB;IAACC,MAAM,EAAG,EAAI;IAACf,KAAK,EAAGA,KAAO;IAACgB,SAAS;EAAA,GAClE,IAAAT,MAAA,CAAAC,aAAA,EAAC5B,mBAAmB,CAACqC,QAAQ;IAAChC,KAAK,EAAG;MAAEe;IAAM;EAAG,GAC9CP,QAC2B,CACH,CAC3B,CAAC;AAEL;AAEO,SAASyB,gBAAgBA,CAAE;EACjCzB,QAAQ;EACR,GAAGM;AAC4D,CAAC,EAAG;EACnE,MAAMoB,mBAAmB,GAAG,IAAAC,mBAAU,EAAExC,mBAAoB,CAAC;EAC7D,OACC,IAAA2B,MAAA,CAAAC,aAAA,EAACnD,MAAM,CAAC6D,gBAAgB;IACvBlB,KAAK,EAAGmB,mBAAmB,EAAEnB,KAAO;IAAA,GAC/BD;EAAK,GAERN,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAIM,KAAK,CAACd,KAAK,EACzB,IAAAsB,MAAA,CAAAC,aAAA,EAACxD,OAAO,CAACqE,eAAe,MAAE,CACF,CAAC;AAE5B"}