import { __ } from '@wordpress/i18n';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Dashicon 
} from '@wordpress/components';

/**
 * Composant pour la section Historique
 */
const HistorySection = ({ history }) => {
  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Fonction pour obtenir la classe de couleur en fonction du score
  const getScoreColorClass = (score) => {
    if (score >= 90) return 'boss-text-green-600';
    if (score >= 70) return 'boss-text-yellow-600';
    if (score >= 50) return 'boss-text-orange-600';
    return 'boss-text-red-600';
  };

  // Fonction pour obtenir la classe de fond en fonction du score
  const getScoreBgClass = (score) => {
    if (score >= 90) return 'boss-bg-green-100';
    if (score >= 70) return 'boss-bg-yellow-100';
    if (score >= 50) return 'boss-bg-orange-100';
    return 'boss-bg-red-100';
  };

  // Fonction pour obtenir le libellé d'évaluation en fonction du score
  const getScoreLabel = (score) => {
    if (score >= 90) return __('Excellent', 'boss-seo');
    if (score >= 70) return __('Bon', 'boss-seo');
    if (score >= 50) return __('Moyen', 'boss-seo');
    return __('À améliorer', 'boss-seo');
  };

  // Calculer la tendance entre la première et la dernière analyse
  const calculateTrend = () => {
    if (history.length < 2) return 0;
    
    const firstAnalysis = history[history.length - 1];
    const lastAnalysis = history[0];
    
    return lastAnalysis.score - firstAnalysis.score;
  };

  const trend = calculateTrend();

  return (
    <div>
      {/* En-tête avec résumé */}
      <Card className="boss-mb-6">
        <CardBody>
          <div className="boss-flex boss-flex-wrap boss-justify-between boss-items-center boss-gap-4">
            <div>
              <h3 className="boss-text-xl boss-font-bold boss-text-boss-dark boss-mb-1">
                {__('Historique des analyses', 'boss-seo')}
              </h3>
              <p className="boss-text-boss-gray">
                {history.length} {__('analyses effectuées', 'boss-seo')}
              </p>
            </div>
            
            {trend !== 0 && (
              <div className={`boss-flex boss-items-center boss-px-4 boss-py-2 boss-rounded-lg ${
                trend > 0 ? 'boss-bg-green-50' : 'boss-bg-red-50'
              }`}>
                <Dashicon 
                  icon={trend > 0 ? 'arrow-up-alt' : 'arrow-down-alt'} 
                  className={`boss-mr-2 ${trend > 0 ? 'boss-text-green-600' : 'boss-text-red-600'}`} 
                />
                <div>
                  <span className={`boss-font-bold ${trend > 0 ? 'boss-text-green-600' : 'boss-text-red-600'}`}>
                    {trend > 0 ? '+' : ''}{trend} {__('points', 'boss-seo')}
                  </span>
                  <span className="boss-text-boss-gray boss-ml-1 boss-text-sm">
                    {__('depuis la première analyse', 'boss-seo')}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardBody>
      </Card>
      
      {/* Graphique d'évolution */}
      <Card className="boss-mb-6">
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Évolution du score technique', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-relative boss-h-64 boss-w-full">
            {/* Lignes horizontales de référence */}
            <div className="boss-absolute boss-inset-0 boss-flex boss-flex-col boss-justify-between boss-pointer-events-none boss-z-0">
              <div className="boss-h-px boss-w-full boss-bg-gray-100 boss-relative">
                <span className="boss-absolute boss-right-0 boss-top-0 boss-transform boss-translate-y-[-50%] boss-text-xs boss-text-boss-gray">100</span>
              </div>
              <div className="boss-h-px boss-w-full boss-bg-gray-100 boss-relative">
                <span className="boss-absolute boss-right-0 boss-top-0 boss-transform boss-translate-y-[-50%] boss-text-xs boss-text-boss-gray">75</span>
              </div>
              <div className="boss-h-px boss-w-full boss-bg-gray-100 boss-relative">
                <span className="boss-absolute boss-right-0 boss-top-0 boss-transform boss-translate-y-[-50%] boss-text-xs boss-text-boss-gray">50</span>
              </div>
              <div className="boss-h-px boss-w-full boss-bg-gray-100 boss-relative">
                <span className="boss-absolute boss-right-0 boss-top-0 boss-transform boss-translate-y-[-50%] boss-text-xs boss-text-boss-gray">25</span>
              </div>
              <div className="boss-h-px boss-w-full boss-bg-gray-100 boss-relative">
                <span className="boss-absolute boss-right-0 boss-top-0 boss-transform boss-translate-y-[-50%] boss-text-xs boss-text-boss-gray">0</span>
              </div>
            </div>
            
            {/* Ligne d'évolution */}
            <svg className="boss-absolute boss-inset-0 boss-z-10" viewBox={`0 0 ${history.length - 1} 100`} preserveAspectRatio="none">
              <polyline
                points={history
                  .map((item, index) => `${history.length - 1 - index}, ${100 - item.score}`)
                  .join(' ')}
                fill="none"
                stroke="#4F46E5"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            
            {/* Points de données */}
            <div className="boss-absolute boss-inset-0 boss-flex boss-justify-between boss-items-end boss-z-20">
              {history.map((item, index) => (
                <div 
                  key={index} 
                  className="boss-relative boss-flex boss-flex-col boss-items-center boss-group"
                  style={{ height: '100%' }}
                >
                  <div 
                    className="boss-w-3 boss-h-3 boss-rounded-full boss-bg-white boss-border-2 boss-border-boss-primary boss-absolute boss-group-hover:boss-w-4 boss-group-hover:boss-h-4 boss-transition-all boss-duration-200"
                    style={{ bottom: `${item.score}%`, transform: 'translateX(-50%)' }}
                  ></div>
                  
                  {/* Info bulle au survol */}
                  <div 
                    className="boss-absolute boss-opacity-0 boss-group-hover:boss-opacity-100 boss-transition-opacity boss-duration-200 boss-pointer-events-none boss-z-30"
                    style={{ bottom: `${item.score + 5}%`, transform: 'translateX(-50%)' }}
                  >
                    <div className="boss-bg-boss-dark boss-text-white boss-rounded boss-py-1 boss-px-2 boss-text-xs boss-whitespace-nowrap">
                      <div className="boss-font-bold">{item.score}/100</div>
                      <div>{formatDate(item.date)}</div>
                    </div>
                    <div className="boss-w-2 boss-h-2 boss-bg-boss-dark boss-transform boss-rotate-45 boss-mx-auto boss-mt-[-4px]"></div>
                  </div>
                  
                  {/* Étiquette de date */}
                  <div className="boss-absolute boss-bottom-[-24px] boss-text-xs boss-text-boss-gray boss-transform boss-translate-x-[-50%]">
                    {new Date(item.date).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardBody>
      </Card>
      
      {/* Tableau des analyses */}
      <Card>
        <CardHeader>
          <h3 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Détail des analyses', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody className="boss-p-0">
          <table className="boss-w-full boss-border-collapse">
            <thead>
              <tr className="boss-bg-gray-50 boss-border-b boss-border-gray-200">
                <th className="boss-p-4 boss-text-left boss-font-medium boss-text-boss-gray">
                  {__('Date', 'boss-seo')}
                </th>
                <th className="boss-p-4 boss-text-center boss-font-medium boss-text-boss-gray">
                  {__('Score', 'boss-seo')}
                </th>
                <th className="boss-p-4 boss-text-center boss-font-medium boss-text-boss-gray">
                  {__('Critiques', 'boss-seo')}
                </th>
                <th className="boss-p-4 boss-text-center boss-font-medium boss-text-boss-gray">
                  {__('Erreurs', 'boss-seo')}
                </th>
                <th className="boss-p-4 boss-text-center boss-font-medium boss-text-boss-gray">
                  {__('Avertissements', 'boss-seo')}
                </th>
                <th className="boss-p-4 boss-text-center boss-font-medium boss-text-boss-gray">
                  {__('Améliorations', 'boss-seo')}
                </th>
                <th className="boss-p-4 boss-text-center boss-font-medium boss-text-boss-gray">
                  {__('Réussites', 'boss-seo')}
                </th>
                <th className="boss-p-4 boss-text-right boss-font-medium boss-text-boss-gray">
                  {__('Actions', 'boss-seo')}
                </th>
              </tr>
            </thead>
            <tbody>
              {history.map((item, index) => (
                <tr 
                  key={item.id} 
                  className={`boss-border-b boss-border-gray-200 ${
                    index === 0 ? 'boss-bg-blue-50' : 'boss-hover:boss-bg-gray-50'
                  }`}
                >
                  <td className="boss-p-4 boss-font-medium boss-text-boss-dark">
                    {formatDate(item.date)}
                    {index === 0 && (
                      <span className="boss-ml-2 boss-text-xs boss-font-medium boss-text-blue-600 boss-bg-blue-100 boss-px-2 boss-py-0.5 boss-rounded-full">
                        {__('Dernière', 'boss-seo')}
                      </span>
                    )}
                  </td>
                  <td className="boss-p-4 boss-text-center">
                    <span className={`boss-inline-flex boss-items-center boss-justify-center boss-w-10 boss-h-10 boss-rounded-full ${getScoreBgClass(item.score)} boss-font-medium ${getScoreColorClass(item.score)}`}>
                      {item.score}
                    </span>
                  </td>
                  <td className="boss-p-4 boss-text-center">
                    <span className={`boss-inline-flex boss-items-center boss-px-2.5 boss-py-0.5 boss-rounded-full boss-text-xs boss-font-medium ${
                      item.issues.critical > 0 ? 'boss-bg-red-100 boss-text-red-800' : 'boss-bg-gray-100 boss-text-gray-800'
                    }`}>
                      {item.issues.critical}
                    </span>
                  </td>
                  <td className="boss-p-4 boss-text-center">
                    <span className={`boss-inline-flex boss-items-center boss-px-2.5 boss-py-0.5 boss-rounded-full boss-text-xs boss-font-medium ${
                      item.issues.errors > 0 ? 'boss-bg-orange-100 boss-text-orange-800' : 'boss-bg-gray-100 boss-text-gray-800'
                    }`}>
                      {item.issues.errors}
                    </span>
                  </td>
                  <td className="boss-p-4 boss-text-center">
                    <span className={`boss-inline-flex boss-items-center boss-px-2.5 boss-py-0.5 boss-rounded-full boss-text-xs boss-font-medium ${
                      item.issues.warnings > 0 ? 'boss-bg-yellow-100 boss-text-yellow-800' : 'boss-bg-gray-100 boss-text-gray-800'
                    }`}>
                      {item.issues.warnings}
                    </span>
                  </td>
                  <td className="boss-p-4 boss-text-center">
                    <span className={`boss-inline-flex boss-items-center boss-px-2.5 boss-py-0.5 boss-rounded-full boss-text-xs boss-font-medium ${
                      item.issues.improvements > 0 ? 'boss-bg-blue-100 boss-text-blue-800' : 'boss-bg-gray-100 boss-text-gray-800'
                    }`}>
                      {item.issues.improvements}
                    </span>
                  </td>
                  <td className="boss-p-4 boss-text-center">
                    <span className={`boss-inline-flex boss-items-center boss-px-2.5 boss-py-0.5 boss-rounded-full boss-text-xs boss-font-medium ${
                      item.issues.successes > 0 ? 'boss-bg-green-100 boss-text-green-800' : 'boss-bg-gray-100 boss-text-gray-800'
                    }`}>
                      {item.issues.successes}
                    </span>
                  </td>
                  <td className="boss-p-4 boss-text-right">
                    <Button
                      isSmall
                      isSecondary
                    >
                      {__('Voir le rapport', 'boss-seo')}
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </CardBody>
      </Card>
    </div>
  );
};

export default HistorySection;
