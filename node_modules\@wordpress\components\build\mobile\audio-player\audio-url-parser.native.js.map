{"version": 3, "names": ["_url", "require", "parseAudioUrl", "src", "decodedURI", "safeDecodeURI", "fileName", "split", "shift", "pop", "parts", "extension", "length", "toUpperCase", "title", "join", "exports"], "sources": ["@wordpress/components/src/mobile/audio-player/audio-url-parser.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { safeDecodeURI } from '@wordpress/url';\n\nexport const parseAudioUrl = ( src ) => {\n\tconst decodedURI = safeDecodeURI( src );\n\tconst fileName = decodedURI\n\t\t.split( '#' )\n\t\t.shift()\n\t\t.split( '?' )\n\t\t.shift()\n\t\t.split( '/' )\n\t\t.pop();\n\n\tconst parts = fileName.split( '.' );\n\tconst extension = parts.length === 2 ? parts.pop().toUpperCase() + ' ' : '';\n\tconst title = parts.join( '.' );\n\treturn { title, extension };\n};\n"], "mappings": ";;;;;;AAGA,IAAAA,IAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGO,MAAMC,aAAa,GAAKC,GAAG,IAAM;EACvC,MAAMC,UAAU,GAAG,IAAAC,kBAAa,EAAEF,GAAI,CAAC;EACvC,MAAMG,QAAQ,GAAGF,UAAU,CACzBG,KAAK,CAAE,GAAI,CAAC,CACZC,KAAK,CAAC,CAAC,CACPD,KAAK,CAAE,GAAI,CAAC,CACZC,KAAK,CAAC,CAAC,CACPD,KAAK,CAAE,GAAI,CAAC,CACZE,GAAG,CAAC,CAAC;EAEP,MAAMC,KAAK,GAAGJ,QAAQ,CAACC,KAAK,CAAE,GAAI,CAAC;EACnC,MAAMI,SAAS,GAAGD,KAAK,CAACE,MAAM,KAAK,CAAC,GAAGF,KAAK,CAACD,GAAG,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;EAC3E,MAAMC,KAAK,GAAGJ,KAAK,CAACK,IAAI,CAAE,GAAI,CAAC;EAC/B,OAAO;IAAED,KAAK;IAAEH;EAAU,CAAC;AAC5B,CAAC;AAACK,OAAA,CAAAd,aAAA,GAAAA,aAAA"}