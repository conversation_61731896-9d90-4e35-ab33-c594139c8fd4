{"version": 3, "names": ["_cell", "_interopRequireDefault", "require", "_picker", "BottomSheetPickerCell", "props", "options", "hideCancelButton", "onChangeValue", "value", "cellProps", "picker", "onCellPress", "presentPicker", "onChange", "newValue", "option", "find", "opt", "label", "_react", "createElement", "default", "onPress", "editable", "leftAlign", "ref", "instance"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/picker-cell.native.js"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport Picker from '../picker';\n\nexport default function BottomSheetPickerCell( props ) {\n\tconst { options, hideCancelButton, onChangeValue, value, ...cellProps } =\n\t\tprops;\n\n\tlet picker;\n\n\tconst onCellPress = () => {\n\t\tpicker.presentPicker();\n\t};\n\n\tconst onChange = ( newValue ) => {\n\t\tonChangeValue( newValue );\n\t};\n\n\tconst option = options.find( ( opt ) => opt.value === value );\n\tconst label = option ? option.label : value;\n\n\treturn (\n\t\t<Cell\n\t\t\tonPress={ onCellPress }\n\t\t\teditable={ false }\n\t\t\tvalue={ label }\n\t\t\t{ ...cellProps }\n\t\t>\n\t\t\t<Picker\n\t\t\t\tleftAlign\n\t\t\t\thideCancelButton={ hideCancelButton }\n\t\t\t\tref={ ( instance ) => ( picker = instance ) }\n\t\t\t\toptions={ options }\n\t\t\t\tonChange={ onChange }\n\t\t\t/>\n\t\t</Cell>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AAJA;AACA;AACA;;AAIe,SAASE,qBAAqBA,CAAEC,KAAK,EAAG;EACtD,MAAM;IAAEC,OAAO;IAAEC,gBAAgB;IAAEC,aAAa;IAAEC,KAAK;IAAE,GAAGC;EAAU,CAAC,GACtEL,KAAK;EAEN,IAAIM,MAAM;EAEV,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACzBD,MAAM,CAACE,aAAa,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,QAAQ,GAAKC,QAAQ,IAAM;IAChCP,aAAa,CAAEO,QAAS,CAAC;EAC1B,CAAC;EAED,MAAMC,MAAM,GAAGV,OAAO,CAACW,IAAI,CAAIC,GAAG,IAAMA,GAAG,CAACT,KAAK,KAAKA,KAAM,CAAC;EAC7D,MAAMU,KAAK,GAAGH,MAAM,GAAGA,MAAM,CAACG,KAAK,GAAGV,KAAK;EAE3C,OACC,IAAAW,MAAA,CAAAC,aAAA,EAACrB,KAAA,CAAAsB,OAAI;IACJC,OAAO,EAAGX,WAAa;IACvBY,QAAQ,EAAG,KAAO;IAClBf,KAAK,EAAGU,KAAO;IAAA,GACVT;EAAS,GAEd,IAAAU,MAAA,CAAAC,aAAA,EAAClB,OAAA,CAAAmB,OAAM;IACNG,SAAS;IACTlB,gBAAgB,EAAGA,gBAAkB;IACrCmB,GAAG,EAAKC,QAAQ,IAAQhB,MAAM,GAAGgB,QAAY;IAC7CrB,OAAO,EAAGA,OAAS;IACnBQ,QAAQ,EAAGA;EAAU,CACrB,CACI,CAAC;AAET"}