<?php
/**
 * Gestionnaire d'authentification pour les services Analytics
 *
 * Gère les tokens OAuth, leur stockage et leur rafraîchissement
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/analytics
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe de gestion de l'authentification Analytics
 */
class Boss_Analytics_Auth {

    /**
     * Préfixe pour les options de tokens
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $token_prefix    Préfixe des options
     */
    private $token_prefix = 'boss_analytics_tokens_';

    /**
     * URL du proxy OAuth
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $proxy_url    URL du proxy
     */
    private $proxy_url;

    /**
     * Constructeur
     *
     * @since    1.2.0
     */
    public function __construct() {
        $this->proxy_url = 'https://oauth-proxy.bossseo.com';
    }

    /**
     * Sauvegarde les tokens pour un service
     *
     * @since    1.2.0
     * @param    string    $service    Service (ga4 ou gsc)
     * @param    array     $tokens     Tokens à sauvegarder
     * @return   bool                  True si succès
     */
    public function save_tokens($service, $tokens) {
        if (!in_array($service, array('ga4', 'gsc'))) {
            return false;
        }

        // Préparer les données à sauvegarder
        $token_data = array(
            'access_token' => sanitize_text_field($tokens['access_token']),
            'refresh_token' => isset($tokens['refresh_token']) ? sanitize_text_field($tokens['refresh_token']) : '',
            'expires_in' => isset($tokens['expires_in']) ? absint($tokens['expires_in']) : 3600,
            'token_type' => isset($tokens['token_type']) ? sanitize_text_field($tokens['token_type']) : 'Bearer',
            'scope' => isset($tokens['scope']) ? sanitize_text_field($tokens['scope']) : '',
            'created_at' => time(),
            'expires_at' => time() + (isset($tokens['expires_in']) ? absint($tokens['expires_in']) : 3600)
        );

        // Ajouter les propriétés si disponibles
        if (isset($tokens['properties'])) {
            $token_data['properties'] = $this->sanitize_properties($tokens['properties']);
        }

        // Chiffrer les tokens sensibles
        $token_data['access_token'] = $this->encrypt_token($token_data['access_token']);
        if (!empty($token_data['refresh_token'])) {
            $token_data['refresh_token'] = $this->encrypt_token($token_data['refresh_token']);
        }

        // Sauvegarder dans wp_options
        $option_name = $this->token_prefix . $service;
        $result = update_option($option_name, $token_data, false);

        if ($result) {
            // Mettre à jour le timestamp de dernière synchronisation
            update_option($this->token_prefix . $service . '_last_sync', time(), false);
            
            // Logger l'événement
            $this->log_auth_event($service, 'tokens_saved', 'Tokens sauvegardés avec succès');
        }

        return $result;
    }

    /**
     * Récupère les tokens pour un service
     *
     * @since    1.2.0
     * @param    string    $service    Service (ga4 ou gsc)
     * @return   array|false           Tokens ou false si non trouvés
     */
    public function get_tokens($service) {
        if (!in_array($service, array('ga4', 'gsc'))) {
            return false;
        }

        $option_name = $this->token_prefix . $service;
        $token_data = get_option($option_name, false);

        if (!$token_data || !is_array($token_data)) {
            return false;
        }

        // Déchiffrer les tokens
        if (isset($token_data['access_token'])) {
            $token_data['access_token'] = $this->decrypt_token($token_data['access_token']);
        }
        if (isset($token_data['refresh_token']) && !empty($token_data['refresh_token'])) {
            $token_data['refresh_token'] = $this->decrypt_token($token_data['refresh_token']);
        }

        return $token_data;
    }

    /**
     * Vérifie si un service est connecté
     *
     * @since    1.2.0
     * @param    string    $service    Service à vérifier
     * @return   bool                  True si connecté
     */
    public function is_service_connected($service) {
        $tokens = $this->get_tokens($service);
        
        if (!$tokens || empty($tokens['access_token'])) {
            return false;
        }

        // Vérifier si le token n'est pas expiré
        if (isset($tokens['expires_at']) && $tokens['expires_at'] < time()) {
            // Essayer de rafraîchir le token
            if (!empty($tokens['refresh_token'])) {
                return $this->refresh_access_token($service);
            }
            return false;
        }

        return true;
    }

    /**
     * Rafraîchit le token d'accès
     *
     * @since    1.2.0
     * @param    string    $service    Service à rafraîchir
     * @return   bool                  True si succès
     */
    public function refresh_access_token($service) {
        $tokens = $this->get_tokens($service);
        
        if (!$tokens || empty($tokens['refresh_token'])) {
            $this->log_auth_event($service, 'refresh_failed', 'Refresh token manquant');
            return false;
        }

        try {
            // Appeler le proxy pour rafraîchir le token
            $response = wp_remote_post($this->proxy_url . '/refresh', array(
                'timeout' => 30,
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Boss SEO WordPress Plugin/1.2.0'
                ),
                'body' => wp_json_encode(array(
                    'service' => $service,
                    'refresh_token' => $tokens['refresh_token']
                ))
            ));

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            if ($status_code !== 200) {
                throw new Exception(sprintf(__('Erreur HTTP %d lors du rafraîchissement', 'boss-seo'), $status_code));
            }

            $data = json_decode($body, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception(__('Réponse JSON invalide du proxy', 'boss-seo'));
            }

            if (!isset($data['access_token'])) {
                throw new Exception(__('Nouveau token d\'accès manquant', 'boss-seo'));
            }

            // Mettre à jour les tokens
            $tokens['access_token'] = $data['access_token'];
            $tokens['expires_in'] = isset($data['expires_in']) ? absint($data['expires_in']) : 3600;
            $tokens['expires_at'] = time() + $tokens['expires_in'];
            $tokens['created_at'] = time();

            // Mettre à jour le refresh token si fourni
            if (isset($data['refresh_token'])) {
                $tokens['refresh_token'] = $data['refresh_token'];
            }

            // Sauvegarder les nouveaux tokens
            $result = $this->save_tokens($service, $tokens);
            
            if ($result) {
                $this->log_auth_event($service, 'token_refreshed', 'Token rafraîchi avec succès');
            }

            return $result;

        } catch (Exception $e) {
            $this->log_auth_event($service, 'refresh_error', $e->getMessage());
            return false;
        }
    }

    /**
     * Obtient un token d'accès valide
     *
     * @since    1.2.0
     * @param    string    $service    Service
     * @return   string|false          Token d'accès ou false
     */
    public function get_valid_access_token($service) {
        if (!$this->is_service_connected($service)) {
            return false;
        }

        $tokens = $this->get_tokens($service);
        return isset($tokens['access_token']) ? $tokens['access_token'] : false;
    }

    /**
     * Obtient les propriétés d'un service
     *
     * @since    1.2.0
     * @param    string    $service    Service
     * @return   array                 Propriétés du service
     */
    public function get_service_properties($service) {
        $tokens = $this->get_tokens($service);
        
        if (!$tokens || !isset($tokens['properties'])) {
            return array();
        }

        return $tokens['properties'];
    }

    /**
     * Met à jour les propriétés d'un service
     *
     * @since    1.2.0
     * @param    string    $service      Service
     * @param    array     $properties   Propriétés à sauvegarder
     * @return   bool                    True si succès
     */
    public function update_service_properties($service, $properties) {
        $tokens = $this->get_tokens($service);
        
        if (!$tokens) {
            return false;
        }

        $tokens['properties'] = $this->sanitize_properties($properties);
        return $this->save_tokens($service, $tokens);
    }

    /**
     * Obtient la date de dernière synchronisation
     *
     * @since    1.2.0
     * @param    string    $service    Service
     * @return   int|false             Timestamp ou false
     */
    public function get_last_sync($service) {
        $option_name = $this->token_prefix . $service . '_last_sync';
        return get_option($option_name, false);
    }

    /**
     * Déconnecte un service
     *
     * @since    1.2.0
     * @param    string    $service    Service à déconnecter
     * @return   bool                  True si succès
     */
    public function disconnect_service($service) {
        if (!in_array($service, array('ga4', 'gsc'))) {
            return false;
        }

        // Supprimer les tokens
        $option_name = $this->token_prefix . $service;
        $result1 = delete_option($option_name);
        
        // Supprimer la date de dernière sync
        $sync_option = $this->token_prefix . $service . '_last_sync';
        $result2 = delete_option($sync_option);

        if ($result1) {
            $this->log_auth_event($service, 'disconnected', 'Service déconnecté');
        }

        return $result1;
    }

    /**
     * Nettoie les propriétés
     *
     * @since    1.2.0
     * @param    array    $properties    Propriétés à nettoyer
     * @return   array                   Propriétés nettoyées
     */
    private function sanitize_properties($properties) {
        if (!is_array($properties)) {
            return array();
        }

        $sanitized = array();
        
        foreach ($properties as $property) {
            if (is_array($property)) {
                $clean_property = array();
                
                if (isset($property['id'])) {
                    $clean_property['id'] = sanitize_text_field($property['id']);
                }
                if (isset($property['name'])) {
                    $clean_property['name'] = sanitize_text_field($property['name']);
                }
                if (isset($property['url'])) {
                    $clean_property['url'] = esc_url_raw($property['url']);
                }
                if (isset($property['selected'])) {
                    $clean_property['selected'] = (bool) $property['selected'];
                }
                
                $sanitized[] = $clean_property;
            }
        }

        return $sanitized;
    }

    /**
     * Chiffre un token
     *
     * @since    1.2.0
     * @param    string    $token    Token à chiffrer
     * @return   string              Token chiffré
     */
    private function encrypt_token($token) {
        if (empty($token)) {
            return '';
        }

        // Utiliser la clé de sécurité WordPress
        $key = defined('AUTH_KEY') ? AUTH_KEY : 'boss-seo-default-key';
        
        // Chiffrement simple avec base64 et XOR
        $encrypted = '';
        $key_length = strlen($key);
        
        for ($i = 0; $i < strlen($token); $i++) {
            $encrypted .= chr(ord($token[$i]) ^ ord($key[$i % $key_length]));
        }
        
        return base64_encode($encrypted);
    }

    /**
     * Déchiffre un token
     *
     * @since    1.2.0
     * @param    string    $encrypted_token    Token chiffré
     * @return   string                        Token déchiffré
     */
    private function decrypt_token($encrypted_token) {
        if (empty($encrypted_token)) {
            return '';
        }

        $key = defined('AUTH_KEY') ? AUTH_KEY : 'boss-seo-default-key';
        $encrypted = base64_decode($encrypted_token);
        
        if ($encrypted === false) {
            return '';
        }
        
        $decrypted = '';
        $key_length = strlen($key);
        
        for ($i = 0; $i < strlen($encrypted); $i++) {
            $decrypted .= chr(ord($encrypted[$i]) ^ ord($key[$i % $key_length]));
        }
        
        return $decrypted;
    }

    /**
     * Enregistre un événement d'authentification
     *
     * @since    1.2.0
     * @param    string    $service    Service concerné
     * @param    string    $event      Type d'événement
     * @param    string    $message    Message de l'événement
     */
    private function log_auth_event($service, $event, $message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log(sprintf(
                '[Boss SEO Analytics Auth] %s - %s: %s',
                strtoupper($service),
                $event,
                $message
            ));
        }

        // Optionnel: Sauvegarder dans une table de logs personnalisée
        do_action('boss_analytics_auth_event', $service, $event, $message);
    }
}
