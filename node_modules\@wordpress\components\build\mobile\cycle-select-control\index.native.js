"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _cyclePickerCell = _interopRequireDefault(require("../bottom-sheet/cycle-picker-cell"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function CycleSelectControl({
  help,
  instanceId,
  label,
  multiple = false,
  onChange,
  options = [],
  className,
  hideLabelFromVision,
  ...props
}) {
  const id = `inspector-select-control-${instanceId}`;
  return (0, _react.createElement)(_cyclePickerCell.default, {
    label: label,
    hideLabelFromVision: hideLabelFromVision,
    id: id,
    help: help,
    className: className,
    onChangeValue: onChange,
    "aria-describedby": !!help ? `${id}__help` : undefined,
    multiple: multiple,
    options: options,
    ...props
  });
}
var _default = (0, _element.memo)(CycleSelectControl);
exports.default = _default;
//# sourceMappingURL=index.native.js.map