{"version": 3, "names": ["_reactNative", "require", "_i18n", "_icons", "ExternalLink", "href", "children", "_react", "createElement", "TouchableOpacity", "onPress", "Linking", "openURL", "accessibilityLabel", "__", "Text", "Icon", "icon", "external", "_default", "exports", "default"], "sources": ["@wordpress/components/src/external-link/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\n\nimport { TouchableOpacity, Text, Linking } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { external, Icon } from '@wordpress/icons';\n\nexport function ExternalLink( { href, children } ) {\n\treturn (\n\t\t<TouchableOpacity\n\t\t\tonPress={ () => Linking.openURL( href ) }\n\t\t\taccessibilityLabel={ __( 'Open link in a browser' ) }\n\t\t>\n\t\t\t<Text>{ children }</Text>\n\t\t\t<Icon icon={ external } />\n\t\t</TouchableOpacity>\n\t);\n}\n\nexport default ExternalLink;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAIO,SAASG,YAAYA,CAAE;EAAEC,IAAI;EAAEC;AAAS,CAAC,EAAG;EAClD,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACR,YAAA,CAAAS,gBAAgB;IAChBC,OAAO,EAAGA,CAAA,KAAMC,oBAAO,CAACC,OAAO,CAAEP,IAAK,CAAG;IACzCQ,kBAAkB,EAAG,IAAAC,QAAE,EAAE,wBAAyB;EAAG,GAErD,IAAAP,MAAA,CAAAC,aAAA,EAACR,YAAA,CAAAe,IAAI,QAAGT,QAAgB,CAAC,EACzB,IAAAC,MAAA,CAAAC,aAAA,EAACL,MAAA,CAAAa,IAAI;IAACC,IAAI,EAAGC;EAAU,CAAE,CACR,CAAC;AAErB;AAAC,IAAAC,QAAA,GAEcf,YAAY;AAAAgB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}