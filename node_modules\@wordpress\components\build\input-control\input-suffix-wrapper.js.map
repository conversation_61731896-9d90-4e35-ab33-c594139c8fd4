{"version": 3, "names": ["_spacer", "require", "_context", "UnconnectedInputControlSuffixWrapper", "props", "forwardedRef", "derivedProps", "useContextSystem", "_react", "createElement", "Spacer", "marginBottom", "ref", "InputControlSuffixWrapper", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/input-control/input-suffix-wrapper.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport { Spacer } from '../spacer';\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnect, useContextSystem } from '../context';\nimport type { InputControlSuffixWrapperProps } from './types';\n\nfunction UnconnectedInputControlSuffixWrapper(\n\tprops: WordPressComponentProps< InputControlSuffixWrapperProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst derivedProps = useContextSystem( props, 'InputControlSuffixWrapper' );\n\n\treturn (\n\t\t<Spacer marginBottom={ 0 } { ...derivedProps } ref={ forwardedRef } />\n\t);\n}\n\n/**\n * A convenience wrapper for the `suffix` when you want to apply\n * standard padding in accordance with the size variant.\n *\n * ```jsx\n * import {\n *   __experimentalInputControl as InputControl,\n *   __experimentalInputControlSuffixWrapper as InputControlSuffixWrapper,\n * } from '@wordpress/components';\n *\n * <InputControl\n *   suffix={<InputControlSuffixWrapper>%</InputControlSuffixWrapper>}\n * />\n * ```\n */\nexport const InputControlSuffixWrapper = contextConnect(\n\tUnconnectedInputControlSuffixWrapper,\n\t'InputControlSuffixWrapper'\n);\n\nexport default InputControlSuffixWrapper;\n"], "mappings": ";;;;;;;AAQA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AAVA;AACA;AACA;;AAGA;AACA;AACA;;AAMA,SAASE,oCAAoCA,CAC5CC,KAAuE,EACvEC,YAAiC,EAChC;EACD,MAAMC,YAAY,GAAG,IAAAC,yBAAgB,EAAEH,KAAK,EAAE,2BAA4B,CAAC;EAE3E,OACC,IAAAI,MAAA,CAAAC,aAAA,EAACT,OAAA,CAAAU,MAAM;IAACC,YAAY,EAAG,CAAG;IAAA,GAAML,YAAY;IAAGM,GAAG,EAAGP;EAAc,CAAE,CAAC;AAExE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMQ,yBAAyB,GAAG,IAAAC,uBAAc,EACtDX,oCAAoC,EACpC,2BACD,CAAC;AAACY,OAAA,CAAAF,yBAAA,GAAAA,yBAAA;AAAA,IAAAG,QAAA,GAEaH,yBAAyB;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}