# Image SSIM [![Build Status](https://travis-ci.org/darosh/image-ssim-js.svg)](https://travis-ci.org/darosh/image-ssim-js)

Image structural similarity (SSIM). In TypeScript/JavaScript. For browser/server.

- Original TypeScript implementation: https://github.com/igor-bezkrovny/image-quantization/blob/9f62764ac047c3e53accdf1d7e4e424b0ef2fb60/src/quality/ssim.ts
- Based on Java implementation: https://github.com/rhys-e/structural-similarity
- For more information see: http://en.wikipedia.org/wiki/Structural_similarity

## Demo

[test/browser_test.html](http://darosh.github.io/image-ssim-js/test/browser_test.html)

## Similar stuff

- http://www.cns.nyu.edu/lcv/ssim/
- http://www.researchgate.net/publication/270584309_Window_Size_Influence_on_SSIM_Fidelity
- http://users.eecs.northwestern.edu/~pappas/papers/brooks_tip08.pdf
- http://foulard.ece.cornell.edu/dmr58/dmr_icip2008.pdf
- https://gist.github.com/Bibimaw/8873663
- https://github.com/bytespider/ssim

## Documentation

[http://darosh.github.io/image-ssim-js/doc/](http://darosh.github.io/image-ssim-js/doc/)
