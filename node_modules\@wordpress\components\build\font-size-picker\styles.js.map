{"version": 3, "names": ["_baseControl", "_interopRequireDefault", "require", "_button", "_hStack", "_space", "_utils", "_EMOTION_STRINGIFIED_CSS_ERROR__", "Container", "_base", "default", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports", "Header", "HStack", "space", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseControl", "VisualLabel", "HeaderHint", "COLORS", "gray", "Controls", "props", "__nextHasNoMarginBottom"], "sources": ["@wordpress/components/src/font-size-picker/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport BaseControl from '../base-control';\nimport Button from '../button';\nimport { HStack } from '../h-stack';\nimport { space } from '../utils/space';\nimport { COLORS } from '../utils';\n\nexport const Container = styled.fieldset`\n\tborder: 0;\n\tmargin: 0;\n\tpadding: 0;\n`;\n\nexport const Header = styled( HStack )`\n\theight: ${ space( 4 ) };\n`;\n\nexport const HeaderToggle = styled( Button )`\n\tmargin-top: ${ space( -1 ) };\n`;\n\nexport const HeaderLabel = styled( BaseControl.VisualLabel )`\n\tdisplay: flex;\n\tgap: ${ space( 1 ) };\n\tjustify-content: flex-start;\n\tmargin-bottom: 0;\n`;\n\nexport const HeaderHint = styled.span`\n\tcolor: ${ COLORS.gray[ 700 ] };\n`;\n\nexport const Controls = styled.div< {\n\t__nextHasNoMarginBottom: boolean;\n} >`\n\t${ ( props ) =>\n\t\t! props.__nextHasNoMarginBottom && `margin-bottom: ${ space( 6 ) };` }\n`;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAkC,SAAAK,iCAAA;AAE3B,MAAMC,SAAS,OAAAC,KAAA,CAAAC,OAAA,cAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAIrB;AAACa,OAAA,CAAAZ,SAAA,GAAAA,SAAA;AAEK,MAAMa,MAAM,GAAG,kBAAAZ,KAAA,CAAAC,OAAA,EAAQY,cAAM,EAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,YAC1B,IAAAQ,YAAK,EAAE,CAAE,CAAC,SAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,woDACrB;AAACO,OAAA,CAAAC,MAAA,GAAAA,MAAA;AAEK,MAAMG,YAAY,GAAG,kBAAAf,KAAA,CAAAC,OAAA,EAAQe,eAAM,EAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,gBAC5B,IAAAQ,YAAK,EAAE,CAAC,CAAE,CAAC,SAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,woDAC1B;AAACO,OAAA,CAAAI,YAAA,GAAAA,YAAA;AAEK,MAAME,WAAW,GAAG,kBAAAjB,KAAA,CAAAC,OAAA,EAAQiB,oBAAW,CAACC,WAAW,EAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,sBAEnD,IAAAQ,YAAK,EAAE,CAAE,CAAC,oDAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,woDAGlB;AAACO,OAAA,CAAAM,WAAA,GAAAA,WAAA;AAEK,MAAMG,UAAU,OAAApB,KAAA,CAAAC,OAAA,UAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,aACZe,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,SAAApB,OAAA,CAAAC,GAAA,CAAAC,QAAA,woDAC5B;AAACO,OAAA,CAAAS,UAAA,GAAAA,UAAA;AAEK,MAAMG,QAAQ,OAAAvB,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAGfkB,KAAK,IACT,CAAEA,KAAK,CAACC,uBAAuB,IAAK,kBAAkB,IAAAX,YAAK,EAAE,CAAE,CAAG,GAAE,SAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,woDACrE;AAACO,OAAA,CAAAY,QAAA,GAAAA,QAAA"}