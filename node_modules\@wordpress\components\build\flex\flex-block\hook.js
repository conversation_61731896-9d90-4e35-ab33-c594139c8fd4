"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useFlexBlock = useFlexBlock;
var _context = require("../../context");
var _flexItem = require("../flex-item");
/**
 * Internal dependencies
 */

function useFlexBlock(props) {
  const otherProps = (0, _context.useContextSystem)(props, 'FlexBlock');
  const flexItemProps = (0, _flexItem.useFlexItem)({
    isBlock: true,
    ...otherProps
  });
  return flexItemProps;
}
//# sourceMappingURL=hook.js.map