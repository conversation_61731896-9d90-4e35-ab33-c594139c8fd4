/**
 * Composant pour afficher les recommandations d'amélioration générées par l'IA
 */
import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardHeader,
  CardBody,
  Button,
  Panel,
  PanelBody,
  PanelRow,
  Dashicon,
  Tooltip,
  SelectControl
} from '@wordpress/components';

/**
 * Composant pour afficher les recommandations d'amélioration générées par l'IA
 * 
 * @param {Object} props Propriétés du composant
 * @param {Object} props.suggestions Suggestions d'amélioration
 * @param {Function} props.onImplement Fonction appelée lorsqu'une suggestion est implémentée
 */
const AIRecommendationsSection = ({ suggestions, onImplement }) => {
  // États
  const [activeCategory, setActiveCategory] = useState('performance');
  const [expandedSuggestions, setExpandedSuggestions] = useState({});

  // Vérifier si des suggestions sont disponibles
  const hasSuggestions = suggestions && Object.keys(suggestions).some(category => suggestions[category] && suggestions[category].length > 0);

  // Fonction pour basculer l'état d'expansion d'une suggestion
  const toggleSuggestionExpanded = (suggestionId) => {
    setExpandedSuggestions(prev => ({
      ...prev,
      [suggestionId]: !prev[suggestionId]
    }));
  };

  // Fonction pour obtenir la classe de couleur en fonction de l'impact
  const getImpactColorClass = (impact) => {
    switch (impact) {
      case 'high':
        return 'boss-text-red-600';
      case 'medium':
        return 'boss-text-orange-500';
      case 'low':
        return 'boss-text-green-600';
      default:
        return 'boss-text-gray-600';
    }
  };

  // Fonction pour obtenir la classe de couleur en fonction de la difficulté
  const getDifficultyColorClass = (difficulty) => {
    switch (difficulty) {
      case 'high':
        return 'boss-text-red-600';
      case 'medium':
        return 'boss-text-orange-500';
      case 'low':
        return 'boss-text-green-600';
      default:
        return 'boss-text-gray-600';
    }
  };

  // Fonction pour obtenir le libellé de l'impact
  const getImpactLabel = (impact) => {
    switch (impact) {
      case 'high':
        return __('Élevé', 'boss-seo');
      case 'medium':
        return __('Moyen', 'boss-seo');
      case 'low':
        return __('Faible', 'boss-seo');
      default:
        return __('Inconnu', 'boss-seo');
    }
  };

  // Fonction pour obtenir le libellé de la difficulté
  const getDifficultyLabel = (difficulty) => {
    switch (difficulty) {
      case 'high':
        return __('Difficile', 'boss-seo');
      case 'medium':
        return __('Moyenne', 'boss-seo');
      case 'low':
        return __('Facile', 'boss-seo');
      default:
        return __('Inconnue', 'boss-seo');
    }
  };

  // Fonction pour obtenir le libellé de la catégorie
  const getCategoryLabel = (category) => {
    switch (category) {
      case 'performance':
        return __('Performance', 'boss-seo');
      case 'accessibility':
        return __('Accessibilité', 'boss-seo');
      case 'best_practices':
        return __('Bonnes pratiques', 'boss-seo');
      case 'seo':
        return __('SEO', 'boss-seo');
      default:
        return category;
    }
  };

  // Fonction pour obtenir l'icône de la catégorie
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'performance':
        return 'dashboard';
      case 'accessibility':
        return 'universal-access-alt';
      case 'best_practices':
        return 'yes-alt';
      case 'seo':
        return 'search';
      default:
        return 'info';
    }
  };

  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            <Dashicon icon="lightbulb" className="boss-mr-2" />
            {__('Recommandations d\'amélioration (IA)', 'boss-seo')}
          </h2>
          
          <SelectControl
            value={activeCategory}
            options={[
              { label: __('Performance', 'boss-seo'), value: 'performance' },
              { label: __('Accessibilité', 'boss-seo'), value: 'accessibility' },
              { label: __('Bonnes pratiques', 'boss-seo'), value: 'best_practices' },
              { label: __('SEO', 'boss-seo'), value: 'seo' }
            ]}
            onChange={setActiveCategory}
          />
        </div>
      </CardHeader>
      
      <CardBody>
        {!hasSuggestions ? (
          <div className="boss-text-center boss-py-8 boss-text-boss-gray">
            <Dashicon icon="info" className="boss-mb-2 boss-text-3xl" />
            <p>{__('Aucune recommandation disponible pour le moment.', 'boss-seo')}</p>
            <p className="boss-text-sm">{__('Lancez une analyse pour obtenir des recommandations personnalisées.', 'boss-seo')}</p>
          </div>
        ) : (
          <div className="boss-space-y-4">
            {suggestions[activeCategory] && suggestions[activeCategory].length > 0 ? (
              suggestions[activeCategory].map((suggestion, index) => {
                const suggestionId = `${activeCategory}-${index}`;
                const isExpanded = expandedSuggestions[suggestionId];
                
                return (
                  <Panel key={suggestionId} className="boss-border boss-border-gray-200 boss-rounded-lg boss-overflow-hidden">
                    <PanelBody
                      title={
                        <div className="boss-flex boss-justify-between boss-items-center boss-w-full">
                          <div className="boss-flex boss-items-center">
                            <Dashicon icon={getCategoryIcon(activeCategory)} className="boss-mr-2" />
                            <span>{suggestion.title}</span>
                          </div>
                          <div className="boss-flex boss-items-center boss-space-x-4">
                            <div className="boss-flex boss-items-center">
                              <Tooltip text={__('Impact', 'boss-seo')}>
                                <span className={`boss-text-sm boss-font-medium ${getImpactColorClass(suggestion.impact)}`}>
                                  {getImpactLabel(suggestion.impact)}
                                </span>
                              </Tooltip>
                            </div>
                            <div className="boss-flex boss-items-center">
                              <Tooltip text={__('Difficulté', 'boss-seo')}>
                                <span className={`boss-text-sm boss-font-medium ${getDifficultyColorClass(suggestion.difficulty)}`}>
                                  {getDifficultyLabel(suggestion.difficulty)}
                                </span>
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      }
                      initialOpen={false}
                      onToggle={() => toggleSuggestionExpanded(suggestionId)}
                    >
                      <div className="boss-p-4 boss-space-y-4">
                        <div>
                          <h4 className="boss-font-medium boss-mb-2">{__('Description', 'boss-seo')}</h4>
                          <p className="boss-text-boss-gray">{suggestion.description}</p>
                        </div>
                        
                        <div>
                          <h4 className="boss-font-medium boss-mb-2">{__('Solution recommandée', 'boss-seo')}</h4>
                          <p className="boss-text-boss-gray">{suggestion.solution}</p>
                        </div>
                        
                        {suggestion.items && suggestion.items.length > 0 && (
                          <div>
                            <h4 className="boss-font-medium boss-mb-2">{__('Éléments concernés', 'boss-seo')}</h4>
                            <ul className="boss-list-disc boss-pl-5 boss-text-boss-gray boss-text-sm">
                              {suggestion.items.map((item, itemIndex) => (
                                <li key={itemIndex} className="boss-mb-1">
                                  {item.url || item.node || item.source || JSON.stringify(item)}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        <div className="boss-flex boss-justify-end">
                          <Button
                            isPrimary
                            onClick={() => onImplement && onImplement(suggestion, activeCategory)}
                          >
                            {__('Implémenter cette suggestion', 'boss-seo')}
                          </Button>
                        </div>
                      </div>
                    </PanelBody>
                  </Panel>
                );
              })
            ) : (
              <div className="boss-text-center boss-py-8 boss-text-boss-gray">
                <Dashicon icon="info" className="boss-mb-2 boss-text-3xl" />
                <p>{__(`Aucune recommandation disponible pour la catégorie "${getCategoryLabel(activeCategory)}".`, 'boss-seo')}</p>
              </div>
            )}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default AIRecommendationsSection;
