<?php
/**
 * Initialisation du module Analytics
 *
 * Charge et initialise tous les composants du module Analytics
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

// Empêcher l'accès direct
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe d'initialisation du module Analytics
 */
class Boss_Analytics_Init {

    /**
     * Instance unique de la classe
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Init    $instance    Instance unique
     */
    private static $instance = null;

    /**
     * Gestionnaire principal Analytics
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Analytics_Manager    $analytics_manager    Gestionnaire principal
     */
    private $analytics_manager;

    /**
     * Constructeur privé pour le singleton
     *
     * @since    1.2.0
     */
    private function __construct() {
        $this->load_dependencies();
        $this->init_analytics();
        $this->register_hooks();
    }

    /**
     * Obtient l'instance unique de la classe
     *
     * @since    1.2.0
     * @return   Boss_Analytics_Init    Instance unique
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Charge les dépendances du module
     *
     * @since    1.2.0
     */
    private function load_dependencies() {
        // Charger le gestionnaire principal
        require_once plugin_dir_path(__FILE__) . 'class-boss-analytics-manager.php';

        // Les autres classes sont chargées automatiquement par le gestionnaire principal
    }

    /**
     * Initialise le module Analytics
     *
     * @since    1.2.0
     */
    private function init_analytics() {
        // Initialiser le gestionnaire principal
        $this->analytics_manager = Boss_Analytics_Manager::get_instance();
    }

    /**
     * Enregistre les hooks WordPress
     *
     * @since    1.2.0
     */
    private function register_hooks() {
        // Hook d'activation du plugin
        register_activation_hook(BOSS_SEO_PLUGIN_FILE, array($this, 'activate_analytics_module'));

        // Hook de désactivation du plugin
        register_deactivation_hook(BOSS_SEO_PLUGIN_FILE, array($this, 'deactivate_analytics_module'));

        // Hook de suppression du plugin
        register_uninstall_hook(BOSS_SEO_PLUGIN_FILE, array('Boss_Analytics_Init', 'uninstall_analytics_module'));

        // Hooks d'administration
        add_action('admin_init', array($this, 'check_analytics_requirements'));
        add_action('admin_notices', array($this, 'display_analytics_notices'));

        // Hook pour les scripts admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_analytics_scripts'));
    }

    /**
     * Active le module Analytics
     *
     * @since    1.2.0
     */
    public function activate_analytics_module() {
        // Créer les options par défaut
        $default_options = array(
            'analytics_enabled' => true,
            'cache_duration' => 3600, // 1 heure
            'sync_frequency' => 'boss_analytics_6hours',
            'debug_mode' => false
        );

        add_option('boss_analytics_options', $default_options);

        // Programmer les tâches CRON
        if (!wp_next_scheduled('boss_analytics_sync_data')) {
            wp_schedule_event(time() + 300, 'boss_analytics_6hours', 'boss_analytics_sync_data');
        }

        if (!wp_next_scheduled('boss_analytics_cleanup_cache')) {
            wp_schedule_event(time() + 600, 'daily', 'boss_analytics_cleanup_cache');
        }

        // Créer les tables personnalisées si nécessaire
        $this->create_analytics_tables();

        // Logger l'activation
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Boss SEO Analytics] Module activé avec succès');
        }
    }

    /**
     * Désactive le module Analytics
     *
     * @since    1.2.0
     */
    public function deactivate_analytics_module() {
        // Arrêter les tâches CRON
        wp_clear_scheduled_hook('boss_analytics_sync_data');
        wp_clear_scheduled_hook('boss_analytics_cleanup_cache');

        // Vider le cache
        if (class_exists('Boss_Analytics_Cache')) {
            $cache_manager = new Boss_Analytics_Cache();
            $cache_manager->flush_all();
        }

        // Logger la désactivation
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Boss SEO Analytics] Module désactivé');
        }
    }

    /**
     * Désinstalle le module Analytics
     *
     * @since    1.2.0
     */
    public static function uninstall_analytics_module() {
        // Supprimer les options
        delete_option('boss_analytics_options');

        // Supprimer les tokens d'authentification
        delete_option('boss_analytics_tokens_ga4');
        delete_option('boss_analytics_tokens_gsc');
        delete_option('boss_analytics_tokens_ga4_last_sync');
        delete_option('boss_analytics_tokens_gsc_last_sync');

        // Supprimer les métadonnées de cache
        delete_option('boss_analytics_metadata');

        // Supprimer tous les transients Analytics
        global $wpdb;
        $wpdb->query(
            "DELETE FROM {$wpdb->options}
             WHERE option_name LIKE '_transient_boss_analytics_%'
             OR option_name LIKE '_transient_timeout_boss_analytics_%'"
        );

        // Supprimer les tables personnalisées si elles existent
        self::drop_analytics_tables();

        // Logger la désinstallation
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Boss SEO Analytics] Module désinstallé');
        }
    }

    /**
     * Vérifie les prérequis du module Analytics
     *
     * @since    1.2.0
     */
    public function check_analytics_requirements() {
        // Vérifier la version de PHP
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>';
                echo __('Boss SEO Analytics nécessite PHP 7.4 ou supérieur.', 'boss-seo');
                echo '</p></div>';
            });
        }

        // Vérifier les extensions PHP nécessaires
        $required_extensions = array('curl', 'json', 'openssl');
        $missing_extensions = array();

        foreach ($required_extensions as $extension) {
            if (!extension_loaded($extension)) {
                $missing_extensions[] = $extension;
            }
        }

        if (!empty($missing_extensions)) {
            add_action('admin_notices', function() use ($missing_extensions) {
                echo '<div class="notice notice-error"><p>';
                printf(
                    __('Boss SEO Analytics nécessite les extensions PHP suivantes : %s', 'boss-seo'),
                    implode(', ', $missing_extensions)
                );
                echo '</p></div>';
            });
        }
    }

    /**
     * Affiche les notices d'administration
     *
     * @since    1.2.0
     */
    public function display_analytics_notices() {
        // Vérifier si les services sont connectés
        if (class_exists('Boss_Analytics_Auth')) {
            $auth_manager = new Boss_Analytics_Auth();
            $ga4_connected = $auth_manager->is_service_connected('ga4');
            $gsc_connected = $auth_manager->is_service_connected('gsc');

            // Afficher une notice si aucun service n'est connecté
            if (!$ga4_connected && !$gsc_connected) {
                $current_screen = get_current_screen();
                if ($current_screen && strpos($current_screen->id, 'boss-seo') !== false) {
                    echo '<div class="notice notice-warning is-dismissible">';
                    echo '<p>';
                    printf(
                        __('Pour utiliser pleinement Boss SEO Analytics, veuillez <a href="%s">connecter vos comptes Google Analytics et Search Console</a>.', 'boss-seo'),
                        admin_url('admin.php?page=boss-seo-analytics')
                    );
                    echo '</p>';
                    echo '</div>';
                }
            }
        }
    }

    /**
     * Charge les scripts et styles pour l'administration
     *
     * @since    1.2.0
     * @param    string    $hook    Hook de la page courante
     */
    public function enqueue_analytics_scripts($hook) {
        // Charger seulement sur les pages Boss SEO
        if (strpos($hook, 'boss-seo') === false) {
            return;
        }

        // Ajouter les données de configuration pour le JavaScript
        // Utiliser 'boss-seo-dashboard' qui est le script principal chargé
        wp_localize_script('boss-seo-dashboard', 'bossAnalyticsConfig', array(
            'apiUrl' => rest_url('boss-seo/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
            'proxyUrl' => 'https://oauth-proxy.bossseo.com',
            'siteUrl' => home_url(),
            'adminUrl' => admin_url(),
            'isDebug' => defined('WP_DEBUG') && WP_DEBUG
        ));
    }

    /**
     * Crée les tables personnalisées pour Analytics
     *
     * @since    1.2.0
     */
    private function create_analytics_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Table pour les logs d'événements Analytics (optionnel)
        $table_name = $wpdb->prefix . 'boss_analytics_events';

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            service varchar(10) NOT NULL,
            event_type varchar(50) NOT NULL,
            event_data longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY service_event (service, event_type),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Supprime les tables personnalisées
     *
     * @since    1.2.0
     */
    private static function drop_analytics_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'boss_analytics_events';
        $wpdb->query("DROP TABLE IF EXISTS $table_name");
    }

    /**
     * Obtient le gestionnaire Analytics
     *
     * @since    1.2.0
     * @return   Boss_Analytics_Manager    Gestionnaire Analytics
     */
    public function get_analytics_manager() {
        return $this->analytics_manager;
    }

    /**
     * Vérifie si le module Analytics est activé
     *
     * @since    1.2.0
     * @return   bool    True si activé
     */
    public function is_analytics_enabled() {
        $options = get_option('boss_analytics_options', array());
        return isset($options['analytics_enabled']) ? $options['analytics_enabled'] : false;
    }

    /**
     * Active ou désactive le module Analytics
     *
     * @since    1.2.0
     * @param    bool    $enabled    True pour activer, false pour désactiver
     */
    public function set_analytics_enabled($enabled) {
        $options = get_option('boss_analytics_options', array());
        $options['analytics_enabled'] = (bool) $enabled;
        update_option('boss_analytics_options', $options);
    }
}

// Initialiser le module Analytics
Boss_Analytics_Init::get_instance();
