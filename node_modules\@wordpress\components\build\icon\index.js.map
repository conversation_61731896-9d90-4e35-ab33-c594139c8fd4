{"version": 3, "names": ["_element", "require", "_primitives", "_dashicon", "_interopRequireDefault", "Icon", "icon", "size", "additionalProps", "createElement", "default", "isValidElement", "Dashicon", "type", "cloneElement", "SVG", "appliedProps", "props", "width", "height", "_default", "exports"], "sources": ["@wordpress/components/src/icon/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ComponentType, HTMLProps, SVGProps } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tcloneElement,\n\tcreateElement,\n\tisValidElement,\n} from '@wordpress/element';\nimport { SVG } from '@wordpress/primitives';\n\n/**\n * Internal dependencies\n */\nimport Dashicon from '../dashicon';\nimport type { IconKey as DashiconIconKey } from '../dashicon/types';\n\nexport type IconType =\n\t| DashiconIconKey\n\t| ComponentType< { size?: number } >\n\t| ( ( props: { size?: number } ) => JSX.Element )\n\t| JSX.Element;\n\ninterface BaseProps {\n\t/**\n\t * The icon to render. Supported values are: Dashicons (specified as\n\t * strings), functions, Component instances and `null`.\n\t *\n\t * @default null\n\t */\n\ticon?: IconType | null;\n\t/**\n\t * The size (width and height) of the icon.\n\t *\n\t * @default `20` when a Dashicon is rendered, `24` for all other icons.\n\t */\n\tsize?: number;\n}\n\ntype AdditionalProps< T > = T extends ComponentType< infer U >\n\t? U\n\t: T extends DashiconIconKey\n\t? SVGProps< SVGSVGElement >\n\t: {};\n\nexport type Props = BaseProps & AdditionalProps< IconType >;\n\nfunction Icon( {\n\ticon = null,\n\tsize = 'string' === typeof icon ? 20 : 24,\n\t...additionalProps\n}: Props ) {\n\tif ( 'string' === typeof icon ) {\n\t\treturn (\n\t\t\t<Dashicon\n\t\t\t\ticon={ icon }\n\t\t\t\tsize={ size }\n\t\t\t\t{ ...( additionalProps as HTMLProps< HTMLSpanElement > ) }\n\t\t\t/>\n\t\t);\n\t}\n\n\tif ( isValidElement( icon ) && Dashicon === icon.type ) {\n\t\treturn cloneElement( icon, {\n\t\t\t...additionalProps,\n\t\t} );\n\t}\n\n\tif ( 'function' === typeof icon ) {\n\t\treturn createElement( icon, {\n\t\t\tsize,\n\t\t\t...additionalProps,\n\t\t} );\n\t}\n\n\tif ( icon && ( icon.type === 'svg' || icon.type === SVG ) ) {\n\t\tconst appliedProps = {\n\t\t\t...icon.props,\n\t\t\twidth: size,\n\t\t\theight: size,\n\t\t\t...additionalProps,\n\t\t};\n\n\t\treturn <SVG { ...appliedProps } />;\n\t}\n\n\tif ( isValidElement( icon ) ) {\n\t\treturn cloneElement( icon, {\n\t\t\t// @ts-ignore Just forwarding the size prop along\n\t\t\tsize,\n\t\t\t...additionalProps,\n\t\t} );\n\t}\n\n\treturn icon;\n}\n\nexport default Icon;\n"], "mappings": ";;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,WAAA,GAAAD,OAAA;AAKA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;AAlBA;AACA;AACA;;AAGA;AACA;AACA;;AAQA;AACA;AACA;;AAkCA,SAASI,IAAIA,CAAE;EACdC,IAAI,GAAG,IAAI;EACXC,IAAI,GAAG,QAAQ,KAAK,OAAOD,IAAI,GAAG,EAAE,GAAG,EAAE;EACzC,GAAGE;AACG,CAAC,EAAG;EACV,IAAK,QAAQ,KAAK,OAAOF,IAAI,EAAG;IAC/B,OACC,IAAAN,QAAA,CAAAS,aAAA,EAACN,SAAA,CAAAO,OAAQ;MACRJ,IAAI,EAAGA,IAAM;MACbC,IAAI,EAAGA,IAAM;MAAA,GACNC;IAAe,CACtB,CAAC;EAEJ;EAEA,IAAK,IAAAG,uBAAc,EAAEL,IAAK,CAAC,IAAIM,iBAAQ,KAAKN,IAAI,CAACO,IAAI,EAAG;IACvD,OAAO,IAAAC,qBAAY,EAAER,IAAI,EAAE;MAC1B,GAAGE;IACJ,CAAE,CAAC;EACJ;EAEA,IAAK,UAAU,KAAK,OAAOF,IAAI,EAAG;IACjC,OAAO,IAAAG,sBAAa,EAAEH,IAAI,EAAE;MAC3BC,IAAI;MACJ,GAAGC;IACJ,CAAE,CAAC;EACJ;EAEA,IAAKF,IAAI,KAAMA,IAAI,CAACO,IAAI,KAAK,KAAK,IAAIP,IAAI,CAACO,IAAI,KAAKE,eAAG,CAAE,EAAG;IAC3D,MAAMC,YAAY,GAAG;MACpB,GAAGV,IAAI,CAACW,KAAK;MACbC,KAAK,EAAEX,IAAI;MACXY,MAAM,EAAEZ,IAAI;MACZ,GAAGC;IACJ,CAAC;IAED,OAAO,IAAAR,QAAA,CAAAS,aAAA,EAACP,WAAA,CAAAa,GAAG;MAAA,GAAMC;IAAY,CAAI,CAAC;EACnC;EAEA,IAAK,IAAAL,uBAAc,EAAEL,IAAK,CAAC,EAAG;IAC7B,OAAO,IAAAQ,qBAAY,EAAER,IAAI,EAAE;MAC1B;MACAC,IAAI;MACJ,GAAGC;IACJ,CAAE,CAAC;EACJ;EAEA,OAAOF,IAAI;AACZ;AAAC,IAAAc,QAAA,GAEcf,IAAI;AAAAgB,OAAA,CAAAX,OAAA,GAAAU,QAAA"}