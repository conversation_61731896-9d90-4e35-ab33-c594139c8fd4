{"version": 3, "names": ["_es", "_interopRequireDefault", "require", "_element", "_compose", "_default", "mapNodeToProps", "createHigherOrderComponent", "WrappedComponent", "Component", "constructor", "props", "nodeRef", "node", "state", "fallbackStyles", "undefined", "grabStylesCompleted", "bindRef", "bind", "componentDidMount", "grabFallbackStyles", "componentDidUpdate", "newFallbackStyles", "fastDeepEqual", "setState", "Object", "values", "every", "Boolean", "render", "wrappedComponent", "_react", "createElement", "ref", "exports", "default"], "sources": ["@wordpress/components/src/higher-order/with-fallback-styles/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport fastDeepEqual from 'fast-deep-equal/es6';\n\n/**\n * WordPress dependencies\n */\nimport { Component } from '@wordpress/element';\nimport { createHigherOrderComponent } from '@wordpress/compose';\n\ntype Props = {\n\tnode?: HTMLElement;\n\t[ key: string ]: any;\n};\n\ntype State = {\n\tfallbackStyles?: { [ key: string ]: any };\n\tgrabStylesCompleted: boolean;\n};\n\nexport default (\n\tmapNodeToProps: (\n\t\tnode: HTMLElement,\n\t\tprops: Props\n\t) => { [ key: string ]: any }\n) =>\n\tcreateHigherOrderComponent( ( WrappedComponent ) => {\n\t\treturn class extends Component< Props, State > {\n\t\t\tnodeRef?: HTMLElement;\n\n\t\t\tconstructor( props: Props ) {\n\t\t\t\tsuper( props );\n\t\t\t\tthis.nodeRef = this.props.node;\n\t\t\t\tthis.state = {\n\t\t\t\t\tfallbackStyles: undefined,\n\t\t\t\t\tgrabStylesCompleted: false,\n\t\t\t\t};\n\n\t\t\t\tthis.bindRef = this.bindRef.bind( this );\n\t\t\t}\n\n\t\t\tbindRef( node: HTMLDivElement ) {\n\t\t\t\tif ( ! node ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.nodeRef = node;\n\t\t\t}\n\n\t\t\tcomponentDidMount() {\n\t\t\t\tthis.grabFallbackStyles();\n\t\t\t}\n\n\t\t\tcomponentDidUpdate() {\n\t\t\t\tthis.grabFallbackStyles();\n\t\t\t}\n\n\t\t\tgrabFallbackStyles() {\n\t\t\t\tconst { grabStylesCompleted, fallbackStyles } = this.state;\n\t\t\t\tif ( this.nodeRef && ! grabStylesCompleted ) {\n\t\t\t\t\tconst newFallbackStyles = mapNodeToProps(\n\t\t\t\t\t\tthis.nodeRef,\n\t\t\t\t\t\tthis.props\n\t\t\t\t\t);\n\n\t\t\t\t\tif (\n\t\t\t\t\t\t! fastDeepEqual( newFallbackStyles, fallbackStyles )\n\t\t\t\t\t) {\n\t\t\t\t\t\tthis.setState( {\n\t\t\t\t\t\t\tfallbackStyles: newFallbackStyles,\n\t\t\t\t\t\t\tgrabStylesCompleted:\n\t\t\t\t\t\t\t\tObject.values( newFallbackStyles ).every(\n\t\t\t\t\t\t\t\t\tBoolean\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\trender() {\n\t\t\t\tconst wrappedComponent = (\n\t\t\t\t\t<WrappedComponent\n\t\t\t\t\t\t{ ...this.props }\n\t\t\t\t\t\t{ ...this.state.fallbackStyles }\n\t\t\t\t\t/>\n\t\t\t\t);\n\t\t\t\treturn this.props.node ? (\n\t\t\t\t\twrappedComponent\n\t\t\t\t) : (\n\t\t\t\t\t<div ref={ this.bindRef }> { wrappedComponent } </div>\n\t\t\t\t);\n\t\t\t}\n\t\t};\n\t}, 'withFallbackStyles' );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,GAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AATA;AACA;AACA;AAGA;AACA;AACA;AAFA,IAAAG,QAAA,GAiBCC,cAG6B,IAE7B,IAAAC,mCAA0B,EAAIC,gBAAgB,IAAM;EACnD,OAAO,cAAcC,kBAAS,CAAiB;IAG9CC,WAAWA,CAAEC,KAAY,EAAG;MAC3B,KAAK,CAAEA,KAAM,CAAC;MACd,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,KAAK,CAACE,IAAI;MAC9B,IAAI,CAACC,KAAK,GAAG;QACZC,cAAc,EAAEC,SAAS;QACzBC,mBAAmB,EAAE;MACtB,CAAC;MAED,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,IAAI,CAAE,IAAK,CAAC;IACzC;IAEAD,OAAOA,CAAEL,IAAoB,EAAG;MAC/B,IAAK,CAAEA,IAAI,EAAG;QACb;MACD;MACA,IAAI,CAACD,OAAO,GAAGC,IAAI;IACpB;IAEAO,iBAAiBA,CAAA,EAAG;MACnB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC1B;IAEAC,kBAAkBA,CAAA,EAAG;MACpB,IAAI,CAACD,kBAAkB,CAAC,CAAC;IAC1B;IAEAA,kBAAkBA,CAAA,EAAG;MACpB,MAAM;QAAEJ,mBAAmB;QAAEF;MAAe,CAAC,GAAG,IAAI,CAACD,KAAK;MAC1D,IAAK,IAAI,CAACF,OAAO,IAAI,CAAEK,mBAAmB,EAAG;QAC5C,MAAMM,iBAAiB,GAAGjB,cAAc,CACvC,IAAI,CAACM,OAAO,EACZ,IAAI,CAACD,KACN,CAAC;QAED,IACC,CAAE,IAAAa,WAAa,EAAED,iBAAiB,EAAER,cAAe,CAAC,EACnD;UACD,IAAI,CAACU,QAAQ,CAAE;YACdV,cAAc,EAAEQ,iBAAiB;YACjCN,mBAAmB,EAClBS,MAAM,CAACC,MAAM,CAAEJ,iBAAkB,CAAC,CAACK,KAAK,CACvCC,OACD;UACF,CAAE,CAAC;QACJ;MACD;IACD;IAEAC,MAAMA,CAAA,EAAG;MACR,MAAMC,gBAAgB,GACrB,IAAAC,MAAA,CAAAC,aAAA,EAACzB,gBAAgB;QAAA,GACX,IAAI,CAACG,KAAK;QAAA,GACV,IAAI,CAACG,KAAK,CAACC;MAAc,CAC9B,CACD;MACD,OAAO,IAAI,CAACJ,KAAK,CAACE,IAAI,GACrBkB,gBAAgB,GAEhB,IAAAC,MAAA,CAAAC,aAAA;QAAKC,GAAG,EAAG,IAAI,CAAChB;MAAS,GAAC,GAAC,EAAEa,gBAAgB,EAAE,GAAM,CACrD;IACF;EACD,CAAC;AACF,CAAC,EAAE,oBAAqB,CAAC;AAAAI,OAAA,CAAAC,OAAA,GAAA/B,QAAA"}