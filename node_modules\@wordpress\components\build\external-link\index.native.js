"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ExternalLink = ExternalLink;
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function ExternalLink({
  href,
  children
}) {
  return (0, _react.createElement)(_reactNative.TouchableOpacity, {
    onPress: () => _reactNative.Linking.openURL(href),
    accessibilityLabel: (0, _i18n.__)('Open link in a browser')
  }, (0, _react.createElement)(_reactNative.Text, null, children), (0, _react.createElement)(_icons.Icon, {
    icon: _icons.external
  }));
}
var _default = ExternalLink;
exports.default = _default;
//# sourceMappingURL=index.native.js.map