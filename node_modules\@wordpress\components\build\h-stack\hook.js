"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useHStack = useHStack;
var _react = require("react");
var _context = require("../context");
var _flex = require("../flex");
var _utils = require("./utils");
var _getValidChildren = require("../utils/get-valid-children");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function useHStack(props) {
  const {
    alignment = 'edge',
    children,
    direction,
    spacing = 2,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'HStack');
  const align = (0, _utils.getAlignmentProps)(alignment, direction);
  const validChildren = (0, _getValidChildren.getValidChildren)(children);
  const clonedChildren = validChildren.map((child, index) => {
    const _isSpacer = (0, _context.hasConnectNamespace)(child, ['Spacer']);
    if (_isSpacer) {
      const childElement = child;
      const _key = childElement.key || `hstack-${index}`;
      return (0, _react.createElement)(_flex.FlexItem, {
        isBlock: true,
        key: _key,
        ...childElement.props
      });
    }
    return child;
  });
  const propsForFlex = {
    children: clonedChildren,
    direction,
    justify: 'center',
    ...align,
    ...otherProps,
    gap: spacing
  };
  const flexProps = (0, _flex.useFlex)(propsForFlex);
  return flexProps;
}
//# sourceMappingURL=hook.js.map