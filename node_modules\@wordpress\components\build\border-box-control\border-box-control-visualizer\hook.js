"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useBorderBoxControlVisualizer = useBorderBoxControlVisualizer;
var _element = require("@wordpress/element");
var styles = _interopRequireWildcard(require("../styles"));
var _context = require("../../context");
var _utils = require("../../utils");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function useBorderBoxControlVisualizer(props) {
  const {
    className,
    value,
    size = 'default',
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'BorderBoxControlVisualizer');

  // Generate class names.
  const cx = (0, _utils.useCx)();
  const classes = (0, _element.useMemo)(() => {
    return cx(styles.borderBoxControlVisualizer(value, size), className);
  }, [cx, className, value, size]);
  return {
    ...otherProps,
    className: classes,
    value
  };
}
//# sourceMappingURL=hook.js.map