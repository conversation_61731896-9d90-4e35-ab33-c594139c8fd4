"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ItemGroup = void 0;
var _react = require("react");
var _context = require("../../context");
var _hook = require("./hook");
var _context2 = require("../context");
var _view = require("../../view");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedItemGroup(props, forwardedRef) {
  const {
    isBordered,
    isSeparated,
    size: sizeProp,
    ...otherProps
  } = (0, _hook.useItemGroup)(props);
  const {
    size: contextSize
  } = (0, _context2.useItemGroupContext)();
  const spacedAround = !isBordered && !isSeparated;
  const size = sizeProp || contextSize;
  const contextValue = {
    spacedAround,
    size
  };
  return (0, _react.createElement)(_context2.ItemGroupContext.Provider, {
    value: contextValue
  }, (0, _react.createElement)(_view.View, {
    ...otherProps,
    ref: forwardedRef
  }));
}

/**
 * `ItemGroup` displays a list of `Item`s grouped and styled together.
 *
 * ```jsx
 * import {
 *   __experimentalItemGroup as ItemGroup,
 *   __experimentalItem as Item,
 * } from '@wordpress/components';
 *
 * function Example() {
 *   return (
 *     <ItemGroup>
 *       <Item>Code</Item>
 *       <Item>is</Item>
 *       <Item>Poetry</Item>
 *     </ItemGroup>
 *   );
 * }
 * ```
 */
const ItemGroup = (0, _context.contextConnect)(UnconnectedItemGroup, 'ItemGroup');
exports.ItemGroup = ItemGroup;
var _default = ItemGroup;
exports.default = _default;
//# sourceMappingURL=component.js.map