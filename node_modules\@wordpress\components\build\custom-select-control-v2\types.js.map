{"version": 3, "names": [], "sources": ["@wordpress/components/src/custom-select-control-v2/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport type * as Ariaki<PERSON> from '@ariakit/react';\n\nexport type CustomSelectContext =\n\t| {\n\t\t\t/**\n\t\t\t * The store object returned by Ariakit's `useSelectStore` hook.\n\t\t\t */\n\t\t\tstore: Ariakit.SelectStore;\n\t  }\n\t| undefined;\n\nexport type CustomSelectProps = {\n\t/**\n\t * The child elements. This should be composed of CustomSelectItem components.\n\t */\n\tchildren: React.ReactNode;\n\t/**\n\t * An optional default value for the control. If left `undefined`, the first\n\t * non-disabled item will be used.\n\t */\n\tdefaultValue?: string | string[];\n\t/**\n\t * Label for the control.\n\t */\n\tlabel: string;\n\t/**\n\t * A function that receives the new value of the input.\n\t */\n\tonChange?: ( newValue: string | string[] ) => void;\n\t/**\n\t * Can be used to render select UI with custom styled values.\n\t */\n\trenderSelectedValue?: (\n\t\tselectedValue: string | string[]\n\t) => React.ReactNode;\n\t/**\n\t * The size of the control.\n\t *\n\t * @default 'default'\n\t */\n\tsize?: 'default' | 'small';\n\t/**\n\t * Can be used to externally control the value of the control.\n\t */\n\tvalue?: string | string[];\n};\n\nexport type CustomSelectItemProps = {\n\t/**\n\t * The value of the select item. This will be used as the children if\n\t * children are left `undefined`.\n\t */\n\tvalue: string;\n\t/**\n\t * The children to display for each select item. The `value` will be\n\t * used if left `undefined`.\n\t */\n\tchildren?: React.ReactNode;\n};\n"], "mappings": ""}