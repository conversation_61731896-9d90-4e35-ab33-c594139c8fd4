"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DropdownMenuSeparator = exports.DropdownMenuRadioItem = exports.DropdownMenuItemLabel = exports.DropdownMenuItemHelpText = exports.DropdownMenuItem = exports.DropdownMenuGroup = exports.DropdownMenuContext = exports.DropdownMenuCheckboxItem = exports.DropdownMenu = void 0;
var _react = require("react");
var Ariakit = _interopRequireWildcard(require("@ariakit/react"));
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _primitives = require("@wordpress/primitives");
var _context = require("../context");
var _icon = _interopRequireDefault(require("../icon"));
var Styled = _interopRequireWildcard(require("./styles"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */
// eslint-disable-next-line no-restricted-imports

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const DropdownMenuContext = (0, _element.createContext)(undefined);
exports.DropdownMenuContext = DropdownMenuContext;
const DropdownMenuItem = (0, _element.forwardRef)(function DropdownMenuItem({
  prefix,
  suffix,
  children,
  hideOnClick = true,
  ...props
}, ref) {
  const dropdownMenuContext = (0, _element.useContext)(DropdownMenuContext);
  return (0, _react.createElement)(Styled.DropdownMenuItem, {
    ref: ref,
    ...props,
    accessibleWhenDisabled: true,
    hideOnClick: hideOnClick,
    store: dropdownMenuContext?.store
  }, (0, _react.createElement)(Styled.ItemPrefixWrapper, null, prefix), (0, _react.createElement)(Styled.DropdownMenuItemContentWrapper, null, (0, _react.createElement)(Styled.DropdownMenuItemChildrenWrapper, null, children), suffix && (0, _react.createElement)(Styled.ItemSuffixWrapper, null, suffix)));
});
exports.DropdownMenuItem = DropdownMenuItem;
const DropdownMenuCheckboxItem = (0, _element.forwardRef)(function DropdownMenuCheckboxItem({
  suffix,
  children,
  hideOnClick = false,
  ...props
}, ref) {
  const dropdownMenuContext = (0, _element.useContext)(DropdownMenuContext);
  return (0, _react.createElement)(Styled.DropdownMenuCheckboxItem, {
    ref: ref,
    ...props,
    accessibleWhenDisabled: true,
    hideOnClick: hideOnClick,
    store: dropdownMenuContext?.store
  }, (0, _react.createElement)(Ariakit.MenuItemCheck, {
    store: dropdownMenuContext?.store,
    render: (0, _react.createElement)(Styled.ItemPrefixWrapper, null)
    // Override some ariakit inline styles
    ,
    style: {
      width: 'auto',
      height: 'auto'
    }
  }, (0, _react.createElement)(_icon.default, {
    icon: _icons.check,
    size: 24
  })), (0, _react.createElement)(Styled.DropdownMenuItemContentWrapper, null, (0, _react.createElement)(Styled.DropdownMenuItemChildrenWrapper, null, children), suffix && (0, _react.createElement)(Styled.ItemSuffixWrapper, null, suffix)));
});
exports.DropdownMenuCheckboxItem = DropdownMenuCheckboxItem;
const radioCheck = (0, _react.createElement)(_primitives.SVG, {
  xmlns: "http://www.w3.org/2000/svg",
  viewBox: "0 0 24 24"
}, (0, _react.createElement)(_primitives.Circle, {
  cx: 12,
  cy: 12,
  r: 3
}));
const DropdownMenuRadioItem = (0, _element.forwardRef)(function DropdownMenuRadioItem({
  suffix,
  children,
  hideOnClick = false,
  ...props
}, ref) {
  const dropdownMenuContext = (0, _element.useContext)(DropdownMenuContext);
  return (0, _react.createElement)(Styled.DropdownMenuRadioItem, {
    ref: ref,
    ...props,
    accessibleWhenDisabled: true,
    hideOnClick: hideOnClick,
    store: dropdownMenuContext?.store
  }, (0, _react.createElement)(Ariakit.MenuItemCheck, {
    store: dropdownMenuContext?.store,
    render: (0, _react.createElement)(Styled.ItemPrefixWrapper, null)
    // Override some ariakit inline styles
    ,
    style: {
      width: 'auto',
      height: 'auto'
    }
  }, (0, _react.createElement)(_icon.default, {
    icon: radioCheck,
    size: 24
  })), (0, _react.createElement)(Styled.DropdownMenuItemContentWrapper, null, (0, _react.createElement)(Styled.DropdownMenuItemChildrenWrapper, null, children), suffix && (0, _react.createElement)(Styled.ItemSuffixWrapper, null, suffix)));
});
exports.DropdownMenuRadioItem = DropdownMenuRadioItem;
const DropdownMenuGroup = (0, _element.forwardRef)(function DropdownMenuGroup(props, ref) {
  const dropdownMenuContext = (0, _element.useContext)(DropdownMenuContext);
  return (0, _react.createElement)(Styled.DropdownMenuGroup, {
    ref: ref,
    ...props,
    store: dropdownMenuContext?.store
  });
});
exports.DropdownMenuGroup = DropdownMenuGroup;
const UnconnectedDropdownMenu = (props, ref) => {
  var _props$placement;
  const {
    // Store props
    open,
    defaultOpen = false,
    onOpenChange,
    placement,
    // Menu trigger props
    trigger,
    // Menu props
    gutter,
    children,
    shift,
    modal = true,
    // From internal components context
    variant,
    // Rest
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'DropdownMenu');
  const parentContext = (0, _element.useContext)(DropdownMenuContext);
  const computedDirection = (0, _i18n.isRTL)() ? 'rtl' : 'ltr';

  // If an explicit value for the `placement` prop is not passed,
  // apply a default placement of `bottom-start` for the root dropdown,
  // and of `right-start` for nested dropdowns.
  let computedPlacement = (_props$placement = props.placement) !== null && _props$placement !== void 0 ? _props$placement : parentContext?.store ? 'right-start' : 'bottom-start';
  // Swap left/right in case of RTL direction
  if (computedDirection === 'rtl') {
    if (/right/.test(computedPlacement)) {
      computedPlacement = computedPlacement.replace('right', 'left');
    } else if (/left/.test(computedPlacement)) {
      computedPlacement = computedPlacement.replace('left', 'right');
    }
  }
  const dropdownMenuStore = Ariakit.useMenuStore({
    parent: parentContext?.store,
    open,
    defaultOpen,
    placement: computedPlacement,
    focusLoop: true,
    setOpen(willBeOpen) {
      onOpenChange?.(willBeOpen);
    },
    rtl: computedDirection === 'rtl'
  });
  const contextValue = (0, _element.useMemo)(() => ({
    store: dropdownMenuStore,
    variant
  }), [dropdownMenuStore, variant]);

  // Extract the side from the applied placement — useful for animations.
  const appliedPlacementSide = dropdownMenuStore.useState('placement').split('-')[0];
  if (dropdownMenuStore.parent && !((0, _element.isValidElement)(trigger) && DropdownMenuItem === trigger.type)) {
    // eslint-disable-next-line no-console
    console.warn('For nested DropdownMenus, the `trigger` should always be a `DropdownMenuItem`.');
  }
  const hideOnEscape = (0, _element.useCallback)(event => {
    // Pressing Escape can cause unexpected consequences (ie. exiting
    // full screen mode on MacOs, close parent modals...).
    event.preventDefault();
    // Returning `true` causes the menu to hide.
    return true;
  }, []);
  const wrapperProps = (0, _element.useMemo)(() => ({
    dir: computedDirection,
    style: {
      direction: computedDirection
    }
  }), [computedDirection]);
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(Ariakit.MenuButton, {
    ref: ref,
    store: dropdownMenuStore,
    render: dropdownMenuStore.parent ? (0, _element.cloneElement)(trigger, {
      // Add submenu arrow, unless a `suffix` is explicitly specified
      suffix: (0, _react.createElement)(_react.Fragment, null, trigger.props.suffix, (0, _react.createElement)(Styled.SubmenuChevronIcon, {
        "aria-hidden": "true",
        icon: _icons.chevronRightSmall,
        size: 24,
        preserveAspectRatio: "xMidYMid slice"
      }))
    }) : trigger
  }), (0, _react.createElement)(Styled.DropdownMenu, {
    ...otherProps,
    modal: modal,
    store: dropdownMenuStore
    // Root menu has an 8px distance from its trigger,
    // otherwise 0 (which causes the submenu to slightly overlap)
    ,
    gutter: gutter !== null && gutter !== void 0 ? gutter : dropdownMenuStore.parent ? 0 : 8
    // Align nested menu by the same (but opposite) amount
    // as the menu container's padding.
    ,
    shift: shift !== null && shift !== void 0 ? shift : dropdownMenuStore.parent ? -4 : 0,
    hideOnHoverOutside: false,
    "data-side": appliedPlacementSide,
    variant: variant,
    wrapperProps: wrapperProps,
    hideOnEscape: hideOnEscape,
    unmountOnHide: true
  }, (0, _react.createElement)(DropdownMenuContext.Provider, {
    value: contextValue
  }, children)));
};
const DropdownMenu = (0, _context.contextConnect)(UnconnectedDropdownMenu, 'DropdownMenu');
exports.DropdownMenu = DropdownMenu;
const DropdownMenuSeparator = (0, _element.forwardRef)(function DropdownMenuSeparator(props, ref) {
  const dropdownMenuContext = (0, _element.useContext)(DropdownMenuContext);
  return (0, _react.createElement)(Styled.DropdownMenuSeparator, {
    ref: ref,
    ...props,
    store: dropdownMenuContext?.store,
    variant: dropdownMenuContext?.variant
  });
});
exports.DropdownMenuSeparator = DropdownMenuSeparator;
const DropdownMenuItemLabel = (0, _element.forwardRef)(function DropdownMenuItemLabel(props, ref) {
  return (0, _react.createElement)(Styled.DropdownMenuItemLabel, {
    numberOfLines: 1,
    ref: ref,
    ...props
  });
});
exports.DropdownMenuItemLabel = DropdownMenuItemLabel;
const DropdownMenuItemHelpText = (0, _element.forwardRef)(function DropdownMenuItemHelpText(props, ref) {
  return (0, _react.createElement)(Styled.DropdownMenuItemHelpText, {
    numberOfLines: 2,
    ref: ref,
    ...props
  });
});
exports.DropdownMenuItemHelpText = DropdownMenuItemHelpText;
//# sourceMappingURL=index.js.map