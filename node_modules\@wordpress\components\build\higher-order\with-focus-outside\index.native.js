"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
/**
 * External dependencies
 */
/**
 * WordPress dependencies
 */
var _default = (0, _compose.createHigherOrderComponent)(WrappedComponent => props => {
  const [handleFocusOutside, setHandleFocusOutside] = (0, _element.useState)();
  const bindFocusOutsideHandler = (0, _element.useCallback)(node => setHandleFocusOutside(() => node?.handleFocusOutside ? node.handleFocusOutside.bind(node) : undefined), []);
  return (0, _react.createElement)(_reactNative.View, {
    ...(0, _compose.__experimentalUseFocusOutside)(handleFocusOutside)
  }, (0, _react.createElement)(WrappedComponent, {
    ref: bindFocusOutsideHandler,
    ...props
  }));
}, 'withFocusOutside');
exports.default = _default;
//# sourceMappingURL=index.native.js.map