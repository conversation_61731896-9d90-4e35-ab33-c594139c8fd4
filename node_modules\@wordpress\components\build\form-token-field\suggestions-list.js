"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SuggestionsList = SuggestionsList;
exports.default = void 0;
var _react = require("react");
var _domScrollIntoView = _interopRequireDefault(require("dom-scroll-into-view"));
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

const handleMouseDown = e => {
  // By preventing default here, we will not lose focus of <input> when clicking a suggestion.
  e.preventDefault();
};
function SuggestionsList({
  selectedIndex,
  scrollIntoView,
  match,
  onHover,
  onSelect,
  suggestions = [],
  displayTransform,
  instanceId,
  __experimentalRenderItem
}) {
  const [scrollingIntoView, setScrollingIntoView] = (0, _element.useState)(false);
  const listRef = (0, _compose.useRefEffect)(listNode => {
    // only have to worry about scrolling selected suggestion into view
    // when already expanded.
    let rafId;
    if (selectedIndex > -1 && scrollIntoView && listNode.children[selectedIndex]) {
      setScrollingIntoView(true);
      (0, _domScrollIntoView.default)(listNode.children[selectedIndex], listNode, {
        onlyScrollIfNeeded: true
      });
      rafId = requestAnimationFrame(() => {
        setScrollingIntoView(false);
      });
    }
    return () => {
      if (rafId !== undefined) {
        cancelAnimationFrame(rafId);
      }
    };
  }, [selectedIndex, scrollIntoView]);
  const handleHover = suggestion => {
    return () => {
      if (!scrollingIntoView) {
        onHover?.(suggestion);
      }
    };
  };
  const handleClick = suggestion => {
    return () => {
      onSelect?.(suggestion);
    };
  };
  const computeSuggestionMatch = suggestion => {
    const matchText = displayTransform(match).toLocaleLowerCase();
    if (matchText.length === 0) {
      return null;
    }
    const transformedSuggestion = displayTransform(suggestion);
    const indexOfMatch = transformedSuggestion.toLocaleLowerCase().indexOf(matchText);
    return {
      suggestionBeforeMatch: transformedSuggestion.substring(0, indexOfMatch),
      suggestionMatch: transformedSuggestion.substring(indexOfMatch, indexOfMatch + matchText.length),
      suggestionAfterMatch: transformedSuggestion.substring(indexOfMatch + matchText.length)
    };
  };
  return (0, _react.createElement)("ul", {
    ref: listRef,
    className: "components-form-token-field__suggestions-list",
    id: `components-form-token-suggestions-${instanceId}`,
    role: "listbox"
  }, suggestions.map((suggestion, index) => {
    const matchText = computeSuggestionMatch(suggestion);
    const className = (0, _classnames.default)('components-form-token-field__suggestion', {
      'is-selected': index === selectedIndex
    });
    let output;
    if (typeof __experimentalRenderItem === 'function') {
      output = __experimentalRenderItem({
        item: suggestion
      });
    } else if (matchText) {
      output = (0, _react.createElement)("span", {
        "aria-label": displayTransform(suggestion)
      }, matchText.suggestionBeforeMatch, (0, _react.createElement)("strong", {
        className: "components-form-token-field__suggestion-match"
      }, matchText.suggestionMatch), matchText.suggestionAfterMatch);
    } else {
      output = displayTransform(suggestion);
    }

    /* eslint-disable jsx-a11y/click-events-have-key-events */
    return (0, _react.createElement)("li", {
      id: `components-form-token-suggestions-${instanceId}-${index}`,
      role: "option",
      className: className,
      key: typeof suggestion === 'object' && 'value' in suggestion ? suggestion?.value : displayTransform(suggestion),
      onMouseDown: handleMouseDown,
      onClick: handleClick(suggestion),
      onMouseEnter: handleHover(suggestion),
      "aria-selected": index === selectedIndex
    }, output);
    /* eslint-enable jsx-a11y/click-events-have-key-events */
  }));
}
var _default = SuggestionsList;
exports.default = _default;
//# sourceMappingURL=suggestions-list.js.map