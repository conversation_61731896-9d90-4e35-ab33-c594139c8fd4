{"version": 3, "names": ["_components", "require", "_i18n", "_element", "_utils", "_utils2", "_serializer", "_constants", "_style", "_interopRequireDefault", "CustomGradientPicker", "setColor", "currentValue", "isGradientColor", "gradientOrientation", "setGradientOrientation", "useState", "HORIZONTAL_GRADIENT_ORIENTATION", "currentColor", "setCurrentColor", "getGradientType", "gradients", "gradientOptions", "colorsUtils", "gradientAST", "getGradientAstWithDefault", "gradientType", "isLinearGradient", "type", "linear", "getGradientColor", "orientation", "restGradientAST", "serializeGradient", "onGradientTypeChange", "gradientColor", "setGradientAngle", "value", "getGradientAngle", "_gradientAST$orientat", "DEFAULT_LINEAR_GRADIENT_ANGLE", "_react", "createElement", "Fragment", "PanelBody", "title", "__", "RadioControl", "selected", "options", "onChange", "style", "styles", "angleControl", "RangeControl", "label", "minimumValue", "maximumValue", "_default", "exports", "default"], "sources": ["@wordpress/components/src/custom-gradient-picker/index.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { PanelBody, RadioControl, RangeControl } from '@wordpress/components';\nimport { __ } from '@wordpress/i18n';\nimport { useState } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport { colorsUtils } from '../mobile/color-settings/utils';\nimport { getGradientAstWithDefault } from './utils';\nimport { serializeGradient } from './serializer';\nimport {\n\tDEFAULT_LINEAR_GRADIENT_ANGLE,\n\tHORIZONTAL_GRADIENT_ORIENTATION,\n} from './constants';\nimport styles from './style.scss';\n\nfunction CustomGradientPicker( { setColor, currentValue, isGradientColor } ) {\n\tconst [ gradientOrientation, setGradientOrientation ] = useState(\n\t\tHORIZONTAL_GRADIENT_ORIENTATION\n\t);\n\n\tconst [ currentColor, setCurrentColor ] = useState( currentValue );\n\n\tconst { getGradientType, gradients, gradientOptions } = colorsUtils;\n\tconst { gradientAST } = getGradientAstWithDefault( currentColor );\n\tconst gradientType = getGradientType( currentColor );\n\n\tfunction isLinearGradient( type ) {\n\t\treturn type === gradients.linear;\n\t}\n\n\tfunction getGradientColor( type ) {\n\t\tconst { orientation, ...restGradientAST } = gradientAST;\n\n\t\tif ( orientation ) {\n\t\t\tsetGradientOrientation( orientation );\n\t\t}\n\n\t\treturn serializeGradient(\n\t\t\tisLinearGradient( type )\n\t\t\t\t? {\n\t\t\t\t\t\t...gradientAST,\n\t\t\t\t\t\t...( gradientAST.orientation\n\t\t\t\t\t\t\t? {}\n\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\torientation: gradientOrientation,\n\t\t\t\t\t\t\t  } ),\n\t\t\t\t\t\ttype,\n\t\t\t\t  }\n\t\t\t\t: {\n\t\t\t\t\t\t...restGradientAST,\n\t\t\t\t\t\ttype,\n\t\t\t\t  }\n\t\t);\n\t}\n\n\tfunction onGradientTypeChange( type ) {\n\t\tconst gradientColor = getGradientColor( type );\n\t\tsetCurrentColor( gradientColor );\n\t\tsetColor( gradientColor );\n\t}\n\n\tfunction setGradientAngle( value ) {\n\t\tconst gradientColor = serializeGradient( {\n\t\t\t...gradientAST,\n\t\t\torientation: {\n\t\t\t\ttype: 'angular',\n\t\t\t\tvalue,\n\t\t\t},\n\t\t} );\n\n\t\tif ( isGradientColor && gradientColor !== currentColor ) {\n\t\t\tsetCurrentColor( gradientColor );\n\t\t\tsetColor( gradientColor );\n\t\t}\n\t}\n\n\tfunction getGradientAngle() {\n\t\treturn gradientAST?.orientation?.value ?? DEFAULT_LINEAR_GRADIENT_ANGLE;\n\t}\n\treturn (\n\t\t<>\n\t\t\t<PanelBody title={ __( 'Gradient Type' ) }>\n\t\t\t\t<RadioControl\n\t\t\t\t\tselected={ gradientType }\n\t\t\t\t\toptions={ gradientOptions }\n\t\t\t\t\tonChange={ onGradientTypeChange }\n\t\t\t\t/>\n\t\t\t</PanelBody>\n\t\t\t{ isLinearGradient( gradientType ) && (\n\t\t\t\t<PanelBody style={ styles.angleControl }>\n\t\t\t\t\t<RangeControl\n\t\t\t\t\t\tlabel={ __( 'Angle' ) }\n\t\t\t\t\t\tminimumValue={ 0 }\n\t\t\t\t\t\tmaximumValue={ 360 }\n\t\t\t\t\t\tvalue={ getGradientAngle() }\n\t\t\t\t\t\tonChange={ setGradientAngle }\n\t\t\t\t\t/>\n\t\t\t\t</PanelBody>\n\t\t\t) }\n\t\t</>\n\t);\n}\n\nexport default CustomGradientPicker;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AAIA,IAAAO,MAAA,GAAAC,sBAAA,CAAAR,OAAA;AAjBA;AACA;AACA;;AAKA;AACA;AACA;;AAUA,SAASS,oBAAoBA,CAAE;EAAEC,QAAQ;EAAEC,YAAY;EAAEC;AAAgB,CAAC,EAAG;EAC5E,MAAM,CAAEC,mBAAmB,EAAEC,sBAAsB,CAAE,GAAG,IAAAC,iBAAQ,EAC/DC,0CACD,CAAC;EAED,MAAM,CAAEC,YAAY,EAAEC,eAAe,CAAE,GAAG,IAAAH,iBAAQ,EAAEJ,YAAa,CAAC;EAElE,MAAM;IAAEQ,eAAe;IAAEC,SAAS;IAAEC;EAAgB,CAAC,GAAGC,kBAAW;EACnE,MAAM;IAAEC;EAAY,CAAC,GAAG,IAAAC,iCAAyB,EAAEP,YAAa,CAAC;EACjE,MAAMQ,YAAY,GAAGN,eAAe,CAAEF,YAAa,CAAC;EAEpD,SAASS,gBAAgBA,CAAEC,IAAI,EAAG;IACjC,OAAOA,IAAI,KAAKP,SAAS,CAACQ,MAAM;EACjC;EAEA,SAASC,gBAAgBA,CAAEF,IAAI,EAAG;IACjC,MAAM;MAAEG,WAAW;MAAE,GAAGC;IAAgB,CAAC,GAAGR,WAAW;IAEvD,IAAKO,WAAW,EAAG;MAClBhB,sBAAsB,CAAEgB,WAAY,CAAC;IACtC;IAEA,OAAO,IAAAE,6BAAiB,EACvBN,gBAAgB,CAAEC,IAAK,CAAC,GACrB;MACA,GAAGJ,WAAW;MACd,IAAKA,WAAW,CAACO,WAAW,GACzB,CAAC,CAAC,GACF;QACAA,WAAW,EAAEjB;MACb,CAAC,CAAE;MACNc;IACA,CAAC,GACD;MACA,GAAGI,eAAe;MAClBJ;IACA,CACJ,CAAC;EACF;EAEA,SAASM,oBAAoBA,CAAEN,IAAI,EAAG;IACrC,MAAMO,aAAa,GAAGL,gBAAgB,CAAEF,IAAK,CAAC;IAC9CT,eAAe,CAAEgB,aAAc,CAAC;IAChCxB,QAAQ,CAAEwB,aAAc,CAAC;EAC1B;EAEA,SAASC,gBAAgBA,CAAEC,KAAK,EAAG;IAClC,MAAMF,aAAa,GAAG,IAAAF,6BAAiB,EAAE;MACxC,GAAGT,WAAW;MACdO,WAAW,EAAE;QACZH,IAAI,EAAE,SAAS;QACfS;MACD;IACD,CAAE,CAAC;IAEH,IAAKxB,eAAe,IAAIsB,aAAa,KAAKjB,YAAY,EAAG;MACxDC,eAAe,CAAEgB,aAAc,CAAC;MAChCxB,QAAQ,CAAEwB,aAAc,CAAC;IAC1B;EACD;EAEA,SAASG,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,qBAAA;IAC3B,QAAAA,qBAAA,GAAOf,WAAW,EAAEO,WAAW,EAAEM,KAAK,cAAAE,qBAAA,cAAAA,qBAAA,GAAIC,wCAA6B;EACxE;EACA,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAAC1C,WAAA,CAAA4C,SAAS;IAACC,KAAK,EAAG,IAAAC,QAAE,EAAE,eAAgB;EAAG,GACzC,IAAAL,MAAA,CAAAC,aAAA,EAAC1C,WAAA,CAAA+C,YAAY;IACZC,QAAQ,EAAGtB,YAAc;IACzBuB,OAAO,EAAG3B,eAAiB;IAC3B4B,QAAQ,EAAGhB;EAAsB,CACjC,CACS,CAAC,EACVP,gBAAgB,CAAED,YAAa,CAAC,IACjC,IAAAe,MAAA,CAAAC,aAAA,EAAC1C,WAAA,CAAA4C,SAAS;IAACO,KAAK,EAAGC,cAAM,CAACC;EAAc,GACvC,IAAAZ,MAAA,CAAAC,aAAA,EAAC1C,WAAA,CAAAsD,YAAY;IACZC,KAAK,EAAG,IAAAT,QAAE,EAAE,OAAQ,CAAG;IACvBU,YAAY,EAAG,CAAG;IAClBC,YAAY,EAAG,GAAK;IACpBpB,KAAK,EAAGC,gBAAgB,CAAC,CAAG;IAC5BY,QAAQ,EAAGd;EAAkB,CAC7B,CACS,CAEX,CAAC;AAEL;AAAC,IAAAsB,QAAA,GAEchD,oBAAoB;AAAAiD,OAAA,CAAAC,OAAA,GAAAF,QAAA"}