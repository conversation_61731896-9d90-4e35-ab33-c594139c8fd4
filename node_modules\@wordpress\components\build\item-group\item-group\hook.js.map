{"version": 3, "names": ["_context", "require", "styles", "_interopRequireWildcard", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useItemGroup", "props", "className", "isBordered", "isRounded", "isSeparated", "role", "otherProps", "useContextSystem", "cx", "useCx", "classes", "bordered", "separated", "rounded"], "sources": ["@wordpress/components/src/item-group/item-group/hook.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\n\n/**\n * Internal dependencies\n */\nimport * as styles from '../styles';\nimport { useCx } from '../../utils/hooks/use-cx';\nimport type { ItemGroupProps } from '../types';\n\nexport function useItemGroup(\n\tprops: WordPressComponentProps< ItemGroupProps, 'div' >\n) {\n\tconst {\n\t\tclassName,\n\t\tisBordered = false,\n\t\tisRounded = true,\n\t\tisSeparated = false,\n\t\trole = 'list',\n\t\t...otherProps\n\t} = useContextSystem( props, 'ItemGroup' );\n\n\tconst cx = useCx();\n\n\tconst classes = cx(\n\t\tisBordered && styles.bordered,\n\t\tisSeparated && styles.separated,\n\t\tisRounded && styles.rounded,\n\t\tclassName\n\t);\n\n\treturn {\n\t\tisBordered,\n\t\tclassName: classes,\n\t\trole,\n\t\tisSeparated,\n\t\t...otherProps,\n\t};\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAAiD,SAAAI,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAVjD;AACA;AACA;;AAIA;AACA;AACA;;AAKO,SAASW,YAAYA,CAC3BC,KAAuD,EACtD;EACD,MAAM;IACLC,SAAS;IACTC,UAAU,GAAG,KAAK;IAClBC,SAAS,GAAG,IAAI;IAChBC,WAAW,GAAG,KAAK;IACnBC,IAAI,GAAG,MAAM;IACb,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEP,KAAK,EAAE,WAAY,CAAC;EAE1C,MAAMQ,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAGF,EAAE,CACjBN,UAAU,IAAI5B,MAAM,CAACqC,QAAQ,EAC7BP,WAAW,IAAI9B,MAAM,CAACsC,SAAS,EAC/BT,SAAS,IAAI7B,MAAM,CAACuC,OAAO,EAC3BZ,SACD,CAAC;EAED,OAAO;IACNC,UAAU;IACVD,SAAS,EAAES,OAAO;IAClBL,IAAI;IACJD,WAAW;IACX,GAAGE;EACJ,CAAC;AACF"}