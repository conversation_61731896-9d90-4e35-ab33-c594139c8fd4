{"version": 3, "names": [], "sources": ["@wordpress/components/src/gradient-picker/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { HeadingSize } from '../heading/types';\n\nexport type GradientObject = {\n\tgradient: string;\n\tname: string;\n\tslug: string;\n};\nexport type OriginObject = { name: string; gradients: GradientObject[] };\nexport type GradientsProp = GradientObject[] | OriginObject[];\n\ntype GradientPickerBaseProps = {\n\t/**\n\t * The class name added to the wrapper.\n\t */\n\tclassName?: string;\n\t/**\n\t * The function called when a new gradient has been defined. It is passed to\n\t * the `currentGradient` as an argument.\n\t */\n\tonChange: ( currentGradient: string | undefined ) => void;\n\t/**\n\t * The current value of the gradient. Pass a css gradient string (See default value for example).\n\t * Optionally pass in a `null` value to specify no gradient is currently selected.\n\t *\n\t * @default 'linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%)'\n\t */\n\tvalue?: GradientObject[ 'gradient' ] | null;\n\t/**\n\t * Whether the palette should have a clearing button or not.\n\t *\n\t * @default true\n\t */\n\tclearable?: boolean;\n\t/**\n\t * The heading level. Only applies in cases where gradients are provided\n\t * from multiple origins (ie. when the array passed as the `gradients` prop\n\t * contains two or more items).\n\t *\n\t * @default 2\n\t */\n\theadingLevel?: HeadingSize;\n\t/**\n\t * Whether the control should present as a set of buttons,\n\t * each with its own tab stop.\n\t *\n\t * @default false\n\t */\n\tasButtons?: boolean;\n\t/**\n\t * Prevents keyboard interaction from wrapping around.\n\t * Only used when `asButtons` is not true.\n\t *\n\t * @default true\n\t */\n\tloop?: boolean;\n} & (\n\t| {\n\t\t\t/**\n\t\t\t * A label to identify the purpose of the control.\n\t\t\t *\n\t\t\t * @todo [#54055] Either this or `aria-labelledby` should be required\n\t\t\t */\n\t\t\t'aria-label'?: string;\n\t\t\t'aria-labelledby'?: never;\n\t  }\n\t| {\n\t\t\t/**\n\t\t\t * An ID of an element to provide a label for the control.\n\t\t\t *\n\t\t\t * @todo [#54055] Either this or `aria-label` should be required\n\t\t\t */\n\t\t\t'aria-labelledby'?: string;\n\t\t\t'aria-label'?: never;\n\t  }\n);\n\nexport type GradientPickerComponentProps = GradientPickerBaseProps & {\n\t/**\n\t * An array of objects as predefined gradients displayed above the gradient\n\t * selector. Alternatively, if there are multiple sets (or 'origins') of\n\t * gradients, you can pass an array of objects each with a `name` and a\n\t * `gradients` array which will in turn contain the predefined gradient objects.\n\t *\n\t * @default []\n\t */\n\tgradients?: GradientsProp;\n\t/**\n\t * Start opting in to the new margin-free styles that will become the default\n\t * in a future version, currently scheduled to be WordPress 6.4. (The prop\n\t * can be safely removed once this happens.)\n\t *\n\t * @default false\n\t */\n\t__nextHasNoMargin?: boolean;\n\t/**\n\t * If true, the gradient picker will not be displayed and only defined\n\t * gradients from `gradients` will be shown.\n\t *\n\t * @default false\n\t */\n\tdisableCustomGradients?: boolean;\n\t/**\n\t * Whether this is rendered in the sidebar.\n\t *\n\t * @default false\n\t */\n\t__experimentalIsRenderedInSidebar?: boolean;\n};\n\nexport type PickerProps< TOriginType extends GradientObject | OriginObject > =\n\tGradientPickerBaseProps & {\n\t\tclearGradient: () => void;\n\t\tonChange: (\n\t\t\tcurrentGradient: string | undefined,\n\t\t\tindex: number\n\t\t) => void;\n\t\tactions?: React.ReactNode;\n\t\tgradients: TOriginType[];\n\t};\n"], "mappings": ""}