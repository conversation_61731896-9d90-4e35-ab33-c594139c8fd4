{"version": 3, "names": ["_reactNative", "require", "_icons", "_compose", "_style", "_interopRequireDefault", "Stepper", "getStylesFromColorScheme", "isMaxValue", "isMinValue", "onPressInDecrement", "onPressInIncrement", "onPressOut", "value", "shouldDisplayTextInput", "children", "valueStyle", "styles", "valueTextDark", "buttonIconStyle", "buttonNoBg", "buttonNoBgTextDark", "_react", "createElement", "View", "style", "container", "TouchableOpacity", "disabled", "onPressIn", "opacity", "Icon", "icon", "chevronDown", "size", "color", "Text", "spacings", "testID", "chevronUp", "_default", "withPreferredColorScheme", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/stepper-cell/stepper.android.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Text, TouchableOpacity, View } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { Icon, chevronDown, chevronUp } from '@wordpress/icons';\nimport { withPreferredColorScheme } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nfunction Stepper( {\n\tgetStylesFromColorScheme,\n\tisMaxValue,\n\tisMinValue,\n\tonPressInDecrement,\n\tonPressInIncrement,\n\tonPressOut,\n\tvalue,\n\tshouldDisplayTextInput,\n\tchildren,\n} ) {\n\tconst valueStyle = getStylesFromColorScheme(\n\t\tstyles.value,\n\t\tstyles.valueTextDark\n\t);\n\tconst buttonIconStyle = getStylesFromColorScheme(\n\t\tstyles.buttonNoBg,\n\t\tstyles.buttonNoBgTextDark\n\t);\n\n\treturn (\n\t\t<View style={ styles.container }>\n\t\t\t<TouchableOpacity\n\t\t\t\tdisabled={ isMinValue }\n\t\t\t\tonPressIn={ onPressInDecrement }\n\t\t\t\tonPressOut={ onPressOut }\n\t\t\t\tstyle={ [\n\t\t\t\t\tstyles.buttonNoBg,\n\t\t\t\t\tisMinValue ? { opacity: 0.4 } : null,\n\t\t\t\t] }\n\t\t\t>\n\t\t\t\t<Icon\n\t\t\t\t\ticon={ chevronDown }\n\t\t\t\t\tsize={ 24 }\n\t\t\t\t\tcolor={ buttonIconStyle.color }\n\t\t\t\t/>\n\t\t\t</TouchableOpacity>\n\t\t\t{ ! shouldDisplayTextInput && (\n\t\t\t\t<Text style={ [ valueStyle, styles.spacings ] }>{ value }</Text>\n\t\t\t) }\n\t\t\t{ children }\n\t\t\t<TouchableOpacity\n\t\t\t\ttestID={ 'Increment' }\n\t\t\t\tdisabled={ isMaxValue }\n\t\t\t\tonPressIn={ onPressInIncrement }\n\t\t\t\tonPressOut={ onPressOut }\n\t\t\t\tstyle={ [\n\t\t\t\t\tstyles.buttonNoBg,\n\t\t\t\t\tisMaxValue ? { opacity: 0.4 } : null,\n\t\t\t\t] }\n\t\t\t>\n\t\t\t\t<Icon\n\t\t\t\t\ticon={ chevronUp }\n\t\t\t\t\tsize={ 24 }\n\t\t\t\t\tcolor={ buttonIconStyle.color }\n\t\t\t\t/>\n\t\t\t</TouchableOpacity>\n\t\t</View>\n\t);\n}\n\nexport default withPreferredColorScheme( Stepper );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,MAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAdA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAGA,SAASK,OAAOA,CAAE;EACjBC,wBAAwB;EACxBC,UAAU;EACVC,UAAU;EACVC,kBAAkB;EAClBC,kBAAkB;EAClBC,UAAU;EACVC,KAAK;EACLC,sBAAsB;EACtBC;AACD,CAAC,EAAG;EACH,MAAMC,UAAU,GAAGT,wBAAwB,CAC1CU,cAAM,CAACJ,KAAK,EACZI,cAAM,CAACC,aACR,CAAC;EACD,MAAMC,eAAe,GAAGZ,wBAAwB,CAC/CU,cAAM,CAACG,UAAU,EACjBH,cAAM,CAACI,kBACR,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAAwB,IAAI;IAACC,KAAK,EAAGR,cAAM,CAACS;EAAW,GAC/B,IAAAJ,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAA2B,gBAAgB;IAChBC,QAAQ,EAAGnB,UAAY;IACvBoB,SAAS,EAAGnB,kBAAoB;IAChCE,UAAU,EAAGA,UAAY;IACzBa,KAAK,EAAG,CACPR,cAAM,CAACG,UAAU,EACjBX,UAAU,GAAG;MAAEqB,OAAO,EAAE;IAAI,CAAC,GAAG,IAAI;EAClC,GAEH,IAAAR,MAAA,CAAAC,aAAA,EAACrB,MAAA,CAAA6B,IAAI;IACJC,IAAI,EAAGC,kBAAa;IACpBC,IAAI,EAAG,EAAI;IACXC,KAAK,EAAGhB,eAAe,CAACgB;EAAO,CAC/B,CACgB,CAAC,EACjB,CAAErB,sBAAsB,IACzB,IAAAQ,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAAoC,IAAI;IAACX,KAAK,EAAG,CAAET,UAAU,EAAEC,cAAM,CAACoB,QAAQ;EAAI,GAAGxB,KAAa,CAC/D,EACCE,QAAQ,EACV,IAAAO,MAAA,CAAAC,aAAA,EAACvB,YAAA,CAAA2B,gBAAgB;IAChBW,MAAM,EAAG,WAAa;IACtBV,QAAQ,EAAGpB,UAAY;IACvBqB,SAAS,EAAGlB,kBAAoB;IAChCC,UAAU,EAAGA,UAAY;IACzBa,KAAK,EAAG,CACPR,cAAM,CAACG,UAAU,EACjBZ,UAAU,GAAG;MAAEsB,OAAO,EAAE;IAAI,CAAC,GAAG,IAAI;EAClC,GAEH,IAAAR,MAAA,CAAAC,aAAA,EAACrB,MAAA,CAAA6B,IAAI;IACJC,IAAI,EAAGO,gBAAW;IAClBL,IAAI,EAAG,EAAI;IACXC,KAAK,EAAGhB,eAAe,CAACgB;EAAO,CAC/B,CACgB,CACb,CAAC;AAET;AAAC,IAAAK,QAAA,GAEc,IAAAC,iCAAwB,EAAEnC,OAAQ,CAAC;AAAAoC,OAAA,CAAAC,OAAA,GAAAH,QAAA"}