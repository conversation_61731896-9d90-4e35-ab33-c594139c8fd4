/**
 * Script de test pour vérifier les corrections des APIs Boss SEO
 * À exécuter dans la console du navigateur après les corrections
 */

console.log('🔧 Test des Corrections APIs Boss SEO');
console.log('====================================');

// Fonction pour tester une API avec gestion d'erreurs détaillée
async function testAPIFixed(url, method = 'POST', data = null, name = '') {
    console.log(`\n🔍 Test: ${name || url}`);
    
    try {
        const options = {
            method: method,
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        };
        
        if (data && method !== 'GET') {
            options.headers['Content-Type'] = 'application/json';
            options.body = JSON.stringify(data);
        }
        
        console.log('📤 Envoi de la requête...');
        const response = await fetch(url, options);
        
        console.log(`📥 Statut: ${response.status} ${response.statusText}`);
        
        // Lire la réponse comme texte d'abord
        const responseText = await response.text();
        
        // Essayer de parser en JSON
        let jsonData = null;
        try {
            jsonData = JSON.parse(responseText);
            console.log('✅ JSON valide reçu');
            
            if (jsonData.success) {
                console.log(`🎉 ${name}: SUCCÈS`);
                if (jsonData.message) {
                    console.log(`   Message: ${jsonData.message}`);
                }
                if (jsonData.data) {
                    console.log(`   Données: ${Object.keys(jsonData.data).length} propriétés`);
                }
            } else {
                console.log(`⚠️ ${name}: Réponse avec erreur`);
                console.log(`   Message: ${jsonData.message || 'Aucun message'}`);
            }
            
        } catch (jsonError) {
            console.log('❌ JSON invalide:', jsonError.message);
            console.log('📄 Réponse brute:', responseText.substring(0, 200) + '...');
        }
        
        return {
            success: response.ok && jsonData && jsonData.success,
            status: response.status,
            data: jsonData,
            rawResponse: responseText
        };
        
    } catch (error) {
        console.log(`💥 Erreur de requête: ${error.message}`);
        return {
            success: false,
            error: error.message
        };
    }
}

// Tester les APIs qui posaient problème
async function testFixedAPIs() {
    console.log('\n🚨 Test des APIs corrigées...\n');
    
    const fixedAPIs = [
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate',
            method: 'POST',
            name: 'Régénération Sitemap (CORRIGÉE)'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings',
            method: 'POST',
            data: { 
                enabled: true,
                includedPostTypes: ['post', 'page'],
                defaultChangeFreq: 'weekly',
                defaultPriority: 0.7
            },
            name: 'Sauvegarde Paramètres (CORRIGÉE)'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats',
            method: 'GET',
            name: 'Stats Dashboard'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/ping/all',
            method: 'POST',
            name: 'Ping Moteurs'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/custom-urls/all',
            method: 'GET',
            name: 'URLs Personnalisées'
        }
    ];
    
    const results = {};
    let successCount = 0;
    
    for (const api of fixedAPIs) {
        const result = await testAPIFixed(api.url, api.method, api.data, api.name);
        results[api.name] = result;
        
        if (result.success) {
            successCount++;
        }
        
        // Petite pause entre les requêtes
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n📊 === RÉSULTATS DES CORRECTIONS ===');
    console.log(`✅ APIs fonctionnelles: ${successCount}/${fixedAPIs.length}`);
    console.log(`📈 Taux de réussite: ${Math.round((successCount / fixedAPIs.length) * 100)}%`);
    
    if (successCount === fixedAPIs.length) {
        console.log('\n🎉 TOUTES LES CORRECTIONS SONT OPÉRATIONNELLES !');
        console.log('✅ Les erreurs 500 ont été corrigées');
        console.log('✅ Les réponses JSON sont maintenant valides');
    } else {
        console.log('\n⚠️ Quelques APIs nécessitent encore des corrections');
        
        Object.entries(results).forEach(([name, result]) => {
            if (!result.success) {
                console.log(`❌ ${name}: ${result.error || 'Erreur inconnue'}`);
            }
        });
    }
    
    return results;
}

// Tester les nouvelles APIs Phase 3
async function testPhase3APIs() {
    console.log('\n🚀 Test des APIs Phase 3...\n');
    
    const phase3APIs = [
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/smart-optimization/priorities',
            method: 'GET',
            name: 'Priorités Intelligentes'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/search-console/data',
            method: 'GET',
            name: 'Données Search Console'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/cache/stats',
            method: 'GET',
            name: 'Statistiques Cache'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/reports/templates',
            method: 'GET',
            name: 'Templates Rapports'
        }
    ];
    
    let phase3Success = 0;
    
    for (const api of phase3APIs) {
        const result = await testAPIFixed(api.url, api.method, null, api.name);
        
        if (result.success) {
            phase3Success++;
        }
        
        await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    console.log(`\n📊 APIs Phase 3: ${phase3Success}/${phase3APIs.length} fonctionnelles`);
    
    return phase3Success === phase3APIs.length;
}

// Test complet avec rapport détaillé
async function runCompleteFixTest() {
    console.log('🚀 Démarrage du test complet des corrections...\n');
    
    const startTime = Date.now();
    
    try {
        // 1. Tester les APIs corrigées
        console.log('🔧 === PHASE 1: APIS CORRIGÉES ===');
        const fixedResults = await testFixedAPIs();
        
        // 2. Tester les APIs Phase 3
        console.log('\n🚀 === PHASE 2: APIS PHASE 3 ===');
        const phase3Success = await testPhase3APIs();
        
        // 3. Test d'intégration
        console.log('\n🔄 === PHASE 3: TEST D\'INTÉGRATION ===');
        console.log('Test du workflow complet...');
        
        // Workflow: Dashboard → Régénération → Ping
        const workflowResults = [];
        
        // Étape 1: Récupérer les stats
        const statsResult = await testAPIFixed(
            '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats',
            'GET',
            null,
            'Stats Dashboard (Workflow)'
        );
        workflowResults.push(statsResult.success);
        
        // Étape 2: Régénérer les sitemaps
        const regenResult = await testAPIFixed(
            '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate',
            'POST',
            null,
            'Régénération (Workflow)'
        );
        workflowResults.push(regenResult.success);
        
        // Étape 3: Ping les moteurs
        const pingResult = await testAPIFixed(
            '/wp-json/boss-seo/v1/robots-sitemap/ping/all',
            'POST',
            null,
            'Ping Moteurs (Workflow)'
        );
        workflowResults.push(pingResult.success);
        
        const workflowSuccess = workflowResults.filter(r => r).length;
        console.log(`✅ Workflow: ${workflowSuccess}/3 étapes réussies`);
        
        const endTime = Date.now();
        
        // Rapport final
        console.log('\n🎯 === RAPPORT FINAL DES CORRECTIONS ===');
        console.log(`⏱️  Durée totale: ${endTime - startTime}ms`);
        
        const fixedCount = Object.values(fixedResults).filter(r => r.success).length;
        const fixedTotal = Object.keys(fixedResults).length;
        
        console.log(`🔧 APIs corrigées: ${fixedCount}/${fixedTotal}`);
        console.log(`🚀 APIs Phase 3: ${phase3Success ? 'Toutes fonctionnelles' : 'Quelques problèmes'}`);
        console.log(`🔄 Workflow intégré: ${workflowSuccess}/3 étapes`);
        
        const overallSuccess = fixedCount === fixedTotal && phase3Success && workflowSuccess === 3;
        
        if (overallSuccess) {
            console.log('\n🎉 === SUCCÈS COMPLET ! ===');
            console.log('✅ Toutes les erreurs 500 ont été corrigées');
            console.log('✅ Toutes les APIs retournent du JSON valide');
            console.log('✅ Le système Boss SEO est entièrement opérationnel');
            console.log('🚀 Prêt pour la production !');
        } else {
            console.log('\n⚠️ === CORRECTIONS PARTIELLES ===');
            console.log('🔧 Quelques APIs nécessitent encore des ajustements');
            console.log('📋 Consultez les détails ci-dessus');
        }
        
        return {
            success: overallSuccess,
            fixed: fixedResults,
            phase3: phase3Success,
            workflow: workflowSuccess
        };
        
    } catch (error) {
        console.log('\n💥 Erreur lors du test des corrections:', error);
        return { success: false, error: error.message };
    }
}

// Fonction pour tester une API spécifique après correction
async function testSpecificFix(url, method = 'GET', data = null) {
    console.log(`\n🎯 Test spécifique après correction: ${method} ${url}\n`);
    return await testAPIFixed(url, method, data, 'Test Spécifique');
}

// Rendre les fonctions disponibles globalement
window.testAPIFixes = runCompleteFixTest;
window.testFixedAPI = testSpecificFix;
window.testFixed = testFixedAPIs;

console.log('\n🛠️ Fonctions de test des corrections disponibles:');
console.log('- testAPIFixes() - Test complet de toutes les corrections');
console.log('- testFixed() - Tester uniquement les APIs corrigées');
console.log('- testFixedAPI(url, method, data) - Tester une API spécifique');

console.log('\n🎯 Pour tester les corrections, tapez: testAPIFixes()');
console.log('💡 Ou testez une API spécifique: testFixedAPI("/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate", "POST")');
