"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getCommonSizeUnit = getCommonSizeUnit;
exports.isSimpleCssValue = isSimpleCssValue;
var _unitControl = require("../unit-control");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * Some themes use css vars for their font sizes, so until we
 * have the way of calculating them don't display them.
 *
 * @param value The value that is checked.
 * @return Whether the value is a simple css value.
 */
function isSimpleCssValue(value) {
  const sizeRegex = /^[\d\.]+(px|em|rem|vw|vh|%|svw|lvw|dvw|svh|lvh|dvh|vi|svi|lvi|dvi|vb|svb|lvb|dvb|vmin|svmin|lvmin|dvmin|vmax|svmax|lvmax|dvmax)?$/i;
  return sizeRegex.test(String(value));
}

/**
 * If all of the given font sizes have the same unit (e.g. 'px'), return that
 * unit. Otherwise return null.
 *
 * @param fontSizes List of font sizes.
 * @return The common unit, or null.
 */
function getCommonSizeUnit(fontSizes) {
  const [firstFontSize, ...otherFontSizes] = fontSizes;
  if (!firstFontSize) {
    return null;
  }
  const [, firstUnit] = (0, _unitControl.parseQuantityAndUnitFromRawValue)(firstFontSize.size);
  const areAllSizesSameUnit = otherFontSizes.every(fontSize => {
    const [, unit] = (0, _unitControl.parseQuantityAndUnitFromRawValue)(fontSize.size);
    return unit === firstUnit;
  });
  return areAllSizesSameUnit ? firstUnit : null;
}
//# sourceMappingURL=utils.js.map