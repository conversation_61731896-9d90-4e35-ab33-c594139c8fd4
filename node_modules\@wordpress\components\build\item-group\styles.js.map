{"version": 3, "names": ["_react", "require", "_utils", "_EMOTION_STRINGIFIED_CSS_ERROR__", "unstyledButton", "as", "css", "font", "undefined", "COLORS", "theme", "accent", "process", "env", "NODE_ENV", "exports", "itemWrapper", "name", "styles", "map", "toString", "item", "bordered", "CONFIG", "surfaceBorderColor", "separated", "borderRadius", "controlBorderRadius", "spacedAround", "rounded", "baseFontHeight", "fontSize", "fontLineHeightBase", "paddingY", "controlHeight", "paddingYSmall", "controlHeightSmall", "paddingYLarge", "controlHeightLarge", "itemSizes", "small", "controlPaddingXSmall", "medium", "controlPaddingX", "large", "controlPaddingXLarge"], "sources": ["@wordpress/components/src/item-group/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { CONFIG, COLORS, font } from '../utils';\n\nexport const unstyledButton = ( as: 'a' | 'button' ) => {\n\treturn css`\n\t\tfont-size: ${ font( 'default.fontSize' ) };\n\t\tfont-family: inherit;\n\t\tappearance: none;\n\t\tborder: 1px solid transparent;\n\t\tcursor: pointer;\n\t\tbackground: none;\n\t\ttext-align: start;\n\t\ttext-decoration: ${ as === 'a' ? 'none' : undefined };\n\n\t\tsvg,\n\t\tpath {\n\t\t\tfill: currentColor;\n\t\t}\n\n\t\t&:hover {\n\t\t\tcolor: ${ COLORS.theme.accent };\n\t\t}\n\n\t\t&:focus {\n\t\t\tbox-shadow: none;\n\t\t\toutline: none;\n\t\t}\n\n\t\t&:focus-visible {\n\t\t\tbox-shadow: 0 0 0 var( --wp-admin-border-width-focus )\n\t\t\t\tvar(\n\t\t\t\t\t--wp-components-color-accent,\n\t\t\t\t\tvar( --wp-admin-theme-color, ${ COLORS.theme.accent } )\n\t\t\t\t);\n\t\t\t// Windows high contrast mode.\n\t\t\toutline: 2px solid transparent;\n\t\t\toutline-offset: 0;\n\t\t}\n\t`;\n};\n\nexport const itemWrapper = css`\n\twidth: 100%;\n\tdisplay: block;\n`;\n\nexport const item = css`\n\tbox-sizing: border-box;\n\twidth: 100%;\n\tdisplay: block;\n\tmargin: 0;\n\tcolor: inherit;\n`;\n\nexport const bordered = css`\n\tborder: 1px solid ${ CONFIG.surfaceBorderColor };\n`;\n\nexport const separated = css`\n\t> *:not( marquee ) > * {\n\t\tborder-bottom: 1px solid ${ CONFIG.surfaceBorderColor };\n\t}\n\n\t> *:last-of-type > *:not( :focus ) {\n\t\tborder-bottom-color: transparent;\n\t}\n`;\n\nconst borderRadius = CONFIG.controlBorderRadius;\n\nexport const spacedAround = css`\n\tborder-radius: ${ borderRadius };\n`;\n\nexport const rounded = css`\n\tborder-radius: ${ borderRadius };\n\n\t> *:first-of-type > * {\n\t\tborder-top-left-radius: ${ borderRadius };\n\t\tborder-top-right-radius: ${ borderRadius };\n\t}\n\n\t> *:last-of-type > * {\n\t\tborder-bottom-left-radius: ${ borderRadius };\n\t\tborder-bottom-right-radius: ${ borderRadius };\n\t}\n`;\n\nconst baseFontHeight = `calc(${ CONFIG.fontSize } * ${ CONFIG.fontLineHeightBase })`;\n\n/*\n * Math:\n * - Use the desired height as the base value\n * - Subtract the computed height of (default) text\n * - Subtract the effects of border\n * - Divide the calculated number by 2, in order to get an individual top/bottom padding\n */\nconst paddingY = `calc((${ CONFIG.controlHeight } - ${ baseFontHeight } - 2px) / 2)`;\nconst paddingYSmall = `calc((${ CONFIG.controlHeightSmall } - ${ baseFontHeight } - 2px) / 2)`;\nconst paddingYLarge = `calc((${ CONFIG.controlHeightLarge } - ${ baseFontHeight } - 2px) / 2)`;\n\nexport const itemSizes = {\n\tsmall: css`\n\t\tpadding: ${ paddingYSmall } ${ CONFIG.controlPaddingXSmall };\n\t`,\n\tmedium: css`\n\t\tpadding: ${ paddingY } ${ CONFIG.controlPaddingX };\n\t`,\n\tlarge: css`\n\t\tpadding: ${ paddingYLarge } ${ CONFIG.controlPaddingXLarge };\n\t`,\n};\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AAAgD,SAAAE,iCAAA;AAEzC,MAAMC,cAAc,GAAKC,EAAkB,IAAM;EACvD,wBAAOC,UAAG,gBACK,IAAAC,WAAI,EAAE,kBAAmB,CAAC,wIAOpBF,EAAE,KAAK,GAAG,GAAG,MAAM,GAAGG,SAAS,iDAQxCC,aAAM,CAACC,KAAK,CAACC,MAAM,0MAYKF,aAAM,CAACC,KAAK,CAACC,MAAM,sEAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAOxD,CAAC;AAACC,OAAA,CAAAX,cAAA,GAAAA,cAAA;AAEK,MAAMY,WAAW,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAjB;AAAA,CAGvB;AAACY,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAEK,MAAMK,IAAI,GAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAjB;AAAA,CAMhB;AAACY,OAAA,CAAAM,IAAA,GAAAA,IAAA;AAEK,MAAMC,QAAQ,oBAAGhB,UAAG,uBACLiB,aAAM,CAACC,kBAAkB,SAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,8CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,2oIAC9C;AAACC,OAAA,CAAAO,QAAA,GAAAA,QAAA;AAEK,MAAMG,SAAS,oBAAGnB,UAAG,kDAEEiB,aAAM,CAACC,kBAAkB,2EAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,+CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,2oIAMtD;AAACC,OAAA,CAAAU,SAAA,GAAAA,SAAA;AAEF,MAAMC,YAAY,GAAGH,aAAM,CAACI,mBAAmB;AAExC,MAAMC,YAAY,oBAAGtB,UAAG,oBACZoB,YAAY,SAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA,kDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,2oIAC9B;AAACC,OAAA,CAAAa,YAAA,GAAAA,YAAA;AAEK,MAAMC,OAAO,oBAAGvB,UAAG,oBACPoB,YAAY,iDAGFA,YAAY,+BACXA,YAAY,oDAIVA,YAAY,kCACXA,YAAY,UAAAd,OAAA,CAAAC,GAAA,CAAAC,QAAA,6CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,2oIAE5C;AAACC,OAAA,CAAAc,OAAA,GAAAA,OAAA;AAEF,MAAMC,cAAc,GAAI,QAAQP,aAAM,CAACQ,QAAU,MAAMR,aAAM,CAACS,kBAAoB,GAAE;;AAEpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAI,SAASV,aAAM,CAACW,aAAe,MAAMJ,cAAgB,cAAa;AACpF,MAAMK,aAAa,GAAI,SAASZ,aAAM,CAACa,kBAAoB,MAAMN,cAAgB,cAAa;AAC9F,MAAMO,aAAa,GAAI,SAASd,aAAM,CAACe,kBAAoB,MAAMR,cAAgB,cAAa;AAEvF,MAAMS,SAAS,GAAG;EACxBC,KAAK,mBAAElC,UAAG,cACG6B,aAAa,OAAMZ,aAAM,CAACkB,oBAAoB,SAAA7B,OAAA,CAAAC,GAAA,CAAAC,QAAA,2CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,uoIAC1D;EACD4B,MAAM,mBAAEpC,UAAG,cACE2B,QAAQ,OAAMV,aAAM,CAACoB,eAAe,SAAA/B,OAAA,CAAAC,GAAA,CAAAC,QAAA,4CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,uoIAChD;EACD8B,KAAK,mBAAEtC,UAAG,cACG+B,aAAa,OAAMd,aAAM,CAACsB,oBAAoB,SAAAjC,OAAA,CAAAC,GAAA,CAAAC,QAAA,2CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAE5D,CAAC;AAACC,OAAA,CAAAwB,SAAA,GAAAA,SAAA"}