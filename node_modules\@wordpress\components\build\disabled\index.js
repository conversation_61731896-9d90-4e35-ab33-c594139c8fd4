"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
var _disabledStyles = require("./styles/disabled-styles");
var _utils = require("../utils");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const Context = (0, _element.createContext)(false);
const {
  Consumer,
  Provider
} = Context;

/**
 * `Disabled` is a component which disables descendant tabbable elements and
 * prevents pointer interaction.
 *
 * _Note: this component may not behave as expected in browsers that don't
 * support the `inert` HTML attribute. We recommend adding the official WICG
 * polyfill when using this component in your project._
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/inert
 *
 * ```jsx
 * import { Button, Disabled, TextControl } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyDisabled = () => {
 * 	const [ isDisabled, setIsDisabled ] = useState( true );
 *
 * 	let input = <TextControl label="Input" onChange={ () => {} } />;
 * 	if ( isDisabled ) {
 * 		input = <Disabled>{ input }</Disabled>;
 * 	}
 *
 * 	const toggleDisabled = () => {
 * 		setIsDisabled( ( state ) => ! state );
 * 	};
 *
 * 	return (
 * 		<div>
 * 			{ input }
 * 			<Button variant="primary" onClick={ toggleDisabled }>
 * 				Toggle Disabled
 * 			</Button>
 * 		</div>
 * 	);
 * };
 * ```
 */
function Disabled({
  className,
  children,
  isDisabled = true,
  ...props
}) {
  const cx = (0, _utils.useCx)();
  return (0, _react.createElement)(Provider, {
    value: isDisabled
  }, (0, _react.createElement)("div", {
    // @ts-ignore Reason: inert is a recent HTML attribute
    inert: isDisabled ? 'true' : undefined,
    className: isDisabled ? cx(_disabledStyles.disabledStyles, className, 'components-disabled') : undefined,
    ...props
  }, children));
}
Disabled.Context = Context;
Disabled.Consumer = Consumer;
var _default = Disabled;
exports.default = _default;
//# sourceMappingURL=index.js.map