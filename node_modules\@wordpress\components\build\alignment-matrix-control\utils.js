"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GRID = exports.ALIGNMENT_LABEL = exports.ALIGNMENTS = void 0;
exports.getAlignmentIndex = getAlignmentIndex;
exports.getItemId = getItemId;
exports.getItemValue = getItemValue;
var _i18n = require("@wordpress/i18n");
/**
 * WordPress dependencies
 */

const GRID = [['top left', 'top center', 'top right'], ['center left', 'center center', 'center right'], ['bottom left', 'bottom center', 'bottom right']];

// Stored as map as i18n __() only accepts strings (not variables)
exports.GRID = GRID;
const ALIGNMENT_LABEL = {
  'top left': (0, _i18n.__)('Top Left'),
  'top center': (0, _i18n.__)('Top Center'),
  'top right': (0, _i18n.__)('Top Right'),
  'center left': (0, _i18n.__)('Center Left'),
  'center center': (0, _i18n.__)('Center'),
  center: (0, _i18n.__)('Center'),
  'center right': (0, _i18n.__)('Center Right'),
  'bottom left': (0, _i18n.__)('Bottom Left'),
  'bottom center': (0, _i18n.__)('Bottom Center'),
  'bottom right': (0, _i18n.__)('Bottom Right')
};

// Transforms GRID into a flat Array of values.
exports.ALIGNMENT_LABEL = ALIGNMENT_LABEL;
const ALIGNMENTS = GRID.flat();

/**
 * Normalizes and transforms an incoming value to better match the alignment values
 *
 * @param value An alignment value to parse.
 *
 * @return The parsed value.
 */
exports.ALIGNMENTS = ALIGNMENTS;
function normalize(value) {
  const normalized = value === 'center' ? 'center center' : value;

  // Strictly speaking, this could be `string | null | undefined`,
  // but will be validated shortly, so we're typecasting to an
  // `AlignmentMatrixControlValue` to keep TypeScript happy.
  const transformed = normalized?.replace('-', ' ');
  return ALIGNMENTS.includes(transformed) ? transformed : undefined;
}

/**
 * Creates an item ID based on a prefix ID and an alignment value.
 *
 * @param prefixId An ID to prefix.
 * @param value    An alignment value.
 *
 * @return The item id.
 */
function getItemId(prefixId, value) {
  const normalized = normalize(value);
  if (!normalized) return;
  const id = normalized.replace(' ', '-');
  return `${prefixId}-${id}`;
}

/**
 * Extracts an item value from its ID
 *
 * @param prefixId An ID prefix to remove
 * @param id       An item ID
 * @return         The item value
 */
function getItemValue(prefixId, id) {
  const value = id?.replace(prefixId + '-', '');
  return normalize(value);
}

/**
 * Retrieves the alignment index from a value.
 *
 * @param alignment Value to check.
 *
 * @return The index of a matching alignment.
 */
function getAlignmentIndex(alignment = 'center') {
  const normalized = normalize(alignment);
  if (!normalized) return undefined;
  const index = ALIGNMENTS.indexOf(normalized);
  return index > -1 ? index : undefined;
}
//# sourceMappingURL=utils.js.map