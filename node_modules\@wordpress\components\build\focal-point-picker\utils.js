"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.INITIAL_BOUNDS = void 0;
exports.fractionToPercentage = fractionToPercentage;
exports.getExtension = getExtension;
exports.isVideoType = isVideoType;
const INITIAL_BOUNDS = {
  width: 200,
  height: 170
};
exports.INITIAL_BOUNDS = INITIAL_BOUNDS;
const VIDEO_EXTENSIONS = ['avi', 'mpg', 'mpeg', 'mov', 'mp4', 'm4v', 'ogg', 'ogv', 'webm', 'wmv'];

/**
 * Gets the extension of a file name.
 *
 * @param filename The file name.
 * @return  The extension of the file name.
 */
function getExtension(filename = '') {
  const parts = filename.split('.');
  return parts[parts.length - 1];
}

/**
 * Checks if a file is a video.
 *
 * @param filename The file name.
 * @return Whether the file is a video.
 */
function isVideoType(filename = '') {
  if (!filename) return false;
  return filename.startsWith('data:video/') || VIDEO_EXTENSIONS.includes(getExtension(filename));
}

/**
 * Transforms a fraction value to a percentage value.
 *
 * @param fraction The fraction value.
 * @return A percentage value.
 */
function fractionToPercentage(fraction) {
  return Math.round(fraction * 100);
}
//# sourceMappingURL=utils.js.map