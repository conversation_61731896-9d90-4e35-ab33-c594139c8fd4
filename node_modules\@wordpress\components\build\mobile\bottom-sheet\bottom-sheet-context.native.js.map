{"version": 3, "names": ["_reactNative", "require", "_element", "Platform", "OS", "UIManager", "setLayoutAnimationEnabledExperimental", "BottomSheetContext", "createContext", "isBottomSheetContentScrolling", "shouldEnableBottomSheetScroll", "shouldEnableBottomSheetMaxHeight", "onHandleClosingBottomSheet", "onHandleHardwareButtonPress", "setIsFullScreen", "exports", "Provider", "BottomSheetProvider", "Consumer", "BottomSheetConsumer"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/bottom-sheet-context.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Platform, UIManager } from 'react-native';\n/**\n * WordPress dependencies\n */\nimport { createContext } from '@wordpress/element';\n\n// It's needed to set the following flags via UIManager\n// to have `LayoutAnimation` working on Android\nif (\n\tPlatform.OS === 'android' &&\n\tUIManager.setLayoutAnimationEnabledExperimental\n) {\n\tUIManager.setLayoutAnimationEnabledExperimental( true );\n}\n\n// Context in BottomSheet is necessary for controlling the\n// transition flow between subsheets and replacing a content inside them\nexport const BottomSheetContext = createContext( {\n\t// Specifies whether content is currently scrolling.\n\tisBottomSheetContentScrolling: false,\n\t// Function called to enable scroll within bottom sheet.\n\tshouldEnableBottomSheetScroll: () => {},\n\t// Function called to enable/disable bottom sheet max height.\n\t// E.g. used to extend bottom sheet on full screen in ColorPicker,\n\t// which is helpful on small devices with set the largest font/display size.\n\tshouldEnableBottomSheetMaxHeight: () => {},\n\t// Callback that is called on closing bottom sheet.\n\tonHandleClosingBottomSheet: () => {},\n\t// Android only: Function called to control android hardware back button functionality\n\t// Return true if the bottom-sheet default close action shouldn't be called.\n\tonHandleHardwareButtonPress: () => {},\n\t// Toggle full-screen styles and dimensions\n\tsetIsFullScreen: () => {},\n} );\n\nexport const { Provider: BottomSheetProvider, Consumer: BottomSheetConsumer } =\n\tBottomSheetContext;\n"], "mappings": ";;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAIA,IAAAC,QAAA,GAAAD,OAAA;AAPA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA,IACCE,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBC,sBAAS,CAACC,qCAAqC,EAC9C;EACDD,sBAAS,CAACC,qCAAqC,CAAE,IAAK,CAAC;AACxD;;AAEA;AACA;AACO,MAAMC,kBAAkB,GAAG,IAAAC,sBAAa,EAAE;EAChD;EACAC,6BAA6B,EAAE,KAAK;EACpC;EACAC,6BAA6B,EAAEA,CAAA,KAAM,CAAC,CAAC;EACvC;EACA;EACA;EACAC,gCAAgC,EAAEA,CAAA,KAAM,CAAC,CAAC;EAC1C;EACAC,0BAA0B,EAAEA,CAAA,KAAM,CAAC,CAAC;EACpC;EACA;EACAC,2BAA2B,EAAEA,CAAA,KAAM,CAAC,CAAC;EACrC;EACAC,eAAe,EAAEA,CAAA,KAAM,CAAC;AACzB,CAAE,CAAC;AAACC,OAAA,CAAAR,kBAAA,GAAAA,kBAAA;AAEG,MAAM;EAAES,QAAQ,EAAEC,mBAAmB;EAAEC,QAAQ,EAAEC;AAAoB,CAAC,GAC5EZ,kBAAkB;AAACQ,OAAA,CAAAI,mBAAA,GAAAA,mBAAA;AAAAJ,OAAA,CAAAE,mBAAA,GAAAA,mBAAA"}