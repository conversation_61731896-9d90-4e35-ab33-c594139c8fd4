"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "DuotonePicker", {
  enumerable: true,
  get: function () {
    return _duotonePicker.default;
  }
});
Object.defineProperty(exports, "DuotoneSwatch", {
  enumerable: true,
  get: function () {
    return _duotoneSwatch.default;
  }
});
var _duotonePicker = _interopRequireDefault(require("./duotone-picker"));
var _duotoneSwatch = _interopRequireDefault(require("./duotone-swatch"));
//# sourceMappingURL=index.js.map