{"version": 3, "names": ["_primitives", "require", "fromPathData24x24", "pathData", "_react", "createElement", "SVG", "xmlns", "viewBox", "Path", "d", "facebook", "exports", "instagram", "empty"], "sources": ["@wordpress/components/src/mobile/dashicons/index.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { SVG, Path } from '@wordpress/primitives';\n\nconst fromPathData24x24 = ( pathData ) => (\n\t<SVG xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n\t\t<Path d={ pathData } />\n\t</SVG>\n);\n\nexport const facebook = fromPathData24x24(\n\t'M20 3H4c-.6 0-1 .4-1 1v16c0 .5.4 1 1 1h8.6v-7h-2.3v-2.7h2.3v-2c0-2.3 1.4-3.6 3.5-3.6 1 0 1.8.1 2.1.1v2.4h-1.4c-1.1 0-1.3.5-1.3 1.3v1.7h2.7l-.4 2.8h-2.3v7H20c.5 0 1-.4 1-1V4c0-.6-.4-1-1-1z'\n);\n\nexport const instagram = fromPathData24x24(\n\t'M12 4.622c2.403 0 2.688.01 3.637.052.877.04 1.354.187 1.67.31.42.163.72.358 1.036.673.315.315.51.615.673 1.035.123.317.27.794.31 1.67.043.95.052 1.235.052 3.638s-.01 2.688-.052 3.637c-.04.877-.187 1.354-.31 1.67-.163.42-.358.72-.673 1.036-.315.315-.615.51-1.035.673-.317.123-.794.27-1.67.31-.95.043-1.234.052-3.638.052s-2.688-.01-3.637-.052c-.877-.04-1.354-.187-1.67-.31-.42-.163-.72-.358-1.036-.673-.315-.315-.51-.615-.673-1.035-.123-.317-.27-.794-.31-1.67-.043-.95-.052-1.235-.052-3.638s.01-2.688.052-3.637c.04-.877.187-1.354.31-1.67.163-.42.358-.72.673-1.036.315-.315.615-.51 1.035-.673.317-.123.794-.27 1.67-.31.95-.043 1.235-.052 3.638-.052M12 3c-2.444 0-2.75.01-3.71.054s-1.613.196-2.185.418c-.592.23-1.094.538-1.594 1.04-.5.5-.807 1-1.037 1.593-.223.572-.375 1.226-.42 2.184C3.01 9.25 3 9.555 3 12s.01 2.75.054 3.71.196 1.613.418 2.186c.23.592.538 1.094 1.038 1.594s1.002.808 1.594 1.038c.572.222 1.227.375 2.185.418.96.044 1.266.054 3.71.054s2.75-.01 3.71-.054 1.613-.196 2.186-.418c.592-.23 1.094-.538 1.594-1.038s.808-1.002 1.038-1.594c.222-.572.375-1.227.418-2.185.044-.96.054-1.266.054-3.71s-.01-2.75-.054-3.71-.196-1.613-.418-2.186c-.23-.592-.538-1.094-1.038-1.594s-1.002-.808-1.594-1.038c-.572-.222-1.227-.375-2.185-.418C14.75 3.01 14.445 3 12 3zm0 4.378c-2.552 0-4.622 2.07-4.622 4.622s2.07 4.622 4.622 4.622 4.622-2.07 4.622-4.622S14.552 7.378 12 7.378zM12 15c-1.657 0-3-1.343-3-3s1.343-3 3-3 3 1.343 3 3-1.343 3-3 3zm4.804-8.884c-.596 0-1.08.484-1.08 1.08s.484 1.08 1.08 1.08c.596 0 1.08-.484 1.08-1.08s-.483-1.08-1.08-1.08z'\n);\n\nexport const empty = (\n\t<SVG xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" />\n);\n"], "mappings": ";;;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA,MAAMC,iBAAiB,GAAKC,QAAQ,IACnC,IAAAC,MAAA,CAAAC,aAAA,EAACL,WAAA,CAAAM,GAAG;EAACC,KAAK,EAAC,4BAA4B;EAACC,OAAO,EAAC;AAAW,GAC1D,IAAAJ,MAAA,CAAAC,aAAA,EAACL,WAAA,CAAAS,IAAI;EAACC,CAAC,EAAGP;AAAU,CAAE,CAClB,CACL;AAEM,MAAMQ,QAAQ,GAAGT,iBAAiB,CACxC,6LACD,CAAC;AAACU,OAAA,CAAAD,QAAA,GAAAA,QAAA;AAEK,MAAME,SAAS,GAAGX,iBAAiB,CACzC,ugDACD,CAAC;AAACU,OAAA,CAAAC,SAAA,GAAAA,SAAA;AAEK,MAAMC,KAAK,GACjB,IAAAV,MAAA,CAAAC,aAAA,EAACL,WAAA,CAAAM,GAAG;EAACC,KAAK,EAAC,4BAA4B;EAACC,OAAO,EAAC;AAAW,CAAE,CAC7D;AAACI,OAAA,CAAAE,KAAA,GAAAA,KAAA"}