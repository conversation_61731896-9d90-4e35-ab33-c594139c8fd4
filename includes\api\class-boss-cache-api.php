<?php
/**
 * API REST pour la gestion du cache Boss SEO.
 *
 * Cette classe fournit les endpoints REST pour gérer le cache du plugin.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 */

/**
 * API REST pour la gestion du cache Boss SEO.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/api
 * <AUTHOR> SEO Team
 */
class Boss_Cache_API {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance du gestionnaire de cache.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_Cache_Manager    $cache_manager    Instance du gestionnaire de cache.
     */
    private $cache_manager;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->cache_manager = Boss_Cache_Manager::get_instance();
    }

    /**
     * Enregistre les hooks pour cette classe.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        add_action( 'rest_api_init', array( $this, 'register_routes' ) );
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.1.0
     */
    public function register_routes() {
        // Route pour vider tout le cache
        register_rest_route( 'boss-seo/v1', '/cache/flush-all', array(
            array(
                'methods'             => WP_REST_Server::DELETABLE,
                'callback'            => array( $this, 'flush_all_cache' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Route pour vider le cache d'un module spécifique
        register_rest_route( 'boss-seo/v1', '/cache/flush/(?P<module>[a-zA-Z0-9_-]+)', array(
            array(
                'methods'             => WP_REST_Server::DELETABLE,
                'callback'            => array( $this, 'flush_module_cache' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'module' => array(
                        'required'          => true,
                        'validate_callback' => array( $this, 'validate_module_name' ),
                        'sanitize_callback' => 'sanitize_text_field',
                    ),
                ),
            ),
        ) );

        // Route pour vider seulement le cache des assets
        register_rest_route( 'boss-seo/v1', '/cache/flush-assets', array(
            array(
                'methods'             => WP_REST_Server::DELETABLE,
                'callback'            => array( $this, 'flush_assets_cache' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Route pour récupérer les statistiques du cache
        register_rest_route( 'boss-seo/v1', '/cache/stats', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_cache_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Route pour récupérer la version actuelle des assets
        register_rest_route( 'boss-seo/v1', '/cache/assets-version', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_assets_version' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );
    }

    /**
     * Vide tout le cache.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function flush_all_cache( $request ) {
        try {
            $results = $this->cache_manager->flush_all_cache();

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Tous les caches ont été vidés avec succès.', 'boss-seo' ),
                'results' => $results,
                'timestamp' => current_time( 'mysql' )
            ) );
        } catch ( Exception $e ) {
            return new WP_Error(
                'cache_flush_failed',
                __( 'Erreur lors du vidage du cache: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Vide le cache d'un module spécifique.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function flush_module_cache( $request ) {
        $module = $request->get_param( 'module' );

        try {
            $success = $this->cache_manager->flush_module_cache( $module );

            if ( $success ) {
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => sprintf( __( 'Cache du module %s vidé avec succès.', 'boss-seo' ), $module ),
                    'module' => $module,
                    'timestamp' => current_time( 'mysql' )
                ) );
            } else {
                return new WP_Error(
                    'module_cache_flush_failed',
                    sprintf( __( 'Impossible de vider le cache du module %s.', 'boss-seo' ), $module ),
                    array( 'status' => 500 )
                );
            }
        } catch ( Exception $e ) {
            return new WP_Error(
                'module_cache_flush_error',
                __( 'Erreur lors du vidage du cache du module: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Vide seulement le cache des assets.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function flush_assets_cache( $request ) {
        try {
            $success = $this->cache_manager->force_assets_refresh();

            if ( $success ) {
                return rest_ensure_response( array(
                    'success' => true,
                    'message' => __( 'Cache des assets vidé avec succès.', 'boss-seo' ),
                    'new_version' => $this->cache_manager->get_assets_version(),
                    'timestamp' => current_time( 'mysql' )
                ) );
            } else {
                return new WP_Error(
                    'assets_cache_flush_failed',
                    __( 'Impossible de vider le cache des assets.', 'boss-seo' ),
                    array( 'status' => 500 )
                );
            }
        } catch ( Exception $e ) {
            return new WP_Error(
                'assets_cache_flush_error',
                __( 'Erreur lors du vidage du cache des assets: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère les statistiques du cache.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_cache_stats( $request ) {
        try {
            $stats = $this->cache_manager->get_cache_stats();

            return rest_ensure_response( array(
                'success' => true,
                'stats' => $stats,
                'timestamp' => current_time( 'mysql' )
            ) );
        } catch ( Exception $e ) {
            return new WP_Error(
                'cache_stats_error',
                __( 'Erreur lors de la récupération des statistiques: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Récupère la version actuelle des assets.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_assets_version( $request ) {
        try {
            $version = $this->cache_manager->get_assets_version();

            return rest_ensure_response( array(
                'success' => true,
                'version' => $version,
                'debug_mode' => defined( 'WP_DEBUG' ) && WP_DEBUG,
                'timestamp' => current_time( 'mysql' )
            ) );
        } catch ( Exception $e ) {
            return new WP_Error(
                'assets_version_error',
                __( 'Erreur lors de la récupération de la version des assets: ', 'boss-seo' ) . $e->getMessage(),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Valide le nom du module.
     *
     * @since    1.1.0
     * @param    string    $param      Paramètre à valider.
     * @param    WP_REST_Request    $request    Requête REST.
     * @param    string    $key        Clé du paramètre.
     * @return   bool                          True si valide, false sinon.
     */
    public function validate_module_name( $param, $request, $key ) {
        $valid_modules = array( 'optimizer', 'analytics', 'ecommerce', 'technical', 'local', 'structured_schemas' );
        return in_array( $param, $valid_modules );
    }

    /**
     * Vérifie les permissions pour les endpoints.
     *
     * @since    1.1.0
     * @param    WP_REST_Request    $request    Requête REST.
     * @return   bool                          True si autorisé, false sinon.
     */
    public function check_permissions( $request ) {
        return current_user_can( 'manage_options' );
    }
}
