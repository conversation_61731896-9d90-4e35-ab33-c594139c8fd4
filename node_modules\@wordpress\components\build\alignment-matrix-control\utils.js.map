{"version": 3, "names": ["_i18n", "require", "GRID", "exports", "ALIGNMENT_LABEL", "__", "center", "ALIGNMENTS", "flat", "normalize", "value", "normalized", "transformed", "replace", "includes", "undefined", "getItemId", "prefixId", "id", "getItemValue", "getAlignmentIndex", "alignment", "index", "indexOf"], "sources": ["@wordpress/components/src/alignment-matrix-control/utils.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport type { AlignmentMatrixControlValue } from './types';\n\nexport const GRID: AlignmentMatrixControlValue[][] = [\n\t[ 'top left', 'top center', 'top right' ],\n\t[ 'center left', 'center center', 'center right' ],\n\t[ 'bottom left', 'bottom center', 'bottom right' ],\n];\n\n// Stored as map as i18n __() only accepts strings (not variables)\nexport const ALIGNMENT_LABEL: Record< AlignmentMatrixControlValue, string > = {\n\t'top left': __( 'Top Left' ),\n\t'top center': __( 'Top Center' ),\n\t'top right': __( 'Top Right' ),\n\t'center left': __( 'Center Left' ),\n\t'center center': __( 'Center' ),\n\tcenter: __( 'Center' ),\n\t'center right': __( 'Center Right' ),\n\t'bottom left': __( 'Bottom Left' ),\n\t'bottom center': __( 'Bottom Center' ),\n\t'bottom right': __( 'Bottom Right' ),\n};\n\n// Transforms GRID into a flat Array of values.\nexport const ALIGNMENTS = GRID.flat();\n\n/**\n * Normalizes and transforms an incoming value to better match the alignment values\n *\n * @param value An alignment value to parse.\n *\n * @return The parsed value.\n */\nfunction normalize( value?: string | null ) {\n\tconst normalized = value === 'center' ? 'center center' : value;\n\n\t// Strictly speaking, this could be `string | null | undefined`,\n\t// but will be validated shortly, so we're typecasting to an\n\t// `AlignmentMatrixControlValue` to keep TypeScript happy.\n\tconst transformed = normalized?.replace(\n\t\t'-',\n\t\t' '\n\t) as AlignmentMatrixControlValue;\n\n\treturn ALIGNMENTS.includes( transformed ) ? transformed : undefined;\n}\n\n/**\n * Creates an item ID based on a prefix ID and an alignment value.\n *\n * @param prefixId An ID to prefix.\n * @param value    An alignment value.\n *\n * @return The item id.\n */\nexport function getItemId(\n\tprefixId: string,\n\tvalue?: AlignmentMatrixControlValue\n) {\n\tconst normalized = normalize( value );\n\tif ( ! normalized ) return;\n\n\tconst id = normalized.replace( ' ', '-' );\n\treturn `${ prefixId }-${ id }`;\n}\n\n/**\n * Extracts an item value from its ID\n *\n * @param prefixId An ID prefix to remove\n * @param id       An item ID\n * @return         The item value\n */\nexport function getItemValue( prefixId: string, id?: string | null ) {\n\tconst value = id?.replace( prefixId + '-', '' );\n\treturn normalize( value );\n}\n\n/**\n * Retrieves the alignment index from a value.\n *\n * @param alignment Value to check.\n *\n * @return The index of a matching alignment.\n */\nexport function getAlignmentIndex(\n\talignment: AlignmentMatrixControlValue = 'center'\n) {\n\tconst normalized = normalize( alignment );\n\tif ( ! normalized ) return undefined;\n\n\tconst index = ALIGNMENTS.indexOf( normalized );\n\treturn index > -1 ? index : undefined;\n}\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAQO,MAAMC,IAAqC,GAAG,CACpD,CAAE,UAAU,EAAE,YAAY,EAAE,WAAW,CAAE,EACzC,CAAE,aAAa,EAAE,eAAe,EAAE,cAAc,CAAE,EAClD,CAAE,aAAa,EAAE,eAAe,EAAE,cAAc,CAAE,CAClD;;AAED;AAAAC,OAAA,CAAAD,IAAA,GAAAA,IAAA;AACO,MAAME,eAA8D,GAAG;EAC7E,UAAU,EAAE,IAAAC,QAAE,EAAE,UAAW,CAAC;EAC5B,YAAY,EAAE,IAAAA,QAAE,EAAE,YAAa,CAAC;EAChC,WAAW,EAAE,IAAAA,QAAE,EAAE,WAAY,CAAC;EAC9B,aAAa,EAAE,IAAAA,QAAE,EAAE,aAAc,CAAC;EAClC,eAAe,EAAE,IAAAA,QAAE,EAAE,QAAS,CAAC;EAC/BC,MAAM,EAAE,IAAAD,QAAE,EAAE,QAAS,CAAC;EACtB,cAAc,EAAE,IAAAA,QAAE,EAAE,cAAe,CAAC;EACpC,aAAa,EAAE,IAAAA,QAAE,EAAE,aAAc,CAAC;EAClC,eAAe,EAAE,IAAAA,QAAE,EAAE,eAAgB,CAAC;EACtC,cAAc,EAAE,IAAAA,QAAE,EAAE,cAAe;AACpC,CAAC;;AAED;AAAAF,OAAA,CAAAC,eAAA,GAAAA,eAAA;AACO,MAAMG,UAAU,GAAGL,IAAI,CAACM,IAAI,CAAC,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AANAL,OAAA,CAAAI,UAAA,GAAAA,UAAA;AAOA,SAASE,SAASA,CAAEC,KAAqB,EAAG;EAC3C,MAAMC,UAAU,GAAGD,KAAK,KAAK,QAAQ,GAAG,eAAe,GAAGA,KAAK;;EAE/D;EACA;EACA;EACA,MAAME,WAAW,GAAGD,UAAU,EAAEE,OAAO,CACtC,GAAG,EACH,GACD,CAAgC;EAEhC,OAAON,UAAU,CAACO,QAAQ,CAAEF,WAAY,CAAC,GAAGA,WAAW,GAAGG,SAAS;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,SAASA,CACxBC,QAAgB,EAChBP,KAAmC,EAClC;EACD,MAAMC,UAAU,GAAGF,SAAS,CAAEC,KAAM,CAAC;EACrC,IAAK,CAAEC,UAAU,EAAG;EAEpB,MAAMO,EAAE,GAAGP,UAAU,CAACE,OAAO,CAAE,GAAG,EAAE,GAAI,CAAC;EACzC,OAAQ,GAAGI,QAAU,IAAIC,EAAI,EAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,YAAYA,CAAEF,QAAgB,EAAEC,EAAkB,EAAG;EACpE,MAAMR,KAAK,GAAGQ,EAAE,EAAEL,OAAO,CAAEI,QAAQ,GAAG,GAAG,EAAE,EAAG,CAAC;EAC/C,OAAOR,SAAS,CAAEC,KAAM,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,iBAAiBA,CAChCC,SAAsC,GAAG,QAAQ,EAChD;EACD,MAAMV,UAAU,GAAGF,SAAS,CAAEY,SAAU,CAAC;EACzC,IAAK,CAAEV,UAAU,EAAG,OAAOI,SAAS;EAEpC,MAAMO,KAAK,GAAGf,UAAU,CAACgB,OAAO,CAAEZ,UAAW,CAAC;EAC9C,OAAOW,KAAK,GAAG,CAAC,CAAC,GAAGA,KAAK,GAAGP,SAAS;AACtC"}