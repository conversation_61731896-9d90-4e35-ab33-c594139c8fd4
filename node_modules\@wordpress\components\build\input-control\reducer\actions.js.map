{"version": 3, "names": ["CHANGE", "exports", "COMMIT", "CONTROL", "DRAG_END", "DRAG_START", "DRAG", "INVALIDATE", "PRESS_DOWN", "PRESS_ENTER", "PRESS_UP", "RESET"], "sources": ["@wordpress/components/src/input-control/reducer/actions.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { SyntheticEvent } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { DragProps } from '../types';\n\nexport const CHANGE = 'CHANGE';\nexport const COMMIT = 'COMMIT';\nexport const CONTROL = 'CONTROL';\nexport const DRAG_END = 'DRAG_END';\nexport const DRAG_START = 'DRAG_START';\nexport const DRAG = 'DRAG';\nexport const INVALIDATE = 'INVALIDATE';\nexport const PRESS_DOWN = 'PRESS_DOWN';\nexport const PRESS_ENTER = 'PRESS_ENTER';\nexport const PRESS_UP = 'PRESS_UP';\nexport const RESET = 'RESET';\n\ninterface EventPayload {\n\tevent?: SyntheticEvent;\n}\n\nexport interface Action< Type = string, ExtraPayload = {} > {\n\ttype: Type;\n\tpayload: EventPayload & ExtraPayload;\n}\n\ninterface ValuePayload {\n\tvalue: string;\n}\n\nexport type ChangeAction = Action< typeof CHANGE, ValuePayload >;\nexport type CommitAction = Action< typeof COMMIT, ValuePayload >;\nexport type ControlAction = Action< typeof CONTROL, ValuePayload >;\nexport type PressUpAction = Action< typeof PRESS_UP >;\nexport type PressDownAction = Action< typeof PRESS_DOWN >;\nexport type PressEnterAction = Action< typeof PRESS_ENTER >;\nexport type DragStartAction = Action< typeof DRAG_START, DragProps >;\nexport type DragEndAction = Action< typeof DRAG_END, DragProps >;\nexport type DragAction = Action< typeof DRAG, DragProps >;\nexport type ResetAction = Action< typeof RESET, Partial< ValuePayload > >;\nexport type InvalidateAction = Action< typeof INVALIDATE, { error: unknown } >;\n\nexport type ChangeEventAction = ChangeAction | ResetAction | CommitAction;\n\nexport type DragEventAction = DragStartAction | DragEndAction | DragAction;\n\nexport type KeyEventAction = PressDownAction | PressUpAction | PressEnterAction;\n\nexport type InputAction =\n\t| ChangeEventAction\n\t| KeyEventAction\n\t| DragEventAction\n\t| InvalidateAction;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;;AAGA;AACA;AACA;;AAGO,MAAMA,MAAM,GAAG,QAAQ;AAACC,OAAA,CAAAD,MAAA,GAAAA,MAAA;AACxB,MAAME,MAAM,GAAG,QAAQ;AAACD,OAAA,CAAAC,MAAA,GAAAA,MAAA;AACxB,MAAMC,OAAO,GAAG,SAAS;AAACF,OAAA,CAAAE,OAAA,GAAAA,OAAA;AAC1B,MAAMC,QAAQ,GAAG,UAAU;AAACH,OAAA,CAAAG,QAAA,GAAAA,QAAA;AAC5B,MAAMC,UAAU,GAAG,YAAY;AAACJ,OAAA,CAAAI,UAAA,GAAAA,UAAA;AAChC,MAAMC,IAAI,GAAG,MAAM;AAACL,OAAA,CAAAK,IAAA,GAAAA,IAAA;AACpB,MAAMC,UAAU,GAAG,YAAY;AAACN,OAAA,CAAAM,UAAA,GAAAA,UAAA;AAChC,MAAMC,UAAU,GAAG,YAAY;AAACP,OAAA,CAAAO,UAAA,GAAAA,UAAA;AAChC,MAAMC,WAAW,GAAG,aAAa;AAACR,OAAA,CAAAQ,WAAA,GAAAA,WAAA;AAClC,MAAMC,QAAQ,GAAG,UAAU;AAACT,OAAA,CAAAS,QAAA,GAAAA,QAAA;AAC5B,MAAMC,KAAK,GAAG,OAAO;AAACV,OAAA,CAAAU,KAAA,GAAAA,KAAA"}