{"version": 3, "names": [], "sources": ["@wordpress/components/src/flex/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties, ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { ResponsiveCSSValue } from '../utils/types';\nimport type { SpaceInput } from '../utils/space';\n\nexport type FlexDirection = ResponsiveCSSValue<\n\tCSSProperties[ 'flexDirection' ]\n>;\n\nexport type FlexProps = {\n\t/**\n\t * Aligns children using CSS Flexbox `align-items`. Vertically aligns\n\t * content if the `direction` is `row`, or horizontally aligns content if\n\t * the `direction` is `column`.\n\t *\n\t * @default 'center'\n\t */\n\talign?: CSSProperties[ 'alignItems' ];\n\t/**\n\t * The direction flow of the children content can be adjusted with\n\t * `direction`. `column` will align children vertically and `row` will align\n\t * children horizontally.\n\t *\n\t * @default 'row'\n\t */\n\tdirection?: FlexDirection;\n\t/**\n\t * Expands to the maximum available width (if horizontal) or height (if\n\t * vertical).\n\t *\n\t * @default true\n\t */\n\texpanded?: boolean;\n\t/**\n\t * Spacing in between each child can be adjusted by using `gap`.\n\t *\n\t * Can either be a number (which will act as a multiplier to the library's\n\t * grid system base of 4px), or a literal CSS value string.\n\t *\n\t * @default 2\n\t */\n\tgap?: SpaceInput;\n\t/**\n\t * Horizontally aligns content if the `direction` is `row`, or vertically\n\t * aligns content if the `direction` is `column`.\n\t *\n\t * @default 'space-between'\n\t */\n\tjustify?: CSSProperties[ 'justifyContent' ];\n\t/**\n\t * Determines if children should wrap.\n\t *\n\t * @default false\n\t */\n\twrap?: boolean;\n\t/**\n\t * The children elements.\n\t */\n\tchildren: ReactNode;\n\t/**\n\t * @deprecated\n\t */\n\tisReversed?: boolean;\n};\n\nexport type FlexItemProps = {\n\t/**\n\t * The (CSS) display of the `FlexItem`.\n\t */\n\tdisplay?: CSSProperties[ 'display' ];\n\t/**\n\t * Determines if `FlexItem` should render as an adaptive full-width block.\n\t *\n\t * @default true\n\t */\n\tisBlock?: boolean;\n\t/**\n\t * The children elements.\n\t */\n\tchildren?: ReactNode;\n};\n\nexport type FlexBlockProps = Omit< FlexItemProps, 'isBlock' >;\n"], "mappings": ""}