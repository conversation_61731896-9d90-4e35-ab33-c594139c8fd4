{"version": 3, "names": ["_compose", "require", "_utils", "_tooltip", "_interopRequireDefault", "_utils2", "_boxControlStyles", "groupedSides", "AxialInputControls", "__next40pxDefaultSize", "onChange", "onFocus", "values", "selected<PERSON><PERSON><PERSON>", "setSelectedUnits", "sides", "props", "generatedId", "useInstanceId", "createHandleOnFocus", "side", "event", "handleOnValueChange", "next", "nextV<PERSON>ues", "isNumeric", "undefined", "isNaN", "parseFloat", "nextValue", "top", "bottom", "left", "right", "createHandleOnUnitChange", "newUnits", "filteredSides", "length", "filter", "includes", "_react", "createElement", "Fragment", "map", "_CUSTOM_VALUE_SETTING", "_CUSTOM_VALUE_SETTING2", "parsedQuantity", "parsedUnit", "parseQuantityAndUnitFromRawValue", "<PERSON><PERSON><PERSON><PERSON>", "inputId", "join", "InputWrapper", "key", "FlexedBoxControlIcon", "default", "placement", "text", "LABELS", "StyledUnitControl", "className", "id", "isPressEnterToChange", "value", "newValue", "onUnitChange", "label", "hideLabelFromVision", "FlexedRangeControl", "__nextHasNoMarginBottom", "min", "max", "CUSTOM_VALUE_SETTINGS", "step", "withInputField"], "sources": ["@wordpress/components/src/box-control/axial-input-controls.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\n/**\n * Internal dependencies\n */\nimport { parseQuantityAndUnitFromRawValue } from '../unit-control/utils';\nimport Tooltip from '../tooltip';\nimport { CUSTOM_VALUE_SETTINGS, LABELS } from './utils';\nimport {\n\tFlexedBoxControlIcon,\n\tFlexedRangeControl,\n\tInputWrapper,\n\tStyledUnitControl,\n} from './styles/box-control-styles';\nimport type { BoxControlInputControlProps } from './types';\n\nconst groupedSides = [ 'vertical', 'horizontal' ] as const;\ntype GroupedSide = ( typeof groupedSides )[ number ];\n\nexport default function AxialInputControls( {\n\t__next40pxDefaultSize,\n\tonChange,\n\tonFocus,\n\tvalues,\n\tselectedUnits,\n\tsetSelectedUnits,\n\tsides,\n\t...props\n}: BoxControlInputControlProps ) {\n\tconst generatedId = useInstanceId(\n\t\tAxialInputControls,\n\t\t`box-control-input`\n\t);\n\n\tconst createHandleOnFocus =\n\t\t( side: GroupedSide ) =>\n\t\t( event: React.FocusEvent< HTMLInputElement > ) => {\n\t\t\tif ( ! onFocus ) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tonFocus( event, { side } );\n\t\t};\n\n\tconst handleOnValueChange = ( side: GroupedSide, next?: string ) => {\n\t\tif ( ! onChange ) {\n\t\t\treturn;\n\t\t}\n\t\tconst nextValues = { ...values };\n\t\tconst isNumeric = next !== undefined && ! isNaN( parseFloat( next ) );\n\t\tconst nextValue = isNumeric ? next : undefined;\n\n\t\tif ( side === 'vertical' ) {\n\t\t\tnextValues.top = nextValue;\n\t\t\tnextValues.bottom = nextValue;\n\t\t}\n\n\t\tif ( side === 'horizontal' ) {\n\t\t\tnextValues.left = nextValue;\n\t\t\tnextValues.right = nextValue;\n\t\t}\n\n\t\tonChange( nextValues );\n\t};\n\n\tconst createHandleOnUnitChange =\n\t\t( side: GroupedSide ) => ( next?: string ) => {\n\t\t\tconst newUnits = { ...selectedUnits };\n\n\t\t\tif ( side === 'vertical' ) {\n\t\t\t\tnewUnits.top = next;\n\t\t\t\tnewUnits.bottom = next;\n\t\t\t}\n\n\t\t\tif ( side === 'horizontal' ) {\n\t\t\t\tnewUnits.left = next;\n\t\t\t\tnewUnits.right = next;\n\t\t\t}\n\n\t\t\tsetSelectedUnits( newUnits );\n\t\t};\n\n\t// Filter sides if custom configuration provided, maintaining default order.\n\tconst filteredSides = sides?.length\n\t\t? groupedSides.filter( ( side ) => sides.includes( side ) )\n\t\t: groupedSides;\n\n\treturn (\n\t\t<>\n\t\t\t{ filteredSides.map( ( side ) => {\n\t\t\t\tconst [ parsedQuantity, parsedUnit ] =\n\t\t\t\t\tparseQuantityAndUnitFromRawValue(\n\t\t\t\t\t\tside === 'vertical' ? values.top : values.left\n\t\t\t\t\t);\n\t\t\t\tconst selectedUnit =\n\t\t\t\t\tside === 'vertical'\n\t\t\t\t\t\t? selectedUnits.top\n\t\t\t\t\t\t: selectedUnits.left;\n\n\t\t\t\tconst inputId = [ generatedId, side ].join( '-' );\n\n\t\t\t\treturn (\n\t\t\t\t\t<InputWrapper key={ side }>\n\t\t\t\t\t\t<FlexedBoxControlIcon side={ side } sides={ sides } />\n\t\t\t\t\t\t<Tooltip placement=\"top-end\" text={ LABELS[ side ] }>\n\t\t\t\t\t\t\t<StyledUnitControl\n\t\t\t\t\t\t\t\t{ ...props }\n\t\t\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t\t\t\tclassName=\"component-box-control__unit-control\"\n\t\t\t\t\t\t\t\tid={ inputId }\n\t\t\t\t\t\t\t\tisPressEnterToChange\n\t\t\t\t\t\t\t\tvalue={ [\n\t\t\t\t\t\t\t\t\tparsedQuantity,\n\t\t\t\t\t\t\t\t\tselectedUnit ?? parsedUnit,\n\t\t\t\t\t\t\t\t].join( '' ) }\n\t\t\t\t\t\t\t\tonChange={ ( newValue ) =>\n\t\t\t\t\t\t\t\t\thandleOnValueChange( side, newValue )\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tonUnitChange={ createHandleOnUnitChange(\n\t\t\t\t\t\t\t\t\tside\n\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\tonFocus={ createHandleOnFocus( side ) }\n\t\t\t\t\t\t\t\tlabel={ LABELS[ side ] }\n\t\t\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t\t\t\tkey={ side }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</Tooltip>\n\t\t\t\t\t\t<FlexedRangeControl\n\t\t\t\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t\t\taria-controls={ inputId }\n\t\t\t\t\t\t\tlabel={ LABELS[ side ] }\n\t\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t\t\tonChange={ ( newValue ) =>\n\t\t\t\t\t\t\t\thandleOnValueChange(\n\t\t\t\t\t\t\t\t\tside,\n\t\t\t\t\t\t\t\t\tnewValue !== undefined\n\t\t\t\t\t\t\t\t\t\t? [\n\t\t\t\t\t\t\t\t\t\t\t\tnewValue,\n\t\t\t\t\t\t\t\t\t\t\t\tselectedUnit ?? parsedUnit,\n\t\t\t\t\t\t\t\t\t\t  ].join( '' )\n\t\t\t\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\tmax={\n\t\t\t\t\t\t\t\tCUSTOM_VALUE_SETTINGS[ selectedUnit ?? 'px' ]\n\t\t\t\t\t\t\t\t\t?.max ?? 10\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tstep={\n\t\t\t\t\t\t\t\tCUSTOM_VALUE_SETTINGS[ selectedUnit ?? 'px' ]\n\t\t\t\t\t\t\t\t\t?.step ?? 0.1\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tvalue={ parsedQuantity ?? 0 }\n\t\t\t\t\t\t\twithInputField={ false }\n\t\t\t\t\t\t/>\n\t\t\t\t\t</InputWrapper>\n\t\t\t\t);\n\t\t\t} ) }\n\t\t</>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAIA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AAVA;AACA;AACA;;AAEA;AACA;AACA;;AAYA,MAAMM,YAAY,GAAG,CAAE,UAAU,EAAE,YAAY,CAAW;AAG3C,SAASC,kBAAkBA,CAAE;EAC3CC,qBAAqB;EACrBC,QAAQ;EACRC,OAAO;EACPC,MAAM;EACNC,aAAa;EACbC,gBAAgB;EAChBC,KAAK;EACL,GAAGC;AACyB,CAAC,EAAG;EAChC,MAAMC,WAAW,GAAG,IAAAC,sBAAa,EAChCV,kBAAkB,EACjB,mBACF,CAAC;EAED,MAAMW,mBAAmB,GACtBC,IAAiB,IACjBC,KAA2C,IAAM;IAClD,IAAK,CAAEV,OAAO,EAAG;MAChB;IACD;IACAA,OAAO,CAAEU,KAAK,EAAE;MAAED;IAAK,CAAE,CAAC;EAC3B,CAAC;EAEF,MAAME,mBAAmB,GAAGA,CAAEF,IAAiB,EAAEG,IAAa,KAAM;IACnE,IAAK,CAAEb,QAAQ,EAAG;MACjB;IACD;IACA,MAAMc,UAAU,GAAG;MAAE,GAAGZ;IAAO,CAAC;IAChC,MAAMa,SAAS,GAAGF,IAAI,KAAKG,SAAS,IAAI,CAAEC,KAAK,CAAEC,UAAU,CAAEL,IAAK,CAAE,CAAC;IACrE,MAAMM,SAAS,GAAGJ,SAAS,GAAGF,IAAI,GAAGG,SAAS;IAE9C,IAAKN,IAAI,KAAK,UAAU,EAAG;MAC1BI,UAAU,CAACM,GAAG,GAAGD,SAAS;MAC1BL,UAAU,CAACO,MAAM,GAAGF,SAAS;IAC9B;IAEA,IAAKT,IAAI,KAAK,YAAY,EAAG;MAC5BI,UAAU,CAACQ,IAAI,GAAGH,SAAS;MAC3BL,UAAU,CAACS,KAAK,GAAGJ,SAAS;IAC7B;IAEAnB,QAAQ,CAAEc,UAAW,CAAC;EACvB,CAAC;EAED,MAAMU,wBAAwB,GAC3Bd,IAAiB,IAAQG,IAAa,IAAM;IAC7C,MAAMY,QAAQ,GAAG;MAAE,GAAGtB;IAAc,CAAC;IAErC,IAAKO,IAAI,KAAK,UAAU,EAAG;MAC1Be,QAAQ,CAACL,GAAG,GAAGP,IAAI;MACnBY,QAAQ,CAACJ,MAAM,GAAGR,IAAI;IACvB;IAEA,IAAKH,IAAI,KAAK,YAAY,EAAG;MAC5Be,QAAQ,CAACH,IAAI,GAAGT,IAAI;MACpBY,QAAQ,CAACF,KAAK,GAAGV,IAAI;IACtB;IAEAT,gBAAgB,CAAEqB,QAAS,CAAC;EAC7B,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAGrB,KAAK,EAAEsB,MAAM,GAChC9B,YAAY,CAAC+B,MAAM,CAAIlB,IAAI,IAAML,KAAK,CAACwB,QAAQ,CAAEnB,IAAK,CAAE,CAAC,GACzDb,YAAY;EAEf,OACC,IAAAiC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGN,aAAa,CAACO,GAAG,CAAIvB,IAAI,IAAM;IAAA,IAAAwB,qBAAA,EAAAC,sBAAA;IAChC,MAAM,CAAEC,cAAc,EAAEC,UAAU,CAAE,GACnC,IAAAC,uCAAgC,EAC/B5B,IAAI,KAAK,UAAU,GAAGR,MAAM,CAACkB,GAAG,GAAGlB,MAAM,CAACoB,IAC3C,CAAC;IACF,MAAMiB,YAAY,GACjB7B,IAAI,KAAK,UAAU,GAChBP,aAAa,CAACiB,GAAG,GACjBjB,aAAa,CAACmB,IAAI;IAEtB,MAAMkB,OAAO,GAAG,CAAEjC,WAAW,EAAEG,IAAI,CAAE,CAAC+B,IAAI,CAAE,GAAI,CAAC;IAEjD,OACC,IAAAX,MAAA,CAAAC,aAAA,EAACnC,iBAAA,CAAA8C,YAAY;MAACC,GAAG,EAAGjC;IAAM,GACzB,IAAAoB,MAAA,CAAAC,aAAA,EAACnC,iBAAA,CAAAgD,oBAAoB;MAAClC,IAAI,EAAGA,IAAM;MAACL,KAAK,EAAGA;IAAO,CAAE,CAAC,EACtD,IAAAyB,MAAA,CAAAC,aAAA,EAACtC,QAAA,CAAAoD,OAAO;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAGC,cAAM,CAAEtC,IAAI;IAAI,GACnD,IAAAoB,MAAA,CAAAC,aAAA,EAACnC,iBAAA,CAAAqD,iBAAiB;MAAA,GACZ3C,KAAK;MACVP,qBAAqB,EAAGA,qBAAuB;MAC/CmD,SAAS,EAAC,qCAAqC;MAC/CC,EAAE,EAAGX,OAAS;MACdY,oBAAoB;MACpBC,KAAK,EAAG,CACPjB,cAAc,EACdG,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAIF,UAAU,CAC1B,CAACI,IAAI,CAAE,EAAG,CAAG;MACdzC,QAAQ,EAAKsD,QAAQ,IACpB1C,mBAAmB,CAAEF,IAAI,EAAE4C,QAAS,CACpC;MACDC,YAAY,EAAG/B,wBAAwB,CACtCd,IACD,CAAG;MACHT,OAAO,EAAGQ,mBAAmB,CAAEC,IAAK,CAAG;MACvC8C,KAAK,EAAGR,cAAM,CAAEtC,IAAI,CAAI;MACxB+C,mBAAmB;MACnBd,GAAG,EAAGjC;IAAM,CACZ,CACO,CAAC,EACV,IAAAoB,MAAA,CAAAC,aAAA,EAACnC,iBAAA,CAAA8D,kBAAkB;MAClBC,uBAAuB;MACvB5D,qBAAqB,EAAGA,qBAAuB;MAC/C,iBAAgByC,OAAS;MACzBgB,KAAK,EAAGR,cAAM,CAAEtC,IAAI,CAAI;MACxB+C,mBAAmB;MACnBzD,QAAQ,EAAKsD,QAAQ,IACpB1C,mBAAmB,CAClBF,IAAI,EACJ4C,QAAQ,KAAKtC,SAAS,GACnB,CACAsC,QAAQ,EACRf,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAIF,UAAU,CACzB,CAACI,IAAI,CAAE,EAAG,CAAC,GACZzB,SACJ,CACA;MACD4C,GAAG,EAAG,CAAG;MACTC,GAAG,GAAA3B,qBAAA,GACF4B,6BAAqB,CAAEvB,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAI,IAAI,CAAE,EAC1CsB,GAAG,cAAA3B,qBAAA,cAAAA,qBAAA,GAAI,EACV;MACD6B,IAAI,GAAA5B,sBAAA,GACH2B,6BAAqB,CAAEvB,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAI,IAAI,CAAE,EAC1CwB,IAAI,cAAA5B,sBAAA,cAAAA,sBAAA,GAAI,GACX;MACDkB,KAAK,EAAGjB,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAI,CAAG;MAC7B4B,cAAc,EAAG;IAAO,CACxB,CACY,CAAC;EAEjB,CAAE,CACD,CAAC;AAEL"}