{"version": 3, "names": [], "sources": ["@wordpress/components/src/button/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { Props as IconProps } from '../icon';\nimport type { PopoverProps } from '../popover/types';\nimport type { WordPressComponentProps } from '../context/wordpress-component';\n\nexport type ButtonProps =\n\t| WordPressComponentProps< ButtonAsButtonProps, 'button', false >\n\t| WordPressComponentProps< ButtonAsAnchorProps, 'a', false >;\n\nexport type ButtonAsButtonProps = BaseButtonProps & _ButtonProps;\nexport type ButtonAsAnchorProps = BaseButtonProps & AnchorProps;\n\ntype BaseButtonProps = {\n\t/**\n\t * Start opting into the larger default height that will become the\n\t * default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n\t/**\n\t * The button's children.\n\t */\n\tchildren?: ReactNode;\n\t/**\n\t * An accessible description for the button.\n\t */\n\tdescribedBy?: string;\n\t/**\n\t * If provided, renders an Icon component inside the button.\n\t */\n\ticon?: IconProps[ 'icon' ];\n\t/**\n\t * If provided with `icon`, sets the position of icon relative to the `text`.\n\t *\n\t * @default 'left'\n\t */\n\ticonPosition?: 'left' | 'right';\n\t/**\n\t * If provided with `icon`, sets the icon size.\n\t * Please refer to the Icon component for more details regarding\n\t * the default value of its `size` prop.\n\t */\n\ticonSize?: IconProps[ 'size' ];\n\t/**\n\t * Indicates activity while a action is being performed.\n\t */\n\tisBusy?: boolean;\n\t/**\n\t * Renders a red text-based button style to indicate destructive behavior.\n\t */\n\tisDestructive?: boolean;\n\t/**\n\t * Renders a pressed button style.\n\t */\n\tisPressed?: boolean;\n\t// TODO: Deprecate officially (add console warning and move to DeprecatedButtonProps).\n\t/**\n\t * Decreases the size of the button.\n\t *\n\t * Deprecated in favor of the `size` prop. If both props are defined, the `size` prop will take precedence.\n\t *\n\t * @deprecated Use the `'small'` value on the `size` prop instead.\n\t */\n\tisSmall?: boolean;\n\t/**\n\t * Sets the `aria-label` of the component, if none is provided.\n\t * Sets the Tooltip content if `showTooltip` is provided.\n\t */\n\tlabel?: string;\n\t/**\n\t * If provided with `showTooltip`, appends the Shortcut label to the tooltip content.\n\t * If an object is provided, it should contain `display` and `ariaLabel` keys.\n\t */\n\tshortcut?: string | { display: string; ariaLabel: string };\n\t/**\n\t * If provided, renders a Tooltip component for the button.\n\t */\n\tshowTooltip?: boolean;\n\t/**\n\t * The size of the button.\n\t *\n\t * - `'default'`: For normal text-label buttons, unless it is a toggle button.\n\t * - `'compact'`: For toggle buttons, icon buttons, and buttons when used in context of either.\n\t * - `'small'`: For icon buttons associated with more advanced or auxiliary features.\n\t *\n\t * If the deprecated `isSmall` prop is also defined, this prop will take precedence.\n\t *\n\t * @default 'default'\n\t */\n\tsize?: 'default' | 'compact' | 'small';\n\t/**\n\t * If provided, displays the given text inside the button. If the button contains children elements, the text is displayed before them.\n\t */\n\ttext?: string;\n\t/**\n\t * If provided with `showTooltip`, sets the position of the tooltip.\n\t * Please refer to the Tooltip component for more details regarding the defaults.\n\t */\n\ttooltipPosition?: PopoverProps[ 'position' ];\n\t/**\n\t * Specifies the button's style.\n\t * The accepted values are:\n\t * 'primary' (the primary button styles)\n\t * 'secondary' (the default button styles)\n\t * 'tertiary' (the text-based button styles)\n\t * 'link' (the link button styles)\n\t */\n\tvariant?: 'primary' | 'secondary' | 'tertiary' | 'link';\n\t/**\n\t * Whether to keep the button focusable when disabled.\n\t *\n\t * @default false\n\t */\n\t__experimentalIsFocusable?: boolean;\n};\n\ntype _ButtonProps = {\n\t/**\n\t * Whether the button is disabled.\n\t *\n\t * If `true`, this will force a `button` element to be rendered, even when an `href` is given.\n\t */\n\tdisabled?: boolean;\n};\n\ntype AnchorProps = {\n\t/**\n\t * Whether the button is disabled.\n\t *\n\t * If `true`, this will force a `button` element to be rendered, even when an `href` is given.\n\t */\n\tdisabled?: false;\n\t/**\n\t * If provided, renders `a` instead of `button`.\n\t */\n\thref: string;\n\t/**\n\t * If provided with `href`, sets the `target` attribute to the `a`.\n\t */\n\ttarget?: string;\n};\n\nexport type DeprecatedButtonProps = {\n\tisDefault?: boolean;\n\tisLink?: boolean;\n\tisPrimary?: boolean;\n\tisSecondary?: boolean;\n\tisTertiary?: boolean;\n};\n\nexport type DeprecatedIconButtonProps = {\n\tlabelPosition: ButtonProps[ 'tooltipPosition' ];\n\tshowTooltip?: boolean;\n\tsize: ButtonProps[ 'iconSize' ];\n\tlabel: ButtonProps[ 'label' ];\n\ttooltip: ButtonProps[ 'label' ];\n};\n"], "mappings": ""}