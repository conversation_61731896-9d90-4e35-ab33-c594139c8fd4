{"version": 3, "names": ["_reactNative", "require", "_reactNativeGestureHandler", "_element", "_compose", "_tooltip", "_interopRequireDefault", "_icon", "_style", "isAndroid", "Platform", "OS", "marginBottom", "marginLeft", "styles", "StyleSheet", "create", "container", "flex", "padding", "justifyContent", "alignItems", "buttonInactive", "flexDirection", "fixedRatio", "aspectRatio", "buttonActive", "borderRadius", "subscriptInactive", "color", "fontWeight", "fontSize", "alignSelf", "subscriptInactiveDark", "subscriptActive", "<PERSON><PERSON>", "props", "children", "onClick", "onLongPress", "disabled", "hint", "isPressed", "ariaDisabled", "subscript", "testID", "icon", "iconSize", "showTooltip", "label", "shortcut", "tooltipPosition", "isActiveStyle", "customContainerStyles", "hitSlop", "preferredColorScheme", "usePreferredColorScheme", "isDisabled", "containerStyle", "buttonActiveColorStyles", "usePreferredColorSchemeStyle", "style", "buttonViewStyle", "opacity", "backgroundColor", "states", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Children", "map", "child", "cloneElement", "colorScheme", "shouldShowTooltip", "Array", "isArray", "length", "newIcon", "_react", "createElement", "default", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useCallback", "nativeEvent", "state", "State", "ACTIVE", "element", "TouchableOpacity", "activeOpacity", "accessible", "accessibilityLabel", "accessibilityStates", "accessibilityRole", "accessibilityHint", "onPress", "LongPressGestureHandler", "minDurationMs", "maxDist", "onHandlerStateChange", "View", "Text", "text", "position", "visible", "_default", "exports"], "sources": ["@wordpress/components/src/button/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tStyleSheet,\n\tTouchableOpacity,\n\tText,\n\tView,\n\tPlatform,\n} from 'react-native';\nimport { LongPressGestureHandler, State } from 'react-native-gesture-handler';\n\n/**\n * WordPress dependencies\n */\nimport { Children, cloneElement, useCallback } from '@wordpress/element';\nimport {\n\tusePreferredColorScheme,\n\tusePreferredColorSchemeStyle,\n} from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport Tooltip from '../tooltip';\nimport Icon from '../icon';\nimport style from './style.scss';\n\nconst isAndroid = Platform.OS === 'android';\nconst marginBottom = isAndroid ? -0.5 : 0;\nconst marginLeft = -3;\n\nconst styles = StyleSheet.create( {\n\tcontainer: {\n\t\tflex: 1,\n\t\tpadding: 3,\n\t\tjustifyContent: 'center',\n\t\talignItems: 'center',\n\t},\n\tbuttonInactive: {\n\t\tflex: 1,\n\t\tflexDirection: 'row',\n\t\tjustifyContent: 'center',\n\t\talignItems: 'center',\n\t},\n\tfixedRatio: {\n\t\taspectRatio: 1,\n\t},\n\tbuttonActive: {\n\t\tflex: 1,\n\t\tflexDirection: 'row',\n\t\tjustifyContent: 'center',\n\t\talignItems: 'center',\n\t\tborderRadius: 6,\n\t},\n\tsubscriptInactive: {\n\t\tcolor: '#7b9ab1', // $toolbar-button.\n\t\tfontWeight: 'bold',\n\t\tfontSize: 13,\n\t\talignSelf: 'flex-end',\n\t\tmarginLeft,\n\t\tmarginBottom,\n\t},\n\tsubscriptInactiveDark: {\n\t\tcolor: '#a7aaad', // $gray_20.\n\t},\n\tsubscriptActive: {\n\t\tcolor: 'white',\n\t\tfontWeight: 'bold',\n\t\tfontSize: 13,\n\t\talignSelf: 'flex-end',\n\t\tmarginLeft,\n\t\tmarginBottom,\n\t},\n} );\n\nexport function Button( props ) {\n\tconst {\n\t\tchildren,\n\t\tonClick,\n\t\tonLongPress,\n\t\tdisabled,\n\t\thint,\n\t\tfixedRatio = true,\n\t\tisPressed,\n\t\t'aria-disabled': ariaDisabled,\n\t\t'data-subscript': subscript,\n\t\ttestID,\n\t\ticon,\n\t\ticonSize,\n\t\tshowTooltip,\n\t\tlabel,\n\t\tshortcut,\n\t\ttooltipPosition,\n\t\tisActiveStyle,\n\t\tcustomContainerStyles,\n\t\thitSlop,\n\t} = props;\n\tconst preferredColorScheme = usePreferredColorScheme();\n\n\tconst isDisabled = ariaDisabled || disabled;\n\n\tconst containerStyle = [\n\t\tstyles.container,\n\t\tcustomContainerStyles && { ...customContainerStyles },\n\t];\n\n\tconst buttonActiveColorStyles = usePreferredColorSchemeStyle(\n\t\tstyle[ 'components-button-light--active' ],\n\t\tstyle[ 'components-button-dark--active' ]\n\t);\n\n\tconst buttonViewStyle = {\n\t\topacity: isDisabled ? 0.3 : 1,\n\t\t...( fixedRatio && styles.fixedRatio ),\n\t\t...( isPressed ? styles.buttonActive : styles.buttonInactive ),\n\t\t...( isPressed ? buttonActiveColorStyles : {} ),\n\t\t...( isPressed &&\n\t\t\tisActiveStyle?.borderRadius && {\n\t\t\t\tborderRadius: isActiveStyle.borderRadius,\n\t\t\t} ),\n\t\t...( isActiveStyle?.backgroundColor && {\n\t\t\tbackgroundColor: isActiveStyle.backgroundColor,\n\t\t} ),\n\t};\n\n\tconst states = [];\n\tif ( isPressed ) {\n\t\tstates.push( 'selected' );\n\t}\n\n\tif ( isDisabled ) {\n\t\tstates.push( 'disabled' );\n\t}\n\n\tconst subscriptInactive = usePreferredColorSchemeStyle(\n\t\tstyles.subscriptInactive,\n\t\tstyles.subscriptInactiveDark\n\t);\n\n\tconst newChildren = Children.map( children, ( child ) => {\n\t\treturn child\n\t\t\t? cloneElement( child, {\n\t\t\t\t\tcolorScheme: preferredColorScheme,\n\t\t\t\t\tisPressed,\n\t\t\t  } )\n\t\t\t: child;\n\t} );\n\n\t// Should show the tooltip if...\n\tconst shouldShowTooltip =\n\t\t! isDisabled &&\n\t\t// An explicit tooltip is passed or...\n\t\t( ( showTooltip && label ) ||\n\t\t\t// There's a shortcut or...\n\t\t\tshortcut ||\n\t\t\t// There's a label and...\n\t\t\t( !! label &&\n\t\t\t\t// The children are empty and...\n\t\t\t\t( ! children ||\n\t\t\t\t\t( Array.isArray( children ) && ! children.length ) ) &&\n\t\t\t\t// The tooltip is not explicitly disabled.\n\t\t\t\tfalse !== showTooltip ) );\n\n\tconst newIcon = icon\n\t\t? cloneElement( <Icon icon={ icon } size={ iconSize } />, {\n\t\t\t\tisPressed,\n\t\t  } )\n\t\t: null;\n\n\tconst longPressHandler = useCallback(\n\t\t( { nativeEvent } ) => {\n\t\t\tif ( nativeEvent.state === State.ACTIVE && onLongPress ) {\n\t\t\t\tonLongPress();\n\t\t\t}\n\t\t},\n\t\t[ onLongPress ]\n\t);\n\n\tconst element = (\n\t\t<TouchableOpacity\n\t\t\tactiveOpacity={ 0.7 }\n\t\t\taccessible={ true }\n\t\t\taccessibilityLabel={ label }\n\t\t\taccessibilityStates={ states }\n\t\t\taccessibilityRole={ 'button' }\n\t\t\taccessibilityHint={ hint }\n\t\t\tonPress={ onClick }\n\t\t\tstyle={ containerStyle }\n\t\t\tdisabled={ isDisabled }\n\t\t\ttestID={ testID }\n\t\t\thitSlop={ hitSlop }\n\t\t>\n\t\t\t<LongPressGestureHandler\n\t\t\t\tminDurationMs={ 500 }\n\t\t\t\tmaxDist={ 150 }\n\t\t\t\tonHandlerStateChange={ longPressHandler }\n\t\t\t>\n\t\t\t\t<View style={ buttonViewStyle }>\n\t\t\t\t\t<View style={ { flexDirection: 'row' } }>\n\t\t\t\t\t\t{ newIcon }\n\t\t\t\t\t\t{ newChildren }\n\t\t\t\t\t\t{ subscript && (\n\t\t\t\t\t\t\t<Text\n\t\t\t\t\t\t\t\tstyle={\n\t\t\t\t\t\t\t\t\tisPressed\n\t\t\t\t\t\t\t\t\t\t? styles.subscriptActive\n\t\t\t\t\t\t\t\t\t\t: subscriptInactive\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{ subscript }\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t) }\n\t\t\t\t\t</View>\n\t\t\t\t</View>\n\t\t\t</LongPressGestureHandler>\n\t\t</TouchableOpacity>\n\t);\n\n\tif ( ! shouldShowTooltip ) {\n\t\treturn element;\n\t}\n\n\treturn (\n\t\t<Tooltip\n\t\t\ttext={ label }\n\t\t\tshortcut={ shortcut }\n\t\t\tposition={ tooltipPosition }\n\t\t\tvisible={ showTooltip === true }\n\t\t>\n\t\t\t{ element }\n\t\t</Tooltip>\n\t);\n}\n\nexport default Button;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAOA,IAAAC,0BAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAQA,IAAAI,QAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,KAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,MAAA,GAAAF,sBAAA,CAAAL,OAAA;AA1BA;AACA;AACA;;AAUA;AACA;AACA;;AAOA;AACA;AACA;;AAKA,MAAMQ,SAAS,GAAGC,qBAAQ,CAACC,EAAE,KAAK,SAAS;AAC3C,MAAMC,YAAY,GAAGH,SAAS,GAAG,CAAC,GAAG,GAAG,CAAC;AACzC,MAAMI,UAAU,GAAG,CAAC,CAAC;AAErB,MAAMC,MAAM,GAAGC,uBAAU,CAACC,MAAM,CAAE;EACjCC,SAAS,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACb,CAAC;EACDC,cAAc,EAAE;IACfJ,IAAI,EAAE,CAAC;IACPK,aAAa,EAAE,KAAK;IACpBH,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACb,CAAC;EACDG,UAAU,EAAE;IACXC,WAAW,EAAE;EACd,CAAC;EACDC,YAAY,EAAE;IACbR,IAAI,EAAE,CAAC;IACPK,aAAa,EAAE,KAAK;IACpBH,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBM,YAAY,EAAE;EACf,CAAC;EACDC,iBAAiB,EAAE;IAClBC,KAAK,EAAE,SAAS;IAAE;IAClBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,UAAU;IACrBnB,UAAU;IACVD;EACD,CAAC;EACDqB,qBAAqB,EAAE;IACtBJ,KAAK,EAAE,SAAS,CAAE;EACnB,CAAC;;EACDK,eAAe,EAAE;IAChBL,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,UAAU;IACrBnB,UAAU;IACVD;EACD;AACD,CAAE,CAAC;AAEI,SAASuB,MAAMA,CAAEC,KAAK,EAAG;EAC/B,MAAM;IACLC,QAAQ;IACRC,OAAO;IACPC,WAAW;IACXC,QAAQ;IACRC,IAAI;IACJjB,UAAU,GAAG,IAAI;IACjBkB,SAAS;IACT,eAAe,EAAEC,YAAY;IAC7B,gBAAgB,EAAEC,SAAS;IAC3BC,MAAM;IACNC,IAAI;IACJC,QAAQ;IACRC,WAAW;IACXC,KAAK;IACLC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACbC,qBAAqB;IACrBC;EACD,CAAC,GAAGlB,KAAK;EACT,MAAMmB,oBAAoB,GAAG,IAAAC,gCAAuB,EAAC,CAAC;EAEtD,MAAMC,UAAU,GAAGd,YAAY,IAAIH,QAAQ;EAE3C,MAAMkB,cAAc,GAAG,CACtB5C,MAAM,CAACG,SAAS,EAChBoC,qBAAqB,IAAI;IAAE,GAAGA;EAAsB,CAAC,CACrD;EAED,MAAMM,uBAAuB,GAAG,IAAAC,qCAA4B,EAC3DC,cAAK,CAAE,iCAAiC,CAAE,EAC1CA,cAAK,CAAE,gCAAgC,CACxC,CAAC;EAED,MAAMC,eAAe,GAAG;IACvBC,OAAO,EAAEN,UAAU,GAAG,GAAG,GAAG,CAAC;IAC7B,IAAKjC,UAAU,IAAIV,MAAM,CAACU,UAAU,CAAE;IACtC,IAAKkB,SAAS,GAAG5B,MAAM,CAACY,YAAY,GAAGZ,MAAM,CAACQ,cAAc,CAAE;IAC9D,IAAKoB,SAAS,GAAGiB,uBAAuB,GAAG,CAAC,CAAC,CAAE;IAC/C,IAAKjB,SAAS,IACbU,aAAa,EAAEzB,YAAY,IAAI;MAC9BA,YAAY,EAAEyB,aAAa,CAACzB;IAC7B,CAAC,CAAE;IACJ,IAAKyB,aAAa,EAAEY,eAAe,IAAI;MACtCA,eAAe,EAAEZ,aAAa,CAACY;IAChC,CAAC;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAKvB,SAAS,EAAG;IAChBuB,MAAM,CAACC,IAAI,CAAE,UAAW,CAAC;EAC1B;EAEA,IAAKT,UAAU,EAAG;IACjBQ,MAAM,CAACC,IAAI,CAAE,UAAW,CAAC;EAC1B;EAEA,MAAMtC,iBAAiB,GAAG,IAAAgC,qCAA4B,EACrD9C,MAAM,CAACc,iBAAiB,EACxBd,MAAM,CAACmB,qBACR,CAAC;EAED,MAAMkC,WAAW,GAAGC,iBAAQ,CAACC,GAAG,CAAEhC,QAAQ,EAAIiC,KAAK,IAAM;IACxD,OAAOA,KAAK,GACT,IAAAC,qBAAY,EAAED,KAAK,EAAE;MACrBE,WAAW,EAAEjB,oBAAoB;MACjCb;IACA,CAAE,CAAC,GACH4B,KAAK;EACT,CAAE,CAAC;;EAEH;EACA,MAAMG,iBAAiB,GACtB,CAAEhB,UAAU;EACZ;EACIT,WAAW,IAAIC,KAAK;EACvB;EACAC,QAAQ;EACR;EACE,CAAC,CAAED,KAAK;EACT;EACE,CAAEZ,QAAQ,IACTqC,KAAK,CAACC,OAAO,CAAEtC,QAAS,CAAC,IAAI,CAAEA,QAAQ,CAACuC,MAAQ,CAAE;EACrD;EACA,KAAK,KAAK5B,WAAa,CAAE;EAE5B,MAAM6B,OAAO,GAAG/B,IAAI,GACjB,IAAAyB,qBAAY,EAAE,IAAAO,MAAA,CAAAC,aAAA,EAACxE,KAAA,CAAAyE,OAAI;IAAClC,IAAI,EAAGA,IAAM;IAACmC,IAAI,EAAGlC;EAAU,CAAE,CAAC,EAAE;IACxDL;EACA,CAAE,CAAC,GACH,IAAI;EAEP,MAAMwC,gBAAgB,GAAG,IAAAC,oBAAW,EACnC,CAAE;IAAEC;EAAY,CAAC,KAAM;IACtB,IAAKA,WAAW,CAACC,KAAK,KAAKC,gCAAK,CAACC,MAAM,IAAIhD,WAAW,EAAG;MACxDA,WAAW,CAAC,CAAC;IACd;EACD,CAAC,EACD,CAAEA,WAAW,CACd,CAAC;EAED,MAAMiD,OAAO,GACZ,IAAAV,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAyF,gBAAgB;IAChBC,aAAa,EAAG,GAAK;IACrBC,UAAU,EAAG,IAAM;IACnBC,kBAAkB,EAAG3C,KAAO;IAC5B4C,mBAAmB,EAAG5B,MAAQ;IAC9B6B,iBAAiB,EAAG,QAAU;IAC9BC,iBAAiB,EAAGtD,IAAM;IAC1BuD,OAAO,EAAG1D,OAAS;IACnBuB,KAAK,EAAGH,cAAgB;IACxBlB,QAAQ,EAAGiB,UAAY;IACvBZ,MAAM,EAAGA,MAAQ;IACjBS,OAAO,EAAGA;EAAS,GAEnB,IAAAwB,MAAA,CAAAC,aAAA,EAAC7E,0BAAA,CAAA+F,uBAAuB;IACvBC,aAAa,EAAG,GAAK;IACrBC,OAAO,EAAG,GAAK;IACfC,oBAAoB,EAAGlB;EAAkB,GAEzC,IAAAJ,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAqG,IAAI;IAACxC,KAAK,EAAGC;EAAiB,GAC9B,IAAAgB,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAqG,IAAI;IAACxC,KAAK,EAAG;MAAEtC,aAAa,EAAE;IAAM;EAAG,GACrCsD,OAAO,EACPV,WAAW,EACXvB,SAAS,IACV,IAAAkC,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAsG,IAAI;IACJzC,KAAK,EACJnB,SAAS,GACN5B,MAAM,CAACoB,eAAe,GACtBN;EACH,GAECgB,SACG,CAEF,CACD,CACkB,CACR,CAClB;EAED,IAAK,CAAE6B,iBAAiB,EAAG;IAC1B,OAAOe,OAAO;EACf;EAEA,OACC,IAAAV,MAAA,CAAAC,aAAA,EAAC1E,QAAA,CAAA2E,OAAO;IACPuB,IAAI,EAAGtD,KAAO;IACdC,QAAQ,EAAGA,QAAU;IACrBsD,QAAQ,EAAGrD,eAAiB;IAC5BsD,OAAO,EAAGzD,WAAW,KAAK;EAAM,GAE9BwC,OACM,CAAC;AAEZ;AAAC,IAAAkB,QAAA,GAEcvE,MAAM;AAAAwE,OAAA,CAAA3B,OAAA,GAAA0B,QAAA"}