{"version": 3, "names": ["_context", "require", "_flex", "_hook", "UnconnectedCardFooter", "props", "forwardedRef", "footerProps", "useCardFooter", "_react", "createElement", "Flex", "ref", "<PERSON><PERSON><PERSON>er", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/card/card-footer/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { Flex } from '../../flex';\nimport { useCardFooter } from './hook';\nimport type { FooterProps } from '../types';\n\nfunction UnconnectedCardFooter(\n\tprops: WordPressComponentProps< FooterProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst footerProps = useCardFooter( props );\n\n\treturn <Flex { ...footerProps } ref={ forwardedRef } />;\n}\n\n/**\n * `CardFooter` renders an optional footer within a `Card`.\n *\n * ```jsx\n * import { Card, CardBody, CardFooter } from `@wordpress/components`;\n *\n * <Card>\n * \t<CardBody>...</CardBody>\n * \t<CardFooter>...</CardFooter>\n * </Card>\n * ```\n */\nexport const CardFooter = contextConnect( UnconnectedCardFooter, 'CardFooter' );\n\nexport default CardFooter;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,qBAAqBA,CAC7BC,KAAoD,EACpDC,YAAiC,EAChC;EACD,MAAMC,WAAW,GAAG,IAAAC,mBAAa,EAAEH,KAAM,CAAC;EAE1C,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,WAAW;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,UAAU,GAAG,IAAAC,uBAAc,EAAEV,qBAAqB,EAAE,YAAa,CAAC;AAACW,OAAA,CAAAF,UAAA,GAAAA,UAAA;AAAA,IAAAG,QAAA,GAEjEH,UAAU;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}