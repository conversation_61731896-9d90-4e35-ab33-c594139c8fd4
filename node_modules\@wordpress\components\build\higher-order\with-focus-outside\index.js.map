{"version": 3, "names": ["_element", "require", "_compose", "_default", "createHigherOrderComponent", "WrappedComponent", "props", "handleFocusOutside", "setHandleFocusOutside", "useState", "undefined", "bindFocusOutsideHandler", "useCallback", "node", "bind", "_react", "createElement", "useFocusOutside", "ref", "exports", "default"], "sources": ["@wordpress/components/src/higher-order/with-focus-outside/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useCallback, useState } from '@wordpress/element';\nimport {\n\tcreateHigherOrderComponent,\n\t__experimentalUseFocusOutside as useFocusOutside,\n} from '@wordpress/compose';\n\nexport default createHigherOrderComponent(\n\t( WrappedComponent ) => ( props ) => {\n\t\tconst [ handleFocusOutside, setHandleFocusOutside ] = useState<\n\t\t\tundefined | ( ( event: React.FocusEvent ) => void )\n\t\t>( undefined );\n\n\t\tconst bindFocusOutsideHandler = useCallback<\n\t\t\t( node: React.FocusEvent ) => void\n\t\t>(\n\t\t\t( node: any ) =>\n\t\t\t\tsetHandleFocusOutside( () =>\n\t\t\t\t\tnode?.handleFocusOutside\n\t\t\t\t\t\t? node.handleFocusOutside.bind( node )\n\t\t\t\t\t\t: undefined\n\t\t\t\t),\n\t\t\t[]\n\t\t);\n\n\t\treturn (\n\t\t\t<div { ...useFocusOutside( handleFocusOutside ) }>\n\t\t\t\t<WrappedComponent\n\t\t\t\t\tref={ bindFocusOutsideHandler }\n\t\t\t\t\t{ ...props }\n\t\t\t\t/>\n\t\t\t</div>\n\t\t);\n\t},\n\t'withFocusOutside'\n);\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAJA;AACA;AACA;AAFA,IAAAE,QAAA,GASe,IAAAC,mCAA0B,EACtCC,gBAAgB,IAAQC,KAAK,IAAM;EACpC,MAAM,CAAEC,kBAAkB,EAAEC,qBAAqB,CAAE,GAAG,IAAAC,iBAAQ,EAE3DC,SAAU,CAAC;EAEd,MAAMC,uBAAuB,GAAG,IAAAC,oBAAW,EAGxCC,IAAS,IACVL,qBAAqB,CAAE,MACtBK,IAAI,EAAEN,kBAAkB,GACrBM,IAAI,CAACN,kBAAkB,CAACO,IAAI,CAAED,IAAK,CAAC,GACpCH,SACJ,CAAC,EACF,EACD,CAAC;EAED,OACC,IAAAK,MAAA,CAAAC,aAAA;IAAA,GAAU,IAAAC,sCAAe,EAAEV,kBAAmB;EAAC,GAC9C,IAAAQ,MAAA,CAAAC,aAAA,EAACX,gBAAgB;IAChBa,GAAG,EAAGP,uBAAyB;IAAA,GAC1BL;EAAK,CACV,CACG,CAAC;AAER,CAAC,EACD,kBACD,CAAC;AAAAa,OAAA,CAAAC,OAAA,GAAAjB,QAAA"}