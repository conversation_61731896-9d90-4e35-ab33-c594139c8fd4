{"version": 3, "names": ["_reactNative", "require", "_element", "_components", "Fill", "Slot", "createSlotFill", "BottomSheetSubSheet", "children", "navigationButton", "showSheet", "isFullScreen", "setIsFullScreen", "useContext", "BottomSheetContext", "useEffect", "_react", "createElement", "Fragment", "SafeAreaView", "Children", "count", "screenName", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/sub-sheet/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { SafeAreaView } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { Children, useEffect, useContext } from '@wordpress/element';\nimport { createSlotFill, BottomSheetContext } from '@wordpress/components';\n\nconst { Fill, Slot } = createSlotFill( 'BottomSheetSubSheet' );\n\nconst BottomSheetSubSheet = ( {\n\tchildren,\n\tnavigationButton,\n\tshowSheet,\n\tisFullScreen,\n} ) => {\n\tconst { setIsFullScreen } = useContext( BottomSheetContext );\n\n\tuseEffect( () => {\n\t\tif ( showSheet ) {\n\t\t\tsetIsFullScreen( isFullScreen );\n\t\t}\n\t\t// Disable reason: deferring this refactor to the native team.\n\t\t// see https://github.com/WordPress/gutenberg/pull/41166\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [ showSheet, isFullScreen ] );\n\n\treturn (\n\t\t<>\n\t\t\t{ showSheet && (\n\t\t\t\t<Fill>\n\t\t\t\t\t<SafeAreaView>{ children }</SafeAreaView>\n\t\t\t\t</Fill>\n\t\t\t) }\n\t\t\t{ Children.count( children ) > 0 && navigationButton }\n\t\t</>\n\t);\n};\n\nBottomSheetSubSheet.Slot = Slot;\nBottomSheetSubSheet.screenName = 'BottomSheetSubSheet';\n\nexport default BottomSheetSubSheet;\n"], "mappings": ";;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AAIA,MAAM;EAAEG,IAAI;EAAEC;AAAK,CAAC,GAAG,IAAAC,0BAAc,EAAE,qBAAsB,CAAC;AAE9D,MAAMC,mBAAmB,GAAGA,CAAE;EAC7BC,QAAQ;EACRC,gBAAgB;EAChBC,SAAS;EACTC;AACD,CAAC,KAAM;EACN,MAAM;IAAEC;EAAgB,CAAC,GAAG,IAAAC,mBAAU,EAAEC,8BAAmB,CAAC;EAE5D,IAAAC,kBAAS,EAAE,MAAM;IAChB,IAAKL,SAAS,EAAG;MAChBE,eAAe,CAAED,YAAa,CAAC;IAChC;IACA;IACA;IACA;EACD,CAAC,EAAE,CAAED,SAAS,EAAEC,YAAY,CAAG,CAAC;EAEhC,OACC,IAAAK,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGR,SAAS,IACV,IAAAM,MAAA,CAAAC,aAAA,EAACb,IAAI,QACJ,IAAAY,MAAA,CAAAC,aAAA,EAACjB,YAAA,CAAAmB,YAAY,QAAGX,QAAwB,CACnC,CACN,EACCY,iBAAQ,CAACC,KAAK,CAAEb,QAAS,CAAC,GAAG,CAAC,IAAIC,gBACnC,CAAC;AAEL,CAAC;AAEDF,mBAAmB,CAACF,IAAI,GAAGA,IAAI;AAC/BE,mBAAmB,CAACe,UAAU,GAAG,qBAAqB;AAAC,IAAAC,QAAA,GAExChB,mBAAmB;AAAAiB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}