"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.colorsUtils = void 0;
var _i18n = require("@wordpress/i18n");
/**
 * WordPress dependencies
 */

const gradients = {
  linear: 'linear-gradient',
  radial: 'radial-gradient'
};
const gradientOptions = [{
  label: (0, _i18n.__)('Linear'),
  value: gradients.linear
}, {
  label: (0, _i18n.__)('Radial'),
  value: gradients.radial
}];
const getGradientType = color => {
  if (color?.includes(gradients.radial)) {
    return gradients.radial;
  } else if (color?.includes(gradients.linear)) {
    return gradients.linear;
  }
  return false;
};
const colorsUtils = {
  screens: {
    gradientPicker: 'GradientPicker',
    picker: 'Picker',
    palette: 'Palette'
  },
  segments: [(0, _i18n.__)('Solid'), (0, _i18n.__)('Gradient')],
  gradients,
  gradientOptions,
  getGradientType,
  isGradient: color => !!getGradientType(color)
};
exports.colorsUtils = colorsUtils;
//# sourceMappingURL=utils.native.js.map