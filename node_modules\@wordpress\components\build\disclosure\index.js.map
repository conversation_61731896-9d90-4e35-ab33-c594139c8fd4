{"version": 3, "names": ["Ariakit", "_interopRequireWildcard", "require", "_element", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "UnforwardedDisclosureContent", "visible", "children", "props", "ref", "disclosure", "useDisclosureStore", "open", "_react", "createElement", "DisclosureContent", "store", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/disclosure/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport * as Ariakit from '@ariakit/react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { DisclosureContentProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\n/**\n * Accessible Disclosure component that controls visibility of a section of\n * content. It follows the WAI-ARIA Disclosure Pattern.\n */\nconst UnforwardedDisclosureContent = (\n\t{\n\t\tvisible,\n\t\tchildren,\n\t\t...props\n\t}: WordPressComponentProps< DisclosureContentProps, 'div', false >,\n\tref: React.ForwardedRef< any >\n) => {\n\tconst disclosure = Ariakit.useDisclosureStore( { open: visible } );\n\n\treturn (\n\t\t<Ariakit.DisclosureContent\n\t\t\tstore={ disclosure }\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t>\n\t\t\t{ children }\n\t\t</Ariakit.DisclosureContent>\n\t);\n};\n\nexport const DisclosureContent = forwardRef( UnforwardedDisclosureContent );\nexport default DisclosureContent;\n"], "mappings": ";;;;;;;AAIA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAAgD,SAAAE,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAThD;AACA;AACA;AACA;;AAGA;AACA;AACA;;AASA;AACA;AACA;AACA;AACA,MAAMW,4BAA4B,GAAGA,CACpC;EACCC,OAAO;EACPC,QAAQ;EACR,GAAGC;AAC6D,CAAC,EAClEC,GAA8B,KAC1B;EACJ,MAAMC,UAAU,GAAG/B,OAAO,CAACgC,kBAAkB,CAAE;IAAEC,IAAI,EAAEN;EAAQ,CAAE,CAAC;EAElE,OACC,IAAAO,MAAA,CAAAC,aAAA,EAACnC,OAAO,CAACoC,iBAAiB;IACzBC,KAAK,EAAGN,UAAY;IACpBD,GAAG,EAAGA,GAAK;IAAA,GACND;EAAK,GAERD,QACwB,CAAC;AAE9B,CAAC;AAEM,MAAMQ,iBAAiB,GAAG,IAAAE,mBAAU,EAAEZ,4BAA6B,CAAC;AAACa,OAAA,CAAAH,iBAAA,GAAAA,iBAAA;AAAA,IAAAI,QAAA,GAC7DJ,iBAAiB;AAAAG,OAAA,CAAA5B,OAAA,GAAA6B,QAAA"}