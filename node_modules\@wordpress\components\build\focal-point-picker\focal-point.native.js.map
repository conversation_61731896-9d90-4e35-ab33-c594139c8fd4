{"version": 3, "names": ["_primitives", "require", "_style", "_interopRequireDefault", "FocalPoint", "height", "style", "width", "_react", "createElement", "SVG", "viewBox", "xmlns", "Path", "styles", "focalPointIconPathOutline", "d", "focalPointIconPathFill"], "sources": ["@wordpress/components/src/focal-point-picker/focal-point.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { Path, SVG } from '@wordpress/primitives';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nexport default function FocalPoint( { height, style, width } ) {\n\treturn (\n\t\t<SVG\n\t\t\theight={ height }\n\t\t\tstyle={ style }\n\t\t\tviewBox=\"0 0 30 30\"\n\t\t\twidth={ width }\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t>\n\t\t\t<Path\n\t\t\t\tstyle={ styles.focalPointIconPathOutline }\n\t\t\t\td=\"M15 1C7.3 1 1 7.3 1 15s6.3 14 14 14 14-6.3 14-14S22.7 1 15 1zm0 22c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8z\"\n\t\t\t/>\n\t\t\t<Path\n\t\t\t\tstyle={ styles.focalPointIconPathFill }\n\t\t\t\td=\"M15 3C8.4 3 3 8.4 3 15s5.4 12 12 12 12-5.4 12-12S21.6 3 15 3zm0 22C9.5 25 5 20.5 5 15S9.5 5 15 5s10 4.5 10 10-4.5 10-10 10z\"\n\t\t\t/>\n\t\t</SVG>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAGe,SAASG,UAAUA,CAAE;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAM,CAAC,EAAG;EAC9D,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACT,WAAA,CAAAU,GAAG;IACHL,MAAM,EAAGA,MAAQ;IACjBC,KAAK,EAAGA,KAAO;IACfK,OAAO,EAAC,WAAW;IACnBJ,KAAK,EAAGA,KAAO;IACfK,KAAK,EAAC;EAA4B,GAElC,IAAAJ,MAAA,CAAAC,aAAA,EAACT,WAAA,CAAAa,IAAI;IACJP,KAAK,EAAGQ,cAAM,CAACC,yBAA2B;IAC1CC,CAAC,EAAC;EAAoH,CACtH,CAAC,EACF,IAAAR,MAAA,CAAAC,aAAA,EAACT,WAAA,CAAAa,IAAI;IACJP,KAAK,EAAGQ,cAAM,CAACG,sBAAwB;IACvCD,CAAC,EAAC;EAA6H,CAC/H,CACG,CAAC;AAER"}