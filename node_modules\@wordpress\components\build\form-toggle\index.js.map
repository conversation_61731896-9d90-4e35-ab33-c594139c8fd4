{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "noop", "exports", "FormToggle", "props", "className", "checked", "id", "disabled", "onChange", "additionalProps", "wrapperClasses", "classnames", "_react", "createElement", "type", "_default", "default"], "sources": ["@wordpress/components/src/form-toggle/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * Internal dependencies\n */\nimport type { FormToggleProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\nexport const noop = () => {};\n\n/**\n * FormToggle switches a single setting on or off.\n *\n * ```jsx\n * import { FormToggle } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyFormToggle = () => {\n *   const [ isChecked, setChecked ] = useState( true );\n *\n *   return (\n *     <FormToggle\n *       checked={ isChecked }\n *       onChange={ () => setChecked( ( state ) => ! state ) }\n *     />\n *   );\n * };\n * ```\n */\nexport function FormToggle(\n\tprops: WordPressComponentProps< FormToggleProps, 'input', false >\n) {\n\tconst {\n\t\tclassName,\n\t\tchecked,\n\t\tid,\n\t\tdisabled,\n\t\tonChange = noop,\n\t\t...additionalProps\n\t} = props;\n\tconst wrapperClasses = classnames( 'components-form-toggle', className, {\n\t\t'is-checked': checked,\n\t\t'is-disabled': disabled,\n\t} );\n\n\treturn (\n\t\t<span className={ wrapperClasses }>\n\t\t\t<input\n\t\t\t\tclassName=\"components-form-toggle__input\"\n\t\t\t\tid={ id }\n\t\t\t\ttype=\"checkbox\"\n\t\t\t\tchecked={ checked }\n\t\t\t\tonChange={ onChange }\n\t\t\t\tdisabled={ disabled }\n\t\t\t\t{ ...additionalProps }\n\t\t\t/>\n\t\t\t<span className=\"components-form-toggle__track\"></span>\n\t\t\t<span className=\"components-form-toggle__thumb\"></span>\n\t\t</span>\n\t);\n}\n\nexport default FormToggle;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAHA;AACA;AACA;;AASO,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBAC,OAAA,CAAAD,IAAA,GAAAA,IAAA;AAmBO,SAASE,UAAUA,CACzBC,KAAiE,EAChE;EACD,MAAM;IACLC,SAAS;IACTC,OAAO;IACPC,EAAE;IACFC,QAAQ;IACRC,QAAQ,GAAGR,IAAI;IACf,GAAGS;EACJ,CAAC,GAAGN,KAAK;EACT,MAAMO,cAAc,GAAG,IAAAC,mBAAU,EAAE,wBAAwB,EAAEP,SAAS,EAAE;IACvE,YAAY,EAAEC,OAAO;IACrB,aAAa,EAAEE;EAChB,CAAE,CAAC;EAEH,OACC,IAAAK,MAAA,CAAAC,aAAA;IAAMT,SAAS,EAAGM;EAAgB,GACjC,IAAAE,MAAA,CAAAC,aAAA;IACCT,SAAS,EAAC,+BAA+B;IACzCE,EAAE,EAAGA,EAAI;IACTQ,IAAI,EAAC,UAAU;IACfT,OAAO,EAAGA,OAAS;IACnBG,QAAQ,EAAGA,QAAU;IACrBD,QAAQ,EAAGA,QAAU;IAAA,GAChBE;EAAe,CACpB,CAAC,EACF,IAAAG,MAAA,CAAAC,aAAA;IAAMT,SAAS,EAAC;EAA+B,CAAO,CAAC,EACvD,IAAAQ,MAAA,CAAAC,aAAA;IAAMT,SAAS,EAAC;EAA+B,CAAO,CACjD,CAAC;AAET;AAAC,IAAAW,QAAA,GAEcb,UAAU;AAAAD,OAAA,CAAAe,OAAA,GAAAD,QAAA"}