"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _element = require("@wordpress/element");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * This is a simplified version of Facebook's KeyboardAvoidingView.
 * It's meant to work specifically with BottomSheets.
 * This fixes an issue in the bottom padding calculation, when the
 * BottomSheet was presented on Landscape, with the keyboard already present,
 * and a TextField on Autofocus (situation present on Links UI)
 */
class KeyboardAvoidingView extends _element.Component {
  constructor() {
    super(...arguments);
    this._onKeyboardChange = this._onKeyboardChange.bind(this);
    this._subscriptions = [];
    this.state = {
      bottom: 0
    };
  }
  _relativeKeyboardHeight(keyboardFrame) {
    if (!keyboardFrame) {
      return 0;
    }
    const windowWidth = _reactNative.Dimensions.get('window').width;
    const isFloatingKeyboard = keyboardFrame.width !== windowWidth;
    if (isFloatingKeyboard) {
      return 0;
    }
    const windowHeight = _reactNative.Dimensions.get('window').height;
    const keyboardY = keyboardFrame.screenY - this.props.keyboardVerticalOffset;
    const final = Math.max(windowHeight - keyboardY, 0);
    return final;
  }

  /**
   * @param {Object} event Keyboard event.
   */
  _onKeyboardChange(event) {
    if (event === null) {
      this.setState({
        bottom: 0
      });
      return;
    }
    const {
      duration,
      easing,
      endCoordinates
    } = event;
    const height = this._relativeKeyboardHeight(endCoordinates);
    if (this.state.bottom === height) {
      return;
    }
    if (duration && easing) {
      _reactNative.LayoutAnimation.configureNext({
        duration,
        update: {
          duration,
          type: _reactNative.LayoutAnimation.Types[easing] || 'keyboard'
        }
      });
    }
    this.setState({
      bottom: height
    });
  }
  componentDidMount() {
    if (_reactNative.Platform.OS === 'ios') {
      this._subscriptions = [_reactNative.Keyboard.addListener('keyboardWillChangeFrame', this._onKeyboardChange)];
    }
  }
  componentWillUnmount() {
    this._subscriptions.forEach(subscription => {
      subscription.remove();
    });
  }
  render() {
    const {
      children,
      enabled,
      keyboardVerticalOffset,
      style,
      ...props
    } = this.props;
    let finalStyle = style;
    if (_reactNative.Platform.OS === 'ios') {
      const bottomHeight = enabled ? this.state.bottom : 0;
      finalStyle = _reactNative.StyleSheet.compose(style, {
        paddingBottom: bottomHeight
      });
    }
    return (0, _react.createElement)(_reactNative.View, {
      style: finalStyle,
      ...props
    }, children);
  }
}
KeyboardAvoidingView.defaultProps = {
  enabled: true,
  keyboardVerticalOffset: 0
};
var _default = KeyboardAvoidingView;
exports.default = _default;
//# sourceMappingURL=keyboard-avoiding-view.native.js.map