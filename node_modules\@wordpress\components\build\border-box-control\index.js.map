{"version": 3, "names": ["_component", "_interopRequireDefault", "require", "_hook", "_utils"], "sources": ["@wordpress/components/src/border-box-control/index.ts"], "sourcesContent": ["export { default as BorderBoxControl } from './border-box-control/component';\nexport { useBorderBoxControl } from './border-box-control/hook';\nexport { hasSplitBorders, isEmptyBorder, isDefinedBorder } from './utils';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA"}