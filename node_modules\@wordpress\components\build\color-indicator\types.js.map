{"version": 3, "names": [], "sources": ["@wordpress/components/src/color-indicator/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\nexport type ColorIndicatorProps = {\n\t/**\n\t * The color of the indicator. Any value from the CSS `background` property\n\t * is supported.\n\t */\n\tcolorValue: CSSProperties[ 'background' ];\n};\n"], "mappings": ""}