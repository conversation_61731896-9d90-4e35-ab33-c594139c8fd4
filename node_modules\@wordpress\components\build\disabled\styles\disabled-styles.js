"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.disabledStyles = void 0;
var _react = require("@emotion/react");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const disabledStyles = process.env.NODE_ENV === "production" ? {
  name: "u2jump",
  styles: "position:relative;pointer-events:none;&::after{content:'';position:absolute;top:0;right:0;bottom:0;left:0;}*{pointer-events:none;}"
} : {
  name: "iqemdn-disabledStyles",
  styles: "position:relative;pointer-events:none;&::after{content:'';position:absolute;top:0;right:0;bottom:0;left:0;}*{pointer-events:none;};label:disabledStyles;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZGlzYWJsZWQvc3R5bGVzL2Rpc2FibGVkLXN0eWxlcy50c3giXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBS2lDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZGlzYWJsZWQvc3R5bGVzL2Rpc2FibGVkLXN0eWxlcy50c3giLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5cbmV4cG9ydCBjb25zdCBkaXNhYmxlZFN0eWxlcyA9IGNzc2Bcblx0cG9zaXRpb246IHJlbGF0aXZlO1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblxuXHQmOjphZnRlciB7XG5cdFx0Y29udGVudDogJyc7XG5cdFx0cG9zaXRpb246IGFic29sdXRlO1xuXHRcdHRvcDogMDtcblx0XHRyaWdodDogMDtcblx0XHRib3R0b206IDA7XG5cdFx0bGVmdDogMDtcblx0fVxuXG5cdC8vIEFsc28gbWFrZSBuZXN0ZWQgYmxvY2tzIHVuc2VsZWN0YWJsZS5cblx0KiB7XG5cdFx0cG9pbnRlci1ldmVudHM6IG5vbmU7XG5cdH1cbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
exports.disabledStyles = disabledStyles;
//# sourceMappingURL=disabled-styles.js.map