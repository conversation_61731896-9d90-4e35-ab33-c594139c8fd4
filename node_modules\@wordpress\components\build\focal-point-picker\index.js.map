{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_i18n", "_element", "_compose", "_baseControl", "_controls", "_focalPoint", "_grid", "_media", "_focalPointPickerStyle", "_utils", "_hooks", "GRID_OVERLAY_TIMEOUT", "FocalPointPicker", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "autoPlay", "className", "help", "label", "onChange", "onDrag", "onDragEnd", "onDragStart", "resolvePoint", "url", "value", "valueProp", "x", "y", "restProps", "point", "setPoint", "useState", "showGridOverlay", "setShowGridOverlay", "startDrag", "endDrag", "isDragging", "useDragging", "event", "dragAreaRef", "current", "focus", "getValueWithinDragArea", "onDragMove", "preventDefault", "useRef", "bounds", "setBounds", "INITIAL_BOUNDS", "refUpdateBounds", "clientWidth", "width", "clientHeight", "height", "useEffect", "updateBounds", "defaultView", "ownerDocument", "addEventListener", "removeEventListener", "useIsomorphicLayoutEffect", "clientX", "clientY", "shift<PERSON>ey", "top", "left", "getBoundingClientRect", "nextX", "nextY", "Math", "round", "getFinalValue", "_resolvePoint", "resolvedValue", "max", "min", "roundToTwoDecimalPlaces", "n", "arrowKeyStep", "code", "includes", "step", "delta", "axis", "focalPointPosition", "classes", "classnames", "instanceId", "useInstanceId", "id", "useUpdateEffect", "timeout", "window", "setTimeout", "clearTimeout", "_react", "createElement", "default", "MediaWrapper", "MediaContainer", "onKeyDown", "onMouseDown", "onBlur", "ref", "role", "tabIndex", "showOverlay", "alt", "__", "onLoad", "src", "hasHelpText", "_default", "exports"], "sources": ["@wordpress/components/src/focal-point-picker/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { useEffect, useRef, useState } from '@wordpress/element';\nimport {\n\t__experimentalUseDragging as useDragging,\n\tuseInstanceId,\n\tuseIsomorphicLayoutEffect,\n} from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport BaseControl from '../base-control';\nimport Controls from './controls';\nimport FocalPoint from './focal-point';\nimport Grid from './grid';\nimport Media from './media';\nimport {\n\tMediaWrapper,\n\tMediaContainer,\n} from './styles/focal-point-picker-style';\nimport { INITIAL_BOUNDS } from './utils';\nimport { useUpdateEffect } from '../utils/hooks';\nimport type { WordPressComponentProps } from '../context/wordpress-component';\nimport type {\n\tFocalPoint as FocalPointType,\n\tFocalPointPickerProps,\n} from './types';\nimport type { KeyboardEventHandler } from 'react';\n\nconst GRID_OVERLAY_TIMEOUT = 600;\n\n/**\n * Focal Point Picker is a component which creates a UI for identifying the most important visual point of an image.\n *\n * This component addresses a specific problem: with large background images it is common to see undesirable crops,\n * especially when viewing on smaller viewports such as mobile phones. This component allows the selection of\n * the point with the most important visual information and returns it as a pair of numbers between 0 and 1.\n * This value can be easily converted into the CSS `background-position` attribute, and will ensure that the\n * focal point is never cropped out, regardless of viewport.\n *\n * - Example focal point picker value: `{ x: 0.5, y: 0.1 }`\n * - Corresponding CSS: `background-position: 50% 10%;`\n *\n * ```jsx\n * import { FocalPointPicker } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const Example = () => {\n * \tconst [ focalPoint, setFocalPoint ] = useState( {\n * \t\tx: 0.5,\n * \t\ty: 0.5,\n * \t} );\n *\n * \tconst url = '/path/to/image';\n *\n * \t// Example function to render the CSS styles based on Focal Point Picker value\n * \tconst style = {\n * \t\tbackgroundImage: `url(${ url })`,\n * \t\tbackgroundPosition: `${ focalPoint.x * 100 }% ${ focalPoint.y * 100 }%`,\n * \t};\n *\n * \treturn (\n * \t\t<>\n * \t\t\t<FocalPointPicker\n * \t\t\t\turl={ url }\n * \t\t\t\tvalue={ focalPoint }\n * \t\t\t\tonDragStart={ setFocalPoint }\n * \t\t\t\tonDrag={ setFocalPoint }\n * \t\t\t\tonChange={ setFocalPoint }\n * \t\t\t/>\n * \t\t\t<div style={ style } />\n * \t\t</>\n * \t);\n * };\n * ```\n */\nexport function FocalPointPicker( {\n\t__nextHasNoMarginBottom,\n\t__next40pxDefaultSize = false,\n\tautoPlay = true,\n\tclassName,\n\thelp,\n\tlabel,\n\tonChange,\n\tonDrag,\n\tonDragEnd,\n\tonDragStart,\n\tresolvePoint,\n\turl,\n\tvalue: valueProp = {\n\t\tx: 0.5,\n\t\ty: 0.5,\n\t},\n\t...restProps\n}: WordPressComponentProps< FocalPointPickerProps, 'div', false > ) {\n\tconst [ point, setPoint ] = useState( valueProp );\n\tconst [ showGridOverlay, setShowGridOverlay ] = useState( false );\n\n\tconst { startDrag, endDrag, isDragging } = useDragging( {\n\t\tonDragStart: ( event ) => {\n\t\t\tdragAreaRef.current?.focus();\n\t\t\tconst value = getValueWithinDragArea( event );\n\n\t\t\t// `value` can technically be undefined if getValueWithinDragArea() is\n\t\t\t// called before dragAreaRef is set, but this shouldn't happen in reality.\n\t\t\tif ( ! value ) return;\n\n\t\t\tonDragStart?.( value, event );\n\t\t\tsetPoint( value );\n\t\t},\n\t\tonDragMove: ( event ) => {\n\t\t\t// Prevents text-selection when dragging.\n\t\t\tevent.preventDefault();\n\t\t\tconst value = getValueWithinDragArea( event );\n\t\t\tif ( ! value ) return;\n\t\t\tonDrag?.( value, event );\n\t\t\tsetPoint( value );\n\t\t},\n\t\tonDragEnd: () => {\n\t\t\tonDragEnd?.();\n\t\t\tonChange?.( point );\n\t\t},\n\t} );\n\n\t// Uses the internal point while dragging or else the value from props.\n\tconst { x, y } = isDragging ? point : valueProp;\n\n\tconst dragAreaRef = useRef< HTMLDivElement >( null );\n\tconst [ bounds, setBounds ] = useState( INITIAL_BOUNDS );\n\tconst refUpdateBounds = useRef( () => {\n\t\tif ( ! dragAreaRef.current ) return;\n\n\t\tconst { clientWidth: width, clientHeight: height } =\n\t\t\tdragAreaRef.current;\n\t\t// Falls back to initial bounds if the ref has no size. Since styles\n\t\t// give the drag area dimensions even when the media has not loaded\n\t\t// this should only happen in unit tests (jsdom).\n\t\tsetBounds(\n\t\t\twidth > 0 && height > 0 ? { width, height } : { ...INITIAL_BOUNDS }\n\t\t);\n\t} );\n\n\tuseEffect( () => {\n\t\tconst updateBounds = refUpdateBounds.current;\n\t\tif ( ! dragAreaRef.current ) return;\n\n\t\tconst { defaultView } = dragAreaRef.current.ownerDocument;\n\t\tdefaultView?.addEventListener( 'resize', updateBounds );\n\t\treturn () => defaultView?.removeEventListener( 'resize', updateBounds );\n\t}, [] );\n\n\t// Updates the bounds to cover cases of unspecified media or load failures.\n\tuseIsomorphicLayoutEffect( () => void refUpdateBounds.current(), [] );\n\n\t// TODO: Consider refactoring getValueWithinDragArea() into a pure function.\n\t// https://github.com/WordPress/gutenberg/pull/43872#discussion_r963455173\n\tconst getValueWithinDragArea = ( {\n\t\tclientX,\n\t\tclientY,\n\t\tshiftKey,\n\t}: {\n\t\tclientX: number;\n\t\tclientY: number;\n\t\tshiftKey: boolean;\n\t} ) => {\n\t\tif ( ! dragAreaRef.current ) return;\n\n\t\tconst { top, left } = dragAreaRef.current.getBoundingClientRect();\n\t\tlet nextX = ( clientX - left ) / bounds.width;\n\t\tlet nextY = ( clientY - top ) / bounds.height;\n\t\t// Enables holding shift to jump values by 10%.\n\t\tif ( shiftKey ) {\n\t\t\tnextX = Math.round( nextX / 0.1 ) * 0.1;\n\t\t\tnextY = Math.round( nextY / 0.1 ) * 0.1;\n\t\t}\n\t\treturn getFinalValue( { x: nextX, y: nextY } );\n\t};\n\n\tconst getFinalValue = ( value: FocalPointType ): FocalPointType => {\n\t\tconst resolvedValue = resolvePoint?.( value ) ?? value;\n\t\tresolvedValue.x = Math.max( 0, Math.min( resolvedValue.x, 1 ) );\n\t\tresolvedValue.y = Math.max( 0, Math.min( resolvedValue.y, 1 ) );\n\t\tconst roundToTwoDecimalPlaces = ( n: number ) =>\n\t\t\tMath.round( n * 1e2 ) / 1e2;\n\n\t\treturn {\n\t\t\tx: roundToTwoDecimalPlaces( resolvedValue.x ),\n\t\t\ty: roundToTwoDecimalPlaces( resolvedValue.y ),\n\t\t};\n\t};\n\n\tconst arrowKeyStep: KeyboardEventHandler< HTMLDivElement > = ( event ) => {\n\t\tconst { code, shiftKey } = event;\n\t\tif (\n\t\t\t! [ 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight' ].includes(\n\t\t\t\tcode\n\t\t\t)\n\t\t)\n\t\t\treturn;\n\n\t\tevent.preventDefault();\n\t\tconst value = { x, y };\n\t\tconst step = shiftKey ? 0.1 : 0.01;\n\t\tconst delta =\n\t\t\tcode === 'ArrowUp' || code === 'ArrowLeft' ? -1 * step : step;\n\t\tconst axis = code === 'ArrowUp' || code === 'ArrowDown' ? 'y' : 'x';\n\t\tvalue[ axis ] = value[ axis ] + delta;\n\t\tonChange?.( getFinalValue( value ) );\n\t};\n\n\tconst focalPointPosition = {\n\t\tleft: x * bounds.width,\n\t\ttop: y * bounds.height,\n\t};\n\n\tconst classes = classnames(\n\t\t'components-focal-point-picker-control',\n\t\tclassName\n\t);\n\n\tconst instanceId = useInstanceId( FocalPointPicker );\n\tconst id = `inspector-focal-point-picker-control-${ instanceId }`;\n\n\tuseUpdateEffect( () => {\n\t\tsetShowGridOverlay( true );\n\t\tconst timeout = window.setTimeout( () => {\n\t\t\tsetShowGridOverlay( false );\n\t\t}, GRID_OVERLAY_TIMEOUT );\n\n\t\treturn () => window.clearTimeout( timeout );\n\t}, [ x, y ] );\n\n\treturn (\n\t\t<BaseControl\n\t\t\t{ ...restProps }\n\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\tlabel={ label }\n\t\t\tid={ id }\n\t\t\thelp={ help }\n\t\t\tclassName={ classes }\n\t\t>\n\t\t\t<MediaWrapper className=\"components-focal-point-picker-wrapper\">\n\t\t\t\t<MediaContainer\n\t\t\t\t\tclassName=\"components-focal-point-picker\"\n\t\t\t\t\tonKeyDown={ arrowKeyStep }\n\t\t\t\t\tonMouseDown={ startDrag }\n\t\t\t\t\tonBlur={ () => {\n\t\t\t\t\t\tif ( isDragging ) endDrag();\n\t\t\t\t\t} }\n\t\t\t\t\tref={ dragAreaRef }\n\t\t\t\t\trole=\"button\"\n\t\t\t\t\ttabIndex={ -1 }\n\t\t\t\t>\n\t\t\t\t\t<Grid bounds={ bounds } showOverlay={ showGridOverlay } />\n\t\t\t\t\t<Media\n\t\t\t\t\t\talt={ __( 'Media preview' ) }\n\t\t\t\t\t\tautoPlay={ autoPlay }\n\t\t\t\t\t\tonLoad={ refUpdateBounds.current }\n\t\t\t\t\t\tsrc={ url }\n\t\t\t\t\t/>\n\t\t\t\t\t<FocalPoint\n\t\t\t\t\t\t{ ...focalPointPosition }\n\t\t\t\t\t\tisDragging={ isDragging }\n\t\t\t\t\t/>\n\t\t\t\t</MediaContainer>\n\t\t\t</MediaWrapper>\n\t\t\t<Controls\n\t\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\thasHelpText={ !! help }\n\t\t\t\tpoint={ { x, y } }\n\t\t\t\tonChange={ ( value ) => {\n\t\t\t\t\tonChange?.( getFinalValue( value ) );\n\t\t\t\t} }\n\t\t\t/>\n\t\t</BaseControl>\n\t);\n}\n\nexport default FocalPointPicker;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AASA,IAAAI,YAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,WAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,KAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,MAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,sBAAA,GAAAT,OAAA;AAIA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AA7BA;AACA;AACA;;AAGA;AACA;AACA;;AASA;AACA;AACA;;AAmBA,MAAMY,oBAAoB,GAAG,GAAG;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,gBAAgBA,CAAE;EACjCC,uBAAuB;EACvBC,qBAAqB,GAAG,KAAK;EAC7BC,QAAQ,GAAG,IAAI;EACfC,SAAS;EACTC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,MAAM;EACNC,SAAS;EACTC,WAAW;EACXC,YAAY;EACZC,GAAG;EACHC,KAAK,EAAEC,SAAS,GAAG;IAClBC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE;EACJ,CAAC;EACD,GAAGC;AAC4D,CAAC,EAAG;EACnE,MAAM,CAAEC,KAAK,EAAEC,QAAQ,CAAE,GAAG,IAAAC,iBAAQ,EAAEN,SAAU,CAAC;EACjD,MAAM,CAAEO,eAAe,EAAEC,kBAAkB,CAAE,GAAG,IAAAF,iBAAQ,EAAE,KAAM,CAAC;EAEjE,MAAM;IAAEG,SAAS;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAG,IAAAC,kCAAW,EAAE;IACvDhB,WAAW,EAAIiB,KAAK,IAAM;MACzBC,WAAW,CAACC,OAAO,EAAEC,KAAK,CAAC,CAAC;MAC5B,MAAMjB,KAAK,GAAGkB,sBAAsB,CAAEJ,KAAM,CAAC;;MAE7C;MACA;MACA,IAAK,CAAEd,KAAK,EAAG;MAEfH,WAAW,GAAIG,KAAK,EAAEc,KAAM,CAAC;MAC7BR,QAAQ,CAAEN,KAAM,CAAC;IAClB,CAAC;IACDmB,UAAU,EAAIL,KAAK,IAAM;MACxB;MACAA,KAAK,CAACM,cAAc,CAAC,CAAC;MACtB,MAAMpB,KAAK,GAAGkB,sBAAsB,CAAEJ,KAAM,CAAC;MAC7C,IAAK,CAAEd,KAAK,EAAG;MACfL,MAAM,GAAIK,KAAK,EAAEc,KAAM,CAAC;MACxBR,QAAQ,CAAEN,KAAM,CAAC;IAClB,CAAC;IACDJ,SAAS,EAAEA,CAAA,KAAM;MAChBA,SAAS,GAAG,CAAC;MACbF,QAAQ,GAAIW,KAAM,CAAC;IACpB;EACD,CAAE,CAAC;;EAEH;EACA,MAAM;IAAEH,CAAC;IAAEC;EAAE,CAAC,GAAGS,UAAU,GAAGP,KAAK,GAAGJ,SAAS;EAE/C,MAAMc,WAAW,GAAG,IAAAM,eAAM,EAAoB,IAAK,CAAC;EACpD,MAAM,CAAEC,MAAM,EAAEC,SAAS,CAAE,GAAG,IAAAhB,iBAAQ,EAAEiB,qBAAe,CAAC;EACxD,MAAMC,eAAe,GAAG,IAAAJ,eAAM,EAAE,MAAM;IACrC,IAAK,CAAEN,WAAW,CAACC,OAAO,EAAG;IAE7B,MAAM;MAAEU,WAAW,EAAEC,KAAK;MAAEC,YAAY,EAAEC;IAAO,CAAC,GACjDd,WAAW,CAACC,OAAO;IACpB;IACA;IACA;IACAO,SAAS,CACRI,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,GAAG;MAAEF,KAAK;MAAEE;IAAO,CAAC,GAAG;MAAE,GAAGL;IAAe,CACnE,CAAC;EACF,CAAE,CAAC;EAEH,IAAAM,kBAAS,EAAE,MAAM;IAChB,MAAMC,YAAY,GAAGN,eAAe,CAACT,OAAO;IAC5C,IAAK,CAAED,WAAW,CAACC,OAAO,EAAG;IAE7B,MAAM;MAAEgB;IAAY,CAAC,GAAGjB,WAAW,CAACC,OAAO,CAACiB,aAAa;IACzDD,WAAW,EAAEE,gBAAgB,CAAE,QAAQ,EAAEH,YAAa,CAAC;IACvD,OAAO,MAAMC,WAAW,EAAEG,mBAAmB,CAAE,QAAQ,EAAEJ,YAAa,CAAC;EACxE,CAAC,EAAE,EAAG,CAAC;;EAEP;EACA,IAAAK,kCAAyB,EAAE,MAAM,KAAKX,eAAe,CAACT,OAAO,CAAC,CAAC,EAAE,EAAG,CAAC;;EAErE;EACA;EACA,MAAME,sBAAsB,GAAGA,CAAE;IAChCmB,OAAO;IACPC,OAAO;IACPC;EAKD,CAAC,KAAM;IACN,IAAK,CAAExB,WAAW,CAACC,OAAO,EAAG;IAE7B,MAAM;MAAEwB,GAAG;MAAEC;IAAK,CAAC,GAAG1B,WAAW,CAACC,OAAO,CAAC0B,qBAAqB,CAAC,CAAC;IACjE,IAAIC,KAAK,GAAG,CAAEN,OAAO,GAAGI,IAAI,IAAKnB,MAAM,CAACK,KAAK;IAC7C,IAAIiB,KAAK,GAAG,CAAEN,OAAO,GAAGE,GAAG,IAAKlB,MAAM,CAACO,MAAM;IAC7C;IACA,IAAKU,QAAQ,EAAG;MACfI,KAAK,GAAGE,IAAI,CAACC,KAAK,CAAEH,KAAK,GAAG,GAAI,CAAC,GAAG,GAAG;MACvCC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAEF,KAAK,GAAG,GAAI,CAAC,GAAG,GAAG;IACxC;IACA,OAAOG,aAAa,CAAE;MAAE7C,CAAC,EAAEyC,KAAK;MAAExC,CAAC,EAAEyC;IAAM,CAAE,CAAC;EAC/C,CAAC;EAED,MAAMG,aAAa,GAAK/C,KAAqB,IAAsB;IAAA,IAAAgD,aAAA;IAClE,MAAMC,aAAa,IAAAD,aAAA,GAAGlD,YAAY,GAAIE,KAAM,CAAC,cAAAgD,aAAA,cAAAA,aAAA,GAAIhD,KAAK;IACtDiD,aAAa,CAAC/C,CAAC,GAAG2C,IAAI,CAACK,GAAG,CAAE,CAAC,EAAEL,IAAI,CAACM,GAAG,CAAEF,aAAa,CAAC/C,CAAC,EAAE,CAAE,CAAE,CAAC;IAC/D+C,aAAa,CAAC9C,CAAC,GAAG0C,IAAI,CAACK,GAAG,CAAE,CAAC,EAAEL,IAAI,CAACM,GAAG,CAAEF,aAAa,CAAC9C,CAAC,EAAE,CAAE,CAAE,CAAC;IAC/D,MAAMiD,uBAAuB,GAAKC,CAAS,IAC1CR,IAAI,CAACC,KAAK,CAAEO,CAAC,GAAG,GAAI,CAAC,GAAG,GAAG;IAE5B,OAAO;MACNnD,CAAC,EAAEkD,uBAAuB,CAAEH,aAAa,CAAC/C,CAAE,CAAC;MAC7CC,CAAC,EAAEiD,uBAAuB,CAAEH,aAAa,CAAC9C,CAAE;IAC7C,CAAC;EACF,CAAC;EAED,MAAMmD,YAAoD,GAAKxC,KAAK,IAAM;IACzE,MAAM;MAAEyC,IAAI;MAAEhB;IAAS,CAAC,GAAGzB,KAAK;IAChC,IACC,CAAE,CAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAE,CAAC0C,QAAQ,CAC/DD,IACD,CAAC,EAED;IAEDzC,KAAK,CAACM,cAAc,CAAC,CAAC;IACtB,MAAMpB,KAAK,GAAG;MAAEE,CAAC;MAAEC;IAAE,CAAC;IACtB,MAAMsD,IAAI,GAAGlB,QAAQ,GAAG,GAAG,GAAG,IAAI;IAClC,MAAMmB,KAAK,GACVH,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,WAAW,GAAG,CAAC,CAAC,GAAGE,IAAI,GAAGA,IAAI;IAC9D,MAAME,IAAI,GAAGJ,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,WAAW,GAAG,GAAG,GAAG,GAAG;IACnEvD,KAAK,CAAE2D,IAAI,CAAE,GAAG3D,KAAK,CAAE2D,IAAI,CAAE,GAAGD,KAAK;IACrChE,QAAQ,GAAIqD,aAAa,CAAE/C,KAAM,CAAE,CAAC;EACrC,CAAC;EAED,MAAM4D,kBAAkB,GAAG;IAC1BnB,IAAI,EAAEvC,CAAC,GAAGoB,MAAM,CAACK,KAAK;IACtBa,GAAG,EAAErC,CAAC,GAAGmB,MAAM,CAACO;EACjB,CAAC;EAED,MAAMgC,OAAO,GAAG,IAAAC,mBAAU,EACzB,uCAAuC,EACvCvE,SACD,CAAC;EAED,MAAMwE,UAAU,GAAG,IAAAC,sBAAa,EAAE7E,gBAAiB,CAAC;EACpD,MAAM8E,EAAE,GAAI,wCAAwCF,UAAY,EAAC;EAEjE,IAAAG,sBAAe,EAAE,MAAM;IACtBzD,kBAAkB,CAAE,IAAK,CAAC;IAC1B,MAAM0D,OAAO,GAAGC,MAAM,CAACC,UAAU,CAAE,MAAM;MACxC5D,kBAAkB,CAAE,KAAM,CAAC;IAC5B,CAAC,EAAEvB,oBAAqB,CAAC;IAEzB,OAAO,MAAMkF,MAAM,CAACE,YAAY,CAAEH,OAAQ,CAAC;EAC5C,CAAC,EAAE,CAAEjE,CAAC,EAAEC,CAAC,CAAG,CAAC;EAEb,OACC,IAAAoE,MAAA,CAAAC,aAAA,EAAC9F,YAAA,CAAA+F,OAAW;IAAA,GACNrE,SAAS;IACdhB,uBAAuB,EAAGA,uBAAyB;IACnDK,KAAK,EAAGA,KAAO;IACfwE,EAAE,EAAGA,EAAI;IACTzE,IAAI,EAAGA,IAAM;IACbD,SAAS,EAAGsE;EAAS,GAErB,IAAAU,MAAA,CAAAC,aAAA,EAACzF,sBAAA,CAAA2F,YAAY;IAACnF,SAAS,EAAC;EAAuC,GAC9D,IAAAgF,MAAA,CAAAC,aAAA,EAACzF,sBAAA,CAAA4F,cAAc;IACdpF,SAAS,EAAC,+BAA+B;IACzCqF,SAAS,EAAGtB,YAAc;IAC1BuB,WAAW,EAAGnE,SAAW;IACzBoE,MAAM,EAAGA,CAAA,KAAM;MACd,IAAKlE,UAAU,EAAGD,OAAO,CAAC,CAAC;IAC5B,CAAG;IACHoE,GAAG,EAAGhE,WAAa;IACnBiE,IAAI,EAAC,QAAQ;IACbC,QAAQ,EAAG,CAAC;EAAG,GAEf,IAAAV,MAAA,CAAAC,aAAA,EAAC3F,KAAA,CAAA4F,OAAI;IAACnD,MAAM,EAAGA,MAAQ;IAAC4D,WAAW,EAAG1E;EAAiB,CAAE,CAAC,EAC1D,IAAA+D,MAAA,CAAAC,aAAA,EAAC1F,MAAA,CAAA2F,OAAK;IACLU,GAAG,EAAG,IAAAC,QAAE,EAAE,eAAgB,CAAG;IAC7B9F,QAAQ,EAAGA,QAAU;IACrB+F,MAAM,EAAG5D,eAAe,CAACT,OAAS;IAClCsE,GAAG,EAAGvF;EAAK,CACX,CAAC,EACF,IAAAwE,MAAA,CAAAC,aAAA,EAAC5F,WAAA,CAAA6F,OAAU;IAAA,GACLb,kBAAkB;IACvBhD,UAAU,EAAGA;EAAY,CACzB,CACc,CACH,CAAC,EACf,IAAA2D,MAAA,CAAAC,aAAA,EAAC7F,SAAA,CAAA8F,OAAQ;IACRrF,uBAAuB,EAAGA,uBAAyB;IACnDC,qBAAqB,EAAGA,qBAAuB;IAC/CkG,WAAW,EAAG,CAAC,CAAE/F,IAAM;IACvBa,KAAK,EAAG;MAAEH,CAAC;MAAEC;IAAE,CAAG;IAClBT,QAAQ,EAAKM,KAAK,IAAM;MACvBN,QAAQ,GAAIqD,aAAa,CAAE/C,KAAM,CAAE,CAAC;IACrC;EAAG,CACH,CACW,CAAC;AAEhB;AAAC,IAAAwF,QAAA,GAEcrG,gBAAgB;AAAAsG,OAAA,CAAAhB,OAAA,GAAAe,QAAA"}