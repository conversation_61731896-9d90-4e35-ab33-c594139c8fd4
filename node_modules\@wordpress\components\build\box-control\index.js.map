{"version": 3, "names": ["_compose", "require", "_element", "_i18n", "_baseControl", "_allInputControl", "_interopRequireDefault", "_inputControls", "_axialInputControls", "_linked<PERSON><PERSON>on", "_grid", "_boxControlStyles", "_utils", "_utils2", "_hooks", "defaultInputProps", "min", "noop", "useUniqueId", "idProp", "instanceId", "useInstanceId", "BoxControl", "__next40pxDefaultSize", "id", "inputProps", "onChange", "label", "__", "values", "valuesProp", "units", "sides", "splitOnAxis", "allowReset", "resetValues", "DEFAULT_VALUES", "onMouseOver", "onMouseOut", "set<PERSON><PERSON><PERSON>", "useControlledState", "fallback", "inputValues", "hasInitialValue", "isValuesDefined", "hasOneSide", "length", "isDirty", "setIsDirty", "useState", "isLinked", "setIsLinked", "isValuesMixed", "side", "setSide", "getInitialSide", "selected<PERSON><PERSON><PERSON>", "setSelectedUnits", "top", "parseQuantityAndUnitFromRawValue", "right", "bottom", "left", "headingId", "toggleLinked", "handleOnFocus", "_event", "nextSide", "handleOnChange", "nextV<PERSON>ues", "handleOnReset", "inputControlProps", "onFocus", "_react", "createElement", "Grid", "columns", "templateColumns", "role", "BaseControl", "VisualLabel", "InputWrapper", "FlexedBoxControlIcon", "default", "LinkedButtonWrapper", "onClick", "ResetButton", "className", "variant", "size", "disabled", "_default", "exports"], "sources": ["@wordpress/components/src/box-control/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { useState } from '@wordpress/element';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { BaseControl } from '../base-control';\nimport AllInputControl from './all-input-control';\nimport InputControls from './input-controls';\nimport AxialInputControls from './axial-input-controls';\nimport LinkedButton from './linked-button';\nimport { Grid } from '../grid';\nimport {\n\tFlexedBoxControlIcon,\n\tInputWrapper,\n\tResetButton,\n\tLinkedButtonWrapper,\n} from './styles/box-control-styles';\nimport { parseQuantityAndUnitFromRawValue } from '../unit-control/utils';\nimport {\n\tDEFAULT_VALUES,\n\tgetInitialSide,\n\tisValuesMixed,\n\tisValuesDefined,\n} from './utils';\nimport { useControlledState } from '../utils/hooks';\nimport type {\n\tBoxControlIconProps,\n\tBoxControlProps,\n\tBoxControlValue,\n} from './types';\n\nconst defaultInputProps = {\n\tmin: 0,\n};\n\nconst noop = () => {};\n\nfunction useUniqueId( idProp?: string ) {\n\tconst instanceId = useInstanceId( BoxControl, 'inspector-box-control' );\n\n\treturn idProp || instanceId;\n}\n\n/**\n * BoxControl components let users set values for Top, Right, Bottom, and Left.\n * This can be used as an input control for values like `padding` or `margin`.\n *\n * ```jsx\n * import { __experimentalBoxControl as BoxControl } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const Example = () => {\n * \tconst [ values, setValues ] = useState( {\n * \t\ttop: '50px',\n * \t\tleft: '10%',\n * \t\tright: '10%',\n * \t\tbottom: '50px',\n * \t} );\n *\n * \treturn (\n * \t\t<BoxControl\n * \t\t\tvalues={ values }\n * \t\t\tonChange={ ( nextValues ) => setValues( nextValues ) }\n * \t\t/>\n * \t);\n * };\n * ```\n */\nfunction BoxControl( {\n\t__next40pxDefaultSize = false,\n\tid: idProp,\n\tinputProps = defaultInputProps,\n\tonChange = noop,\n\tlabel = __( 'Box Control' ),\n\tvalues: valuesProp,\n\tunits,\n\tsides,\n\tsplitOnAxis = false,\n\tallowReset = true,\n\tresetValues = DEFAULT_VALUES,\n\tonMouseOver,\n\tonMouseOut,\n}: BoxControlProps ) {\n\tconst [ values, setValues ] = useControlledState( valuesProp, {\n\t\tfallback: DEFAULT_VALUES,\n\t} );\n\tconst inputValues = values || DEFAULT_VALUES;\n\tconst hasInitialValue = isValuesDefined( valuesProp );\n\tconst hasOneSide = sides?.length === 1;\n\n\tconst [ isDirty, setIsDirty ] = useState( hasInitialValue );\n\tconst [ isLinked, setIsLinked ] = useState(\n\t\t! hasInitialValue || ! isValuesMixed( inputValues ) || hasOneSide\n\t);\n\n\tconst [ side, setSide ] = useState< BoxControlIconProps[ 'side' ] >(\n\t\tgetInitialSide( isLinked, splitOnAxis )\n\t);\n\n\t// Tracking selected units via internal state allows filtering of CSS unit\n\t// only values from being saved while maintaining preexisting unit selection\n\t// behaviour. Filtering CSS only values prevents invalid style values.\n\tconst [ selectedUnits, setSelectedUnits ] = useState< BoxControlValue >( {\n\t\ttop: parseQuantityAndUnitFromRawValue( valuesProp?.top )[ 1 ],\n\t\tright: parseQuantityAndUnitFromRawValue( valuesProp?.right )[ 1 ],\n\t\tbottom: parseQuantityAndUnitFromRawValue( valuesProp?.bottom )[ 1 ],\n\t\tleft: parseQuantityAndUnitFromRawValue( valuesProp?.left )[ 1 ],\n\t} );\n\n\tconst id = useUniqueId( idProp );\n\tconst headingId = `${ id }-heading`;\n\n\tconst toggleLinked = () => {\n\t\tsetIsLinked( ! isLinked );\n\t\tsetSide( getInitialSide( ! isLinked, splitOnAxis ) );\n\t};\n\n\tconst handleOnFocus = (\n\t\t_event: React.FocusEvent< HTMLInputElement >,\n\t\t{ side: nextSide }: { side: typeof side }\n\t) => {\n\t\tsetSide( nextSide );\n\t};\n\n\tconst handleOnChange = ( nextValues: BoxControlValue ) => {\n\t\tonChange( nextValues );\n\t\tsetValues( nextValues );\n\t\tsetIsDirty( true );\n\t};\n\n\tconst handleOnReset = () => {\n\t\tonChange( resetValues );\n\t\tsetValues( resetValues );\n\t\tsetSelectedUnits( resetValues );\n\t\tsetIsDirty( false );\n\t};\n\n\tconst inputControlProps = {\n\t\t...inputProps,\n\t\tonChange: handleOnChange,\n\t\tonFocus: handleOnFocus,\n\t\tisLinked,\n\t\tunits,\n\t\tselectedUnits,\n\t\tsetSelectedUnits,\n\t\tsides,\n\t\tvalues: inputValues,\n\t\tonMouseOver,\n\t\tonMouseOut,\n\t\t__next40pxDefaultSize,\n\t};\n\n\treturn (\n\t\t<Grid\n\t\t\tid={ id }\n\t\t\tcolumns={ 3 }\n\t\t\ttemplateColumns=\"1fr min-content min-content\"\n\t\t\trole=\"group\"\n\t\t\taria-labelledby={ headingId }\n\t\t>\n\t\t\t<BaseControl.VisualLabel id={ headingId }>\n\t\t\t\t{ label }\n\t\t\t</BaseControl.VisualLabel>\n\t\t\t{ isLinked && (\n\t\t\t\t<InputWrapper>\n\t\t\t\t\t<FlexedBoxControlIcon side={ side } sides={ sides } />\n\t\t\t\t\t<AllInputControl { ...inputControlProps } />\n\t\t\t\t</InputWrapper>\n\t\t\t) }\n\t\t\t{ ! hasOneSide && (\n\t\t\t\t<LinkedButtonWrapper>\n\t\t\t\t\t<LinkedButton\n\t\t\t\t\t\tonClick={ toggleLinked }\n\t\t\t\t\t\tisLinked={ isLinked }\n\t\t\t\t\t/>\n\t\t\t\t</LinkedButtonWrapper>\n\t\t\t) }\n\n\t\t\t{ ! isLinked && splitOnAxis && (\n\t\t\t\t<AxialInputControls { ...inputControlProps } />\n\t\t\t) }\n\t\t\t{ ! isLinked && ! splitOnAxis && (\n\t\t\t\t<InputControls { ...inputControlProps } />\n\t\t\t) }\n\t\t\t{ allowReset && (\n\t\t\t\t<ResetButton\n\t\t\t\t\tclassName=\"component-box-control__reset-button\"\n\t\t\t\t\tvariant=\"secondary\"\n\t\t\t\t\tsize=\"small\"\n\t\t\t\t\tonClick={ handleOnReset }\n\t\t\t\t\tdisabled={ ! isDirty }\n\t\t\t\t>\n\t\t\t\t\t{ __( 'Reset' ) }\n\t\t\t\t</ResetButton>\n\t\t\t) }\n\t\t</Grid>\n\t);\n}\n\nexport { applyValueToSides } from './utils';\nexport default BoxControl;\n"], "mappings": ";;;;;;;;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAKA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,cAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,mBAAA,GAAAF,sBAAA,CAAAL,OAAA;AACA,IAAAQ,aAAA,GAAAH,sBAAA,CAAAL,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,iBAAA,GAAAV,OAAA;AAMA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AAMA,IAAAa,MAAA,GAAAb,OAAA;AA7BA;AACA;AACA;;AAKA;AACA;AACA;;AA2BA,MAAMc,iBAAiB,GAAG;EACzBC,GAAG,EAAE;AACN,CAAC;AAED,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAErB,SAASC,WAAWA,CAAEC,MAAe,EAAG;EACvC,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEC,UAAU,EAAE,uBAAwB,CAAC;EAEvE,OAAOH,MAAM,IAAIC,UAAU;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAAE;EACpBC,qBAAqB,GAAG,KAAK;EAC7BC,EAAE,EAAEL,MAAM;EACVM,UAAU,GAAGV,iBAAiB;EAC9BW,QAAQ,GAAGT,IAAI;EACfU,KAAK,GAAG,IAAAC,QAAE,EAAE,aAAc,CAAC;EAC3BC,MAAM,EAAEC,UAAU;EAClBC,KAAK;EACLC,KAAK;EACLC,WAAW,GAAG,KAAK;EACnBC,UAAU,GAAG,IAAI;EACjBC,WAAW,GAAGC,sBAAc;EAC5BC,WAAW;EACXC;AACgB,CAAC,EAAG;EACpB,MAAM,CAAET,MAAM,EAAEU,SAAS,CAAE,GAAG,IAAAC,yBAAkB,EAAEV,UAAU,EAAE;IAC7DW,QAAQ,EAAEL;EACX,CAAE,CAAC;EACH,MAAMM,WAAW,GAAGb,MAAM,IAAIO,sBAAc;EAC5C,MAAMO,eAAe,GAAG,IAAAC,uBAAe,EAAEd,UAAW,CAAC;EACrD,MAAMe,UAAU,GAAGb,KAAK,EAAEc,MAAM,KAAK,CAAC;EAEtC,MAAM,CAAEC,OAAO,EAAEC,UAAU,CAAE,GAAG,IAAAC,iBAAQ,EAAEN,eAAgB,CAAC;EAC3D,MAAM,CAAEO,QAAQ,EAAEC,WAAW,CAAE,GAAG,IAAAF,iBAAQ,EACzC,CAAEN,eAAe,IAAI,CAAE,IAAAS,qBAAa,EAAEV,WAAY,CAAC,IAAIG,UACxD,CAAC;EAED,MAAM,CAAEQ,IAAI,EAAEC,OAAO,CAAE,GAAG,IAAAL,iBAAQ,EACjC,IAAAM,sBAAc,EAAEL,QAAQ,EAAEjB,WAAY,CACvC,CAAC;;EAED;EACA;EACA;EACA,MAAM,CAAEuB,aAAa,EAAEC,gBAAgB,CAAE,GAAG,IAAAR,iBAAQ,EAAqB;IACxES,GAAG,EAAE,IAAAC,uCAAgC,EAAE7B,UAAU,EAAE4B,GAAI,CAAC,CAAE,CAAC,CAAE;IAC7DE,KAAK,EAAE,IAAAD,uCAAgC,EAAE7B,UAAU,EAAE8B,KAAM,CAAC,CAAE,CAAC,CAAE;IACjEC,MAAM,EAAE,IAAAF,uCAAgC,EAAE7B,UAAU,EAAE+B,MAAO,CAAC,CAAE,CAAC,CAAE;IACnEC,IAAI,EAAE,IAAAH,uCAAgC,EAAE7B,UAAU,EAAEgC,IAAK,CAAC,CAAE,CAAC;EAC9D,CAAE,CAAC;EAEH,MAAMtC,EAAE,GAAGN,WAAW,CAAEC,MAAO,CAAC;EAChC,MAAM4C,SAAS,GAAI,GAAGvC,EAAI,UAAS;EAEnC,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IAC1Bb,WAAW,CAAE,CAAED,QAAS,CAAC;IACzBI,OAAO,CAAE,IAAAC,sBAAc,EAAE,CAAEL,QAAQ,EAAEjB,WAAY,CAAE,CAAC;EACrD,CAAC;EAED,MAAMgC,aAAa,GAAGA,CACrBC,MAA4C,EAC5C;IAAEb,IAAI,EAAEc;EAAgC,CAAC,KACrC;IACJb,OAAO,CAAEa,QAAS,CAAC;EACpB,CAAC;EAED,MAAMC,cAAc,GAAKC,UAA2B,IAAM;IACzD3C,QAAQ,CAAE2C,UAAW,CAAC;IACtB9B,SAAS,CAAE8B,UAAW,CAAC;IACvBrB,UAAU,CAAE,IAAK,CAAC;EACnB,CAAC;EAED,MAAMsB,aAAa,GAAGA,CAAA,KAAM;IAC3B5C,QAAQ,CAAES,WAAY,CAAC;IACvBI,SAAS,CAAEJ,WAAY,CAAC;IACxBsB,gBAAgB,CAAEtB,WAAY,CAAC;IAC/Ba,UAAU,CAAE,KAAM,CAAC;EACpB,CAAC;EAED,MAAMuB,iBAAiB,GAAG;IACzB,GAAG9C,UAAU;IACbC,QAAQ,EAAE0C,cAAc;IACxBI,OAAO,EAAEP,aAAa;IACtBf,QAAQ;IACRnB,KAAK;IACLyB,aAAa;IACbC,gBAAgB;IAChBzB,KAAK;IACLH,MAAM,EAAEa,WAAW;IACnBL,WAAW;IACXC,UAAU;IACVf;EACD,CAAC;EAED,OACC,IAAAkD,MAAA,CAAAC,aAAA,EAAChE,KAAA,CAAAiE,IAAI;IACJnD,EAAE,EAAGA,EAAI;IACToD,OAAO,EAAG,CAAG;IACbC,eAAe,EAAC,6BAA6B;IAC7CC,IAAI,EAAC,OAAO;IACZ,mBAAkBf;EAAW,GAE7B,IAAAU,MAAA,CAAAC,aAAA,EAACtE,YAAA,CAAA2E,WAAW,CAACC,WAAW;IAACxD,EAAE,EAAGuC;EAAW,GACtCpC,KACsB,CAAC,EACxBuB,QAAQ,IACT,IAAAuB,MAAA,CAAAC,aAAA,EAAC/D,iBAAA,CAAAsE,YAAY,QACZ,IAAAR,MAAA,CAAAC,aAAA,EAAC/D,iBAAA,CAAAuE,oBAAoB;IAAC7B,IAAI,EAAGA,IAAM;IAACrB,KAAK,EAAGA;EAAO,CAAE,CAAC,EACtD,IAAAyC,MAAA,CAAAC,aAAA,EAACrE,gBAAA,CAAA8E,OAAe;IAAA,GAAMZ;EAAiB,CAAI,CAC9B,CACd,EACC,CAAE1B,UAAU,IACb,IAAA4B,MAAA,CAAAC,aAAA,EAAC/D,iBAAA,CAAAyE,mBAAmB,QACnB,IAAAX,MAAA,CAAAC,aAAA,EAACjE,aAAA,CAAA0E,OAAY;IACZE,OAAO,EAAGrB,YAAc;IACxBd,QAAQ,EAAGA;EAAU,CACrB,CACmB,CACrB,EAEC,CAAEA,QAAQ,IAAIjB,WAAW,IAC1B,IAAAwC,MAAA,CAAAC,aAAA,EAAClE,mBAAA,CAAA2E,OAAkB;IAAA,GAAMZ;EAAiB,CAAI,CAC9C,EACC,CAAErB,QAAQ,IAAI,CAAEjB,WAAW,IAC5B,IAAAwC,MAAA,CAAAC,aAAA,EAACnE,cAAA,CAAA4E,OAAa;IAAA,GAAMZ;EAAiB,CAAI,CACzC,EACCrC,UAAU,IACX,IAAAuC,MAAA,CAAAC,aAAA,EAAC/D,iBAAA,CAAA2E,WAAW;IACXC,SAAS,EAAC,qCAAqC;IAC/CC,OAAO,EAAC,WAAW;IACnBC,IAAI,EAAC,OAAO;IACZJ,OAAO,EAAGf,aAAe;IACzBoB,QAAQ,EAAG,CAAE3C;EAAS,GAEpB,IAAAnB,QAAE,EAAE,OAAQ,CACF,CAET,CAAC;AAET;AAAC,IAAA+D,QAAA,GAGcrE,UAAU;AAAAsE,OAAA,CAAAT,OAAA,GAAAQ,QAAA"}