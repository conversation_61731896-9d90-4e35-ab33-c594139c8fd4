"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _reactNativeLinearGradient = _interopRequireDefault(require("react-native-linear-gradient"));
var _colord = require("colord");
var _element = _interopRequireWildcard(require("@wordpress/element"));
var _styleNative = _interopRequireDefault(require("./style.native.scss"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

class SaturationValuePicker extends _element.Component {
  constructor(props) {
    super(props);
    this.panResponder = _reactNative.PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onStartShouldSetPanResponderCapture: () => true,
      onMoveShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponderCapture: () => true,
      onPanResponderGrant: (evt, gestureState) => {
        const {
          onPress
        } = this.props;
        const {
          saturation,
          value
        } = this.computeSatValPress(evt);
        this.dragStartValue = {
          saturation,
          value
        };
        if (onPress) {
          onPress({
            ...this.computeSatValPress(evt),
            nativeEvent: evt.nativeEvent
          });
        }
        this.fireDragEvent('onDragStart', gestureState);
      },
      onPanResponderMove: (evt, gestureState) => {
        this.fireDragEvent('onDragMove', gestureState);
      },
      onPanResponderTerminationRequest: () => true,
      onPanResponderRelease: (evt, gestureState) => {
        this.fireDragEvent('onDragEnd', gestureState);
      },
      onPanResponderTerminate: (evt, gestureState) => {
        this.fireDragEvent('onDragTerminate', gestureState);
      },
      onShouldBlockNativeResponder: () => true
    });
  }
  normalizeValue(value) {
    if (value < 0) return 0;
    if (value > 1) return 1;
    return value;
  }
  computeSatValDrag(gestureState) {
    const {
      dx,
      dy
    } = gestureState;
    const {
      size
    } = this.props;
    const {
      saturation,
      value
    } = this.dragStartValue;
    const diffx = dx / size.width;
    const diffy = dy / size.height;
    return {
      saturation: this.normalizeValue(saturation + diffx),
      value: this.normalizeValue(value - diffy)
    };
  }
  computeSatValPress(event) {
    const {
      nativeEvent
    } = event;
    const {
      locationX,
      locationY
    } = nativeEvent;
    const {
      size
    } = this.props;
    return {
      saturation: this.normalizeValue(locationX / size.width),
      value: 1 - this.normalizeValue(locationY / size.height)
    };
  }
  fireDragEvent(eventName, gestureState) {
    const {
      [eventName]: event
    } = this.props;
    if (event) {
      event({
        ...this.computeSatValDrag(gestureState),
        gestureState
      });
    }
  }
  render() {
    const {
      size,
      sliderSize = 24,
      hue = 0,
      value = 1,
      saturation = 1,
      containerStyle = {},
      borderRadius = 0,
      currentColor
    } = this.props;
    return (0, _react.createElement)(_reactNative.View, {
      style: [_styleNative.default['hsv-container'], containerStyle, {
        height: size.height + sliderSize,
        width: size.width + sliderSize
      }],
      ...this.panResponder.panHandlers
    }, (0, _react.createElement)(_reactNativeLinearGradient.default, {
      style: [_styleNative.default['gradient-container'], {
        borderRadius
      }],
      colors: ['#fff', (0, _colord.colord)({
        h: hue,
        s: 100,
        l: 50
      }).toHex()],
      start: {
        x: 0,
        y: 0.5
      },
      end: {
        x: 1,
        y: 0.5
      }
    }, (0, _react.createElement)(_reactNativeLinearGradient.default, {
      colors: ['rgba(0, 0, 0, 0)', '#000']
    }, (0, _react.createElement)(_reactNative.View, {
      style: {
        height: size.height,
        width: size.width
      }
    }))), (0, _react.createElement)(_reactNative.View, {
      pointerEvents: "none",
      style: [_styleNative.default['saturation-slider'], {
        width: sliderSize,
        height: sliderSize,
        borderRadius: sliderSize / 2,
        borderWidth: sliderSize / 10,
        backgroundColor: currentColor,
        transform: [{
          translateX: size.width * saturation
        }, {
          translateY: size.height * (1 - value)
        }]
      }]
    }));
  }
}
exports.default = SaturationValuePicker;
//# sourceMappingURL=saturation-picker.native.js.map