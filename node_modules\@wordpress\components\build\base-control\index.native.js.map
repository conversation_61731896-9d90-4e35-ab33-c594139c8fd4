{"version": 3, "names": ["_reactNative", "require", "BaseControl", "label", "help", "children", "_react", "createElement", "View", "accessible", "accessibilityLabel", "Text"], "sources": ["@wordpress/components/src/base-control/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Text, View } from 'react-native';\n\nexport default function BaseControl( { label, help, children } ) {\n\treturn (\n\t\t<View accessible={ true } accessibilityLabel={ label }>\n\t\t\t{ label && <Text>{ label }</Text> }\n\t\t\t{ children }\n\t\t\t{ help && <Text>{ help }</Text> }\n\t\t</View>\n\t);\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGe,SAASC,WAAWA,CAAE;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAS,CAAC,EAAG;EAChE,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACP,YAAA,CAAAQ,IAAI;IAACC,UAAU,EAAG,IAAM;IAACC,kBAAkB,EAAGP;EAAO,GACnDA,KAAK,IAAI,IAAAG,MAAA,CAAAC,aAAA,EAACP,YAAA,CAAAW,IAAI,QAAGR,KAAa,CAAC,EAC/BE,QAAQ,EACRD,IAAI,IAAI,IAAAE,MAAA,CAAAC,aAAA,EAACP,YAAA,CAAAW,IAAI,QAAGP,IAAY,CACzB,CAAC;AAET"}