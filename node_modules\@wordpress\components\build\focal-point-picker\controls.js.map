{"version": 3, "names": ["_i18n", "require", "_focalPointPickerStyle", "_utils", "TEXTCONTROL_MIN", "TEXTCONTROL_MAX", "noop", "FocalPointPickerControls", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "hasHelpText", "onChange", "point", "x", "y", "valueX", "fractionToPercentage", "valueY", "handleChange", "value", "axis", "undefined", "num", "parseInt", "isNaN", "_react", "createElement", "ControlWrapper", "className", "gap", "FocalPointUnitControl", "label", "__", "join", "next", "dragDirection", "props", "StyledUnitControl", "labelPosition", "max", "min", "units"], "sources": ["@wordpress/components/src/focal-point-picker/controls.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport {\n\tStyledUnitControl,\n\tControlWrapper,\n} from './styles/focal-point-picker-style';\nimport { fractionToPercentage } from './utils';\nimport type {\n\tUnitControlProps,\n\tUnitControlOnChangeCallback,\n} from '../unit-control/types';\nimport type { FocalPointAxis, FocalPointPickerControlsProps } from './types';\n\nconst TEXTCONTROL_MIN = 0;\nconst TEXTCONTROL_MAX = 100;\nconst noop = () => {};\n\nexport default function FocalPointPickerControls( {\n\t__nextHasNoMarginBottom,\n\t__next40pxDefaultSize,\n\thasHelpText,\n\tonChange = noop,\n\tpoint = {\n\t\tx: 0.5,\n\t\ty: 0.5,\n\t},\n}: FocalPointPickerControlsProps ) {\n\tconst valueX = fractionToPercentage( point.x );\n\tconst valueY = fractionToPercentage( point.y );\n\n\tconst handleChange = (\n\t\tvalue: Parameters< UnitControlOnChangeCallback >[ 0 ],\n\t\taxis: FocalPointAxis\n\t) => {\n\t\tif ( value === undefined ) return;\n\n\t\tconst num = parseInt( value, 10 );\n\n\t\tif ( ! isNaN( num ) ) {\n\t\t\tonChange( { ...point, [ axis ]: num / 100 } );\n\t\t}\n\t};\n\n\treturn (\n\t\t<ControlWrapper\n\t\t\tclassName=\"focal-point-picker__controls\"\n\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\thasHelpText={ hasHelpText }\n\t\t\tgap={ 4 }\n\t\t>\n\t\t\t<FocalPointUnitControl\n\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\tlabel={ __( 'Left' ) }\n\t\t\t\taria-label={ __( 'Focal point left position' ) }\n\t\t\t\tvalue={ [ valueX, '%' ].join( '' ) }\n\t\t\t\tonChange={\n\t\t\t\t\t( ( next ) =>\n\t\t\t\t\t\thandleChange(\n\t\t\t\t\t\t\tnext,\n\t\t\t\t\t\t\t'x'\n\t\t\t\t\t\t) ) as UnitControlOnChangeCallback\n\t\t\t\t}\n\t\t\t\tdragDirection=\"e\"\n\t\t\t/>\n\t\t\t<FocalPointUnitControl\n\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\tlabel={ __( 'Top' ) }\n\t\t\t\taria-label={ __( 'Focal point top position' ) }\n\t\t\t\tvalue={ [ valueY, '%' ].join( '' ) }\n\t\t\t\tonChange={\n\t\t\t\t\t( ( next ) =>\n\t\t\t\t\t\thandleChange(\n\t\t\t\t\t\t\tnext,\n\t\t\t\t\t\t\t'y'\n\t\t\t\t\t\t) ) as UnitControlOnChangeCallback\n\t\t\t\t}\n\t\t\t\tdragDirection=\"s\"\n\t\t\t/>\n\t\t</ControlWrapper>\n\t);\n}\n\nfunction FocalPointUnitControl( props: UnitControlProps ) {\n\treturn (\n\t\t<StyledUnitControl\n\t\t\tclassName=\"focal-point-picker__controls-position-unit-control\"\n\t\t\tlabelPosition=\"top\"\n\t\t\tmax={ TEXTCONTROL_MAX }\n\t\t\tmin={ TEXTCONTROL_MIN }\n\t\t\tunits={ [ { value: '%', label: '%' } ] }\n\t\t\t{ ...props }\n\t\t/>\n\t);\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,sBAAA,GAAAD,OAAA;AAIA,IAAAE,MAAA,GAAAF,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAYA,MAAMG,eAAe,GAAG,CAAC;AACzB,MAAMC,eAAe,GAAG,GAAG;AAC3B,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAEN,SAASC,wBAAwBA,CAAE;EACjDC,uBAAuB;EACvBC,qBAAqB;EACrBC,WAAW;EACXC,QAAQ,GAAGL,IAAI;EACfM,KAAK,GAAG;IACPC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE;EACJ;AAC8B,CAAC,EAAG;EAClC,MAAMC,MAAM,GAAG,IAAAC,2BAAoB,EAAEJ,KAAK,CAACC,CAAE,CAAC;EAC9C,MAAMI,MAAM,GAAG,IAAAD,2BAAoB,EAAEJ,KAAK,CAACE,CAAE,CAAC;EAE9C,MAAMI,YAAY,GAAGA,CACpBC,KAAqD,EACrDC,IAAoB,KAChB;IACJ,IAAKD,KAAK,KAAKE,SAAS,EAAG;IAE3B,MAAMC,GAAG,GAAGC,QAAQ,CAAEJ,KAAK,EAAE,EAAG,CAAC;IAEjC,IAAK,CAAEK,KAAK,CAAEF,GAAI,CAAC,EAAG;MACrBX,QAAQ,CAAE;QAAE,GAAGC,KAAK;QAAE,CAAEQ,IAAI,GAAIE,GAAG,GAAG;MAAI,CAAE,CAAC;IAC9C;EACD,CAAC;EAED,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACxB,sBAAA,CAAAyB,cAAc;IACdC,SAAS,EAAC,8BAA8B;IACxCpB,uBAAuB,EAAGA,uBAAyB;IACnDE,WAAW,EAAGA,WAAa;IAC3BmB,GAAG,EAAG;EAAG,GAET,IAAAJ,MAAA,CAAAC,aAAA,EAACI,qBAAqB;IACrBrB,qBAAqB,EAAGA,qBAAuB;IAC/CsB,KAAK,EAAG,IAAAC,QAAE,EAAE,MAAO,CAAG;IACtB,cAAa,IAAAA,QAAE,EAAE,2BAA4B,CAAG;IAChDb,KAAK,EAAG,CAAEJ,MAAM,EAAE,GAAG,CAAE,CAACkB,IAAI,CAAE,EAAG,CAAG;IACpCtB,QAAQ,EACHuB,IAAI,IACPhB,YAAY,CACXgB,IAAI,EACJ,GACD,CACD;IACDC,aAAa,EAAC;EAAG,CACjB,CAAC,EACF,IAAAV,MAAA,CAAAC,aAAA,EAACI,qBAAqB;IACrBrB,qBAAqB,EAAGA,qBAAuB;IAC/CsB,KAAK,EAAG,IAAAC,QAAE,EAAE,KAAM,CAAG;IACrB,cAAa,IAAAA,QAAE,EAAE,0BAA2B,CAAG;IAC/Cb,KAAK,EAAG,CAAEF,MAAM,EAAE,GAAG,CAAE,CAACgB,IAAI,CAAE,EAAG,CAAG;IACpCtB,QAAQ,EACHuB,IAAI,IACPhB,YAAY,CACXgB,IAAI,EACJ,GACD,CACD;IACDC,aAAa,EAAC;EAAG,CACjB,CACc,CAAC;AAEnB;AAEA,SAASL,qBAAqBA,CAAEM,KAAuB,EAAG;EACzD,OACC,IAAAX,MAAA,CAAAC,aAAA,EAACxB,sBAAA,CAAAmC,iBAAiB;IACjBT,SAAS,EAAC,oDAAoD;IAC9DU,aAAa,EAAC,KAAK;IACnBC,GAAG,EAAGlC,eAAiB;IACvBmC,GAAG,EAAGpC,eAAiB;IACvBqC,KAAK,EAAG,CAAE;MAAEtB,KAAK,EAAE,GAAG;MAAEY,KAAK,EAAE;IAAI,CAAC,CAAI;IAAA,GACnCK;EAAK,CACV,CAAC;AAEJ"}