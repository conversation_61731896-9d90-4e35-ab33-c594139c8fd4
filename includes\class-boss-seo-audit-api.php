<?php
/**
 * API REST pour le module Audit SEO assisté par IA
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe pour gérer les routes API du module Audit SEO
 */
class Boss_SEO_Audit_API {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance de la classe d'analyse.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_Analysis    $analysis    Instance d'analyse SEO.
     */
    private $analysis;

    /**
     * Instance de la classe IA.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_AI    $ai    Instance IA.
     */
    private $ai;

    /**
     * Instance des services externes.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_External_Services    $external_services    Instance des services externes.
     */
    private $external_services;

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    string                    $plugin_name    Le nom du plugin.
     * @param    string                    $version        La version du plugin.
     * @param    Boss_Optimizer_Analysis   $analysis       Instance d'analyse.
     * @param    Boss_Optimizer_AI         $ai             Instance IA.
     */
    public function __construct( $plugin_name, $version, $analysis, $ai ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->analysis = $analysis;
        $this->ai = $ai;

        // Initialiser les services externes pour accéder aux APIs configurées
        $this->external_services = new Boss_External_Services();
    }

    /**
     * Enregistre les routes API.
     *
     * @since    1.2.0
     */
    public function register_routes() {
        // Route pour les statistiques globales
        register_rest_route( 'boss-seo/v1', '/audit/global-stats', array(
            'methods' => 'GET',
            'callback' => array( $this, 'get_global_stats' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args' => array(
                'batch_size' => array(
                    'required' => false,
                    'type' => 'integer',
                    'default' => 500,
                    'minimum' => 100,
                    'maximum' => 1000,
                    'description' => 'Nombre d\'articles à traiter par batch'
                ),
                'offset' => array(
                    'required' => false,
                    'type' => 'integer',
                    'default' => 0,
                    'minimum' => 0,
                    'description' => 'Décalage pour la pagination'
                ),
                'use_cache' => array(
                    'required' => false,
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Utiliser le cache si disponible'
                )
            )
        ));

        // Route pour vider le cache des statistiques
        register_rest_route( 'boss-seo/v1', '/audit/clear-cache', array(
            'methods' => 'POST',
            'callback' => array( $this, 'clear_stats_cache' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour récupérer les pages
        register_rest_route( 'boss-seo/v1', '/audit/pages', array(
            'methods' => 'GET',
            'callback' => array( $this, 'get_pages' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour effectuer un audit
        register_rest_route( 'boss-seo/v1', '/audit/perform', array(
            'methods' => 'POST',
            'callback' => array( $this, 'perform_audit' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args' => array(
                'post_id' => array(
                    'required' => false,
                    'type' => 'integer',
                    'description' => 'ID du post à auditer'
                ),
                'url' => array(
                    'required' => false,
                    'type' => 'string',
                    'description' => 'URL à auditer'
                ),
                'mode' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'expert',
                    'enum' => array( 'basic', 'expert', 'complete' ),
                    'description' => 'Mode d\'audit'
                ),
                'useAI' => array(
                    'required' => false,
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Utiliser l\'IA pour l\'audit'
                ),
                'checks' => array(
                    'required' => false,
                    'type' => 'array',
                    'description' => 'Types de vérifications à effectuer',
                    'default' => array(
                        'meta_tags',
                        'content_quality',
                        'technical_seo',
                        'performance',
                        'accessibility',
                        'images',
                        'links',
                        'structure',
                        'keywords',
                        'schema'
                    ),
                    'items' => array(
                        'type' => 'string',
                        'enum' => array(
                            'meta_tags',
                            'content_quality',
                            'technical_seo',
                            'performance',
                            'accessibility',
                            'images',
                            'links',
                            'structure',
                            'keywords',
                            'schema'
                        )
                    ),
                    'validate_callback' => array( $this, 'validate_checks_param' )
                )
            )
        ) );

        // Route pour l'historique des audits
        register_rest_route( 'boss-seo/v1', '/audit/history', array(
            'methods' => 'GET',
            'callback' => array( $this, 'get_audit_history' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour sauvegarder un audit
        register_rest_route( 'boss-seo/v1', '/audit/save', array(
            'methods' => 'POST',
            'callback' => array( $this, 'save_audit' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour explication IA
        register_rest_route( 'boss-seo/v1', '/ai/explain', array(
            'methods' => 'POST',
            'callback' => array( $this, 'get_ai_explanation' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );

        // Route pour suggestion IA
        register_rest_route( 'boss-seo/v1', '/ai/correct', array(
            'methods' => 'POST',
            'callback' => array( $this, 'get_ai_correction' ),
            'permission_callback' => array( $this, 'check_permissions' )
        ) );
    }

    /**
     * Vérifie les permissions.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Valide le paramètre checks.
     *
     * @since    1.2.0
     * @param    mixed    $value    Valeur à valider.
     * @return   bool               True si valide.
     */
    public function validate_checks_param( $value ) {
        // Accepter un tableau vide ou null
        if ( empty( $value ) ) {
            return true;
        }

        // Vérifier que c'est un tableau
        if ( ! is_array( $value ) ) {
            return false;
        }

        // Types de vérifications autorisés
        $allowed_checks = array(
            'meta_tags',
            'content_quality',
            'technical_seo',
            'performance',
            'accessibility',
            'images',
            'links',
            'structure',
            'keywords',
            'schema'
        );

        // Vérifier chaque élément du tableau
        foreach ( $value as $check ) {
            if ( ! is_string( $check ) || ! in_array( $check, $allowed_checks, true ) ) {
                error_log( 'Boss SEO Audit: Check invalide détecté: ' . print_r( $check, true ) );
                return false;
            }
        }

        return true;
    }

    /**
     * Récupère les statistiques globales du site.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_global_stats( $request ) {
        try {
            $batch_size = $request->get_param( 'batch_size' ) ?: 500;
            $offset = $request->get_param( 'offset' ) ?: 0;
            $use_cache = $request->get_param( 'use_cache' ) !== false;

            // Vérifier le cache si demandé et si c'est le premier batch
            if ( $use_cache && $offset === 0 ) {
                $cached_stats = get_transient( 'boss_seo_global_stats_cache' );
                if ( $cached_stats !== false ) {
                    return new WP_REST_Response( array(
                        'success' => true,
                        'data' => $cached_stats,
                        'cached' => true,
                        'cache_expires' => get_option( '_transient_timeout_boss_seo_global_stats_cache' )
                    ), 200 );
                }
            }

            // Récupérer tous les types de contenu supportés
            $post_types = array( 'post', 'page' );

            // Ajouter les produits WooCommerce si disponible
            if ( class_exists( 'WooCommerce' ) ) {
                $post_types[] = 'product';
            }

            // Ajouter d'autres types de contenu personnalisés
            $custom_post_types = get_post_types( array( 'public' => true, '_builtin' => false ), 'names' );
            $post_types = array_merge( $post_types, $custom_post_types );

            // Récupérer les stats existantes si c'est un batch suivant
            $stats = $offset > 0 ? get_transient( 'boss_seo_partial_stats_' . $offset ) : array(
                'totalContent' => 0,
                'optimizedContent' => 0,
                'unoptimizedContent' => 0,
                'averageScore' => 0,
                'contentTypes' => array(),
                'commonErrors' => array(),
                'criticalIssues' => 0,
                'lastScanDate' => get_option( 'boss_seo_last_global_scan', null ),
                'processed' => 0,
                'total_to_process' => 0
            );

            if ( $offset === 0 ) {
                // Calculer le total à traiter pour la barre de progression
                $total_to_process = 0;
                foreach ( $post_types as $post_type ) {
                    $count = wp_count_posts( $post_type );
                    $total_to_process += $count->publish;
                }
                $stats['total_to_process'] = $total_to_process;
            }

            $total_score = $offset > 0 ? ( $stats['averageScore'] * $stats['processed'] ) : 0;
            $scored_content = $offset > 0 ? $stats['processed'] : 0;
            $all_errors = $offset > 0 ? $stats['commonErrors'] : array();

            // Traitement par batch pour éviter les timeouts
            $processed_in_batch = 0;
            $remaining_batch_size = $batch_size;

            foreach ( $post_types as $post_type ) {
                if ( $remaining_batch_size <= 0 ) break;

                // Calculer l'offset pour ce type de post
                $type_offset = max( 0, $offset );

                $posts = get_posts( array(
                    'post_type' => $post_type,
                    'post_status' => 'publish',
                    'numberposts' => min( $remaining_batch_size, 500 ), // CORRECTION: Limiter à 500 max
                    'offset' => $type_offset,
                    'orderby' => 'date',
                    'order' => 'DESC',
                    'fields' => 'ids', // OPTIMISATION: Récupérer seulement les IDs
                    'meta_query' => array(
                        'relation' => 'OR',
                        array(
                            'key' => '_boss_seo_title',
                            'compare' => 'EXISTS'
                        ),
                        array(
                            'key' => '_boss_seo_meta_description',
                            'compare' => 'EXISTS'
                        )
                    )
                ) );

                $type_stats = isset( $stats['contentTypes'][$post_type] ) ? $stats['contentTypes'][$post_type] : array(
                    'total' => 0,
                    'optimized' => 0,
                    'unoptimized' => 0,
                    'average_score' => 0
                );

                foreach ( $posts as $post_id ) {
                    if ( $processed_in_batch >= $batch_size ) break 2;

                    $stats['totalContent']++;
                    $type_stats['total']++;
                    $processed_in_batch++;
                    $remaining_batch_size--;

                    // Vérifier si le contenu est optimisé
                    $is_optimized = get_post_meta( $post_id, '_boss_seo_optimized', true );
                    $seo_score = get_post_meta( $post_id, '_boss_seo_score', true );

                    if ( $is_optimized || $seo_score > 70 ) {
                        $stats['optimizedContent']++;
                        $type_stats['optimized']++;
                    } else {
                        $stats['unoptimizedContent']++;
                        $type_stats['unoptimized']++;
                    }

                    // Calculer le score moyen
                    if ( $seo_score ) {
                        $total_score += intval( $seo_score );
                        $scored_content++;
                    }

                    // Collecter les erreurs communes (limité pour les performances)
                    if ( $processed_in_batch <= 100 ) { // Limiter l'analyse des erreurs aux 100 premiers
                        $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );
                        if ( is_array( $recommendations ) ) {
                            foreach ( $recommendations as $rec ) {
                                if ( isset( $rec['type'] ) ) {
                                    $error_key = $rec['type'];
                                    if ( ! isset( $all_errors[ $error_key ] ) ) {
                                        $all_errors[ $error_key ] = array(
                                            'type' => $error_key,
                                            'title' => $rec['text'] ?? $error_key,
                                            'count' => 0,
                                            'severity' => $rec['type']
                                        );
                                    }
                                    $all_errors[ $error_key ]['count']++;

                                    if ( $rec['type'] === 'critical' ) {
                                        $stats['criticalIssues']++;
                                    }
                                }
                            }
                        }
                    }
                }

                if ( $type_stats['total'] > 0 ) {
                    $stats['contentTypes'][ $post_type ] = $type_stats;
                }
            }

            // Mettre à jour les statistiques de progression
            $stats['processed'] = $stats['totalContent'];
            $stats['progress'] = $stats['total_to_process'] > 0 ? round( ( $stats['processed'] / $stats['total_to_process'] ) * 100 ) : 100;
            $stats['has_more'] = $processed_in_batch >= $batch_size;

            // Calculer le score moyen global
            if ( $scored_content > 0 ) {
                $stats['averageScore'] = round( $total_score / $scored_content );
            }

            // Trier les erreurs par fréquence
            uasort( $all_errors, function( $a, $b ) {
                return $b['count'] - $a['count'];
            } );

            $stats['commonErrors'] = array_slice( array_values( $all_errors ), 0, 10 );

            // Sauvegarder en cache si c'est terminé
            if ( ! $stats['has_more'] ) {
                set_transient( 'boss_seo_global_stats_cache', $stats, 3600 ); // Cache 1 heure
                update_option( 'boss_seo_last_global_scan', current_time( 'mysql' ) );
                delete_transient( 'boss_seo_partial_stats_' . $offset ); // Nettoyer le cache partiel
            } else {
                // Sauvegarder les stats partielles pour le prochain batch
                set_transient( 'boss_seo_partial_stats_' . ( $offset + $batch_size ), $stats, 1800 ); // 30 minutes
            }

            return new WP_REST_Response( array(
                'success' => true,
                'data' => $stats,
                'batch_info' => array(
                    'processed' => $processed_in_batch,
                    'offset' => $offset,
                    'has_more' => $stats['has_more'],
                    'progress' => $stats['progress'],
                    'next_offset' => $stats['has_more'] ? $offset + $batch_size : null
                )
            ), 200 );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Audit API - Erreur get_global_stats: ' . $e->getMessage() );
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la récupération des statistiques: ' . $e->getMessage()
            ), 500 );
        } catch ( Error $e ) {
            error_log( 'Boss SEO Audit API - Erreur fatale get_global_stats: ' . $e->getMessage() );
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur fatale lors de la récupération des statistiques'
            ), 500 );
        }
    }

    /**
     * Vide le cache des statistiques globales.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function clear_stats_cache( $request ) {
        try {
            // Supprimer le cache principal
            delete_transient( 'boss_seo_global_stats_cache' );

            // Supprimer tous les caches partiels
            global $wpdb;
            $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_boss_seo_partial_stats_%'" );
            $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_boss_seo_partial_stats_%'" );

            return new WP_REST_Response( array(
                'success' => true,
                'message' => 'Cache vidé avec succès'
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors du vidage du cache: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Récupère la liste des pages disponibles.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_pages( $request ) {
        try {
            $post_types = array( 'post', 'page' );

            // Ajouter les produits WooCommerce si disponible
            if ( class_exists( 'WooCommerce' ) ) {
                $post_types[] = 'product';
            }

            $pages = array();

            foreach ( $post_types as $post_type ) {
                $posts = get_posts( array(
                    'post_type' => $post_type,
                    'post_status' => 'publish',
                    'numberposts' => 50, // Limiter pour les performances
                    'orderby' => 'date',
                    'order' => 'DESC'
                ) );

                foreach ( $posts as $post ) {
                    $pages[] = array(
                        'id' => $post->ID,
                        'title' => $post->post_title,
                        'url' => get_permalink( $post->ID ),
                        'type' => $post_type,
                        'date' => $post->post_date,
                        'seo_score' => get_post_meta( $post->ID, '_boss_seo_score', true ) ?: 0,
                        'is_optimized' => get_post_meta( $post->ID, '_boss_seo_optimized', true ) ? true : false
                    );
                }
            }

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array( 'pages' => $pages )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la récupération des pages: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Effectue un audit SEO sur un post.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function perform_audit( $request ) {
        try {
            // DÉBOGAGE : Logger tous les paramètres reçus
            $all_params = $request->get_params();
            error_log( 'Boss SEO Audit: TOUS les paramètres reçus: ' . print_r( $all_params, true ) );

            $post_id = $request->get_param( 'post_id' );
            $url = $request->get_param( 'url' );
            $mode = $request->get_param( 'mode' ) ?: 'expert';
            $use_ai = $request->get_param( 'useAI' ) !== false;
            $checks = $request->get_param( 'checks' );

            // DÉBOGAGE : Logger le type et contenu de checks
            error_log( 'Boss SEO Audit: Type de checks: ' . gettype( $checks ) );
            error_log( 'Boss SEO Audit: Contenu de checks: ' . print_r( $checks, true ) );

            // Valeur par défaut si checks est vide
            if ( empty( $checks ) || ! is_array( $checks ) ) {
                $checks = array(
                    'meta_tags',
                    'content_quality',
                    'technical_seo',
                    'performance',
                    'accessibility',
                    'images',
                    'links',
                    'structure',
                    'keywords',
                    'schema'
                );
                error_log( 'Boss SEO Audit: Utilisation des checks par défaut' );
            }

            error_log( 'Boss SEO Audit: Paramètres finaux - post_id: ' . $post_id . ', url: ' . $url . ', mode: ' . $mode . ', checks: ' . implode( ',', $checks ) );

            // CORRECTION : Accepter soit post_id soit url
            if ( ! $post_id && ! $url ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'ID de post ou URL requis'
                ), 400 );
            }

            // CORRECTION : Convertir URL en post_id si nécessaire
            if ( ! $post_id && $url ) {
                $post_id = url_to_postid( $url );
                if ( ! $post_id ) {
                    return new WP_REST_Response( array(
                        'success' => false,
                        'message' => 'Impossible de trouver le post correspondant à cette URL'
                    ), 404 );
                }
            }

            $post = get_post( $post_id );
            if ( ! $post ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Post non trouvé'
                ), 404 );
            }

            // Utiliser le système d'analyse existant de Boss SEO
            $analysis_result = $this->analysis->analyze( $post );

            if ( ! $analysis_result['success'] ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => $analysis_result['message']
                ), 500 );
            }

            // Ajouter les données de performance Google PageSpeed si configuré
            $performance_data = $this->get_pagespeed_data( get_permalink( $post_id ) );

            // Formater les résultats pour l'interface d'audit
            $audit_result = array(
                'post_id' => $post_id,
                'url' => get_permalink( $post_id ),
                'title' => $post->post_title,
                'date' => current_time( 'mysql' ),
                'globalScore' => $analysis_result['score'] ?? 0,
                'errors' => $this->format_errors_for_audit( $analysis_result ),
                'performance' => array_merge(
                    array(
                        'seo_score' => $analysis_result['score'] ?? 0,
                        'content_length' => str_word_count( strip_tags( $post->post_content ) ),
                        'images_count' => substr_count( $post->post_content, '<img' ),
                        'links_count' => substr_count( $post->post_content, '<a' )
                    ),
                    $performance_data
                ),
                'metadata' => array(
                    'title' => get_post_meta( $post_id, '_boss_seo_title', true ) ?: $post->post_title,
                    'description' => get_post_meta( $post_id, '_boss_seo_meta_description', true ),
                    'keywords' => get_post_meta( $post_id, '_boss_seo_focus_keyword', true ),
                    'og_title' => get_post_meta( $post_id, '_boss_seo_og_title', true ),
                    'og_description' => get_post_meta( $post_id, '_boss_seo_og_description', true ),
                    'twitter_title' => get_post_meta( $post_id, '_boss_seo_twitter_title', true ),
                    'twitter_description' => get_post_meta( $post_id, '_boss_seo_twitter_description', true )
                )
            );

            // Sauvegarder les résultats d'audit
            update_post_meta( $post_id, '_boss_seo_audit_result', $audit_result );
            update_post_meta( $post_id, '_boss_seo_audit_date', current_time( 'mysql' ) );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => $audit_result
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de l\'audit: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Formate les erreurs pour l'interface d'audit.
     *
     * @since    1.2.0
     * @param    array    $analysis_result    Résultat de l'analyse.
     * @return   array                        Erreurs formatées.
     */
    private function format_errors_for_audit( $analysis_result ) {
        // Initialiser avec une structure valide par défaut
        $errors = array(
            'critical' => array(),
            'medium' => array(),
            'low' => array()
        );

        // Vérifier que $analysis_result est valide
        if ( ! is_array( $analysis_result ) ) {
            error_log( 'Boss SEO Audit API: analysis_result n\'est pas un tableau: ' . print_r( $analysis_result, true ) );
            return $errors;
        }

        if ( isset( $analysis_result['recommendations'] ) && is_array( $analysis_result['recommendations'] ) ) {
            foreach ( $analysis_result['recommendations'] as $recommendation ) {
                // Vérifier que la recommandation est valide
                if ( ! is_array( $recommendation ) ) {
                    continue;
                }

                // Adapter le format de Boss_Optimizer_Analysis
                $severity = $this->map_optimizer_severity_to_audit( $recommendation['type'] ?? 'info' );
                $category = $this->map_optimizer_element_to_category( $recommendation['element'] ?? 'general' );

                $error = array(
                    'id' => 'optimizer_' . sanitize_title( $recommendation['text'] ?? 'unknown' ),
                    'title' => $recommendation['text'] ?? 'Problème détecté',
                    'description' => $recommendation['text'] ?? '',
                    'category' => $category,
                    'severity' => $severity,
                    'location' => $this->map_optimizer_element_to_location( $recommendation['element'] ?? 'general' ),
                    'elements' => array( $recommendation['element'] ?? 'general' ),
                    'currentValue' => null,
                    'recommendedValue' => null
                );

                $errors[ $severity ][] = $error;
            }
        } else {
            // Si aucune recommandation n'est trouvée, ajouter un message informatif
            error_log( 'Boss SEO Audit API: Aucune recommandation trouvée dans analysis_result: ' . print_r( $analysis_result, true ) );
        }

        return $errors;
    }

    /**
     * Mappe la sévérité des recommandations.
     *
     * @since    1.2.0
     * @param    string    $type    Type de recommandation.
     * @return   string             Sévérité mappée.
     */
    private function map_recommendation_severity( $type ) {
        $severity_map = array(
            'critical' => 'critical',
            'error' => 'critical',
            'warning' => 'medium',
            'info' => 'low',
            'success' => 'low'
        );

        return $severity_map[ $type ] ?? 'low';
    }

    /**
     * Mappe la sévérité des recommandations de l'optimizer vers l'audit.
     *
     * @since    1.2.0
     * @param    string    $type    Type de recommandation de l'optimizer.
     * @return   string             Sévérité mappée pour l'audit.
     */
    private function map_optimizer_severity_to_audit( $type ) {
        switch ( $type ) {
            case 'critical':
                return 'critical';
            case 'warning':
                return 'medium';
            case 'info':
            default:
                return 'low';
        }
    }

    /**
     * Mappe l'élément de l'optimizer vers une catégorie d'audit.
     *
     * @since    1.2.0
     * @param    string    $element    Élément de l'optimizer.
     * @return   string                Catégorie d'audit.
     */
    private function map_optimizer_element_to_category( $element ) {
        switch ( $element ) {
            case 'title':
                return 'meta_tags';
            case 'content':
                return 'content_quality';
            case 'headings':
                return 'structure';
            case 'images':
                return 'images';
            case 'links':
                return 'links';
            case 'keywords':
                return 'keywords';
            default:
                return 'technical_seo';
        }
    }

    /**
     * Mappe l'élément de l'optimizer vers une localisation.
     *
     * @since    1.2.0
     * @param    string    $element    Élément de l'optimizer.
     * @return   string                Localisation.
     */
    private function map_optimizer_element_to_location( $element ) {
        switch ( $element ) {
            case 'title':
                return '<head>';
            case 'content':
                return '<body>';
            case 'headings':
                return '<body>';
            case 'images':
                return '<body>';
            case 'links':
                return '<body>';
            default:
                return 'page';
        }
    }



    /**
     * Mappe la catégorie des recommandations.
     *
     * @since    1.2.0
     * @param    array    $recommendation    Recommandation.
     * @return   string                      Catégorie mappée.
     */
    private function map_recommendation_category( $recommendation ) {
        $text = strtolower( $recommendation['text'] ?? '' );

        if ( strpos( $text, 'title' ) !== false || strpos( $text, 'titre' ) !== false ) {
            return 'meta';
        } elseif ( strpos( $text, 'description' ) !== false ) {
            return 'meta';
        } elseif ( strpos( $text, 'keyword' ) !== false || strpos( $text, 'mot-clé' ) !== false ) {
            return 'keywords';
        } elseif ( strpos( $text, 'image' ) !== false || strpos( $text, 'alt' ) !== false ) {
            return 'images';
        } elseif ( strpos( $text, 'link' ) !== false || strpos( $text, 'lien' ) !== false ) {
            return 'links';
        } elseif ( strpos( $text, 'h1' ) !== false || strpos( $text, 'heading' ) !== false ) {
            return 'structure';
        } else {
            return 'technical';
        }
    }

    /**
     * Récupère l'historique des audits.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_audit_history( $request ) {
        try {
            $history = get_option( 'boss_seo_audit_history', array() );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array( 'history' => $history )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la récupération de l\'historique: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Sauvegarde un audit.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_audit( $request ) {
        try {
            $audit_data = $request->get_json_params();

            if ( ! $audit_data ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Données d\'audit requises'
                ), 400 );
            }

            // Ajouter à l'historique
            $history = get_option( 'boss_seo_audit_history', array() );
            array_unshift( $history, $audit_data );

            // Garder seulement les 50 derniers audits
            $history = array_slice( $history, 0, 50 );

            update_option( 'boss_seo_audit_history', $history );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array( 'saved' => true )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la sauvegarde: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Génère une explication IA pour une erreur.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ai_explanation( $request ) {
        try {
            $error = $request->get_param( 'error' );
            $language = $request->get_param( 'language' ) ?: 'fr';

            if ( ! $error ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Erreur requise'
                ), 400 );
            }

            // Utiliser l'IA configurée pour générer l'explication
            $prompt = "Explique cette erreur SEO en français de manière simple et claire : " . $error['title'] . ". " . $error['description'];

            $ai_result = $this->ai->generate_content( $prompt, array(
                'max_tokens' => 200,
                'temperature' => 0.7
            ) );

            if ( $ai_result['success'] ) {
                $explanation = array(
                    'description' => $ai_result['content'],
                    'impact' => $this->get_impact_description( $error['severity'] ),
                    'recommendation' => 'Corrigez cette erreur pour améliorer votre référencement.'
                );

                return new WP_REST_Response( array(
                    'success' => true,
                    'data' => array( 'explanation' => $explanation )
                ), 200 );
            } else {
                throw new Exception( $ai_result['message'] ?? 'Erreur IA' );
            }

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la génération de l\'explication: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Génère une suggestion de correction IA.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    La requête REST.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ai_correction( $request ) {
        try {
            $error = $request->get_param( 'error' );
            $language = $request->get_param( 'language' ) ?: 'fr';

            if ( ! $error ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => 'Erreur requise'
                ), 400 );
            }

            // Utiliser l'IA pour générer une suggestion de correction
            $prompt = "Donne une suggestion de correction concrète et actionnable pour cette erreur SEO : " . $error['title'] . ". " . $error['description'] . ". Fournis du code HTML si nécessaire.";

            $ai_result = $this->ai->generate_content( $prompt, array(
                'max_tokens' => 300,
                'temperature' => 0.5
            ) );

            if ( $ai_result['success'] ) {
                $suggestion = array(
                    'type' => 'text',
                    'title' => 'Suggestion de correction IA',
                    'description' => $ai_result['content'],
                    'canApply' => false,
                    'steps' => array(
                        'Analysez la suggestion ci-dessus',
                        'Appliquez les modifications recommandées',
                        'Vérifiez le résultat'
                    )
                );

                return new WP_REST_Response( array(
                    'success' => true,
                    'data' => array( 'suggestion' => $suggestion )
                ), 200 );
            } else {
                throw new Exception( $ai_result['message'] ?? 'Erreur IA' );
            }

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => 'Erreur lors de la génération de la suggestion: ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Récupère les données de performance Google PageSpeed.
     *
     * @since    1.2.0
     * @param    string    $url    URL à analyser.
     * @return   array             Données de performance.
     */
    private function get_pagespeed_data( $url ) {
        try {
            // Vérifier si l'API Google PageSpeed est configurée
            $pagespeed_settings = get_option( 'boss_seo_external_services', array() );

            if ( empty( $pagespeed_settings['google_pagespeed_api_key'] ) ) {
                return array(
                    'pagespeed_available' => false,
                    'pagespeed_message' => 'API Google PageSpeed non configurée'
                );
            }

            // Utiliser les services externes pour récupérer les données PageSpeed
            $pagespeed_data = $this->external_services->get_pagespeed_insights( $url );

            if ( $pagespeed_data && isset( $pagespeed_data['lighthouseResult'] ) ) {
                $lighthouse = $pagespeed_data['lighthouseResult'];
                $audits = $lighthouse['audits'] ?? array();

                return array(
                    'pagespeed_available' => true,
                    'performance_score' => round( ( $lighthouse['categories']['performance']['score'] ?? 0 ) * 100 ),
                    'seo_score_pagespeed' => round( ( $lighthouse['categories']['seo']['score'] ?? 0 ) * 100 ),
                    'accessibility_score' => round( ( $lighthouse['categories']['accessibility']['score'] ?? 0 ) * 100 ),
                    'best_practices_score' => round( ( $lighthouse['categories']['best-practices']['score'] ?? 0 ) * 100 ),
                    'first_contentful_paint' => $audits['first-contentful-paint']['displayValue'] ?? 'N/A',
                    'largest_contentful_paint' => $audits['largest-contentful-paint']['displayValue'] ?? 'N/A',
                    'cumulative_layout_shift' => $audits['cumulative-layout-shift']['displayValue'] ?? 'N/A',
                    'total_blocking_time' => $audits['total-blocking-time']['displayValue'] ?? 'N/A',
                    'speed_index' => $audits['speed-index']['displayValue'] ?? 'N/A'
                );
            } else {
                return array(
                    'pagespeed_available' => false,
                    'pagespeed_message' => 'Erreur lors de la récupération des données PageSpeed'
                );
            }

        } catch ( Exception $e ) {
            return array(
                'pagespeed_available' => false,
                'pagespeed_message' => 'Erreur PageSpeed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Récupère la description de l'impact selon la sévérité.
     *
     * @since    1.2.0
     * @param    string    $severity    Sévérité de l'erreur.
     * @return   string                 Description de l'impact.
     */
    private function get_impact_description( $severity ) {
        switch ( $severity ) {
            case 'critical':
                return 'Impact élevé sur le SEO - Correction prioritaire recommandée';
            case 'medium':
                return 'Impact modéré sur le SEO - Correction recommandée';
            case 'low':
                return 'Impact faible sur le SEO - Amélioration suggérée';
            default:
                return 'Impact sur le SEO à évaluer';
        }
    }
}
