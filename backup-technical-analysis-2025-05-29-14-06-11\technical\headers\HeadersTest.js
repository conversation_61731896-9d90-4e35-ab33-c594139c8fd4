import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  Spinner,
  Notice
} from '@wordpress/components';

const HeadersTest = ({ onRunTest, isRunningTest, testResults }) => {
  const [url, setUrl] = useState('');
  const [urlError, setUrlError] = useState(null);
  
  // Fonction pour valider l'URL
  const validateUrl = () => {
    if (!url) {
      setUrlError(__('Veuillez entrer une URL', 'boss-seo'));
      return false;
    }
    
    try {
      new URL(url);
      setUrlError(null);
      return true;
    } catch (e) {
      setUrlError(__('URL invalide. Veuillez entrer une URL complète (ex: https://example.com)', 'boss-seo'));
      return false;
    }
  };
  
  // Fonction pour exécuter le test
  const handleRunTest = () => {
    if (validateUrl()) {
      onRunTest(url);
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'success':
        return 'boss-text-green-600';
      case 'warning':
        return 'boss-text-yellow-600';
      case 'error':
        return 'boss-text-red-600';
      default:
        return 'boss-text-boss-gray';
    }
  };
  
  // Fonction pour obtenir l'icône en fonction du statut
  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return 'dashicons-yes-alt';
      case 'warning':
        return 'dashicons-warning';
      case 'error':
        return 'dashicons-dismiss';
      default:
        return 'dashicons-info';
    }
  };

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Tester les en-têtes HTTP', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-mb-6">
          <div className="boss-flex boss-space-x-2">
            <TextControl
              label={__('URL à tester', 'boss-seo')}
              help={__('Entrez l\'URL complète de la page à tester', 'boss-seo')}
              value={url}
              onChange={(value) => {
                setUrl(value);
                if (urlError) setUrlError(null);
              }}
              placeholder="https://example.com"
              className="boss-flex-1"
            />
            <div className="boss-flex boss-items-end boss-mb-2">
              <Button
                isPrimary
                onClick={handleRunTest}
                disabled={isRunningTest}
                isBusy={isRunningTest}
              >
                {isRunningTest ? __('Test en cours...', 'boss-seo') : __('Tester', 'boss-seo')}
              </Button>
            </div>
          </div>
          
          {urlError && (
            <Notice status="error" isDismissible={false} className="boss-mt-2">
              {urlError}
            </Notice>
          )}
        </div>
        
        {isRunningTest ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : testResults ? (
          <div>
            <div className="boss-mb-6">
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                {__('Résultats du test', 'boss-seo')}
              </h3>
              
              <div className="boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-mb-4">
                <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
                  <span className="boss-text-boss-gray">{__('URL testée:', 'boss-seo')}</span>
                  <span className="boss-font-medium boss-text-boss-dark">{testResults.url}</span>
                </div>
                
                <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
                  <span className="boss-text-boss-gray">{__('Code de statut:', 'boss-seo')}</span>
                  <span className="boss-font-medium boss-text-boss-dark">{testResults.statusCode}</span>
                </div>
                
                <div className="boss-flex boss-justify-between boss-items-center">
                  <span className="boss-text-boss-gray">{__('Temps de réponse:', 'boss-seo')}</span>
                  <span className="boss-font-medium boss-text-boss-dark">{testResults.responseTime} ms</span>
                </div>
              </div>
            </div>
            
            <div className="boss-mb-6">
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                {__('En-têtes détectés', 'boss-seo')}
              </h3>
              
              <div className="boss-overflow-x-auto">
                <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
                  <thead className="boss-bg-gray-50">
                    <tr>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Nom', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Valeur', 'boss-seo')}
                      </th>
                      <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                        {__('Statut', 'boss-seo')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                    {testResults.headers.length === 0 ? (
                      <tr>
                        <td colSpan="3" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                          {__('Aucun en-tête détecté.', 'boss-seo')}
                        </td>
                      </tr>
                    ) : (
                      testResults.headers.map((header, index) => (
                        <tr key={index} className="boss-hover:boss-bg-gray-50">
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className="boss-font-medium boss-text-boss-dark">{header.name}</div>
                          </td>
                          <td className="boss-px-6 boss-py-4">
                            <div className="boss-text-boss-gray boss-truncate boss-max-w-xs">{header.value}</div>
                          </td>
                          <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                            <div className={`boss-flex boss-items-center ${getStatusColorClass(header.status)}`}>
                              <span className={`dashicons ${getStatusIcon(header.status)} boss-mr-1`}></span>
                              {header.statusText}
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
            
            <div>
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                {__('Recommandations', 'boss-seo')}
              </h3>
              
              {testResults.recommendations.length === 0 ? (
                <div className="boss-p-4 boss-bg-green-50 boss-rounded-lg boss-text-green-800">
                  {__('Aucune recommandation. Vos en-têtes HTTP sont bien configurés !', 'boss-seo')}
                </div>
              ) : (
                <div className="boss-space-y-3">
                  {testResults.recommendations.map((recommendation, index) => (
                    <div key={index} className={`boss-p-4 boss-rounded-lg ${
                      recommendation.priority === 'high' 
                        ? 'boss-bg-red-50 boss-text-red-800' 
                        : recommendation.priority === 'medium'
                          ? 'boss-bg-yellow-50 boss-text-yellow-800'
                          : 'boss-bg-blue-50 boss-text-blue-800'
                    }`}>
                      <div className="boss-flex boss-items-start">
                        <div className="boss-flex-shrink-0 boss-mr-3">
                          <span className={`dashicons ${
                            recommendation.priority === 'high' 
                              ? 'dashicons-warning' 
                              : recommendation.priority === 'medium'
                                ? 'dashicons-flag'
                                : 'dashicons-info'
                          }`}></span>
                        </div>
                        <div>
                          <h4 className="boss-font-medium boss-mb-1">{recommendation.title}</h4>
                          <p className="boss-text-sm">{recommendation.description}</p>
                          {recommendation.suggestion && (
                            <div className="boss-mt-2 boss-p-2 boss-bg-white boss-rounded boss-text-sm boss-font-mono boss-text-boss-dark">
                              {recommendation.suggestion}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="boss-text-center boss-py-8 boss-text-boss-gray">
            <div className="boss-text-5xl boss-mb-4">
              <span className="dashicons dashicons-admin-network"></span>
            </div>
            <h3 className="boss-text-lg boss-font-medium boss-mb-2">
              {__('Testez vos en-têtes HTTP', 'boss-seo')}
            </h3>
            <p className="boss-mb-4">
              {__('Entrez une URL et cliquez sur "Tester" pour analyser les en-têtes HTTP de votre site.', 'boss-seo')}
            </p>
            <p className="boss-text-sm">
              {__('Le test vérifiera la présence des en-têtes de sécurité importants, les paramètres de cache et les configurations CORS.', 'boss-seo')}
            </p>
          </div>
        )}
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-text-sm boss-text-boss-gray">
          {__('Les en-têtes HTTP jouent un rôle crucial dans la sécurité et les performances de votre site.', 'boss-seo')}
        </div>
      </CardFooter>
    </Card>
  );
};

export default HeadersTest;
