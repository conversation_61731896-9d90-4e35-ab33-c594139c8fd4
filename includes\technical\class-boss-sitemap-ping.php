<?php
/**
 * Classe pour gérer le ping des moteurs de recherche
 *
 * @package    <PERSON>_Seo
 * @subpackage Boss_Seo/includes/technical
 * @since      1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Boss_Sitemap_Ping
 *
 * Gère le ping des moteurs de recherche pour les sitemaps
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */
class Boss_Sitemap_Ping {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * URLs des services de ping.
     *
     * @since    1.2.0
     * @access   private
     * @var      array    $ping_services    Les services de ping.
     */
    private $ping_services;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        $this->ping_services = array(
            'google' => array(
                'name' => 'Google',
                'url' => 'https://www.google.com/ping?sitemap=%s',
                'enabled' => true
            ),
            'bing' => array(
                'name' => 'Bing',
                'url' => 'https://www.bing.com/ping?sitemap=%s',
                'enabled' => true
            ),
            'yandex' => array(
                'name' => 'Yandex',
                'url' => 'https://webmaster.yandex.com/ping?sitemap=%s',
                'enabled' => false // Désactivé par défaut
            )
        );
    }

    /**
     * Ping tous les moteurs de recherche configurés.
     *
     * @since    1.2.0
     * @param    array    $sitemap_urls    URLs des sitemaps à ping (optionnel).
     * @return   array                     Résultats du ping.
     */
    public function ping_all_engines( $sitemap_urls = null ) {
        if ( $sitemap_urls === null ) {
            $sitemap_urls = $this->get_sitemap_urls();
        }

        $results = array();
        $overall_success = true;

        foreach ( $this->ping_services as $engine => $service ) {
            if ( ! $service['enabled'] ) {
                continue;
            }

            $engine_results = array();
            $engine_success = true;

            foreach ( $sitemap_urls as $sitemap_url ) {
                $ping_result = $this->ping_engine( $engine, $sitemap_url );
                $engine_results[] = $ping_result;
                
                if ( ! $ping_result['success'] ) {
                    $engine_success = false;
                    $overall_success = false;
                }
            }

            $results[$engine] = array(
                'name' => $service['name'],
                'success' => $engine_success,
                'results' => $engine_results,
                'total_sitemaps' => count( $sitemap_urls ),
                'successful_pings' => count( array_filter( $engine_results, function( $r ) { return $r['success']; } ) )
            );
        }

        // Enregistrer l'historique
        $this->log_ping_history( $results );

        return array(
            'success' => $overall_success,
            'engines' => $results,
            'total_sitemaps' => count( $sitemap_urls ),
            'timestamp' => current_time( 'mysql' ),
            'message' => $overall_success 
                ? __( 'Ping réussi pour tous les moteurs', 'boss-seo' )
                : __( 'Ping partiellement réussi', 'boss-seo' )
        );
    }

    /**
     * Ping un moteur de recherche spécifique.
     *
     * @since    1.2.0
     * @param    string    $engine         Le moteur de recherche.
     * @param    string    $sitemap_url    L'URL du sitemap.
     * @return   array                     Résultat du ping.
     */
    private function ping_engine( $engine, $sitemap_url ) {
        if ( ! isset( $this->ping_services[$engine] ) ) {
            return array(
                'success' => false,
                'engine' => $engine,
                'sitemap_url' => $sitemap_url,
                'message' => __( 'Moteur de recherche non supporté', 'boss-seo' ),
                'response_code' => 0,
                'timestamp' => current_time( 'mysql' )
            );
        }

        $service = $this->ping_services[$engine];
        $ping_url = sprintf( $service['url'], urlencode( $sitemap_url ) );

        try {
            $response = wp_remote_get( $ping_url, array(
                'timeout' => 30,
                'user-agent' => 'Boss SEO Sitemap Ping/' . $this->version,
                'headers' => array(
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                )
            ) );

            if ( is_wp_error( $response ) ) {
                return array(
                    'success' => false,
                    'engine' => $engine,
                    'sitemap_url' => $sitemap_url,
                    'message' => $response->get_error_message(),
                    'response_code' => 0,
                    'timestamp' => current_time( 'mysql' )
                );
            }

            $response_code = wp_remote_retrieve_response_code( $response );
            $response_body = wp_remote_retrieve_body( $response );

            // Codes de succès acceptés
            $success_codes = array( 200, 201, 202, 204 );
            $is_success = in_array( $response_code, $success_codes );

            // Messages spécifiques par moteur
            $message = $this->get_ping_message( $engine, $response_code, $response_body );

            return array(
                'success' => $is_success,
                'engine' => $engine,
                'sitemap_url' => $sitemap_url,
                'message' => $message,
                'response_code' => $response_code,
                'response_body' => substr( $response_body, 0, 500 ), // Limiter la taille
                'timestamp' => current_time( 'mysql' )
            );

        } catch ( Exception $e ) {
            return array(
                'success' => false,
                'engine' => $engine,
                'sitemap_url' => $sitemap_url,
                'message' => sprintf( __( 'Erreur lors du ping: %s', 'boss-seo' ), $e->getMessage() ),
                'response_code' => 0,
                'timestamp' => current_time( 'mysql' )
            );
        }
    }

    /**
     * Récupère les URLs des sitemaps à ping.
     *
     * @since    1.2.0
     * @return   array    Les URLs des sitemaps.
     */
    private function get_sitemap_urls() {
        $site_url = get_site_url();
        $sitemap_settings = get_option( 'boss-seo_advanced_sitemap_settings', array() );
        
        $urls = array();
        
        // Sitemap principal
        $urls[] = $site_url . '/sitemap.xml';
        
        // Sitemaps par type de contenu
        if ( isset( $sitemap_settings['enablePostTypeSitemaps'] ) && is_array( $sitemap_settings['enablePostTypeSitemaps'] ) ) {
            foreach ( $sitemap_settings['enablePostTypeSitemaps'] as $post_type => $enabled ) {
                if ( $enabled ) {
                    $urls[] = $site_url . '/sitemap-' . $post_type . '.xml';
                }
            }
        }
        
        // Sitemaps spécialisés
        $specialized_sitemaps = array(
            'enableImageSitemap' => 'sitemap-image.xml',
            'enableVideoSitemap' => 'sitemap-video.xml',
            'enableNewsSitemap' => 'sitemap-news.xml',
            'enableStoriesSitemap' => 'sitemap-stories.xml'
        );
        
        foreach ( $specialized_sitemaps as $setting => $filename ) {
            if ( isset( $sitemap_settings[$setting] ) && $sitemap_settings[$setting] ) {
                $urls[] = $site_url . '/' . $filename;
            }
        }
        
        return array_unique( $urls );
    }

    /**
     * Génère un message approprié selon la réponse du ping.
     *
     * @since    1.2.0
     * @param    string    $engine           Le moteur de recherche.
     * @param    int       $response_code    Le code de réponse HTTP.
     * @param    string    $response_body    Le corps de la réponse.
     * @return   string                      Le message.
     */
    private function get_ping_message( $engine, $response_code, $response_body ) {
        switch ( $response_code ) {
            case 200:
            case 201:
            case 202:
            case 204:
                return sprintf( __( 'Ping %s réussi', 'boss-seo' ), $this->ping_services[$engine]['name'] );
                
            case 400:
                return sprintf( __( 'Erreur %s: Requête invalide', 'boss-seo' ), $this->ping_services[$engine]['name'] );
                
            case 403:
                return sprintf( __( 'Erreur %s: Accès refusé', 'boss-seo' ), $this->ping_services[$engine]['name'] );
                
            case 404:
                return sprintf( __( 'Erreur %s: Service non trouvé', 'boss-seo' ), $this->ping_services[$engine]['name'] );
                
            case 429:
                return sprintf( __( 'Erreur %s: Trop de requêtes', 'boss-seo' ), $this->ping_services[$engine]['name'] );
                
            case 500:
            case 502:
            case 503:
                return sprintf( __( 'Erreur %s: Problème serveur', 'boss-seo' ), $this->ping_services[$engine]['name'] );
                
            default:
                return sprintf( 
                    __( 'Ping %s échoué (Code: %d)', 'boss-seo' ), 
                    $this->ping_services[$engine]['name'], 
                    $response_code 
                );
        }
    }

    /**
     * Enregistre l'historique des pings.
     *
     * @since    1.2.0
     * @param    array    $results    Les résultats du ping.
     */
    private function log_ping_history( $results ) {
        $history = get_option( 'boss_seo_ping_history', array() );
        
        // Ajouter le nouvel enregistrement
        $history[] = array(
            'timestamp' => current_time( 'mysql' ),
            'results' => $results,
            'total_sitemaps' => count( $this->get_sitemap_urls() )
        );
        
        // Garder seulement les 50 derniers enregistrements
        if ( count( $history ) > 50 ) {
            $history = array_slice( $history, -50 );
        }
        
        update_option( 'boss_seo_ping_history', $history );
    }

    /**
     * Récupère l'historique des pings.
     *
     * @since    1.2.0
     * @param    int    $limit    Nombre d'enregistrements à retourner.
     * @return   array            L'historique des pings.
     */
    public function get_ping_history( $limit = 10 ) {
        $history = get_option( 'boss_seo_ping_history', array() );
        
        // Retourner les plus récents en premier
        $history = array_reverse( $history );
        
        if ( $limit > 0 ) {
            $history = array_slice( $history, 0, $limit );
        }
        
        return $history;
    }

    /**
     * Vérifie si le ping automatique est activé.
     *
     * @since    1.2.0
     * @return   bool    True si activé.
     */
    public function is_auto_ping_enabled() {
        $sitemap_settings = get_option( 'boss-seo_advanced_sitemap_settings', array() );
        return isset( $sitemap_settings['autoPing'] ) && $sitemap_settings['autoPing'];
    }

    /**
     * Ping automatique après génération de sitemap.
     *
     * @since    1.2.0
     */
    public function auto_ping_after_generation() {
        if ( $this->is_auto_ping_enabled() ) {
            // Ping en arrière-plan pour éviter de ralentir la génération
            wp_schedule_single_event( time() + 30, 'boss_seo_auto_ping_engines' );
        }
    }
}
?>
