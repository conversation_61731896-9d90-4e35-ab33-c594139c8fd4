<?php
/**
 * API REST pour le module Audit SEO assisté par IA
 *
 * @link       https://boss-seo.com
 * @since      1.0.0
 *
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes
 */

/**
 * Classe API REST pour l'audit SEO assisté par IA.
 *
 * Cette classe définit toutes les routes API nécessaires pour le module
 * d'audit SEO avec assistance IA.
 *
 * @since      1.0.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes
 * <AUTHOR> SEO Team
 */
class Boss_Audit_IA_API {

    /**
     * Instance du service IA.
     *
     * @since    1.0.0
     * @access   private
     * @var      Boss_Seo_AI_Service    $ai_service    Instance du service IA.
     */
    private $ai_service;

    /**
     * Instance du service PageSpeed.
     *
     * @since    1.0.0
     * @access   private
     * @var      Boss_Seo_PageSpeed_Service    $pagespeed_service    Instance du service PageSpeed.
     */
    private $pagespeed_service;

    /**
     * Namespace de l'API REST.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $namespace    Namespace de l'API.
     */
    private $namespace = 'boss-seo/v1';

    /**
     * Initialiser la classe et définir ses propriétés.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Charger les services existants
        $this->load_services();
    }

    /**
     * Charger les services nécessaires.
     *
     * @since    1.0.0
     */
    private function load_services() {
        // Charger le service IA existant
        if ( ! class_exists( 'Boss_AI_Service' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-ai-service.php';
        }

        // Charger le service PageSpeed existant
        if ( ! class_exists( 'Boss_PageSpeed_Manager' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-pagespeed-manager.php';
        }

        try {
            $this->ai_service = new Boss_AI_Service( 'boss-seo', '1.2.0' );
            $this->pagespeed_service = new Boss_PageSpeed_Manager();
        } catch ( Exception $e ) {
            error_log( 'Boss SEO Audit IA: Erreur lors du chargement des services: ' . $e->getMessage() );
        }
    }

    /**
     * Enregistrer les routes de l'API REST.
     *
     * @since    1.0.0
     */
    public function register_rest_routes() {
        // Route pour vérifier la configuration IA
        register_rest_route( $this->namespace, '/ai/check', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'check_ai_configuration' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour récupérer les pages disponibles
        register_rest_route( $this->namespace, '/audit/pages', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_available_pages' ),
            'permission_callback' => array( $this, 'check_permissions' ),
        ) );

        // Route pour effectuer un audit
        register_rest_route( $this->namespace, '/audit/perform', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'perform_audit' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'url' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'format'            => 'uri',
                    'sanitize_callback' => 'esc_url_raw',
                    'validate_callback' => array( $this, 'validate_url' ),
                ),
                'post_id' => array(
                    'required'          => false,
                    'type'              => 'integer',
                    'minimum'           => 1,
                    'sanitize_callback' => 'absint',
                ),
                'mode' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'novice',
                    'enum'              => array( 'novice', 'expert' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'useAI' => array(
                    'required'          => false,
                    'type'              => 'boolean',
                    'default'           => true,
                ),
                'checks' => array(
                    'required'          => false,
                    'type'              => 'array',
                    'default'           => array(
                        'meta_tags',
                        'content_quality',
                        'technical_seo',
                        'performance',
                        'accessibility',
                        'images',
                        'links',
                        'structure',
                        'keywords',
                        'schema'
                    ),
                    'items'             => array(
                        'type' => 'string',
                        'enum' => array(
                            'meta_tags',
                            'content_quality',
                            'technical_seo',
                            'performance',
                            'accessibility',
                            'images',
                            'links',
                            'structure',
                            'keywords',
                            'schema'
                        ),
                    ),
                ),
            ),
        ) );

        // Route pour récupérer l'historique des audits
        register_rest_route( $this->namespace, '/audit/history', array(
            'methods'             => WP_REST_Server::READABLE,
            'callback'            => array( $this, 'get_audit_history' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'limit' => array(
                    'required'          => false,
                    'type'              => 'integer',
                    'default'           => 20,
                    'minimum'           => 1,
                    'maximum'           => 100,
                    'sanitize_callback' => 'absint',
                ),
                'offset' => array(
                    'required'          => false,
                    'type'              => 'integer',
                    'default'           => 0,
                    'minimum'           => 0,
                    'sanitize_callback' => 'absint',
                ),
            ),
        ) );

        // Route pour sauvegarder un audit
        register_rest_route( $this->namespace, '/audit/save', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'save_audit' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'url' => array(
                    'required'          => true,
                    'type'              => 'string',
                    'format'            => 'uri',
                    'sanitize_callback' => 'esc_url_raw',
                ),
                'globalScore' => array(
                    'required'          => true,
                    'type'              => 'integer',
                    'minimum'           => 0,
                    'maximum'           => 100,
                    'sanitize_callback' => 'absint',
                ),
                'errors' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'performance' => array(
                    'required'          => false,
                    'type'              => 'object',
                ),
                'metadata' => array(
                    'required'          => false,
                    'type'              => 'object',
                ),
            ),
        ) );

        // Route pour obtenir une explication IA
        register_rest_route( $this->namespace, '/ai/explain', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'get_ai_explanation' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'error' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'language' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'fr',
                    'enum'              => array( 'fr', 'en', 'es', 'de', 'it' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'mode' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'explanation',
                    'enum'              => array( 'explanation', 'detailed' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ) );

        // Route pour obtenir une suggestion de correction IA
        register_rest_route( $this->namespace, '/ai/correct', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'get_ai_correction' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'error' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'language' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'fr',
                    'enum'              => array( 'fr', 'en', 'es', 'de', 'it' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'mode' => array(
                    'required'          => false,
                    'type'              => 'string',
                    'default'           => 'correction',
                    'enum'              => array( 'correction', 'advanced' ),
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ) );

        // Route pour appliquer une correction automatique
        register_rest_route( $this->namespace, '/audit/apply-fix', array(
            'methods'             => WP_REST_Server::CREATABLE,
            'callback'            => array( $this, 'apply_automatic_fix' ),
            'permission_callback' => array( $this, 'check_permissions' ),
            'args'                => array(
                'error' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'suggestion' => array(
                    'required'          => true,
                    'type'              => 'object',
                ),
                'post_id' => array(
                    'required'          => false,
                    'type'              => 'integer',
                    'sanitize_callback' => 'absint',
                ),
            ),
        ) );
    }

    /**
     * Vérifier les permissions pour l'API.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   bool                          True si l'utilisateur a les permissions.
     */
    public function check_permissions( $request ) {
        return current_user_can( 'manage_options' );
    }

    /**
     * Valider une URL.
     *
     * @since    1.0.0
     * @param    string             $param      URL à valider.
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @param    string             $key        Clé du paramètre.
     * @return   bool                          True si l'URL est valide.
     */
    public function validate_url( $param, $request, $key ) {
        return filter_var( $param, FILTER_VALIDATE_URL ) !== false;
    }

    /**
     * Vérifier la configuration IA.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function check_ai_configuration( $request ) {
        try {
            if ( ! $this->ai_service ) {
                throw new Exception( __( 'Service IA non disponible.', 'boss-seo' ) );
            }

            $is_configured = $this->ai_service->is_configured();
            $is_available = $this->ai_service->is_available();

            return new WP_REST_Response( array(
                'success' => true,
                'data'    => array(
                    'ai_available' => $is_available,
                    'providers'    => array( $this->ai_service->get_current_model() ),
                    'message'      => $is_available ?
                        __( 'IA configurée et disponible.', 'boss-seo' ) :
                        __( 'IA non configurée. Veuillez configurer vos clés API.', 'boss-seo' )
                ),
                'message' => __( 'Configuration IA vérifiée avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data'    => array(
                    'ai_available' => false,
                    'providers'    => array(),
                    'message'      => $e->getMessage()
                ),
                'message' => __( 'Erreur lors de la vérification de la configuration IA.', 'boss-seo' )
            ), 500 );
        }
    }

    /**
     * Récupérer les pages disponibles pour l'audit.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_available_pages( $request ) {
        try {
            $pages = array();

            // Récupérer les pages publiées
            $published_pages = get_posts( array(
                'post_type'      => array( 'page', 'post' ),
                'post_status'    => 'publish',
                'posts_per_page' => 50,
                'orderby'        => 'title',
                'order'          => 'ASC'
            ) );

            foreach ( $published_pages as $page ) {
                $pages[] = array(
                    'id'    => $page->ID,
                    'title' => $page->post_title,
                    'url'   => get_permalink( $page->ID ),
                    'type'  => $page->post_type
                );
            }

            // Ajouter la page d'accueil
            array_unshift( $pages, array(
                'id'    => 0,
                'title' => __( 'Page d\'accueil', 'boss-seo' ),
                'url'   => home_url(),
                'type'  => 'home'
            ) );

            return new WP_REST_Response( array(
                'success' => true,
                'data'    => array(
                    'pages' => $pages,
                    'total' => count( $pages )
                ),
                'message' => __( 'Pages récupérées avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data'    => array(
                    'pages' => array(),
                    'total' => 0
                ),
                'message' => __( 'Erreur lors de la récupération des pages.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Effectuer un audit SEO complet.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function perform_audit( $request ) {
        $url = $request->get_param( 'url' );
        $post_id = $request->get_param( 'post_id' );
        $mode = $request->get_param( 'mode' );
        $use_ai = $request->get_param( 'useAI' );
        $checks = $request->get_param( 'checks' );

        // Validation : il faut soit une URL soit un post_id
        if ( empty( $url ) && empty( $post_id ) ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data' => null,
                'message' => __( 'Vous devez fournir soit une URL soit un post_id.', 'boss-seo' )
            ), 400 );
        }

        // Si on a un post_id, récupérer l'URL
        if ( ! empty( $post_id ) ) {
            $url = get_permalink( $post_id );
            if ( ! $url ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'data' => null,
                    'message' => __( 'Impossible de récupérer l\'URL pour ce post_id.', 'boss-seo' )
                ), 400 );
            }
        }

        try {
            // Initialiser les résultats d'audit
            $audit_results = array(
                'url' => $url,
                'date' => current_time( 'mysql' ),
                'mode' => $mode,
                'errors' => array(
                    'meta_tags' => array(),
                    'content_quality' => array(),
                    'technical_seo' => array(),
                    'performance' => array(),
                    'accessibility' => array(),
                    'images' => array(),
                    'links' => array(),
                    'structure' => array(),
                    'keywords' => array(),
                    'schema' => array()
                ),
                'performance_data' => array(),
                'metadata' => array()
            );

            // Récupérer le contenu de la page
            $page_content = $this->fetch_page_content( $url );
            if ( is_wp_error( $page_content ) ) {
                throw new Exception( $page_content->get_error_message() );
            }

            // Effectuer les différents types de vérifications
            foreach ( $checks as $check_type ) {
                switch ( $check_type ) {
                    case 'meta_tags':
                        $audit_results['errors']['meta_tags'] = $this->check_meta_tags( $page_content, $url );
                        break;
                    case 'content_quality':
                        $audit_results['errors']['content_quality'] = $this->check_content_quality( $page_content, $mode );
                        break;
                    case 'technical_seo':
                        $audit_results['errors']['technical_seo'] = $this->check_technical_seo( $page_content, $url );
                        break;
                    case 'performance':
                        $performance_data = $this->check_performance( $url );
                        $audit_results['performance_data'] = $performance_data['data'];
                        $audit_results['errors']['performance'] = $performance_data['errors'];
                        break;
                    case 'accessibility':
                        $audit_results['errors']['accessibility'] = $this->check_accessibility( $page_content );
                        break;
                    case 'images':
                        $audit_results['errors']['images'] = $this->check_images( $page_content );
                        break;
                    case 'links':
                        $audit_results['errors']['links'] = $this->check_links( $page_content, $url );
                        break;
                    case 'structure':
                        $audit_results['errors']['structure'] = $this->check_structure( $page_content );
                        break;
                    case 'keywords':
                        $audit_results['errors']['keywords'] = $this->check_keywords( $page_content, $url );
                        break;
                    case 'schema':
                        $audit_results['errors']['schema'] = $this->check_schema( $page_content, $url );
                        break;
                }
            }

            // Extraire les métadonnées
            $audit_results['metadata'] = $this->extract_metadata( $page_content );

            // Calculer le score global
            $global_score = $this->calculate_global_score( $audit_results['errors'] );

            // Restructurer les erreurs par sévérité pour le frontend
            $structured_errors = $this->structure_errors_by_severity( $audit_results['errors'] );

            // Préparer les données de performance
            $performance_data = array(
                'load_time' => $audit_results['performance_data']['load_time'] ?? 0,
                'page_size' => $audit_results['performance_data']['page_size'] ?? 0,
                'requests' => $audit_results['performance_data']['requests'] ?? 0,
                'content_length' => $audit_results['performance_data']['content_length'] ?? 0,
                'images_count' => $audit_results['performance_data']['images_count'] ?? 0,
                'links_count' => $audit_results['performance_data']['links_count'] ?? 0
            );

            // Préparer la réponse finale
            $final_data = array(
                'url' => $url,
                'post_id' => $post_id,
                'date' => $audit_results['date'],
                'globalScore' => $global_score,
                'errors' => $structured_errors,
                'performance' => $performance_data,
                'metadata' => $audit_results['metadata']
            );

            // Sauvegarder automatiquement l'audit
            $this->save_audit_to_database( $final_data );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => $final_data,
                'message' => __( 'Audit effectué avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data' => null,
                'message' => __( 'Erreur lors de l\'audit SEO.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Récupérer le contenu d'une page.
     *
     * @since    1.0.0
     * @param    string    $url    URL de la page.
     * @return   string|WP_Error   Contenu HTML ou erreur.
     */
    private function fetch_page_content( $url ) {
        $response = wp_remote_get( $url, array(
            'timeout' => 30,
            'user-agent' => 'Boss SEO Audit Bot/1.0'
        ) );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        if ( $status_code !== 200 ) {
            return new WP_Error( 'http_error', sprintf( __( 'Erreur HTTP %d lors de la récupération de la page.', 'boss-seo' ), $status_code ) );
        }

        return wp_remote_retrieve_body( $response );
    }

    /**
     * Restructurer les erreurs par sévérité.
     *
     * @since    1.0.0
     * @param    array    $errors_by_category    Erreurs groupées par catégorie.
     * @return   array                          Erreurs groupées par sévérité.
     */
    private function structure_errors_by_severity( $errors_by_category ) {
        $structured = array(
            'critical' => array(),
            'medium' => array(),
            'low' => array()
        );

        foreach ( $errors_by_category as $category => $errors ) {
            foreach ( $errors as $error ) {
                $severity = $error['severity'] ?? 'low';

                // Ajouter la catégorie à l'erreur
                $error['category'] = $category;

                // Mapper les sévérités
                switch ( $severity ) {
                    case 'critical':
                        $structured['critical'][] = $error;
                        break;
                    case 'medium':
                    case 'warning':
                        $structured['medium'][] = $error;
                        break;
                    case 'low':
                    case 'info':
                    default:
                        $structured['low'][] = $error;
                        break;
                }
            }
        }

        return $structured;
    }

    /**
     * Analyser le contenu de la page pour obtenir des statistiques.
     *
     * @since    1.0.0
     * @param    string    $url    URL à analyser.
     * @return   array             Statistiques du contenu.
     */
    private function analyze_page_content( $url ) {
        try {
            // Récupérer le contenu de la page
            $response = wp_remote_get( $url, array(
                'timeout' => 30,
                'user-agent' => 'Boss SEO Audit Bot'
            ) );

            if ( is_wp_error( $response ) ) {
                throw new Exception( $response->get_error_message() );
            }

            $content = wp_remote_retrieve_body( $response );
            $dom = new DOMDocument();
            @$dom->loadHTML( $content );

            // Extraire le texte visible
            $text_content = $this->extract_text_content( $dom );
            $word_count = str_word_count( $text_content );

            // Compter les images
            $images = $dom->getElementsByTagName( 'img' );
            $images_count = $images->length;

            // Compter les liens
            $links = $dom->getElementsByTagName( 'a' );
            $links_count = $links->length;

            return array(
                'content_length' => $word_count,
                'images_count' => $images_count,
                'links_count' => $links_count
            );

        } catch ( Exception $e ) {
            return array(
                'content_length' => 0,
                'images_count' => 0,
                'links_count' => 0
            );
        }
    }

    /**
     * Vérifier les balises meta.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @param    string    $url        URL de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_meta_tags( $content, $url ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        // Vérifier le titre
        $title_tags = $dom->getElementsByTagName( 'title' );
        if ( $title_tags->length === 0 ) {
            $errors[] = array(
                'id' => 'title-missing',
                'title' => __( 'Balise title manquante', 'boss-seo' ),
                'description' => __( 'La page n\'a pas de balise title, ce qui est critique pour le SEO.', 'boss-seo' ),
                'severity' => 'critical',
                'location' => '<head>',
                'elements' => array( 'title' ),
                'currentValue' => null,
                'recommendedValue' => __( 'Un titre de 50-60 caractères décrivant le contenu de la page', 'boss-seo' )
            );
        } else {
            $title = $title_tags->item( 0 )->textContent;
            $title_length = strlen( $title );

            if ( $title_length < 30 ) {
                $errors[] = array(
                    'id' => 'title-too-short',
                    'title' => __( 'Titre trop court', 'boss-seo' ),
                    'description' => __( 'Le titre de la page est plus court que la longueur recommandée.', 'boss-seo' ),
                    'severity' => 'medium',
                    'location' => '<head>',
                    'elements' => array( 'title' ),
                    'currentValue' => sprintf( __( '%d caractères', 'boss-seo' ), $title_length ),
                    'recommendedValue' => __( '50-60 caractères', 'boss-seo' )
                );
            } elseif ( $title_length > 60 ) {
                $errors[] = array(
                    'id' => 'title-too-long',
                    'title' => __( 'Titre trop long', 'boss-seo' ),
                    'description' => __( 'Le titre de la page est plus long que la longueur recommandée et pourrait être tronqué dans les résultats de recherche.', 'boss-seo' ),
                    'severity' => 'medium',
                    'location' => '<head>',
                    'elements' => array( 'title' ),
                    'currentValue' => sprintf( __( '%d caractères', 'boss-seo' ), $title_length ),
                    'recommendedValue' => __( '50-60 caractères', 'boss-seo' )
                );
            }
        }

        // Vérifier la méta description
        $meta_description = null;
        $meta_tags = $dom->getElementsByTagName( 'meta' );
        foreach ( $meta_tags as $meta ) {
            if ( $meta->getAttribute( 'name' ) === 'description' ) {
                $meta_description = $meta->getAttribute( 'content' );
                break;
            }
        }

        if ( empty( $meta_description ) ) {
            $errors[] = array(
                'id' => 'meta-description-missing',
                'title' => __( 'Méta description manquante', 'boss-seo' ),
                'description' => __( 'Cette page n\'a pas de méta description, ce qui peut affecter son apparence dans les résultats de recherche.', 'boss-seo' ),
                'severity' => 'critical',
                'location' => '<head>',
                'elements' => array( 'meta[name="description"]' ),
                'currentValue' => null,
                'recommendedValue' => __( 'Une description de 150-160 caractères décrivant le contenu de la page', 'boss-seo' )
            );
        } else {
            $desc_length = strlen( $meta_description );
            if ( $desc_length < 120 ) {
                $errors[] = array(
                    'id' => 'meta-description-too-short',
                    'title' => __( 'Méta description trop courte', 'boss-seo' ),
                    'description' => __( 'La méta description est plus courte que la longueur recommandée.', 'boss-seo' ),
                    'severity' => 'medium',
                    'location' => '<head>',
                    'elements' => array( 'meta[name="description"]' ),
                    'currentValue' => sprintf( __( '%d caractères', 'boss-seo' ), $desc_length ),
                    'recommendedValue' => __( '150-160 caractères', 'boss-seo' )
                );
            } elseif ( $desc_length > 160 ) {
                $errors[] = array(
                    'id' => 'meta-description-too-long',
                    'title' => __( 'Méta description trop longue', 'boss-seo' ),
                    'description' => __( 'La méta description est plus longue que la longueur recommandée et pourrait être tronquée.', 'boss-seo' ),
                    'severity' => 'medium',
                    'location' => '<head>',
                    'elements' => array( 'meta[name="description"]' ),
                    'currentValue' => sprintf( __( '%d caractères', 'boss-seo' ), $desc_length ),
                    'recommendedValue' => __( '150-160 caractères', 'boss-seo' )
                );
            }
        }

        // Vérifier les balises Open Graph
        $has_og_title = false;
        $has_og_description = false;
        $has_og_image = false;

        foreach ( $meta_tags as $meta ) {
            $property = $meta->getAttribute( 'property' );
            if ( $property === 'og:title' ) $has_og_title = true;
            if ( $property === 'og:description' ) $has_og_description = true;
            if ( $property === 'og:image' ) $has_og_image = true;
        }

        if ( ! $has_og_title || ! $has_og_description || ! $has_og_image ) {
            $missing_og = array();
            if ( ! $has_og_title ) $missing_og[] = 'og:title';
            if ( ! $has_og_description ) $missing_og[] = 'og:description';
            if ( ! $has_og_image ) $missing_og[] = 'og:image';

            $errors[] = array(
                'id' => 'open-graph-missing',
                'title' => __( 'Balises Open Graph manquantes', 'boss-seo' ),
                'description' => sprintf( __( 'Les balises Open Graph suivantes sont manquantes : %s', 'boss-seo' ), implode( ', ', $missing_og ) ),
                'severity' => 'low',
                'location' => '<head>',
                'elements' => $missing_og,
                'currentValue' => sprintf( __( '%d balises manquantes', 'boss-seo' ), count( $missing_og ) ),
                'recommendedValue' => __( 'Toutes les balises Open Graph essentielles présentes', 'boss-seo' )
            );
        }

        return $errors;
    }

    /**
     * Vérifier la qualité du contenu.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @param    string    $mode       Mode d'analyse (novice/expert).
     * @return   array                 Erreurs détectées.
     */
    private function check_content_quality( $content, $mode ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        // Extraire le texte visible
        $text_content = $this->extract_text_content( $dom );
        $word_count = str_word_count( $text_content );

        // Vérifier la longueur du contenu
        if ( $word_count < 300 ) {
            $errors[] = array(
                'id' => 'content-too-short',
                'title' => __( 'Contenu trop court', 'boss-seo' ),
                'description' => __( 'Le contenu de la page est trop court pour être bien référencé.', 'boss-seo' ),
                'severity' => 'medium',
                'location' => 'body',
                'elements' => array( 'body' ),
                'currentValue' => sprintf( __( '%d mots', 'boss-seo' ), $word_count ),
                'recommendedValue' => __( 'Au moins 300 mots', 'boss-seo' )
            );
        }

        // Vérifier la duplication de contenu (simulation)
        if ( $mode === 'expert' ) {
            $duplicate_percentage = $this->check_content_duplication( $text_content );
            if ( $duplicate_percentage > 30 ) {
                $errors[] = array(
                    'id' => 'content-duplication',
                    'title' => __( 'Contenu dupliqué détecté', 'boss-seo' ),
                    'description' => __( 'Une partie importante du contenu semble être dupliquée.', 'boss-seo' ),
                    'severity' => 'critical',
                    'location' => 'body',
                    'elements' => array( 'body' ),
                    'currentValue' => sprintf( __( '%d%% de duplication', 'boss-seo' ), $duplicate_percentage ),
                    'recommendedValue' => __( 'Moins de 30% de duplication', 'boss-seo' )
                );
            }
        }

        return $errors;
    }

    /**
     * Vérifier la structure HTML.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_structure( $content ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        // Vérifier les balises H1
        $h1_tags = $dom->getElementsByTagName( 'h1' );
        if ( $h1_tags->length === 0 ) {
            $errors[] = array(
                'id' => 'h1-missing',
                'title' => __( 'Balise H1 manquante', 'boss-seo' ),
                'description' => __( 'La page n\'a pas de balise H1, ce qui est important pour la structure SEO.', 'boss-seo' ),
                'severity' => 'critical',
                'location' => 'body',
                'elements' => array( 'h1' ),
                'currentValue' => __( 'Aucune balise H1', 'boss-seo' ),
                'recommendedValue' => __( 'Une balise H1 unique et descriptive', 'boss-seo' )
            );
        } elseif ( $h1_tags->length > 1 ) {
            $errors[] = array(
                'id' => 'h1-multiple',
                'title' => __( 'Plusieurs balises H1 détectées', 'boss-seo' ),
                'description' => __( 'Cette page contient plusieurs balises H1, ce qui peut créer de la confusion pour les moteurs de recherche.', 'boss-seo' ),
                'severity' => 'medium',
                'location' => 'body',
                'elements' => array( 'h1' ),
                'currentValue' => sprintf( __( '%d balises H1', 'boss-seo' ), $h1_tags->length ),
                'recommendedValue' => __( '1 seule balise H1 par page', 'boss-seo' )
            );
        }

        // Vérifier la hiérarchie des titres
        $heading_hierarchy = $this->check_heading_hierarchy( $dom );
        if ( ! empty( $heading_hierarchy ) ) {
            $errors[] = array(
                'id' => 'heading-hierarchy-broken',
                'title' => __( 'Hiérarchie des titres incorrecte', 'boss-seo' ),
                'description' => __( 'La hiérarchie des balises de titre (H1, H2, H3...) n\'est pas respectée.', 'boss-seo' ),
                'severity' => 'low',
                'location' => 'body',
                'elements' => $heading_hierarchy,
                'currentValue' => sprintf( __( '%d problèmes de hiérarchie', 'boss-seo' ), count( $heading_hierarchy ) ),
                'recommendedValue' => __( 'Hiérarchie logique H1 > H2 > H3...', 'boss-seo' )
            );
        }

        return $errors;
    }

    /**
     * Vérifier les images.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_images( $content ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        $images = $dom->getElementsByTagName( 'img' );
        $images_without_alt = array();
        $images_without_title = array();
        $large_images = array();

        foreach ( $images as $img ) {
            $src = $img->getAttribute( 'src' );
            $alt = $img->getAttribute( 'alt' );
            $title = $img->getAttribute( 'title' );

            // Vérifier l'attribut alt
            if ( empty( $alt ) ) {
                $images_without_alt[] = $src;
            }

            // Vérifier l'attribut title (mode expert)
            if ( empty( $title ) ) {
                $images_without_title[] = $src;
            }

            // Vérifier la taille des images (simulation)
            if ( $this->is_large_image( $src ) ) {
                $large_images[] = $src;
            }
        }

        // Erreurs pour les attributs alt manquants
        if ( ! empty( $images_without_alt ) ) {
            $errors[] = array(
                'id' => 'images-alt-missing',
                'title' => __( 'Images sans attribut alt', 'boss-seo' ),
                'description' => __( 'Certaines images n\'ont pas d\'attribut alt, ce qui nuit à l\'accessibilité et au SEO.', 'boss-seo' ),
                'severity' => 'medium',
                'location' => 'body',
                'elements' => array_slice( $images_without_alt, 0, 5 ), // Limiter à 5 exemples
                'currentValue' => sprintf( __( '%d images sans alt', 'boss-seo' ), count( $images_without_alt ) ),
                'recommendedValue' => __( 'Toutes les images doivent avoir un attribut alt descriptif', 'boss-seo' )
            );
        }

        // Erreurs pour les images trop lourdes
        if ( ! empty( $large_images ) ) {
            $errors[] = array(
                'id' => 'images-too-large',
                'title' => __( 'Images trop volumineuses', 'boss-seo' ),
                'description' => __( 'Certaines images sont trop volumineuses et peuvent ralentir le chargement de la page.', 'boss-seo' ),
                'severity' => 'low',
                'location' => 'body',
                'elements' => array_slice( $large_images, 0, 3 ),
                'currentValue' => sprintf( __( '%d images volumineuses', 'boss-seo' ), count( $large_images ) ),
                'recommendedValue' => __( 'Optimiser les images pour le web', 'boss-seo' )
            );
        }

        return $errors;
    }

    /**
     * Vérifier les liens.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @param    string    $url        URL de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_links( $content, $url ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        $links = $dom->getElementsByTagName( 'a' );
        $broken_links = array();
        $external_links_without_nofollow = array();

        foreach ( $links as $link ) {
            $href = $link->getAttribute( 'href' );
            $rel = $link->getAttribute( 'rel' );

            if ( empty( $href ) || $href === '#' ) {
                continue;
            }

            // Vérifier les liens externes sans nofollow
            if ( $this->is_external_link( $href, $url ) ) {
                if ( strpos( $rel, 'nofollow' ) === false ) {
                    $external_links_without_nofollow[] = $href;
                }
            }

            // Vérifier les liens cassés (simulation - dans la vraie implémentation, on ferait des requêtes HTTP)
            if ( $this->is_broken_link( $href ) ) {
                $broken_links[] = $href;
            }
        }

        // Erreurs pour les liens cassés
        if ( ! empty( $broken_links ) ) {
            $errors[] = array(
                'id' => 'broken-links',
                'title' => __( 'Liens cassés détectés', 'boss-seo' ),
                'description' => __( 'Certains liens de la page ne fonctionnent pas.', 'boss-seo' ),
                'severity' => 'medium',
                'location' => 'body',
                'elements' => array_slice( $broken_links, 0, 5 ),
                'currentValue' => sprintf( __( '%d liens cassés', 'boss-seo' ), count( $broken_links ) ),
                'recommendedValue' => __( 'Tous les liens doivent être fonctionnels', 'boss-seo' )
            );
        }

        return $errors;
    }

    /**
     * Vérifier les aspects techniques SEO.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @param    string    $url        URL de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_technical_seo( $content, $url ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        // Vérifier HTTPS
        if ( strpos( $url, 'https://' ) !== 0 ) {
            $errors[] = array(
                'id' => 'no-https',
                'title' => __( 'Site non sécurisé (HTTP)', 'boss-seo' ),
                'description' => __( 'Le site n\'utilise pas HTTPS, ce qui peut affecter le référencement et la confiance des utilisateurs.', 'boss-seo' ),
                'severity' => 'critical',
                'location' => 'URL',
                'elements' => array( $url ),
                'currentValue' => 'HTTP',
                'recommendedValue' => 'HTTPS'
            );
        }

        // Vérifier la balise canonical
        $canonical_found = false;
        $link_tags = $dom->getElementsByTagName( 'link' );
        foreach ( $link_tags as $link ) {
            if ( $link->getAttribute( 'rel' ) === 'canonical' ) {
                $canonical_found = true;
                break;
            }
        }

        if ( ! $canonical_found ) {
            $errors[] = array(
                'id' => 'canonical-missing',
                'title' => __( 'Balise canonical manquante', 'boss-seo' ),
                'description' => __( 'La page n\'a pas de balise canonical, ce qui peut causer des problèmes de contenu dupliqué.', 'boss-seo' ),
                'severity' => 'medium',
                'location' => '<head>',
                'elements' => array( 'link[rel="canonical"]' ),
                'currentValue' => __( 'Aucune balise canonical', 'boss-seo' ),
                'recommendedValue' => __( 'Balise canonical pointant vers l\'URL principale', 'boss-seo' )
            );
        }

        return $errors;
    }

    /**
     * Vérifier l'accessibilité.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_accessibility( $content ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        // Vérifier l'attribut lang
        $html_tags = $dom->getElementsByTagName( 'html' );
        if ( $html_tags->length > 0 ) {
            $lang = $html_tags->item( 0 )->getAttribute( 'lang' );
            if ( empty( $lang ) ) {
                $errors[] = array(
                    'id' => 'lang-missing',
                    'title' => __( 'Attribut lang manquant', 'boss-seo' ),
                    'description' => __( 'La balise HTML n\'a pas d\'attribut lang, ce qui nuit à l\'accessibilité.', 'boss-seo' ),
                    'severity' => 'low',
                    'location' => '<html>',
                    'elements' => array( 'html' ),
                    'currentValue' => __( 'Aucun attribut lang', 'boss-seo' ),
                    'recommendedValue' => __( 'lang="fr" ou autre code de langue approprié', 'boss-seo' )
                );
            }
        }

        return $errors;
    }

    /**
     * Vérifier les performances avec PageSpeed API.
     *
     * @since    1.0.0
     * @param    string    $url    URL à analyser.
     * @return   array             Données de performance et erreurs.
     */
    private function check_performance( $url ) {
        try {
            if ( ! $this->pagespeed_service ) {
                throw new Exception( __( 'Service PageSpeed non disponible.', 'boss-seo' ) );
            }

            // Utiliser le service PageSpeed existant
            $performance_data = $this->pagespeed_service->analyze_url( $url, 'mobile', array( 'performance' ) );

            if ( ! $performance_data ) {
                throw new Exception( __( 'Échec de l\'analyse PageSpeed.', 'boss-seo' ) );
            }

            $errors = array();

            // Extraire les données de performance
            $performance_score = isset( $performance_data['scores']['performance']['score'] ) ?
                $performance_data['scores']['performance']['score'] : 0;

            $core_web_vitals = isset( $performance_data['core_web_vitals'] ) ?
                $performance_data['core_web_vitals'] : array();

            // Analyser le contenu pour obtenir des statistiques supplémentaires
            $content_stats = $this->analyze_page_content( $url );

            $data = array(
                'score' => $performance_score,
                'load_time' => isset( $core_web_vitals['lcp']['value'] ) ? $core_web_vitals['lcp']['value'] : 0,
                'page_size' => isset( $performance_data['page_size'] ) ? $performance_data['page_size'] : 0,
                'requests' => isset( $performance_data['requests'] ) ? $performance_data['requests'] : 0,
                'content_length' => $content_stats['content_length'],
                'images_count' => $content_stats['images_count'],
                'links_count' => $content_stats['links_count'],
                'firstContentfulPaint' => isset( $core_web_vitals['fcp']['value'] ) ? $core_web_vitals['fcp']['value'] : 0,
                'largestContentfulPaint' => isset( $core_web_vitals['lcp']['value'] ) ? $core_web_vitals['lcp']['value'] : 0
            );

            // Analyser les métriques de performance
            if ( $performance_score < 50 ) {
                $errors[] = array(
                    'id' => 'performance-poor',
                    'title' => __( 'Performance faible', 'boss-seo' ),
                    'description' => __( 'Le score de performance de la page est faible selon PageSpeed Insights.', 'boss-seo' ),
                    'severity' => 'critical',
                    'location' => 'Performance',
                    'elements' => array( 'Page entière' ),
                    'currentValue' => sprintf( __( 'Score: %d/100', 'boss-seo' ), $performance_score ),
                    'recommendedValue' => __( 'Score supérieur à 90', 'boss-seo' )
                );
            }

            // Vérifier LCP (Largest Contentful Paint)
            if ( isset( $core_web_vitals['lcp'] ) && $core_web_vitals['lcp']['status'] === 'poor' ) {
                $errors[] = array(
                    'id' => 'lcp-poor',
                    'title' => __( 'LCP trop élevé', 'boss-seo' ),
                    'description' => __( 'Le Largest Contentful Paint est trop élevé, ce qui affecte l\'expérience utilisateur.', 'boss-seo' ),
                    'severity' => 'medium',
                    'location' => 'Performance',
                    'elements' => array( 'Contenu principal' ),
                    'currentValue' => sprintf( __( '%.1f secondes', 'boss-seo' ), $core_web_vitals['lcp']['value'] ),
                    'recommendedValue' => __( 'Moins de 2.5 secondes', 'boss-seo' )
                );
            }

            // Vérifier CLS (Cumulative Layout Shift)
            if ( isset( $core_web_vitals['cls'] ) && $core_web_vitals['cls']['status'] === 'poor' ) {
                $errors[] = array(
                    'id' => 'cls-poor',
                    'title' => __( 'Décalage de mise en page', 'boss-seo' ),
                    'description' => __( 'La page présente des décalages de mise en page qui nuisent à l\'expérience utilisateur.', 'boss-seo' ),
                    'severity' => 'medium',
                    'location' => 'Performance',
                    'elements' => array( 'Mise en page' ),
                    'currentValue' => sprintf( __( '%.3f', 'boss-seo' ), $core_web_vitals['cls']['value'] ),
                    'recommendedValue' => __( 'Moins de 0.1', 'boss-seo' )
                );
            }

            return array(
                'data' => $data,
                'errors' => $errors
            );

        } catch ( Exception $e ) {
            return array(
                'data' => array(
                    'score' => 0,
                    'loadTime' => 0,
                    'firstContentfulPaint' => 0,
                    'largestContentfulPaint' => 0
                ),
                'errors' => array(
                    array(
                        'id' => 'performance-check-failed',
                        'title' => __( 'Vérification de performance échouée', 'boss-seo' ),
                        'description' => sprintf( __( 'Impossible d\'analyser les performances de la page: %s', 'boss-seo' ), $e->getMessage() ),
                        'severity' => 'low',
                        'location' => 'Performance',
                        'elements' => array(),
                        'currentValue' => __( 'Analyse échouée', 'boss-seo' ),
                        'recommendedValue' => __( 'Vérifier la configuration PageSpeed API', 'boss-seo' )
                    )
                )
            );
        }
    }

    /**
     * Extraire les métadonnées de la page.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @return   array                 Métadonnées extraites.
     */
    private function extract_metadata( $content ) {
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        $metadata = array(
            'title' => '',
            'description' => '',
            'keywords' => array(),
            'og_title' => '',
            'og_description' => '',
            'og_image' => ''
        );

        // Extraire le titre
        $title_tags = $dom->getElementsByTagName( 'title' );
        if ( $title_tags->length > 0 ) {
            $metadata['title'] = $title_tags->item( 0 )->textContent;
        }

        // Extraire les balises meta
        $meta_tags = $dom->getElementsByTagName( 'meta' );
        foreach ( $meta_tags as $meta ) {
            $name = $meta->getAttribute( 'name' );
            $property = $meta->getAttribute( 'property' );
            $content_attr = $meta->getAttribute( 'content' );

            if ( $name === 'description' ) {
                $metadata['description'] = $content_attr;
            } elseif ( $name === 'keywords' ) {
                $metadata['keywords'] = array_map( 'trim', explode( ',', $content_attr ) );
            } elseif ( $property === 'og:title' ) {
                $metadata['og_title'] = $content_attr;
            } elseif ( $property === 'og:description' ) {
                $metadata['og_description'] = $content_attr;
            } elseif ( $property === 'og:image' ) {
                $metadata['og_image'] = $content_attr;
            }
        }

        return $metadata;
    }

    /**
     * Calculer le score global SEO.
     *
     * @since    1.0.0
     * @param    array    $errors    Erreurs détectées par catégorie.
     * @return   int                 Score global sur 100.
     */
    private function calculate_global_score( $errors ) {
        $total_weight = 0;

        foreach ( $errors as $category => $category_errors ) {
            foreach ( $category_errors as $error ) {
                switch ( $error['severity'] ) {
                    case 'critical':
                        $total_weight += 15;
                        break;
                    case 'medium':
                        $total_weight += 8;
                        break;
                    case 'low':
                        $total_weight += 3;
                        break;
                }
            }
        }

        return max( 0, 100 - $total_weight );
    }

    /**
     * Obtenir une explication IA pour une erreur.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_ai_explanation( $request ) {
        $error = $request->get_param( 'error' );
        $language = $request->get_param( 'language' );
        $mode = $request->get_param( 'mode' );

        try {
            // Construire le prompt pour l'IA
            $prompt = $this->build_explanation_prompt( $error, $language, $mode );

            // Appeler le service IA existant
            $ai_response = $this->ai_service->generate_content( $prompt, array(
                'max_tokens' => 500,
                'temperature' => 0.7
            ) );

            if ( ! $ai_response['success'] ) {
                throw new Exception( $ai_response['message'] );
            }

            // Parser la réponse IA
            $explanation = $this->parse_ai_explanation( $ai_response['content'] );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array(
                    'explanation' => $explanation
                ),
                'message' => __( 'Explication générée avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data' => null,
                'message' => __( 'Erreur lors de la génération de l\'explication.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Obtenir une suggestion de correction IA.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_ai_correction( $request ) {
        $error = $request->get_param( 'error' );
        $language = $request->get_param( 'language' );
        $mode = $request->get_param( 'mode' );

        try {
            // Construire le prompt pour la correction
            $prompt = $this->build_correction_prompt( $error, $language, $mode );

            // Appeler le service IA existant avec des paramètres pour des réponses courtes
            $ai_response = $this->ai_service->generate_content( $prompt, array(
                'max_tokens' => 300, // Réduit de 800 à 300 pour des réponses plus courtes
                'temperature' => 0.3 // Réduit pour plus de précision
            ) );

            if ( ! $ai_response['success'] ) {
                throw new Exception( $ai_response['message'] );
            }

            // Parser la réponse IA
            $suggestion = $this->parse_ai_correction( $ai_response['content'], $error );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array(
                    'suggestion' => $suggestion
                ),
                'message' => __( 'Suggestion générée avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data' => null,
                'message' => __( 'Erreur lors de la génération de la suggestion.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Récupérer l'historique des audits.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function get_audit_history( $request ) {
        $limit = $request->get_param( 'limit' );
        $offset = $request->get_param( 'offset' );

        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'boss_seo_audits';

            // Vérifier si la table existe
            if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) !== $table_name ) {
                $this->create_audit_table();
            }

            // Récupérer les audits
            $audits = $wpdb->get_results( $wpdb->prepare(
                "SELECT * FROM $table_name ORDER BY date_created DESC LIMIT %d OFFSET %d",
                $limit,
                $offset
            ) );

            // Décoder les données JSON
            foreach ( $audits as &$audit ) {
                $audit->errors = json_decode( $audit->errors, true );
                $audit->performance_data = json_decode( $audit->performance_data, true );
                $audit->metadata = json_decode( $audit->metadata, true );
            }

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array(
                    'history' => $audits,
                    'total' => $wpdb->get_var( "SELECT COUNT(*) FROM $table_name" )
                ),
                'message' => __( 'Historique récupéré avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data' => array(
                    'history' => array(),
                    'total' => 0
                ),
                'message' => __( 'Erreur lors de la récupération de l\'historique.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Sauvegarder un audit.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function save_audit( $request ) {
        $audit_data = array(
            'url' => $request->get_param( 'url' ),
            'globalScore' => $request->get_param( 'globalScore' ),
            'errors' => $request->get_param( 'errors' ),
            'performance' => $request->get_param( 'performance' ),
            'metadata' => $request->get_param( 'metadata' )
        );

        try {
            $audit_id = $this->save_audit_to_database( $audit_data );

            return new WP_REST_Response( array(
                'success' => true,
                'data' => array(
                    'audit_id' => $audit_id
                ),
                'message' => __( 'Audit sauvegardé avec succès.', 'boss-seo' )
            ), 200 );

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data' => null,
                'message' => __( 'Erreur lors de la sauvegarde de l\'audit.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }

    /**
     * Appliquer une correction automatique.
     *
     * @since    1.0.0
     * @param    WP_REST_Request    $request    Requête REST complète.
     * @return   WP_REST_Response               Réponse REST.
     */
    public function apply_automatic_fix( $request ) {
        $error = $request->get_param( 'error' );
        $suggestion = $request->get_param( 'suggestion' );
        $post_id = $request->get_param( 'post_id' );

        try {
            $result = false;

            // Appliquer la correction selon le type d'erreur
            switch ( $error['category'] ) {
                case 'meta':
                    $result = $this->apply_meta_fix( $error, $suggestion, $post_id );
                    break;
                case 'content':
                    $result = $this->apply_content_fix( $error, $suggestion, $post_id );
                    break;
                default:
                    throw new Exception( __( 'Type de correction non supporté.', 'boss-seo' ) );
            }

            if ( $result ) {
                return new WP_REST_Response( array(
                    'success' => true,
                    'data' => array(
                        'applied' => true
                    ),
                    'message' => __( 'Correction appliquée avec succès.', 'boss-seo' )
                ), 200 );
            } else {
                throw new Exception( __( 'Échec de l\'application de la correction.', 'boss-seo' ) );
            }

        } catch ( Exception $e ) {
            return new WP_REST_Response( array(
                'success' => false,
                'data' => array(
                    'applied' => false
                ),
                'message' => __( 'Erreur lors de l\'application de la correction.', 'boss-seo' ) . ' ' . $e->getMessage()
            ), 500 );
        }
    }

    // ========================================
    // MÉTHODES UTILITAIRES PRIVÉES
    // ========================================

    /**
     * Extraire le texte visible d'un document DOM.
     *
     * @since    1.0.0
     * @param    DOMDocument    $dom    Document DOM.
     * @return   string                 Texte visible.
     */
    private function extract_text_content( $dom ) {
        // Supprimer les scripts et styles
        $scripts = $dom->getElementsByTagName( 'script' );
        for ( $i = $scripts->length - 1; $i >= 0; $i-- ) {
            $scripts->item( $i )->parentNode->removeChild( $scripts->item( $i ) );
        }

        $styles = $dom->getElementsByTagName( 'style' );
        for ( $i = $styles->length - 1; $i >= 0; $i-- ) {
            $styles->item( $i )->parentNode->removeChild( $styles->item( $i ) );
        }

        return trim( $dom->textContent );
    }

    /**
     * Vérifier la duplication de contenu (simulation).
     *
     * @since    1.0.0
     * @param    string    $content    Contenu à vérifier.
     * @return   int                   Pourcentage de duplication.
     */
    private function check_content_duplication( $content ) {
        // Simulation - dans la vraie implémentation, on comparerait avec d'autres pages
        return rand( 0, 50 );
    }

    /**
     * Vérifier la hiérarchie des titres.
     *
     * @since    1.0.0
     * @param    DOMDocument    $dom    Document DOM.
     * @return   array                 Problèmes de hiérarchie.
     */
    private function check_heading_hierarchy( $dom ) {
        $problems = array();
        $headings = array();

        // Récupérer tous les titres
        for ( $level = 1; $level <= 6; $level++ ) {
            $tags = $dom->getElementsByTagName( "h$level" );
            foreach ( $tags as $tag ) {
                $headings[] = array(
                    'level' => $level,
                    'text' => trim( $tag->textContent )
                );
            }
        }

        // Vérifier la hiérarchie
        $previous_level = 0;
        foreach ( $headings as $heading ) {
            if ( $heading['level'] > $previous_level + 1 ) {
                $problems[] = "h{$heading['level']}";
            }
            $previous_level = $heading['level'];
        }

        return array_unique( $problems );
    }

    /**
     * Vérifier si une image est trop volumineuse (simulation).
     *
     * @since    1.0.0
     * @param    string    $src    Source de l'image.
     * @return   bool              True si l'image est trop volumineuse.
     */
    private function is_large_image( $src ) {
        // Simulation - dans la vraie implémentation, on vérifierait la taille réelle
        return rand( 0, 1 ) === 1;
    }

    /**
     * Vérifier si un lien est externe.
     *
     * @since    1.0.0
     * @param    string    $href    URL du lien.
     * @param    string    $base    URL de base du site.
     * @return   bool               True si le lien est externe.
     */
    private function is_external_link( $href, $base ) {
        $base_domain = parse_url( $base, PHP_URL_HOST );
        $link_domain = parse_url( $href, PHP_URL_HOST );

        return $link_domain && $link_domain !== $base_domain;
    }

    /**
     * Vérifier si un lien est cassé (simulation).
     *
     * @since    1.0.0
     * @param    string    $href    URL du lien.
     * @return   bool               True si le lien est cassé.
     */
    private function is_broken_link( $href ) {
        // Simulation - dans la vraie implémentation, on ferait une requête HTTP
        return rand( 0, 10 ) === 1; // 10% de chance d'être cassé
    }

    /**
     * Construire le prompt pour l'explication IA.
     *
     * @since    1.0.0
     * @param    array     $error      Erreur à expliquer.
     * @param    string    $language   Langue de la réponse.
     * @param    string    $mode       Mode d'explication.
     * @return   string                Prompt pour l'IA.
     */
    private function build_explanation_prompt( $error, $language, $mode ) {
        $prompt = "Tu es un expert SEO. Explique cette erreur SEO de manière claire et pédagogique.\n\n";
        $prompt .= "Erreur: {$error['title']}\n";
        $prompt .= "Description: {$error['description']}\n";
        $prompt .= "Catégorie: {$error['category']}\n";
        $prompt .= "Sévérité: {$error['severity']}\n\n";

        if ( $language === 'fr' ) {
            $prompt .= "Réponds en français et structure ta réponse ainsi:\n";
            $prompt .= "1. Explication simple de l'erreur\n";
            $prompt .= "2. Pourquoi c'est important pour le SEO\n";
            $prompt .= "3. Impact sur le référencement\n\n";
        } else {
            $prompt .= "Respond in English and structure your answer as:\n";
            $prompt .= "1. Simple explanation of the error\n";
            $prompt .= "2. Why it's important for SEO\n";
            $prompt .= "3. Impact on search rankings\n\n";
        }

        if ( $mode === 'detailed' ) {
            $prompt .= "Donne une explication détaillée avec des exemples techniques.";
        } else {
            $prompt .= "Donne une explication simple et accessible aux débutants.";
        }

        return $prompt;
    }

    /**
     * Construire le prompt pour la correction IA.
     *
     * @since    1.0.0
     * @param    array     $error      Erreur à corriger.
     * @param    string    $language   Langue de la réponse.
     * @param    string    $mode       Mode de correction.
     * @return   string                Prompt pour l'IA.
     */
    private function build_correction_prompt( $error, $language, $mode ) {
        $prompt = "Tu es un expert SEO. Donne une correction COURTE et PRÉCISE pour cette erreur.\n\n";
        $prompt .= "Erreur: {$error['title']}\n";
        $prompt .= "Description: {$error['description']}\n";

        if ( isset( $error['currentValue'] ) ) {
            $prompt .= "Valeur actuelle: {$error['currentValue']}\n";
        }

        if ( isset( $error['recommendedValue'] ) ) {
            $prompt .= "Valeur recommandée: {$error['recommendedValue']}\n";
        }

        $prompt .= "\n";

        if ( $language === 'fr' ) {
            $prompt .= "CONTRAINTES STRICTES:\n";
            $prompt .= "- Maximum 200 mots\n";
            $prompt .= "- Réponse en HTML formaté\n";
            $prompt .= "- Seulement les étapes essentielles\n";
            $prompt .= "- Code concret si applicable\n\n";

            $prompt .= "Format de réponse:\n";
            $prompt .= "<div class='correction-suggestion'>\n";
            $prompt .= "<h4>Solution rapide</h4>\n";
            $prompt .= "<ol>\n";
            $prompt .= "<li>Étape 1 concrète</li>\n";
            $prompt .= "<li>Étape 2 concrète</li>\n";
            $prompt .= "</ol>\n";
            if ( in_array( $error['category'], array( 'meta_tags', 'content_quality', 'technical_seo' ) ) ) {
                $prompt .= "<h5>Code à utiliser:</h5>\n";
                $prompt .= "<code>Code HTML/CSS exact</code>\n";
            }
            $prompt .= "</div>\n\n";
        } else {
            $prompt .= "STRICT CONSTRAINTS:\n";
            $prompt .= "- Maximum 200 words\n";
            $prompt .= "- HTML formatted response\n";
            $prompt .= "- Only essential steps\n";
            $prompt .= "- Concrete code if applicable\n\n";

            $prompt .= "Response format:\n";
            $prompt .= "<div class='correction-suggestion'>\n";
            $prompt .= "<h4>Quick Solution</h4>\n";
            $prompt .= "<ol>\n";
            $prompt .= "<li>Concrete step 1</li>\n";
            $prompt .= "<li>Concrete step 2</li>\n";
            $prompt .= "</ol>\n";
            if ( in_array( $error['category'], array( 'meta_tags', 'content_quality', 'technical_seo' ) ) ) {
                $prompt .= "<h5>Code to use:</h5>\n";
                $prompt .= "<code>Exact HTML/CSS code</code>\n";
            }
            $prompt .= "</div>\n\n";
        }

        $prompt .= "IMPORTANT: Réponds UNIQUEMENT avec le HTML formaté, sans texte avant ou après.";

        return $prompt;
    }

    /**
     * Parser la réponse IA pour l'explication.
     *
     * @since    1.0.0
     * @param    string    $ai_text    Texte généré par l'IA.
     * @return   array                 Explication structurée.
     */
    private function parse_ai_explanation( $ai_text ) {
        return array(
            'description' => $ai_text,
            'impact' => 'Impact analysé par l\'IA',
            'recommendation' => 'Recommandation basée sur l\'analyse IA'
        );
    }

    /**
     * Parser la réponse IA pour la correction.
     *
     * @since    1.0.0
     * @param    string    $ai_text    Texte généré par l'IA.
     * @param    array     $error      Erreur originale.
     * @return   array                 Suggestion structurée.
     */
    private function parse_ai_correction( $ai_text, $error ) {
        // Nettoyer la réponse IA
        $cleaned_text = trim( $ai_text );

        // Extraire le code si présent dans des balises <code>
        preg_match( '/<code[^>]*>(.*?)<\/code>/s', $cleaned_text, $code_matches );
        $code = isset( $code_matches[1] ) ? trim( $code_matches[1] ) : '';

        // Extraire les étapes si présentes dans une liste <ol>
        preg_match( '/<ol[^>]*>(.*?)<\/ol>/s', $cleaned_text, $steps_matches );
        $steps = array();
        if ( isset( $steps_matches[1] ) ) {
            preg_match_all( '/<li[^>]*>(.*?)<\/li>/s', $steps_matches[1], $li_matches );
            if ( isset( $li_matches[1] ) ) {
                $steps = array_map( 'strip_tags', $li_matches[1] );
                $steps = array_map( 'trim', $steps );
            }
        }

        // Si pas d'étapes trouvées, utiliser des étapes par défaut
        if ( empty( $steps ) ) {
            $steps = array(
                'Appliquer la correction suggérée',
                'Vérifier le résultat',
                'Relancer un audit pour confirmer'
            );
        }

        // Déterminer si la correction peut être appliquée automatiquement
        $can_apply = in_array( $error['category'], array( 'meta_tags', 'content_quality' ) ) && !empty( $code );

        return array(
            'type' => 'ai_suggestion',
            'title' => 'Correction suggérée par IA',
            'description' => $cleaned_text, // HTML formaté
            'code' => $code,
            'canApply' => $can_apply,
            'steps' => $steps,
            'expectedResult' => 'Amélioration du score SEO après application de la correction',
            'category' => $error['category'],
            'severity' => $error['severity'] ?? 'medium'
        );
    }

    /**
     * Sauvegarder un audit en base de données.
     *
     * @since    1.0.0
     * @param    array    $audit_data    Données de l'audit.
     * @return   int                     ID de l'audit sauvegardé.
     */
    private function save_audit_to_database( $audit_data ) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'boss_seo_audits';

        // Vérifier si la table existe
        if ( $wpdb->get_var( "SHOW TABLES LIKE '$table_name'" ) !== $table_name ) {
            $this->create_audit_table();
        }

        $result = $wpdb->insert(
            $table_name,
            array(
                'url' => $audit_data['url'],
                'global_score' => $audit_data['globalScore'],
                'errors' => wp_json_encode( $audit_data['errors'] ),
                'performance_data' => wp_json_encode( $audit_data['performance'] ?? array() ),
                'metadata' => wp_json_encode( $audit_data['metadata'] ?? array() ),
                'date_created' => current_time( 'mysql' )
            ),
            array( '%s', '%d', '%s', '%s', '%s', '%s' )
        );

        if ( $result === false ) {
            throw new Exception( 'Erreur lors de la sauvegarde en base de données' );
        }

        return $wpdb->insert_id;
    }

    /**
     * Créer la table des audits.
     *
     * @since    1.0.0
     */
    private function create_audit_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'boss_seo_audits';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            url varchar(255) NOT NULL,
            global_score tinyint(3) NOT NULL,
            errors longtext NOT NULL,
            performance_data text,
            metadata text,
            date_created datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY url (url),
            KEY date_created (date_created)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Appliquer une correction meta.
     *
     * @since    1.0.0
     * @param    array    $error        Erreur à corriger.
     * @param    array    $suggestion   Suggestion de correction.
     * @param    int      $post_id      ID du post (optionnel).
     * @return   bool                   True si la correction a été appliquée.
     */
    private function apply_meta_fix( $error, $suggestion, $post_id = null ) {
        if ( ! $post_id ) {
            return false;
        }

        // Exemple d'application pour les méta descriptions
        if ( $error['id'] === 'meta-description-missing' && isset( $suggestion['code'] ) ) {
            // Extraire la méta description du code suggéré
            preg_match( '/content="([^"]+)"/', $suggestion['code'], $matches );
            if ( isset( $matches[1] ) ) {
                update_post_meta( $post_id, '_boss_seo_meta_description', $matches[1] );
                return true;
            }
        }

        return false;
    }

    /**
     * Appliquer une correction de contenu.
     *
     * @since    1.0.0
     * @param    array    $error        Erreur à corriger.
     * @param    array    $suggestion   Suggestion de correction.
     * @param    int      $post_id      ID du post (optionnel).
     * @return   bool                   True si la correction a été appliquée.
     */
    private function apply_content_fix( $error, $suggestion, $post_id = null ) {
        // Les corrections de contenu nécessitent généralement une intervention manuelle
        return false;
    }

    /**
     * Vérifier l'optimisation des mots-clés.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @param    string    $url        URL de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_keywords( $content, $url ) {
        $errors = array();
        $dom = new DOMDocument();
        @$dom->loadHTML( $content );

        // Extraire le texte visible
        $text_content = $this->extract_text_content( $dom );

        // Obtenir le titre de la page
        $title_tags = $dom->getElementsByTagName( 'title' );
        $title = $title_tags->length > 0 ? $title_tags->item( 0 )->textContent : '';

        // Obtenir la méta description
        $meta_description = '';
        $meta_tags = $dom->getElementsByTagName( 'meta' );
        foreach ( $meta_tags as $meta ) {
            if ( $meta->getAttribute( 'name' ) === 'description' ) {
                $meta_description = $meta->getAttribute( 'content' );
                break;
            }
        }

        // Vérifier la densité des mots-clés (simulation basique)
        $word_count = str_word_count( $text_content );
        if ( $word_count > 0 ) {
            // Analyser les mots les plus fréquents
            $words = str_word_count( strtolower( $text_content ), 1 );
            $word_frequency = array_count_values( $words );
            arsort( $word_frequency );

            // Vérifier s'il y a des mots-clés sur-optimisés
            foreach ( array_slice( $word_frequency, 0, 5, true ) as $word => $frequency ) {
                if ( strlen( $word ) > 3 ) { // Ignorer les mots trop courts
                    $density = ( $frequency / $word_count ) * 100;
                    if ( $density > 3 ) { // Plus de 3% de densité
                        $errors[] = array(
                            'id' => 'keyword-over-optimization',
                            'title' => __( 'Sur-optimisation de mot-clé détectée', 'boss-seo' ),
                            'description' => sprintf( __( 'Le mot "%s" apparaît trop fréquemment dans le contenu.', 'boss-seo' ), $word ),
                            'severity' => 'medium',
                            'location' => 'body',
                            'elements' => array( $word ),
                            'currentValue' => sprintf( __( '%.1f%% de densité', 'boss-seo' ), $density ),
                            'recommendedValue' => __( 'Moins de 3% de densité par mot-clé', 'boss-seo' )
                        );
                    }
                }
            }
        }

        // Vérifier l'absence de mots-clés dans le titre
        if ( empty( $title ) || str_word_count( $title ) < 3 ) {
            $errors[] = array(
                'id' => 'title-lacks-keywords',
                'title' => __( 'Titre peu optimisé pour les mots-clés', 'boss-seo' ),
                'description' => __( 'Le titre de la page semble manquer de mots-clés pertinents.', 'boss-seo' ),
                'severity' => 'medium',
                'location' => '<head>',
                'elements' => array( 'title' ),
                'currentValue' => $title ?: __( 'Titre vide', 'boss-seo' ),
                'recommendedValue' => __( 'Titre contenant des mots-clés pertinents', 'boss-seo' )
            );
        }

        return $errors;
    }

    /**
     * Vérifier le Schema Markup.
     *
     * @since    1.0.0
     * @param    string    $content    Contenu HTML de la page.
     * @param    string    $url        URL de la page.
     * @return   array                 Erreurs détectées.
     */
    private function check_schema( $content, $url ) {
        $errors = array();

        try {
            // Utiliser l'analyseur de Schema existant
            if ( ! class_exists( 'Boss_Schema_Analyzer' ) ) {
                require_once BOSS_SEO_PLUGIN_DIR . 'includes/class-boss-schema-analyzer.php';
            }

            $schema_analyzer = new Boss_Schema_Analyzer();
            $schema_results = $schema_analyzer->analyze_url( $url );

            if ( $schema_results['success'] ) {
                // Convertir les issues du Schema Analyzer en format d'erreurs d'audit
                if ( isset( $schema_results['issues'] ) && is_array( $schema_results['issues'] ) ) {
                    foreach ( $schema_results['issues'] as $issue ) {
                        $severity = 'low';
                        if ( isset( $issue['severity'] ) ) {
                            switch ( $issue['severity'] ) {
                                case 'error':
                                    $severity = 'critical';
                                    break;
                                case 'warning':
                                    $severity = 'medium';
                                    break;
                                default:
                                    $severity = 'low';
                            }
                        }

                        $errors[] = array(
                            'id' => 'schema-' . ( $issue['type'] ?? 'issue' ),
                            'title' => $issue['message'] ?? __( 'Problème de Schema Markup', 'boss-seo' ),
                            'description' => $issue['description'] ?? $issue['message'] ?? '',
                            'severity' => $severity,
                            'location' => '<head>',
                            'elements' => array( 'script[type="application/ld+json"]' ),
                            'currentValue' => isset( $issue['schema_type'] ) ? sprintf( __( 'Schema %s', 'boss-seo' ), $issue['schema_type'] ) : __( 'Schema détecté', 'boss-seo' ),
                            'recommendedValue' => __( 'Schema valide et complet', 'boss-seo' )
                        );
                    }
                }

                // Si aucun schema n'est trouvé
                if ( $schema_results['schemas_found'] === 0 ) {
                    $errors[] = array(
                        'id' => 'schema-missing',
                        'title' => __( 'Aucun Schema Markup détecté', 'boss-seo' ),
                        'description' => __( 'Cette page n\'a pas de données structurées (Schema Markup), ce qui peut limiter sa visibilité dans les résultats de recherche enrichis.', 'boss-seo' ),
                        'severity' => 'medium',
                        'location' => '<head>',
                        'elements' => array( 'script[type="application/ld+json"]' ),
                        'currentValue' => __( 'Aucun schema', 'boss-seo' ),
                        'recommendedValue' => __( 'Au moins un schema de base (Organization, WebSite, WebPage)', 'boss-seo' )
                    );
                }
            } else {
                // Erreur lors de l'analyse du schema
                $errors[] = array(
                    'id' => 'schema-analysis-failed',
                    'title' => __( 'Analyse du Schema Markup échouée', 'boss-seo' ),
                    'description' => $schema_results['message'] ?? __( 'Impossible d\'analyser le Schema Markup de cette page.', 'boss-seo' ),
                    'severity' => 'low',
                    'location' => '<head>',
                    'elements' => array(),
                    'currentValue' => __( 'Analyse échouée', 'boss-seo' ),
                    'recommendedValue' => __( 'Vérifier la configuration du Schema Markup', 'boss-seo' )
                );
            }

        } catch ( Exception $e ) {
            $errors[] = array(
                'id' => 'schema-check-error',
                'title' => __( 'Erreur lors de la vérification du Schema', 'boss-seo' ),
                'description' => sprintf( __( 'Une erreur s\'est produite lors de la vérification du Schema Markup: %s', 'boss-seo' ), $e->getMessage() ),
                'severity' => 'low',
                'location' => '<head>',
                'elements' => array(),
                'currentValue' => __( 'Erreur de vérification', 'boss-seo' ),
                'recommendedValue' => __( 'Vérifier la configuration du plugin', 'boss-seo' )
            );
        }

        return $errors;
    }
}