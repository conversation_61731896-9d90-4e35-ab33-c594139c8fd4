{"version": 3, "names": ["_element", "require", "_context", "styles", "_interopRequireWildcard", "_context2", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useItem", "props", "as", "asProp", "className", "onClick", "role", "size", "sizeProp", "otherProps", "useContextSystem", "spacedAround", "contextSize", "useItemGroupContext", "cx", "useCx", "classes", "useMemo", "unstyledButton", "itemSizes", "medium", "item", "wrapperClassName", "itemWrapper"], "sources": ["@wordpress/components/src/item-group/item/hook.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ElementType } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport * as styles from '../styles';\nimport { useItemGroupContext } from '../context';\nimport { useCx } from '../../utils/hooks/use-cx';\nimport type { ItemProps } from '../types';\n\nexport function useItem( props: WordPressComponentProps< ItemProps, 'div' > ) {\n\tconst {\n\t\tas: asProp,\n\t\tclassName,\n\t\tonClick,\n\t\trole = 'listitem',\n\t\tsize: sizeProp,\n\t\t...otherProps\n\t} = useContextSystem( props, 'Item' );\n\n\tconst { spacedAround, size: contextSize } = useItemGroupContext();\n\n\tconst size = sizeProp || contextSize;\n\n\tconst as =\n\t\tasProp ||\n\t\t( ( typeof onClick !== 'undefined'\n\t\t\t? 'button'\n\t\t\t: 'div' ) as ElementType );\n\n\tconst cx = useCx();\n\n\tconst classes = useMemo(\n\t\t() =>\n\t\t\tcx(\n\t\t\t\t( as === 'button' || as === 'a' ) &&\n\t\t\t\t\tstyles.unstyledButton( as ),\n\t\t\t\tstyles.itemSizes[ size ] || styles.itemSizes.medium,\n\t\t\t\tstyles.item,\n\t\t\t\tspacedAround && styles.spacedAround,\n\t\t\t\tclassName\n\t\t\t),\n\t\t[ as, className, cx, size, spacedAround ]\n\t);\n\n\tconst wrapperClassName = cx( styles.itemWrapper );\n\n\treturn {\n\t\tas,\n\t\tclassName: classes,\n\t\tonClick,\n\t\twrapperClassName,\n\t\trole,\n\t\t...otherProps,\n\t};\n}\n"], "mappings": ";;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAAiD,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAjBjD;AACA;AACA;;AAGA;AACA;AACA;;AAaO,SAASW,OAAOA,CAAEC,KAAkD,EAAG;EAC7E,MAAM;IACLC,EAAE,EAAEC,MAAM;IACVC,SAAS;IACTC,OAAO;IACPC,IAAI,GAAG,UAAU;IACjBC,IAAI,EAAEC,QAAQ;IACd,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAET,KAAK,EAAE,MAAO,CAAC;EAErC,MAAM;IAAEU,YAAY;IAAEJ,IAAI,EAAEK;EAAY,CAAC,GAAG,IAAAC,6BAAmB,EAAC,CAAC;EAEjE,MAAMN,IAAI,GAAGC,QAAQ,IAAII,WAAW;EAEpC,MAAMV,EAAE,GACPC,MAAM,KACF,OAAOE,OAAO,KAAK,WAAW,GAC/B,QAAQ,GACR,KAAK,CAAmB;EAE5B,MAAMS,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EACtB,MACCH,EAAE,CACD,CAAEZ,EAAE,KAAK,QAAQ,IAAIA,EAAE,KAAK,GAAG,KAC9B5B,MAAM,CAAC4C,cAAc,CAAEhB,EAAG,CAAC,EAC5B5B,MAAM,CAAC6C,SAAS,CAAEZ,IAAI,CAAE,IAAIjC,MAAM,CAAC6C,SAAS,CAACC,MAAM,EACnD9C,MAAM,CAAC+C,IAAI,EACXV,YAAY,IAAIrC,MAAM,CAACqC,YAAY,EACnCP,SACD,CAAC,EACF,CAAEF,EAAE,EAAEE,SAAS,EAAEU,EAAE,EAAEP,IAAI,EAAEI,YAAY,CACxC,CAAC;EAED,MAAMW,gBAAgB,GAAGR,EAAE,CAAExC,MAAM,CAACiD,WAAY,CAAC;EAEjD,OAAO;IACNrB,EAAE;IACFE,SAAS,EAAEY,OAAO;IAClBX,OAAO;IACPiB,gBAAgB;IAChBhB,IAAI;IACJ,GAAGG;EACJ,CAAC;AACF"}