"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BottomSheetSwitchCell;
var _react = require("react");
var _reactNative = require("react-native");
var _i18n = require("@wordpress/i18n");
var _cell = _interopRequireDefault(require("./cell"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const EMPTY_STYLE = {};
function BottomSheetSwitchCell(props) {
  const {
    value,
    onValueChange,
    disabled,
    ...cellProps
  } = props;
  const onPress = () => {
    onValueChange(!value);
  };
  const getAccessibilityLabel = () => {
    if (!cellProps.help) {
      return value ? (0, _i18n.sprintf)( /* translators: accessibility text. Switch setting ON state. %s: Switch title. */
      (0, _i18n._x)('%s. On', 'switch control'), cellProps.label) : (0, _i18n.sprintf)( /* translators: accessibility text. Switch setting OFF state. %s: Switch title. */
      (0, _i18n._x)('%s. Off', 'switch control'), cellProps.label);
    }
    return value ? (0, _i18n.sprintf)( /* translators: accessibility text. Switch setting ON state. %1: Switch title, %2: switch help. */
    (0, _i18n._x)('%1$s, %2$s. On', 'switch control'), cellProps.label, cellProps.help) : (0, _i18n.sprintf)( /* translators: accessibility text. Switch setting OFF state. %1: Switch title, %2: switch help. */
    (0, _i18n._x)('%1$s, %2$s. Off', 'switch control'), cellProps.label, cellProps.help);
  };
  return (0, _react.createElement)(_cell.default, {
    ...cellProps,
    accessibilityLabel: getAccessibilityLabel(),
    accessibilityRole: 'none',
    accessibilityHint: /* translators: accessibility text (hint for switches) */
    (0, _i18n.__)('Double tap to toggle setting'),
    onPress: onPress,
    editable: false,
    value: '',
    disabled: disabled,
    disabledStyle: EMPTY_STYLE
  }, (0, _react.createElement)(_reactNative.Switch, {
    value: value,
    onValueChange: onValueChange,
    disabled: disabled
  }));
}
//# sourceMappingURL=switch-cell.native.js.map