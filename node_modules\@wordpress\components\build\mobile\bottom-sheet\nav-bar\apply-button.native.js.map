{"version": 3, "names": ["_reactNative", "require", "_i18n", "_icons", "_compose", "_styles", "_interopRequireDefault", "_actionButton", "App<PERSON><PERSON><PERSON><PERSON>", "onPress", "buttonTextStyle", "usePreferredColorSchemeStyle", "styles", "applyButtonStyle", "_react", "createElement", "View", "style", "default", "accessibilityLabel", "__", "accessibilityHint", "Platform", "OS", "Text", "maxFontSizeMultiplier", "Icon", "icon", "check", "size", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/nav-bar/apply-button.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, Text, Platform } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { Icon, check } from '@wordpress/icons';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\nimport ActionButton from './action-button';\n\nfunction ApplyButton( { onPress } ) {\n\tconst buttonTextStyle = usePreferredColorSchemeStyle(\n\t\tstyles[ 'button-text' ],\n\t\tstyles[ 'button-text-dark' ]\n\t);\n\n\tconst applyButtonStyle = usePreferredColorSchemeStyle(\n\t\tstyles[ 'apply-button-icon' ],\n\t\tstyles[ 'apply-button-icon-dark' ]\n\t);\n\n\treturn (\n\t\t<View style={ styles[ 'apply-button' ] }>\n\t\t\t<ActionButton\n\t\t\t\tonPress={ onPress }\n\t\t\t\taccessibilityLabel={ __( 'Apply' ) }\n\t\t\t\taccessibilityHint={ __( 'Applies the setting' ) }\n\t\t\t>\n\t\t\t\t{ Platform.OS === 'ios' ? (\n\t\t\t\t\t<Text style={ buttonTextStyle } maxFontSizeMultiplier={ 2 }>\n\t\t\t\t\t\t{ __( 'Apply' ) }\n\t\t\t\t\t</Text>\n\t\t\t\t) : (\n\t\t\t\t\t<Icon\n\t\t\t\t\t\ticon={ check }\n\t\t\t\t\t\tsize={ 24 }\n\t\t\t\t\t\tstyle={ applyButtonStyle }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t</ActionButton>\n\t\t</View>\n\t);\n}\n\nexport default ApplyButton;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,OAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,aAAA,GAAAD,sBAAA,CAAAL,OAAA;AAhBA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;;AAIA,SAASO,WAAWA,CAAE;EAAEC;AAAQ,CAAC,EAAG;EACnC,MAAMC,eAAe,GAAG,IAAAC,qCAA4B,EACnDC,eAAM,CAAE,aAAa,CAAE,EACvBA,eAAM,CAAE,kBAAkB,CAC3B,CAAC;EAED,MAAMC,gBAAgB,GAAG,IAAAF,qCAA4B,EACpDC,eAAM,CAAE,mBAAmB,CAAE,EAC7BA,eAAM,CAAE,wBAAwB,CACjC,CAAC;EAED,OACC,IAAAE,MAAA,CAAAC,aAAA,EAACf,YAAA,CAAAgB,IAAI;IAACC,KAAK,EAAGL,eAAM,CAAE,cAAc;EAAI,GACvC,IAAAE,MAAA,CAAAC,aAAA,EAACR,aAAA,CAAAW,OAAY;IACZT,OAAO,EAAGA,OAAS;IACnBU,kBAAkB,EAAG,IAAAC,QAAE,EAAE,OAAQ,CAAG;IACpCC,iBAAiB,EAAG,IAAAD,QAAE,EAAE,qBAAsB;EAAG,GAE/CE,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACtB,IAAAT,MAAA,CAAAC,aAAA,EAACf,YAAA,CAAAwB,IAAI;IAACP,KAAK,EAAGP,eAAiB;IAACe,qBAAqB,EAAG;EAAG,GACxD,IAAAL,QAAE,EAAE,OAAQ,CACT,CAAC,GAEP,IAAAN,MAAA,CAAAC,aAAA,EAACZ,MAAA,CAAAuB,IAAI;IACJC,IAAI,EAAGC,YAAO;IACdC,IAAI,EAAG,EAAI;IACXZ,KAAK,EAAGJ;EAAkB,CAC1B,CAEW,CACT,CAAC;AAET;AAAC,IAAAiB,QAAA,GAEctB,WAAW;AAAAuB,OAAA,CAAAb,OAAA,GAAAY,QAAA"}