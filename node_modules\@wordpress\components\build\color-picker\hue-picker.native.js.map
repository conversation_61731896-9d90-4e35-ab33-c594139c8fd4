{"version": 3, "names": ["_reactNative", "require", "_reactNativeLinearGradient", "_interopRequireDefault", "_element", "_interopRequireWildcard", "_style", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "<PERSON>ePicker", "Component", "constructor", "props", "hueColors", "sliderX", "Animated", "Value", "barHeight", "hue", "panResponder", "PanResponder", "create", "onStartShouldSetPanResponder", "onStartShouldSetPanResponderCapture", "onMoveShouldSetPanResponder", "onMoveShouldSetPanResponderCapture", "onPanResponderGrant", "evt", "gestureState", "onPress", "dragStartValue", "computeHueValuePress", "nativeEvent", "fireDragEvent", "onPanResponderMove", "onPanResponderTerminationRequest", "onPanResponderRelease", "onPanResponderTerminate", "onShouldBlockNativeResponder", "componentDidUpdate", "prevProps", "<PERSON><PERSON><PERSON><PERSON>", "sliderSize", "borderWidth", "setValue", "normalizeValue", "value", "getContainerStyle", "containerStyle", "paddingLeft", "paddingTop", "styles", "paddingBottom", "paddingRight", "computeHueValueDrag", "dx", "diff", "updatedHue", "event", "locationX", "eventName", "firePressEvent", "render", "borderRadius", "_react", "createElement", "View", "style", "panHandlers", "hitSlop", "top", "bottom", "left", "right", "colors", "start", "x", "y", "end", "width", "height", "pointerEvents", "transform", "translateX", "exports"], "sources": ["@wordpress/components/src/color-picker/hue-picker.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Animated, View, PanResponder } from 'react-native';\nimport LinearGradient from 'react-native-linear-gradient';\n\n/**\n * WordPress dependencies\n */\nimport React, { Component } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nexport default class HuePicker extends Component {\n\tconstructor( props ) {\n\t\tsuper( props );\n\t\tthis.hueColors = [\n\t\t\t'#ff0000',\n\t\t\t'#ffff00',\n\t\t\t'#00ff00',\n\t\t\t'#00ffff',\n\t\t\t'#0000ff',\n\t\t\t'#ff00ff',\n\t\t\t'#ff0000',\n\t\t];\n\t\tthis.sliderX = new Animated.Value(\n\t\t\t( props.barHeight * props.hue ) / 360\n\t\t);\n\t\tthis.panResponder = PanResponder.create( {\n\t\t\tonStartShouldSetPanResponder: () => true,\n\t\t\tonStartShouldSetPanResponderCapture: () => true,\n\t\t\tonMoveShouldSetPanResponder: () => true,\n\t\t\tonMoveShouldSetPanResponderCapture: () => true,\n\t\t\tonPanResponderGrant: ( evt, gestureState ) => {\n\t\t\t\tconst { onPress } = this.props;\n\t\t\t\tthis.dragStartValue = this.computeHueValuePress( evt );\n\n\t\t\t\tif ( onPress ) {\n\t\t\t\t\tonPress( {\n\t\t\t\t\t\thue: this.computeHueValuePress( evt ),\n\t\t\t\t\t\tnativeEvent: evt.nativeEvent,\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\tthis.fireDragEvent( 'onDragStart', gestureState );\n\t\t\t},\n\t\t\tonPanResponderMove: ( evt, gestureState ) => {\n\t\t\t\tthis.fireDragEvent( 'onDragMove', gestureState );\n\t\t\t},\n\t\t\tonPanResponderTerminationRequest: () => true,\n\t\t\tonPanResponderRelease: ( evt, gestureState ) => {\n\t\t\t\tthis.fireDragEvent( 'onDragEnd', gestureState );\n\t\t\t},\n\t\t\tonPanResponderTerminate: ( evt, gestureState ) => {\n\t\t\t\tthis.fireDragEvent( 'onDragTerminate', gestureState );\n\t\t\t},\n\t\t\tonShouldBlockNativeResponder: () => true,\n\t\t} );\n\t}\n\n\tcomponentDidUpdate( prevProps ) {\n\t\tconst { hue = 0, barWidth = 200, sliderSize = 24 } = this.props;\n\t\tconst borderWidth = sliderSize / 10;\n\t\tif ( prevProps.hue !== hue || prevProps.barWidth !== barWidth ) {\n\t\t\tthis.sliderX.setValue(\n\t\t\t\t( ( barWidth - sliderSize + borderWidth ) * hue ) / 360\n\t\t\t);\n\t\t}\n\t}\n\n\tnormalizeValue( value ) {\n\t\tif ( value < 0 ) return 0;\n\t\tif ( value > 1 ) return 1;\n\t\treturn value;\n\t}\n\n\tgetContainerStyle() {\n\t\tconst {\n\t\t\tsliderSize = 24,\n\t\t\tbarHeight = 12,\n\t\t\tcontainerStyle = {},\n\t\t} = this.props;\n\t\tconst paddingLeft = sliderSize / 2;\n\t\tconst paddingTop =\n\t\t\tsliderSize - barHeight > 0 ? ( sliderSize - barHeight ) / 2 : 0;\n\t\treturn [\n\t\t\tstyles[ 'hsv-container' ],\n\t\t\tcontainerStyle,\n\t\t\t{\n\t\t\t\tpaddingTop,\n\t\t\t\tpaddingBottom: paddingTop,\n\t\t\t\tpaddingLeft,\n\t\t\t\tpaddingRight: paddingLeft,\n\t\t\t},\n\t\t];\n\t}\n\n\tcomputeHueValueDrag( gestureState ) {\n\t\tconst { dx } = gestureState;\n\t\tconst { barWidth = 200 } = this.props;\n\t\tconst { dragStartValue } = this;\n\t\tconst diff = dx / barWidth;\n\t\tconst updatedHue =\n\t\t\tthis.normalizeValue( dragStartValue / 360 + diff ) * 360;\n\t\treturn updatedHue;\n\t}\n\n\tcomputeHueValuePress( event ) {\n\t\tconst { nativeEvent } = event;\n\t\tconst { locationX } = nativeEvent;\n\t\tconst { barWidth = 200 } = this.props;\n\t\tconst updatedHue = this.normalizeValue( locationX / barWidth ) * 360;\n\t\treturn updatedHue;\n\t}\n\n\tfireDragEvent( eventName, gestureState ) {\n\t\tconst { [ eventName ]: event } = this.props;\n\t\tif ( event ) {\n\t\t\tevent( {\n\t\t\t\thue: this.computeHueValueDrag( gestureState ),\n\t\t\t\tgestureState,\n\t\t\t} );\n\t\t}\n\t}\n\n\tfirePressEvent( event ) {\n\t\tconst { onPress } = this.props;\n\t\tif ( onPress ) {\n\t\t\tonPress( {\n\t\t\t\thue: this.computeHueValuePress( event ),\n\t\t\t\tnativeEvent: event.nativeEvent,\n\t\t\t} );\n\t\t}\n\t}\n\n\trender() {\n\t\tconst { hueColors } = this;\n\t\tconst {\n\t\t\tsliderSize = 24,\n\t\t\tbarWidth = 200,\n\t\t\tbarHeight = 12,\n\t\t\tborderRadius = 0,\n\t\t} = this.props;\n\t\tconst borderWidth = sliderSize / 10;\n\t\treturn (\n\t\t\t<View\n\t\t\t\tstyle={ this.getContainerStyle() }\n\t\t\t\t{ ...this.panResponder.panHandlers }\n\t\t\t\thitSlop={ {\n\t\t\t\t\ttop: 10,\n\t\t\t\t\tbottom: 10,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t} }\n\t\t\t>\n\t\t\t\t<LinearGradient\n\t\t\t\t\tcolors={ hueColors }\n\t\t\t\t\tstyle={ {\n\t\t\t\t\t\tborderRadius,\n\t\t\t\t\t} }\n\t\t\t\t\tstart={ { x: 0, y: 0 } }\n\t\t\t\t\tend={ { x: 1, y: 0 } }\n\t\t\t\t>\n\t\t\t\t\t<View\n\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\twidth: barWidth,\n\t\t\t\t\t\t\theight: barHeight,\n\t\t\t\t\t\t} }\n\t\t\t\t\t/>\n\t\t\t\t</LinearGradient>\n\t\t\t\t<Animated.View\n\t\t\t\t\tpointerEvents=\"none\"\n\t\t\t\t\tstyle={ [\n\t\t\t\t\t\tstyles[ 'hue-slider' ],\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\twidth: sliderSize,\n\t\t\t\t\t\t\theight: sliderSize,\n\t\t\t\t\t\t\tleft: ( sliderSize - borderWidth ) / 2,\n\t\t\t\t\t\t\tborderRadius: sliderSize / 2,\n\t\t\t\t\t\t\ttransform: [\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\ttranslateX: this.sliderX,\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t],\n\t\t\t\t\t\t},\n\t\t\t\t\t] }\n\t\t\t\t/>\n\t\t\t</View>\n\t\t);\n\t}\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,0BAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,QAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAKA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AAAkC,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAdlC;AACA;AACA;;AAIA;AACA;AACA;;AAGA;AACA;AACA;;AAGe,MAAMW,SAAS,SAASC,kBAAS,CAAC;EAChDC,WAAWA,CAAEC,KAAK,EAAG;IACpB,KAAK,CAAEA,KAAM,CAAC;IACd,IAAI,CAACC,SAAS,GAAG,CAChB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACT;IACD,IAAI,CAACC,OAAO,GAAG,IAAIC,qBAAQ,CAACC,KAAK,CAC9BJ,KAAK,CAACK,SAAS,GAAGL,KAAK,CAACM,GAAG,GAAK,GACnC,CAAC;IACD,IAAI,CAACC,YAAY,GAAGC,yBAAY,CAACC,MAAM,CAAE;MACxCC,4BAA4B,EAAEA,CAAA,KAAM,IAAI;MACxCC,mCAAmC,EAAEA,CAAA,KAAM,IAAI;MAC/CC,2BAA2B,EAAEA,CAAA,KAAM,IAAI;MACvCC,kCAAkC,EAAEA,CAAA,KAAM,IAAI;MAC9CC,mBAAmB,EAAEA,CAAEC,GAAG,EAAEC,YAAY,KAAM;QAC7C,MAAM;UAAEC;QAAQ,CAAC,GAAG,IAAI,CAACjB,KAAK;QAC9B,IAAI,CAACkB,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAAEJ,GAAI,CAAC;QAEtD,IAAKE,OAAO,EAAG;UACdA,OAAO,CAAE;YACRX,GAAG,EAAE,IAAI,CAACa,oBAAoB,CAAEJ,GAAI,CAAC;YACrCK,WAAW,EAAEL,GAAG,CAACK;UAClB,CAAE,CAAC;QACJ;QAEA,IAAI,CAACC,aAAa,CAAE,aAAa,EAAEL,YAAa,CAAC;MAClD,CAAC;MACDM,kBAAkB,EAAEA,CAAEP,GAAG,EAAEC,YAAY,KAAM;QAC5C,IAAI,CAACK,aAAa,CAAE,YAAY,EAAEL,YAAa,CAAC;MACjD,CAAC;MACDO,gCAAgC,EAAEA,CAAA,KAAM,IAAI;MAC5CC,qBAAqB,EAAEA,CAAET,GAAG,EAAEC,YAAY,KAAM;QAC/C,IAAI,CAACK,aAAa,CAAE,WAAW,EAAEL,YAAa,CAAC;MAChD,CAAC;MACDS,uBAAuB,EAAEA,CAAEV,GAAG,EAAEC,YAAY,KAAM;QACjD,IAAI,CAACK,aAAa,CAAE,iBAAiB,EAAEL,YAAa,CAAC;MACtD,CAAC;MACDU,4BAA4B,EAAEA,CAAA,KAAM;IACrC,CAAE,CAAC;EACJ;EAEAC,kBAAkBA,CAAEC,SAAS,EAAG;IAC/B,MAAM;MAAEtB,GAAG,GAAG,CAAC;MAAEuB,QAAQ,GAAG,GAAG;MAAEC,UAAU,GAAG;IAAG,CAAC,GAAG,IAAI,CAAC9B,KAAK;IAC/D,MAAM+B,WAAW,GAAGD,UAAU,GAAG,EAAE;IACnC,IAAKF,SAAS,CAACtB,GAAG,KAAKA,GAAG,IAAIsB,SAAS,CAACC,QAAQ,KAAKA,QAAQ,EAAG;MAC/D,IAAI,CAAC3B,OAAO,CAAC8B,QAAQ,CAClB,CAAEH,QAAQ,GAAGC,UAAU,GAAGC,WAAW,IAAKzB,GAAG,GAAK,GACrD,CAAC;IACF;EACD;EAEA2B,cAAcA,CAAEC,KAAK,EAAG;IACvB,IAAKA,KAAK,GAAG,CAAC,EAAG,OAAO,CAAC;IACzB,IAAKA,KAAK,GAAG,CAAC,EAAG,OAAO,CAAC;IACzB,OAAOA,KAAK;EACb;EAEAC,iBAAiBA,CAAA,EAAG;IACnB,MAAM;MACLL,UAAU,GAAG,EAAE;MACfzB,SAAS,GAAG,EAAE;MACd+B,cAAc,GAAG,CAAC;IACnB,CAAC,GAAG,IAAI,CAACpC,KAAK;IACd,MAAMqC,WAAW,GAAGP,UAAU,GAAG,CAAC;IAClC,MAAMQ,UAAU,GACfR,UAAU,GAAGzB,SAAS,GAAG,CAAC,GAAG,CAAEyB,UAAU,GAAGzB,SAAS,IAAK,CAAC,GAAG,CAAC;IAChE,OAAO,CACNkC,cAAM,CAAE,eAAe,CAAE,EACzBH,cAAc,EACd;MACCE,UAAU;MACVE,aAAa,EAAEF,UAAU;MACzBD,WAAW;MACXI,YAAY,EAAEJ;IACf,CAAC,CACD;EACF;EAEAK,mBAAmBA,CAAE1B,YAAY,EAAG;IACnC,MAAM;MAAE2B;IAAG,CAAC,GAAG3B,YAAY;IAC3B,MAAM;MAAEa,QAAQ,GAAG;IAAI,CAAC,GAAG,IAAI,CAAC7B,KAAK;IACrC,MAAM;MAAEkB;IAAe,CAAC,GAAG,IAAI;IAC/B,MAAM0B,IAAI,GAAGD,EAAE,GAAGd,QAAQ;IAC1B,MAAMgB,UAAU,GACf,IAAI,CAACZ,cAAc,CAAEf,cAAc,GAAG,GAAG,GAAG0B,IAAK,CAAC,GAAG,GAAG;IACzD,OAAOC,UAAU;EAClB;EAEA1B,oBAAoBA,CAAE2B,KAAK,EAAG;IAC7B,MAAM;MAAE1B;IAAY,CAAC,GAAG0B,KAAK;IAC7B,MAAM;MAAEC;IAAU,CAAC,GAAG3B,WAAW;IACjC,MAAM;MAAES,QAAQ,GAAG;IAAI,CAAC,GAAG,IAAI,CAAC7B,KAAK;IACrC,MAAM6C,UAAU,GAAG,IAAI,CAACZ,cAAc,CAAEc,SAAS,GAAGlB,QAAS,CAAC,GAAG,GAAG;IACpE,OAAOgB,UAAU;EAClB;EAEAxB,aAAaA,CAAE2B,SAAS,EAAEhC,YAAY,EAAG;IACxC,MAAM;MAAE,CAAEgC,SAAS,GAAIF;IAAM,CAAC,GAAG,IAAI,CAAC9C,KAAK;IAC3C,IAAK8C,KAAK,EAAG;MACZA,KAAK,CAAE;QACNxC,GAAG,EAAE,IAAI,CAACoC,mBAAmB,CAAE1B,YAAa,CAAC;QAC7CA;MACD,CAAE,CAAC;IACJ;EACD;EAEAiC,cAAcA,CAAEH,KAAK,EAAG;IACvB,MAAM;MAAE7B;IAAQ,CAAC,GAAG,IAAI,CAACjB,KAAK;IAC9B,IAAKiB,OAAO,EAAG;MACdA,OAAO,CAAE;QACRX,GAAG,EAAE,IAAI,CAACa,oBAAoB,CAAE2B,KAAM,CAAC;QACvC1B,WAAW,EAAE0B,KAAK,CAAC1B;MACpB,CAAE,CAAC;IACJ;EACD;EAEA8B,MAAMA,CAAA,EAAG;IACR,MAAM;MAAEjD;IAAU,CAAC,GAAG,IAAI;IAC1B,MAAM;MACL6B,UAAU,GAAG,EAAE;MACfD,QAAQ,GAAG,GAAG;MACdxB,SAAS,GAAG,EAAE;MACd8C,YAAY,GAAG;IAChB,CAAC,GAAG,IAAI,CAACnD,KAAK;IACd,MAAM+B,WAAW,GAAGD,UAAU,GAAG,EAAE;IACnC,OACC,IAAAsB,MAAA,CAAAC,aAAA,EAACrF,YAAA,CAAAsF,IAAI;MACJC,KAAK,EAAG,IAAI,CAACpB,iBAAiB,CAAC,CAAG;MAAA,GAC7B,IAAI,CAAC5B,YAAY,CAACiD,WAAW;MAClCC,OAAO,EAAG;QACTC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACR;IAAG,GAEH,IAAAT,MAAA,CAAAC,aAAA,EAACnF,0BAAA,CAAAY,OAAc;MACdgF,MAAM,EAAG7D,SAAW;MACpBsD,KAAK,EAAG;QACPJ;MACD,CAAG;MACHY,KAAK,EAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAG;MACxBC,GAAG,EAAG;QAAEF,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE;IAAG,GAEtB,IAAAb,MAAA,CAAAC,aAAA,EAACrF,YAAA,CAAAsF,IAAI;MACJC,KAAK,EAAG;QACPY,KAAK,EAAEtC,QAAQ;QACfuC,MAAM,EAAE/D;MACT;IAAG,CACH,CACc,CAAC,EACjB,IAAA+C,MAAA,CAAAC,aAAA,EAACrF,YAAA,CAAAmC,QAAQ,CAACmD,IAAI;MACbe,aAAa,EAAC,MAAM;MACpBd,KAAK,EAAG,CACPhB,cAAM,CAAE,YAAY,CAAE,EACtB;QACC4B,KAAK,EAAErC,UAAU;QACjBsC,MAAM,EAAEtC,UAAU;QAClB8B,IAAI,EAAE,CAAE9B,UAAU,GAAGC,WAAW,IAAK,CAAC;QACtCoB,YAAY,EAAErB,UAAU,GAAG,CAAC;QAC5BwC,SAAS,EAAE,CACV;UACCC,UAAU,EAAE,IAAI,CAACrE;QAClB,CAAC;MAEH,CAAC;IACC,CACH,CACI,CAAC;EAET;AACD;AAACsE,OAAA,CAAA1F,OAAA,GAAAe,SAAA"}