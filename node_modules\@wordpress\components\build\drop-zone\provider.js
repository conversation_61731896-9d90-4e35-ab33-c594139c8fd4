"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = DropZoneProvider;
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
/**
 * WordPress dependencies
 */

function DropZoneProvider({
  children
}) {
  (0, _deprecated.default)('wp.components.DropZoneProvider', {
    since: '5.8',
    hint: 'wp.component.DropZone no longer needs a provider. wp.components.DropZoneProvider is safe to remove from your code.'
  });
  return children;
}
//# sourceMappingURL=provider.js.map