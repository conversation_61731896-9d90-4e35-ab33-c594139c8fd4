"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _5F4DVUNScjs = require('./5F4DVUNS.cjs');

// src/utils/platform.ts
function isTouchDevice() {
  return _5F4DVUNScjs.canUseDOM && !!navigator.maxTouchPoints;
}
function isApple() {
  if (!_5F4DVUNScjs.canUseDOM)
    return false;
  return /mac|iphone|ipad|ipod/i.test(navigator.platform);
}
function isSafari() {
  return _5F4DVUNScjs.canUseDOM && isApple() && /apple/i.test(navigator.vendor);
}
function isFirefox() {
  return _5F4DVUNScjs.canUseDOM && /firefox\//i.test(navigator.userAgent);
}
function isMac() {
  return _5F4DVUNScjs.canUseDOM && navigator.platform.startsWith("Mac") && !isTouchDevice();
}







exports.isTouchDevice = isTouchDevice; exports.isApple = isApple; exports.isSafari = isSafari; exports.isFirefox = isFirefox; exports.isMac = isMac;
