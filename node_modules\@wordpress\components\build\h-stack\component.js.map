{"version": 3, "names": ["_context", "require", "_view", "_hook", "UnconnectedHStack", "props", "forwardedRef", "hStackProps", "useHStack", "_react", "createElement", "View", "ref", "HStack", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/h-stack/component.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnect } from '../context';\nimport { View } from '../view';\nimport { useHStack } from './hook';\nimport type { Props } from './types';\n\nfunction UnconnectedHStack(\n\tprops: WordPressComponentProps< Props, 'div' >,\n\tforwardedRef: React.ForwardedRef< any >\n) {\n\tconst hStackProps = useHStack( props );\n\n\treturn <View { ...hStackProps } ref={ forwardedRef } />;\n}\n\n/**\n * `HStack` (Horizontal Stack) arranges child elements in a horizontal line.\n *\n * `HStack` can render anything inside.\n *\n * ```jsx\n * import {\n * \t__experimentalHStack as HStack,\n * \t__experimentalText as Text,\n * } from `@wordpress/components`;\n *\n * function Example() {\n * \treturn (\n * \t\t<HStack>\n * \t\t\t<Text>Code</Text>\n * \t\t\t<Text>is</Text>\n * \t\t\t<Text>Poetry</Text>\n * \t\t</HStack>\n * \t);\n * }\n * ```\n */\nexport const HStack = contextConnect( UnconnectedHStack, 'HStack' );\n\nexport default HStack;\n"], "mappings": ";;;;;;;AAIA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AANA;AACA;AACA;;AAOA,SAASG,iBAAiBA,CACzBC,KAA8C,EAC9CC,YAAuC,EACtC;EACD,MAAMC,WAAW,GAAG,IAAAC,eAAS,EAAEH,KAAM,CAAC;EAEtC,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,WAAW;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,MAAM,GAAG,IAAAC,uBAAc,EAAEV,iBAAiB,EAAE,QAAS,CAAC;AAACW,OAAA,CAAAF,MAAA,GAAAA,MAAA;AAAA,IAAAG,QAAA,GAErDH,MAAM;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}