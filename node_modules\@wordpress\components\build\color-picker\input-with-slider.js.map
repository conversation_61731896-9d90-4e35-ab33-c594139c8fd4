{"version": 3, "names": ["_hStack", "require", "_text", "_spacer", "_space", "_styles", "_colorsValues", "InputWithSlider", "min", "max", "label", "abbreviation", "onChange", "value", "onNumberControlChange", "newValue", "parseInt", "_react", "createElement", "HStack", "spacing", "NumberControlWrapper", "hideLabelFromVision", "prefix", "Spacer", "as", "Text", "paddingLeft", "space", "color", "COLORS", "theme", "accent", "lineHeight", "spinControls", "size", "RangeControl", "__nextHasNoMarginBottom", "withInputField", "exports"], "sources": ["@wordpress/components/src/color-picker/input-with-slider.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport { HStack } from '../h-stack';\nimport { Text } from '../text';\nimport { Spacer } from '../spacer';\nimport { space } from '../utils/space';\nimport { RangeControl, NumberControlWrapper } from './styles';\nimport { COLORS } from '../utils/colors-values';\nimport type { InputWithSliderProps } from './types';\n\nexport const InputWithSlider = ( {\n\tmin,\n\tmax,\n\tlabel,\n\tabbreviation,\n\tonChange,\n\tvalue,\n}: InputWithSliderProps ) => {\n\tconst onNumberControlChange = ( newValue?: number | string ) => {\n\t\tif ( ! newValue ) {\n\t\t\tonChange( 0 );\n\t\t\treturn;\n\t\t}\n\t\tif ( typeof newValue === 'string' ) {\n\t\t\tonChange( parseInt( newValue, 10 ) );\n\t\t\treturn;\n\t\t}\n\t\tonChange( newValue );\n\t};\n\n\treturn (\n\t\t<HStack spacing={ 4 }>\n\t\t\t<NumberControlWrapper\n\t\t\t\tmin={ min }\n\t\t\t\tmax={ max }\n\t\t\t\tlabel={ label }\n\t\t\t\thideLabelFromVision\n\t\t\t\tvalue={ value }\n\t\t\t\tonChange={ onNumberControlChange }\n\t\t\t\tprefix={\n\t\t\t\t\t<Spacer\n\t\t\t\t\t\tas={ Text }\n\t\t\t\t\t\tpaddingLeft={ space( 4 ) }\n\t\t\t\t\t\tcolor={ COLORS.theme.accent }\n\t\t\t\t\t\tlineHeight={ 1 }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ abbreviation }\n\t\t\t\t\t</Spacer>\n\t\t\t\t}\n\t\t\t\tspinControls=\"none\"\n\t\t\t\tsize=\"__unstable-large\"\n\t\t\t/>\n\t\t\t<RangeControl\n\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\tlabel={ label }\n\t\t\t\thideLabelFromVision\n\t\t\t\tmin={ min }\n\t\t\t\tmax={ max }\n\t\t\t\tvalue={ value }\n\t\t\t\t// @ts-expect-error\n\t\t\t\t// See: https://github.com/WordPress/gutenberg/pull/40535#issuecomment-1172418185\n\t\t\t\tonChange={ onChange }\n\t\t\t\twithInputField={ false }\n\t\t\t/>\n\t\t</HStack>\n\t);\n};\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AARA;AACA;AACA;;AASO,MAAMM,eAAe,GAAGA,CAAE;EAChCC,GAAG;EACHC,GAAG;EACHC,KAAK;EACLC,YAAY;EACZC,QAAQ;EACRC;AACqB,CAAC,KAAM;EAC5B,MAAMC,qBAAqB,GAAKC,QAA0B,IAAM;IAC/D,IAAK,CAAEA,QAAQ,EAAG;MACjBH,QAAQ,CAAE,CAAE,CAAC;MACb;IACD;IACA,IAAK,OAAOG,QAAQ,KAAK,QAAQ,EAAG;MACnCH,QAAQ,CAAEI,QAAQ,CAAED,QAAQ,EAAE,EAAG,CAAE,CAAC;MACpC;IACD;IACAH,QAAQ,CAAEG,QAAS,CAAC;EACrB,CAAC;EAED,OACC,IAAAE,MAAA,CAAAC,aAAA,EAAClB,OAAA,CAAAmB,MAAM;IAACC,OAAO,EAAG;EAAG,GACpB,IAAAH,MAAA,CAAAC,aAAA,EAACb,OAAA,CAAAgB,oBAAoB;IACpBb,GAAG,EAAGA,GAAK;IACXC,GAAG,EAAGA,GAAK;IACXC,KAAK,EAAGA,KAAO;IACfY,mBAAmB;IACnBT,KAAK,EAAGA,KAAO;IACfD,QAAQ,EAAGE,qBAAuB;IAClCS,MAAM,EACL,IAAAN,MAAA,CAAAC,aAAA,EAACf,OAAA,CAAAqB,MAAM;MACNC,EAAE,EAAGC,UAAM;MACXC,WAAW,EAAG,IAAAC,YAAK,EAAE,CAAE,CAAG;MAC1BC,KAAK,EAAGC,oBAAM,CAACC,KAAK,CAACC,MAAQ;MAC7BC,UAAU,EAAG;IAAG,GAEdtB,YACK,CACR;IACDuB,YAAY,EAAC,MAAM;IACnBC,IAAI,EAAC;EAAkB,CACvB,CAAC,EACF,IAAAlB,MAAA,CAAAC,aAAA,EAACb,OAAA,CAAA+B,YAAY;IACZC,uBAAuB;IACvB3B,KAAK,EAAGA,KAAO;IACfY,mBAAmB;IACnBd,GAAG,EAAGA,GAAK;IACXC,GAAG,EAAGA,GAAK;IACXI,KAAK,EAAGA;IACR;IACA;IAAA;IACAD,QAAQ,EAAGA,QAAU;IACrB0B,cAAc,EAAG;EAAO,CACxB,CACM,CAAC;AAEX,CAAC;AAACC,OAAA,CAAAhC,eAAA,GAAAA,eAAA"}