{"version": 3, "names": [], "sources": ["@wordpress/components/src/alignment-matrix-control/types.ts"], "sourcesContent": ["export type AlignmentMatrixControlValue =\n\t| 'top left'\n\t| 'top center'\n\t| 'top right'\n\t| 'center left'\n\t| 'center'\n\t| 'center center'\n\t| 'center right'\n\t| 'bottom left'\n\t| 'bottom center'\n\t| 'bottom right';\n\nexport type AlignmentMatrixControlProps = {\n\t/**\n\t * Accessible label. If provided, sets the `aria-label` attribute of the\n\t * underlying `grid` widget.\n\t *\n\t * @default 'Alignment Matrix Control'\n\t */\n\tlabel?: string;\n\t/**\n\t * If provided, sets the default alignment value.\n\t *\n\t * @default 'center center'\n\t */\n\tdefaultValue?: AlignmentMatrixControlValue;\n\t/**\n\t * The current alignment value.\n\t */\n\tvalue?: AlignmentMatrixControlValue;\n\t/**\n\t * A function that receives the updated alignment value.\n\t */\n\tonChange?: ( newValue: AlignmentMatrixControlValue ) => void;\n\t/**\n\t * If provided, sets the width of the control.\n\t *\n\t * @default 92\n\t */\n\twidth?: number;\n};\n\nexport type AlignmentMatrixControlIconProps = Pick<\n\tAlignmentMatrixControlProps,\n\t'value'\n> & {\n\tdisablePointerEvents?: boolean;\n\tsize?: number;\n};\n\nexport type AlignmentMatrixControlCellProps = {\n\tisActive?: boolean;\n\tvalue: NonNullable< AlignmentMatrixControlProps[ 'value' ] >;\n};\n"], "mappings": ""}