"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.InputWithSlider = void 0;
var _react = require("react");
var _hStack = require("../h-stack");
var _text = require("../text");
var _spacer = require("../spacer");
var _space = require("../utils/space");
var _styles = require("./styles");
var _colorsValues = require("../utils/colors-values");
/**
 * Internal dependencies
 */

const InputWithSlider = ({
  min,
  max,
  label,
  abbreviation,
  onChange,
  value
}) => {
  const onNumberControlChange = newValue => {
    if (!newValue) {
      onChange(0);
      return;
    }
    if (typeof newValue === 'string') {
      onChange(parseInt(newValue, 10));
      return;
    }
    onChange(newValue);
  };
  return (0, _react.createElement)(_hStack.HStack, {
    spacing: 4
  }, (0, _react.createElement)(_styles.NumberControlWrapper, {
    min: min,
    max: max,
    label: label,
    hideLabelFromVision: true,
    value: value,
    onChange: onNumberControlChange,
    prefix: (0, _react.createElement)(_spacer.Spacer, {
      as: _text.Text,
      paddingLeft: (0, _space.space)(4),
      color: _colorsValues.COLORS.theme.accent,
      lineHeight: 1
    }, abbreviation),
    spinControls: "none",
    size: "__unstable-large"
  }), (0, _react.createElement)(_styles.RangeControl, {
    __nextHasNoMarginBottom: true,
    label: label,
    hideLabelFromVision: true,
    min: min,
    max: max,
    value: value
    // @ts-expect-error
    // See: https://github.com/WordPress/gutenberg/pull/40535#issuecomment-1172418185
    ,
    onChange: onChange,
    withInputField: false
  }));
};
exports.InputWithSlider = InputWithSlider;
//# sourceMappingURL=input-with-slider.js.map