"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _compose = require("@wordpress/compose");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const Badge = ({
  label,
  children,
  show = true
}) => {
  return (0, _react.createElement)(_react.Fragment, null, children, (0, _react.createElement)(_reactNative.View, {
    style: _style.default.badgeContainer
  }, show && (0, _react.createElement)(_reactNative.Text, {
    style: _style.default.badge
  }, label)));
};
var _default = (0, _compose.withPreferredColorScheme)(Badge);
exports.default = _default;
//# sourceMappingURL=index.native.js.map