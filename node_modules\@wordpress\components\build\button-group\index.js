"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ButtonGroup = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function UnforwardedButtonGroup(props, ref) {
  const {
    className,
    ...restProps
  } = props;
  const classes = (0, _classnames.default)('components-button-group', className);
  return (0, _react.createElement)("div", {
    ref: ref,
    role: "group",
    className: classes,
    ...restProps
  });
}

/**
 * ButtonGroup can be used to group any related buttons together. To emphasize
 * related buttons, a group should share a common container.
 *
 * ```jsx
 * import { Button, ButtonGroup } from '@wordpress/components';
 *
 * const MyButtonGroup = () => (
 *   <ButtonGroup>
 *     <Button variant="primary">Button 1</Button>
 *     <Button variant="primary">Button 2</Button>
 *   </ButtonGroup>
 * );
 * ```
 */
const ButtonGroup = (0, _element.forwardRef)(UnforwardedButtonGroup);
exports.ButtonGroup = ButtonGroup;
var _default = ButtonGroup;
exports.default = _default;
//# sourceMappingURL=index.js.map