{"version": 3, "sources": ["image-ssim.min.js", "/source/image-ssim.min.js"], "names": ["f", "exports", "module", "define", "g", "window", "ImageSSIM", "global", "s", "this", "require", "u", "n", "a", "o", "code", "call", "l", "i", "Error", "t", "e", 1, "r", "length", "image2", "bitsPerComponent", "compare", "K2", "lumaValues1", "averageLumaValue2", "sigsqx", "image1", "windowSize", "K1", "luminance", "iteration", "lumaValues2", "averageLumaValue1", "sigxy", "sigsqy", "Math", "pow", "numPixelsInWin", "numerator", "c1", "c2", "denominator", "mssim", "mcs", "numWindows", "width", "height", "L", "Internals", "_iterate", "ssim", "Channels", "callback", "y", "x", "windowWidth", "min", "windowHeight", "_lumaValuesForWindow", "averageLuma1", "_averageLuma", "averageLuma2", "image", "array", "data", "luma<PERSON><PERSON><PERSON>", "Float32Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "counter", "maxj", "j", "offset", "channels", "maxi", "sumLuma"], "mappings": "CAAA,SCAUA,GAAG,GAAoBC,gBAAVA,UAAoC,mBAAAC,QAAaA,OAAOD,QAAQD,QAAmBG,IAASA,kBAATA,SAAqBA,OAAYA,IAAAA,UAAUH,OAAiCI,CAA8CA,GAAvDC,EAAmGD,GAA1FC,mBAATA,QAAmGA,OAAkCC,mBAAvFC,QAAuFD,OAA+DA,mBAAAA,MAA4BE,KAAiDC,KAAAL,EAAAE,UAAYI,MAAQF,WAAkD,MAAA,SAAVE,GAAAA,EAAAA,EAAAA,GAAoBA,QAAAF,GAAAG,EAAAA,GAAA,IAAAC,EAAyBC,GAAKC,CAAAA,IAAMd,EAAAA,GAAEe,CAAAA,GAAAA,GAAK,kBAAAL,UAA2BE,OAAAA,KAAAA,GAAAA,EAAAA,MAA0BI,GAAKC,GAAAA,EAAEhB,IAAAA,EAAAA,MAAQiB,GAAAJ,GAAA,EAAA,IAAAd,GAAA,GAAuBmB,OAAMP,uBAAqBX,EAAAA,IAAUmB,MAAER,GAAAA,KAAK,mBAAkEE,EAA1CI,GAAkBD,GAAAL,EAAAE,IAAAN,WAATE,GAAAA,GAAAA,GAAAA,KAAqBA,EAAgBI,QAAIA,SAAWA,GAAAA,GAerxBF,GAAAQ,EAAAN,GAfiyBI,GAAeG,EAAA,OAAAb,GAAAI,EAAAA,EAAAS,IAAHC,EAAAA,EAAAA,QAc7yBZ,EAAAA,EACWJ,EACPiB,GAAAT,MAAAU,GAAAA,GAeAV,QAhBJ,IAAA,GAgB6BW,GAcrB,kBAd4DC,UAjBpEhB,QACWJ,EACP,EAAAQ,EAAAS,EAAAC,OAeAV,IAAAN,EAAAe,EAASI,GAAgBF,OAAwBG,KAAeF,GAc5D,SAAmBG,EAA6CC,EAEjDC,GDjCvB,GAAIzB,IACJ,SAAWA,GACP,YAeA,SAASqB,GAAQK,EAAQP,EAAQQ,EAAYC,EAAIN,EAAIO,EAAWT,GAc5D,QAASU,GAAUP,EAAaQ,EAAaC,EAAmBR,GAE5D,GAAIS,GAAOR,EAAQS,CACnBD,GAAQR,EAASS,EAAS,CAC1B,KAAK,GAAItB,GAAI,EAAGA,EAAIW,EAAYL,OAAQN,IACpCa,GAAUU,KAAKC,IAAKb,EAAYX,GAAKoB,EAAoB,GACzDE,GAAUC,KAAKC,IAAKL,EAAYnB,GAAKY,EAAoB,GACzDS,IAAUV,EAAYX,GAAKoB,IAAsBD,EAAYnB,GAAKY,EAEtE,IAAIa,GAAiBd,EAAYL,OAAS,CAC1CO,IAAUY,EACVH,GAAUG,EACVJ,GAASI,CAET,IAAIC,IAAa,EAAIN,EAAoBR,EAAoBe,IAAO,EAAIN,EAAQO,GAC5EC,GAAeN,KAAKC,IAAIJ,EAAmB,GAAKG,KAAKC,IAAIZ,EAAmB,GAAKe,IAAOd,EAASS,EAASM,EAC9GE,IAASJ,EAAYG,EACrBE,IAAQ,EAAIV,EAAQO,IAAOf,EAASS,EAASM,GAC7CI,IA1BJ,GALmB,SAAfjB,IAAyBA,EAAa,GAC/B,SAAPC,IAAiBA,EAAK,KACf,SAAPN,IAAiBA,EAAK,KACR,SAAdO,IAAwBA,GAAY,GACf,SAArBT,IAA+BA,EAAmB,GAClDM,EAAOmB,QAAU1B,EAAO0B,OAASnB,EAAOoB,SAAW3B,EAAO2B,OAC1D,KAAM,IAAIjC,OAAM,+BAGpB,IAAIkC,IAAK,GAAK3B,GAAoB,EAE9BmB,EAAKJ,KAAKC,IAAKR,EAAKmB,EAAI,GAAIP,EAAKL,KAAKC,IAAKd,EAAKyB,EAAI,GAAIH,EAAa,EAAGF,EAAQ,EAChFC,EAAM,CAuBV,OADAK,GAAUC,SAASvB,EAAQP,EAAQQ,EAAYE,EAAWC,IACjDoB,KAAMR,EAAQE,EAAYD,IAAKA,EAAMC,IA/ClD,SAAWO,GACPA,EAASA,EAAe,KAAI,GAAK,OACjCA,EAASA,EAAoB,UAAI,GAAK,YACtCA,EAASA,EAAc,IAAI,GAAK,MAChCA,EAASA,EAAmB,SAAI,GAAK,YACtCnD,EAAUmD,WAAanD,EAAUmD,aACrBnD,GAAUmD,QA2CzBnD,GAAUqB,QAAUA,CAIpB,IAAI2B,IACJ,SAAWA,GACP,QAASC,GAASvB,EAAQP,EAAQQ,EAAYE,EAAWuB,GAErD,IAAK,GADDP,GAAQnB,EAAOmB,MAAOC,EAASpB,EAAOoB,OACjCO,EAAI,EAAOP,EAAJO,EAAYA,GAAK1B,EAC7B,IAAK,GAAI2B,GAAI,EAAOT,EAAJS,EAAWA,GAAK3B,EAAY,CAExC,GAAI4B,GAAcpB,KAAKqB,IAAI7B,EAAYkB,EAAQS,GAAIG,EAAetB,KAAKqB,IAAI7B,EAAYmB,EAASO,GAC5F9B,EAAcmC,EAAqBhC,EAAQ4B,EAAGD,EAAGE,EAAaE,EAAc5B,GAAYE,EAAc2B,EAAqBvC,EAAQmC,EAAGD,EAAGE,EAAaE,EAAc5B,GAAY8B,EAAeC,EAAarC,GAAcsC,EAAeD,EAAa7B,EAC1PqB,GAAS7B,EAAaQ,EAAa4B,EAAcE,IAK7D,QAASH,GAAqBI,EAAOR,EAAGD,EAAGR,EAAOC,EAAQjB,GAGtD,IAAK,GAFDkC,GAAQD,EAAME,KAAMC,EAAa,GAAIC,cAAa,GAAIC,aAAYtB,EAAQC,EAAS,IAAKsB,EAAU,EAClGC,EAAOhB,EAAIP,EACNwB,EAAIjB,EAAOgB,EAAJC,EAAUA,IAAK,CAC3B,GAAIC,GAASD,EAAIR,EAAMjB,MACnBjC,GAAK2D,EAASjB,GAAKQ,EAAMU,SACzBC,GAAQF,EAASjB,EAAIT,GAASiB,EAAMU,QACxC,QAAQV,EAAMU,UACV,IAAK,GACD,KAAWC,EAAJ7D,GAEHqD,EAAWG,KAAaL,EAAMnD,IAElC,MACJ,KAAK,GACD,KAAW6D,EAAJ7D,GACHqD,EAAWG,KAAaL,EAAMnD,MAAQmD,EAAMnD,KAAO,IAEvD,MACJ,KAAK,GACD,GAAIiB,EACA,KAAW4C,EAAJ7D,GACHqD,EAAWG,KAA2B,QAAbL,EAAMnD,KAA+B,QAAbmD,EAAMnD,KAA+B,QAAbmD,EAAMnD,SAInF,MAAW6D,EAAJ7D,GACHqD,EAAWG,KAAcL,EAAMnD,KAAOmD,EAAMnD,KAAOmD,EAAMnD,IAGjE,MACJ,KAAK,GACD,GAAIiB,EACA,KAAW4C,EAAJ7D,GACHqD,EAAWG,MAA2B,QAAbL,EAAMnD,KAA+B,QAAbmD,EAAMnD,KAA+B,QAAbmD,EAAMnD,OAAoBmD,EAAMnD,KAAO,SAIpH,MAAW6D,EAAJ7D,GACHqD,EAAWG,MAAcL,EAAMnD,KAAOmD,EAAMnD,KAAOmD,EAAMnD,OAASmD,EAAMnD,KAAO,MAMnG,MAAOqD,GAEX,QAASL,GAAaK,GAElB,IAAK,GADDS,GAAU,EACL9D,EAAI,EAAGA,EAAIqD,EAAW/C,OAAQN,IACnC8D,GAAWT,EAAWrD,EAE1B,OAAO8D,GAAUT,EAAW/C,OArDhC8B,EAAUC,SAAWA,GAuDtBD,IAAcA,QAClBhD,IAAcA,OACjBJ,EAAOD,QAAUK,YAEN,IAAI", "file": "image-ssim.min.js", "sourcesContent": ["!function(e){if(\"object\"==typeof exports&&\"undefined\"!=typeof module)module.exports=e();else if(\"function\"==typeof define&&define.amd)define([],e);else{var r;r=\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:this,r.ImageSSIM=e()}}(function(){return function e(r,n,t){function o(a,f){if(!n[a]){if(!r[a]){var u=\"function\"==typeof require&&require;if(!f&&u)return u(a,!0);if(i)return i(a,!0);var h=new Error(\"Cannot find module '\"+a+\"'\");throw h.code=\"MODULE_NOT_FOUND\",h}var s=n[a]={exports:{}};r[a][0].call(s.exports,function(e){var n=r[a][1][e];return o(n?n:e)},s,s.exports,e,r,n,t)}return n[a].exports}for(var i=\"function\"==typeof require&&require,a=0;a<t.length;a++)o(t[a]);return o}({1:[function(e,r,n){var t;!function(e){\"use strict\";function r(e,r,t,o,i,a,f){function u(e,r,n,t){var o,i,a;o=i=a=0;for(var f=0;f<e.length;f++)i+=Math.pow(e[f]-n,2),a+=Math.pow(r[f]-t,2),o+=(e[f]-n)*(r[f]-t);var u=e.length-1;i/=u,a/=u,o/=u;var h=(2*n*t+s)*(2*o+c),v=(Math.pow(n,2)+Math.pow(t,2)+s)*(i+a+c);d+=h/v,p+=(2*o+c)/(i+a+c),l++}if(void 0===t&&(t=8),void 0===o&&(o=.01),void 0===i&&(i=.03),void 0===a&&(a=!0),void 0===f&&(f=8),e.width!==r.width||e.height!==r.height)throw new Error(\"Images have different sizes!\");var h=(1<<f)-1,s=Math.pow(o*h,2),c=Math.pow(i*h,2),l=0,d=0,p=0;return n._iterate(e,r,t,a,u),{ssim:d/l,mcs:p/l}}!function(e){e[e.Grey=1]=\"Grey\",e[e.GreyAlpha=2]=\"GreyAlpha\",e[e.RGB=3]=\"RGB\",e[e.RGBAlpha=4]=\"RGBAlpha\"}(e.Channels||(e.Channels={}));e.Channels;e.compare=r;var n;!function(e){function r(e,r,o,i,a){for(var f=e.width,u=e.height,h=0;u>h;h+=o)for(var s=0;f>s;s+=o){var c=Math.min(o,f-s),l=Math.min(o,u-h),d=n(e,s,h,c,l,i),p=n(r,s,h,c,l,i),v=t(d),w=t(p);a(d,p,v,w)}}function n(e,r,n,t,o,i){for(var a=e.data,f=new Float32Array(new ArrayBuffer(t*o*4)),u=0,h=n+o,s=n;h>s;s++){var c=s*e.width,l=(c+r)*e.channels,d=(c+r+t)*e.channels;switch(e.channels){case 1:for(;d>l;)f[u++]=a[l++];break;case 2:for(;d>l;)f[u++]=a[l++]*(a[l++]/255);break;case 3:if(i)for(;d>l;)f[u++]=.212655*a[l++]+.715158*a[l++]+.072187*a[l++];else for(;d>l;)f[u++]=a[l++]+a[l++]+a[l++];break;case 4:if(i)for(;d>l;)f[u++]=(.212655*a[l++]+.715158*a[l++]+.072187*a[l++])*(a[l++]/255);else for(;d>l;)f[u++]=(a[l++]+a[l++]+a[l++])*(a[l++]/255)}}return f}function t(e){for(var r=0,n=0;n<e.length;n++)r+=e[n];return r/e.length}e._iterate=r}(n||(n={}))}(t||(t={})),r.exports=t},{}]},{},[1])(1)});\n//# sourceMappingURL=image-ssim.min.js.map", "!function(e){if(\"object\"==typeof exports&&\"undefined\"!=typeof module)module.exports=e();else if(\"function\"==typeof define&&define.amd)define([],e);else{var r;r=\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:this,r.ImageSSIM=e()}}(function(){return function e(r,n,t){function o(a,f){if(!n[a]){if(!r[a]){var u=\"function\"==typeof require&&require;if(!f&&u)return u(a,!0);if(i)return i(a,!0);var h=new Error(\"Cannot find module '\"+a+\"'\");throw h.code=\"MODULE_NOT_FOUND\",h}var s=n[a]={exports:{}};r[a][0].call(s.exports,function(e){var n=r[a][1][e];return o(n?n:e)},s,s.exports,e,r,n,t)}return n[a].exports}for(var i=\"function\"==typeof require&&require,a=0;a<t.length;a++)o(t[a]);return o}({1:[function(e,r,n){var t;!function(e){\"use strict\";function r(e,r,t,o,i,a,f){function u(e,r,n,t){var o,i,a;o=i=a=0;for(var f=0;f<e.length;f++)i+=Math.pow(e[f]-n,2),a+=Math.pow(r[f]-t,2),o+=(e[f]-n)*(r[f]-t);var u=e.length-1;i/=u,a/=u,o/=u;var h=(2*n*t+s)*(2*o+c),v=(Math.pow(n,2)+Math.pow(t,2)+s)*(i+a+c);d+=h/v,p+=(2*o+c)/(i+a+c),l++}if(void 0===t&&(t=8),void 0===o&&(o=.01),void 0===i&&(i=.03),void 0===a&&(a=!0),void 0===f&&(f=8),e.width!==r.width||e.height!==r.height)throw new Error(\"Images have different sizes!\");var h=(1<<f)-1,s=Math.pow(o*h,2),c=Math.pow(i*h,2),l=0,d=0,p=0;return n._iterate(e,r,t,a,u),{ssim:d/l,mcs:p/l}}!function(e){e[e.Grey=1]=\"Grey\",e[e.GreyAlpha=2]=\"GreyAlpha\",e[e.RGB=3]=\"RGB\",e[e.RGBAlpha=4]=\"RGBAlpha\"}(e.Channels||(e.Channels={}));e.Channels;e.compare=r;var n;!function(e){function r(e,r,o,i,a){for(var f=e.width,u=e.height,h=0;u>h;h+=o)for(var s=0;f>s;s+=o){var c=Math.min(o,f-s),l=Math.min(o,u-h),d=n(e,s,h,c,l,i),p=n(r,s,h,c,l,i),v=t(d),w=t(p);a(d,p,v,w)}}function n(e,r,n,t,o,i){for(var a=e.data,f=new Float32Array(new ArrayBuffer(t*o*4)),u=0,h=n+o,s=n;h>s;s++){var c=s*e.width,l=(c+r)*e.channels,d=(c+r+t)*e.channels;switch(e.channels){case 1:for(;d>l;)f[u++]=a[l++];break;case 2:for(;d>l;)f[u++]=a[l++]*(a[l++]/255);break;case 3:if(i)for(;d>l;)f[u++]=.212655*a[l++]+.715158*a[l++]+.072187*a[l++];else for(;d>l;)f[u++]=a[l++]+a[l++]+a[l++];break;case 4:if(i)for(;d>l;)f[u++]=(.212655*a[l++]+.715158*a[l++]+.072187*a[l++])*(a[l++]/255);else for(;d>l;)f[u++]=(a[l++]+a[l++]+a[l++])*(a[l++]/255)}}return f}function t(e){for(var r=0,n=0;n<e.length;n++)r+=e[n];return r/e.length}e._iterate=r}(n||(n={}))}(t||(t={})),r.exports=t},{}]},{},[1])(1)});\n//# sourceMappingURL=image-ssim.min.js.map"], "sourceRoot": "/source/"}