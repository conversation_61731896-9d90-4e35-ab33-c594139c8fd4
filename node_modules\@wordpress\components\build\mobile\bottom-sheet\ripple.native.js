"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _compose = require("@wordpress/compose");
var _rippleNative = _interopRequireDefault(require("./ripple.native.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const ANDROID_VERSION_LOLLIPOP = 21;
const ANDROID_VERSION_PIE = 28;
const TouchableRipple = ({
  style,
  onPress,
  disabled: disabledProp,
  children,
  activeOpacity,
  getStylesFromColorScheme,
  borderless = false,
  ...touchableProps
}) => {
  const isTouchableNativeSupported = _reactNative.Platform.OS === 'android' && _reactNative.Platform.Version >= ANDROID_VERSION_LOLLIPOP;
  const disabled = disabledProp || !onPress;
  const rippleColor = getStylesFromColorScheme(_rippleNative.default.ripple, _rippleNative.default.rippleDark).backgroundColor;
  if (isTouchableNativeSupported) {
    // A workaround for ripple on Android P is to use useForeground + overflow: 'hidden'
    // https://github.com/facebook/react-native/issues/6480
    const useForeground = _reactNative.Platform.OS === 'android' && _reactNative.Platform.Version >= ANDROID_VERSION_PIE && borderless;
    return (0, _react.createElement)(_reactNative.TouchableNativeFeedback, {
      ...touchableProps,
      onPress: onPress,
      disabled: disabled,
      useForeground: useForeground,
      background: _reactNative.TouchableNativeFeedback.Ripple(rippleColor, borderless)
    }, (0, _react.createElement)(_reactNative.View, {
      style: [borderless && _rippleNative.default.overflow, style]
    }, children));
  }
  return (0, _react.createElement)(_reactNative.TouchableOpacity, {
    ...touchableProps,
    onPress: onPress,
    disabled: disabled,
    activeOpacity: activeOpacity,
    style: style
  }, children);
};
var _default = (0, _compose.withPreferredColorScheme)(TouchableRipple);
exports.default = _default;
//# sourceMappingURL=ripple.native.js.map