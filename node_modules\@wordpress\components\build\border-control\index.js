"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "BorderControl", {
  enumerable: true,
  get: function () {
    return _component.default;
  }
});
Object.defineProperty(exports, "useBorderControl", {
  enumerable: true,
  get: function () {
    return _hook.useBorderControl;
  }
});
var _component = _interopRequireDefault(require("./border-control/component"));
var _hook = require("./border-control/hook");
//# sourceMappingURL=index.js.map