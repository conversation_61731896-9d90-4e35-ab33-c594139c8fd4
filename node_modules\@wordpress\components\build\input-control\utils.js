"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getDragCursor = getDragCursor;
exports.useDraft = useDraft;
exports.useDragCursor = useDragCursor;
var _element = require("@wordpress/element");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Gets a CSS cursor value based on a drag direction.
 *
 * @param dragDirection The drag direction.
 * @return  The CSS cursor value.
 */
function getDragCursor(dragDirection) {
  let dragCursor = 'ns-resize';
  switch (dragDirection) {
    case 'n':
    case 's':
      dragCursor = 'ns-resize';
      break;
    case 'e':
    case 'w':
      dragCursor = 'ew-resize';
      break;
  }
  return dragCursor;
}

/**
 * Custom hook that renders a drag cursor when dragging.
 *
 * @param {boolean} isDragging    The dragging state.
 * @param {string}  dragDirection The drag direction.
 *
 * @return {string} The CSS cursor value.
 */
function useDragCursor(isDragging, dragDirection) {
  const dragCursor = getDragCursor(dragDirection);
  (0, _element.useEffect)(() => {
    if (isDragging) {
      document.documentElement.style.cursor = dragCursor;
    } else {
      // @ts-expect-error
      document.documentElement.style.cursor = null;
    }
  }, [isDragging, dragCursor]);
  return dragCursor;
}
function useDraft(props) {
  const refPreviousValue = (0, _element.useRef)(props.value);
  const [draft, setDraft] = (0, _element.useState)({});
  const value = draft.value !== undefined ? draft.value : props.value;

  // Determines when to discard the draft value to restore controlled status.
  // To do so, it tracks the previous value and marks the draft value as stale
  // after each render.
  (0, _element.useLayoutEffect)(() => {
    const {
      current: previousValue
    } = refPreviousValue;
    refPreviousValue.current = props.value;
    if (draft.value !== undefined && !draft.isStale) setDraft({
      ...draft,
      isStale: true
    });else if (draft.isStale && props.value !== previousValue) setDraft({});
  }, [props.value, draft]);
  const onChange = (nextValue, extra) => {
    // Mutates the draft value to avoid an extra effect run.
    setDraft(current => Object.assign(current, {
      value: nextValue,
      isStale: false
    }));
    props.onChange(nextValue, extra);
  };
  const onBlur = event => {
    setDraft({});
    props.onBlur?.(event);
  };
  return {
    value,
    onBlur,
    onChange
  };
}
//# sourceMappingURL=utils.js.map