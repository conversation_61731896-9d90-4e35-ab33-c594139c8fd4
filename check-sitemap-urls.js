/**
 * Script pour vérifier toutes les URLs de sitemaps Boss SEO
 * À exécuter dans la console du navigateur
 */

console.log('🗺️ Vérification des URLs de Sitemaps Boss SEO');
console.log('===============================================');

// URLs des sitemaps à vérifier
const sitemapUrls = [
    // Sitemap principal
    { url: '/sitemap.xml', name: 'Sitemap Principal (Index)', type: 'index' },
    
    // Sitemaps par type de contenu
    { url: '/sitemap-post.xml', name: 'Articles', type: 'content' },
    { url: '/sitemap-page.xml', name: 'Pages', type: 'content' },
    { url: '/sitemap-category.xml', name: 'Catégories', type: 'taxonomy' },
    { url: '/sitemap-post_tag.xml', name: '<PERSON>ti<PERSON><PERSON>', type: 'taxonomy' },
    
    // Sitemaps spécialisés
    { url: '/sitemap-image.xml', name: 'Images', type: 'specialized' },
    { url: '/sitemap-video.xml', name: 'Vidéos', type: 'specialized' },
    { url: '/sitemap-news.xml', name: 'Actualités', type: 'specialized' },
    { url: '/sitemap-stories.xml', name: 'Web Stories', type: 'specialized' },
    
    // Sitemaps personnalisés
    { url: '/sitemap-custom.xml', name: 'URLs Personnalisées', type: 'custom' },
    
    // Robots.txt
    { url: '/robots.txt', name: 'Robots.txt', type: 'robots' }
];

// Fonction pour vérifier une URL de sitemap
async function checkSitemapUrl(sitemapInfo) {
    const fullUrl = window.location.origin + sitemapInfo.url;
    
    try {
        const response = await fetch(fullUrl, {
            method: 'HEAD', // Utiliser HEAD pour vérifier sans télécharger
            cache: 'no-cache'
        });
        
        const contentType = response.headers.get('content-type') || '';
        const contentLength = response.headers.get('content-length') || 'Inconnu';
        const lastModified = response.headers.get('last-modified') || 'Inconnu';
        
        return {
            available: response.ok,
            status: response.status,
            contentType: contentType,
            size: contentLength,
            lastModified: lastModified,
            url: fullUrl
        };
    } catch (error) {
        return {
            available: false,
            error: error.message,
            url: fullUrl
        };
    }
}

// Fonction pour analyser le contenu XML d'un sitemap
async function analyzeSitemapContent(sitemapInfo) {
    const fullUrl = window.location.origin + sitemapInfo.url;
    
    try {
        const response = await fetch(fullUrl);
        if (!response.ok) return null;
        
        const xmlText = await response.text();
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
        
        // Compter les URLs
        const urls = xmlDoc.querySelectorAll('url');
        const sitemaps = xmlDoc.querySelectorAll('sitemap'); // Pour sitemap index
        
        // Vérifier les erreurs de parsing
        const parseError = xmlDoc.querySelector('parsererror');
        
        return {
            isValid: !parseError,
            urlCount: urls.length,
            sitemapCount: sitemaps.length,
            isIndex: sitemaps.length > 0,
            hasUrls: urls.length > 0,
            xmlSize: xmlText.length,
            parseError: parseError ? parseError.textContent : null
        };
    } catch (error) {
        return {
            isValid: false,
            error: error.message
        };
    }
}

// Fonction pour vérifier tous les sitemaps
async function checkAllSitemaps() {
    console.log('\n🔍 Vérification de la disponibilité des sitemaps...\n');
    
    const results = {};
    
    for (const sitemapInfo of sitemapUrls) {
        console.log(`📋 Vérification: ${sitemapInfo.name}`);
        
        // Vérifier la disponibilité
        const availability = await checkSitemapUrl(sitemapInfo);
        
        // Analyser le contenu si disponible
        let content = null;
        if (availability.available && sitemapInfo.type !== 'robots') {
            content = await analyzeSitemapContent(sitemapInfo);
        }
        
        results[sitemapInfo.name] = {
            info: sitemapInfo,
            availability: availability,
            content: content
        };
        
        // Afficher le résultat
        if (availability.available) {
            console.log(`✅ ${sitemapInfo.name}: Disponible`);
            console.log(`   📍 URL: ${availability.url}`);
            console.log(`   📊 Statut: ${availability.status}`);
            console.log(`   📦 Type: ${availability.contentType}`);
            console.log(`   📏 Taille: ${availability.size} bytes`);
            
            if (content) {
                if (content.isValid) {
                    if (content.isIndex) {
                        console.log(`   🗂️  Sitemap Index: ${content.sitemapCount} sitemaps`);
                    } else {
                        console.log(`   🔗 URLs: ${content.urlCount}`);
                    }
                } else {
                    console.log(`   ❌ XML invalide: ${content.parseError || content.error}`);
                }
            }
        } else {
            console.log(`❌ ${sitemapInfo.name}: Indisponible (${availability.status || 'Erreur'})`);
            if (availability.error) {
                console.log(`   🚨 Erreur: ${availability.error}`);
            }
        }
        console.log('');
    }
    
    return results;
}

// Fonction pour générer un rapport détaillé
function generateSitemapReport(results) {
    console.log('\n📊 === RAPPORT DÉTAILLÉ DES SITEMAPS ===\n');
    
    const available = Object.values(results).filter(r => r.availability.available);
    const unavailable = Object.values(results).filter(r => !r.availability.available);
    
    console.log(`📈 Statistiques Générales:`);
    console.log(`   ✅ Sitemaps disponibles: ${available.length}`);
    console.log(`   ❌ Sitemaps indisponibles: ${unavailable.length}`);
    console.log(`   📊 Total: ${Object.keys(results).length}`);
    
    // Analyser par type
    const byType = {};
    Object.values(results).forEach(result => {
        const type = result.info.type;
        if (!byType[type]) byType[type] = { available: 0, total: 0 };
        byType[type].total++;
        if (result.availability.available) byType[type].available++;
    });
    
    console.log(`\n📋 Par Type de Sitemap:`);
    Object.entries(byType).forEach(([type, stats]) => {
        const percentage = Math.round((stats.available / stats.total) * 100);
        console.log(`   ${type}: ${stats.available}/${stats.total} (${percentage}%)`);
    });
    
    // URLs totales
    let totalUrls = 0;
    available.forEach(result => {
        if (result.content && result.content.urlCount) {
            totalUrls += result.content.urlCount;
        }
    });
    
    console.log(`\n🔗 URLs Totales dans les Sitemaps: ${totalUrls}`);
    
    // Recommandations
    console.log(`\n💡 Recommandations:`);
    
    if (unavailable.length > 0) {
        console.log(`   🔧 Corriger les ${unavailable.length} sitemaps indisponibles`);
    }
    
    if (totalUrls === 0) {
        console.log(`   📝 Ajouter du contenu pour générer des URLs dans les sitemaps`);
    }
    
    const invalidXml = available.filter(r => r.content && !r.content.isValid);
    if (invalidXml.length > 0) {
        console.log(`   🔍 Corriger ${invalidXml.length} sitemaps avec XML invalide`);
    }
    
    if (available.length === Object.keys(results).length) {
        console.log(`   🎉 Tous les sitemaps sont opérationnels !`);
    }
    
    return {
        total: Object.keys(results).length,
        available: available.length,
        unavailable: unavailable.length,
        totalUrls: totalUrls,
        byType: byType
    };
}

// Fonction pour tester la soumission aux moteurs de recherche
async function testSearchEngineSubmission() {
    console.log('\n🔔 === TEST SOUMISSION MOTEURS DE RECHERCHE ===\n');
    
    try {
        // Tester le ping via l'API Boss SEO
        const response = await fetch('/wp-json/boss-seo/v1/robots-sitemap/ping/all', {
            method: 'POST',
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ API de ping disponible');
            
            if (result.success && result.results) {
                console.log('📊 Résultats du ping:');
                Object.entries(result.results.engines || {}).forEach(([engine, engineResult]) => {
                    console.log(`   ${engine}: ${engineResult.success ? '✅ Succès' : '❌ Échec'}`);
                    if (engineResult.message) {
                        console.log(`      Message: ${engineResult.message}`);
                    }
                });
            }
        } else {
            console.log('❌ API de ping indisponible');
        }
    } catch (error) {
        console.log('❌ Erreur lors du test de ping:', error.message);
    }
}

// Fonction pour vérifier robots.txt
async function checkRobotsTxt() {
    console.log('\n🤖 === VÉRIFICATION ROBOTS.TXT ===\n');
    
    try {
        const response = await fetch('/robots.txt');
        if (response.ok) {
            const robotsContent = await response.text();
            console.log('✅ robots.txt disponible');
            
            // Vérifier si les sitemaps sont référencés
            const sitemapReferences = robotsContent.match(/Sitemap:\s*(.*)/gi) || [];
            console.log(`📋 Références de sitemaps trouvées: ${sitemapReferences.length}`);
            
            sitemapReferences.forEach(ref => {
                console.log(`   ${ref}`);
            });
            
            if (sitemapReferences.length === 0) {
                console.log('⚠️ Aucune référence de sitemap dans robots.txt');
                console.log('💡 Recommandation: Ajouter "Sitemap: ' + window.location.origin + '/sitemap.xml"');
            }
        } else {
            console.log('❌ robots.txt indisponible');
        }
    } catch (error) {
        console.log('❌ Erreur lors de la vérification robots.txt:', error.message);
    }
}

// Fonction principale
async function runSitemapCheck() {
    console.log('🚀 Démarrage de la vérification complète des sitemaps...\n');
    
    const startTime = Date.now();
    
    try {
        // Vérifier tous les sitemaps
        const results = await checkAllSitemaps();
        
        // Générer le rapport
        const report = generateSitemapReport(results);
        
        // Tester la soumission
        await testSearchEngineSubmission();
        
        // Vérifier robots.txt
        await checkRobotsTxt();
        
        const endTime = Date.now();
        
        console.log('\n🎯 === RÉSUMÉ FINAL ===');
        console.log(`⏱️  Durée: ${endTime - startTime}ms`);
        console.log(`📊 Sitemaps: ${report.available}/${report.total} disponibles`);
        console.log(`🔗 URLs totales: ${report.totalUrls}`);
        
        if (report.available === report.total && report.totalUrls > 0) {
            console.log('\n🎉 TOUS LES SITEMAPS SONT OPÉRATIONNELS !');
            console.log('✅ Votre site est prêt pour l\'indexation par les moteurs de recherche');
        } else {
            console.log('\n⚠️ QUELQUES AMÉLIORATIONS NÉCESSAIRES');
            console.log('🔧 Consultez les recommandations ci-dessus');
        }
        
        return results;
        
    } catch (error) {
        console.log('\n💥 Erreur lors de la vérification:', error);
        return null;
    }
}

// Rendre les fonctions disponibles globalement
window.checkSitemaps = runSitemapCheck;
window.checkSitemapUrl = checkSitemapUrl;

console.log('\n🛠️ Fonctions disponibles:');
console.log('- checkSitemaps() - Vérification complète de tous les sitemaps');
console.log('- checkSitemapUrl({url: "/sitemap.xml", name: "Test"}) - Vérifier une URL spécifique');
console.log('\n🎯 Pour commencer, tapez: checkSitemaps()');
