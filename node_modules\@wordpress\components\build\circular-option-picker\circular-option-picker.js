"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _compose = require("@wordpress/compose");
var _i18n = require("@wordpress/i18n");
var _circularOptionPickerContext = require("./circular-option-picker-context");
var _v = require("../composite/v2");
var _circularOptionPickerOption = require("./circular-option-picker-option");
var _circularOptionPickerOptionGroup = require("./circular-option-picker-option-group");
var _circularOptionPickerActions = require("./circular-option-picker-actions");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 *`CircularOptionPicker` is a component that displays a set of options as circular buttons.
 *
 * ```jsx
 * import { CircularOptionPicker } from '../circular-option-picker';
 * import { useState } from '@wordpress/element';
 *
 * const Example = () => {
 * 	const [ currentColor, setCurrentColor ] = useState();
 * 	const colors = [
 * 		{ color: '#f00', name: 'Red' },
 * 		{ color: '#0f0', name: 'Green' },
 * 		{ color: '#00f', name: 'Blue' },
 * 	];
 * 	const colorOptions = (
 * 		<>
 * 			{ colors.map( ( { color, name }, index ) => {
 * 				return (
 * 					<CircularOptionPicker.Option
 * 						key={ `${ color }-${ index }` }
 * 						tooltipText={ name }
 * 						style={ { backgroundColor: color, color } }
 * 						isSelected={ index === currentColor }
 * 						onClick={ () => setCurrentColor( index ) }
 * 						aria-label={ name }
 * 					/>
 * 				);
 * 			} ) }
 * 		</>
 * 	);
 * 	return (
 * 		<CircularOptionPicker
 * 				options={ colorOptions }
 * 				actions={
 * 					<CircularOptionPicker.ButtonAction
 * 						onClick={ () => setCurrentColor( undefined ) }
 * 					>
 * 						{ 'Clear' }
 * 					</CircularOptionPicker.ButtonAction>
 * 				}
 * 			/>
 * 	);
 * };
 * ```
 */

function ListboxCircularOptionPicker(props) {
  const {
    actions,
    options,
    baseId,
    className,
    loop = true,
    children,
    ...additionalProps
  } = props;
  const compositeStore = (0, _v.useCompositeStore)({
    focusLoop: loop,
    rtl: (0, _i18n.isRTL)()
  });
  const compositeContext = {
    baseId,
    compositeStore
  };
  return (0, _react.createElement)("div", {
    className: className
  }, (0, _react.createElement)(_circularOptionPickerContext.CircularOptionPickerContext.Provider, {
    value: compositeContext
  }, (0, _react.createElement)(_v.Composite, {
    ...additionalProps,
    id: baseId,
    store: compositeStore,
    role: 'listbox'
  }, options), children, actions));
}
function ButtonsCircularOptionPicker(props) {
  const {
    actions,
    options,
    children,
    baseId,
    ...additionalProps
  } = props;
  return (0, _react.createElement)("div", {
    ...additionalProps,
    id: baseId
  }, (0, _react.createElement)(_circularOptionPickerContext.CircularOptionPickerContext.Provider, {
    value: {
      baseId
    }
  }, options, children, actions));
}
function CircularOptionPicker(props) {
  const {
    asButtons,
    actions: actionsProp,
    options: optionsProp,
    children,
    className,
    ...additionalProps
  } = props;
  const baseId = (0, _compose.useInstanceId)(CircularOptionPicker, 'components-circular-option-picker', additionalProps.id);
  const OptionPickerImplementation = asButtons ? ButtonsCircularOptionPicker : ListboxCircularOptionPicker;
  const actions = actionsProp ? (0, _react.createElement)("div", {
    className: "components-circular-option-picker__custom-clear-wrapper"
  }, actionsProp) : undefined;
  const options = (0, _react.createElement)("div", {
    className: 'components-circular-option-picker__swatches'
  }, optionsProp);
  return (0, _react.createElement)(OptionPickerImplementation, {
    ...additionalProps,
    baseId: baseId,
    className: (0, _classnames.default)('components-circular-option-picker', className),
    actions: actions,
    options: options
  }, children);
}
CircularOptionPicker.Option = _circularOptionPickerOption.Option;
CircularOptionPicker.OptionGroup = _circularOptionPickerOptionGroup.OptionGroup;
CircularOptionPicker.ButtonAction = _circularOptionPickerActions.ButtonAction;
CircularOptionPicker.DropdownLinkAction = _circularOptionPickerActions.DropdownLinkAction;
var _default = CircularOptionPicker;
exports.default = _default;
//# sourceMappingURL=circular-option-picker.js.map