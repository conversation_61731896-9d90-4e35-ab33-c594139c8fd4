"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _native = require("@react-navigation/native");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _components = require("@wordpress/components");
var _getPxFromCssUnit = _interopRequireDefault(require("../mobile/utils/get-px-from-css-unit"));
var _unitControl = _interopRequireWildcard(require("../unit-control"));
var _style = _interopRequireDefault(require("./style.scss"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const DEFAULT_FONT_SIZE = 16;
function FontSizePicker({
  fontSizes = [],
  disableCustomFontSizes = false,
  onChange,
  value: selectedValue
}) {
  var _fontSizes$find;
  const [showSubSheet, setShowSubSheet] = (0, _element.useState)(false);
  const navigation = (0, _native.useNavigation)();
  const {
    height,
    width
  } = (0, _reactNative.useWindowDimensions)();
  const cssUnitOptions = {
    height,
    width,
    fontSize: DEFAULT_FONT_SIZE
  };
  // We need to always convert to px units because the selected value
  // could be coming from the web where it could be stored as a different unit.
  const selectedPxValue = (0, _getPxFromCssUnit.default)(selectedValue, cssUnitOptions);
  const onChangeValue = value => {
    return () => {
      goBack();
      onChange(value);
    };
  };
  const selectedOption = (_fontSizes$find = fontSizes.find(option => option.sizePx === selectedPxValue)) !== null && _fontSizes$find !== void 0 ? _fontSizes$find : {
    name: 'Custom'
  };
  const goBack = () => {
    setShowSubSheet(false);
    navigation.goBack();
  };
  const openSubSheet = () => {
    navigation.navigate(_components.BottomSheet.SubSheet.screenName);
    setShowSubSheet(true);
  };
  const label = (0, _i18n.__)('Font Size');
  const units = (0, _unitControl.useCustomUnits)({
    availableUnits: ['px', 'em', 'rem']
  });
  const accessibilityLabel = (0, _i18n.sprintf)(
  // translators: %1$s: Font size name e.g. Small
  (0, _i18n.__)('Font Size, %1$s'), selectedOption.name);
  return (0, _react.createElement)(_components.BottomSheet.SubSheet, {
    navigationButton: (0, _react.createElement)(_components.BottomSheet.Cell, {
      label: label,
      separatorType: "none",
      value: selectedValue ? (0, _i18n.sprintf)(
      // translators: %1$s: Select control font size name e.g. Small, %2$s: Select control font size e.g. 12px
      (0, _i18n.__)('%1$s (%2$s)'), selectedOption.name, selectedPxValue) : (0, _i18n.__)('Default'),
      onPress: openSubSheet,
      accessibilityRole: 'button',
      accessibilityLabel: accessibilityLabel,
      accessibilityHint: (0, _i18n.sprintf)(
      // translators: %s: Select control button label e.g. Small
      (0, _i18n.__)('Navigates to select %s'), selectedOption.name)
    }, (0, _react.createElement)(_icons.Icon, {
      icon: _icons.chevronRight
    })),
    showSheet: showSubSheet
  }, (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_components.BottomSheet.NavBar, null, (0, _react.createElement)(_components.BottomSheet.NavBar.BackButton, {
    onPress: goBack
  }), (0, _react.createElement)(_components.BottomSheet.NavBar.Heading, null, label)), (0, _react.createElement)(_reactNative.View, {
    style: _style.default['components-font-size-picker']
  }, (0, _react.createElement)(_components.BottomSheet.Cell, {
    customActionButton: true,
    separatorType: "none",
    label: (0, _i18n.__)('Default'),
    onPress: onChangeValue(undefined),
    leftAlign: true,
    key: 'default',
    accessibilityRole: 'button',
    accessibilityLabel: (0, _i18n.__)('Selected: Default'),
    accessibilityHint: (0, _i18n.__)('Double tap to select default font size')
  }, (0, _react.createElement)(_reactNative.View, null, selectedValue === undefined && (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check
  }))), fontSizes.map((item, index) => {
    // Only display a choice that we can currenly select.
    if (!parseFloat(item.sizePx)) {
      return null;
    }
    return (0, _react.createElement)(_components.BottomSheet.Cell, {
      customActionButton: true,
      separatorType: "none",
      label: item.name,
      subLabel: item.sizePx,
      onPress: onChangeValue(item.sizePx),
      leftAlign: true,
      key: index,
      accessibilityRole: 'button',
      accessibilityLabel: item.sizePx === selectedValue ? (0, _i18n.sprintf)(
      // translators: %s: Select font size option value e.g: "Selected: Large".
      (0, _i18n.__)('Selected: %s'), item.name) : item.name,
      accessibilityHint: (0, _i18n.__)('Double tap to select font size')
    }, (0, _react.createElement)(_reactNative.View, null, item.sizePx === selectedPxValue && (0, _react.createElement)(_icons.Icon, {
      icon: _icons.check
    })));
  }), !disableCustomFontSizes && (0, _react.createElement)(_unitControl.default, {
    label: (0, _i18n.__)('Custom'),
    min: 0,
    max: 200,
    step: 1,
    value: selectedValue,
    onChange: nextSize => {
      if (0 === parseFloat(nextSize) || !nextSize) {
        onChange(undefined);
      } else {
        onChange(nextSize);
      }
    },
    units: units
  }))));
}
var _default = FontSizePicker;
exports.default = _default;
//# sourceMappingURL=index.native.js.map