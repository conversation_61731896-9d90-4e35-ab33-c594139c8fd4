{"version": 3, "names": ["Ariakit", "_interopRequireWildcard", "require", "_react2", "_utils", "_space", "_icon", "_interopRequireDefault", "_truncate", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "_EMOTION_STRINGIFIED_CSS_ERROR__", "ANIMATION_PARAMS", "SLIDE_AMOUNT", "DURATION", "EASING", "CONTENT_WRAPPER_PADDING", "space", "ITEM_PADDING_BLOCK", "ITEM_PADDING_INLINE", "DEFAULT_BORDER_COLOR", "COLORS", "gray", "DIVIDER_COLOR", "TOOLBAR_VARIANT_BORDER_COLOR", "DEFAULT_BOX_SHADOW", "CONFIG", "borderWidth", "popoverShadow", "TOOLBAR_VARIANT_BOX_SHADOW", "GRID_TEMPLATE_COLS", "slideUpAndFade", "keyframes", "opacity", "transform", "slideRightAndFade", "slideDownAndFade", "slideLeftAndFade", "DropdownMenu", "_base", "<PERSON><PERSON>", "process", "env", "NODE_ENV", "target", "label", "ui", "background", "props", "css", "variant", "exports", "baseItem", "font", "radiusBlockUi", "textDisabled", "theme", "accent", "white", "DropdownMenuItem", "MenuItem", "DropdownMenuCheckboxItem", "MenuItemCheckbox", "DropdownMenuRadioItem", "MenuItemRadio", "ItemPrefixWrapper", "DropdownMenuItemContentWrapper", "DropdownMenuItemChildrenWrapper", "ItemSuffixWrapper", "DropdownMenuGroup", "MenuGroup", "name", "styles", "map", "toString", "DropdownMenuSeparator", "MenuSeparator", "SubmenuChevronIcon", "Icon", "rtl", "DropdownMenuItemLabel", "Truncate", "DropdownMenuItemHelpText"], "sources": ["@wordpress/components/src/dropdown-menu-v2/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport * as Ariakit from '@ariakit/react';\nimport { css, keyframes } from '@emotion/react';\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport { COLORS, font, rtl, CONFIG } from '../utils';\nimport { space } from '../utils/space';\nimport Icon from '../icon';\nimport { Truncate } from '../truncate';\nimport type { DropdownMenuContext } from './types';\n\nconst ANIMATION_PARAMS = {\n\tSLIDE_AMOUNT: '2px',\n\tDURATION: '400ms',\n\tEASING: 'cubic-bezier( 0.16, 1, 0.3, 1 )',\n};\n\nconst CONTENT_WRAPPER_PADDING = space( 1 );\nconst ITEM_PADDING_BLOCK = space( 2 );\nconst ITEM_PADDING_INLINE = space( 3 );\n\n// TODO:\n// - those values are different from saved variables?\n// - should bring this into the config, and make themeable\n// - border color and divider color are different?\nconst DEFAULT_BORDER_COLOR = COLORS.gray[ 300 ];\nconst DIVIDER_COLOR = COLORS.gray[ 200 ];\nconst TOOLBAR_VARIANT_BORDER_COLOR = COLORS.gray[ '900' ];\nconst DEFAULT_BOX_SHADOW = `0 0 0 ${ CONFIG.borderWidth } ${ DEFAULT_BORDER_COLOR }, ${ CONFIG.popoverShadow }`;\nconst TOOLBAR_VARIANT_BOX_SHADOW = `0 0 0 ${ CONFIG.borderWidth } ${ TOOLBAR_VARIANT_BORDER_COLOR }`;\n\nconst GRID_TEMPLATE_COLS = 'minmax( 0, max-content ) 1fr';\n\nconst slideUpAndFade = keyframes( {\n\t'0%': {\n\t\topacity: 0,\n\t\ttransform: `translateY(${ ANIMATION_PARAMS.SLIDE_AMOUNT })`,\n\t},\n\t'100%': { opacity: 1, transform: 'translateY(0)' },\n} );\n\nconst slideRightAndFade = keyframes( {\n\t'0%': {\n\t\topacity: 0,\n\t\ttransform: `translateX(-${ ANIMATION_PARAMS.SLIDE_AMOUNT })`,\n\t},\n\t'100%': { opacity: 1, transform: 'translateX(0)' },\n} );\n\nconst slideDownAndFade = keyframes( {\n\t'0%': {\n\t\topacity: 0,\n\t\ttransform: `translateY(-${ ANIMATION_PARAMS.SLIDE_AMOUNT })`,\n\t},\n\t'100%': { opacity: 1, transform: 'translateY(0)' },\n} );\n\nconst slideLeftAndFade = keyframes( {\n\t'0%': {\n\t\topacity: 0,\n\t\ttransform: `translateX(${ ANIMATION_PARAMS.SLIDE_AMOUNT })`,\n\t},\n\t'100%': { opacity: 1, transform: 'translateX(0)' },\n} );\n\nexport const DropdownMenu = styled( Ariakit.Menu )<\n\tPick< DropdownMenuContext, 'variant' >\n>`\n\tposition: relative;\n\t/* Same as popover component */\n\t/* TODO: is there a way to read the sass variable? */\n\tz-index: 1000000;\n\n\tdisplay: grid;\n\tgrid-template-columns: ${ GRID_TEMPLATE_COLS };\n\tgrid-template-rows: auto;\n\n\tbox-sizing: border-box;\n\tmin-width: 160px;\n\tmax-width: 320px;\n\tmax-height: var( --popover-available-height );\n\tpadding: ${ CONTENT_WRAPPER_PADDING };\n\n\tbackground-color: ${ COLORS.ui.background };\n\tborder-radius: 4px;\n\t${ ( props ) => css`\n\t\tbox-shadow: ${ props.variant === 'toolbar'\n\t\t\t? TOOLBAR_VARIANT_BOX_SHADOW\n\t\t\t: DEFAULT_BOX_SHADOW };\n\t` }\n\n\toverscroll-behavior: contain;\n\toverflow: auto;\n\n\t/* Only visible in Windows High Contrast mode */\n\toutline: 2px solid transparent !important;\n\n\t/* Animation */\n\tanimation-duration: ${ ANIMATION_PARAMS.DURATION };\n\tanimation-timing-function: ${ ANIMATION_PARAMS.EASING };\n\twill-change: transform, opacity;\n\t/* Default animation.*/\n\tanimation-name: ${ slideDownAndFade };\n\n\t&[data-side='right'] {\n\t\tanimation-name: ${ slideLeftAndFade };\n\t}\n\t&[data-side='bottom'] {\n\t\tanimation-name: ${ slideUpAndFade };\n\t}\n\t&[data-side='left'] {\n\t\tanimation-name: ${ slideRightAndFade };\n\t}\n\t@media ( prefers-reduced-motion ) {\n\t\tanimation-duration: 0s;\n\t}\n`;\n\nconst baseItem = css`\n\tall: unset;\n\n\tposition: relative;\n\tmin-height: ${ space( 10 ) };\n\tbox-sizing: border-box;\n\n\t/* Occupy the width of all grid columns (ie. full width) */\n\tgrid-column: 1 / -1;\n\n\tdisplay: grid;\n\tgrid-template-columns: ${ GRID_TEMPLATE_COLS };\n\talign-items: center;\n\n\t@supports ( grid-template-columns: subgrid ) {\n\t\t/*\n\t\t * Define a grid layout which inherits the same columns configuration\n\t\t * from the parent layout (ie. subgrid). This allows the menu\n\t\t * to synchronize the indentation of all its items.\n\t\t */\n\t\tgrid-template-columns: subgrid;\n\t}\n\n\tfont-size: ${ font( 'default.fontSize' ) };\n\tfont-family: inherit;\n\tfont-weight: normal;\n\tline-height: 20px;\n\n\tcolor: ${ COLORS.gray[ 900 ] };\n\tborder-radius: ${ CONFIG.radiusBlockUi };\n\n\tpadding-block: ${ ITEM_PADDING_BLOCK };\n\tpadding-inline: ${ ITEM_PADDING_INLINE };\n\n\t/*\n\t * Make sure that, when an item is scrolled into view (eg. while using the\n\t * keyboard to move focus), the whole item comes into view\n\t */\n\tscroll-margin: ${ CONTENT_WRAPPER_PADDING };\n\n\tuser-select: none;\n\toutline: none;\n\n\t&[aria-disabled='true'] {\n\t\tcolor: ${ COLORS.ui.textDisabled };\n\t\tcursor: not-allowed;\n\t}\n\n\t/* Hover */\n\t&[data-active-item]:not( [data-focus-visible] ):not(\n\t\t\t[aria-disabled='true']\n\t\t) {\n\t\tbackground-color: ${ COLORS.theme.accent };\n\t\tcolor: ${ COLORS.white };\n\t}\n\n\t/* Keyboard focus (focus-visible) */\n\t&[data-focus-visible] {\n\t\tbox-shadow: 0 0 0 1.5px var( --wp-admin-theme-color );\n\n\t\t/* Only visible in Windows High Contrast mode */\n\t\toutline: 2px solid transparent;\n\t}\n\n\t/* Active (ie. pressed, mouse down) */\n\t&:active,\n\t&[data-active] {\n\t\t/* TODO: should there be a visual active state? */\n\t}\n\n\t/* When the item is the trigger of an open submenu */\n\t${ DropdownMenu }:not(:focus) &:not(:focus)[aria-expanded=\"true\"] {\n\t\tbackground-color: ${ COLORS.gray[ 100 ] };\n\t\tcolor: ${ COLORS.gray[ 900 ] };\n\t}\n\n\tsvg {\n\t\tfill: currentColor;\n\t}\n`;\n\nexport const DropdownMenuItem = styled( Ariakit.MenuItem )`\n\t${ baseItem };\n`;\n\nexport const DropdownMenuCheckboxItem = styled( Ariakit.MenuItemCheckbox )`\n\t${ baseItem };\n`;\n\nexport const DropdownMenuRadioItem = styled( Ariakit.MenuItemRadio )`\n\t${ baseItem };\n`;\n\nexport const ItemPrefixWrapper = styled.span`\n\t/* Always occupy the first column, even when auto-collapsing */\n\tgrid-column: 1;\n\n\t/*\n\t * Even when the item is not checked, occupy the same screen space to avoid\n\t * the space collapside when no items are checked.\n\t */\n\t${ DropdownMenuCheckboxItem } > &,\n\t${ DropdownMenuRadioItem } > & {\n\t\t/* Same width as the check icons */\n\t\tmin-width: ${ space( 6 ) };\n\t}\n\n\t${ DropdownMenuCheckboxItem } > &,\n\t${ DropdownMenuRadioItem } > &,\n\t&:not( :empty ) {\n\t\tmargin-inline-end: ${ space( 2 ) };\n\t}\n\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\n\tcolor: ${ COLORS.gray[ '700' ] };\n\n\t/*\n\t* When the parent menu item is active, except when it's a non-focused/hovered\n\t* submenu trigger (in that case, color should not be inherited)\n\t*/\n\t[data-active-item]:not( [data-focus-visible] ) > &,\n\t/* When the parent menu item is disabled */\n\t[aria-disabled='true'] > & {\n\t\tcolor: inherit;\n\t}\n`;\n\nexport const DropdownMenuItemContentWrapper = styled.div`\n\t/*\n\t * Always occupy the second column, since the first column\n\t * is taken by the prefix wrapper (when displayed).\n\t */\n\tgrid-column: 2;\n\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tgap: ${ space( 3 ) };\n\n\tpointer-events: none;\n`;\n\nexport const DropdownMenuItemChildrenWrapper = styled.div`\n\tflex: 1;\n\n\tdisplay: inline-flex;\n\tflex-direction: column;\n\tgap: ${ space( 1 ) };\n`;\n\nexport const ItemSuffixWrapper = styled.span`\n\tflex: 0 1 fit-content;\n\tmin-width: 0;\n\twidth: fit-content;\n\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: ${ space( 3 ) };\n\n\tcolor: ${ COLORS.gray[ '700' ] };\n\n\t/*\n\t * When the parent menu item is active, except when it's a non-focused/hovered\n\t * submenu trigger (in that case, color should not be inherited)\n\t */\n\t[data-active-item]:not( [data-focus-visible] ) *:not(${ DropdownMenu }) &,\n\t/* When the parent menu item is disabled */\n\t[aria-disabled='true'] *:not(${ DropdownMenu }) & {\n\t\tcolor: inherit;\n\t}\n`;\n\nexport const DropdownMenuGroup = styled( Ariakit.MenuGroup )`\n\t/* Ignore this element when calculating the layout. Useful for subgrid */\n\tdisplay: contents;\n`;\n\nexport const DropdownMenuSeparator = styled( Ariakit.MenuSeparator )<\n\tPick< DropdownMenuContext, 'variant' >\n>`\n\t/* Occupy the width of all grid columns (ie. full width) */\n\tgrid-column: 1 / -1;\n\n\tborder: none;\n\theight: ${ CONFIG.borderWidth };\n\tbackground-color: ${ ( props ) =>\n\t\tprops.variant === 'toolbar'\n\t\t\t? TOOLBAR_VARIANT_BORDER_COLOR\n\t\t\t: DIVIDER_COLOR };\n\t/* Align with menu items' content */\n\tmargin-block: ${ space( 2 ) };\n\tmargin-inline: ${ ITEM_PADDING_INLINE };\n\n\t/* Only visible in Windows High Contrast mode */\n\toutline: 2px solid transparent;\n`;\n\nexport const SubmenuChevronIcon = styled( Icon )`\n\twidth: ${ space( 1.5 ) };\n\t${ rtl(\n\t\t{\n\t\t\ttransform: `scaleX(1)`,\n\t\t},\n\t\t{\n\t\t\ttransform: `scaleX(-1)`,\n\t\t}\n\t) };\n`;\n\nexport const DropdownMenuItemLabel = styled( Truncate )`\n\tfont-size: ${ font( 'default.fontSize' ) };\n\tline-height: 20px;\n\tcolor: inherit;\n`;\n\nexport const DropdownMenuItemHelpText = styled( Truncate )`\n\tfont-size: ${ font( 'helpText.fontSize' ) };\n\tline-height: 16px;\n\tcolor: ${ COLORS.gray[ '700' ] };\n\n\t[data-active-item]:not( [data-focus-visible] ) *:not( ${ DropdownMenu } ) &,\n\t[aria-disabled='true'] *:not( ${ DropdownMenu } ) & {\n\t\tcolor: inherit;\n\t}\n`;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAMA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,SAAA,GAAAN,OAAA;AAAuC,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAT,wBAAAa,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAW,iCAAA;AAGvC,MAAMC,gBAAgB,GAAG;EACxBC,YAAY,EAAE,KAAK;EACnBC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE;AACT,CAAC;AAED,MAAMC,uBAAuB,GAAG,IAAAC,YAAK,EAAE,CAAE,CAAC;AAC1C,MAAMC,kBAAkB,GAAG,IAAAD,YAAK,EAAE,CAAE,CAAC;AACrC,MAAME,mBAAmB,GAAG,IAAAF,YAAK,EAAE,CAAE,CAAC;;AAEtC;AACA;AACA;AACA;AACA,MAAMG,oBAAoB,GAAGC,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE;AAC/C,MAAMC,aAAa,GAAGF,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE;AACxC,MAAME,4BAA4B,GAAGH,aAAM,CAACC,IAAI,CAAE,KAAK,CAAE;AACzD,MAAMG,kBAAkB,GAAI,SAASC,aAAM,CAACC,WAAa,IAAIP,oBAAsB,KAAKM,aAAM,CAACE,aAAe,EAAC;AAC/G,MAAMC,0BAA0B,GAAI,SAASH,aAAM,CAACC,WAAa,IAAIH,4BAA8B,EAAC;AAEpG,MAAMM,kBAAkB,GAAG,8BAA8B;AAEzD,MAAMC,cAAc,GAAG,IAAAC,iBAAS,EAAE;EACjC,IAAI,EAAE;IACLC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAG,cAActB,gBAAgB,CAACC,YAAc;EAC1D,CAAC;EACD,MAAM,EAAE;IAAEoB,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAgB;AAClD,CAAE,CAAC;AAEH,MAAMC,iBAAiB,GAAG,IAAAH,iBAAS,EAAE;EACpC,IAAI,EAAE;IACLC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAG,eAAetB,gBAAgB,CAACC,YAAc;EAC3D,CAAC;EACD,MAAM,EAAE;IAAEoB,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAgB;AAClD,CAAE,CAAC;AAEH,MAAME,gBAAgB,GAAG,IAAAJ,iBAAS,EAAE;EACnC,IAAI,EAAE;IACLC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAG,eAAetB,gBAAgB,CAACC,YAAc;EAC3D,CAAC;EACD,MAAM,EAAE;IAAEoB,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAgB;AAClD,CAAE,CAAC;AAEH,MAAMG,gBAAgB,GAAG,IAAAL,iBAAS,EAAE;EACnC,IAAI,EAAE;IACLC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAG,cAActB,gBAAgB,CAACC,YAAc;EAC1D,CAAC;EACD,MAAM,EAAE;IAAEoB,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAgB;AAClD,CAAE,CAAC;AAEI,MAAMI,YAAY,GAAG,kBAAAC,KAAA,CAAA3C,OAAA,EAAQhB,OAAO,CAAC4D,IAAI,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,0EASvBf,kBAAkB,0IAOhCd,uBAAuB,wBAEdK,aAAM,CAACyB,EAAE,CAACC,UAAU,yBAEpCC,KAAK,qBAAMC,WAAG,iBACHD,KAAK,CAACE,OAAO,KAAK,SAAS,GACvCrB,0BAA0B,GAC1BJ,kBAAkB,SAAAgB,OAAA,CAAAC,GAAA,CAAAC,QAAA,kDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,uxZACrB,4GASsB/B,gBAAgB,CAACE,QAAQ,iCAClBF,gBAAgB,CAACG,MAAM,oDAGlCqB,gBAAgB,2CAGfC,gBAAgB,6CAGhBN,cAAc,2CAGdI,iBAAiB,mEAAAM,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAKrC;AAACQ,OAAA,CAAAb,YAAA,GAAAA,YAAA;AAEF,MAAMc,QAAQ,oBAAGH,WAAG,6CAIJ,IAAAhC,YAAK,EAAE,EAAG,CAAC,iFAOAa,kBAAkB,gHAY9B,IAAAuB,WAAI,EAAE,kBAAmB,CAAC,qEAK9BhC,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,qBACVI,aAAM,CAAC4B,aAAa,qBAEpBpC,kBAAkB,sBACjBC,mBAAmB,qBAMpBH,uBAAuB,mEAM9BK,aAAM,CAACyB,EAAE,CAACS,YAAY,sIAQXlC,aAAM,CAACmC,KAAK,CAACC,MAAM,aAC9BpC,aAAM,CAACqC,KAAK,2IAkBpBpB,YAAY,0EACOjB,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,aAC7BD,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,iCAAAmB,OAAA,CAAAC,GAAA,CAAAC,QAAA,8CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,uxZAM7B;AAEM,MAAMgB,gBAAgB,GAAG,kBAAApB,KAAA,CAAA3C,OAAA,EAAQhB,OAAO,CAACgF,QAAQ,EAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CACtDO,QAAQ,SAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZACX;AAACQ,OAAA,CAAAQ,gBAAA,GAAAA,gBAAA;AAEK,MAAME,wBAAwB,GAAG,kBAAAtB,KAAA,CAAA3C,OAAA,EAAQhB,OAAO,CAACkF,gBAAgB,EAAArB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CACtEO,QAAQ,SAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZACX;AAACQ,OAAA,CAAAU,wBAAA,GAAAA,wBAAA;AAEK,MAAME,qBAAqB,GAAG,kBAAAxB,KAAA,CAAA3C,OAAA,EAAQhB,OAAO,CAACoF,aAAa,EAAAvB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAChEO,QAAQ,SAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZACX;AAACQ,OAAA,CAAAY,qBAAA,GAAAA,qBAAA;AAEK,MAAME,iBAAiB,OAAA1B,KAAA,CAAA3C,OAAA,UAAA6C,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,qBAQ1BgB,wBAAwB,SACxBE,qBAAqB,mBAET,IAAA9C,YAAK,EAAE,CAAE,CAAC,QAGtB4C,wBAAwB,SACxBE,qBAAqB,2CAED,IAAA9C,YAAK,EAAE,CAAE,CAAC,qEAOvBI,aAAM,CAACC,IAAI,CAAE,KAAK,CAAE,kGAAAmB,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAW9B;AAACQ,OAAA,CAAAc,iBAAA,GAAAA,iBAAA;AAEK,MAAMC,8BAA8B,OAAA3B,KAAA,CAAA3C,OAAA,SAAA6C,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,uFAUlC,IAAA5B,YAAK,EAAE,CAAE,CAAC,6BAAAwB,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAGlB;AAACQ,OAAA,CAAAe,8BAAA,GAAAA,8BAAA;AAEK,MAAMC,+BAA+B,OAAA5B,KAAA,CAAA3C,OAAA,SAAA6C,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,4DAKnC,IAAA5B,YAAK,EAAE,CAAE,CAAC,SAAAwB,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAClB;AAACQ,OAAA,CAAAgB,+BAAA,GAAAA,+BAAA;AAEK,MAAMC,iBAAiB,OAAA7B,KAAA,CAAA3C,OAAA,UAAA6C,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,qHAQrB,IAAA5B,YAAK,EAAE,CAAE,CAAC,aAERI,aAAM,CAACC,IAAI,CAAE,KAAK,CAAE,4DAM0BgB,YAAY,uCAEpCA,YAAY,2BAAAG,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAG5C;AAACQ,OAAA,CAAAiB,iBAAA,GAAAA,iBAAA;AAEK,MAAMC,iBAAiB,GAAG,kBAAA9B,KAAA,CAAA3C,OAAA,EAAQhB,OAAO,CAAC0F,SAAS,EAAA7B,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAA4B,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAA/D;AAAA,EAG3D;AAACwC,OAAA,CAAAkB,iBAAA,GAAAA,iBAAA;AAEK,MAAMM,qBAAqB,GAAG,kBAAApC,KAAA,CAAA3C,OAAA,EAAQhB,OAAO,CAACgG,aAAa,EAAAnC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,yCAOxDnB,aAAM,CAACC,WAAW,wBACNqB,KAAK,IAC3BA,KAAK,CAACE,OAAO,KAAK,SAAS,GACxB1B,4BAA4B,GAC5BD,aAAa,oBAEA,IAAAN,YAAK,EAAE,CAAE,CAAC,qBACTE,mBAAmB,uCAAAsB,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAIrC;AAACQ,OAAA,CAAAwB,qBAAA,GAAAA,qBAAA;AAEK,MAAME,kBAAkB,GAAG,kBAAAtC,KAAA,CAAA3C,OAAA,EAAQkF,aAAI,EAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,WACrC,IAAA5B,YAAK,EAAE,GAAI,CAAC,OACnB,IAAA8D,UAAG,EACL;EACC7C,SAAS,EAAG;AACb,CAAC,EACD;EACCA,SAAS,EAAG;AACb,CACD,CAAC,SAAAO,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZACD;AAACQ,OAAA,CAAA0B,kBAAA,GAAAA,kBAAA;AAEK,MAAMG,qBAAqB,GAAG,kBAAAzC,KAAA,CAAA3C,OAAA,EAAQqF,kBAAQ,EAAAxC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,eACxC,IAAAQ,WAAI,EAAE,kBAAmB,CAAC,wCAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAGxC;AAACQ,OAAA,CAAA6B,qBAAA,GAAAA,qBAAA;AAEK,MAAME,wBAAwB,GAAG,kBAAA3C,KAAA,CAAA3C,OAAA,EAAQqF,kBAAQ,EAAAxC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,eAC3C,IAAAQ,WAAI,EAAE,mBAAoB,CAAC,8BAE/BhC,aAAM,CAACC,IAAI,CAAE,KAAK,CAAE,6DAE2BgB,YAAY,yCACpCA,YAAY,4BAAAG,OAAA,CAAAC,GAAA,CAAAC,QAAA,wxZAG7C;AAACQ,OAAA,CAAA+B,wBAAA,GAAAA,wBAAA"}