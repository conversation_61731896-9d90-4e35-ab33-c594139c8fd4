{"version": 3, "names": ["_reactNative", "require", "_compose", "_style", "_interopRequireDefault", "Badge", "label", "children", "show", "_react", "createElement", "Fragment", "View", "style", "styles", "badgeContainer", "Text", "badge", "_default", "withPreferredColorScheme", "exports", "default"], "sources": ["@wordpress/components/src/mobile/badge/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, Text } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { withPreferredColorScheme } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\n\nconst Badge = ( { label, children, show = true } ) => {\n\treturn (\n\t\t<>\n\t\t\t{ children }\n\t\t\t<View style={ styles.badgeContainer }>\n\t\t\t\t{ show && <Text style={ styles.badge }>{ label }</Text> }\n\t\t\t</View>\n\t\t</>\n\t);\n};\n\nexport default withPreferredColorScheme( Badge );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAbA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAGA,MAAMI,KAAK,GAAGA,CAAE;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,IAAI,GAAG;AAAK,CAAC,KAAM;EACrD,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGJ,QAAQ,EACV,IAAAE,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAY,IAAI;IAACC,KAAK,EAAGC,cAAM,CAACC;EAAgB,GAClCP,IAAI,IAAI,IAAAC,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAgB,IAAI;IAACH,KAAK,EAAGC,cAAM,CAACG;EAAO,GAAGX,KAAa,CACjD,CACL,CAAC;AAEL,CAAC;AAAC,IAAAY,QAAA,GAEa,IAAAC,iCAAwB,EAAEd,KAAM,CAAC;AAAAe,OAAA,CAAAC,OAAA,GAAAH,QAAA"}