{"version": 3, "names": [], "sources": ["@wordpress/components/src/item-group/types.ts"], "sourcesContent": ["type ItemSize = 'small' | 'medium' | 'large';\n\nexport interface ItemGroupProps {\n\t/**\n\t * Renders a border around the itemgroup.\n\t *\n\t * @default false\n\t */\n\tisBordered?: boolean;\n\t/**\n\t * Renders with rounded corners.\n\t *\n\t * @default true\n\t */\n\tisRounded?: boolean;\n\t/**\n\t * Renders a separator between each item.\n\t *\n\t * @default false\n\t */\n\tisSeparated?: boolean;\n\t/**\n\t * Determines the amount of padding within the component.\n\t *\n\t * @default 'medium'\n\t */\n\tsize?: ItemSize;\n\t/**\n\t * The children elements.\n\t */\n\tchildren: React.ReactNode;\n}\n\nexport interface ItemProps {\n\t/**\n\t * Determines the amount of padding within the component.\n\t *\n\t * @default 'medium'\n\t */\n\tsize?: ItemSize;\n\t/**\n\t * The children elements.\n\t */\n\tchildren: React.ReactNode;\n}\n\nexport type ItemGroupContext = {\n\t/**\n\t * When true, each `Item` will be styled as an individual item (e.g. with rounded\n\t * borders), instead of being part of the same UI block with the rest of the items.\n\t *\n\t * @default false\n\t */\n\tspacedAround: boolean;\n\t/**\n\t * Determines the amount of padding within the component.\n\t *\n\t * @default 'medium'\n\t */\n\tsize: ItemSize;\n};\n"], "mappings": ""}