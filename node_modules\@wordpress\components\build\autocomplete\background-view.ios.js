"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _blur = require("@react-native-community/blur");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const BackgroundView = ({
  children
}) => {
  return (0, _react.createElement)(_blur.BlurView, {
    style: _style.default['components-autocomplete__background-blur'],
    blurType: "prominent",
    blurAmount: 10
  }, children);
};
var _default = BackgroundView;
exports.default = _default;
//# sourceMappingURL=background-view.ios.js.map