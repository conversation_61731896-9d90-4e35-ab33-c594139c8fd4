{"version": 3, "names": ["_react2", "require", "_element", "_context", "_elevation", "_view", "styles", "_interopRequireWildcard", "_hook", "_configValues", "_interopRequireDefault", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "UnconnectedCard", "props", "forwardedRef", "children", "elevation", "isBorderless", "isRounded", "size", "otherProps", "useCard", "elevationBorderRadius", "CONFIG", "cardBorderRadius", "cx", "useCx", "elevationClassName", "useMemo", "css", "borderRadius", "process", "env", "NODE_ENV", "contextProviderValue", "contextProps", "CardBody", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "_react", "createElement", "ContextSystemProvider", "value", "View", "ref", "className", "Content", "Elevation", "isInteractive", "Card", "contextConnect", "exports", "_default"], "sources": ["@wordpress/components/src/card/card/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect, ContextSystemProvider } from '../../context';\nimport { Elevation } from '../../elevation';\nimport { View } from '../../view';\nimport * as styles from '../styles';\nimport { useCard } from './hook';\nimport CONFIG from '../../utils/config-values';\nimport { useCx } from '../../utils/hooks/use-cx';\nimport type { Props } from '../types';\n\nfunction UnconnectedCard(\n\tprops: WordPressComponentProps< Props, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst {\n\t\tchildren,\n\t\televation,\n\t\tisBorderless,\n\t\tisRounded,\n\t\tsize,\n\t\t...otherProps\n\t} = useCard( props );\n\tconst elevationBorderRadius = isRounded ? CONFIG.cardBorderRadius : 0;\n\n\tconst cx = useCx();\n\n\tconst elevationClassName = useMemo(\n\t\t() => cx( css( { borderRadius: elevationBorderRadius } ) ),\n\t\t[ cx, elevationBorderRadius ]\n\t);\n\n\tconst contextProviderValue = useMemo( () => {\n\t\tconst contextProps = {\n\t\t\tsize,\n\t\t\tisBorderless,\n\t\t};\n\t\treturn {\n\t\t\tCardBody: contextProps,\n\t\t\tCardHeader: contextProps,\n\t\t\tCardFooter: contextProps,\n\t\t};\n\t}, [ isBorderless, size ] );\n\n\treturn (\n\t\t<ContextSystemProvider value={ contextProviderValue }>\n\t\t\t<View { ...otherProps } ref={ forwardedRef }>\n\t\t\t\t<View className={ cx( styles.Content ) }>{ children }</View>\n\t\t\t\t<Elevation\n\t\t\t\t\tclassName={ elevationClassName }\n\t\t\t\t\tisInteractive={ false }\n\t\t\t\t\tvalue={ elevation ? 1 : 0 }\n\t\t\t\t/>\n\t\t\t\t<Elevation\n\t\t\t\t\tclassName={ elevationClassName }\n\t\t\t\t\tisInteractive={ false }\n\t\t\t\t\tvalue={ elevation }\n\t\t\t\t/>\n\t\t\t</View>\n\t\t</ContextSystemProvider>\n\t);\n}\n\n/**\n * `Card` provides a flexible and extensible content container.\n * `Card` also provides a convenient set of sub-components such as `CardBody`,\n * `CardHeader`, `CardFooter`, and more.\n *\n * ```jsx\n * import {\n *   Card,\n *   CardHeader,\n *   CardBody,\n *   CardFooter,\n *   __experimentalText as Text,\n *   __experimentalHeading as Heading,\n * } from `@wordpress/components`;\n *\n * function Example() {\n *   return (\n *     <Card>\n *       <CardHeader>\n *         <Heading level={ 4 }>Card Title</Heading>\n *       </CardHeader>\n *       <CardBody>\n *         <Text>Card Content</Text>\n *       </CardBody>\n *       <CardFooter>\n *         <Text>Card Footer</Text>\n *       </CardFooter>\n *     </Card>\n *   );\n * }\n * ```\n */\nexport const Card = contextConnect( UnconnectedCard, 'Card' );\n\nexport default Card;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AAMA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAC,uBAAA,CAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAC,sBAAA,CAAAT,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AAAiD,SAAAW,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAN,wBAAAU,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AArBjD;AACA;AACA;;AAIA;AACA;AACA;;AAgBA,SAASW,eAAeA,CACvBC,KAA8C,EAC9CC,YAAiC,EAChC;EACD,MAAM;IACLC,QAAQ;IACRC,SAAS;IACTC,YAAY;IACZC,SAAS;IACTC,IAAI;IACJ,GAAGC;EACJ,CAAC,GAAG,IAAAC,aAAO,EAAER,KAAM,CAAC;EACpB,MAAMS,qBAAqB,GAAGJ,SAAS,GAAGK,qBAAM,CAACC,gBAAgB,GAAG,CAAC;EAErE,MAAMC,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,kBAAkB,GAAG,IAAAC,gBAAO,EACjC,MAAMH,EAAE,eAAE,IAAAI,WAAG,EAAE;IAAEC,YAAY,EAAER;EAAsB,CAAC,EAAAS,OAAA,CAAAC,GAAA,CAAAC,QAAA,uDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,siIAAC,CAAE,CAAC,EAC1D,CAAER,EAAE,EAAEH,qBAAqB,CAC5B,CAAC;EAED,MAAMY,oBAAoB,GAAG,IAAAN,gBAAO,EAAE,MAAM;IAC3C,MAAMO,YAAY,GAAG;MACpBhB,IAAI;MACJF;IACD,CAAC;IACD,OAAO;MACNmB,QAAQ,EAAED,YAAY;MACtBE,UAAU,EAAEF,YAAY;MACxBG,UAAU,EAAEH;IACb,CAAC;EACF,CAAC,EAAE,CAAElB,YAAY,EAAEE,IAAI,CAAG,CAAC;EAE3B,OACC,IAAAoB,MAAA,CAAAC,aAAA,EAAC3D,QAAA,CAAA4D,qBAAqB;IAACC,KAAK,EAAGR;EAAsB,GACpD,IAAAK,MAAA,CAAAC,aAAA,EAACzD,KAAA,CAAA4D,IAAI;IAAA,GAAMvB,UAAU;IAAGwB,GAAG,EAAG9B;EAAc,GAC3C,IAAAyB,MAAA,CAAAC,aAAA,EAACzD,KAAA,CAAA4D,IAAI;IAACE,SAAS,EAAGpB,EAAE,CAAEzC,MAAM,CAAC8D,OAAQ;EAAG,GAAG/B,QAAgB,CAAC,EAC5D,IAAAwB,MAAA,CAAAC,aAAA,EAAC1D,UAAA,CAAAiE,SAAS;IACTF,SAAS,EAAGlB,kBAAoB;IAChCqB,aAAa,EAAG,KAAO;IACvBN,KAAK,EAAG1B,SAAS,GAAG,CAAC,GAAG;EAAG,CAC3B,CAAC,EACF,IAAAuB,MAAA,CAAAC,aAAA,EAAC1D,UAAA,CAAAiE,SAAS;IACTF,SAAS,EAAGlB,kBAAoB;IAChCqB,aAAa,EAAG,KAAO;IACvBN,KAAK,EAAG1B;EAAW,CACnB,CACI,CACgB,CAAC;AAE1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMiC,IAAI,GAAG,IAAAC,uBAAc,EAAEtC,eAAe,EAAE,MAAO,CAAC;AAACuC,OAAA,CAAAF,IAAA,GAAAA,IAAA;AAAA,IAAAG,QAAA,GAE/CH,IAAI;AAAAE,OAAA,CAAAtD,OAAA,GAAAuD,QAAA"}