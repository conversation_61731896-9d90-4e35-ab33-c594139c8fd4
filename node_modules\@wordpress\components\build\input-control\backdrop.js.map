{"version": 3, "names": ["_element", "require", "_inputControlStyles", "Backdrop", "disabled", "isFocused", "_react", "createElement", "BackdropUI", "className", "MemoizedBackdrop", "memo", "_default", "exports", "default"], "sources": ["@wordpress/components/src/input-control/backdrop.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { memo } from '@wordpress/element';\n/**\n * Internal dependencies\n */\nimport { BackdropUI } from './styles/input-control-styles';\n\nfunction Backdrop( { disabled = false, isFocused = false } ) {\n\treturn (\n\t\t<BackdropUI\n\t\t\taria-hidden=\"true\"\n\t\t\tclassName=\"components-input-control__backdrop\"\n\t\t\tdisabled={ disabled }\n\t\t\tisFocused={ isFocused }\n\t\t/>\n\t);\n}\n\nconst MemoizedBackdrop = memo( Backdrop );\n\nexport default MemoizedBackdrop;\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAIA,IAAAC,mBAAA,GAAAD,OAAA;AAPA;AACA;AACA;;AAEA;AACA;AACA;;AAGA,SAASE,QAAQA,CAAE;EAAEC,QAAQ,GAAG,KAAK;EAAEC,SAAS,GAAG;AAAM,CAAC,EAAG;EAC5D,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACL,mBAAA,CAAAM,UAAU;IACV,eAAY,MAAM;IAClBC,SAAS,EAAC,oCAAoC;IAC9CL,QAAQ,EAAGA,QAAU;IACrBC,SAAS,EAAGA;EAAW,CACvB,CAAC;AAEJ;AAEA,MAAMK,gBAAgB,GAAG,IAAAC,aAAI,EAAER,QAAS,CAAC;AAAC,IAAAS,QAAA,GAE3BF,gBAAgB;AAAAG,OAAA,CAAAC,OAAA,GAAAF,QAAA"}