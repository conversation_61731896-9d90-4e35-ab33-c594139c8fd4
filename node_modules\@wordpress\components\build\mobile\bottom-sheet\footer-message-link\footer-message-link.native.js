"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _compose = require("@wordpress/compose");
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function FooterMessageLink({
  href,
  value
}) {
  const textStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.footerMessageLink, _styles.default.footerMessageLinkDark);
  return (0, _react.createElement)(_reactNative.Text, {
    style: textStyle,
    onPress: () => _reactNative.Linking.openURL(href)
  }, value);
}
var _default = FooterMessageLink;
exports.default = _default;
//# sourceMappingURL=footer-message-link.native.js.map