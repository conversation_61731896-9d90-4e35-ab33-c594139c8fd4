# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.8.1](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.8.1) (2019-06-28)

**Note:** Version bump only for package intl-messageformat-parser

# [1.8.0](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.8.0) (2019-06-27)

### Features

- **intl-messageformat:** allow passing in formatters ([#107](https://github.com/formatjs/formatjs/issues/107)) ([3605693](https://github.com/formatjs/formatjs/commit/3605693))

## [1.7.1](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.7.1) (2019-06-26)

### Bug Fixes

- **intl-messageformat-parser:** Escape double-' to a single ' ([#103](https://github.com/formatjs/formatjs/issues/103)) ([4d0cd1f](https://github.com/formatjs/formatjs/commit/4d0cd1f))

# [1.7.0](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.7.0) (2019-06-27)

### Features

- **intl-utils:** Add intl-utils ([#98](https://github.com/formatjs/formatjs/issues/98)) ([2329c57](https://github.com/formatjs/formatjs/commit/2329c57))

## [1.6.8](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.6.8) (2019-06-18)

**Note:** Version bump only for package intl-messageformat-parser

## [1.6.7](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.6.7) (2019-06-18)

**Note:** Version bump only for package intl-messageformat-parser

## [1.6.6](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.6.6) (2019-06-12)

**Note:** Version bump only for package intl-messageformat-parser

## [1.6.5](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.6.5) (2019-06-03)

**Note:** Version bump only for package intl-messageformat-parser

## [1.6.4](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.6.4) (2019-06-03)

**Note:** Version bump only for package intl-messageformat-parser

## [1.6.3](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.6.3) (2019-05-28)

**Note:** Version bump only for package intl-messageformat-parser

## [1.6.2](https://github.com/formatjs/formatjs/compare/<EMAIL>-messageformat-parser@1.6.2) (2019-05-28)
