{"version": 3, "names": ["_compose", "require", "_tooltip", "_interopRequireDefault", "_utils", "_utils2", "_boxControlStyles", "noop", "BoxInputControls", "__next40pxDefaultSize", "onChange", "onFocus", "values", "selected<PERSON><PERSON><PERSON>", "setSelectedUnits", "sides", "props", "generatedId", "useInstanceId", "createHandleOnFocus", "side", "event", "handleOnChange", "nextV<PERSON>ues", "handleOnValueChange", "next", "extra", "isNumeric", "undefined", "isNaN", "parseFloat", "nextValue", "altKey", "bottom", "top", "right", "left", "createHandleOnUnitChange", "newUnits", "filteredSides", "length", "ALL_SIDES", "filter", "includes", "_react", "createElement", "Fragment", "map", "_CUSTOM_VALUE_SETTING", "_CUSTOM_VALUE_SETTING2", "parsedQuantity", "parsedUnit", "parseQuantityAndUnitFromRawValue", "computedUnit", "inputId", "join", "InputWrapper", "key", "expanded", "FlexedBoxControlIcon", "default", "placement", "text", "LABELS", "StyledUnitControl", "className", "id", "isPressEnterToChange", "value", "onUnitChange", "label", "hideLabelFromVision", "FlexedRangeControl", "__nextHasNoMarginBottom", "newValue", "min", "max", "CUSTOM_VALUE_SETTINGS", "step", "withInputField"], "sources": ["@wordpress/components/src/box-control/input-controls.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\n/**\n * Internal dependencies\n */\nimport Tooltip from '../tooltip';\nimport { parseQuantityAndUnitFromRawValue } from '../unit-control/utils';\nimport { ALL_SIDES, CUSTOM_VALUE_SETTINGS, LABELS } from './utils';\nimport {\n\tFlexedBoxControlIcon,\n\tFlexedRangeControl,\n\tInputWrapper,\n\tStyledUnitControl,\n} from './styles/box-control-styles';\nimport type { BoxControlInputControlProps, BoxControlValue } from './types';\n\nconst noop = () => {};\n\nexport default function BoxInputControls( {\n\t__next40pxDefaultSize,\n\tonChange = noop,\n\tonFocus = noop,\n\tvalues,\n\tselectedUnits,\n\tsetSelectedUnits,\n\tsides,\n\t...props\n}: BoxControlInputControlProps ) {\n\tconst generatedId = useInstanceId( BoxInputControls, 'box-control-input' );\n\n\tconst createHandleOnFocus =\n\t\t( side: keyof BoxControlValue ) =>\n\t\t( event: React.FocusEvent< HTMLInputElement > ) => {\n\t\t\tonFocus( event, { side } );\n\t\t};\n\n\tconst handleOnChange = ( nextValues: BoxControlValue ) => {\n\t\tonChange( nextValues );\n\t};\n\n\tconst handleOnValueChange = (\n\t\tside: keyof BoxControlValue,\n\t\tnext?: string,\n\t\textra?: { event: React.SyntheticEvent< Element, Event > }\n\t) => {\n\t\tconst nextValues = { ...values };\n\t\tconst isNumeric = next !== undefined && ! isNaN( parseFloat( next ) );\n\t\tconst nextValue = isNumeric ? next : undefined;\n\n\t\tnextValues[ side ] = nextValue;\n\n\t\t/**\n\t\t * Supports changing pair sides. For example, holding the ALT key\n\t\t * when changing the TOP will also update BOTTOM.\n\t\t */\n\t\t// @ts-expect-error - TODO: event.altKey is only present when the change event was\n\t\t// triggered by a keyboard event. Should this feature be implemented differently so\n\t\t// it also works with drag events?\n\t\tif ( extra?.event.altKey ) {\n\t\t\tswitch ( side ) {\n\t\t\t\tcase 'top':\n\t\t\t\t\tnextValues.bottom = nextValue;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'bottom':\n\t\t\t\t\tnextValues.top = nextValue;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'left':\n\t\t\t\t\tnextValues.right = nextValue;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'right':\n\t\t\t\t\tnextValues.left = nextValue;\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\thandleOnChange( nextValues );\n\t};\n\n\tconst createHandleOnUnitChange =\n\t\t( side: keyof BoxControlValue ) => ( next?: string ) => {\n\t\t\tconst newUnits = { ...selectedUnits };\n\t\t\tnewUnits[ side ] = next;\n\t\t\tsetSelectedUnits( newUnits );\n\t\t};\n\n\t// Filter sides if custom configuration provided, maintaining default order.\n\tconst filteredSides = sides?.length\n\t\t? ALL_SIDES.filter( ( side ) => sides.includes( side ) )\n\t\t: ALL_SIDES;\n\n\treturn (\n\t\t<>\n\t\t\t{ filteredSides.map( ( side ) => {\n\t\t\t\tconst [ parsedQuantity, parsedUnit ] =\n\t\t\t\t\tparseQuantityAndUnitFromRawValue( values[ side ] );\n\n\t\t\t\tconst computedUnit = values[ side ]\n\t\t\t\t\t? parsedUnit\n\t\t\t\t\t: selectedUnits[ side ];\n\n\t\t\t\tconst inputId = [ generatedId, side ].join( '-' );\n\n\t\t\t\treturn (\n\t\t\t\t\t<InputWrapper key={ `box-control-${ side }` } expanded>\n\t\t\t\t\t\t<FlexedBoxControlIcon side={ side } sides={ sides } />\n\t\t\t\t\t\t<Tooltip placement=\"top-end\" text={ LABELS[ side ] }>\n\t\t\t\t\t\t\t<StyledUnitControl\n\t\t\t\t\t\t\t\t{ ...props }\n\t\t\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t\t\t\tclassName=\"component-box-control__unit-control\"\n\t\t\t\t\t\t\t\tid={ inputId }\n\t\t\t\t\t\t\t\tisPressEnterToChange\n\t\t\t\t\t\t\t\tvalue={ [ parsedQuantity, computedUnit ].join(\n\t\t\t\t\t\t\t\t\t''\n\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\tonChange={ ( nextValue, extra ) =>\n\t\t\t\t\t\t\t\t\thandleOnValueChange(\n\t\t\t\t\t\t\t\t\t\tside,\n\t\t\t\t\t\t\t\t\t\tnextValue,\n\t\t\t\t\t\t\t\t\t\textra\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tonUnitChange={ createHandleOnUnitChange(\n\t\t\t\t\t\t\t\t\tside\n\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\tonFocus={ createHandleOnFocus( side ) }\n\t\t\t\t\t\t\t\tlabel={ LABELS[ side ] }\n\t\t\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</Tooltip>\n\n\t\t\t\t\t\t<FlexedRangeControl\n\t\t\t\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t\t\taria-controls={ inputId }\n\t\t\t\t\t\t\tlabel={ LABELS[ side ] }\n\t\t\t\t\t\t\thideLabelFromVision\n\t\t\t\t\t\t\tonChange={ ( newValue ) => {\n\t\t\t\t\t\t\t\thandleOnValueChange(\n\t\t\t\t\t\t\t\t\tside,\n\t\t\t\t\t\t\t\t\tnewValue !== undefined\n\t\t\t\t\t\t\t\t\t\t? [ newValue, computedUnit ].join( '' )\n\t\t\t\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\tmin={ 0 }\n\t\t\t\t\t\t\tmax={\n\t\t\t\t\t\t\t\tCUSTOM_VALUE_SETTINGS[ computedUnit ?? 'px' ]\n\t\t\t\t\t\t\t\t\t?.max ?? 10\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tstep={\n\t\t\t\t\t\t\t\tCUSTOM_VALUE_SETTINGS[ computedUnit ?? 'px' ]\n\t\t\t\t\t\t\t\t\t?.step ?? 0.1\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tvalue={ parsedQuantity ?? 0 }\n\t\t\t\t\t\t\twithInputField={ false }\n\t\t\t\t\t\t/>\n\t\t\t\t\t</InputWrapper>\n\t\t\t\t);\n\t\t\t} ) }\n\t\t</>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAIA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AAVA;AACA;AACA;;AAEA;AACA;AACA;;AAYA,MAAMM,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAEN,SAASC,gBAAgBA,CAAE;EACzCC,qBAAqB;EACrBC,QAAQ,GAAGH,IAAI;EACfI,OAAO,GAAGJ,IAAI;EACdK,MAAM;EACNC,aAAa;EACbC,gBAAgB;EAChBC,KAAK;EACL,GAAGC;AACyB,CAAC,EAAG;EAChC,MAAMC,WAAW,GAAG,IAAAC,sBAAa,EAAEV,gBAAgB,EAAE,mBAAoB,CAAC;EAE1E,MAAMW,mBAAmB,GACtBC,IAA2B,IAC3BC,KAA2C,IAAM;IAClDV,OAAO,CAAEU,KAAK,EAAE;MAAED;IAAK,CAAE,CAAC;EAC3B,CAAC;EAEF,MAAME,cAAc,GAAKC,UAA2B,IAAM;IACzDb,QAAQ,CAAEa,UAAW,CAAC;EACvB,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAC3BJ,IAA2B,EAC3BK,IAAa,EACbC,KAAyD,KACrD;IACJ,MAAMH,UAAU,GAAG;MAAE,GAAGX;IAAO,CAAC;IAChC,MAAMe,SAAS,GAAGF,IAAI,KAAKG,SAAS,IAAI,CAAEC,KAAK,CAAEC,UAAU,CAAEL,IAAK,CAAE,CAAC;IACrE,MAAMM,SAAS,GAAGJ,SAAS,GAAGF,IAAI,GAAGG,SAAS;IAE9CL,UAAU,CAAEH,IAAI,CAAE,GAAGW,SAAS;;IAE9B;AACF;AACA;AACA;IACE;IACA;IACA;IACA,IAAKL,KAAK,EAAEL,KAAK,CAACW,MAAM,EAAG;MAC1B,QAASZ,IAAI;QACZ,KAAK,KAAK;UACTG,UAAU,CAACU,MAAM,GAAGF,SAAS;UAC7B;QACD,KAAK,QAAQ;UACZR,UAAU,CAACW,GAAG,GAAGH,SAAS;UAC1B;QACD,KAAK,MAAM;UACVR,UAAU,CAACY,KAAK,GAAGJ,SAAS;UAC5B;QACD,KAAK,OAAO;UACXR,UAAU,CAACa,IAAI,GAAGL,SAAS;UAC3B;MACF;IACD;IAEAT,cAAc,CAAEC,UAAW,CAAC;EAC7B,CAAC;EAED,MAAMc,wBAAwB,GAC3BjB,IAA2B,IAAQK,IAAa,IAAM;IACvD,MAAMa,QAAQ,GAAG;MAAE,GAAGzB;IAAc,CAAC;IACrCyB,QAAQ,CAAElB,IAAI,CAAE,GAAGK,IAAI;IACvBX,gBAAgB,CAAEwB,QAAS,CAAC;EAC7B,CAAC;;EAEF;EACA,MAAMC,aAAa,GAAGxB,KAAK,EAAEyB,MAAM,GAChCC,iBAAS,CAACC,MAAM,CAAItB,IAAI,IAAML,KAAK,CAAC4B,QAAQ,CAAEvB,IAAK,CAAE,CAAC,GACtDqB,iBAAS;EAEZ,OACC,IAAAG,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGP,aAAa,CAACQ,GAAG,CAAI3B,IAAI,IAAM;IAAA,IAAA4B,qBAAA,EAAAC,sBAAA;IAChC,MAAM,CAAEC,cAAc,EAAEC,UAAU,CAAE,GACnC,IAAAC,uCAAgC,EAAExC,MAAM,CAAEQ,IAAI,CAAG,CAAC;IAEnD,MAAMiC,YAAY,GAAGzC,MAAM,CAAEQ,IAAI,CAAE,GAChC+B,UAAU,GACVtC,aAAa,CAAEO,IAAI,CAAE;IAExB,MAAMkC,OAAO,GAAG,CAAErC,WAAW,EAAEG,IAAI,CAAE,CAACmC,IAAI,CAAE,GAAI,CAAC;IAEjD,OACC,IAAAX,MAAA,CAAAC,aAAA,EAACvC,iBAAA,CAAAkD,YAAY;MAACC,GAAG,EAAI,eAAerC,IAAM,EAAG;MAACsC,QAAQ;IAAA,GACrD,IAAAd,MAAA,CAAAC,aAAA,EAACvC,iBAAA,CAAAqD,oBAAoB;MAACvC,IAAI,EAAGA,IAAM;MAACL,KAAK,EAAGA;IAAO,CAAE,CAAC,EACtD,IAAA6B,MAAA,CAAAC,aAAA,EAAC3C,QAAA,CAAA0D,OAAO;MAACC,SAAS,EAAC,SAAS;MAACC,IAAI,EAAGC,cAAM,CAAE3C,IAAI;IAAI,GACnD,IAAAwB,MAAA,CAAAC,aAAA,EAACvC,iBAAA,CAAA0D,iBAAiB;MAAA,GACZhD,KAAK;MACVP,qBAAqB,EAAGA,qBAAuB;MAC/CwD,SAAS,EAAC,qCAAqC;MAC/CC,EAAE,EAAGZ,OAAS;MACda,oBAAoB;MACpBC,KAAK,EAAG,CAAElB,cAAc,EAAEG,YAAY,CAAE,CAACE,IAAI,CAC5C,EACD,CAAG;MACH7C,QAAQ,EAAGA,CAAEqB,SAAS,EAAEL,KAAK,KAC5BF,mBAAmB,CAClBJ,IAAI,EACJW,SAAS,EACTL,KACD,CACA;MACD2C,YAAY,EAAGhC,wBAAwB,CACtCjB,IACD,CAAG;MACHT,OAAO,EAAGQ,mBAAmB,CAAEC,IAAK,CAAG;MACvCkD,KAAK,EAAGP,cAAM,CAAE3C,IAAI,CAAI;MACxBmD,mBAAmB;IAAA,CACnB,CACO,CAAC,EAEV,IAAA3B,MAAA,CAAAC,aAAA,EAACvC,iBAAA,CAAAkE,kBAAkB;MAClBC,uBAAuB;MACvBhE,qBAAqB,EAAGA,qBAAuB;MAC/C,iBAAgB6C,OAAS;MACzBgB,KAAK,EAAGP,cAAM,CAAE3C,IAAI,CAAI;MACxBmD,mBAAmB;MACnB7D,QAAQ,EAAKgE,QAAQ,IAAM;QAC1BlD,mBAAmB,CAClBJ,IAAI,EACJsD,QAAQ,KAAK9C,SAAS,GACnB,CAAE8C,QAAQ,EAAErB,YAAY,CAAE,CAACE,IAAI,CAAE,EAAG,CAAC,GACrC3B,SACJ,CAAC;MACF,CAAG;MACH+C,GAAG,EAAG,CAAG;MACTC,GAAG,GAAA5B,qBAAA,GACF6B,6BAAqB,CAAExB,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAI,IAAI,CAAE,EAC1CuB,GAAG,cAAA5B,qBAAA,cAAAA,qBAAA,GAAI,EACV;MACD8B,IAAI,GAAA7B,sBAAA,GACH4B,6BAAqB,CAAExB,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAI,IAAI,CAAE,EAC1CyB,IAAI,cAAA7B,sBAAA,cAAAA,sBAAA,GAAI,GACX;MACDmB,KAAK,EAAGlB,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAI,CAAG;MAC7B6B,cAAc,EAAG;IAAO,CACxB,CACY,CAAC;EAEjB,CAAE,CACD,CAAC;AAEL"}