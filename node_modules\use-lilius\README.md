<div align="center">
  <h1>📅 use-lilius</h1>

  [<img src="./meta/logo.png" width="200px">](https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>)

  <p>A headless calendar hook for React.</p>

  [![npm](https://img.shields.io/npm/v/use-lilius)](https://www.npmjs.com/package/use-lilius)
  ![GitHub Workflow Status](https://img.shields.io/github/workflow/status/its-danny/use-lilius/test)
  ![LGTM Alerts](https://img.shields.io/lgtm/alerts/github/its-danny/use-lilius)
  [![DeepScan grade](https://deepscan.io/api/teams/16450/projects/19746/branches/517163/badge/grade.svg)](https://deepscan.io/dashboard#view=project&tid=16450&pid=19746&bid=517163)
  [![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-pink.svg)](https://conventionalcommits.org)
  [![License](https://img.shields.io/github/license/its-danny/use-lilius)](LICENSE)
</div>

## Installation

```zsh
yarn add use-lilius
```

## Usage

- The [API docs](docs/README.md) should help you get started.
- See [Storybook](https://use-lilius.vercel.app/) for some
examples built with `use-lilius` and [Chakra](https://chakra-ui.com/).

## Shout-Outs

- [dayzed](https://github.com/deseretdigital/dayzed) was the first thing I
looked into when creating this library. while it didn't suit my needs, it might suit yours.
- [date-fns](https://date-fns.org/) isn't a calendar library, but it's easy
enough to roll your own with it.

## LICENSE

[MIT](LICENSE)
