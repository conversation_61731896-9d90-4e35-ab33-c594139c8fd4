/**
 * Service pour la gestion des robots.txt et sitemaps
 *
 * Gère les communications avec l'API pour les fonctionnalités de robots.txt et sitemaps
 */

import apiFetch from '@wordpress/api-fetch';

class RobotsSitemapService {
  /**
   * R<PERSON>cupère le contenu du fichier robots.txt
   *
   * @returns {Promise} Promesse contenant le contenu du fichier robots.txt
   */
  async getRobotsContent() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/robots';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du contenu robots.txt:', error);
      throw error;
    }
  }

  /**
   * Enregistre le contenu du fichier robots.txt
   *
   * @param {string} content - Nouveau contenu du fichier robots.txt
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveRobotsContent(content) {
    try {
      const path = '/boss-seo/v1/robots-sitemap/robots';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { content }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du contenu robots.txt:', error);
      throw error;
    }
  }

  /**
   * Valide le contenu du fichier robots.txt.
   *
   * @param {string} content Le contenu du fichier robots.txt à valider.
   * @return {Promise} Une promesse qui se résout avec le résultat de la validation.
   */
  async validateRobotsContent(content) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/validate-robots',
        method: 'POST',
        data: { content },
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la validation du contenu du robots.txt:', error);
      throw error;
    }
  }

  /**
   * Récupère des règles prédéfinies pour le fichier robots.txt.
   *
   * @param {string} type Le type de règles à récupérer.
   * @return {Promise} Une promesse qui se résout avec les règles prédéfinies.
   */
  async getRobotsRules(type) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/robots-rules?type=${type}`,
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error(`Erreur lors de la récupération des règles ${type}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres du sitemap
   *
   * @returns {Promise} Promesse contenant les paramètres du sitemap
   */
  async getSitemapSettings() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/settings';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du sitemap:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres du sitemap
   *
   * @param {Object} settings - Nouveaux paramètres du sitemap
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async saveSitemapSettings(settings) {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/settings';
      const response = await apiFetch({
        path,
        method: 'POST',
        data: { settings }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du sitemap:', error);
      throw error;
    }
  }

  /**
   * Récupère les types de contenu disponibles pour le sitemap
   *
   * @returns {Promise} Promesse contenant les types de contenu
   */
  async getContentTypes() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/content-types';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des types de contenu:', error);
      throw error;
    }
  }

  /**
   * Récupère les taxonomies disponibles pour le sitemap
   *
   * @returns {Promise} Promesse contenant les taxonomies
   */
  async getTaxonomies() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/taxonomies';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des taxonomies:', error);
      throw error;
    }
  }

  /**
   * Régénère le sitemap
   *
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async regenerateSitemap() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/regenerate';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la régénération du sitemap:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques d'indexation
   *
   * @returns {Promise} Promesse contenant les statistiques d'indexation
   */
  async getIndexationStats() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/indexation-stats';
      const response = await apiFetch({
        path,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques d\'indexation:', error);
      throw error;
    }
  }

  /**
   * Soumet le sitemap à Google
   *
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async submitSitemapToGoogle() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/submit-google';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la soumission du sitemap à Google:', error);
      throw error;
    }
  }

  /**
   * Soumet le sitemap à Bing
   *
   * @returns {Promise} Promesse contenant le résultat de l'opération
   */
  async submitSitemapToBing() {
    try {
      const path = '/boss-seo/v1/robots-sitemap/sitemap/submit-bing';
      const response = await apiFetch({
        path,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la soumission du sitemap à Bing:', error);
      throw error;
    }
  }

  /**
   * Récupère les types de contenu disponibles.
   *
   * @return {Promise} Une promesse qui se résout avec les types de contenu.
   */
  async getContentTypes() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/content-types',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des types de contenu:', error);
      throw error;
    }
  }

  /**
   * Récupère les taxonomies disponibles.
   *
   * @return {Promise} Une promesse qui se résout avec les taxonomies.
   */
  async getTaxonomies() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/taxonomies',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des taxonomies:', error);
      throw error;
    }
  }

  /**
   * Régénère le sitemap.
   *
   * @return {Promise} Une promesse qui se résout avec le résultat de la régénération.
   */
  async regenerateSitemap() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate',
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la régénération du sitemap:', error);
      throw error;
    }
  }

  /**
   * Récupère les paramètres du sitemap avancé.
   *
   * @return {Promise} Une promesse qui se résout avec les paramètres du sitemap.
   */
  async getAdvancedSitemapSettings() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/settings',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des paramètres du sitemap avancé:', error);
      throw error;
    }
  }

  /**
   * Enregistre les paramètres du sitemap avancé.
   *
   * @param {Object} settings Les paramètres du sitemap.
   * @return {Promise} Une promesse qui se résout avec le résultat de l'enregistrement.
   */
  async saveAdvancedSitemapSettings(settings) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/settings',
        method: 'POST',
        data: { settings },
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des paramètres du sitemap avancé:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des générations de sitemaps.
   *
   * @return {Promise} Une promesse qui se résout avec l'historique des générations.
   */
  async getSitemapGenerationHistory() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/history',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des générations:', error);
      throw error;
    }
  }

  /**
   * Ping les moteurs de recherche pour les informer de la mise à jour du sitemap.
   *
   * @return {Promise} Une promesse qui se résout avec le résultat du ping.
   */
  async pingSearchEngines() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/ping',
        method: 'POST',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du ping des moteurs de recherche:', error);
      throw error;
    }
  }

  /**
   * Récupère les URLs personnalisées pour le sitemap.
   *
   * @return {Promise} Une promesse qui se résout avec les URLs personnalisées.
   */
  async getCustomUrls() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/custom-urls',
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des URLs personnalisées:', error);
      throw error;
    }
  }

  /**
   * Enregistre les URLs personnalisées pour le sitemap.
   *
   * @param {Array} urls Les URLs personnalisées.
   * @return {Promise} Une promesse qui se résout avec le résultat de l'enregistrement.
   */
  async saveCustomUrls(urls) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/advanced-sitemap/custom-urls',
        method: 'POST',
        data: { urls },
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement des URLs personnalisées:', error);
      throw error;
    }
  }

  // ===== NOUVELLES MÉTHODES API PHASE 1 =====

  /**
   * Récupère les statistiques du dashboard
   *
   * @returns {Promise} Promesse contenant les statistiques
   */
  async getDashboardStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/dashboard/stats',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  /**
   * Actualise les statistiques du dashboard
   *
   * @returns {Promise} Promesse contenant les nouvelles statistiques
   */
  async refreshDashboardStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/dashboard/refresh',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'actualisation des statistiques:', error);
      throw error;
    }
  }

  /**
   * Ping les moteurs de recherche (nouvelle version)
   *
   * @returns {Promise} Promesse contenant les résultats du ping
   */
  async pingAllSearchEngines() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/ping-engines',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors du ping des moteurs de recherche:', error);
      throw error;
    }
  }

  /**
   * Récupère l'historique des pings
   *
   * @param {number} limit - Nombre d'enregistrements à récupérer
   * @returns {Promise} Promesse contenant l'historique
   */
  async getPingHistory(limit = 10) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/ping-history?limit=${limit}`,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des pings:', error);
      throw error;
    }
  }

  /**
   * Récupère toutes les URLs personnalisées (nouvelle version)
   *
   * @returns {Promise} Promesse contenant les URLs
   */
  async getAllCustomUrls() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des URLs personnalisées:', error);
      throw error;
    }
  }

  /**
   * Ajoute une nouvelle URL personnalisée
   *
   * @param {Object} urlData - Données de l'URL
   * @returns {Promise} Promesse contenant le résultat
   */
  async addCustomUrl(urlData) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls',
        method: 'POST',
        data: urlData
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'URL personnalisée:', error);
      throw error;
    }
  }

  /**
   * Met à jour une URL personnalisée
   *
   * @param {string} urlId - ID de l'URL
   * @param {Object} urlData - Nouvelles données de l'URL
   * @returns {Promise} Promesse contenant le résultat
   */
  async updateCustomUrl(urlId, urlData) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/custom-urls/${urlId}`,
        method: 'PUT',
        data: urlData
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'URL personnalisée:', error);
      throw error;
    }
  }

  /**
   * Supprime une URL personnalisée
   *
   * @param {string} urlId - ID de l'URL
   * @returns {Promise} Promesse contenant le résultat
   */
  async deleteCustomUrl(urlId) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/custom-urls/${urlId}`,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'URL personnalisée:', error);
      throw error;
    }
  }

  /**
   * Importe des URLs depuis un CSV
   *
   * @param {string} csvContent - Contenu du CSV
   * @returns {Promise} Promesse contenant le résultat
   */
  async importCustomUrlsCsv(csvContent) {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls/import',
        method: 'POST',
        data: { csv_content: csvContent }
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'import CSV:', error);
      throw error;
    }
  }

  /**
   * Exporte les URLs en CSV
   *
   * @returns {Promise} Promesse contenant le CSV
   */
  async exportCustomUrlsCsv() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/custom-urls/export',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'export CSV:', error);
      throw error;
    }
  }

  // ===== NOUVELLES MÉTHODES API PHASE 2 =====

  /**
   * Récupère les statistiques d'indexation détaillées
   *
   * @returns {Promise} Promesse contenant les statistiques
   */
  async getIndexationStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/indexation-stats',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques d\'indexation:', error);
      throw error;
    }
  }

  /**
   * Actualise les données de monitoring
   *
   * @returns {Promise} Promesse contenant les nouvelles données
   */
  async refreshMonitoringData() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/refresh',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'actualisation du monitoring:', error);
      throw error;
    }
  }

  /**
   * Récupère les erreurs détectées dans les sitemaps
   *
   * @returns {Promise} Promesse contenant les erreurs
   */
  async getSitemapErrors() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/errors',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des erreurs:', error);
      throw error;
    }
  }

  /**
   * Lance la détection d'erreurs dans les sitemaps
   *
   * @returns {Promise} Promesse contenant les erreurs détectées
   */
  async detectSitemapErrors() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/monitoring/detect-errors',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la détection d\'erreurs:', error);
      throw error;
    }
  }

  /**
   * Récupère les statistiques des sitemaps spécialisés
   *
   * @returns {Promise} Promesse contenant les statistiques
   */
  async getSpecializedStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/specialized/stats',
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des stats spécialisés:', error);
      throw error;
    }
  }

  /**
   * Régénère un sitemap spécialisé spécifique
   *
   * @param {string} type - Type de sitemap (image, video, news, stories)
   * @returns {Promise} Promesse contenant le résultat
   */
  async regenerateSpecializedSitemap(type) {
    try {
      const response = await apiFetch({
        path: `/boss-seo/v1/robots-sitemap/specialized/regenerate/${type}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de la régénération du sitemap spécialisé:', error);
      throw error;
    }
  }

  /**
   * Actualise les statistiques des sitemaps spécialisés
   *
   * @returns {Promise} Promesse contenant les nouvelles statistiques
   */
  async refreshSpecializedStats() {
    try {
      const response = await apiFetch({
        path: '/boss-seo/v1/robots-sitemap/specialized/refresh',
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'actualisation des stats spécialisés:', error);
      throw error;
    }
  }
}

export default new RobotsSitemapService();
