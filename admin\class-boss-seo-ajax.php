<?php
/**
 * La classe de gestion des requêtes AJAX du plugin Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 */

/**
 * La classe de gestion des requêtes AJAX du plugin Boss SEO.
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/admin
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Ajax {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * La version actuelle du plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Instance de la classe d'analyse.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Analysis    $analysis    Effectue les analyses SEO.
     */
    protected $analysis;

    /**
     * Instance de la classe de recommandations.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_Recommendations    $recommendations    Gère les recommandations.
     */
    protected $recommendations;

    /**
     * Instance de la classe d'intégration IA.
     *
     * @since    1.1.0
     * @access   protected
     * @var      Boss_Optimizer_AI    $ai    Gère l'intégration avec les services d'IA.
     */
    protected $ai;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string                          $plugin_name       Le nom du plugin.
     * @param    string                          $version           La version du plugin.
     * @param    Boss_Optimizer_Analysis         $analysis          Instance de la classe d'analyse SEO.
     * @param    Boss_Optimizer_Recommendations  $recommendations   Instance de la classe de recommandations.
     * @param    Boss_Optimizer_AI               $ai                Instance de la classe d'intégration IA.
     */
    public function __construct( $plugin_name, $version, $analysis, $recommendations, $ai ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->analysis = $analysis;
        $this->recommendations = $recommendations;
        $this->ai = $ai;
    }

    /**
     * Enregistre les hooks AJAX.
     *
     * @since    1.1.0
     */
    public function register_hooks() {
        // Hooks pour les utilisateurs connectés
        add_action( 'wp_ajax_boss_seo_analyze_content', array( $this, 'analyze_content' ) );
        add_action( 'wp_ajax_boss_seo_optimize_content', array( $this, 'optimize_content' ) );
        add_action( 'wp_ajax_boss_seo_get_fresh_metadata', array( $this, 'get_fresh_metadata' ) );
    }

    /**
     * Analyse un contenu via AJAX.
     *
     * @since    1.1.0
     */
    public function analyze_content() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_metabox_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page et réessayer.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier l'ID du post
        if ( ! isset( $_POST['post_id'] ) || ! is_numeric( $_POST['post_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID de contenu invalide.', 'boss-seo' ) ) );
        }

        $post_id = intval( $_POST['post_id'] );
        $post = get_post( $post_id );

        if ( ! $post ) {
            wp_send_json_error( array( 'message' => __( 'Contenu non trouvé.', 'boss-seo' ) ) );
        }

        // Analyser le contenu
        $analysis_results = $this->analysis->analyze( $post );

        if ( ! $analysis_results['success'] ) {
            wp_send_json_error( array( 'message' => $analysis_results['message'] ) );
        }

        // Récupérer les métadonnées mises à jour
        $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
        $seo_score = get_post_meta( $post_id, '_boss_seo_score', true );
        $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );
        $analysis_date = get_post_meta( $post_id, '_boss_seo_analysis_date', true );

        // Formater la date d'analyse
        $formatted_date = ! empty( $analysis_date )
            ? date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $analysis_date ) )
            : '';

        // Ajouter les métadonnées aux résultats
        $analysis_results['meta_description'] = $meta_description;
        $analysis_results['focus_keyword'] = $focus_keyword;
        $analysis_results['score'] = $seo_score;
        $analysis_results['recommendations'] = $recommendations;
        $analysis_results['analysis_date'] = $formatted_date;

        wp_send_json_success( $analysis_results );
    }

    /**
     * Optimise un contenu via AJAX.
     *
     * @since    1.1.0
     */
    public function optimize_content() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_metabox_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page et réessayer.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier l'ID du post
        if ( ! isset( $_POST['post_id'] ) || ! is_numeric( $_POST['post_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID de contenu invalide.', 'boss-seo' ) ) );
        }

        $post_id = intval( $_POST['post_id'] );
        $post = get_post( $post_id );

        if ( ! $post ) {
            wp_send_json_error( array( 'message' => __( 'Contenu non trouvé.', 'boss-seo' ) ) );
        }

        // Générer des métadonnées optimisées même si l'IA n'est pas configurée
        $optimization_result = $this->generate_optimized_metadata( $post );

        // Analyser d'abord le contenu
        $analysis_results = $this->analysis->analyze( $post );

        if ( ! $analysis_results['success'] ) {
            wp_send_json_error( array( 'message' => $analysis_results['message'] ) );
        }

        // Récupérer les recommandations
        $recommendations = $this->recommendations->get_recommendations( $post_id );

        if ( empty( $recommendations ) ) {
            wp_send_json_error( array( 'message' => __( 'Aucune recommandation trouvée pour ce contenu.', 'boss-seo' ) ) );
        }

        // Appliquer les recommandations critiques et importantes
        $applied_count = 0;
        $error_messages = array();

        foreach ( $recommendations as $recommendation ) {
            if ( $recommendation['type'] === 'critical' || $recommendation['type'] === 'warning' ) {
                $result = $this->recommendations->apply_recommendation( $post_id, $recommendation['id'], $this->ai );

                if ( $result['success'] ) {
                    $applied_count++;
                } else {
                    // Collecter les messages d'erreur pour le débogage
                    $error_messages[] = sprintf(
                        __( 'Échec de l\'optimisation pour "%s": %s', 'boss-seo' ),
                        $recommendation['text'],
                        $result['message']
                    );
                }
            }
        }

        if ( $applied_count === 0 ) {
            // Si aucune optimisation n'a été appliquée, essayer d'appliquer au moins une recommandation
            // même si elle n'est pas critique ou importante
            if (!empty($recommendations)) {
                $recommendation = reset($recommendations);
                $result = $this->recommendations->apply_recommendation( $post_id, $recommendation['id'], $this->ai );

                if ( $result['success'] ) {
                    $applied_count++;
                } else {
                    $error_messages[] = sprintf(
                        __( 'Échec de l\'optimisation de secours pour "%s": %s', 'boss-seo' ),
                        $recommendation['text'],
                        $result['message']
                    );
                }
            }

            // Si toujours aucune optimisation, renvoyer une erreur détaillée
            if ( $applied_count === 0 ) {
                $error_detail = !empty($error_messages) ? implode('; ', $error_messages) : '';
                wp_send_json_error( array(
                    'message' => __( 'Aucune optimisation n\'a pu être appliquée.', 'boss-seo' ),
                    'detail' => $error_detail
                ) );
            }
        }

        // Récupérer le contenu mis à jour
        $updated_post = get_post( $post_id );
        $content = $updated_post->post_content;

        // Récupérer les métadonnées mises à jour
        $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
        $seo_score = get_post_meta( $post_id, '_boss_seo_score', true );
        $recommendations = get_post_meta( $post_id, '_boss_seo_recommendations', true );
        $analysis_date = get_post_meta( $post_id, '_boss_seo_analysis_date', true );

        // Formater la date d'analyse
        $formatted_date = ! empty( $analysis_date )
            ? date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $analysis_date ) )
            : '';

        wp_send_json_success( array(
            'message' => sprintf( __( '%d optimisations ont été appliquées avec succès. Métadonnées optimisées et sauvegardées.', 'boss-seo' ), $applied_count ),
            'applied_count' => $applied_count,
            'content' => $content,
            'meta_description' => $meta_description,
            'focus_keyword' => $focus_keyword,
            'score' => $seo_score,
            'recommendations' => $recommendations,
            'analysis_date' => $formatted_date,
            'optimizations' => $optimization_result,
            'auto_saved' => true
        ) );
    }

    /**
     * Génère des métadonnées optimisées pour un post.
     *
     * @since    1.2.0
     * @param    WP_Post    $post    Post à optimiser.
     * @return   array              Métadonnées optimisées.
     */
    private function generate_optimized_metadata( $post ) {
        $post_id = $post->ID;
        $content = $post->post_content;
        $title = $post->post_title;

        // Extraire des mots-clés basiques du titre et du contenu
        $words = str_word_count( strtolower( $title . ' ' . strip_tags( $content ) ), 1 );
        $word_counts = array_count_values( $words );

        // Filtrer les mots trop courts et les mots vides
        $stop_words = array( 'le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or', 'à', 'au', 'aux', 'avec', 'sans', 'sous', 'sur', 'dans', 'par', 'pour', 'en', 'vers', 'chez', 'contre', 'entre', 'parmi', 'selon', 'pendant', 'avant', 'après', 'depuis', 'jusqu', 'ce', 'cette', 'ces', 'son', 'sa', 'ses', 'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'notre', 'nos', 'votre', 'vos', 'leur', 'leurs', 'qui', 'que', 'quoi', 'dont', 'où', 'il', 'elle', 'on', 'nous', 'vous', 'ils', 'elles', 'je', 'tu', 'me', 'te', 'se', 'nous', 'vous', 'se', 'lui', 'leur', 'y', 'en', 'ne', 'pas', 'plus', 'jamais', 'rien', 'personne', 'aucun', 'aucune', 'nul', 'nulle', 'si', 'oui', 'non', 'peut', 'être', 'avoir', 'faire', 'aller', 'venir', 'voir', 'savoir', 'pouvoir', 'vouloir', 'devoir', 'falloir' );

        $filtered_words = array();
        foreach ( $word_counts as $word => $count ) {
            if ( strlen( $word ) >= 4 && ! in_array( $word, $stop_words ) && $count >= 2 ) {
                $filtered_words[ $word ] = $count;
            }
        }

        // Trier par fréquence
        arsort( $filtered_words );
        $keywords = array_keys( array_slice( $filtered_words, 0, 5 ) );

        $focus_keyword = ! empty( $keywords ) ? $keywords[0] : sanitize_title( $title );
        $secondary_keywords = array_slice( $keywords, 1, 3 );

        // Générer un titre SEO optimisé
        $seo_title = $title;
        if ( ! empty( $focus_keyword ) && strpos( strtolower( $title ), strtolower( $focus_keyword ) ) === false ) {
            $seo_title = $title . ' - ' . ucfirst( $focus_keyword );
        }

        // Limiter à 60 caractères
        if ( strlen( $seo_title ) > 60 ) {
            $seo_title = wp_trim_words( $seo_title, 8 );
        }

        // Générer une meta description optimisée
        $excerpt = wp_trim_words( strip_tags( $content ), 20 );
        $meta_description = $excerpt;

        if ( ! empty( $focus_keyword ) ) {
            $meta_description = $excerpt . ' Découvrez tout sur ' . $focus_keyword . '.';
        }

        // Limiter à 160 caractères
        if ( strlen( $meta_description ) > 160 ) {
            $meta_description = wp_trim_words( $meta_description, 25 );
        }

        // Sauvegarder les métadonnées
        $optimizations = array(
            'title' => $seo_title,
            'meta_description' => $meta_description,
            'focus_keyword' => $focus_keyword,
            'secondary_keywords' => $secondary_keywords,
        );

        // Sauvegarder automatiquement
        update_post_meta( $post_id, '_boss_seo_title', sanitize_text_field( $seo_title ) );
        update_post_meta( $post_id, '_boss_seo_meta_description', sanitize_textarea_field( $meta_description ) );
        update_post_meta( $post_id, '_boss_seo_focus_keyword', sanitize_text_field( $focus_keyword ) );

        if ( ! empty( $secondary_keywords ) ) {
            $secondary_keywords_string = implode( ',', array_map( 'sanitize_text_field', $secondary_keywords ) );
            update_post_meta( $post_id, '_boss_seo_secondary_keywords', $secondary_keywords_string );
        }

        // Marquer comme optimisé
        update_post_meta( $post_id, '_boss_seo_optimized_date', current_time( 'mysql' ) );
        update_post_meta( $post_id, '_boss_seo_optimized', 1 );

        return $optimizations;
    }

    /**
     * Récupère les métadonnées fraîches depuis la base de données.
     *
     * @since    1.2.0
     */
    public function get_fresh_metadata() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_metabox_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité. Veuillez rafraîchir la page et réessayer.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'edit_posts' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Vérifier l'ID du post
        if ( ! isset( $_POST['post_id'] ) || ! is_numeric( $_POST['post_id'] ) ) {
            wp_send_json_error( array( 'message' => __( 'ID de contenu invalide.', 'boss-seo' ) ) );
        }

        $post_id = intval( $_POST['post_id'] );
        $post = get_post( $post_id );

        if ( ! $post ) {
            wp_send_json_error( array( 'message' => __( 'Contenu non trouvé.', 'boss-seo' ) ) );
        }

        // Récupérer toutes les métadonnées SEO depuis la base de données
        $metadata = array(
            // Champs SEO de base
            'seo_title' => get_post_meta( $post_id, '_boss_seo_title', true ),
            'meta_description' => get_post_meta( $post_id, '_boss_seo_meta_description', true ),
            'focus_keyword' => get_post_meta( $post_id, '_boss_seo_focus_keyword', true ),
            'secondary_keywords' => get_post_meta( $post_id, '_boss_seo_secondary_keywords', true ),
            'canonical_url' => get_post_meta( $post_id, '_boss_seo_canonical_url', true ),
            'robots_index' => get_post_meta( $post_id, '_boss_seo_robots_index', true ),
            'robots_follow' => get_post_meta( $post_id, '_boss_seo_robots_follow', true ),

            // Champs Open Graph (Facebook)
            'og_title' => get_post_meta( $post_id, '_boss_seo_og_title', true ),
            'og_description' => get_post_meta( $post_id, '_boss_seo_og_description', true ),
            'og_image' => get_post_meta( $post_id, '_boss_seo_og_image', true ),

            // Champs Twitter
            'twitter_title' => get_post_meta( $post_id, '_boss_seo_twitter_title', true ),
            'twitter_description' => get_post_meta( $post_id, '_boss_seo_twitter_description', true ),
            'twitter_image' => get_post_meta( $post_id, '_boss_seo_twitter_image', true ),
            'twitter_card_type' => get_post_meta( $post_id, '_boss_seo_twitter_card_type', true ),

            // Données d'analyse
            'seo_score' => get_post_meta( $post_id, '_boss_seo_score', true ),
            'analysis_date' => get_post_meta( $post_id, '_boss_seo_analysis_date', true ),
            'recommendations' => get_post_meta( $post_id, '_boss_seo_recommendations', true ),
            'optimized_date' => get_post_meta( $post_id, '_boss_seo_optimized_date', true )
        );

        // Nettoyer les valeurs vides et formater
        $clean_metadata = array();
        foreach ( $metadata as $key => $value ) {
            if ( ! empty( $value ) ) {
                $clean_metadata[ $key ] = $value;
            }
        }

        // Formater la date d'analyse si présente
        if ( ! empty( $clean_metadata['analysis_date'] ) ) {
            $clean_metadata['analysis_date_formatted'] = date_i18n(
                get_option( 'date_format' ) . ' ' . get_option( 'time_format' ),
                strtotime( $clean_metadata['analysis_date'] )
            );
        }

        // Formater la date d'optimisation si présente
        if ( ! empty( $clean_metadata['optimized_date'] ) ) {
            $clean_metadata['optimized_date_formatted'] = date_i18n(
                get_option( 'date_format' ) . ' ' . get_option( 'time_format' ),
                strtotime( $clean_metadata['optimized_date'] )
            );
        }

        wp_send_json_success( $clean_metadata );
    }
}
