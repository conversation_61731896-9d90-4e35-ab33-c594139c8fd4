"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Item = void 0;
var _react = require("react");
var _hook = require("./hook");
var _context = require("../../context");
var _view = require("../../view");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedItem(props, forwardedRef) {
  const {
    role,
    wrapperClassName,
    ...otherProps
  } = (0, _hook.useItem)(props);
  return (0, _react.createElement)("div", {
    role: role,
    className: wrapperClassName
  }, (0, _react.createElement)(_view.View, {
    ...otherProps,
    ref: forwardedRef
  }));
}

/**
 * `Item` is used in combination with `ItemGroup` to display a list of items
 * grouped and styled together.
 *
 * ```jsx
 * import {
 *   __experimentalItemGroup as ItemGroup,
 *   __experimentalItem as Item,
 * } from '@wordpress/components';
 *
 * function Example() {
 *   return (
 *     <ItemGroup>
 *       <Item>Code</Item>
 *       <Item>is</Item>
 *       <Item>Poetry</Item>
 *     </ItemGroup>
 *   );
 * }
 * ```
 */
const Item = (0, _context.contextConnect)(UnconnectedItem, 'Item');
exports.Item = Item;
var _default = Item;
exports.default = _default;
//# sourceMappingURL=component.js.map