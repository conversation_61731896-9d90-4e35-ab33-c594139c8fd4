{"version": 3, "names": ["_rgbInput", "require", "_hslInput", "_hexInput", "ColorInput", "colorType", "color", "onChange", "enableAlpha", "props", "_react", "createElement", "HslInput", "RgbInput", "HexInput", "exports"], "sources": ["@wordpress/components/src/color-picker/color-input.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport { RgbInput } from './rgb-input';\nimport { HslInput } from './hsl-input';\nimport { HexInput } from './hex-input';\nimport type { ColorInputProps } from './types';\n\nexport const ColorInput = ( {\n\tcolorType,\n\tcolor,\n\tonChange,\n\tenableAlpha,\n}: ColorInputProps ) => {\n\tconst props = { color, onChange, enableAlpha };\n\tswitch ( colorType ) {\n\t\tcase 'hsl':\n\t\t\treturn <HslInput { ...props } />;\n\t\tcase 'rgb':\n\t\t\treturn <RgbInput { ...props } />;\n\t\tdefault:\n\t\tcase 'hex':\n\t\t\treturn <HexInput { ...props } />;\n\t}\n};\n"], "mappings": ";;;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AALA;AACA;AACA;;AAMO,MAAMG,UAAU,GAAGA,CAAE;EAC3BC,SAAS;EACTC,KAAK;EACLC,QAAQ;EACRC;AACgB,CAAC,KAAM;EACvB,MAAMC,KAAK,GAAG;IAAEH,KAAK;IAAEC,QAAQ;IAAEC;EAAY,CAAC;EAC9C,QAASH,SAAS;IACjB,KAAK,KAAK;MACT,OAAO,IAAAK,MAAA,CAAAC,aAAA,EAACT,SAAA,CAAAU,QAAQ;QAAA,GAAMH;MAAK,CAAI,CAAC;IACjC,KAAK,KAAK;MACT,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACX,SAAA,CAAAa,QAAQ;QAAA,GAAMJ;MAAK,CAAI,CAAC;IACjC;IACA,KAAK,KAAK;MACT,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACR,SAAA,CAAAW,QAAQ;QAAA,GAAML;MAAK,CAAI,CAAC;EAClC;AACD,CAAC;AAACM,OAAA,CAAAX,UAAA,GAAAA,UAAA"}