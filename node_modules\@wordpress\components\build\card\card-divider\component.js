"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.CardDivider = void 0;
var _react = require("react");
var _context = require("../../context");
var _divider = require("../../divider");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedCardDivider(props, forwardedRef) {
  const dividerProps = (0, _hook.useCardDivider)(props);
  return (0, _react.createElement)(_divider.Divider, {
    ...dividerProps,
    ref: forwardedRef
  });
}

/**
 * `CardDivider` renders an optional divider within a `Card`.
 * It is typically used to divide multiple `CardBody` components from each other.
 *
 * ```jsx
 * import { Card, CardBody, CardDivider } from `@wordpress/components`;
 *
 * <Card>
 *  <CardBody>...</CardBody>
 *  <CardDivider />
 *  <CardBody>...</CardBody>
 * </Card>
 * ```
 */
const CardDivider = (0, _context.contextConnect)(UnconnectedCardDivider, 'CardDivider');
exports.CardDivider = CardDivider;
var _default = CardDivider;
exports.default = _default;
//# sourceMappingURL=component.js.map