<?php
/**
 * Service IA Boss SEO - Pont vers le service IA existant
 *
 * Cette classe fait le pont entre le nouveau module d'analyse technique
 * et le service IA existant de Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Service IA Boss SEO
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_AI_Service {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance du service IA existant.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_Optimizer_AI    $ai_service    Instance du service IA.
     */
    private $ai_service;

    /**
     * Instance des paramètres.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_Optimizer_Settings    $settings    Instance des paramètres.
     */
    private $settings;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Charger les dépendances
        $this->load_dependencies();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.1.0
     */
    private function load_dependencies() {
        // Charger les paramètres si pas déjà fait
        if ( ! class_exists( 'Boss_Optimizer_Settings' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
        }

        // Charger le service IA existant si pas déjà fait
        if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
        }

        try {
            // Initialiser les paramètres avec le nom du plugin
            $this->settings = new Boss_Optimizer_Settings( $this->plugin_name );

            // Initialiser le service IA existant
            $this->ai_service = new Boss_Optimizer_AI( $this->plugin_name, $this->settings );
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de l\'initialisation du service IA: ' . $e->getMessage() );
        }
    }

    /**
     * Vérifie si l'IA est configurée et disponible.
     *
     * @since    1.1.0
     * @return   bool    True si l'IA est configurée.
     */
    public function is_configured() {
        if ( ! $this->settings ) {
            return false;
        }

        return $this->settings->is_ai_configured();
    }

    /**
     * Vérifie si l'IA est disponible.
     *
     * @since    1.1.0
     * @return   bool    True si l'IA est disponible.
     */
    public function is_available() {
        return $this->is_configured() && $this->ai_service;
    }

    /**
     * Récupère le modèle IA actuel.
     *
     * @since    1.1.0
     * @return   string    Nom du modèle IA actuel.
     */
    public function get_current_model() {
        if ( ! $this->settings ) {
            return 'unknown';
        }

        $provider = $this->settings->get_ai_provider();

        switch ( $provider ) {
            case 'openai':
                return $this->settings->get( 'ai', 'openai_model', 'gpt-4' );
            case 'anthropic':
                return $this->settings->get( 'ai', 'anthropic_model', 'claude-3-opus' );
            case 'gemini':
                return $this->settings->get( 'ai', 'gemini_model', 'gemini-1.5-pro' );
            default:
                return $provider;
        }
    }

    /**
     * Génère du contenu avec l'IA.
     *
     * @since    1.1.0
     * @param    string    $prompt     Prompt à envoyer à l'IA.
     * @param    array     $options    Options supplémentaires.
     * @return   array                 Résultat de la génération.
     */
    public function generate_content( $prompt, $options = array() ) {
        if ( ! $this->is_available() ) {
            return array(
                'success' => false,
                'message' => __( 'Service IA non disponible. Veuillez configurer votre API IA dans les paramètres.', 'boss-seo' ),
                'content' => '',
            );
        }

        try {
            // Utiliser le service IA existant
            $result = $this->ai_service->generate_content( $prompt, $options );

            // Normaliser la réponse pour compatibilité
            if ( isset( $result['success'] ) && $result['success'] ) {
                return array(
                    'success' => true,
                    'content' => $result['content'] ?? '',
                    'message' => $result['message'] ?? __( 'Contenu généré avec succès.', 'boss-seo' ),
                );
            } else {
                return array(
                    'success' => false,
                    'content' => '',
                    'message' => $result['message'] ?? __( 'Erreur lors de la génération de contenu.', 'boss-seo' ),
                );
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la génération de contenu IA: ' . $e->getMessage() );

            return array(
                'success' => false,
                'content' => '',
                'message' => __( 'Erreur technique lors de la génération de contenu.', 'boss-seo' ),
            );
        }
    }

    /**
     * Génère des suggestions SEO avec l'IA.
     *
     * @since    1.1.0
     * @param    string    $content_type    Type de contenu (page, article, etc.).
     * @param    string    $topic           Sujet du contenu.
     * @param    array     $context         Contexte supplémentaire.
     * @return   array                      Suggestions générées.
     */
    public function generate_seo_suggestions( $content_type, $topic, $context = array() ) {
        $prompt = $this->build_seo_suggestions_prompt( $content_type, $topic, $context );

        return $this->generate_content( $prompt, array(
            'temperature' => 0.7,
            'max_tokens' => 1000,
        ) );
    }

    /**
     * Génère des suggestions de performance avec l'IA.
     *
     * @since    1.1.0
     * @param    array    $performance_data    Données de performance.
     * @param    string   $url                 URL analysée.
     * @return   array                         Suggestions générées.
     */
    public function generate_performance_suggestions( $performance_data, $url ) {
        $prompt = $this->build_performance_suggestions_prompt( $performance_data, $url );

        return $this->generate_content( $prompt, array(
            'temperature' => 0.5,
            'max_tokens' => 800,
        ) );
    }

    /**
     * Construit un prompt pour les suggestions SEO.
     *
     * @since    1.1.0
     * @param    string    $content_type    Type de contenu.
     * @param    string    $topic           Sujet.
     * @param    array     $context         Contexte.
     * @return   string                     Prompt construit.
     */
    private function build_seo_suggestions_prompt( $content_type, $topic, $context ) {
        $prompt = "En tant qu'expert SEO, génère 3-5 suggestions concrètes pour optimiser le référencement naturel.\n\n";
        $prompt .= "Type de contenu: {$content_type}\n";
        $prompt .= "Sujet: {$topic}\n";

        if ( ! empty( $context['url'] ) ) {
            $prompt .= "URL: {$context['url']}\n";
        }

        if ( ! empty( $context['current_title'] ) ) {
            $prompt .= "Titre actuel: {$context['current_title']}\n";
        }

        if ( ! empty( $context['current_description'] ) ) {
            $prompt .= "Description actuelle: {$context['current_description']}\n";
        }

        $prompt .= "\nFournis des suggestions spécifiques et actionnables pour améliorer le SEO, en français, sous forme de liste numérotée.";

        return $prompt;
    }

    /**
     * Construit un prompt pour les suggestions de performance.
     *
     * @since    1.1.0
     * @param    array     $performance_data    Données de performance.
     * @param    string    $url                 URL.
     * @return   string                         Prompt construit.
     */
    private function build_performance_suggestions_prompt( $performance_data, $url ) {
        $prompt = "En tant qu'expert en performance web, analyse les données suivantes et fournis 3-5 suggestions concrètes d'amélioration.\n\n";
        $prompt .= "URL analysée: {$url}\n";

        if ( isset( $performance_data['scores'] ) ) {
            $prompt .= "Scores PageSpeed:\n";
            foreach ( $performance_data['scores'] as $category => $score ) {
                $prompt .= "- {$category}: {$score}/100\n";
            }
        }

        if ( isset( $performance_data['core_web_vitals'] ) ) {
            $prompt .= "\nCore Web Vitals:\n";
            foreach ( $performance_data['core_web_vitals'] as $metric => $data ) {
                $value = $data['value'] ?? 'N/A';
                $unit = $data['unit'] ?? '';
                $status = $data['status'] ?? 'unknown';
                $prompt .= "- {$metric}: {$value}{$unit} ({$status})\n";
            }
        }

        $prompt .= "\nFournis des suggestions techniques spécifiques et priorisées pour améliorer les performances, en français, sous forme de liste numérotée avec impact estimé.";

        return $prompt;
    }

    /**
     * Teste la connexion avec l'API IA.
     *
     * @since    1.1.0
     * @return   array    Résultat du test.
     */
    public function test_connection() {
        if ( ! $this->is_configured() ) {
            return array(
                'success' => false,
                'message' => __( 'IA non configurée.', 'boss-seo' ),
            );
        }

        // Test simple avec un prompt minimal
        $test_prompt = __( 'Réponds simplement "Test réussi" pour confirmer que l\'API fonctionne.', 'boss-seo' );

        $result = $this->generate_content( $test_prompt, array(
            'temperature' => 0,
            'max_tokens' => 10,
        ) );

        if ( $result['success'] ) {
            return array(
                'success' => true,
                'message' => __( 'Connexion IA réussie.', 'boss-seo' ),
                'provider' => $this->settings->get_ai_provider(),
                'model' => $this->get_current_model(),
            );
        } else {
            return array(
                'success' => false,
                'message' => __( 'Échec de la connexion IA: ', 'boss-seo' ) . $result['message'],
            );
        }
    }

    /**
     * Récupère les statistiques d'utilisation de l'IA.
     *
     * @since    1.1.0
     * @return   array    Statistiques d'utilisation.
     */
    public function get_usage_stats() {
        // Pour l'instant, retourner des statistiques basiques
        return array(
            'provider' => $this->settings ? $this->settings->get_ai_provider() : 'unknown',
            'model' => $this->get_current_model(),
            'configured' => $this->is_configured(),
            'available' => $this->is_available(),
            'last_test' => get_option( 'boss_seo_ai_last_test', null ),
        );
    }
}
