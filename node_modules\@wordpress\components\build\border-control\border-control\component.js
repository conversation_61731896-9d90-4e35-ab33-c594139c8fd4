"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.BorderControl = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _borderControlDropdown = _interopRequireDefault(require("../border-control-dropdown"));
var _unitControl = _interopRequireDefault(require("../../unit-control"));
var _rangeControl = _interopRequireDefault(require("../../range-control"));
var _hStack = require("../../h-stack");
var _baseControlStyles = require("../../base-control/styles/base-control-styles");
var _view = require("../../view");
var _visuallyHidden = require("../../visually-hidden");
var _context = require("../../context");
var _hook = require("./hook");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BorderLabel = props => {
  const {
    label,
    hideLabelFromVision
  } = props;
  if (!label) {
    return null;
  }
  return hideLabelFromVision ? (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    as: "legend"
  }, label) : (0, _react.createElement)(_baseControlStyles.StyledLabel, {
    as: "legend"
  }, label);
};
const UnconnectedBorderControl = (props, forwardedRef) => {
  const {
    __next40pxDefaultSize = false,
    colors,
    disableCustomColors,
    disableUnits,
    enableAlpha,
    enableStyle,
    hideLabelFromVision,
    innerWrapperClassName,
    inputWidth,
    isStyleSettable,
    label,
    onBorderChange,
    onSliderChange,
    onWidthChange,
    placeholder,
    __unstablePopoverProps,
    previousStyleSelection,
    showDropdownHeader,
    size,
    sliderClassName,
    value: border,
    widthUnit,
    widthValue,
    withSlider,
    __experimentalIsRenderedInSidebar,
    ...otherProps
  } = (0, _hook.useBorderControl)(props);
  return (0, _react.createElement)(_view.View, {
    as: "fieldset",
    ...otherProps,
    ref: forwardedRef
  }, (0, _react.createElement)(BorderLabel, {
    label: label,
    hideLabelFromVision: hideLabelFromVision
  }), (0, _react.createElement)(_hStack.HStack, {
    spacing: 4,
    className: innerWrapperClassName
  }, (0, _react.createElement)(_unitControl.default, {
    prefix: (0, _react.createElement)(_borderControlDropdown.default, {
      border: border,
      colors: colors,
      __unstablePopoverProps: __unstablePopoverProps,
      disableCustomColors: disableCustomColors,
      enableAlpha: enableAlpha,
      enableStyle: enableStyle,
      isStyleSettable: isStyleSettable,
      onChange: onBorderChange,
      previousStyleSelection: previousStyleSelection,
      showDropdownHeader: showDropdownHeader,
      __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
      size: size
    }),
    label: (0, _i18n.__)('Border width'),
    hideLabelFromVision: true,
    min: 0,
    onChange: onWidthChange,
    value: border?.width || '',
    placeholder: placeholder,
    disableUnits: disableUnits,
    __unstableInputWidth: inputWidth,
    size: size
  }), withSlider && (0, _react.createElement)(_rangeControl.default, {
    __nextHasNoMarginBottom: true,
    label: (0, _i18n.__)('Border width'),
    hideLabelFromVision: true,
    className: sliderClassName,
    initialPosition: 0,
    max: 100,
    min: 0,
    onChange: onSliderChange,
    step: ['px', '%'].includes(widthUnit) ? 1 : 0.1,
    value: widthValue || undefined,
    withInputField: false,
    __next40pxDefaultSize: __next40pxDefaultSize
  })));
};

/**
 * The `BorderControl` brings together internal sub-components which allow users to
 * set the various properties of a border. The first sub-component, a
 * `BorderDropdown` contains options representing border color and style. The
 * border width is controlled via a `UnitControl` and an optional `RangeControl`.
 *
 * Border radius is not covered by this control as it may be desired separate to
 * color, style, and width. For example, the border radius may be absorbed under
 * a "shape" abstraction.
 *
 * ```jsx
 * import { __experimentalBorderControl as BorderControl } from '@wordpress/components';
 * import { __ } from '@wordpress/i18n';
 *
 * const colors = [
 * 	{ name: 'Blue 20', color: '#72aee6' },
 * 	// ...
 * ];
 *
 * const MyBorderControl = () => {
 * 	const [ border, setBorder ] = useState();
 * 	const onChange = ( newBorder ) => setBorder( newBorder );
 *
 * 	return (
 * 		<BorderControl
 * 			colors={ colors }
 * 			label={ __( 'Border' ) }
 * 			onChange={ onChange }
 * 			value={ border }
 * 		/>
 * 	);
 * };
 * ```
 */
const BorderControl = (0, _context.contextConnect)(UnconnectedBorderControl, 'BorderControl');
exports.BorderControl = BorderControl;
var _default = BorderControl;
exports.default = _default;
//# sourceMappingURL=component.js.map