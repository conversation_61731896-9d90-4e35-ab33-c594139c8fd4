{"version": 3, "names": ["_element", "require", "BottomSheetNavigationContext", "createContext", "currentHeight", "value", "setHeight", "exports", "Provider", "BottomSheetNavigationProvider", "Consumer", "BottomSheetNavigationConsumer"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/bottom-sheet-navigation/bottom-sheet-navigation-context.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { createContext } from '@wordpress/element';\n\n// Navigation context in BottomSheet is necessary for controlling the\n// height of navigation container.\nexport const BottomSheetNavigationContext = createContext( {\n\tcurrentHeight: { value: 0 },\n\tsetHeight: () => {},\n} );\n\nexport const {\n\tProvider: BottomSheetNavigationProvider,\n\tConsumer: BottomSheetNavigationConsumer,\n} = BottomSheetNavigationContext;\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA;AACA;AACO,MAAMC,4BAA4B,GAAG,IAAAC,sBAAa,EAAE;EAC1DC,aAAa,EAAE;IAAEC,KAAK,EAAE;EAAE,CAAC;EAC3BC,SAAS,EAAEA,CAAA,KAAM,CAAC;AACnB,CAAE,CAAC;AAACC,OAAA,CAAAL,4BAAA,GAAAA,4BAAA;AAEG,MAAM;EACZM,QAAQ,EAAEC,6BAA6B;EACvCC,QAAQ,EAAEC;AACX,CAAC,GAAGT,4BAA4B;AAACK,OAAA,CAAAI,6BAAA,GAAAA,6BAAA;AAAAJ,OAAA,CAAAE,6BAAA,GAAAA,6BAAA"}