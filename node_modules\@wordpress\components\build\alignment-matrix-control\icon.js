"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _utils = require("./utils");
var _alignmentMatrixControlIconStyles = require("./styles/alignment-matrix-control-icon-styles");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const BASE_SIZE = 24;
function AlignmentMatrixControlIcon({
  className,
  disablePointerEvents = true,
  size = BASE_SIZE,
  style = {},
  value = 'center',
  ...props
}) {
  const alignIndex = (0, _utils.getAlignmentIndex)(value);
  const scale = (size / BASE_SIZE).toFixed(2);
  const classes = (0, _classnames.default)('component-alignment-matrix-control-icon', className);
  const styles = {
    ...style,
    transform: `scale(${scale})`
  };
  return (0, _react.createElement)(_alignmentMatrixControlIconStyles.Root, {
    ...props,
    className: classes,
    disablePointerEvents: disablePointerEvents,
    role: "presentation",
    style: styles
  }, _utils.ALIGNMENTS.map((align, index) => {
    const isActive = alignIndex === index;
    return (0, _react.createElement)(_alignmentMatrixControlIconStyles.Cell, {
      key: align
    }, (0, _react.createElement)(_alignmentMatrixControlIconStyles.Point, {
      isActive: isActive
    }));
  }));
}
var _default = AlignmentMatrixControlIcon;
exports.default = _default;
//# sourceMappingURL=icon.js.map