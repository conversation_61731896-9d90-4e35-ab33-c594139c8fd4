"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _element = require("@wordpress/element");
/**
 * WordPress dependencies
 */

class Dropdown extends _element.Component {
  constructor() {
    super(...arguments);
    this.toggle = this.toggle.bind(this);
    this.close = this.close.bind(this);
    this.state = {
      isOpen: false
    };
  }
  componentWillUnmount() {
    const {
      isOpen
    } = this.state;
    const {
      onToggle
    } = this.props;
    if (isOpen && onToggle) {
      onToggle(false);
    }
  }
  componentDidUpdate(prevProps, prevState) {
    const {
      isOpen
    } = this.state;
    const {
      onToggle
    } = this.props;
    if (prevState.isOpen !== isOpen && onToggle) {
      onToggle(isOpen);
    }
  }
  toggle() {
    this.setState(state => ({
      isOpen: !state.isOpen
    }));
  }
  close() {
    this.setState({
      isOpen: false
    });
  }
  render() {
    const {
      isOpen
    } = this.state;
    const {
      renderContent,
      renderToggle
    } = this.props;
    const args = {
      isOpen,
      onToggle: this.toggle,
      onClose: this.close
    };
    return (0, _react.createElement)(_react.Fragment, null, renderToggle(args), isOpen && renderContent(args));
  }
}
var _default = Dropdown;
exports.default = _default;
//# sourceMappingURL=index.native.js.map