{"version": 3, "names": ["_icon", "_interopRequireDefault", "require", "_button", "_hStack", "_rangeControl", "_unitControl", "_space", "_EMOTION_STRINGIFIED_CSS_ERROR__", "StyledUnitControl", "_base", "default", "UnitControl", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports", "InputWrapper", "HStack", "ResetButton", "<PERSON><PERSON>", "LinkedButtonWrapper", "FlexedBoxControlIcon", "BoxControlIcon", "FlexedRangeControl", "RangeControl", "space"], "sources": ["@wordpress/components/src/box-control/styles/box-control-styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\n/**\n * Internal dependencies\n */\nimport BoxControlIcon from '../icon';\nimport Button from '../../button';\nimport { HStack } from '../../h-stack';\nimport RangeControl from '../../range-control';\nimport UnitControl from '../../unit-control';\nimport { space } from '../../utils/space';\n\nexport const StyledUnitControl = styled( UnitControl )`\n\tmax-width: 90px;\n`;\n\nexport const InputWrapper = styled( HStack )`\n\tgrid-column: 1 / span 3;\n`;\n\nexport const ResetButton = styled( Button )`\n\tgrid-area: 1 / 2;\n\tjustify-self: end;\n`;\n\nexport const LinkedButtonWrapper = styled.div`\n\tgrid-area: 1 / 3;\n\tjustify-self: end;\n`;\n\nexport const FlexedBoxControlIcon = styled( BoxControlIcon )`\n\tflex: 0 0 auto;\n`;\n\nexport const FlexedRangeControl = styled( RangeControl )`\n\twidth: 100%;\n\tmargin-inline-end: ${ space( 2 ) };\n`;\n"], "mappings": ";;;;;;;;AAOA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,YAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAA0C,SAAAM,iCAAA;AAEnC,MAAMC,iBAAiB,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,oBAAW,EAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAErD;AAACc,OAAA,CAAAb,iBAAA,GAAAA,iBAAA;AAEK,MAAMc,YAAY,GAAG,kBAAAb,KAAA,CAAAC,OAAA,EAAQa,cAAM,EAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAE3C;AAACc,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAEK,MAAME,WAAW,GAAG,kBAAAf,KAAA,CAAAC,OAAA,EAAQe,eAAM,EAAAb,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAG1C;AAACc,OAAA,CAAAG,WAAA,GAAAA,WAAA;AAEK,MAAME,mBAAmB,OAAAjB,KAAA,CAAAC,OAAA,SAAAE,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAG/B;AAACc,OAAA,CAAAK,mBAAA,GAAAA,mBAAA;AAEK,MAAMC,oBAAoB,GAAG,kBAAAlB,KAAA,CAAAC,OAAA,EAAQkB,aAAc,EAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAb;AAAA,EAE3D;AAACc,OAAA,CAAAM,oBAAA,GAAAA,oBAAA;AAEK,MAAME,kBAAkB,GAAG,kBAAApB,KAAA,CAAAC,OAAA,EAAQoB,qBAAY,EAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,kCAEjC,IAAAe,YAAK,EAAE,CAAE,CAAC,SAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA,omDAChC;AAACO,OAAA,CAAAQ,kBAAA,GAAAA,kBAAA"}