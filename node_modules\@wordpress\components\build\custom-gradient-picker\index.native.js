"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _components = require("@wordpress/components");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _utils = require("../mobile/color-settings/utils");
var _utils2 = require("./utils");
var _serializer = require("./serializer");
var _constants = require("./constants");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function CustomGradientPicker({
  setColor,
  currentValue,
  isGradientColor
}) {
  const [gradientOrientation, setGradientOrientation] = (0, _element.useState)(_constants.HORIZONTAL_GRADIENT_ORIENTATION);
  const [currentColor, setCurrentColor] = (0, _element.useState)(currentValue);
  const {
    getGradientType,
    gradients,
    gradientOptions
  } = _utils.colorsUtils;
  const {
    gradientAST
  } = (0, _utils2.getGradientAstWithDefault)(currentColor);
  const gradientType = getGradientType(currentColor);
  function isLinearGradient(type) {
    return type === gradients.linear;
  }
  function getGradientColor(type) {
    const {
      orientation,
      ...restGradientAST
    } = gradientAST;
    if (orientation) {
      setGradientOrientation(orientation);
    }
    return (0, _serializer.serializeGradient)(isLinearGradient(type) ? {
      ...gradientAST,
      ...(gradientAST.orientation ? {} : {
        orientation: gradientOrientation
      }),
      type
    } : {
      ...restGradientAST,
      type
    });
  }
  function onGradientTypeChange(type) {
    const gradientColor = getGradientColor(type);
    setCurrentColor(gradientColor);
    setColor(gradientColor);
  }
  function setGradientAngle(value) {
    const gradientColor = (0, _serializer.serializeGradient)({
      ...gradientAST,
      orientation: {
        type: 'angular',
        value
      }
    });
    if (isGradientColor && gradientColor !== currentColor) {
      setCurrentColor(gradientColor);
      setColor(gradientColor);
    }
  }
  function getGradientAngle() {
    var _gradientAST$orientat;
    return (_gradientAST$orientat = gradientAST?.orientation?.value) !== null && _gradientAST$orientat !== void 0 ? _gradientAST$orientat : _constants.DEFAULT_LINEAR_GRADIENT_ANGLE;
  }
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_components.PanelBody, {
    title: (0, _i18n.__)('Gradient Type')
  }, (0, _react.createElement)(_components.RadioControl, {
    selected: gradientType,
    options: gradientOptions,
    onChange: onGradientTypeChange
  })), isLinearGradient(gradientType) && (0, _react.createElement)(_components.PanelBody, {
    style: _style.default.angleControl
  }, (0, _react.createElement)(_components.RangeControl, {
    label: (0, _i18n.__)('Angle'),
    minimumValue: 0,
    maximumValue: 360,
    value: getGradientAngle(),
    onChange: setGradientAngle
  })));
}
var _default = CustomGradientPicker;
exports.default = _default;
//# sourceMappingURL=index.native.js.map