{"version": 3, "names": ["_reactNative", "require", "_compose", "_styles", "_interopRequireDefault", "FooterMessageLink", "href", "value", "textStyle", "usePreferredColorSchemeStyle", "styles", "footerMessageLink", "footerMessageLinkDark", "_react", "createElement", "Text", "style", "onPress", "Linking", "openURL", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/footer-message-link/footer-message-link.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Text, Linking } from 'react-native';\n/**\n * WordPress dependencies\n */\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\n\nfunction FooterMessageLink( { href, value } ) {\n\tconst textStyle = usePreferredColorSchemeStyle(\n\t\tstyles.footerMessageLink,\n\t\tstyles.footerMessageLinkDark\n\t);\n\treturn (\n\t\t<Text style={ textStyle } onPress={ () => Linking.openURL( href ) }>\n\t\t\t{ value }\n\t\t</Text>\n\t);\n}\n\nexport default FooterMessageLink;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAIA,IAAAC,QAAA,GAAAD,OAAA;AAIA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AAXA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAGA,SAASI,iBAAiBA,CAAE;EAAEC,IAAI;EAAEC;AAAM,CAAC,EAAG;EAC7C,MAAMC,SAAS,GAAG,IAAAC,qCAA4B,EAC7CC,eAAM,CAACC,iBAAiB,EACxBD,eAAM,CAACE,qBACR,CAAC;EACD,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACd,YAAA,CAAAe,IAAI;IAACC,KAAK,EAAGR,SAAW;IAACS,OAAO,EAAGA,CAAA,KAAMC,oBAAO,CAACC,OAAO,CAAEb,IAAK;EAAG,GAChEC,KACG,CAAC;AAET;AAAC,IAAAa,QAAA,GAEcf,iBAAiB;AAAAgB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}