{"version": 3, "names": ["_react", "require"], "sources": ["@wordpress/components/src/composite/v2.ts"], "sourcesContent": ["/**\n * Composite is a component that may contain navigable items represented by\n * CompositeItem. It's inspired by the WAI-ARIA Composite Role and implements\n * all the keyboard navigation mechanisms to ensure that there's only one\n * tab stop for the whole Composite element. This means that it can behave as\n * a roving tabindex or aria-activedescendant container.\n *\n * @see https://ariakit.org/components/composite\n */\n\n/* eslint-disable-next-line no-restricted-imports */\nexport {\n\tComposite,\n\tCompositeGroup,\n\tCompositeGroupLabel,\n\tCompositeItem,\n\tCompositeRow,\n\tuseCompositeStore,\n} from '@ariakit/react';\n\n/* eslint-disable-next-line no-restricted-imports */\nexport type { CompositeStore } from '@ariakit/react';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAAA,MAAA,GAAAC,OAAA"}