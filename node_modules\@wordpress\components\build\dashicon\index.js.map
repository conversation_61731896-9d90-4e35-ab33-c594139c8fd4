{"version": 3, "names": ["Dashicon", "icon", "className", "size", "style", "extraProps", "iconClass", "filter", "Boolean", "join", "sizeStyles", "fontSize", "width", "height", "styles", "_react", "createElement", "_default", "exports", "default"], "sources": ["@wordpress/components/src/dashicon/index.tsx"], "sourcesContent": ["/**\n * @typedef OwnProps\n *\n * @property {import('./types').IconKey} icon        Icon name\n * @property {string}                    [className] Class name\n * @property {number}                    [size]      Size of the icon\n */\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport type { DashiconProps } from './types';\n\nfunction Dashicon( {\n\ticon,\n\tclassName,\n\tsize = 20,\n\tstyle = {},\n\t...extraProps\n}: WordPressComponentProps< DashiconProps, 'span', false > ) {\n\tconst iconClass = [\n\t\t'dashicon',\n\t\t'dashicons',\n\t\t'dashicons-' + icon,\n\t\tclassName,\n\t]\n\t\t.filter( Boolean )\n\t\t.join( ' ' );\n\n\t// For retro-compatibility reasons (for example if people are overriding icon size with CSS), we add inline styles just if the size is different to the default\n\tconst sizeStyles =\n\t\t// using `!=` to catch both 20 and \"20\"\n\t\t// eslint-disable-next-line eqeqeq\n\t\t20 != size\n\t\t\t? {\n\t\t\t\t\tfontSize: `${ size }px`,\n\t\t\t\t\twidth: `${ size }px`,\n\t\t\t\t\theight: `${ size }px`,\n\t\t\t  }\n\t\t\t: {};\n\n\tconst styles = {\n\t\t...sizeStyles,\n\t\t...style,\n\t};\n\n\treturn <span className={ iconClass } style={ styles } { ...extraProps } />;\n}\n\nexport default Dashicon;\n"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAIA,SAASA,QAAQA,CAAE;EAClBC,IAAI;EACJC,SAAS;EACTC,IAAI,GAAG,EAAE;EACTC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACqD,CAAC,EAAG;EAC5D,MAAMC,SAAS,GAAG,CACjB,UAAU,EACV,WAAW,EACX,YAAY,GAAGL,IAAI,EACnBC,SAAS,CACT,CACCK,MAAM,CAAEC,OAAQ,CAAC,CACjBC,IAAI,CAAE,GAAI,CAAC;;EAEb;EACA,MAAMC,UAAU;EACf;EACA;EACA,EAAE,IAAIP,IAAI,GACP;IACAQ,QAAQ,EAAG,GAAGR,IAAM,IAAG;IACvBS,KAAK,EAAG,GAAGT,IAAM,IAAG;IACpBU,MAAM,EAAG,GAAGV,IAAM;EAClB,CAAC,GACD,CAAC,CAAC;EAEN,MAAMW,MAAM,GAAG;IACd,GAAGJ,UAAU;IACb,GAAGN;EACJ,CAAC;EAED,OAAO,IAAAW,MAAA,CAAAC,aAAA;IAAMd,SAAS,EAAGI,SAAW;IAACF,KAAK,EAAGU,MAAQ;IAAA,GAAMT;EAAU,CAAI,CAAC;AAC3E;AAAC,IAAAY,QAAA,GAEcjB,QAAQ;AAAAkB,OAAA,CAAAC,OAAA,GAAAF,QAAA"}