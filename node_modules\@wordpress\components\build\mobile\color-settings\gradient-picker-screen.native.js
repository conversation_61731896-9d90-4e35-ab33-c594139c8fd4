"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _native = require("@react-navigation/native");
var _i18n = require("@wordpress/i18n");
var _customGradientPicker = _interopRequireDefault(require("../../custom-gradient-picker"));
var _navBar = _interopRequireDefault(require("../bottom-sheet/nav-bar"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const GradientPickerScreen = () => {
  const navigation = (0, _native.useNavigation)();
  const route = (0, _native.useRoute)();
  const {
    setColor,
    currentValue,
    isGradientColor
  } = route.params;
  return (0, _react.createElement)(_reactNative.View, null, (0, _react.createElement)(_navBar.default, null, (0, _react.createElement)(_navBar.default.BackButton, {
    onPress: navigation.goBack
  }), (0, _react.createElement)(_navBar.default.Heading, null, (0, _i18n.__)('Customize Gradient'))), (0, _react.createElement)(_customGradientPicker.default, {
    setColor: setColor,
    currentValue: currentValue,
    isGradientColor: isGradientColor
  }));
};
var _default = GradientPickerScreen;
exports.default = _default;
//# sourceMappingURL=gradient-picker-screen.native.js.map