{"version": 3, "names": ["_context", "require", "_flex", "_utils", "_get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useHStack", "props", "alignment", "children", "direction", "spacing", "otherProps", "useContextSystem", "align", "getAlignmentProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cloned<PERSON><PERSON><PERSON><PERSON>", "map", "child", "index", "_isSpacer", "hasConnectNamespace", "childElement", "_key", "key", "_react", "createElement", "FlexItem", "isBlock", "propsForFlex", "justify", "gap", "flexProps", "useFlex"], "sources": ["@wordpress/components/src/h-stack/hook.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactElement } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { hasConnectNamespace, useContextSystem } from '../context';\nimport { FlexItem, useFlex } from '../flex';\nimport { getAlignmentProps } from './utils';\nimport { getValidChildren } from '../utils/get-valid-children';\nimport type { Props } from './types';\n\nexport function useHStack( props: WordPressComponentProps< Props, 'div' > ) {\n\tconst {\n\t\talignment = 'edge',\n\t\tchildren,\n\t\tdirection,\n\t\tspacing = 2,\n\t\t...otherProps\n\t} = useContextSystem( props, 'HStack' );\n\n\tconst align = getAlignmentProps( alignment, direction );\n\n\tconst validChildren = getValidChildren( children );\n\tconst clonedChildren = validChildren.map( ( child, index ) => {\n\t\tconst _isSpacer = hasConnectNamespace( child, [ 'Spacer' ] );\n\n\t\tif ( _isSpacer ) {\n\t\t\tconst childElement = child as ReactElement;\n\t\t\tconst _key = childElement.key || `hstack-${ index }`;\n\n\t\t\treturn <FlexItem isBlock key={ _key } { ...childElement.props } />;\n\t\t}\n\n\t\treturn child;\n\t} );\n\n\tconst propsForFlex = {\n\t\tchildren: clonedChildren,\n\t\tdirection,\n\t\tjustify: 'center',\n\t\t...align,\n\t\t...otherProps,\n\t\tgap: spacing,\n\t};\n\n\tconst flexProps = useFlex( propsForFlex );\n\n\treturn flexProps;\n}\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAH,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAQO,SAASI,SAASA,CAAEC,KAA8C,EAAG;EAC3E,MAAM;IACLC,SAAS,GAAG,MAAM;IAClBC,QAAQ;IACRC,SAAS;IACTC,OAAO,GAAG,CAAC;IACX,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEN,KAAK,EAAE,QAAS,CAAC;EAEvC,MAAMO,KAAK,GAAG,IAAAC,wBAAiB,EAAEP,SAAS,EAAEE,SAAU,CAAC;EAEvD,MAAMM,aAAa,GAAG,IAAAC,kCAAgB,EAAER,QAAS,CAAC;EAClD,MAAMS,cAAc,GAAGF,aAAa,CAACG,GAAG,CAAE,CAAEC,KAAK,EAAEC,KAAK,KAAM;IAC7D,MAAMC,SAAS,GAAG,IAAAC,4BAAmB,EAAEH,KAAK,EAAE,CAAE,QAAQ,CAAG,CAAC;IAE5D,IAAKE,SAAS,EAAG;MAChB,MAAME,YAAY,GAAGJ,KAAqB;MAC1C,MAAMK,IAAI,GAAGD,YAAY,CAACE,GAAG,IAAK,UAAUL,KAAO,EAAC;MAEpD,OAAO,IAAAM,MAAA,CAAAC,aAAA,EAACzB,KAAA,CAAA0B,QAAQ;QAACC,OAAO;QAACJ,GAAG,EAAGD,IAAM;QAAA,GAAMD,YAAY,CAACjB;MAAK,CAAI,CAAC;IACnE;IAEA,OAAOa,KAAK;EACb,CAAE,CAAC;EAEH,MAAMW,YAAY,GAAG;IACpBtB,QAAQ,EAAES,cAAc;IACxBR,SAAS;IACTsB,OAAO,EAAE,QAAQ;IACjB,GAAGlB,KAAK;IACR,GAAGF,UAAU;IACbqB,GAAG,EAAEtB;EACN,CAAC;EAED,MAAMuB,SAAS,GAAG,IAAAC,aAAO,EAAEJ,YAAa,CAAC;EAEzC,OAAOG,SAAS;AACjB"}