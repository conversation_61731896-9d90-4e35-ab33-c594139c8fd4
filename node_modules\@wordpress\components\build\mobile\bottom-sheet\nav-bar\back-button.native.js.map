{"version": 3, "names": ["_reactNative", "require", "_i18n", "_icons", "_compose", "_styles", "_interopRequireDefault", "_actionButton", "_chevronBack", "<PERSON><PERSON>", "onPress", "icon", "text", "buttonTextStyle", "usePreferredColorSchemeStyle", "styles", "_react", "createElement", "View", "style", "default", "accessibilityLabel", "__", "accessibilityHint", "Text", "maxFontSizeMultiplier", "BackButton", "chevronLeftStyle", "arrowLeftStyle", "backIcon", "backText", "Platform", "OS", "Icon", "chevronBack", "size", "arrowLeft", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iosText", "close", "Back", "<PERSON><PERSON><PERSON>", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/nav-bar/back-button.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, Platform, Text } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { Icon, arrowLeft, close } from '@wordpress/icons';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './styles.scss';\nimport ActionButton from './action-button';\nimport chevronBack from './../chevron-back';\n\nfunction Button( { onPress, icon, text } ) {\n\tconst buttonTextStyle = usePreferredColorSchemeStyle(\n\t\tstyles[ 'button-text' ],\n\t\tstyles[ 'button-text-dark' ]\n\t);\n\n\treturn (\n\t\t<View style={ styles[ 'back-button' ] }>\n\t\t\t<ActionButton\n\t\t\t\tonPress={ onPress }\n\t\t\t\taccessibilityLabel={ __( 'Go back' ) }\n\t\t\t\taccessibilityHint={ __(\n\t\t\t\t\t'Navigates to the previous content sheet'\n\t\t\t\t) }\n\t\t\t>\n\t\t\t\t{ icon }\n\t\t\t\t{ text && (\n\t\t\t\t\t<Text style={ buttonTextStyle } maxFontSizeMultiplier={ 2 }>\n\t\t\t\t\t\t{ text }\n\t\t\t\t\t</Text>\n\t\t\t\t) }\n\t\t\t</ActionButton>\n\t\t</View>\n\t);\n}\n\nfunction BackButton( { onPress } ) {\n\tconst chevronLeftStyle = usePreferredColorSchemeStyle(\n\t\tstyles[ 'chevron-left-icon' ],\n\t\tstyles[ 'chevron-left-icon-dark' ]\n\t);\n\tconst arrowLeftStyle = usePreferredColorSchemeStyle(\n\t\tstyles[ 'arrow-left-icon' ],\n\t\tstyles[ 'arrow-left-icon-dark' ]\n\t);\n\n\tlet backIcon;\n\tlet backText;\n\n\tif ( Platform.OS === 'ios' ) {\n\t\tbackIcon = (\n\t\t\t<Icon icon={ chevronBack } size={ 21 } style={ chevronLeftStyle } />\n\t\t);\n\t\tbackText = __( 'Back' );\n\t} else {\n\t\tbackIcon = (\n\t\t\t<Icon icon={ arrowLeft } size={ 24 } style={ arrowLeftStyle } />\n\t\t);\n\t}\n\n\treturn <Button onPress={ onPress } icon={ backIcon } text={ backText } />;\n}\n\nfunction DismissButton( { onPress, iosText } ) {\n\tconst arrowLeftStyle = usePreferredColorSchemeStyle(\n\t\tstyles[ 'arrow-left-icon' ],\n\t\tstyles[ 'arrow-left-icon-dark' ]\n\t);\n\n\tlet backIcon;\n\tlet backText;\n\n\tif ( Platform.OS === 'ios' ) {\n\t\tbackText = iosText ? iosText : __( 'Cancel' );\n\t} else {\n\t\tbackIcon = <Icon icon={ close } size={ 24 } style={ arrowLeftStyle } />;\n\t}\n\n\treturn <Button onPress={ onPress } icon={ backIcon } text={ backText } />;\n}\n\nButton.Back = BackButton;\nButton.Dismiss = DismissButton; // Cancel or Close Button.\n\nexport default Button;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,OAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,aAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,YAAA,GAAAF,sBAAA,CAAAL,OAAA;AAjBA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;;AAKA,SAASQ,MAAMA,CAAE;EAAEC,OAAO;EAAEC,IAAI;EAAEC;AAAK,CAAC,EAAG;EAC1C,MAAMC,eAAe,GAAG,IAAAC,qCAA4B,EACnDC,eAAM,CAAE,aAAa,CAAE,EACvBA,eAAM,CAAE,kBAAkB,CAC3B,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACjB,YAAA,CAAAkB,IAAI;IAACC,KAAK,EAAGJ,eAAM,CAAE,aAAa;EAAI,GACtC,IAAAC,MAAA,CAAAC,aAAA,EAACV,aAAA,CAAAa,OAAY;IACZV,OAAO,EAAGA,OAAS;IACnBW,kBAAkB,EAAG,IAAAC,QAAE,EAAE,SAAU,CAAG;IACtCC,iBAAiB,EAAG,IAAAD,QAAE,EACrB,yCACD;EAAG,GAEDX,IAAI,EACJC,IAAI,IACL,IAAAI,MAAA,CAAAC,aAAA,EAACjB,YAAA,CAAAwB,IAAI;IAACL,KAAK,EAAGN,eAAiB;IAACY,qBAAqB,EAAG;EAAG,GACxDb,IACG,CAEM,CACT,CAAC;AAET;AAEA,SAASc,UAAUA,CAAE;EAAEhB;AAAQ,CAAC,EAAG;EAClC,MAAMiB,gBAAgB,GAAG,IAAAb,qCAA4B,EACpDC,eAAM,CAAE,mBAAmB,CAAE,EAC7BA,eAAM,CAAE,wBAAwB,CACjC,CAAC;EACD,MAAMa,cAAc,GAAG,IAAAd,qCAA4B,EAClDC,eAAM,CAAE,iBAAiB,CAAE,EAC3BA,eAAM,CAAE,sBAAsB,CAC/B,CAAC;EAED,IAAIc,QAAQ;EACZ,IAAIC,QAAQ;EAEZ,IAAKC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAG;IAC5BH,QAAQ,GACP,IAAAb,MAAA,CAAAC,aAAA,EAACd,MAAA,CAAA8B,IAAI;MAACtB,IAAI,EAAGuB,oBAAa;MAACC,IAAI,EAAG,EAAI;MAAChB,KAAK,EAAGQ;IAAkB,CAAE,CACnE;IACDG,QAAQ,GAAG,IAAAR,QAAE,EAAE,MAAO,CAAC;EACxB,CAAC,MAAM;IACNO,QAAQ,GACP,IAAAb,MAAA,CAAAC,aAAA,EAACd,MAAA,CAAA8B,IAAI;MAACtB,IAAI,EAAGyB,gBAAW;MAACD,IAAI,EAAG,EAAI;MAAChB,KAAK,EAAGS;IAAgB,CAAE,CAC/D;EACF;EAEA,OAAO,IAAAZ,MAAA,CAAAC,aAAA,EAACR,MAAM;IAACC,OAAO,EAAGA,OAAS;IAACC,IAAI,EAAGkB,QAAU;IAACjB,IAAI,EAAGkB;EAAU,CAAE,CAAC;AAC1E;AAEA,SAASO,aAAaA,CAAE;EAAE3B,OAAO;EAAE4B;AAAQ,CAAC,EAAG;EAC9C,MAAMV,cAAc,GAAG,IAAAd,qCAA4B,EAClDC,eAAM,CAAE,iBAAiB,CAAE,EAC3BA,eAAM,CAAE,sBAAsB,CAC/B,CAAC;EAED,IAAIc,QAAQ;EACZ,IAAIC,QAAQ;EAEZ,IAAKC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAG;IAC5BF,QAAQ,GAAGQ,OAAO,GAAGA,OAAO,GAAG,IAAAhB,QAAE,EAAE,QAAS,CAAC;EAC9C,CAAC,MAAM;IACNO,QAAQ,GAAG,IAAAb,MAAA,CAAAC,aAAA,EAACd,MAAA,CAAA8B,IAAI;MAACtB,IAAI,EAAG4B,YAAO;MAACJ,IAAI,EAAG,EAAI;MAAChB,KAAK,EAAGS;IAAgB,CAAE,CAAC;EACxE;EAEA,OAAO,IAAAZ,MAAA,CAAAC,aAAA,EAACR,MAAM;IAACC,OAAO,EAAGA,OAAS;IAACC,IAAI,EAAGkB,QAAU;IAACjB,IAAI,EAAGkB;EAAU,CAAE,CAAC;AAC1E;AAEArB,MAAM,CAAC+B,IAAI,GAAGd,UAAU;AACxBjB,MAAM,CAACgC,OAAO,GAAGJ,aAAa,CAAC,CAAC;AAAA,IAAAK,QAAA,GAEjBjC,MAAM;AAAAkC,OAAA,CAAAvB,OAAA,GAAAsB,QAAA"}