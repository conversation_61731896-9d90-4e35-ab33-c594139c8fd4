{"version": 3, "names": ["_native", "require", "_stack", "_reactNativeReanimated", "_interopRequireWildcard", "_element", "_compose", "_bottomSheetNavigationContext", "_bottomSheetContext", "_styles", "_interopRequireDefault", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "AnimationSpec", "animation", "config", "duration", "easing", "Easing", "ease", "fadeConfig", "current", "cardStyle", "opacity", "progress", "options", "transitionSpec", "open", "close", "headerShown", "gestureEnabled", "cardStyleInterpolator", "keyboardHandlingEnabled", "HEIGHT_ANIMATION_DURATION", "DEFAULT_HEIGHT", "BottomSheetNavigationContainer", "children", "animate", "main", "theme", "style", "testID", "<PERSON><PERSON>", "useRef", "createStackNavigator", "navigationContext", "useContext", "BottomSheetNavigationContext", "maxHeight", "sheetMaxHeight", "isMaxHeightSet", "isSheetMaxHeightSet", "BottomSheetContext", "currentHeight", "useSharedValue", "value", "backgroundStyle", "usePreferredColorSchemeStyle", "styles", "background", "backgroundDark", "defaultTheme", "useMemo", "DefaultTheme", "colors", "backgroundColor", "_theme", "setHeight", "useCallback", "height", "Math", "round", "newHeight", "min", "shouldAnimate", "withTiming", "out", "cubic", "animatedStyles", "useAnimatedStyle", "screens", "Children", "map", "child", "screen", "name", "otherProps", "props", "cloneElement", "isNested", "_react", "createElement", "Screen", "View", "BottomSheetNavigationProvider", "NavigationContainer", "Navigator", "screenOptions", "detachInactiveScreens", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/bottom-sheet-navigation/navigation-container.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { NavigationContainer, DefaultTheme } from '@react-navigation/native';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport Animated, {\n\tEasing,\n\tuseAnimatedStyle,\n\tuseSharedValue,\n\twithTiming,\n} from 'react-native-reanimated';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tuseContext,\n\tuseMemo,\n\tuseCallback,\n\tChildren,\n\tuseRef,\n\tcloneElement,\n} from '@wordpress/element';\n\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport {\n\tBottomSheetNavigationContext,\n\tBottomSheetNavigationProvider,\n} from './bottom-sheet-navigation-context';\nimport { BottomSheetContext } from '../bottom-sheet-context';\n\nimport styles from './styles.scss';\n\nconst AnimationSpec = {\n\tanimation: 'timing',\n\tconfig: {\n\t\tduration: 200,\n\t\teasing: Easing.ease,\n\t},\n};\n\nconst fadeConfig = ( { current } ) => {\n\treturn {\n\t\tcardStyle: {\n\t\t\topacity: current.progress,\n\t\t},\n\t};\n};\n\nconst options = {\n\ttransitionSpec: {\n\t\topen: AnimationSpec,\n\t\tclose: AnimationSpec,\n\t},\n\theaderShown: false,\n\tgestureEnabled: false,\n\tcardStyleInterpolator: fadeConfig,\n\tkeyboardHandlingEnabled: false,\n};\n\nconst HEIGHT_ANIMATION_DURATION = 300;\nconst DEFAULT_HEIGHT = 1;\n\nfunction BottomSheetNavigationContainer( {\n\tchildren,\n\tanimate,\n\tmain,\n\ttheme,\n\tstyle,\n\ttestID,\n} ) {\n\tconst Stack = useRef( createStackNavigator() ).current;\n\tconst navigationContext = useContext( BottomSheetNavigationContext );\n\tconst { maxHeight: sheetMaxHeight, isMaxHeightSet: isSheetMaxHeightSet } =\n\t\tuseContext( BottomSheetContext );\n\tconst currentHeight = useSharedValue(\n\t\tnavigationContext.currentHeight?.value || DEFAULT_HEIGHT\n\t);\n\n\tconst backgroundStyle = usePreferredColorSchemeStyle(\n\t\tstyles.background,\n\t\tstyles.backgroundDark\n\t);\n\n\tconst defaultTheme = useMemo(\n\t\t() => ( {\n\t\t\t...DefaultTheme,\n\t\t\tcolors: {\n\t\t\t\t...DefaultTheme.colors,\n\t\t\t\tbackground: backgroundStyle.backgroundColor,\n\t\t\t},\n\t\t} ),\n\t\t[ backgroundStyle.backgroundColor ]\n\t);\n\tconst _theme = theme || defaultTheme;\n\n\tconst setHeight = useCallback(\n\t\t( height ) => {\n\t\t\tif (\n\t\t\t\theight > DEFAULT_HEIGHT &&\n\t\t\t\tMath.round( height ) !== Math.round( currentHeight.value )\n\t\t\t) {\n\t\t\t\t// If max height is set in the bottom sheet, we clamp\n\t\t\t\t// the new height using that value.\n\t\t\t\tconst newHeight = isSheetMaxHeightSet\n\t\t\t\t\t? Math.min( sheetMaxHeight, height )\n\t\t\t\t\t: height;\n\t\t\t\tconst shouldAnimate =\n\t\t\t\t\tanimate && currentHeight.value !== DEFAULT_HEIGHT;\n\n\t\t\t\tif ( shouldAnimate ) {\n\t\t\t\t\tcurrentHeight.value = withTiming( newHeight, {\n\t\t\t\t\t\tduration: HEIGHT_ANIMATION_DURATION,\n\t\t\t\t\t\teasing: Easing.out( Easing.cubic ),\n\t\t\t\t\t} );\n\t\t\t\t} else {\n\t\t\t\t\tcurrentHeight.value = newHeight;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t[ animate, currentHeight, isSheetMaxHeightSet, sheetMaxHeight ]\n\t);\n\n\tconst animatedStyles = useAnimatedStyle( () => ( {\n\t\theight: currentHeight.value,\n\t} ) );\n\n\tconst screens = useMemo( () => {\n\t\treturn Children.map( children, ( child ) => {\n\t\t\tlet screen = child;\n\t\t\tconst { name, ...otherProps } = child.props;\n\t\t\tif ( ! main ) {\n\t\t\t\tscreen = cloneElement( child, {\n\t\t\t\t\t...child.props,\n\t\t\t\t\tisNested: true,\n\t\t\t\t} );\n\t\t\t}\n\t\t\treturn (\n\t\t\t\t<Stack.Screen\n\t\t\t\t\tname={ name }\n\t\t\t\t\t{ ...otherProps }\n\t\t\t\t\tchildren={ () => screen }\n\t\t\t\t/>\n\t\t\t);\n\t\t} );\n\t}, [ children, main ] );\n\n\treturn useMemo( () => {\n\t\treturn (\n\t\t\t<Animated.View\n\t\t\t\tstyle={ [ style, animatedStyles ] }\n\t\t\t\ttestID={ testID }\n\t\t\t>\n\t\t\t\t<BottomSheetNavigationProvider\n\t\t\t\t\tvalue={ { setHeight, currentHeight } }\n\t\t\t\t>\n\t\t\t\t\t{ main ? (\n\t\t\t\t\t\t<NavigationContainer theme={ _theme }>\n\t\t\t\t\t\t\t<Stack.Navigator\n\t\t\t\t\t\t\t\tscreenOptions={ options }\n\t\t\t\t\t\t\t\tdetachInactiveScreens={ false }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{ screens }\n\t\t\t\t\t\t\t</Stack.Navigator>\n\t\t\t\t\t\t</NavigationContainer>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<Stack.Navigator\n\t\t\t\t\t\t\tscreenOptions={ options }\n\t\t\t\t\t\t\tdetachInactiveScreens={ false }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ screens }\n\t\t\t\t\t\t</Stack.Navigator>\n\t\t\t\t\t) }\n\t\t\t\t</BottomSheetNavigationProvider>\n\t\t\t</Animated.View>\n\t\t);\n\t}, [\n\t\t_theme,\n\t\tanimatedStyles,\n\t\tcurrentHeight,\n\t\tmain,\n\t\tscreens,\n\t\tsetHeight,\n\t\tstyle,\n\t\ttestID,\n\t] );\n}\n\nexport default BottomSheetNavigationContainer;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,sBAAA,GAAAC,uBAAA,CAAAH,OAAA;AAUA,IAAAI,QAAA,GAAAJ,OAAA;AASA,IAAAK,QAAA,GAAAL,OAAA;AAKA,IAAAM,6BAAA,GAAAN,OAAA;AAIA,IAAAO,mBAAA,GAAAP,OAAA;AAEA,IAAAQ,OAAA,GAAAC,sBAAA,CAAAT,OAAA;AAAmC,SAAAU,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAR,wBAAAY,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAnCnC;AACA;AACA;;AAUA;AACA;AACA;;AAYA;AACA;AACA;;AASA,MAAMW,aAAa,GAAG;EACrBC,SAAS,EAAE,QAAQ;EACnBC,MAAM,EAAE;IACPC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAEC,6BAAM,CAACC;EAChB;AACD,CAAC;AAED,MAAMC,UAAU,GAAGA,CAAE;EAAEC;AAAQ,CAAC,KAAM;EACrC,OAAO;IACNC,SAAS,EAAE;MACVC,OAAO,EAAEF,OAAO,CAACG;IAClB;EACD,CAAC;AACF,CAAC;AAED,MAAMC,OAAO,GAAG;EACfC,cAAc,EAAE;IACfC,IAAI,EAAEd,aAAa;IACnBe,KAAK,EAAEf;EACR,CAAC;EACDgB,WAAW,EAAE,KAAK;EAClBC,cAAc,EAAE,KAAK;EACrBC,qBAAqB,EAAEX,UAAU;EACjCY,uBAAuB,EAAE;AAC1B,CAAC;AAED,MAAMC,yBAAyB,GAAG,GAAG;AACrC,MAAMC,cAAc,GAAG,CAAC;AAExB,SAASC,8BAA8BA,CAAE;EACxCC,QAAQ;EACRC,OAAO;EACPC,IAAI;EACJC,KAAK;EACLC,KAAK;EACLC;AACD,CAAC,EAAG;EACH,MAAMC,KAAK,GAAG,IAAAC,eAAM,EAAE,IAAAC,2BAAoB,EAAC,CAAE,CAAC,CAACvB,OAAO;EACtD,MAAMwB,iBAAiB,GAAG,IAAAC,mBAAU,EAAEC,0DAA6B,CAAC;EACpE,MAAM;IAAEC,SAAS,EAAEC,cAAc;IAAEC,cAAc,EAAEC;EAAoB,CAAC,GACvE,IAAAL,mBAAU,EAAEM,sCAAmB,CAAC;EACjC,MAAMC,aAAa,GAAG,IAAAC,qCAAc,EACnCT,iBAAiB,CAACQ,aAAa,EAAEE,KAAK,IAAIrB,cAC3C,CAAC;EAED,MAAMsB,eAAe,GAAG,IAAAC,qCAA4B,EACnDC,eAAM,CAACC,UAAU,EACjBD,eAAM,CAACE,cACR,CAAC;EAED,MAAMC,YAAY,GAAG,IAAAC,gBAAO,EAC3B,OAAQ;IACP,GAAGC,oBAAY;IACfC,MAAM,EAAE;MACP,GAAGD,oBAAY,CAACC,MAAM;MACtBL,UAAU,EAAEH,eAAe,CAACS;IAC7B;EACD,CAAC,CAAE,EACH,CAAET,eAAe,CAACS,eAAe,CAClC,CAAC;EACD,MAAMC,MAAM,GAAG3B,KAAK,IAAIsB,YAAY;EAEpC,MAAMM,SAAS,GAAG,IAAAC,oBAAW,EAC1BC,MAAM,IAAM;IACb,IACCA,MAAM,GAAGnC,cAAc,IACvBoC,IAAI,CAACC,KAAK,CAAEF,MAAO,CAAC,KAAKC,IAAI,CAACC,KAAK,CAAElB,aAAa,CAACE,KAAM,CAAC,EACzD;MACD;MACA;MACA,MAAMiB,SAAS,GAAGrB,mBAAmB,GAClCmB,IAAI,CAACG,GAAG,CAAExB,cAAc,EAAEoB,MAAO,CAAC,GAClCA,MAAM;MACT,MAAMK,aAAa,GAClBrC,OAAO,IAAIgB,aAAa,CAACE,KAAK,KAAKrB,cAAc;MAElD,IAAKwC,aAAa,EAAG;QACpBrB,aAAa,CAACE,KAAK,GAAG,IAAAoB,iCAAU,EAAEH,SAAS,EAAE;UAC5CxD,QAAQ,EAAEiB,yBAAyB;UACnChB,MAAM,EAAEC,6BAAM,CAAC0D,GAAG,CAAE1D,6BAAM,CAAC2D,KAAM;QAClC,CAAE,CAAC;MACJ,CAAC,MAAM;QACNxB,aAAa,CAACE,KAAK,GAAGiB,SAAS;MAChC;IACD;EACD,CAAC,EACD,CAAEnC,OAAO,EAAEgB,aAAa,EAAEF,mBAAmB,EAAEF,cAAc,CAC9D,CAAC;EAED,MAAM6B,cAAc,GAAG,IAAAC,uCAAgB,EAAE,OAAQ;IAChDV,MAAM,EAAEhB,aAAa,CAACE;EACvB,CAAC,CAAG,CAAC;EAEL,MAAMyB,OAAO,GAAG,IAAAlB,gBAAO,EAAE,MAAM;IAC9B,OAAOmB,iBAAQ,CAACC,GAAG,CAAE9C,QAAQ,EAAI+C,KAAK,IAAM;MAC3C,IAAIC,MAAM,GAAGD,KAAK;MAClB,MAAM;QAAEE,IAAI;QAAE,GAAGC;MAAW,CAAC,GAAGH,KAAK,CAACI,KAAK;MAC3C,IAAK,CAAEjD,IAAI,EAAG;QACb8C,MAAM,GAAG,IAAAI,qBAAY,EAAEL,KAAK,EAAE;UAC7B,GAAGA,KAAK,CAACI,KAAK;UACdE,QAAQ,EAAE;QACX,CAAE,CAAC;MACJ;MACA,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACjD,KAAK,CAACkD,MAAM;QACZP,IAAI,EAAGA,IAAM;QAAA,GACRC,UAAU;QACflD,QAAQ,EAAGA,CAAA,KAAMgD;MAAQ,CACzB,CAAC;IAEJ,CAAE,CAAC;EACJ,CAAC,EAAE,CAAEhD,QAAQ,EAAEE,IAAI,CAAG,CAAC;EAEvB,OAAO,IAAAwB,gBAAO,EAAE,MAAM;IACrB,OACC,IAAA4B,MAAA,CAAAC,aAAA,EAAC5G,sBAAA,CAAAe,OAAQ,CAAC+F,IAAI;MACbrD,KAAK,EAAG,CAAEA,KAAK,EAAEsC,cAAc,CAAI;MACnCrC,MAAM,EAAGA;IAAQ,GAEjB,IAAAiD,MAAA,CAAAC,aAAA,EAACxG,6BAAA,CAAA2G,6BAA6B;MAC7BvC,KAAK,EAAG;QAAEY,SAAS;QAAEd;MAAc;IAAG,GAEpCf,IAAI,GACL,IAAAoD,MAAA,CAAAC,aAAA,EAAC/G,OAAA,CAAAmH,mBAAmB;MAACxD,KAAK,EAAG2B;IAAQ,GACpC,IAAAwB,MAAA,CAAAC,aAAA,EAACjD,KAAK,CAACsD,SAAS;MACfC,aAAa,EAAGxE,OAAS;MACzByE,qBAAqB,EAAG;IAAO,GAE7BlB,OACc,CACG,CAAC,GAEtB,IAAAU,MAAA,CAAAC,aAAA,EAACjD,KAAK,CAACsD,SAAS;MACfC,aAAa,EAAGxE,OAAS;MACzByE,qBAAqB,EAAG;IAAO,GAE7BlB,OACc,CAEY,CACjB,CAAC;EAElB,CAAC,EAAE,CACFd,MAAM,EACNY,cAAc,EACdzB,aAAa,EACbf,IAAI,EACJ0C,OAAO,EACPb,SAAS,EACT3B,KAAK,EACLC,MAAM,CACL,CAAC;AACJ;AAAC,IAAA0D,QAAA,GAEchE,8BAA8B;AAAAiE,OAAA,CAAAtG,OAAA,GAAAqG,QAAA"}