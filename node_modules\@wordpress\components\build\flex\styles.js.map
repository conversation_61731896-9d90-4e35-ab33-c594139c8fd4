{"version": 3, "names": ["_react", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "Flex", "process", "env", "NODE_ENV", "name", "styles", "map", "toString", "exports", "<PERSON><PERSON>", "block", "ItemsColumn", "ItemsRow"], "sources": ["@wordpress/components/src/flex/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\nexport const Flex = css`\n\tdisplay: flex;\n`;\n\nexport const Item = css`\n\tdisplay: block;\n\tmax-height: 100%;\n\tmax-width: 100%;\n\tmin-height: 0;\n\tmin-width: 0;\n`;\n\nexport const block = css`\n\tflex: 1;\n`;\n\n/**\n * Workaround to optimize DOM rendering.\n * We'll enhance alignment with naive parent flex assumptions.\n *\n * Trade-off:\n * Far less DOM less. However, UI rendering is not as reliable.\n */\n\n/**\n * Improves stability of width/height rendering.\n * https://github.com/ItsJonQ/g2/pull/149\n */\nexport const ItemsColumn = css`\n\t> * {\n\t\tmin-height: 0;\n\t}\n`;\n\nexport const ItemsRow = css`\n\t> * {\n\t\tmin-width: 0;\n\t}\n`;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAAqC,SAAAC,iCAAA;AAE9B,MAAMC,IAAI,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAEhB;AAACS,OAAA,CAAAR,IAAA,GAAAA,IAAA;AAEK,MAAMS,IAAI,GAAAR,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAMhB;AAACS,OAAA,CAAAC,IAAA,GAAAA,IAAA;AAEK,MAAMC,KAAK,GAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAEjB;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAHAS,OAAA,CAAAE,KAAA,GAAAA,KAAA;AAIO,MAAMC,WAAW,GAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAIvB;AAACS,OAAA,CAAAG,WAAA,GAAAA,WAAA;AAEK,MAAMC,QAAQ,GAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAIpB;AAACS,OAAA,CAAAI,QAAA,GAAAA,QAAA"}