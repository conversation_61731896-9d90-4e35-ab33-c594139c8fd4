{"version": 3, "names": ["_reactNative", "require", "_element", "_huePicker", "_interopRequireDefault", "_saturationPicker", "_styleNative", "HsvColorPicker", "props", "max<PERSON><PERSON><PERSON>", "Dimensions", "get", "width", "satValPickerRef", "useRef", "containerStyle", "currentColor", "huePickerContainerStyle", "huePickerBorderRadius", "huePickerHue", "huePickerBarWidth", "huePickerBarHeight", "huePickerSliderSize", "onHuePickerDragStart", "onHuePickerDragMove", "onHuePickerDragEnd", "onHuePickerDragTerminate", "onHuePickerPress", "satValPickerContainerStyle", "satValPickerBorderRadius", "satValPickerSize", "height", "satValPickerSliderSize", "satValPickerHue", "satValPickerSaturation", "satValPickerValue", "onSatValPickerDragStart", "onSatValPickerDragMove", "onSatValPickerDragEnd", "onSatValPickerDragTerminate", "onSatValPickerPress", "_react", "createElement", "View", "style", "styles", "testID", "default", "borderRadius", "size", "sliderSize", "hue", "saturation", "value", "onDragStart", "onDragMove", "onDragEnd", "onDragTerminate", "onPress", "ref", "<PERSON><PERSON><PERSON><PERSON>", "barHeight", "_default", "exports"], "sources": ["@wordpress/components/src/color-picker/hsv-color-picker.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, Dimensions } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { useRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport HuePicker from './hue-picker';\nimport SaturationValuePicker from './saturation-picker';\nimport styles from './style.native.scss';\n\nconst HsvColorPicker = ( props ) => {\n\tconst maxWidth = Dimensions.get( 'window' ).width - 32;\n\tconst satValPickerRef = useRef( null );\n\n\tconst {\n\t\tcontainerStyle = {},\n\t\tcurrentColor,\n\t\thuePickerContainerStyle = {},\n\t\thuePickerBorderRadius = 0,\n\t\thuePickerHue = 0,\n\t\thuePickerBarWidth = maxWidth,\n\t\thuePickerBarHeight = 12,\n\t\thuePickerSliderSize = 24,\n\t\tonHuePickerDragStart,\n\t\tonHuePickerDragMove,\n\t\tonHuePickerDragEnd,\n\t\tonHuePickerDragTerminate,\n\t\tonHuePickerPress,\n\t\tsatValPickerContainerStyle = {},\n\t\tsatValPickerBorderRadius = 0,\n\t\tsatValPickerSize = { width: maxWidth, height: 200 },\n\t\tsatValPickerSliderSize = 24,\n\t\tsatValPickerHue = 0,\n\t\tsatValPickerSaturation = 1,\n\t\tsatValPickerValue = 1,\n\t\tonSatValPickerDragStart,\n\t\tonSatValPickerDragMove,\n\t\tonSatValPickerDragEnd,\n\t\tonSatValPickerDragTerminate,\n\t\tonSatValPickerPress,\n\t} = props;\n\n\treturn (\n\t\t<View\n\t\t\tstyle={ [ styles[ 'hsv-container' ], containerStyle ] }\n\t\t\ttestID=\"hsv-color-picker\"\n\t\t>\n\t\t\t<SaturationValuePicker\n\t\t\t\tcontainerStyle={ satValPickerContainerStyle }\n\t\t\t\tcurrentColor={ currentColor }\n\t\t\t\tborderRadius={ satValPickerBorderRadius }\n\t\t\t\tsize={ satValPickerSize }\n\t\t\t\tsliderSize={ satValPickerSliderSize }\n\t\t\t\thue={ satValPickerHue }\n\t\t\t\tsaturation={ satValPickerSaturation }\n\t\t\t\tvalue={ satValPickerValue }\n\t\t\t\tonDragStart={ onSatValPickerDragStart }\n\t\t\t\tonDragMove={ onSatValPickerDragMove }\n\t\t\t\tonDragEnd={ onSatValPickerDragEnd }\n\t\t\t\tonDragTerminate={ onSatValPickerDragTerminate }\n\t\t\t\tonPress={ onSatValPickerPress }\n\t\t\t\tref={ satValPickerRef }\n\t\t\t/>\n\t\t\t<HuePicker\n\t\t\t\tcontainerStyle={ huePickerContainerStyle }\n\t\t\t\tborderRadius={ huePickerBorderRadius }\n\t\t\t\thue={ huePickerHue }\n\t\t\t\tbarWidth={ huePickerBarWidth }\n\t\t\t\tbarHeight={ huePickerBarHeight }\n\t\t\t\tsliderSize={ huePickerSliderSize }\n\t\t\t\tonDragStart={ onHuePickerDragStart }\n\t\t\t\tonDragMove={ onHuePickerDragMove }\n\t\t\t\tonDragEnd={ onHuePickerDragEnd }\n\t\t\t\tonDragTerminate={ onHuePickerDragTerminate }\n\t\t\t\tonPress={ onHuePickerPress }\n\t\t\t/>\n\t\t</View>\n\t);\n};\n\nexport default HsvColorPicker;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,UAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,YAAA,GAAAF,sBAAA,CAAAH,OAAA;AAfA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAKA,MAAMM,cAAc,GAAKC,KAAK,IAAM;EACnC,MAAMC,QAAQ,GAAGC,uBAAU,CAACC,GAAG,CAAE,QAAS,CAAC,CAACC,KAAK,GAAG,EAAE;EACtD,MAAMC,eAAe,GAAG,IAAAC,eAAM,EAAE,IAAK,CAAC;EAEtC,MAAM;IACLC,cAAc,GAAG,CAAC,CAAC;IACnBC,YAAY;IACZC,uBAAuB,GAAG,CAAC,CAAC;IAC5BC,qBAAqB,GAAG,CAAC;IACzBC,YAAY,GAAG,CAAC;IAChBC,iBAAiB,GAAGX,QAAQ;IAC5BY,kBAAkB,GAAG,EAAE;IACvBC,mBAAmB,GAAG,EAAE;IACxBC,oBAAoB;IACpBC,mBAAmB;IACnBC,kBAAkB;IAClBC,wBAAwB;IACxBC,gBAAgB;IAChBC,0BAA0B,GAAG,CAAC,CAAC;IAC/BC,wBAAwB,GAAG,CAAC;IAC5BC,gBAAgB,GAAG;MAAElB,KAAK,EAAEH,QAAQ;MAAEsB,MAAM,EAAE;IAAI,CAAC;IACnDC,sBAAsB,GAAG,EAAE;IAC3BC,eAAe,GAAG,CAAC;IACnBC,sBAAsB,GAAG,CAAC;IAC1BC,iBAAiB,GAAG,CAAC;IACrBC,uBAAuB;IACvBC,sBAAsB;IACtBC,qBAAqB;IACrBC,2BAA2B;IAC3BC;EACD,CAAC,GAAGhC,KAAK;EAET,OACC,IAAAiC,MAAA,CAAAC,aAAA,EAAC1C,YAAA,CAAA2C,IAAI;IACJC,KAAK,EAAG,CAAEC,oBAAM,CAAE,eAAe,CAAE,EAAE9B,cAAc,CAAI;IACvD+B,MAAM,EAAC;EAAkB,GAEzB,IAAAL,MAAA,CAAAC,aAAA,EAACrC,iBAAA,CAAA0C,OAAqB;IACrBhC,cAAc,EAAGa,0BAA4B;IAC7CZ,YAAY,EAAGA,YAAc;IAC7BgC,YAAY,EAAGnB,wBAA0B;IACzCoB,IAAI,EAAGnB,gBAAkB;IACzBoB,UAAU,EAAGlB,sBAAwB;IACrCmB,GAAG,EAAGlB,eAAiB;IACvBmB,UAAU,EAAGlB,sBAAwB;IACrCmB,KAAK,EAAGlB,iBAAmB;IAC3BmB,WAAW,EAAGlB,uBAAyB;IACvCmB,UAAU,EAAGlB,sBAAwB;IACrCmB,SAAS,EAAGlB,qBAAuB;IACnCmB,eAAe,EAAGlB,2BAA6B;IAC/CmB,OAAO,EAAGlB,mBAAqB;IAC/BmB,GAAG,EAAG9C;EAAiB,CACvB,CAAC,EACF,IAAA4B,MAAA,CAAAC,aAAA,EAACvC,UAAA,CAAA4C,OAAS;IACThC,cAAc,EAAGE,uBAAyB;IAC1C+B,YAAY,EAAG9B,qBAAuB;IACtCiC,GAAG,EAAGhC,YAAc;IACpByC,QAAQ,EAAGxC,iBAAmB;IAC9ByC,SAAS,EAAGxC,kBAAoB;IAChC6B,UAAU,EAAG5B,mBAAqB;IAClCgC,WAAW,EAAG/B,oBAAsB;IACpCgC,UAAU,EAAG/B,mBAAqB;IAClCgC,SAAS,EAAG/B,kBAAoB;IAChCgC,eAAe,EAAG/B,wBAA0B;IAC5CgC,OAAO,EAAG/B;EAAkB,CAC5B,CACI,CAAC;AAET,CAAC;AAAC,IAAAmC,QAAA,GAEavD,cAAc;AAAAwD,OAAA,CAAAhB,OAAA,GAAAe,QAAA"}