{"version": 3, "names": ["_compose", "require", "_element", "dragImageClass", "cloneWrapperClass", "clonePadding", "bodyClass", "Draggable", "children", "onDragStart", "onDragOver", "onDragEnd", "appendToOwnerDocument", "cloneClassname", "elementId", "transferData", "__experimentalTransferDataType", "transferDataType", "__experimentalDragComponent", "dragComponent", "dragComponentRef", "useRef", "cleanup", "end", "event", "preventDefault", "current", "start", "ownerDocument", "target", "dataTransfer", "setData", "JSON", "stringify", "cloneWrapper", "createElement", "style", "top", "left", "dragImage", "setDragImage", "classList", "add", "body", "append<PERSON><PERSON><PERSON>", "x", "y", "clientX", "clientY", "transform", "clonedDragComponent", "innerHTML", "element", "getElementById", "elementRect", "getBoundingClientRect", "elementWrapper", "parentNode", "elementTopOffset", "elementLeftOffset", "width", "clone", "cloneNode", "id", "Array", "from", "querySelectorAll", "for<PERSON>ach", "child", "<PERSON><PERSON><PERSON><PERSON>", "cursor<PERSON><PERSON>t", "cursorTop", "over", "e", "nextX", "nextY", "throttledDragOver", "throttle", "addEventListener", "remove", "removeEventListener", "useEffect", "_react", "Fragment", "onDraggableStart", "onDraggableEnd", "className", "display", "ref", "_default", "exports", "default"], "sources": ["@wordpress/components/src/draggable/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { DragEvent } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { throttle } from '@wordpress/compose';\nimport { useEffect, useRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { DraggableProps } from './types';\n\nconst dragImageClass = 'components-draggable__invisible-drag-image';\nconst cloneWrapperClass = 'components-draggable__clone';\nconst clonePadding = 0;\nconst bodyClass = 'is-dragging-components-draggable';\n\n/**\n * `Draggable` is a Component that provides a way to set up a cross-browser\n * (including IE) customizable drag image and the transfer data for the drag\n * event. It decouples the drag handle and the element to drag: use it by\n * wrapping the component that will become the drag handle and providing the DOM\n * ID of the element to drag.\n *\n * Note that the drag handle needs to declare the `draggable=\"true\"` property\n * and bind the `Draggable`s `onDraggableStart` and `onDraggableEnd` event\n * handlers to its own `onDragStart` and `onDragEnd` respectively. `Draggable`\n * takes care of the logic to setup the drag image and the transfer data, but is\n * not concerned with creating an actual DOM element that is draggable.\n *\n * ```jsx\n * import { Draggable, Panel, PanelBody } from '@wordpress/components';\n * import { Icon, more } from '@wordpress/icons';\n *\n * const MyDraggable = () => (\n *   <div id=\"draggable-panel\">\n *     <Panel header=\"Draggable panel\">\n *       <PanelBody>\n *         <Draggable elementId=\"draggable-panel\" transferData={ {} }>\n *           { ( { onDraggableStart, onDraggableEnd } ) => (\n *             <div\n *               className=\"example-drag-handle\"\n *               draggable\n *               onDragStart={ onDraggableStart }\n *               onDragEnd={ onDraggableEnd }\n *             >\n *               <Icon icon={ more } />\n *             </div>\n *           ) }\n *         </Draggable>\n *       </PanelBody>\n *     </Panel>\n *   </div>\n * );\n * ```\n */\nexport function Draggable( {\n\tchildren,\n\tonDragStart,\n\tonDragOver,\n\tonDragEnd,\n\tappendToOwnerDocument = false,\n\tcloneClassname,\n\telementId,\n\ttransferData,\n\t__experimentalTransferDataType: transferDataType = 'text',\n\t__experimentalDragComponent: dragComponent,\n}: DraggableProps ) {\n\tconst dragComponentRef = useRef< HTMLDivElement >( null );\n\tconst cleanup = useRef( () => {} );\n\n\t/**\n\t * Removes the element clone, resets cursor, and removes drag listener.\n\t *\n\t * @param event The non-custom DragEvent.\n\t */\n\tfunction end( event: DragEvent ) {\n\t\tevent.preventDefault();\n\t\tcleanup.current();\n\n\t\tif ( onDragEnd ) {\n\t\t\tonDragEnd( event );\n\t\t}\n\t}\n\n\t/**\n\t * This method does a couple of things:\n\t *\n\t * - Clones the current element and spawns clone over original element.\n\t * - Adds a fake temporary drag image to avoid browser defaults.\n\t * - Sets transfer data.\n\t * - Adds dragover listener.\n\t *\n\t * @param event The non-custom DragEvent.\n\t */\n\tfunction start( event: DragEvent ) {\n\t\tconst { ownerDocument } = event.target as HTMLElement;\n\n\t\tevent.dataTransfer.setData(\n\t\t\ttransferDataType,\n\t\t\tJSON.stringify( transferData )\n\t\t);\n\n\t\tconst cloneWrapper = ownerDocument.createElement( 'div' );\n\t\t// Reset position to 0,0. Natural stacking order will position this lower, even with a transform otherwise.\n\t\tcloneWrapper.style.top = '0';\n\t\tcloneWrapper.style.left = '0';\n\n\t\tconst dragImage = ownerDocument.createElement( 'div' );\n\n\t\t// Set a fake drag image to avoid browser defaults. Remove from DOM\n\t\t// right after. event.dataTransfer.setDragImage is not supported yet in\n\t\t// IE, we need to check for its existence first.\n\t\tif ( 'function' === typeof event.dataTransfer.setDragImage ) {\n\t\t\tdragImage.classList.add( dragImageClass );\n\t\t\townerDocument.body.appendChild( dragImage );\n\t\t\tevent.dataTransfer.setDragImage( dragImage, 0, 0 );\n\t\t}\n\n\t\tcloneWrapper.classList.add( cloneWrapperClass );\n\n\t\tif ( cloneClassname ) {\n\t\t\tcloneWrapper.classList.add( cloneClassname );\n\t\t}\n\n\t\tlet x = 0;\n\t\tlet y = 0;\n\t\t// If a dragComponent is defined, the following logic will clone the\n\t\t// HTML node and inject it into the cloneWrapper.\n\t\tif ( dragComponentRef.current ) {\n\t\t\t// Position dragComponent at the same position as the cursor.\n\t\t\tx = event.clientX;\n\t\t\ty = event.clientY;\n\t\t\tcloneWrapper.style.transform = `translate( ${ x }px, ${ y }px )`;\n\n\t\t\tconst clonedDragComponent = ownerDocument.createElement( 'div' );\n\t\t\tclonedDragComponent.innerHTML = dragComponentRef.current.innerHTML;\n\t\t\tcloneWrapper.appendChild( clonedDragComponent );\n\n\t\t\t// Inject the cloneWrapper into the DOM.\n\t\t\townerDocument.body.appendChild( cloneWrapper );\n\t\t} else {\n\t\t\tconst element = ownerDocument.getElementById(\n\t\t\t\telementId\n\t\t\t) as HTMLElement;\n\n\t\t\t// Prepare element clone and append to element wrapper.\n\t\t\tconst elementRect = element.getBoundingClientRect();\n\t\t\tconst elementWrapper = element.parentNode;\n\t\t\tconst elementTopOffset = elementRect.top;\n\t\t\tconst elementLeftOffset = elementRect.left;\n\n\t\t\tcloneWrapper.style.width = `${\n\t\t\t\telementRect.width + clonePadding * 2\n\t\t\t}px`;\n\n\t\t\tconst clone = element.cloneNode( true ) as HTMLElement;\n\t\t\tclone.id = `clone-${ elementId }`;\n\n\t\t\t// Position clone right over the original element (20px padding).\n\t\t\tx = elementLeftOffset - clonePadding;\n\t\t\ty = elementTopOffset - clonePadding;\n\t\t\tcloneWrapper.style.transform = `translate( ${ x }px, ${ y }px )`;\n\n\t\t\t// Hack: Remove iFrames as it's causing the embeds drag clone to freeze.\n\t\t\tArray.from< HTMLIFrameElement >(\n\t\t\t\tclone.querySelectorAll( 'iframe' )\n\t\t\t).forEach( ( child ) => child.parentNode?.removeChild( child ) );\n\n\t\t\tcloneWrapper.appendChild( clone );\n\n\t\t\t// Inject the cloneWrapper into the DOM.\n\t\t\tif ( appendToOwnerDocument ) {\n\t\t\t\townerDocument.body.appendChild( cloneWrapper );\n\t\t\t} else {\n\t\t\t\telementWrapper?.appendChild( cloneWrapper );\n\t\t\t}\n\t\t}\n\n\t\t// Mark the current cursor coordinates.\n\t\tlet cursorLeft = event.clientX;\n\t\tlet cursorTop = event.clientY;\n\n\t\tfunction over( e: DragEvent ) {\n\t\t\t// Skip doing any work if mouse has not moved.\n\t\t\tif ( cursorLeft === e.clientX && cursorTop === e.clientY ) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst nextX = x + e.clientX - cursorLeft;\n\t\t\tconst nextY = y + e.clientY - cursorTop;\n\t\t\tcloneWrapper.style.transform = `translate( ${ nextX }px, ${ nextY }px )`;\n\t\t\tcursorLeft = e.clientX;\n\t\t\tcursorTop = e.clientY;\n\t\t\tx = nextX;\n\t\t\ty = nextY;\n\t\t\tif ( onDragOver ) {\n\t\t\t\tonDragOver( e );\n\t\t\t}\n\t\t}\n\n\t\t// Aim for 60fps (16 ms per frame) for now. We can potentially use requestAnimationFrame (raf) instead,\n\t\t// note that browsers may throttle raf below 60fps in certain conditions.\n\t\t// @ts-ignore\n\t\tconst throttledDragOver = throttle( over, 16 );\n\n\t\townerDocument.addEventListener( 'dragover', throttledDragOver );\n\n\t\t// Update cursor to 'grabbing', document wide.\n\t\townerDocument.body.classList.add( bodyClass );\n\n\t\tif ( onDragStart ) {\n\t\t\tonDragStart( event );\n\t\t}\n\n\t\tcleanup.current = () => {\n\t\t\t// Remove drag clone.\n\t\t\tif ( cloneWrapper && cloneWrapper.parentNode ) {\n\t\t\t\tcloneWrapper.parentNode.removeChild( cloneWrapper );\n\t\t\t}\n\n\t\t\tif ( dragImage && dragImage.parentNode ) {\n\t\t\t\tdragImage.parentNode.removeChild( dragImage );\n\t\t\t}\n\n\t\t\t// Reset cursor.\n\t\t\townerDocument.body.classList.remove( bodyClass );\n\n\t\t\townerDocument.removeEventListener( 'dragover', throttledDragOver );\n\t\t};\n\t}\n\n\tuseEffect(\n\t\t() => () => {\n\t\t\tcleanup.current();\n\t\t},\n\t\t[]\n\t);\n\n\treturn (\n\t\t<>\n\t\t\t{ children( {\n\t\t\t\tonDraggableStart: start,\n\t\t\t\tonDraggableEnd: end,\n\t\t\t} ) }\n\t\t\t{ dragComponent && (\n\t\t\t\t<div\n\t\t\t\t\tclassName=\"components-draggable-drag-component-root\"\n\t\t\t\t\tstyle={ { display: 'none' } }\n\t\t\t\t\tref={ dragComponentRef }\n\t\t\t\t>\n\t\t\t\t\t{ dragComponent }\n\t\t\t\t</div>\n\t\t\t) }\n\t\t</>\n\t);\n}\n\nexport default Draggable;\n"], "mappings": ";;;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AASA,MAAME,cAAc,GAAG,4CAA4C;AACnE,MAAMC,iBAAiB,GAAG,6BAA6B;AACvD,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,SAAS,GAAG,kCAAkC;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,SAASA,CAAE;EAC1BC,QAAQ;EACRC,WAAW;EACXC,UAAU;EACVC,SAAS;EACTC,qBAAqB,GAAG,KAAK;EAC7BC,cAAc;EACdC,SAAS;EACTC,YAAY;EACZC,8BAA8B,EAAEC,gBAAgB,GAAG,MAAM;EACzDC,2BAA2B,EAAEC;AACd,CAAC,EAAG;EACnB,MAAMC,gBAAgB,GAAG,IAAAC,eAAM,EAAoB,IAAK,CAAC;EACzD,MAAMC,OAAO,GAAG,IAAAD,eAAM,EAAE,MAAM,CAAC,CAAE,CAAC;;EAElC;AACD;AACA;AACA;AACA;EACC,SAASE,GAAGA,CAAEC,KAAgB,EAAG;IAChCA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBH,OAAO,CAACI,OAAO,CAAC,CAAC;IAEjB,IAAKf,SAAS,EAAG;MAChBA,SAAS,CAAEa,KAAM,CAAC;IACnB;EACD;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASG,KAAKA,CAAEH,KAAgB,EAAG;IAClC,MAAM;MAAEI;IAAc,CAAC,GAAGJ,KAAK,CAACK,MAAqB;IAErDL,KAAK,CAACM,YAAY,CAACC,OAAO,CACzBd,gBAAgB,EAChBe,IAAI,CAACC,SAAS,CAAElB,YAAa,CAC9B,CAAC;IAED,MAAMmB,YAAY,GAAGN,aAAa,CAACO,aAAa,CAAE,KAAM,CAAC;IACzD;IACAD,YAAY,CAACE,KAAK,CAACC,GAAG,GAAG,GAAG;IAC5BH,YAAY,CAACE,KAAK,CAACE,IAAI,GAAG,GAAG;IAE7B,MAAMC,SAAS,GAAGX,aAAa,CAACO,aAAa,CAAE,KAAM,CAAC;;IAEtD;IACA;IACA;IACA,IAAK,UAAU,KAAK,OAAOX,KAAK,CAACM,YAAY,CAACU,YAAY,EAAG;MAC5DD,SAAS,CAACE,SAAS,CAACC,GAAG,CAAEvC,cAAe,CAAC;MACzCyB,aAAa,CAACe,IAAI,CAACC,WAAW,CAAEL,SAAU,CAAC;MAC3Cf,KAAK,CAACM,YAAY,CAACU,YAAY,CAAED,SAAS,EAAE,CAAC,EAAE,CAAE,CAAC;IACnD;IAEAL,YAAY,CAACO,SAAS,CAACC,GAAG,CAAEtC,iBAAkB,CAAC;IAE/C,IAAKS,cAAc,EAAG;MACrBqB,YAAY,CAACO,SAAS,CAACC,GAAG,CAAE7B,cAAe,CAAC;IAC7C;IAEA,IAAIgC,CAAC,GAAG,CAAC;IACT,IAAIC,CAAC,GAAG,CAAC;IACT;IACA;IACA,IAAK1B,gBAAgB,CAACM,OAAO,EAAG;MAC/B;MACAmB,CAAC,GAAGrB,KAAK,CAACuB,OAAO;MACjBD,CAAC,GAAGtB,KAAK,CAACwB,OAAO;MACjBd,YAAY,CAACE,KAAK,CAACa,SAAS,GAAI,cAAcJ,CAAG,OAAOC,CAAG,MAAK;MAEhE,MAAMI,mBAAmB,GAAGtB,aAAa,CAACO,aAAa,CAAE,KAAM,CAAC;MAChEe,mBAAmB,CAACC,SAAS,GAAG/B,gBAAgB,CAACM,OAAO,CAACyB,SAAS;MAClEjB,YAAY,CAACU,WAAW,CAAEM,mBAAoB,CAAC;;MAE/C;MACAtB,aAAa,CAACe,IAAI,CAACC,WAAW,CAAEV,YAAa,CAAC;IAC/C,CAAC,MAAM;MACN,MAAMkB,OAAO,GAAGxB,aAAa,CAACyB,cAAc,CAC3CvC,SACD,CAAgB;;MAEhB;MACA,MAAMwC,WAAW,GAAGF,OAAO,CAACG,qBAAqB,CAAC,CAAC;MACnD,MAAMC,cAAc,GAAGJ,OAAO,CAACK,UAAU;MACzC,MAAMC,gBAAgB,GAAGJ,WAAW,CAACjB,GAAG;MACxC,MAAMsB,iBAAiB,GAAGL,WAAW,CAAChB,IAAI;MAE1CJ,YAAY,CAACE,KAAK,CAACwB,KAAK,GAAI,GAC3BN,WAAW,CAACM,KAAK,GAAGvD,YAAY,GAAG,CACnC,IAAG;MAEJ,MAAMwD,KAAK,GAAGT,OAAO,CAACU,SAAS,CAAE,IAAK,CAAgB;MACtDD,KAAK,CAACE,EAAE,GAAI,SAASjD,SAAW,EAAC;;MAEjC;MACA+B,CAAC,GAAGc,iBAAiB,GAAGtD,YAAY;MACpCyC,CAAC,GAAGY,gBAAgB,GAAGrD,YAAY;MACnC6B,YAAY,CAACE,KAAK,CAACa,SAAS,GAAI,cAAcJ,CAAG,OAAOC,CAAG,MAAK;;MAEhE;MACAkB,KAAK,CAACC,IAAI,CACTJ,KAAK,CAACK,gBAAgB,CAAE,QAAS,CAClC,CAAC,CAACC,OAAO,CAAIC,KAAK,IAAMA,KAAK,CAACX,UAAU,EAAEY,WAAW,CAAED,KAAM,CAAE,CAAC;MAEhElC,YAAY,CAACU,WAAW,CAAEiB,KAAM,CAAC;;MAEjC;MACA,IAAKjD,qBAAqB,EAAG;QAC5BgB,aAAa,CAACe,IAAI,CAACC,WAAW,CAAEV,YAAa,CAAC;MAC/C,CAAC,MAAM;QACNsB,cAAc,EAAEZ,WAAW,CAAEV,YAAa,CAAC;MAC5C;IACD;;IAEA;IACA,IAAIoC,UAAU,GAAG9C,KAAK,CAACuB,OAAO;IAC9B,IAAIwB,SAAS,GAAG/C,KAAK,CAACwB,OAAO;IAE7B,SAASwB,IAAIA,CAAEC,CAAY,EAAG;MAC7B;MACA,IAAKH,UAAU,KAAKG,CAAC,CAAC1B,OAAO,IAAIwB,SAAS,KAAKE,CAAC,CAACzB,OAAO,EAAG;QAC1D;MACD;MACA,MAAM0B,KAAK,GAAG7B,CAAC,GAAG4B,CAAC,CAAC1B,OAAO,GAAGuB,UAAU;MACxC,MAAMK,KAAK,GAAG7B,CAAC,GAAG2B,CAAC,CAACzB,OAAO,GAAGuB,SAAS;MACvCrC,YAAY,CAACE,KAAK,CAACa,SAAS,GAAI,cAAcyB,KAAO,OAAOC,KAAO,MAAK;MACxEL,UAAU,GAAGG,CAAC,CAAC1B,OAAO;MACtBwB,SAAS,GAAGE,CAAC,CAACzB,OAAO;MACrBH,CAAC,GAAG6B,KAAK;MACT5B,CAAC,GAAG6B,KAAK;MACT,IAAKjE,UAAU,EAAG;QACjBA,UAAU,CAAE+D,CAAE,CAAC;MAChB;IACD;;IAEA;IACA;IACA;IACA,MAAMG,iBAAiB,GAAG,IAAAC,iBAAQ,EAAEL,IAAI,EAAE,EAAG,CAAC;IAE9C5C,aAAa,CAACkD,gBAAgB,CAAE,UAAU,EAAEF,iBAAkB,CAAC;;IAE/D;IACAhD,aAAa,CAACe,IAAI,CAACF,SAAS,CAACC,GAAG,CAAEpC,SAAU,CAAC;IAE7C,IAAKG,WAAW,EAAG;MAClBA,WAAW,CAAEe,KAAM,CAAC;IACrB;IAEAF,OAAO,CAACI,OAAO,GAAG,MAAM;MACvB;MACA,IAAKQ,YAAY,IAAIA,YAAY,CAACuB,UAAU,EAAG;QAC9CvB,YAAY,CAACuB,UAAU,CAACY,WAAW,CAAEnC,YAAa,CAAC;MACpD;MAEA,IAAKK,SAAS,IAAIA,SAAS,CAACkB,UAAU,EAAG;QACxClB,SAAS,CAACkB,UAAU,CAACY,WAAW,CAAE9B,SAAU,CAAC;MAC9C;;MAEA;MACAX,aAAa,CAACe,IAAI,CAACF,SAAS,CAACsC,MAAM,CAAEzE,SAAU,CAAC;MAEhDsB,aAAa,CAACoD,mBAAmB,CAAE,UAAU,EAAEJ,iBAAkB,CAAC;IACnE,CAAC;EACF;EAEA,IAAAK,kBAAS,EACR,MAAM,MAAM;IACX3D,OAAO,CAACI,OAAO,CAAC,CAAC;EAClB,CAAC,EACD,EACD,CAAC;EAED,OACC,IAAAwD,MAAA,CAAA/C,aAAA,EAAA+C,MAAA,CAAAC,QAAA,QACG3E,QAAQ,CAAE;IACX4E,gBAAgB,EAAEzD,KAAK;IACvB0D,cAAc,EAAE9D;EACjB,CAAE,CAAC,EACDJ,aAAa,IACd,IAAA+D,MAAA,CAAA/C,aAAA;IACCmD,SAAS,EAAC,0CAA0C;IACpDlD,KAAK,EAAG;MAAEmD,OAAO,EAAE;IAAO,CAAG;IAC7BC,GAAG,EAAGpE;EAAkB,GAEtBD,aACE,CAEL,CAAC;AAEL;AAAC,IAAAsE,QAAA,GAEclF,SAAS;AAAAmF,OAAA,CAAAC,OAAA,GAAAF,QAAA"}