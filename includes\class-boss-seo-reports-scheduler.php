<?php

/**
 * Planificateur de rapports automatiques Boss SEO.
 * 
 * Cette classe gère la génération automatique de rapports
 * selon des planifications définies par l'utilisateur.
 *
 * @link       https://boss-seo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Planificateur de rapports automatiques.
 *
 * Fonctionnalités :
 * - Planification de rapports récurrents
 * - Envoi par email automatique
 * - Gestion des erreurs et notifications
 * - Historique des exécutions
 * - Interface de configuration
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Reports_Scheduler {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * Instance de l'API des rapports.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_SEO_Reports_API    $reports_api    API des rapports.
     */
    private $reports_api;

    /**
     * Hook des tâches cron.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $cron_hook    Hook cron.
     */
    private $cron_hook = 'boss_seo_generate_scheduled_reports';

    /**
     * Initialise la classe.
     *
     * @since    1.2.0
     * @param    string                  $plugin_name    Le nom du plugin.
     * @param    Boss_SEO_Reports_API    $reports_api    API des rapports.
     */
    public function __construct( $plugin_name, $reports_api ) {
        $this->plugin_name = $plugin_name;
        $this->reports_api = $reports_api;
        
        $this->init_hooks();
    }

    /**
     * Initialise les hooks WordPress.
     *
     * @since    1.2.0
     */
    private function init_hooks() {
        // Hook pour la tâche cron
        add_action( $this->cron_hook, array( $this, 'execute_scheduled_reports' ) );
        
        // Hook pour l'activation/désactivation du plugin
        register_activation_hook( __FILE__, array( $this, 'schedule_cron_jobs' ) );
        register_deactivation_hook( __FILE__, array( $this, 'unschedule_cron_jobs' ) );
        
        // Ajouter des intervalles cron personnalisés
        add_filter( 'cron_schedules', array( $this, 'add_custom_cron_intervals' ) );
    }

    /**
     * Ajoute des intervalles cron personnalisés.
     *
     * @since    1.2.0
     * @param    array    $schedules    Intervalles existants.
     * @return   array                  Intervalles avec ajouts.
     */
    public function add_custom_cron_intervals( $schedules ) {
        $schedules['boss_seo_weekly'] = array(
            'interval' => WEEK_IN_SECONDS,
            'display' => __( 'Une fois par semaine', 'boss-seo' )
        );
        
        $schedules['boss_seo_monthly'] = array(
            'interval' => MONTH_IN_SECONDS,
            'display' => __( 'Une fois par mois', 'boss-seo' )
        );
        
        return $schedules;
    }

    /**
     * Programme les tâches cron.
     *
     * @since    1.2.0
     */
    public function schedule_cron_jobs() {
        if ( ! wp_next_scheduled( $this->cron_hook ) ) {
            wp_schedule_event( time(), 'daily', $this->cron_hook );
        }
    }

    /**
     * Déprogramme les tâches cron.
     *
     * @since    1.2.0
     */
    public function unschedule_cron_jobs() {
        $timestamp = wp_next_scheduled( $this->cron_hook );
        if ( $timestamp ) {
            wp_unschedule_event( $timestamp, $this->cron_hook );
        }
    }

    /**
     * Exécute les rapports planifiés.
     *
     * @since    1.2.0
     */
    public function execute_scheduled_reports() {
        $scheduled_reports = $this->get_scheduled_reports();
        
        foreach ( $scheduled_reports as $schedule ) {
            if ( $this->should_execute_schedule( $schedule ) ) {
                $this->execute_single_schedule( $schedule );
            }
        }
    }

    /**
     * Récupère les rapports planifiés.
     *
     * @since    1.2.0
     * @return   array    Rapports planifiés.
     */
    private function get_scheduled_reports() {
        $schedules = get_option( 'boss_seo_scheduled_reports', array() );
        
        // Filtrer les planifications actives
        return array_filter( $schedules, function( $schedule ) {
            return isset( $schedule['enabled'] ) && $schedule['enabled'] === true;
        } );
    }

    /**
     * Vérifie si une planification doit être exécutée.
     *
     * @since    1.2.0
     * @param    array    $schedule    Configuration de la planification.
     * @return   bool                  Doit être exécutée.
     */
    private function should_execute_schedule( $schedule ) {
        $frequency = $schedule['frequency'] ?? 'weekly';
        $last_execution = $schedule['last_execution'] ?? 0;
        $current_time = time();
        
        switch ( $frequency ) {
            case 'daily':
                return ( $current_time - $last_execution ) >= DAY_IN_SECONDS;
                
            case 'weekly':
                return ( $current_time - $last_execution ) >= WEEK_IN_SECONDS;
                
            case 'monthly':
                return ( $current_time - $last_execution ) >= MONTH_IN_SECONDS;
                
            default:
                return false;
        }
    }

    /**
     * Exécute une planification unique.
     *
     * @since    1.2.0
     * @param    array    $schedule    Configuration de la planification.
     */
    private function execute_single_schedule( $schedule ) {
        try {
            // Générer le rapport
            $report_config = array(
                'type' => $schedule['report_type'] ?? 'performance',
                'format' => $schedule['format'] ?? 'pdf',
                'options' => $schedule['options'] ?? array()
            );
            
            $report = $this->reports_api->generate_report( new WP_REST_Request( 'POST' ) );
            
            if ( is_wp_error( $report ) ) {
                $this->log_execution_error( $schedule, $report->get_error_message() );
                return;
            }
            
            // Envoyer par email si configuré
            if ( isset( $schedule['email_enabled'] ) && $schedule['email_enabled'] ) {
                $this->send_report_email( $schedule, $report );
            }
            
            // Mettre à jour la dernière exécution
            $this->update_last_execution( $schedule['id'], time() );
            
            // Logger le succès
            $this->log_execution_success( $schedule, $report );
            
        } catch ( Exception $e ) {
            $this->log_execution_error( $schedule, $e->getMessage() );
        }
    }

    /**
     * Envoie un rapport par email.
     *
     * @since    1.2.0
     * @param    array    $schedule    Configuration de la planification.
     * @param    array    $report      Données du rapport généré.
     */
    private function send_report_email( $schedule, $report ) {
        $email_config = $schedule['email_config'] ?? array();
        $recipients = $email_config['recipients'] ?? array( get_option( 'admin_email' ) );
        $subject = $email_config['subject'] ?? 'Rapport SEO automatique - ' . get_bloginfo( 'name' );
        
        // Préparer le message
        $message = $this->prepare_email_message( $schedule, $report );
        
        // Préparer les en-têtes
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo( 'name' ) . ' <' . get_option( 'admin_email' ) . '>'
        );
        
        // Pièce jointe si c'est un PDF
        $attachments = array();
        if ( isset( $report['file_path'] ) && file_exists( $report['file_path'] ) ) {
            $attachments[] = $report['file_path'];
        }
        
        // Envoyer l'email
        foreach ( $recipients as $recipient ) {
            $sent = wp_mail( $recipient, $subject, $message, $headers, $attachments );
            
            if ( ! $sent ) {
                error_log( "Boss SEO: Échec d'envoi d'email à {$recipient}" );
            }
        }
    }

    /**
     * Prépare le message email.
     *
     * @since    1.2.0
     * @param    array    $schedule    Configuration de la planification.
     * @param    array    $report      Données du rapport.
     * @return   string                Message HTML.
     */
    private function prepare_email_message( $schedule, $report ) {
        $site_name = get_bloginfo( 'name' );
        $site_url = home_url();
        $report_type = $schedule['report_type'] ?? 'performance';
        $generated_date = date( 'd/m/Y à H:i' );
        
        $message = '
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background: #0073aa; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 10px 20px; background: #0073aa; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Rapport SEO Automatique</h1>
                <p>' . esc_html( $site_name ) . '</p>
            </div>
            
            <div class="content">
                <h2>Votre rapport ' . ucfirst( $report_type ) . ' est prêt</h2>
                
                <p>Bonjour,</p>
                
                <p>Votre rapport SEO automatique pour <strong>' . esc_html( $site_name ) . '</strong> a été généré avec succès le ' . $generated_date . '.</p>
                
                <p><strong>Type de rapport :</strong> ' . ucfirst( $report_type ) . '</p>
                <p><strong>Période d\'analyse :</strong> 30 derniers jours</p>
                
                <p>Vous trouverez le rapport en pièce jointe de cet email.</p>
                
                <p style="text-align: center; margin: 30px 0;">
                    <a href="' . admin_url( 'admin.php?page=boss-seo-reports' ) . '" class="button">
                        Voir tous les rapports
                    </a>
                </p>
                
                <p>Ce rapport a été généré automatiquement par le plugin Boss SEO.</p>
            </div>
            
            <div class="footer">
                <p>Boss SEO - Plugin WordPress professionnel | <a href="' . esc_url( $site_url ) . '">' . esc_html( $site_name ) . '</a></p>
            </div>
        </body>
        </html>';
        
        return $message;
    }

    /**
     * Met à jour la dernière exécution d'une planification.
     *
     * @since    1.2.0
     * @param    string    $schedule_id       ID de la planification.
     * @param    int       $execution_time    Timestamp d'exécution.
     */
    private function update_last_execution( $schedule_id, $execution_time ) {
        $schedules = get_option( 'boss_seo_scheduled_reports', array() );
        
        if ( isset( $schedules[ $schedule_id ] ) ) {
            $schedules[ $schedule_id ]['last_execution'] = $execution_time;
            update_option( 'boss_seo_scheduled_reports', $schedules );
        }
    }

    /**
     * Enregistre une exécution réussie.
     *
     * @since    1.2.0
     * @param    array    $schedule    Configuration de la planification.
     * @param    array    $report      Rapport généré.
     */
    private function log_execution_success( $schedule, $report ) {
        $log_entry = array(
            'timestamp' => time(),
            'schedule_id' => $schedule['id'],
            'report_type' => $schedule['report_type'],
            'status' => 'success',
            'report_id' => $report['id'] ?? null,
            'file_size' => isset( $report['file_path'] ) ? filesize( $report['file_path'] ) : 0
        );
        
        $this->add_execution_log( $log_entry );
    }

    /**
     * Enregistre une erreur d'exécution.
     *
     * @since    1.2.0
     * @param    array     $schedule    Configuration de la planification.
     * @param    string    $error       Message d'erreur.
     */
    private function log_execution_error( $schedule, $error ) {
        $log_entry = array(
            'timestamp' => time(),
            'schedule_id' => $schedule['id'],
            'report_type' => $schedule['report_type'],
            'status' => 'error',
            'error_message' => $error
        );
        
        $this->add_execution_log( $log_entry );
        
        // Envoyer une notification d'erreur à l'admin
        $this->send_error_notification( $schedule, $error );
    }

    /**
     * Ajoute une entrée au log d'exécution.
     *
     * @since    1.2.0
     * @param    array    $log_entry    Entrée de log.
     */
    private function add_execution_log( $log_entry ) {
        $logs = get_option( 'boss_seo_scheduler_logs', array() );
        
        // Ajouter la nouvelle entrée
        array_unshift( $logs, $log_entry );
        
        // Limiter à 100 entrées
        $logs = array_slice( $logs, 0, 100 );
        
        update_option( 'boss_seo_scheduler_logs', $logs );
    }

    /**
     * Envoie une notification d'erreur.
     *
     * @since    1.2.0
     * @param    array     $schedule    Configuration de la planification.
     * @param    string    $error       Message d'erreur.
     */
    private function send_error_notification( $schedule, $error ) {
        $admin_email = get_option( 'admin_email' );
        $site_name = get_bloginfo( 'name' );
        
        $subject = 'Erreur de génération de rapport SEO - ' . $site_name;
        $message = "
        Une erreur s'est produite lors de la génération automatique d'un rapport SEO.
        
        Site : {$site_name}
        Type de rapport : {$schedule['report_type']}
        Erreur : {$error}
        Date : " . date( 'd/m/Y à H:i' ) . "
        
        Veuillez vérifier la configuration dans l'administration WordPress.
        ";
        
        wp_mail( $admin_email, $subject, $message );
    }

    /**
     * Récupère les logs d'exécution.
     *
     * @since    1.2.0
     * @param    int    $limit    Nombre d'entrées à récupérer.
     * @return   array           Logs d'exécution.
     */
    public function get_execution_logs( $limit = 50 ) {
        $logs = get_option( 'boss_seo_scheduler_logs', array() );
        return array_slice( $logs, 0, $limit );
    }

    /**
     * Crée une nouvelle planification.
     *
     * @since    1.2.0
     * @param    array    $config    Configuration de la planification.
     * @return   string|WP_Error    ID de la planification ou erreur.
     */
    public function create_schedule( $config ) {
        $schedule_id = uniqid( 'schedule_' );
        
        $schedule = array_merge( array(
            'id' => $schedule_id,
            'name' => 'Rapport automatique',
            'report_type' => 'performance',
            'frequency' => 'weekly',
            'format' => 'pdf',
            'enabled' => true,
            'created_at' => time(),
            'last_execution' => 0,
            'email_enabled' => false,
            'email_config' => array()
        ), $config );
        
        $schedules = get_option( 'boss_seo_scheduled_reports', array() );
        $schedules[ $schedule_id ] = $schedule;
        
        update_option( 'boss_seo_scheduled_reports', $schedules );
        
        return $schedule_id;
    }

    /**
     * Supprime une planification.
     *
     * @since    1.2.0
     * @param    string    $schedule_id    ID de la planification.
     * @return   bool                      Succès de la suppression.
     */
    public function delete_schedule( $schedule_id ) {
        $schedules = get_option( 'boss_seo_scheduled_reports', array() );
        
        if ( isset( $schedules[ $schedule_id ] ) ) {
            unset( $schedules[ $schedule_id ] );
            update_option( 'boss_seo_scheduled_reports', $schedules );
            return true;
        }
        
        return false;
    }
}
