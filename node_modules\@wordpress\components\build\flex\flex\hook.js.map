{"version": 3, "names": ["_react", "require", "_element", "_deprecated", "_interopRequireDefault", "_context", "_useResponsiveValue", "_space", "styles", "_interopRequireWildcard", "_utils", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useDeprecatedProps", "props", "isReversed", "otherProps", "deprecated", "alternative", "since", "direction", "useFlex", "align", "className", "directionProp", "expanded", "gap", "justify", "wrap", "useContextSystem", "directionAsArray", "Array", "isArray", "useResponsiveValue", "isColumn", "includes", "cx", "useCx", "classes", "useMemo", "base", "css", "alignItems", "flexDirection", "flexWrap", "undefined", "space", "justifyContent", "height", "width", "process", "env", "NODE_ENV", "Flex", "ItemsColumn", "ItemsRow"], "sources": ["@wordpress/components/src/flex/flex/hook.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\n/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\nimport deprecated from '@wordpress/deprecated';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useResponsiveValue } from '../../utils/use-responsive-value';\nimport { space } from '../../utils/space';\nimport * as styles from '../styles';\nimport { useCx } from '../../utils';\nimport type { FlexProps } from '../types';\n\nfunction useDeprecatedProps(\n\tprops: WordPressComponentProps< FlexProps, 'div' >\n): WordPressComponentProps< FlexProps, 'div' > {\n\tconst { isReversed, ...otherProps } = props;\n\n\tif ( typeof isReversed !== 'undefined' ) {\n\t\tdeprecated( 'Flex isReversed', {\n\t\t\talternative: 'Flex direction=\"row-reverse\" or \"column-reverse\"',\n\t\t\tsince: '5.9',\n\t\t} );\n\t\treturn {\n\t\t\t...otherProps,\n\t\t\tdirection: isReversed ? 'row-reverse' : 'row',\n\t\t};\n\t}\n\n\treturn otherProps;\n}\n\nexport function useFlex( props: WordPressComponentProps< FlexProps, 'div' > ) {\n\tconst {\n\t\talign,\n\t\tclassName,\n\t\tdirection: directionProp = 'row',\n\t\texpanded = true,\n\t\tgap = 2,\n\t\tjustify = 'space-between',\n\t\twrap = false,\n\t\t...otherProps\n\t} = useContextSystem( useDeprecatedProps( props ), 'Flex' );\n\n\tconst directionAsArray = Array.isArray( directionProp )\n\t\t? directionProp\n\t\t: [ directionProp ];\n\tconst direction = useResponsiveValue( directionAsArray );\n\n\tconst isColumn =\n\t\ttypeof direction === 'string' && !! direction.includes( 'column' );\n\n\tconst cx = useCx();\n\n\tconst classes = useMemo( () => {\n\t\tconst base = css( {\n\t\t\talignItems: align ?? ( isColumn ? 'normal' : 'center' ),\n\t\t\tflexDirection: direction,\n\t\t\tflexWrap: wrap ? 'wrap' : undefined,\n\t\t\tgap: space( gap ),\n\t\t\tjustifyContent: justify,\n\t\t\theight: isColumn && expanded ? '100%' : undefined,\n\t\t\twidth: ! isColumn && expanded ? '100%' : undefined,\n\t\t} );\n\n\t\treturn cx(\n\t\t\tstyles.Flex,\n\t\t\tbase,\n\t\t\tisColumn ? styles.ItemsColumn : styles.ItemsRow,\n\t\t\tclassName\n\t\t);\n\t}, [\n\t\talign,\n\t\tclassName,\n\t\tcx,\n\t\tdirection,\n\t\texpanded,\n\t\tgap,\n\t\tisColumn,\n\t\tjustify,\n\t\twrap,\n\t] );\n\n\treturn { ...otherProps, className: classes, isColumn };\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;AAMA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,mBAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAC,uBAAA,CAAAR,OAAA;AACA,IAAAS,MAAA,GAAAT,OAAA;AAAoC,SAAAU,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAnBpC;AACA;AACA;;AAGA;AACA;AACA;;AAeA,SAASW,kBAAkBA,CAC1BC,KAAkD,EACJ;EAC9C,MAAM;IAAEC,UAAU;IAAE,GAAGC;EAAW,CAAC,GAAGF,KAAK;EAE3C,IAAK,OAAOC,UAAU,KAAK,WAAW,EAAG;IACxC,IAAAE,mBAAU,EAAE,iBAAiB,EAAE;MAC9BC,WAAW,EAAE,kDAAkD;MAC/DC,KAAK,EAAE;IACR,CAAE,CAAC;IACH,OAAO;MACN,GAAGH,UAAU;MACbI,SAAS,EAAEL,UAAU,GAAG,aAAa,GAAG;IACzC,CAAC;EACF;EAEA,OAAOC,UAAU;AAClB;AAEO,SAASK,OAAOA,CAAEP,KAAkD,EAAG;EAC7E,MAAM;IACLQ,KAAK;IACLC,SAAS;IACTH,SAAS,EAAEI,aAAa,GAAG,KAAK;IAChCC,QAAQ,GAAG,IAAI;IACfC,GAAG,GAAG,CAAC;IACPC,OAAO,GAAG,eAAe;IACzBC,IAAI,GAAG,KAAK;IACZ,GAAGZ;EACJ,CAAC,GAAG,IAAAa,yBAAgB,EAAEhB,kBAAkB,CAAEC,KAAM,CAAC,EAAE,MAAO,CAAC;EAE3D,MAAMgB,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAAER,aAAc,CAAC,GACpDA,aAAa,GACb,CAAEA,aAAa,CAAE;EACpB,MAAMJ,SAAS,GAAG,IAAAa,sCAAkB,EAAEH,gBAAiB,CAAC;EAExD,MAAMI,QAAQ,GACb,OAAOd,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAEA,SAAS,CAACe,QAAQ,CAAE,QAAS,CAAC;EAEnE,MAAMC,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,MAAMC,IAAI,gBAAG,IAAAC,UAAG,EAAE;MACjBC,UAAU,EAAEpB,KAAK,aAALA,KAAK,cAALA,KAAK,GAAMY,QAAQ,GAAG,QAAQ,GAAG,QAAU;MACvDS,aAAa,EAAEvB,SAAS;MACxBwB,QAAQ,EAAEhB,IAAI,GAAG,MAAM,GAAGiB,SAAS;MACnCnB,GAAG,EAAE,IAAAoB,YAAK,EAAEpB,GAAI,CAAC;MACjBqB,cAAc,EAAEpB,OAAO;MACvBqB,MAAM,EAAEd,QAAQ,IAAIT,QAAQ,GAAG,MAAM,GAAGoB,SAAS;MACjDI,KAAK,EAAE,CAAEf,QAAQ,IAAIT,QAAQ,GAAG,MAAM,GAAGoB;IAC1C,CAAC,EAAAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,yCAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,sgHAAC,CAAC;IAEH,OAAOhB,EAAE,CACRhD,MAAM,CAACiE,IAAI,EACXb,IAAI,EACJN,QAAQ,GAAG9C,MAAM,CAACkE,WAAW,GAAGlE,MAAM,CAACmE,QAAQ,EAC/ChC,SACD,CAAC;EACF,CAAC,EAAE,CACFD,KAAK,EACLC,SAAS,EACTa,EAAE,EACFhB,SAAS,EACTK,QAAQ,EACRC,GAAG,EACHQ,QAAQ,EACRP,OAAO,EACPC,IAAI,CACH,CAAC;EAEH,OAAO;IAAE,GAAGZ,UAAU;IAAEO,SAAS,EAAEe,OAAO;IAAEJ;EAAS,CAAC;AACvD"}