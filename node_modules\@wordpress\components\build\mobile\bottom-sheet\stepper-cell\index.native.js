"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _cell = _interopRequireDefault(require("../cell"));
var _stepper = _interopRequireDefault(require("./stepper"));
var _style = _interopRequireDefault(require("./style.scss"));
var _rangeTextInput = _interopRequireDefault(require("../range-text-input"));
var _utils = require("../../utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const STEP_DELAY = 200;
const DEFAULT_STEP = 1;
const isIOS = _reactNative.Platform.OS === 'ios';
class BottomSheetStepperCell extends _element.Component {
  constructor(props) {
    super(props);
    this.announceValue = this.announceValue.bind(this);
    this.onDecrementValue = this.onDecrementValue.bind(this);
    this.onDecrementValuePressIn = this.onDecrementValuePressIn.bind(this);
    this.onIncrementValue = this.onIncrementValue.bind(this);
    this.onIncrementValuePressIn = this.onIncrementValuePressIn.bind(this);
    this.onPressOut = this.onPressOut.bind(this);
    const {
      value,
      defaultValue,
      min
    } = props;
    const initialValue = value || defaultValue || min;
    this.state = {
      inputValue: initialValue,
      stepperValue: initialValue
    };
  }
  componentWillUnmount() {
    clearTimeout(this.timeout);
    clearInterval(this.interval);
    clearTimeout(this.timeoutAnnounceValue);
  }
  onIncrementValue() {
    const {
      step,
      max,
      onChange,
      value,
      decimalNum
    } = this.props;
    let newValue = (0, _utils.toFixed)(value + step, decimalNum);
    newValue = parseInt(newValue) === newValue ? parseInt(newValue) : newValue;
    if (newValue <= max || max === undefined) {
      onChange(newValue);
      this.setState({
        inputValue: newValue
      });
      this.announceValue(newValue);
    }
  }
  onDecrementValue() {
    const {
      step,
      min,
      onChange,
      value,
      decimalNum
    } = this.props;
    let newValue = (0, _utils.toFixed)(value - step, decimalNum);
    newValue = parseInt(newValue) === newValue ? parseInt(newValue) : newValue;
    if (newValue >= min) {
      onChange(newValue);
      this.setState({
        inputValue: newValue
      });
      this.announceValue(newValue);
    }
  }
  onIncrementValuePressIn() {
    this.onIncrementValue();
    this.timeout = setTimeout(() => {
      this.startPressInterval(this.onIncrementValue);
    }, 500);
  }
  onDecrementValuePressIn() {
    this.onDecrementValue();
    this.timeout = setTimeout(() => {
      this.startPressInterval(this.onDecrementValue);
    }, 500);
  }
  onPressOut() {
    clearTimeout(this.timeout);
    clearInterval(this.interval);
  }
  startPressInterval(callback, speed = STEP_DELAY) {
    let counter = 0;
    this.interval = setInterval(() => {
      callback();
      counter += 1;
      if (counter === 10) {
        clearInterval(this.interval);
        this.startPressInterval(callback, speed / 2);
      }
    }, speed);
  }
  announceValue(value) {
    const {
      label,
      unitLabel = ''
    } = this.props;
    if (isIOS) {
      // On Android it triggers the accessibilityLabel with the value change
      clearTimeout(this.timeoutAnnounceValue);
      this.timeoutAnnounceValue = setTimeout(() => {
        _reactNative.AccessibilityInfo.announceForAccessibility(`${value} ${unitLabel} ${label}`);
      }, 300);
    }
  }
  render() {
    const {
      label,
      settingLabel = 'Value',
      unitLabel = '',
      icon,
      min,
      max,
      value,
      separatorType,
      children,
      shouldDisplayTextInput = false,
      preview,
      onChange,
      openUnitPicker,
      decimalNum,
      cellContainerStyle,
      disabled
    } = this.props;
    const {
      inputValue
    } = this.state;
    const isMinValue = value === min;
    const isMaxValue = value === max;
    const labelStyle = [_style.default.cellLabel, !icon ? _style.default.cellLabelNoIcon : {}];
    const getAccessibilityHint = () => {
      return openUnitPicker ? (0, _i18n.__)('double-tap to change unit') : '';
    };
    const accessibilityLabel = (0, _i18n.sprintf)( /* translators: accessibility text. Inform about current value. %1$s: Control label %2$s: setting label (example: width), %3$s: Current value. %4$s: value measurement unit (example: pixels) */
    (0, _i18n.__)('%1$s. %2$s is %3$s %4$s.'), label, settingLabel, value, unitLabel);
    const containerStyle = [_style.default.rowContainer, isIOS ? _style.default.containerIOS : _style.default.containerAndroid];
    return (0, _react.createElement)(_reactNative.View, {
      accessible: true,
      accessibilityRole: "adjustable",
      accessibilityLabel: accessibilityLabel,
      accessibilityHint: getAccessibilityHint(),
      accessibilityActions: [{
        name: 'increment'
      }, {
        name: 'decrement'
      }, {
        name: 'activate'
      }],
      onAccessibilityAction: event => {
        switch (event.nativeEvent.actionName) {
          case 'increment':
            this.onIncrementValue();
            break;
          case 'decrement':
            this.onDecrementValue();
            break;
          case 'activate':
            if (openUnitPicker) {
              openUnitPicker();
            }
            break;
        }
      }
    }, (0, _react.createElement)(_reactNative.View, {
      importantForAccessibility: "no-hide-descendants"
    }, (0, _react.createElement)(_cell.default, {
      accessible: false,
      cellContainerStyle: [_style.default.cellContainerStyle, preview && _style.default.columnContainer, cellContainerStyle],
      cellRowContainerStyle: preview ? containerStyle : _style.default.cellRowStyles,
      editable: false,
      icon: icon,
      label: label,
      labelStyle: labelStyle,
      leftAlign: true,
      separatorType: separatorType,
      disabled: disabled
    }, (0, _react.createElement)(_reactNative.View, {
      style: preview && containerStyle
    }, preview, (0, _react.createElement)(_stepper.default, {
      isMaxValue: isMaxValue,
      isMinValue: isMinValue,
      onPressInDecrement: this.onDecrementValuePressIn,
      onPressInIncrement: this.onIncrementValuePressIn,
      onPressOut: this.onPressOut,
      value: value,
      shouldDisplayTextInput: shouldDisplayTextInput
    }, shouldDisplayTextInput && (0, _react.createElement)(_rangeTextInput.default, {
      label: label,
      onChange: onChange,
      defaultValue: `${inputValue}`,
      value: value,
      min: min,
      step: 1,
      decimalNum: decimalNum
    }, children))))));
  }
}
BottomSheetStepperCell.defaultProps = {
  step: DEFAULT_STEP
};
var _default = (0, _compose.withPreferredColorScheme)(BottomSheetStepperCell);
exports.default = _default;
//# sourceMappingURL=index.native.js.map