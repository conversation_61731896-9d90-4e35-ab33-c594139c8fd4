"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _native = require("@react-navigation/native");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _components = require("@wordpress/components");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BottomSheetSelectControl = ({
  label,
  icon,
  options: items,
  onChange,
  value: selectedValue,
  disabled
}) => {
  const [showSubSheet, setShowSubSheet] = (0, _element.useState)(false);
  const navigation = (0, _native.useNavigation)();
  const onChangeValue = value => {
    return () => {
      goBack();
      onChange(value);
    };
  };
  const selectedOption = items.find(option => option.value === selectedValue);
  const goBack = () => {
    setShowSubSheet(false);
    navigation.goBack();
  };
  const openSubSheet = () => {
    navigation.navigate(_components.BottomSheet.SubSheet.screenName);
    setShowSubSheet(true);
  };
  return (0, _react.createElement)(_components.BottomSheet.SubSheet, {
    navigationButton: (0, _react.createElement)(_components.BottomSheet.Cell, {
      label: label,
      separatorType: "none",
      icon: icon,
      value: selectedOption.label,
      onPress: openSubSheet,
      accessibilityRole: 'button',
      accessibilityLabel: (0, _i18n.sprintf)(
      // translators:  %1$s: Select control button label e.g. "Button width". %2$s: Select control option value e.g: "Auto, 25%".
      (0, _i18n.__)('%1$s. Currently selected: %2$s'), label, selectedOption.label),
      accessibilityHint: (0, _i18n.sprintf)(
      // translators: %s: Select control button label e.g. "Button width"
      (0, _i18n.__)('Navigates to select %s'), label),
      disabled: disabled
    }, disabled ? null : (0, _react.createElement)(_icons.Icon, {
      icon: _icons.chevronRight
    })),
    showSheet: showSubSheet
  }, (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_components.BottomSheet.NavBar, null, (0, _react.createElement)(_components.BottomSheet.NavBar.BackButton, {
    onPress: goBack
  }), (0, _react.createElement)(_components.BottomSheet.NavBar.Heading, null, label)), (0, _react.createElement)(_reactNative.View, {
    style: _style.default.selectControl
  }, items.map((item, index) => (0, _react.createElement)(_components.BottomSheet.Cell, {
    customActionButton: true,
    separatorType: "none",
    label: item.label,
    icon: item.icon,
    onPress: onChangeValue(item.value),
    leftAlign: true,
    key: index,
    accessibilityRole: 'button',
    accessibilityLabel: item.value === selectedValue ? (0, _i18n.sprintf)(
    // translators: %s: Select control option value e.g: "Auto, 25%".
    (0, _i18n.__)('Selected: %s'), item.label) : item.label,
    accessibilityHint: (0, _i18n.__)('Double tap to select')
  }, item.value === selectedValue && (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check
  }))))));
};
var _default = BottomSheetSelectControl;
exports.default = _default;
//# sourceMappingURL=index.native.js.map