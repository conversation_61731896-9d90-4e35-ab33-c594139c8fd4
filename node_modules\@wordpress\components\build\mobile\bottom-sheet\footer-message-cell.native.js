"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _cell = _interopRequireDefault(require("./cell"));
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function FooterMessageCell({
  textAlign = 'left',
  ...props
}) {
  return (0, _react.createElement)(_cell.default, {
    ...props,
    editable: false,
    value: '',
    accessibilityRole: 'text',
    labelStyle: [_styles.default.footerMessageCell, {
      textAlign
    }]
  });
}
var _default = (0, _compose.withPreferredColorScheme)(FooterMessageCell);
exports.default = _default;
//# sourceMappingURL=footer-message-cell.native.js.map