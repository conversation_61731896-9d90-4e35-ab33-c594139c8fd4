"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _i18n = require("@wordpress/i18n");
var _utils = require("../utils");
var _styles = _interopRequireDefault(require("./styles.scss"));
var _borderStyles = _interopRequireDefault(require("./borderStyles.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const isIOS = _reactNative.Platform.OS === 'ios';
class RangeTextInput extends _element.Component {
  constructor(props) {
    super(props);
    this.announceCurrentValue = this.announceCurrentValue.bind(this);
    this.onInputFocus = this.onInputFocus.bind(this);
    this.onInputBlur = this.onInputBlur.bind(this);
    this.handleChangePixelRatio = this.handleChangePixelRatio.bind(this);
    this.onSubmitEditing = this.onSubmitEditing.bind(this);
    this.onChangeText = this.onChangeText.bind(this);
    const {
      value,
      defaultValue,
      min,
      decimalNum
    } = props;
    const initialValue = (0, _utils.toFixed)(value || defaultValue || min, decimalNum);
    const fontScale = this.getFontScale();
    this.state = {
      fontScale,
      inputValue: initialValue,
      controlValue: initialValue,
      hasFocus: false
    };
  }
  componentDidMount() {
    this.appStateChangeSubscription = _reactNative.AppState.addEventListener('change', this.handleChangePixelRatio);
  }
  componentWillUnmount() {
    this.appStateChangeSubscription.remove();
    clearTimeout(this.timeoutAnnounceValue);
  }
  componentDidUpdate(prevProps, prevState) {
    const {
      value
    } = this.props;
    const {
      hasFocus,
      inputValue
    } = this.state;
    if (prevProps.value !== value) {
      this.setState({
        inputValue: value
      });
    }
    if (prevState.hasFocus !== hasFocus) {
      const validValue = this.validateInput(inputValue);
      this.setState({
        inputValue: validValue
      });
    }
    if (!prevState.hasFocus && hasFocus) {
      this._valueTextInput.focus();
    }
  }
  getFontScale() {
    return _reactNative.PixelRatio.getFontScale() < 1 ? 1 : _reactNative.PixelRatio.getFontScale();
  }
  handleChangePixelRatio(nextAppState) {
    if (nextAppState === 'active') {
      this.setState({
        fontScale: this.getFontScale()
      });
    }
  }
  onInputFocus() {
    this.setState({
      hasFocus: true
    });
  }
  onInputBlur() {
    const {
      inputValue
    } = this.state;
    this.onChangeText(`${inputValue}`);
    this.setState({
      hasFocus: false
    });
  }
  validateInput(text) {
    const {
      min,
      max,
      decimalNum
    } = this.props;
    let result = min;
    if (!text) {
      return min;
    }
    if (typeof text === 'number') {
      result = Math.max(text, min);
      return max ? Math.min(result, max) : result;
    }
    result = Math.max((0, _utils.removeNonDigit)(text, decimalNum), min);
    return max ? Math.min(result, max) : result;
  }
  updateValue(value) {
    const {
      onChange
    } = this.props;
    const validValue = this.validateInput(value);
    this.announceCurrentValue(`${validValue}`);
    onChange(validValue);
  }
  onChangeText(textValue) {
    const {
      decimalNum
    } = this.props;
    const inputValue = (0, _utils.removeNonDigit)(textValue, decimalNum);
    textValue = inputValue.replace(',', '.');
    textValue = (0, _utils.toFixed)(textValue, decimalNum);
    const value = this.validateInput(textValue);
    this.setState({
      inputValue,
      controlValue: value
    });
    this.updateValue(value);
  }
  onSubmitEditing({
    nativeEvent: {
      text
    }
  }) {
    const {
      decimalNum
    } = this.props;
    const {
      inputValue
    } = this.state;
    if (!isNaN(Number(text))) {
      text = (0, _utils.toFixed)(text.replace(',', '.'), decimalNum);
      const validValue = this.validateInput(text);
      if (inputValue !== validValue) {
        this.setState({
          inputValue: validValue
        });
        this.announceCurrentValue(`${validValue}`);
        this.props.onChange(validValue);
      }
    }
  }
  announceCurrentValue(value) {
    /* translators: %s: current cell value. */
    const announcement = (0, _i18n.sprintf)((0, _i18n.__)('Current value is %s'), value);
    _reactNative.AccessibilityInfo.announceForAccessibility(announcement);
  }
  render() {
    const {
      getStylesFromColorScheme,
      children,
      label
    } = this.props;
    const {
      fontScale,
      inputValue,
      hasFocus
    } = this.state;
    const textInputStyle = getStylesFromColorScheme(_styles.default.textInput, _styles.default.textInputDark);
    const textInputIOSStyle = getStylesFromColorScheme(_styles.default.textInputIOS, _styles.default.textInputIOSDark);
    const inputBorderStyles = [textInputStyle, _borderStyles.default.borderStyle, hasFocus && _borderStyles.default.isSelected];
    const valueFinalStyle = [_reactNative.Platform.select({
      android: inputBorderStyles,
      ios: textInputIOSStyle
    }), {
      width: 50 * fontScale,
      borderRightWidth: children ? 1 : 0
    }];
    return (0, _react.createElement)(_reactNative.TouchableWithoutFeedback, {
      onPress: this.onInputFocus,
      accessible: false
    }, (0, _react.createElement)(_reactNative.View, {
      style: [_styles.default.textInputContainer, isIOS && inputBorderStyles],
      accessible: false
    }, isIOS || hasFocus ? (0, _react.createElement)(_reactNative.TextInput, {
      accessibilityLabel: label,
      ref: c => this._valueTextInput = c,
      style: valueFinalStyle,
      onChangeText: this.onChangeText,
      onSubmitEditing: this.onSubmitEditing,
      onFocus: this.onInputFocus,
      onBlur: this.onInputBlur,
      keyboardType: "numeric",
      returnKeyType: "done",
      numberOfLines: 1,
      defaultValue: `${inputValue}`,
      value: inputValue.toString(),
      pointerEvents: hasFocus ? 'auto' : 'none'
    }) : (0, _react.createElement)(_reactNative.Text, {
      style: valueFinalStyle,
      numberOfLines: 1,
      ellipsizeMode: "clip"
    }, inputValue), children));
  }
}
var _default = (0, _compose.withPreferredColorScheme)(RangeTextInput);
exports.default = _default;
//# sourceMappingURL=range-text-input.native.js.map