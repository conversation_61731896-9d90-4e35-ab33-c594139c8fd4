"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.findSizeBySlug = exports.default = void 0;
var _i18n = require("@wordpress/i18n");
/**
 * Sizes
 *
 * defines the sizes used in dimension controls
 * all hardcoded `size` values are based on the value of
 * the Sass variable `$block-padding` from
 * `packages/block-editor/src/components/dimension-control/sizes.js`.
 */

/**
 * WordPress dependencies
 */

/**
 * Finds the correct size object from the provided sizes
 * table by size slug (eg: `medium`)
 *
 * @param sizes containing objects for each size definition.
 * @param slug  a string representation of the size (eg: `medium`).
 */
const findSizeBySlug = (sizes, slug) => sizes.find(size => slug === size.slug);
exports.findSizeBySlug = findSizeBySlug;
var _default = [{
  name: (0, _i18n._x)('None', 'Size of a UI element'),
  slug: 'none'
}, {
  name: (0, _i18n._x)('Small', 'Size of a UI element'),
  slug: 'small'
}, {
  name: (0, _i18n._x)('Medium', 'Size of a UI element'),
  slug: 'medium'
}, {
  name: (0, _i18n._x)('Large', 'Size of a UI element'),
  slug: 'large'
}, {
  name: (0, _i18n._x)('Extra Large', 'Size of a UI element'),
  slug: 'xlarge'
}];
exports.default = _default;
//# sourceMappingURL=sizes.js.map