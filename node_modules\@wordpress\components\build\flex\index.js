"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Flex", {
  enumerable: true,
  get: function () {
    return _flex.default;
  }
});
Object.defineProperty(exports, "FlexBlock", {
  enumerable: true,
  get: function () {
    return _flexBlock.default;
  }
});
Object.defineProperty(exports, "FlexItem", {
  enumerable: true,
  get: function () {
    return _flexItem.default;
  }
});
Object.defineProperty(exports, "useFlex", {
  enumerable: true,
  get: function () {
    return _flex.useFlex;
  }
});
Object.defineProperty(exports, "useFlexBlock", {
  enumerable: true,
  get: function () {
    return _flexBlock.useFlexBlock;
  }
});
Object.defineProperty(exports, "useFlexItem", {
  enumerable: true,
  get: function () {
    return _flexItem.useFlexItem;
  }
});
var _flex = _interopRequireWildcard(require("./flex"));
var _flexItem = _interopRequireWildcard(require("./flex-item"));
var _flexBlock = _interopRequireWildcard(require("./flex-block"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
//# sourceMappingURL=index.js.map