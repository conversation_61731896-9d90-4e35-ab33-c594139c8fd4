{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_i18n", "_element", "_icons", "_dom", "_compose", "_animation", "DropZoneComponent", "className", "label", "onFilesDrop", "onHTMLDrop", "onDrop", "restProps", "isDraggingOverDocument", "setIsDraggingOverDocument", "useState", "isDraggingOverElement", "setIsDraggingOverElement", "type", "setType", "ref", "useDropZone", "event", "files", "dataTransfer", "getFilesFromDataTransfer", "html", "getData", "length", "onDragStart", "_type", "types", "includes", "onDragEnd", "undefined", "onDragEnter", "onDragLeave", "disableMotion", "useReducedMotion", "children", "backdrop", "hidden", "opacity", "show", "transition", "duration", "delay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exit", "foreground", "scale", "_react", "createElement", "__unstableMotion", "div", "variants", "initial", "animate", "style", "pointerEvents", "Icon", "icon", "upload", "__", "classes", "classnames", "__unstableAnimatePresence", "_default", "exports", "default"], "sources": ["@wordpress/components/src/drop-zone/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { useState } from '@wordpress/element';\nimport { upload, Icon } from '@wordpress/icons';\nimport { getFilesFromDataTransfer } from '@wordpress/dom';\nimport {\n\t__experimentalUseDropZone as useDropZone,\n\tuseReducedMotion,\n} from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport {\n\t__unstableMotion as motion,\n\t__unstableAnimatePresence as AnimatePresence,\n} from '../animation';\nimport type { DropType, DropZoneProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\n/**\n * `DropZone` is a component creating a drop zone area taking the full size of its parent element. It supports dropping files, HTML content or any other HTML drop event.\n *\n * ```jsx\n * import { DropZone } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyDropZone = () => {\n *   const [ hasDropped, setHasDropped ] = useState( false );\n *\n *   return (\n *     <div>\n *       { hasDropped ? 'Dropped!' : 'Drop something here' }\n *       <DropZone\n *         onFilesDrop={ () => setHasDropped( true ) }\n *         onHTMLDrop={ () => setHasDropped( true ) }\n *         onDrop={ () => setHasDropped( true ) }\n *       />\n *     </div>\n *   );\n * }\n * ```\n */\nexport function DropZoneComponent( {\n\tclassName,\n\tlabel,\n\tonFilesDrop,\n\tonHTMLDrop,\n\tonDrop,\n\t...restProps\n}: WordPressComponentProps< DropZoneProps, 'div', false > ) {\n\tconst [ isDraggingOverDocument, setIsDraggingOverDocument ] =\n\t\tuseState< boolean >();\n\tconst [ isDraggingOverElement, setIsDraggingOverElement ] =\n\t\tuseState< boolean >();\n\tconst [ type, setType ] = useState< DropType >();\n\tconst ref = useDropZone( {\n\t\tonDrop( event ) {\n\t\t\tconst files = event.dataTransfer\n\t\t\t\t? getFilesFromDataTransfer( event.dataTransfer )\n\t\t\t\t: [];\n\t\t\tconst html = event.dataTransfer?.getData( 'text/html' );\n\n\t\t\t/**\n\t\t\t * From Windows Chrome 96, the `event.dataTransfer` returns both file object and HTML.\n\t\t\t * The order of the checks is important to recognise the HTML drop.\n\t\t\t */\n\t\t\tif ( html && onHTMLDrop ) {\n\t\t\t\tonHTMLDrop( html );\n\t\t\t} else if ( files.length && onFilesDrop ) {\n\t\t\t\tonFilesDrop( files );\n\t\t\t} else if ( onDrop ) {\n\t\t\t\tonDrop( event );\n\t\t\t}\n\t\t},\n\t\tonDragStart( event ) {\n\t\t\tsetIsDraggingOverDocument( true );\n\n\t\t\tlet _type: DropType = 'default';\n\n\t\t\t/**\n\t\t\t * From Windows Chrome 96, the `event.dataTransfer` returns both file object and HTML.\n\t\t\t * The order of the checks is important to recognise the HTML drop.\n\t\t\t */\n\t\t\tif ( event.dataTransfer?.types.includes( 'text/html' ) ) {\n\t\t\t\t_type = 'html';\n\t\t\t} else if (\n\t\t\t\t// Check for the types because sometimes the files themselves\n\t\t\t\t// are only available on drop.\n\t\t\t\tevent.dataTransfer?.types.includes( 'Files' ) ||\n\t\t\t\t( event.dataTransfer\n\t\t\t\t\t? getFilesFromDataTransfer( event.dataTransfer )\n\t\t\t\t\t: []\n\t\t\t\t).length > 0\n\t\t\t) {\n\t\t\t\t_type = 'file';\n\t\t\t}\n\n\t\t\tsetType( _type );\n\t\t},\n\t\tonDragEnd() {\n\t\t\tsetIsDraggingOverDocument( false );\n\t\t\tsetType( undefined );\n\t\t},\n\t\tonDragEnter() {\n\t\t\tsetIsDraggingOverElement( true );\n\t\t},\n\t\tonDragLeave() {\n\t\t\tsetIsDraggingOverElement( false );\n\t\t},\n\t} );\n\tconst disableMotion = useReducedMotion();\n\n\tlet children;\n\tconst backdrop = {\n\t\thidden: { opacity: 0 },\n\t\tshow: {\n\t\t\topacity: 1,\n\t\t\ttransition: {\n\t\t\t\ttype: 'tween',\n\t\t\t\tduration: 0.2,\n\t\t\t\tdelay: 0,\n\t\t\t\tdelayChildren: 0.1,\n\t\t\t},\n\t\t},\n\t\texit: {\n\t\t\topacity: 0,\n\t\t\ttransition: {\n\t\t\t\tduration: 0.2,\n\t\t\t\tdelayChildren: 0,\n\t\t\t},\n\t\t},\n\t};\n\n\tconst foreground = {\n\t\thidden: { opacity: 0, scale: 0.9 },\n\t\tshow: {\n\t\t\topacity: 1,\n\t\t\tscale: 1,\n\t\t\ttransition: {\n\t\t\t\tduration: 0.1,\n\t\t\t},\n\t\t},\n\t\texit: { opacity: 0, scale: 0.9 },\n\t};\n\n\tif ( isDraggingOverElement ) {\n\t\tchildren = (\n\t\t\t<motion.div\n\t\t\t\tvariants={ backdrop }\n\t\t\t\tinitial={ disableMotion ? 'show' : 'hidden' }\n\t\t\t\tanimate=\"show\"\n\t\t\t\texit={ disableMotion ? 'show' : 'exit' }\n\t\t\t\tclassName=\"components-drop-zone__content\"\n\t\t\t\t// Without this, when this div is shown,\n\t\t\t\t// Safari calls a onDropZoneLeave causing a loop because of this bug\n\t\t\t\t// https://bugs.webkit.org/show_bug.cgi?id=66547\n\t\t\t\tstyle={ { pointerEvents: 'none' } }\n\t\t\t>\n\t\t\t\t<motion.div variants={ foreground }>\n\t\t\t\t\t<Icon\n\t\t\t\t\t\ticon={ upload }\n\t\t\t\t\t\tclassName=\"components-drop-zone__content-icon\"\n\t\t\t\t\t/>\n\t\t\t\t\t<span className=\"components-drop-zone__content-text\">\n\t\t\t\t\t\t{ label ? label : __( 'Drop files to upload' ) }\n\t\t\t\t\t</span>\n\t\t\t\t</motion.div>\n\t\t\t</motion.div>\n\t\t);\n\t}\n\n\tconst classes = classnames( 'components-drop-zone', className, {\n\t\t'is-active':\n\t\t\t( isDraggingOverDocument || isDraggingOverElement ) &&\n\t\t\t( ( type === 'file' && onFilesDrop ) ||\n\t\t\t\t( type === 'html' && onHTMLDrop ) ||\n\t\t\t\t( type === 'default' && onDrop ) ),\n\t\t'is-dragging-over-document': isDraggingOverDocument,\n\t\t'is-dragging-over-element': isDraggingOverElement,\n\t\t[ `is-dragging-${ type }` ]: !! type,\n\t} );\n\n\treturn (\n\t\t<div { ...restProps } ref={ ref } className={ classes }>\n\t\t\t{ disableMotion ? (\n\t\t\t\tchildren\n\t\t\t) : (\n\t\t\t\t<AnimatePresence>{ children }</AnimatePresence>\n\t\t\t) }\n\t\t</div>\n\t);\n}\n\nexport default DropZoneComponent;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,IAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAQA,IAAAM,UAAA,GAAAN,OAAA;AApBA;AACA;AACA;;AAGA;AACA;AACA;;AAUA;AACA;AACA;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASO,iBAAiBA,CAAE;EAClCC,SAAS;EACTC,KAAK;EACLC,WAAW;EACXC,UAAU;EACVC,MAAM;EACN,GAAGC;AACoD,CAAC,EAAG;EAC3D,MAAM,CAAEC,sBAAsB,EAAEC,yBAAyB,CAAE,GAC1D,IAAAC,iBAAQ,EAAY,CAAC;EACtB,MAAM,CAAEC,qBAAqB,EAAEC,wBAAwB,CAAE,GACxD,IAAAF,iBAAQ,EAAY,CAAC;EACtB,MAAM,CAAEG,IAAI,EAAEC,OAAO,CAAE,GAAG,IAAAJ,iBAAQ,EAAa,CAAC;EAChD,MAAMK,GAAG,GAAG,IAAAC,kCAAW,EAAE;IACxBV,MAAMA,CAAEW,KAAK,EAAG;MACf,MAAMC,KAAK,GAAGD,KAAK,CAACE,YAAY,GAC7B,IAAAC,6BAAwB,EAAEH,KAAK,CAACE,YAAa,CAAC,GAC9C,EAAE;MACL,MAAME,IAAI,GAAGJ,KAAK,CAACE,YAAY,EAAEG,OAAO,CAAE,WAAY,CAAC;;MAEvD;AACH;AACA;AACA;MACG,IAAKD,IAAI,IAAIhB,UAAU,EAAG;QACzBA,UAAU,CAAEgB,IAAK,CAAC;MACnB,CAAC,MAAM,IAAKH,KAAK,CAACK,MAAM,IAAInB,WAAW,EAAG;QACzCA,WAAW,CAAEc,KAAM,CAAC;MACrB,CAAC,MAAM,IAAKZ,MAAM,EAAG;QACpBA,MAAM,CAAEW,KAAM,CAAC;MAChB;IACD,CAAC;IACDO,WAAWA,CAAEP,KAAK,EAAG;MACpBR,yBAAyB,CAAE,IAAK,CAAC;MAEjC,IAAIgB,KAAe,GAAG,SAAS;;MAE/B;AACH;AACA;AACA;MACG,IAAKR,KAAK,CAACE,YAAY,EAAEO,KAAK,CAACC,QAAQ,CAAE,WAAY,CAAC,EAAG;QACxDF,KAAK,GAAG,MAAM;MACf,CAAC,MAAM;MACN;MACA;MACAR,KAAK,CAACE,YAAY,EAAEO,KAAK,CAACC,QAAQ,CAAE,OAAQ,CAAC,IAC7C,CAAEV,KAAK,CAACE,YAAY,GACjB,IAAAC,6BAAwB,EAAEH,KAAK,CAACE,YAAa,CAAC,GAC9C,EAAE,EACHI,MAAM,GAAG,CAAC,EACX;QACDE,KAAK,GAAG,MAAM;MACf;MAEAX,OAAO,CAAEW,KAAM,CAAC;IACjB,CAAC;IACDG,SAASA,CAAA,EAAG;MACXnB,yBAAyB,CAAE,KAAM,CAAC;MAClCK,OAAO,CAAEe,SAAU,CAAC;IACrB,CAAC;IACDC,WAAWA,CAAA,EAAG;MACblB,wBAAwB,CAAE,IAAK,CAAC;IACjC,CAAC;IACDmB,WAAWA,CAAA,EAAG;MACbnB,wBAAwB,CAAE,KAAM,CAAC;IAClC;EACD,CAAE,CAAC;EACH,MAAMoB,aAAa,GAAG,IAAAC,yBAAgB,EAAC,CAAC;EAExC,IAAIC,QAAQ;EACZ,MAAMC,QAAQ,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACLD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACX1B,IAAI,EAAE,OAAO;QACb2B,QAAQ,EAAE,GAAG;QACbC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAE;MAChB;IACD,CAAC;IACDC,IAAI,EAAE;MACLN,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACXC,QAAQ,EAAE,GAAG;QACbE,aAAa,EAAE;MAChB;IACD;EACD,CAAC;EAED,MAAME,UAAU,GAAG;IAClBR,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEQ,KAAK,EAAE;IAAI,CAAC;IAClCP,IAAI,EAAE;MACLD,OAAO,EAAE,CAAC;MACVQ,KAAK,EAAE,CAAC;MACRN,UAAU,EAAE;QACXC,QAAQ,EAAE;MACX;IACD,CAAC;IACDG,IAAI,EAAE;MAAEN,OAAO,EAAE,CAAC;MAAEQ,KAAK,EAAE;IAAI;EAChC,CAAC;EAED,IAAKlC,qBAAqB,EAAG;IAC5BuB,QAAQ,GACP,IAAAY,MAAA,CAAAC,aAAA,EAAC/C,UAAA,CAAAgD,gBAAM,CAACC,GAAG;MACVC,QAAQ,EAAGf,QAAU;MACrBgB,OAAO,EAAGnB,aAAa,GAAG,MAAM,GAAG,QAAU;MAC7CoB,OAAO,EAAC,MAAM;MACdT,IAAI,EAAGX,aAAa,GAAG,MAAM,GAAG,MAAQ;MACxC9B,SAAS,EAAC;MACV;MACA;MACA;MAAA;MACAmD,KAAK,EAAG;QAAEC,aAAa,EAAE;MAAO;IAAG,GAEnC,IAAAR,MAAA,CAAAC,aAAA,EAAC/C,UAAA,CAAAgD,gBAAM,CAACC,GAAG;MAACC,QAAQ,EAAGN;IAAY,GAClC,IAAAE,MAAA,CAAAC,aAAA,EAAClD,MAAA,CAAA0D,IAAI;MACJC,IAAI,EAAGC,aAAQ;MACfvD,SAAS,EAAC;IAAoC,CAC9C,CAAC,EACF,IAAA4C,MAAA,CAAAC,aAAA;MAAM7C,SAAS,EAAC;IAAoC,GACjDC,KAAK,GAAGA,KAAK,GAAG,IAAAuD,QAAE,EAAE,sBAAuB,CACxC,CACK,CACD,CACZ;EACF;EAEA,MAAMC,OAAO,GAAG,IAAAC,mBAAU,EAAE,sBAAsB,EAAE1D,SAAS,EAAE;IAC9D,WAAW,EACV,CAAEM,sBAAsB,IAAIG,qBAAqB,MAC7CE,IAAI,KAAK,MAAM,IAAIT,WAAW,IAC/BS,IAAI,KAAK,MAAM,IAAIR,UAAY,IAC/BQ,IAAI,KAAK,SAAS,IAAIP,MAAQ,CAAE;IACpC,2BAA2B,EAAEE,sBAAsB;IACnD,0BAA0B,EAAEG,qBAAqB;IACjD,CAAG,eAAeE,IAAM,EAAC,GAAI,CAAC,CAAEA;EACjC,CAAE,CAAC;EAEH,OACC,IAAAiC,MAAA,CAAAC,aAAA;IAAA,GAAUxC,SAAS;IAAGQ,GAAG,EAAGA,GAAK;IAACb,SAAS,EAAGyD;EAAS,GACpD3B,aAAa,GACdE,QAAQ,GAER,IAAAY,MAAA,CAAAC,aAAA,EAAC/C,UAAA,CAAA6D,yBAAe,QAAG3B,QAA2B,CAE3C,CAAC;AAER;AAAC,IAAA4B,QAAA,GAEc7D,iBAAiB;AAAA8D,OAAA,CAAAC,OAAA,GAAAF,QAAA"}