{"version": 3, "names": ["_context", "require", "_scrollable", "_view", "_hook", "UnconnectedCardBody", "props", "forwardedRef", "isScrollable", "otherProps", "useCardBody", "_react", "createElement", "Scrollable", "ref", "View", "CardBody", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/card/card-body/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { Scrollable } from '../../scrollable';\nimport { View } from '../../view';\nimport { useCardBody } from './hook';\nimport type { BodyProps } from '../types';\n\nfunction UnconnectedCardBody(\n\tprops: WordPressComponentProps< BodyProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst { isScrollable, ...otherProps } = useCardBody( props );\n\n\tif ( isScrollable ) {\n\t\treturn <Scrollable { ...otherProps } ref={ forwardedRef } />;\n\t}\n\n\treturn <View { ...otherProps } ref={ forwardedRef } />;\n}\n\n/**\n * `CardBody` renders an optional content area for a `Card`.\n * Multiple `CardBody` components can be used within `Card` if needed.\n *\n * ```jsx\n * import { Card, CardBody } from `@wordpress/components`;\n *\n * <Card>\n * \t<CardBody>\n * \t\t...\n * \t</CardBody>\n * </Card>\n * ```\n */\nexport const CardBody = contextConnect( UnconnectedCardBody, 'CardBody' );\n\nexport default CardBody;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAQA,SAASI,mBAAmBA,CAC3BC,KAAkD,EAClDC,YAAiC,EAChC;EACD,MAAM;IAAEC,YAAY;IAAE,GAAGC;EAAW,CAAC,GAAG,IAAAC,iBAAW,EAAEJ,KAAM,CAAC;EAE5D,IAAKE,YAAY,EAAG;IACnB,OAAO,IAAAG,MAAA,CAAAC,aAAA,EAACV,WAAA,CAAAW,UAAU;MAAA,GAAMJ,UAAU;MAAGK,GAAG,EAAGP;IAAc,CAAE,CAAC;EAC7D;EAEA,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACT,KAAA,CAAAY,IAAI;IAAA,GAAMN,UAAU;IAAGK,GAAG,EAAGP;EAAc,CAAE,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMS,QAAQ,GAAG,IAAAC,uBAAc,EAAEZ,mBAAmB,EAAE,UAAW,CAAC;AAACa,OAAA,CAAAF,QAAA,GAAAA,QAAA;AAAA,IAAAG,QAAA,GAE3DH,QAAQ;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}