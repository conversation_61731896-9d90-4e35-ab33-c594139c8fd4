{"version": 3, "names": ["_react", "require", "_flex", "_unitControl", "_interopRequireDefault", "_utils", "_utils2", "_EMOTION_STRINGIFIED_CSS_ERROR__", "MediaWrapper", "_base", "default", "process", "env", "NODE_ENV", "target", "label", "name", "styles", "map", "toString", "exports", "MediaContainer", "MediaPlaceholder", "COLORS", "gray", "INITIAL_BOUNDS", "height", "width", "StyledUnitControl", "UnitControl", "_ref2", "deprecatedBottomMargin", "__nextHasNoMarginBottom", "undefined", "_ref", "extraHelpTextMargin", "hasHelpText", "ControlWrapper", "Flex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showOverlay", "GridLine", "GridLineX", "GridLineY"], "sources": ["@wordpress/components/src/focal-point-picker/styles/focal-point-picker-style.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport { Flex } from '../../flex';\nimport UnitControl from '../../unit-control';\nimport { COLORS } from '../../utils';\nimport type { FocalPointPickerControlsProps } from '../types';\nimport { INITIAL_BOUNDS } from '../utils';\n\nexport const MediaWrapper = styled.div`\n\tbackground-color: transparent;\n\ttext-align: center;\n\twidth: 100%;\n`;\n\nexport const MediaContainer = styled.div`\n\talign-items: center;\n\tbox-shadow: 0 0 0 1px rgba( 0, 0, 0, 0.2 );\n\tcursor: pointer;\n\tdisplay: inline-flex;\n\tjustify-content: center;\n\tmargin: auto;\n\tposition: relative;\n\theight: 100%;\n\n\timg,\n\tvideo {\n\t\tbox-sizing: border-box;\n\t\tdisplay: block;\n\t\theight: auto;\n\t\tmargin: 0;\n\t\tmax-height: 100%;\n\t\tmax-width: 100%;\n\t\tpointer-events: none;\n\t\tuser-select: none;\n\t\twidth: auto;\n\t}\n`;\n\nexport const MediaPlaceholder = styled.div`\n\tbackground: ${ COLORS.gray[ 100 ] };\n\tbox-sizing: border-box;\n\theight: ${ INITIAL_BOUNDS.height }px;\n\tmax-width: 280px;\n\tmin-width: ${ INITIAL_BOUNDS.width }px;\n\twidth: 100%;\n`;\n\nexport const StyledUnitControl = styled( UnitControl )`\n\twidth: 100%;\n`;\n\nconst deprecatedBottomMargin = ( {\n\t__nextHasNoMarginBottom,\n}: FocalPointPickerControlsProps ) => {\n\treturn ! __nextHasNoMarginBottom\n\t\t? css`\n\t\t\t\tpadding-bottom: 1em;\n\t\t  `\n\t\t: undefined;\n};\n\nconst extraHelpTextMargin = ( {\n\thasHelpText = false,\n}: FocalPointPickerControlsProps ) => {\n\treturn hasHelpText\n\t\t? css`\n\t\t\t\tpadding-bottom: 1em;\n\t\t  `\n\t\t: undefined;\n};\n\nexport const ControlWrapper = styled( Flex )`\n\tmax-width: 320px;\n\tpadding-top: 1em;\n\n\t${ extraHelpTextMargin }\n\t${ deprecatedBottomMargin }\n`;\n\nexport const GridView = styled.div`\n\tleft: 50%;\n\toverflow: hidden;\n\tpointer-events: none;\n\tposition: absolute;\n\ttop: 50%;\n\ttransform: translate3d( -50%, -50%, 0 );\n\ttransition: opacity 120ms linear;\n\tz-index: 1;\n\n\topacity: ${ ( { showOverlay }: { showOverlay?: boolean } ) =>\n\t\tshowOverlay ? 1 : 0 };\n`;\n\nexport const GridLine = styled.div`\n\tbackground: white;\n\tbox-shadow: 0 0 2px rgba( 0, 0, 0, 0.6 );\n\tposition: absolute;\n\topacity: 0.4;\n\ttransform: translateZ( 0 );\n`;\n\nexport const GridLineX = styled( GridLine )`\n\theight: 1px;\n\tleft: 0;\n\tright: 0;\n`;\n\nexport const GridLineY = styled( GridLine )`\n\twidth: 1px;\n\ttop: 0;\n\tbottom: 0;\n`;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAEA,IAAAK,OAAA,GAAAL,OAAA;AAA0C,SAAAM,iCAAA;AAEnC,MAAMC,YAAY,OAAAC,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAIxB;AAACa,OAAA,CAAAZ,YAAA,GAAAA,YAAA;AAEK,MAAMa,cAAc,OAAAZ,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAsB1B;AAACa,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAEK,MAAMC,gBAAgB,OAAAb,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,kBACbQ,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,oCAEtBC,sBAAc,CAACC,MAAM,mCAElBD,sBAAc,CAACE,KAAK,sBAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA,gwHAElC;AAACO,OAAA,CAAAE,gBAAA,GAAAA,gBAAA;AAEK,MAAMM,iBAAiB,GAAG,kBAAAnB,KAAA,CAAAC,OAAA,EAAQmB,oBAAW,EAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAErD;AAACa,OAAA,CAAAQ,iBAAA,GAAAA,iBAAA;AAAA,IAAAE,KAAA,GAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA;AAEF,MAAMwB,sBAAsB,GAAGA,CAAE;EAChCC;AAC8B,CAAC,KAAM;EACrC,OAAO,CAAEA,uBAAuB,GAAAF,KAAA,GAI7BG,SAAS;AACb,CAAC;AAAC,IAAAC,IAAA,GAAAvB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA;AAEF,MAAM4B,mBAAmB,GAAGA,CAAE;EAC7BC,WAAW,GAAG;AACgB,CAAC,KAAM;EACrC,OAAOA,WAAW,GAAAF,IAAA,GAIfD,SAAS;AACb,CAAC;AAEM,MAAMI,cAAc,GAAG,kBAAA5B,KAAA,CAAAC,OAAA,EAAQ4B,UAAI,EAAA3B,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,qCAIxCoB,mBAAmB,OACnBJ,sBAAsB,SAAApB,OAAA,CAAAC,GAAA,CAAAC,QAAA,gwHACzB;AAACO,OAAA,CAAAiB,cAAA,GAAAA,cAAA;AAEK,MAAME,QAAQ,OAAA9B,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,uKAUR,CAAE;EAAEyB;AAAuC,CAAC,KACvDA,WAAW,GAAG,CAAC,GAAG,CAAC,SAAA7B,OAAA,CAAAC,GAAA,CAAAC,QAAA,gwHACpB;AAACO,OAAA,CAAAmB,QAAA,GAAAA,QAAA;AAEK,MAAME,QAAQ,OAAAhC,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAMpB;AAACa,OAAA,CAAAqB,QAAA,GAAAA,QAAA;AAEK,MAAMC,SAAS,GAAG,kBAAAjC,KAAA,CAAAC,OAAA,EAAQ+B,QAAQ,EAAA9B,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAI1C;AAACa,OAAA,CAAAsB,SAAA,GAAAA,SAAA;AAEK,MAAMC,SAAS,GAAG,kBAAAlC,KAAA,CAAAC,OAAA,EAAQ+B,QAAQ,EAAA9B,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAZ;AAAA,EAI1C;AAACa,OAAA,CAAAuB,SAAA,GAAAA,SAAA"}