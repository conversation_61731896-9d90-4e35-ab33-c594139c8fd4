<?php
/**
 * API REST unifiée pour Boss SEO Optimizer
 * Version propre avec service d'optimisation unifié
 *
 * @package Boss_SEO
 * @subpackage Boss_SEO/includes
 * @since 1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe API REST unifiée pour Boss SEO Optimizer
 */
class Boss_Optimizer_API_Clean {

    /**
     * Nom du plugin
     *
     * @var string
     */
    private $plugin_name;

    /**
     * Version du plugin
     *
     * @var string
     */
    private $version;

    /**
     * Service d'optimisation unifié
     *
     * @var Boss_Optimizer_Unified
     */
    private $optimizer;

    /**
     * Constructeur
     *
     * @param string $plugin_name Nom du plugin
     * @param string $version Version du plugin
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        // Charger le service d'optimisation unifié
        if ( ! class_exists( 'Boss_Optimizer_Unified' ) ) {
            require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-unified.php';
        }
        
        $this->optimizer = new Boss_Optimizer_Unified();
    }

    /**
     * Enregistre les routes REST API
     */
    public function register_routes() {
        register_rest_route( 'boss-seo/v2', '/optimizer/content/(?P<id>\d+)', array(
            'methods' => 'POST',
            'callback' => array( $this, 'optimize_content' ),
            'permission_callback' => array( $this, 'manage_content_permissions_check' ),
            'args' => array(
                'id' => array(
                    'validate_callback' => function( $param, $request, $key ) {
                        return is_numeric( $param );
                    }
                ),
                'settings' => array(
                    'required' => false,
                    'default' => array()
                )
            )
        ) );

        register_rest_route( 'boss-seo/v2', '/optimizer/content/bulk', array(
            'methods' => 'POST',
            'callback' => array( $this, 'optimize_content_bulk' ),
            'permission_callback' => array( $this, 'manage_content_permissions_check' ),
            'args' => array(
                'ids' => array(
                    'required' => true,
                    'validate_callback' => function( $param, $request, $key ) {
                        return is_array( $param ) && ! empty( $param );
                    }
                ),
                'settings' => array(
                    'required' => false,
                    'default' => array()
                )
            )
        ) );

        register_rest_route( 'boss-seo/v2', '/optimizer/keywords', array(
            'methods' => 'POST',
            'callback' => array( $this, 'generate_keywords' ),
            'permission_callback' => array( $this, 'manage_content_permissions_check' ),
            'args' => array(
                'topic' => array(
                    'required' => true,
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'count' => array(
                    'required' => false,
                    'default' => 5,
                    'validate_callback' => function( $param, $request, $key ) {
                        return is_numeric( $param ) && $param > 0 && $param <= 10;
                    }
                )
            )
        ) );

        register_rest_route( 'boss-seo/v2', '/optimizer/title', array(
            'methods' => 'POST',
            'callback' => array( $this, 'generate_title' ),
            'permission_callback' => array( $this, 'manage_content_permissions_check' ),
            'args' => array(
                'content' => array(
                    'required' => true,
                    'sanitize_callback' => 'wp_kses_post'
                ),
                'keyword' => array(
                    'required' => false,
                    'default' => '',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ) );

        register_rest_route( 'boss-seo/v2', '/optimizer/description', array(
            'methods' => 'POST',
            'callback' => array( $this, 'generate_description' ),
            'permission_callback' => array( $this, 'manage_content_permissions_check' ),
            'args' => array(
                'content' => array(
                    'required' => true,
                    'sanitize_callback' => 'wp_kses_post'
                ),
                'keyword' => array(
                    'required' => false,
                    'default' => '',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ) );
    }

    /**
     * Optimise un contenu avec le service unifié
     *
     * @param WP_REST_Request $request Requête REST
     * @return WP_REST_Response|WP_Error Réponse
     */
    public function optimize_content( $request ) {
        $post_id = $request->get_param( 'id' );
        $settings = $request->get_param( 'settings' );

        $post = get_post( $post_id );
        if ( ! $post ) {
            return new WP_Error( 'content_not_found', __( 'Contenu non trouvé.', 'boss-seo' ), array( 'status' => 404 ) );
        }

        try {
            // Préparer les options d'optimisation
            $options = array(
                'use_ai' => isset( $settings['useAI'] ) ? (bool) $settings['useAI'] : true,
                'auto_save' => true,
                'generate_title' => isset( $settings['title_seo'] ) ? (bool) $settings['title_seo'] : true,
                'generate_description' => isset( $settings['meta_description'] ) ? (bool) $settings['meta_description'] : true,
                'generate_keywords' => isset( $settings['keywords'] ) ? (bool) $settings['keywords'] : true,
                'focus_keyword' => isset( $settings['focus_keyword'] ) ? sanitize_text_field( $settings['focus_keyword'] ) : '',
            );

            // Optimiser avec le service unifié
            $result = $this->optimizer->optimize_content( $post_id, $options );

            if ( $result['success'] ) {
                // Récupérer les métadonnées mises à jour
                $seo_title = get_post_meta( $post_id, '_boss_seo_title', true );
                $meta_description = get_post_meta( $post_id, '_boss_seo_meta_description', true );
                $focus_keyword = get_post_meta( $post_id, '_boss_seo_focus_keyword', true );
                $secondary_keywords = get_post_meta( $post_id, '_boss_seo_secondary_keywords', true );

                return rest_ensure_response( array(
                    'success' => true,
                    'message' => $result['message'],
                    'seo_title' => $seo_title,
                    'meta_description' => $meta_description,
                    'focus_keyword' => $focus_keyword,
                    'secondary_keywords' => $secondary_keywords,
                    'auto_saved' => $result['auto_saved'],
                    'optimizations' => $result['optimizations']
                ) );
            } else {
                return new WP_Error( 'optimization_failed', $result['message'], array( 'status' => 500 ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO API Optimization Error: ' . $e->getMessage() );
            return new WP_Error( 'optimization_error', __( 'Erreur lors de l\'optimisation: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Optimise plusieurs contenus en masse
     *
     * @param WP_REST_Request $request Requête REST
     * @return WP_REST_Response|WP_Error Réponse
     */
    public function optimize_content_bulk( $request ) {
        $post_ids = $request->get_param( 'ids' );
        $settings = $request->get_param( 'settings' );

        $results = array();
        $success_count = 0;
        $error_count = 0;

        // Préparer les options d'optimisation
        $options = array(
            'use_ai' => isset( $settings['useAI'] ) ? (bool) $settings['useAI'] : true,
            'auto_save' => true,
            'generate_title' => isset( $settings['title_seo'] ) ? (bool) $settings['title_seo'] : true,
            'generate_description' => isset( $settings['meta_description'] ) ? (bool) $settings['meta_description'] : true,
            'generate_keywords' => isset( $settings['keywords'] ) ? (bool) $settings['keywords'] : true,
            'focus_keyword' => '',
        );

        try {
            foreach ( $post_ids as $post_id ) {
                $post = get_post( $post_id );

                if ( ! $post ) {
                    $results[ $post_id ] = array(
                        'success' => false,
                        'message' => __( 'Contenu non trouvé.', 'boss-seo' )
                    );
                    $error_count++;
                    continue;
                }

                // Optimiser avec le service unifié
                $result = $this->optimizer->optimize_content( $post_id, $options );
                $results[ $post_id ] = $result;

                if ( $result['success'] ) {
                    $success_count++;
                } else {
                    $error_count++;
                }
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Bulk Optimization Error: ' . $e->getMessage() );
            return new WP_Error( 'bulk_optimization_error', __( 'Erreur lors de l\'optimisation en masse: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }

        return rest_ensure_response( array(
            'results' => $results,
            'summary' => array(
                'total' => count( $post_ids ),
                'success' => $success_count,
                'error' => $error_count
            )
        ) );
    }

    /**
     * Génère des mots-clés avec l'IA
     *
     * @param WP_REST_Request $request Requête REST
     * @return WP_REST_Response|WP_Error Réponse
     */
    public function generate_keywords( $request ) {
        $topic = $request->get_param( 'topic' );
        $count = $request->get_param( 'count' );

        try {
            // Charger les prompts
            if ( ! class_exists( 'Boss_Optimizer_Prompts' ) ) {
                require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-prompts.php';
            }

            // Charger le service IA
            if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
                require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-ai.php';
            }

            $settings = new Boss_Optimizer_Settings( 'boss-seo' );
            $ai = new Boss_Optimizer_AI( 'boss-seo', $settings );

            if ( ! $ai->is_available() ) {
                return new WP_Error( 'ai_not_available', __( 'Service IA non disponible.', 'boss-seo' ), array( 'status' => 503 ) );
            }

            // Générer le prompt direct
            $prompt = Boss_Optimizer_Prompts::get_keywords_prompt( $topic, $count );

            // Appeler l'IA
            $ai_response = $ai->generate_content( $prompt, array(
                'temperature' => 0.3,
                'max_tokens' => 100
            ) );

            if ( $ai_response['success'] ) {
                $keywords = Boss_Optimizer_Prompts::parse_keywords_response( $ai_response['content'] );

                return rest_ensure_response( array(
                    'success' => true,
                    'keywords' => $keywords,
                    'raw_response' => $ai_response['content']
                ) );
            } else {
                return new WP_Error( 'ai_generation_failed', $ai_response['message'], array( 'status' => 500 ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Keywords Generation Error: ' . $e->getMessage() );
            return new WP_Error( 'keywords_error', __( 'Erreur lors de la génération des mots-clés: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Génère un titre avec l'IA
     *
     * @param WP_REST_Request $request Requête REST
     * @return WP_REST_Response|WP_Error Réponse
     */
    public function generate_title( $request ) {
        $content = $request->get_param( 'content' );
        $keyword = $request->get_param( 'keyword' );

        try {
            // Charger les prompts
            if ( ! class_exists( 'Boss_Optimizer_Prompts' ) ) {
                require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-prompts.php';
            }

            // Charger le service IA
            if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
                require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-ai.php';
            }

            $settings = new Boss_Optimizer_Settings( 'boss-seo' );
            $ai = new Boss_Optimizer_AI( 'boss-seo', $settings );

            if ( ! $ai->is_available() ) {
                return new WP_Error( 'ai_not_available', __( 'Service IA non disponible.', 'boss-seo' ), array( 'status' => 503 ) );
            }

            // Générer le prompt direct
            $prompt = Boss_Optimizer_Prompts::get_title_prompt( $content, $keyword );

            // Appeler l'IA
            $ai_response = $ai->generate_content( $prompt, array(
                'temperature' => 0.3,
                'max_tokens' => 100
            ) );

            if ( $ai_response['success'] ) {
                $title = trim( $ai_response['content'] );
                // Supprimer les guillemets si présents
                $title = preg_replace( '/^["\']|["\']$/', '', $title );
                $title = substr( $title, 0, 60 );

                return rest_ensure_response( array(
                    'success' => true,
                    'title' => $title,
                    'raw_response' => $ai_response['content']
                ) );
            } else {
                return new WP_Error( 'ai_generation_failed', $ai_response['message'], array( 'status' => 500 ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Title Generation Error: ' . $e->getMessage() );
            return new WP_Error( 'title_error', __( 'Erreur lors de la génération du titre: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Génère une description avec l'IA
     *
     * @param WP_REST_Request $request Requête REST
     * @return WP_REST_Response|WP_Error Réponse
     */
    public function generate_description( $request ) {
        $content = $request->get_param( 'content' );
        $keyword = $request->get_param( 'keyword' );

        try {
            // Charger les prompts
            if ( ! class_exists( 'Boss_Optimizer_Prompts' ) ) {
                require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-prompts.php';
            }

            // Charger le service IA
            if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
                require_once plugin_dir_path( __FILE__ ) . 'class-boss-optimizer-ai.php';
            }

            $settings = new Boss_Optimizer_Settings( 'boss-seo' );
            $ai = new Boss_Optimizer_AI( 'boss-seo', $settings );

            if ( ! $ai->is_available() ) {
                return new WP_Error( 'ai_not_available', __( 'Service IA non disponible.', 'boss-seo' ), array( 'status' => 503 ) );
            }

            // Générer le prompt direct
            $prompt = Boss_Optimizer_Prompts::get_meta_description_prompt( $content, $keyword );

            // Appeler l'IA
            $ai_response = $ai->generate_content( $prompt, array(
                'temperature' => 0.3,
                'max_tokens' => 200
            ) );

            if ( $ai_response['success'] ) {
                $description = trim( $ai_response['content'] );
                // Supprimer les guillemets si présents
                $description = preg_replace( '/^["\']|["\']$/', '', $description );
                $description = substr( $description, 0, 160 );

                return rest_ensure_response( array(
                    'success' => true,
                    'description' => $description,
                    'raw_response' => $ai_response['content']
                ) );
            } else {
                return new WP_Error( 'ai_generation_failed', $ai_response['message'], array( 'status' => 500 ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Description Generation Error: ' . $e->getMessage() );
            return new WP_Error( 'description_error', __( 'Erreur lors de la génération de la description: ', 'boss-seo' ) . $e->getMessage(), array( 'status' => 500 ) );
        }
    }

    /**
     * Vérifie les permissions pour gérer le contenu
     *
     * @return bool
     */
    public function manage_content_permissions_check() {
        return current_user_can( 'edit_posts' );
    }
}
