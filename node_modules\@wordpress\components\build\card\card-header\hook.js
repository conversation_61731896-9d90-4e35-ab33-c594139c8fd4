"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useCardHeader = useCardHeader;
var _element = require("@wordpress/element");
var _context = require("../../context");
var styles = _interopRequireWildcard(require("../styles"));
var _useCx = require("../../utils/hooks/use-cx");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * WordPress dependencies
 */

function useCardHeader(props) {
  const {
    className,
    isBorderless = false,
    isShady = false,
    size = 'medium',
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'CardHeader');
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => cx(styles.Header, styles.borderRadius, styles.borderColor, styles.cardPaddings[size], isBorderless && styles.borderless, isShady && styles.shady,
  // This classname is added for legacy compatibility reasons.
  'components-card__header', className), [className, cx, isBorderless, isShady, size]);
  return {
    ...otherProps,
    className: classes
  };
}
//# sourceMappingURL=hook.js.map