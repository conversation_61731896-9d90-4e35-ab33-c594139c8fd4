"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Elevation = void 0;
var _react = require("react");
var _context = require("../context");
var _view = require("../view");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedElevation(props, forwardedRef) {
  const elevationProps = (0, _hook.useElevation)(props);
  return (0, _react.createElement)(_view.View, {
    ...elevationProps,
    ref: forwardedRef
  });
}

/**
 * `Elevation` is a core component that renders shadow, using the component
 * system's shadow system.
 *
 * The shadow effect is generated using the `value` prop.
 *
 * ```jsx
 * import {
 *	__experimentalElevation as Elevation,
 *	__experimentalSurface as Surface,
 *	__experimentalText as Text,
 * } from '@wordpress/components';
 *
 * function Example() {
 *   return (
 *     <Surface>
 *       <Text>Code is Poetry</Text>
 *       <Elevation value={ 5 } />
 *     </Surface>
 *   );
 * }
 * ```
 */
const Elevation = (0, _context.contextConnect)(UnconnectedElevation, 'Elevation');
exports.Elevation = Elevation;
var _default = Elevation;
exports.default = _default;
//# sourceMappingURL=component.js.map