/**
 * Styles pour le module d'analyse technique Boss SEO
 * Interface professionnelle à onglets
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/modules/technical-analysis/assets/css
 * @since      1.2.0
 */

/* ==========================================================================
   Variables CSS
   ========================================================================== */
:root {
    --boss-primary: #0073aa;
    --boss-primary-dark: #005a87;
    --boss-secondary: #00a0d2;
    --boss-success: #46b450;
    --boss-warning: #ffb900;
    --boss-error: #dc3232;
    --boss-gray-100: #f9f9f9;
    --boss-gray-200: #e5e5e5;
    --boss-gray-300: #ddd;
    --boss-gray-400: #ccc;
    --boss-gray-500: #999;
    --boss-gray-600: #666;
    --boss-gray-700: #444;
    --boss-gray-800: #333;
    --boss-gray-900: #222;
    --boss-white: #ffffff;
    --boss-border-radius: 6px;
    --boss-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --boss-transition: all 0.3s ease;
}

/* ==========================================================================
   Container principal
   ========================================================================== */
.boss-technical-analysis-wrap {
    margin: 20px 20px 0 2px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.boss-technical-analysis-wrap h1 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    color: var(--boss-gray-800);
}

.boss-version-badge {
    background: linear-gradient(135deg, var(--boss-primary), var(--boss-secondary));
    color: var(--boss-white);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ==========================================================================
   Messages et notifications
   ========================================================================== */
.boss-messages-container {
    margin-bottom: 20px;
}

.boss-config-notice {
    border-left: 4px solid var(--boss-warning);
    background: #fff8e1;
    padding: 20px;
    border-radius: var(--boss-border-radius);
    margin-bottom: 20px;
}

.boss-config-notice h3 {
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--boss-gray-800);
}

.boss-config-notice .dashicons {
    color: var(--boss-warning);
}

/* ==========================================================================
   Interface à onglets
   ========================================================================== */
.boss-technical-analysis-container {
    background: var(--boss-white);
    border-radius: var(--boss-border-radius);
    box-shadow: var(--boss-box-shadow);
    overflow: hidden;
}

/* Navigation des onglets */
.boss-tabs-nav {
    background: var(--boss-gray-100);
    border-bottom: 1px solid var(--boss-gray-300);
}

.boss-tabs-list {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
}

.boss-tab-item {
    flex: 1;
    text-align: center;
    cursor: pointer;
    padding: 16px 20px;
    border-bottom: 3px solid transparent;
    transition: var(--boss-transition);
    background: var(--boss-gray-100);
    color: var(--boss-gray-600);
    position: relative;
}

.boss-tab-item:hover {
    background: var(--boss-gray-200);
    color: var(--boss-gray-800);
}

.boss-tab-item.active {
    background: var(--boss-white);
    color: var(--boss-primary);
    border-bottom-color: var(--boss-primary);
}

.boss-tab-item .dashicons {
    font-size: 18px;
    margin-right: 8px;
    vertical-align: middle;
}

.boss-tab-item .tab-label {
    font-weight: 600;
    font-size: 14px;
}

/* Contenu des onglets */
.boss-tabs-content {
    min-height: 600px;
}

.boss-tab-content {
    display: none;
    padding: 30px;
    animation: fadeIn 0.3s ease-in-out;
}

.boss-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* En-têtes des onglets */
.boss-tab-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--boss-gray-200);
}

.boss-tab-header h2 {
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--boss-gray-800);
    font-size: 24px;
}

.boss-tab-header .description {
    color: var(--boss-gray-600);
    font-size: 16px;
    margin: 0;
}

/* ==========================================================================
   Contrôles d'analyse
   ========================================================================== */
.boss-analysis-controls {
    display: flex;
    gap: 20px;
    align-items: end;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--boss-gray-100);
    border-radius: var(--boss-border-radius);
}

.boss-control-group {
    flex: 1;
}

.boss-control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--boss-gray-700);
}

.boss-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--boss-gray-300);
    border-radius: var(--boss-border-radius);
    font-size: 14px;
    background: var(--boss-white);
    transition: var(--boss-transition);
}

.boss-select:focus {
    outline: none;
    border-color: var(--boss-primary);
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

/* ==========================================================================
   Boutons
   ========================================================================== */
.boss-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--boss-border-radius);
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--boss-transition);
    white-space: nowrap;
}

.boss-btn-primary {
    background: linear-gradient(135deg, var(--boss-primary), var(--boss-secondary));
    color: var(--boss-white);
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.2);
}

.boss-btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--boss-primary-dark), var(--boss-primary));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 115, 170, 0.3);
}

.boss-btn:disabled {
    background: var(--boss-gray-400);
    color: var(--boss-gray-600);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* ==========================================================================
   Conteneurs de résultats
   ========================================================================== */
.boss-results-container {
    background: var(--boss-white);
    border: 1px solid var(--boss-gray-300);
    border-radius: var(--boss-border-radius);
    padding: 20px;
    margin-top: 20px;
}

/* ==========================================================================
   Loader
   ========================================================================== */
.boss-loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.boss-loader {
    background: var(--boss-white);
    padding: 40px;
    border-radius: var(--boss-border-radius);
    text-align: center;
    box-shadow: var(--boss-box-shadow);
}

.boss-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--boss-gray-300);
    border-top: 4px solid var(--boss-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.boss-loader-text {
    margin: 0;
    color: var(--boss-gray-700);
    font-weight: 600;
}

/* ==========================================================================
   Responsive
   ========================================================================== */
@media (max-width: 768px) {
    .boss-tabs-list {
        flex-direction: column;
    }
    
    .boss-tab-item {
        border-bottom: none;
        border-right: 3px solid transparent;
    }
    
    .boss-tab-item.active {
        border-right-color: var(--boss-primary);
    }
    
    .boss-analysis-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .boss-control-group {
        flex: none;
    }
}
