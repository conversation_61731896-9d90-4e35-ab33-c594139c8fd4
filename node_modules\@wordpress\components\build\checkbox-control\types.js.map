{"version": 3, "names": [], "sources": ["@wordpress/components/src/checkbox-control/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { BaseControlProps } from '../base-control/types';\n\nexport type CheckboxControlProps = Pick<\n\tBaseControlProps,\n\t'help' | '__nextHasNoMarginBottom'\n> & {\n\t/**\n\t * A function that receives the checked state (boolean) as input.\n\t */\n\tonChange: ( value: boolean ) => void;\n\t/**\n\t * A label for the input field, that appears at the side of the checkbox.\n\t * The prop will be rendered as content a label element. If no prop is\n\t * passed an empty label is rendered. If the prop is set to false no label\n\t * is rendered.\n\t */\n\tlabel?: string | false;\n\t/**\n\t * If checked is true the checkbox will be checked. If checked is false the\n\t * checkbox will be unchecked. If no value is passed the checkbox will be\n\t * unchecked.\n\t */\n\tchecked?: boolean;\n\t/**\n\t * If indeterminate is true the state of the checkbox will be indeterminate.\n\t */\n\tindeterminate?: boolean;\n\t/**\n\t * @deprecated\n\t */\n\theading?: ReactNode;\n};\n"], "mappings": ""}