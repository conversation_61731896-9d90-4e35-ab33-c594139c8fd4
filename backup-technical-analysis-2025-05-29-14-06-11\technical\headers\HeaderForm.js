import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  TextControl,
  TextareaControl,
  SelectControl,
  ToggleControl,
  Notice
} from '@wordpress/components';

const HeaderForm = ({ 
  editingHeader, 
  onSave, 
  onCancel,
  commonHeaders
}) => {
  // États
  const [formData, setFormData] = useState({
    name: '',
    value: '',
    type: 'custom',
    active: true,
    description: ''
  });
  const [formErrors, setFormErrors] = useState({});
  const [selectedCommonHeader, setSelectedCommonHeader] = useState('');
  
  // Mettre à jour le formulaire lorsqu'un en-tête est en cours d'édition
  useEffect(() => {
    if (editingHeader) {
      setFormData({
        id: editingHeader.id,
        name: editingHeader.name,
        value: editingHeader.value,
        type: editingHeader.type,
        active: editingHeader.active,
        description: editingHeader.description || ''
      });
    } else {
      // Réinitialiser le formulaire
      setFormData({
        name: '',
        value: '',
        type: 'custom',
        active: true,
        description: ''
      });
    }
    
    // Réinitialiser les erreurs et la sélection
    setFormErrors({});
    setSelectedCommonHeader('');
  }, [editingHeader]);
  
  // Fonction pour mettre à jour les données du formulaire
  const updateFormData = (key, value) => {
    setFormData({
      ...formData,
      [key]: value
    });
    
    // Effacer l'erreur pour ce champ
    if (formErrors[key]) {
      setFormErrors({
        ...formErrors,
        [key]: null
      });
    }
  };
  
  // Fonction pour valider le formulaire
  const validateForm = () => {
    const errors = {};
    
    if (!formData.name) {
      errors.name = __('Le nom de l\'en-tête est requis', 'boss-seo');
    }
    
    if (!formData.value) {
      errors.value = __('La valeur de l\'en-tête est requise', 'boss-seo');
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Fonction pour gérer la soumission du formulaire
  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };
  
  // Fonction pour sélectionner un en-tête commun
  const handleSelectCommonHeader = () => {
    if (selectedCommonHeader) {
      const header = commonHeaders.find(h => h.id === selectedCommonHeader);
      
      if (header) {
        setFormData({
          ...formData,
          name: header.name,
          value: header.value,
          type: header.type,
          description: header.description || ''
        });
        
        setSelectedCommonHeader('');
      }
    }
  };

  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {editingHeader 
            ? __('Modifier l\'en-tête', 'boss-seo') 
            : __('Ajouter un en-tête', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6 boss-mb-6">
          <div>
            {!editingHeader && (
              <div className="boss-mb-4">
                <div className="boss-flex boss-space-x-2">
                  <SelectControl
                    label={__('En-têtes courants', 'boss-seo')}
                    value={selectedCommonHeader}
                    options={[
                      { label: __('-- Sélectionner un en-tête --', 'boss-seo'), value: '' },
                      ...commonHeaders.map(header => ({
                        label: header.name,
                        value: header.id
                      }))
                    ]}
                    onChange={setSelectedCommonHeader}
                    className="boss-flex-1"
                  />
                  <div className="boss-flex boss-items-end boss-mb-2">
                    <Button
                      isSecondary
                      onClick={handleSelectCommonHeader}
                      disabled={!selectedCommonHeader}
                    >
                      {__('Utiliser', 'boss-seo')}
                    </Button>
                  </div>
                </div>
              </div>
            )}
            
            <TextControl
              label={__('Nom de l\'en-tête', 'boss-seo')}
              help={__('Ex: Content-Security-Policy, X-Frame-Options', 'boss-seo')}
              value={formData.name}
              onChange={(value) => updateFormData('name', value)}
              className="boss-mb-4"
            />
            {formErrors.name && (
              <Notice status="error" isDismissible={false} className="boss-mb-4">
                {formErrors.name}
              </Notice>
            )}
            
            <TextareaControl
              label={__('Valeur de l\'en-tête', 'boss-seo')}
              help={__('La valeur à envoyer avec cet en-tête', 'boss-seo')}
              value={formData.value}
              onChange={(value) => updateFormData('value', value)}
              rows={4}
              className="boss-mb-4"
            />
            {formErrors.value && (
              <Notice status="error" isDismissible={false} className="boss-mb-4">
                {formErrors.value}
              </Notice>
            )}
            
            <SelectControl
              label={__('Type d\'en-tête', 'boss-seo')}
              value={formData.type}
              options={[
                { label: __('Sécurité', 'boss-seo'), value: 'security' },
                { label: __('Cache', 'boss-seo'), value: 'cache' },
                { label: __('Performance', 'boss-seo'), value: 'performance' },
                { label: __('CORS', 'boss-seo'), value: 'cors' },
                { label: __('Personnalisé', 'boss-seo'), value: 'custom' }
              ]}
              onChange={(value) => updateFormData('type', value)}
              className="boss-mb-4"
            />
          </div>
          
          <div>
            <div className="boss-mb-4">
              <ToggleControl
                label={__('Actif', 'boss-seo')}
                help={__('Activer ou désactiver cet en-tête', 'boss-seo')}
                checked={formData.active}
                onChange={(value) => updateFormData('active', value)}
              />
            </div>
            
            <TextareaControl
              label={__('Description', 'boss-seo')}
              help={__('Notes internes sur cet en-tête (facultatif)', 'boss-seo')}
              value={formData.description}
              onChange={(value) => updateFormData('description', value)}
              rows={4}
            />
            
            <div className="boss-mt-6 boss-p-4 boss-bg-gray-50 boss-rounded-lg boss-text-sm boss-text-boss-gray">
              <h3 className="boss-font-medium boss-text-boss-dark boss-mb-2">
                {__('Informations sur l\'en-tête', 'boss-seo')}
              </h3>
              
              {formData.name && (
                <div>
                  {formData.name === 'Content-Security-Policy' && (
                    <p>
                      {__('Définit les sources de contenu autorisées pour votre site. Aide à prévenir les attaques XSS et les injections de code.', 'boss-seo')}
                    </p>
                  )}
                  
                  {formData.name === 'X-Frame-Options' && (
                    <p>
                      {__('Contrôle si votre site peut être affiché dans un iframe. Aide à prévenir les attaques de clickjacking.', 'boss-seo')}
                    </p>
                  )}
                  
                  {formData.name === 'X-Content-Type-Options' && (
                    <p>
                      {__('Empêche les navigateurs d\'interpréter les fichiers comme un type MIME différent de celui déclaré.', 'boss-seo')}
                    </p>
                  )}
                  
                  {formData.name === 'Strict-Transport-Security' && (
                    <p>
                      {__('Force les connexions à utiliser HTTPS au lieu de HTTP. Améliore la sécurité en empêchant les attaques de type downgrade.', 'boss-seo')}
                    </p>
                  )}
                  
                  {formData.name === 'Cache-Control' && (
                    <p>
                      {__('Spécifie les directives pour la mise en cache des ressources. Peut améliorer les performances et réduire la charge du serveur.', 'boss-seo')}
                    </p>
                  )}
                  
                  {formData.name === 'Access-Control-Allow-Origin' && (
                    <p>
                      {__('Indique si la réponse peut être partagée avec des ressources d\'une origine donnée. Essentiel pour les requêtes CORS.', 'boss-seo')}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-end boss-space-x-3">
          <Button
            isSecondary
            onClick={onCancel}
          >
            {__('Annuler', 'boss-seo')}
          </Button>
          <Button
            isPrimary
            onClick={handleSubmit}
          >
            {editingHeader ? __('Mettre à jour', 'boss-seo') : __('Ajouter', 'boss-seo')}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default HeaderForm;
