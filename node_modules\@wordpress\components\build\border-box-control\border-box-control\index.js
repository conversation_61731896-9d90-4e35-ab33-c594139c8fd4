"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "BorderBoxControl", {
  enumerable: true,
  get: function () {
    return _component.default;
  }
});
Object.defineProperty(exports, "useBorderBoxControl", {
  enumerable: true,
  get: function () {
    return _hook.useBorderBoxControl;
  }
});
var _component = _interopRequireDefault(require("./component"));
var _hook = require("./hook");
//# sourceMappingURL=index.js.map