{"version": 3, "names": ["_element", "require", "_compose", "_keycodes", "defaultShortcuts", "previous", "modifier", "character", "next", "useNavigateRegions", "shortcuts", "ref", "useRef", "isFocusingRegions", "setIsFocusingRegions", "useState", "focusRegion", "offset", "_ref$current$querySel", "regions", "Array", "from", "current", "querySelectorAll", "length", "nextRegion", "wrappingRegion", "ownerDocument", "activeElement", "closest", "selectedIndex", "indexOf", "nextIndex", "focus", "clickRef", "useRefEffect", "element", "onClick", "addEventListener", "removeEventListener", "useMergeRefs", "className", "onKeyDown", "event", "some", "isKeyboardEvent", "_default", "createHigherOrderComponent", "Component", "props", "_react", "createElement", "exports", "default"], "sources": ["@wordpress/components/src/higher-order/navigate-regions/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useState, useRef } from '@wordpress/element';\nimport {\n\tcreateHigherOrderComponent,\n\tuseRefEffect,\n\tuseMergeRefs,\n} from '@wordpress/compose';\nimport { isKeyboardEvent } from '@wordpress/keycodes';\nimport type { WPKeycodeModifier } from '@wordpress/keycodes';\n\nconst defaultShortcuts = {\n\tprevious: [\n\t\t{\n\t\t\tmodifier: 'ctrlShift',\n\t\t\tcharacter: '`',\n\t\t},\n\t\t{\n\t\t\tmodifier: 'ctrlShift',\n\t\t\tcharacter: '~',\n\t\t},\n\t\t{\n\t\t\tmodifier: 'access',\n\t\t\tcharacter: 'p',\n\t\t},\n\t] as const,\n\tnext: [\n\t\t{\n\t\t\tmodifier: 'ctrl',\n\t\t\tcharacter: '`',\n\t\t},\n\t\t{\n\t\t\tmodifier: 'access',\n\t\t\tcharacter: 'n',\n\t\t},\n\t] as const,\n};\n\ntype Shortcuts = {\n\tprevious: readonly { modifier: WPKeycodeModifier; character: string }[];\n\tnext: readonly { modifier: WPKeycodeModifier; character: string }[];\n};\n\nexport function useNavigateRegions( shortcuts: Shortcuts = defaultShortcuts ) {\n\tconst ref = useRef< HTMLDivElement >( null );\n\tconst [ isFocusingRegions, setIsFocusingRegions ] = useState( false );\n\n\tfunction focusRegion( offset: number ) {\n\t\tconst regions = Array.from(\n\t\t\tref.current?.querySelectorAll< HTMLElement >(\n\t\t\t\t'[role=\"region\"][tabindex=\"-1\"]'\n\t\t\t) ?? []\n\t\t);\n\t\tif ( ! regions.length ) {\n\t\t\treturn;\n\t\t}\n\t\tlet nextRegion = regions[ 0 ];\n\t\t// Based off the current element, use closest to determine the wrapping region since this operates up the DOM. Also, match tabindex to avoid edge cases with regions we do not want.\n\t\tconst wrappingRegion =\n\t\t\tref.current?.ownerDocument?.activeElement?.closest< HTMLElement >(\n\t\t\t\t'[role=\"region\"][tabindex=\"-1\"]'\n\t\t\t);\n\t\tconst selectedIndex = wrappingRegion\n\t\t\t? regions.indexOf( wrappingRegion )\n\t\t\t: -1;\n\t\tif ( selectedIndex !== -1 ) {\n\t\t\tlet nextIndex = selectedIndex + offset;\n\t\t\tnextIndex = nextIndex === -1 ? regions.length - 1 : nextIndex;\n\t\t\tnextIndex = nextIndex === regions.length ? 0 : nextIndex;\n\t\t\tnextRegion = regions[ nextIndex ];\n\t\t}\n\n\t\tnextRegion.focus();\n\t\tsetIsFocusingRegions( true );\n\t}\n\n\tconst clickRef = useRefEffect(\n\t\t( element ) => {\n\t\t\tfunction onClick() {\n\t\t\t\tsetIsFocusingRegions( false );\n\t\t\t}\n\n\t\t\telement.addEventListener( 'click', onClick );\n\n\t\t\treturn () => {\n\t\t\t\telement.removeEventListener( 'click', onClick );\n\t\t\t};\n\t\t},\n\t\t[ setIsFocusingRegions ]\n\t);\n\n\treturn {\n\t\tref: useMergeRefs( [ ref, clickRef ] ),\n\t\tclassName: isFocusingRegions ? 'is-focusing-regions' : '',\n\t\tonKeyDown( event: React.KeyboardEvent< HTMLDivElement > ) {\n\t\t\tif (\n\t\t\t\tshortcuts.previous.some( ( { modifier, character } ) => {\n\t\t\t\t\treturn isKeyboardEvent[ modifier ]( event, character );\n\t\t\t\t} )\n\t\t\t) {\n\t\t\t\tfocusRegion( -1 );\n\t\t\t} else if (\n\t\t\t\tshortcuts.next.some( ( { modifier, character } ) => {\n\t\t\t\t\treturn isKeyboardEvent[ modifier ]( event, character );\n\t\t\t\t} )\n\t\t\t) {\n\t\t\t\tfocusRegion( 1 );\n\t\t\t}\n\t\t},\n\t};\n}\n\n/**\n * `navigateRegions` is a React [higher-order component](https://facebook.github.io/react/docs/higher-order-components.html)\n * adding keyboard navigation to switch between the different DOM elements marked as \"regions\" (role=\"region\").\n * These regions should be focusable (By adding a tabIndex attribute for example). For better accessibility,\n * these elements must be properly labelled to briefly describe the purpose of the content in the region.\n * For more details, see \"Landmark Roles\" in the [WAI-ARIA specification](https://www.w3.org/TR/wai-aria/)\n * and \"Landmark Regions\" in the [ARIA Authoring Practices Guide](https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/).\n *\n * ```jsx\n * import { navigateRegions } from '@wordpress/components';\n *\n * const MyComponentWithNavigateRegions = navigateRegions( () => (\n * \t<div>\n * \t\t<div role=\"region\" tabIndex=\"-1\" aria-label=\"Header\">\n * \t\t\tHeader\n * \t\t</div>\n * \t\t<div role=\"region\" tabIndex=\"-1\" aria-label=\"Content\">\n * \t\t\tContent\n * \t\t</div>\n * \t\t<div role=\"region\" tabIndex=\"-1\" aria-label=\"Sidebar\">\n * \t\t\tSidebar\n * \t\t</div>\n * \t</div>\n * ) );\n * ```\n */\nexport default createHigherOrderComponent(\n\t( Component ) =>\n\t\t( { shortcuts, ...props } ) => (\n\t\t\t<div { ...useNavigateRegions( shortcuts ) }>\n\t\t\t\t<Component { ...props } />\n\t\t\t</div>\n\t\t),\n\t'navigateRegions'\n);\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,SAAA,GAAAF,OAAA;AATA;AACA;AACA;;AAUA,MAAMG,gBAAgB,GAAG;EACxBC,QAAQ,EAAE,CACT;IACCC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACZ,CAAC,EACD;IACCD,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACZ,CAAC,EACD;IACCD,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE;EACZ,CAAC,CACQ;EACVC,IAAI,EAAE,CACL;IACCF,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE;EACZ,CAAC,EACD;IACCD,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE;EACZ,CAAC;AAEH,CAAC;AAOM,SAASE,kBAAkBA,CAAEC,SAAoB,GAAGN,gBAAgB,EAAG;EAC7E,MAAMO,GAAG,GAAG,IAAAC,eAAM,EAAoB,IAAK,CAAC;EAC5C,MAAM,CAAEC,iBAAiB,EAAEC,oBAAoB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAErE,SAASC,WAAWA,CAAEC,MAAc,EAAG;IAAA,IAAAC,qBAAA;IACtC,MAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,EAAAH,qBAAA,GACzBP,GAAG,CAACW,OAAO,EAAEC,gBAAgB,CAC5B,gCACD,CAAC,cAAAL,qBAAA,cAAAA,qBAAA,GAAI,EACN,CAAC;IACD,IAAK,CAAEC,OAAO,CAACK,MAAM,EAAG;MACvB;IACD;IACA,IAAIC,UAAU,GAAGN,OAAO,CAAE,CAAC,CAAE;IAC7B;IACA,MAAMO,cAAc,GACnBf,GAAG,CAACW,OAAO,EAAEK,aAAa,EAAEC,aAAa,EAAEC,OAAO,CACjD,gCACD,CAAC;IACF,MAAMC,aAAa,GAAGJ,cAAc,GACjCP,OAAO,CAACY,OAAO,CAAEL,cAAe,CAAC,GACjC,CAAC,CAAC;IACL,IAAKI,aAAa,KAAK,CAAC,CAAC,EAAG;MAC3B,IAAIE,SAAS,GAAGF,aAAa,GAAGb,MAAM;MACtCe,SAAS,GAAGA,SAAS,KAAK,CAAC,CAAC,GAAGb,OAAO,CAACK,MAAM,GAAG,CAAC,GAAGQ,SAAS;MAC7DA,SAAS,GAAGA,SAAS,KAAKb,OAAO,CAACK,MAAM,GAAG,CAAC,GAAGQ,SAAS;MACxDP,UAAU,GAAGN,OAAO,CAAEa,SAAS,CAAE;IAClC;IAEAP,UAAU,CAACQ,KAAK,CAAC,CAAC;IAClBnB,oBAAoB,CAAE,IAAK,CAAC;EAC7B;EAEA,MAAMoB,QAAQ,GAAG,IAAAC,qBAAY,EAC1BC,OAAO,IAAM;IACd,SAASC,OAAOA,CAAA,EAAG;MAClBvB,oBAAoB,CAAE,KAAM,CAAC;IAC9B;IAEAsB,OAAO,CAACE,gBAAgB,CAAE,OAAO,EAAED,OAAQ,CAAC;IAE5C,OAAO,MAAM;MACZD,OAAO,CAACG,mBAAmB,CAAE,OAAO,EAAEF,OAAQ,CAAC;IAChD,CAAC;EACF,CAAC,EACD,CAAEvB,oBAAoB,CACvB,CAAC;EAED,OAAO;IACNH,GAAG,EAAE,IAAA6B,qBAAY,EAAE,CAAE7B,GAAG,EAAEuB,QAAQ,CAAG,CAAC;IACtCO,SAAS,EAAE5B,iBAAiB,GAAG,qBAAqB,GAAG,EAAE;IACzD6B,SAASA,CAAEC,KAA4C,EAAG;MACzD,IACCjC,SAAS,CAACL,QAAQ,CAACuC,IAAI,CAAE,CAAE;QAAEtC,QAAQ;QAAEC;MAAU,CAAC,KAAM;QACvD,OAAOsC,yBAAe,CAAEvC,QAAQ,CAAE,CAAEqC,KAAK,EAAEpC,SAAU,CAAC;MACvD,CAAE,CAAC,EACF;QACDS,WAAW,CAAE,CAAC,CAAE,CAAC;MAClB,CAAC,MAAM,IACNN,SAAS,CAACF,IAAI,CAACoC,IAAI,CAAE,CAAE;QAAEtC,QAAQ;QAAEC;MAAU,CAAC,KAAM;QACnD,OAAOsC,yBAAe,CAAEvC,QAAQ,CAAE,CAAEqC,KAAK,EAAEpC,SAAU,CAAC;MACvD,CAAE,CAAC,EACF;QACDS,WAAW,CAAE,CAAE,CAAC;MACjB;IACD;EACD,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,IAAA8B,QAAA,GA0Be,IAAAC,mCAA0B,EACtCC,SAAS,IACV,CAAE;EAAEtC,SAAS;EAAE,GAAGuC;AAAM,CAAC,KACxB,IAAAC,MAAA,CAAAC,aAAA;EAAA,GAAU1C,kBAAkB,CAAEC,SAAU;AAAC,GACxC,IAAAwC,MAAA,CAAAC,aAAA,EAACH,SAAS;EAAA,GAAMC;AAAK,CAAI,CACrB,CACL,EACF,iBACD,CAAC;AAAAG,OAAA,CAAAC,OAAA,GAAAP,QAAA"}