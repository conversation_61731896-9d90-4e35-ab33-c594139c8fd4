"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ExternalLink = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _visuallyHidden = require("../visually-hidden");
var _externalLinkStyles = require("./styles/external-link-styles");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function UnforwardedExternalLink(props, ref) {
  const {
    href,
    children,
    className,
    rel = '',
    ...additionalProps
  } = props;
  const optimizedRel = [...new Set([...rel.split(' '), 'external', 'noreferrer', 'noopener'].filter(Boolean))].join(' ');
  const classes = (0, _classnames.default)('components-external-link', className);
  /* Anchor links are perceived as external links.
  This constant helps check for on page anchor links,
  to prevent them from being opened in the editor. */
  const isInternalAnchor = !!href?.startsWith('#');
  const onClickHandler = event => {
    if (isInternalAnchor) {
      event.preventDefault();
    }
    if (props.onClick) {
      props.onClick(event);
    }
  };
  return (/* eslint-disable react/jsx-no-target-blank */
    (0, _react.createElement)("a", {
      ...additionalProps,
      className: classes,
      href: href,
      onClick: onClickHandler,
      target: "_blank",
      rel: optimizedRel,
      ref: ref
    }, children, (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
      as: "span"
    }, /* translators: accessibility text */
    (0, _i18n.__)('(opens in a new tab)')), (0, _react.createElement)(_externalLinkStyles.StyledIcon, {
      icon: _icons.external,
      className: "components-external-link__icon"
    }))
    /* eslint-enable react/jsx-no-target-blank */
  );
}

/**
 * Link to an external resource.
 *
 * ```jsx
 * import { ExternalLink } from '@wordpress/components';
 *
 * const MyExternalLink = () => (
 *   <ExternalLink href="https://wordpress.org">WordPress.org</ExternalLink>
 * );
 * ```
 */
const ExternalLink = (0, _element.forwardRef)(UnforwardedExternalLink);
exports.ExternalLink = ExternalLink;
var _default = ExternalLink;
exports.default = _default;
//# sourceMappingURL=index.js.map