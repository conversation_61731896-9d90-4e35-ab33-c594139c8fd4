{"version": 3, "names": ["_compose", "require", "_deprecated", "_interopRequireDefault", "FocusableIframe", "iframeRef", "props", "ref", "useMergeRefs", "useFocusableIframe", "deprecated", "since", "alternative", "_react", "createElement"], "sources": ["@wordpress/components/src/focusable-iframe/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMergeRefs, useFocusableIframe } from '@wordpress/compose';\nimport deprecated from '@wordpress/deprecated';\n/**\n * Internal dependencies\n */\nimport type { FocusableIframeProps } from './types';\n\nexport default function FocusableIframe( {\n\tiframeRef,\n\t...props\n}: FocusableIframeProps ) {\n\tconst ref = useMergeRefs( [ iframeRef, useFocusableIframe() ] );\n\tdeprecated( 'wp.components.FocusableIframe', {\n\t\tsince: '5.9',\n\t\talternative: 'wp.compose.useFocusableIframe',\n\t} );\n\t// Disable reason: The rendered iframe is a pass-through component,\n\t// assigning props inherited from the rendering parent. It's the\n\t// responsibility of the parent to assign a title.\n\t// eslint-disable-next-line jsx-a11y/iframe-has-title\n\treturn <iframe ref={ ref } { ...props } />;\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AAJA;AACA;AACA;;AAQe,SAASG,eAAeA,CAAE;EACxCC,SAAS;EACT,GAAGC;AACkB,CAAC,EAAG;EACzB,MAAMC,GAAG,GAAG,IAAAC,qBAAY,EAAE,CAAEH,SAAS,EAAE,IAAAI,2BAAkB,EAAC,CAAC,CAAG,CAAC;EAC/D,IAAAC,mBAAU,EAAE,+BAA+B,EAAE;IAC5CC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE;EACd,CAAE,CAAC;EACH;EACA;EACA;EACA;EACA,OAAO,IAAAC,MAAA,CAAAC,aAAA;IAAQP,GAAG,EAAGA,GAAK;IAAA,GAAMD;EAAK,CAAI,CAAC;AAC3C"}