"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.BorderBoxControl = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _borderBoxControlLinkedButton = _interopRequireDefault(require("../border-box-control-linked-button"));
var _borderBoxControlSplitControls = _interopRequireDefault(require("../border-box-control-split-controls"));
var _borderControl = require("../../border-control");
var _baseControlStyles = require("../../base-control/styles/base-control-styles");
var _view = require("../../view");
var _visuallyHidden = require("../../visually-hidden");
var _context = require("../../context");
var _hook = require("./hook");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const BorderLabel = props => {
  const {
    label,
    hideLabelFromVision
  } = props;
  if (!label) {
    return null;
  }
  return hideLabelFromVision ? (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    as: "label"
  }, label) : (0, _react.createElement)(_baseControlStyles.StyledLabel, null, label);
};
const UnconnectedBorderBoxControl = (props, forwardedRef) => {
  const {
    className,
    colors,
    disableCustomColors,
    disableUnits,
    enableAlpha,
    enableStyle,
    hasMixedBorders,
    hideLabelFromVision,
    isLinked,
    label,
    linkedControlClassName,
    linkedValue,
    onLinkedChange,
    onSplitChange,
    popoverPlacement,
    popoverOffset,
    size,
    splitValue,
    toggleLinked,
    wrapperClassName,
    __experimentalIsRenderedInSidebar,
    ...otherProps
  } = (0, _hook.useBorderBoxControl)(props);

  // Use internal state instead of a ref to make sure that the component
  // re-renders when the popover's anchor updates.
  const [popoverAnchor, setPopoverAnchor] = (0, _element.useState)(null);

  // Memoize popoverProps to avoid returning a new object every time.
  const popoverProps = (0, _element.useMemo)(() => popoverPlacement ? {
    placement: popoverPlacement,
    offset: popoverOffset,
    anchor: popoverAnchor,
    shift: true
  } : undefined, [popoverPlacement, popoverOffset, popoverAnchor]);
  const mergedRef = (0, _compose.useMergeRefs)([setPopoverAnchor, forwardedRef]);
  return (0, _react.createElement)(_view.View, {
    className: className,
    ...otherProps,
    ref: mergedRef
  }, (0, _react.createElement)(BorderLabel, {
    label: label,
    hideLabelFromVision: hideLabelFromVision
  }), (0, _react.createElement)(_view.View, {
    className: wrapperClassName
  }, isLinked ? (0, _react.createElement)(_borderControl.BorderControl, {
    className: linkedControlClassName,
    colors: colors,
    disableUnits: disableUnits,
    disableCustomColors: disableCustomColors,
    enableAlpha: enableAlpha,
    enableStyle: enableStyle,
    onChange: onLinkedChange,
    placeholder: hasMixedBorders ? (0, _i18n.__)('Mixed') : undefined,
    __unstablePopoverProps: popoverProps,
    shouldSanitizeBorder: false // This component will handle that.
    ,
    value: linkedValue,
    withSlider: true,
    width: size === '__unstable-large' ? '116px' : '110px',
    __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
    size: size
  }) : (0, _react.createElement)(_borderBoxControlSplitControls.default, {
    colors: colors,
    disableCustomColors: disableCustomColors,
    enableAlpha: enableAlpha,
    enableStyle: enableStyle,
    onChange: onSplitChange,
    popoverPlacement: popoverPlacement,
    popoverOffset: popoverOffset,
    value: splitValue,
    __experimentalIsRenderedInSidebar: __experimentalIsRenderedInSidebar,
    size: size
  }), (0, _react.createElement)(_borderBoxControlLinkedButton.default, {
    onClick: toggleLinked,
    isLinked: isLinked,
    size: size
  })));
};

/**
 * The `BorderBoxControl` effectively has two view states. The first, a "linked"
 * view, allows configuration of a flat border via a single `BorderControl`.
 * The second, a "split" view, contains a `BorderControl` for each side
 * as well as a visualizer for the currently selected borders. Each view also
 * contains a button to toggle between the two.
 *
 * When switching from the "split" view to "linked", if the individual side
 * borders are not consistent, the "linked" view will display any border
 * properties selections that are consistent while showing a mixed state for
 * those that aren't. For example, if all borders had the same color and style
 * but different widths, then the border dropdown in the "linked" view's
 * `BorderControl` would show that consistent color and style but the "linked"
 * view's width input would show "Mixed" placeholder text.
 *
 * ```jsx
 * import { __experimentalBorderBoxControl as BorderBoxControl } from '@wordpress/components';
 * import { __ } from '@wordpress/i18n';
 *
 * const colors = [
 * 	{ name: 'Blue 20', color: '#72aee6' },
 * 	// ...
 * ];
 *
 * const MyBorderBoxControl = () => {
 * 	const defaultBorder = {
 * 		color: '#72aee6',
 * 		style: 'dashed',
 * 		width: '1px',
 * 	};
 * 	const [ borders, setBorders ] = useState( {
 * 		top: defaultBorder,
 * 		right: defaultBorder,
 * 		bottom: defaultBorder,
 * 		left: defaultBorder,
 * 	} );
 * 	const onChange = ( newBorders ) => setBorders( newBorders );
 *
 * 	return (
 * 		<BorderBoxControl
 * 			colors={ colors }
 * 			label={ __( 'Borders' ) }
 * 			onChange={ onChange }
 * 			value={ borders }
 * 		/>
 * 	);
 * };
 * ```
 */
const BorderBoxControl = (0, _context.contextConnect)(UnconnectedBorderBoxControl, 'BorderBoxControl');
exports.BorderBoxControl = BorderBoxControl;
var _default = BorderBoxControl;
exports.default = _default;
//# sourceMappingURL=component.js.map