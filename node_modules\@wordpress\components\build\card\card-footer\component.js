"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.CardFooter = void 0;
var _react = require("react");
var _context = require("../../context");
var _flex = require("../../flex");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedCardFooter(props, forwardedRef) {
  const footerProps = (0, _hook.useCardFooter)(props);
  return (0, _react.createElement)(_flex.Flex, {
    ...footerProps,
    ref: forwardedRef
  });
}

/**
 * `Card<PERSON>ooter` renders an optional footer within a `Card`.
 *
 * ```jsx
 * import { Card, CardBody, CardFooter } from `@wordpress/components`;
 *
 * <Card>
 * 	<CardBody>...</CardBody>
 * 	<CardFooter>...</CardFooter>
 * </Card>
 * ```
 */
const CardFooter = (0, _context.contextConnect)(UnconnectedCardFooter, 'CardFooter');
exports.CardFooter = CardFooter;
var _default = CardFooter;
exports.default = _default;
//# sourceMappingURL=component.js.map