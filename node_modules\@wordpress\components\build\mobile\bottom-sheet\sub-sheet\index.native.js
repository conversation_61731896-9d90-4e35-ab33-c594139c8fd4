"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _element = require("@wordpress/element");
var _components = require("@wordpress/components");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

const {
  Fill,
  Slot
} = (0, _components.createSlotFill)('BottomSheetSubSheet');
const BottomSheetSubSheet = ({
  children,
  navigationButton,
  showSheet,
  isFullScreen
}) => {
  const {
    setIsFullScreen
  } = (0, _element.useContext)(_components.BottomSheetContext);
  (0, _element.useEffect)(() => {
    if (showSheet) {
      setIsFullScreen(isFullScreen);
    }
    // Disable reason: deferring this refactor to the native team.
    // see https://github.com/WordPress/gutenberg/pull/41166
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showSheet, isFullScreen]);
  return (0, _react.createElement)(_react.Fragment, null, showSheet && (0, _react.createElement)(Fill, null, (0, _react.createElement)(_reactNative.SafeAreaView, null, children)), _element.Children.count(children) > 0 && navigationButton);
};
BottomSheetSubSheet.Slot = Slot;
BottomSheetSubSheet.screenName = 'BottomSheetSubSheet';
var _default = BottomSheetSubSheet;
exports.default = _default;
//# sourceMappingURL=index.native.js.map