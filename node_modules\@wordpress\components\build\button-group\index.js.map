{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "UnforwardedButtonGroup", "props", "ref", "className", "restProps", "classes", "classnames", "_react", "createElement", "role", "ButtonGroup", "forwardRef", "exports", "_default", "default"], "sources": ["@wordpress/components/src/button-group/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { ButtonGroupProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\nfunction UnforwardedButtonGroup(\n\tprops: WordPressComponentProps< ButtonGroupProps, 'div', false >,\n\tref: ForwardedRef< HTMLDivElement >\n) {\n\tconst { className, ...restProps } = props;\n\tconst classes = classnames( 'components-button-group', className );\n\n\treturn (\n\t\t<div ref={ ref } role=\"group\" className={ classes } { ...restProps } />\n\t);\n}\n\n/**\n * ButtonGroup can be used to group any related buttons together. To emphasize\n * related buttons, a group should share a common container.\n *\n * ```jsx\n * import { Button, ButtonGroup } from '@wordpress/components';\n *\n * const MyButtonGroup = () => (\n *   <ButtonGroup>\n *     <Button variant=\"primary\">Button 1</Button>\n *     <Button variant=\"primary\">Button 2</Button>\n *   </ButtonGroup>\n * );\n * ```\n */\nexport const ButtonGroup = forwardRef( UnforwardedButtonGroup );\n\nexport default ButtonGroup;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AASA,SAASE,sBAAsBA,CAC9BC,KAAgE,EAChEC,GAAmC,EAClC;EACD,MAAM;IAAEC,SAAS;IAAE,GAAGC;EAAU,CAAC,GAAGH,KAAK;EACzC,MAAMI,OAAO,GAAG,IAAAC,mBAAU,EAAE,yBAAyB,EAAEH,SAAU,CAAC;EAElE,OACC,IAAAI,MAAA,CAAAC,aAAA;IAAKN,GAAG,EAAGA,GAAK;IAACO,IAAI,EAAC,OAAO;IAACN,SAAS,EAAGE,OAAS;IAAA,GAAMD;EAAS,CAAI,CAAC;AAEzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMM,WAAW,GAAG,IAAAC,mBAAU,EAAEX,sBAAuB,CAAC;AAACY,OAAA,CAAAF,WAAA,GAAAA,WAAA;AAAA,IAAAG,QAAA,GAEjDH,WAAW;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}