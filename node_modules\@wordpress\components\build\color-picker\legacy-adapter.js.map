{"version": 3, "names": ["_component", "_interopRequireDefault", "require", "_useDeprecatedProps", "LegacyAdapter", "props", "_react", "createElement", "default", "useDeprecatedProps", "exports"], "sources": ["@wordpress/components/src/color-picker/legacy-adapter.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport ColorPicker from './component';\nimport type { LegacyAdapterProps } from './types';\nimport { useDeprecatedProps } from './use-deprecated-props';\n\nexport const LegacyAdapter = ( props: LegacyAdapterProps ) => {\n\treturn <ColorPicker { ...useDeprecatedProps( props ) } />;\n};\n"], "mappings": ";;;;;;;;AAGA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,mBAAA,GAAAD,OAAA;AALA;AACA;AACA;;AAKO,MAAME,aAAa,GAAKC,KAAyB,IAAM;EAC7D,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACP,UAAA,CAAAQ,OAAW;IAAA,GAAM,IAAAC,sCAAkB,EAAEJ,KAAM;EAAC,CAAI,CAAC;AAC1D,CAAC;AAACK,OAAA,CAAAN,aAAA,GAAAA,aAAA"}