{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_compose", "_deprecated", "_context", "_hooks", "_popover", "UnconnectedDropdown", "props", "forwardedRef", "renderContent", "renderToggle", "className", "contentClassName", "expandOnMobile", "headerTitle", "focusOnMount", "popoverProps", "onClose", "onToggle", "style", "open", "defaultOpen", "position", "variant", "useContextSystem", "undefined", "deprecated", "since", "alternative", "hint", "fallbackPopoverAnchor", "setFallbackPopoverAnchor", "useState", "containerRef", "useRef", "isOpen", "setIsOpen", "useControlledValue", "defaultValue", "value", "onChange", "closeIfFocusOutside", "current", "ownerDocument", "dialog", "activeElement", "closest", "contains", "close", "args", "popoverPropsHaveAnchor", "anchor", "anchorRef", "getAnchorRect", "anchorRect", "_react", "createElement", "ref", "useMergeRefs", "tabIndex", "default", "onFocusOutside", "offset", "classnames", "Dropdown", "contextConnect", "exports", "_default"], "sources": ["@wordpress/components/src/dropdown/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useRef, useState } from '@wordpress/element';\nimport { useMergeRefs } from '@wordpress/compose';\nimport deprecated from '@wordpress/deprecated';\n\n/**\n * Internal dependencies\n */\nimport { contextConnect, useContextSystem } from '../context';\nimport { useControlledValue } from '../utils/hooks';\nimport Popover from '../popover';\nimport type { DropdownProps, DropdownInternalContext } from './types';\n\nconst UnconnectedDropdown = (\n\tprops: DropdownProps,\n\tforwardedRef: ForwardedRef< any >\n) => {\n\tconst {\n\t\trenderContent,\n\t\trenderToggle,\n\t\tclassName,\n\t\tcontentClassName,\n\t\texpandOnMobile,\n\t\theaderTitle,\n\t\tfocusOnMount,\n\t\tpopoverProps,\n\t\tonClose,\n\t\tonToggle,\n\t\tstyle,\n\n\t\topen,\n\t\tdefaultOpen,\n\n\t\t// Deprecated props\n\t\tposition,\n\n\t\t// From context system\n\t\tvariant,\n\t} = useContextSystem< DropdownProps & DropdownInternalContext >(\n\t\tprops,\n\t\t'Dropdown'\n\t);\n\n\tif ( position !== undefined ) {\n\t\tdeprecated( '`position` prop in wp.components.Dropdown', {\n\t\t\tsince: '6.2',\n\t\t\talternative: '`popoverProps.placement` prop',\n\t\t\thint: 'Note that the `position` prop will override any values passed through the `popoverProps.placement` prop.',\n\t\t} );\n\t}\n\n\t// Use internal state instead of a ref to make sure that the component\n\t// re-renders when the popover's anchor updates.\n\tconst [ fallbackPopoverAnchor, setFallbackPopoverAnchor ] =\n\t\tuseState< HTMLDivElement | null >( null );\n\tconst containerRef = useRef< HTMLDivElement >();\n\n\tconst [ isOpen, setIsOpen ] = useControlledValue( {\n\t\tdefaultValue: defaultOpen,\n\t\tvalue: open,\n\t\tonChange: onToggle,\n\t} );\n\n\t/**\n\t * Closes the popover when focus leaves it unless the toggle was pressed or\n\t * focus has moved to a separate dialog. The former is to let the toggle\n\t * handle closing the popover and the latter is to preserve presence in\n\t * case a dialog has opened, allowing focus to return when it's dismissed.\n\t */\n\tfunction closeIfFocusOutside() {\n\t\tif ( ! containerRef.current ) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst { ownerDocument } = containerRef.current;\n\t\tconst dialog =\n\t\t\townerDocument?.activeElement?.closest( '[role=\"dialog\"]' );\n\t\tif (\n\t\t\t! containerRef.current.contains( ownerDocument.activeElement ) &&\n\t\t\t( ! dialog || dialog.contains( containerRef.current ) )\n\t\t) {\n\t\t\tclose();\n\t\t}\n\t}\n\n\tfunction close() {\n\t\tonClose?.();\n\t\tsetIsOpen( false );\n\t}\n\n\tconst args = {\n\t\tisOpen: !! isOpen,\n\t\tonToggle: () => setIsOpen( ! isOpen ),\n\t\tonClose: close,\n\t};\n\tconst popoverPropsHaveAnchor =\n\t\t!! popoverProps?.anchor ||\n\t\t// Note: `anchorRef`, `getAnchorRect` and `anchorRect` are deprecated and\n\t\t// be removed from `Popover` from WordPress 6.3\n\t\t!! popoverProps?.anchorRef ||\n\t\t!! popoverProps?.getAnchorRect ||\n\t\t!! popoverProps?.anchorRect;\n\n\treturn (\n\t\t<div\n\t\t\tclassName={ className }\n\t\t\tref={ useMergeRefs( [\n\t\t\t\tcontainerRef,\n\t\t\t\tforwardedRef,\n\t\t\t\tsetFallbackPopoverAnchor,\n\t\t\t] ) }\n\t\t\t// Some UAs focus the closest focusable parent when the toggle is\n\t\t\t// clicked. Making this div focusable ensures such UAs will focus\n\t\t\t// it and `closeIfFocusOutside` can tell if the toggle was clicked.\n\t\t\ttabIndex={ -1 }\n\t\t\tstyle={ style }\n\t\t>\n\t\t\t{ renderToggle( args ) }\n\t\t\t{ isOpen && (\n\t\t\t\t<Popover\n\t\t\t\t\tposition={ position }\n\t\t\t\t\tonClose={ close }\n\t\t\t\t\tonFocusOutside={ closeIfFocusOutside }\n\t\t\t\t\texpandOnMobile={ expandOnMobile }\n\t\t\t\t\theaderTitle={ headerTitle }\n\t\t\t\t\tfocusOnMount={ focusOnMount }\n\t\t\t\t\t// This value is used to ensure that the dropdowns\n\t\t\t\t\t// align with the editor header by default.\n\t\t\t\t\toffset={ 13 }\n\t\t\t\t\tanchor={\n\t\t\t\t\t\t! popoverPropsHaveAnchor\n\t\t\t\t\t\t\t? fallbackPopoverAnchor\n\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t}\n\t\t\t\t\tvariant={ variant }\n\t\t\t\t\t{ ...popoverProps }\n\t\t\t\t\tclassName={ classnames(\n\t\t\t\t\t\t'components-dropdown__content',\n\t\t\t\t\t\tpopoverProps?.className,\n\t\t\t\t\t\tcontentClassName\n\t\t\t\t\t) }\n\t\t\t\t>\n\t\t\t\t\t{ renderContent( args ) }\n\t\t\t\t</Popover>\n\t\t\t) }\n\t\t</div>\n\t);\n};\n\n/**\n * Renders a button that opens a floating content modal when clicked.\n *\n * ```jsx\n * import { Button, Dropdown } from '@wordpress/components';\n *\n * const MyDropdown = () => (\n *   <Dropdown\n *     className=\"my-container-class-name\"\n *     contentClassName=\"my-dropdown-content-classname\"\n *     popoverProps={ { placement: 'bottom-start' } }\n *     renderToggle={ ( { isOpen, onToggle } ) => (\n *       <Button\n *         variant=\"primary\"\n *         onClick={ onToggle }\n *         aria-expanded={ isOpen }\n *       >\n *         Toggle Dropdown!\n *       </Button>\n *     ) }\n *     renderContent={ () => <div>This is the content of the dropdown.</div> }\n *   />\n * );\n * ```\n */\nexport const Dropdown = contextConnect( UnconnectedDropdown, 'Dropdown' );\n\nexport default Dropdown;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAKA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAP,sBAAA,CAAAC,OAAA;AAlBA;AACA;AACA;;AAIA;AACA;AACA;;AAKA;AACA;AACA;;AAMA,MAAMO,mBAAmB,GAAGA,CAC3BC,KAAoB,EACpBC,YAAiC,KAC7B;EACJ,MAAM;IACLC,aAAa;IACbC,YAAY;IACZC,SAAS;IACTC,gBAAgB;IAChBC,cAAc;IACdC,WAAW;IACXC,YAAY;IACZC,YAAY;IACZC,OAAO;IACPC,QAAQ;IACRC,KAAK;IAELC,IAAI;IACJC,WAAW;IAEX;IACAC,QAAQ;IAER;IACAC;EACD,CAAC,GAAG,IAAAC,yBAAgB,EACnBjB,KAAK,EACL,UACD,CAAC;EAED,IAAKe,QAAQ,KAAKG,SAAS,EAAG;IAC7B,IAAAC,mBAAU,EAAE,2CAA2C,EAAE;MACxDC,KAAK,EAAE,KAAK;MACZC,WAAW,EAAE,+BAA+B;MAC5CC,IAAI,EAAE;IACP,CAAE,CAAC;EACJ;;EAEA;EACA;EACA,MAAM,CAAEC,qBAAqB,EAAEC,wBAAwB,CAAE,GACxD,IAAAC,iBAAQ,EAA2B,IAAK,CAAC;EAC1C,MAAMC,YAAY,GAAG,IAAAC,eAAM,EAAmB,CAAC;EAE/C,MAAM,CAAEC,MAAM,EAAEC,SAAS,CAAE,GAAG,IAAAC,yBAAkB,EAAE;IACjDC,YAAY,EAAEjB,WAAW;IACzBkB,KAAK,EAAEnB,IAAI;IACXoB,QAAQ,EAAEtB;EACX,CAAE,CAAC;;EAEH;AACD;AACA;AACA;AACA;AACA;EACC,SAASuB,mBAAmBA,CAAA,EAAG;IAC9B,IAAK,CAAER,YAAY,CAACS,OAAO,EAAG;MAC7B;IACD;IAEA,MAAM;MAAEC;IAAc,CAAC,GAAGV,YAAY,CAACS,OAAO;IAC9C,MAAME,MAAM,GACXD,aAAa,EAAEE,aAAa,EAAEC,OAAO,CAAE,iBAAkB,CAAC;IAC3D,IACC,CAAEb,YAAY,CAACS,OAAO,CAACK,QAAQ,CAAEJ,aAAa,CAACE,aAAc,CAAC,KAC5D,CAAED,MAAM,IAAIA,MAAM,CAACG,QAAQ,CAAEd,YAAY,CAACS,OAAQ,CAAC,CAAE,EACtD;MACDM,KAAK,CAAC,CAAC;IACR;EACD;EAEA,SAASA,KAAKA,CAAA,EAAG;IAChB/B,OAAO,GAAG,CAAC;IACXmB,SAAS,CAAE,KAAM,CAAC;EACnB;EAEA,MAAMa,IAAI,GAAG;IACZd,MAAM,EAAE,CAAC,CAAEA,MAAM;IACjBjB,QAAQ,EAAEA,CAAA,KAAMkB,SAAS,CAAE,CAAED,MAAO,CAAC;IACrClB,OAAO,EAAE+B;EACV,CAAC;EACD,MAAME,sBAAsB,GAC3B,CAAC,CAAElC,YAAY,EAAEmC,MAAM;EACvB;EACA;EACA,CAAC,CAAEnC,YAAY,EAAEoC,SAAS,IAC1B,CAAC,CAAEpC,YAAY,EAAEqC,aAAa,IAC9B,CAAC,CAAErC,YAAY,EAAEsC,UAAU;EAE5B,OACC,IAAAC,MAAA,CAAAC,aAAA;IACC7C,SAAS,EAAGA,SAAW;IACvB8C,GAAG,EAAG,IAAAC,qBAAY,EAAE,CACnBzB,YAAY,EACZzB,YAAY,EACZuB,wBAAwB,CACvB;IACF;IACA;IACA;IAAA;IACA4B,QAAQ,EAAG,CAAC,CAAG;IACfxC,KAAK,EAAGA;EAAO,GAEbT,YAAY,CAAEuC,IAAK,CAAC,EACpBd,MAAM,IACP,IAAAoB,MAAA,CAAAC,aAAA,EAACnD,QAAA,CAAAuD,OAAO;IACPtC,QAAQ,EAAGA,QAAU;IACrBL,OAAO,EAAG+B,KAAO;IACjBa,cAAc,EAAGpB,mBAAqB;IACtC5B,cAAc,EAAGA,cAAgB;IACjCC,WAAW,EAAGA,WAAa;IAC3BC,YAAY,EAAGA;IACf;IACA;IAAA;IACA+C,MAAM,EAAG,EAAI;IACbX,MAAM,EACL,CAAED,sBAAsB,GACrBpB,qBAAqB,GACrBL,SACH;IACDF,OAAO,EAAGA,OAAS;IAAA,GACdP,YAAY;IACjBL,SAAS,EAAG,IAAAoD,mBAAU,EACrB,8BAA8B,EAC9B/C,YAAY,EAAEL,SAAS,EACvBC,gBACD;EAAG,GAEDH,aAAa,CAAEwC,IAAK,CACd,CAEN,CAAC;AAER,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMe,QAAQ,GAAG,IAAAC,uBAAc,EAAE3D,mBAAmB,EAAE,UAAW,CAAC;AAAC4D,OAAA,CAAAF,QAAA,GAAAA,QAAA;AAAA,IAAAG,QAAA,GAE3DH,QAAQ;AAAAE,OAAA,CAAAN,OAAA,GAAAO,QAAA"}