"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _icons = require("@wordpress/icons");
var _components = require("@wordpress/components");
var _compose = require("@wordpress/compose");
var _style = _interopRequireDefault(require("./style.scss"));
var _utils = require("../mobile/color-settings/utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function SelectedIcon({
  opacity
}) {
  return (0, _react.createElement)(_reactNative.Animated.View, {
    style: [_style.default.selected, {
      opacity
    }]
  }, (0, _react.createElement)(_reactNative.View, {
    style: [_style.default.outline, _style.default.selectedOutline]
  }), (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check,
    style: _style.default.icon,
    size: 24
  }));
}
function ColorIndicator({
  color,
  isSelected,
  withCustomPicker,
  style,
  opacity
}) {
  const {
    isGradient
  } = _utils.colorsUtils;
  const outlineStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.outline, _style.default.outlineDark);
  if (isGradient(color)) {
    return (0, _react.createElement)(_components.Gradient, {
      style: [_style.default.circleOption, style],
      gradientValue: color
    }, (0, _react.createElement)(_reactNative.View, {
      style: outlineStyle
    }), isSelected && (0, _react.createElement)(SelectedIcon, {
      opacity: opacity
    }));
  } else if (withCustomPicker) {
    return (0, _react.createElement)(_reactNative.View, {
      style: [_style.default.circleOption, style]
    }, (0, _react.createElement)(_reactNative.View, {
      style: outlineStyle
    }), color.map(gradientValue => {
      return (0, _react.createElement)(_components.Gradient, {
        gradientValue: gradientValue,
        style: [_style.default.circleOption, _style.default.absolute, style],
        key: gradientValue
      });
    }), isSelected && (0, _react.createElement)(SelectedIcon, null));
  }
  return (0, _react.createElement)(_reactNative.View, {
    style: [_style.default.circleOption, style, {
      backgroundColor: color
    }]
  }, (0, _react.createElement)(_reactNative.View, {
    style: outlineStyle
  }), isSelected && (0, _react.createElement)(SelectedIcon, {
    opacity: opacity
  }));
}
var _default = ColorIndicator;
exports.default = _default;
//# sourceMappingURL=index.native.js.map