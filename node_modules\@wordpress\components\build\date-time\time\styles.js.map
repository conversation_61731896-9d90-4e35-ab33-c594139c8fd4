{"version": 3, "names": ["_react", "require", "_utils", "_space", "_inputControlStyles", "_numberControl", "_interopRequireDefault", "_EMOTION_STRINGIFIED_CSS_ERROR__", "Wrapper", "_base", "default", "process", "env", "NODE_ENV", "target", "label", "CONFIG", "fontSize", "exports", "<PERSON><PERSON>", "space", "TimeWrapper", "name", "styles", "map", "toString", "baseInput", "css", "Input", "HoursInput", "NumberControl", "BackdropUI", "TimeSeparator", "borderWidth", "COLORS", "gray", "controlHeight", "MinutesInput", "MonthSelectWrapper", "DayInput", "YearInput", "TimeZone"], "sources": ["@wordpress/components/src/date-time/time/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport styled from '@emotion/styled';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { COLORS, CONFIG } from '../../utils';\nimport { space } from '../../utils/space';\nimport {\n\tInput,\n\tBackdropUI,\n} from '../../input-control/styles/input-control-styles';\nimport NumberControl from '../../number-control';\n\nexport const Wrapper = styled.div`\n\tbox-sizing: border-box;\n\tfont-size: ${ CONFIG.fontSize };\n`;\n\nexport const Fieldset = styled.fieldset`\n\tborder: 0;\n\tmargin: 0 0 ${ space( 2 * 2 ) } 0;\n\tpadding: 0;\n\n\t&:last-child {\n\t\tmargin-bottom: 0;\n\t}\n`;\n\nexport const TimeWrapper = styled.div`\n\tdirection: ltr;\n\tdisplay: flex;\n`;\n\nconst baseInput = css`\n\t&&& ${ Input } {\n\t\tpadding-left: ${ space( 2 ) };\n\t\tpadding-right: ${ space( 2 ) };\n\t\ttext-align: center;\n\t}\n`;\n\nexport const HoursInput = styled( NumberControl )`\n\t${ baseInput }\n\n\twidth: ${ space( 9 ) };\n\n\t&&& ${ Input } {\n\t\tpadding-right: 0;\n\t}\n\n\t&&& ${ BackdropUI } {\n\t\tborder-right: 0;\n\t\tborder-top-right-radius: 0;\n\t\tborder-bottom-right-radius: 0;\n\t}\n`;\n\nexport const TimeSeparator = styled.span`\n\tborder-top: ${ CONFIG.borderWidth } solid ${ COLORS.gray[ 700 ] };\n\tborder-bottom: ${ CONFIG.borderWidth } solid ${ COLORS.gray[ 700 ] };\n\tline-height: calc(\n\t\t${ CONFIG.controlHeight } - ${ CONFIG.borderWidth } * 2\n\t);\n\tdisplay: inline-block;\n`;\n\nexport const MinutesInput = styled( NumberControl )`\n\t${ baseInput }\n\n\twidth: ${ space( 9 ) };\n\n\t&&& ${ Input } {\n\t\tpadding-left: 0;\n\t}\n\n\t&&& ${ BackdropUI } {\n\t\tborder-left: 0;\n\t\tborder-top-left-radius: 0;\n\t\tborder-bottom-left-radius: 0;\n\t}\n`;\n\n// Ideally we wouldn't need a wrapper, but can't otherwise target the\n// <BaseControl> in <SelectControl>\nexport const MonthSelectWrapper = styled.div`\n\tflex-grow: 1;\n`;\n\nexport const DayInput = styled( NumberControl )`\n\t${ baseInput }\n\n\twidth: ${ space( 9 ) };\n`;\n\nexport const YearInput = styled( NumberControl )`\n\t${ baseInput }\n\n\twidth: ${ space( 14 ) };\n`;\n\nexport const TimeZone = styled.div`\n\ttext-decoration: underline dotted;\n`;\n"], "mappings": ";;;;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AAIA,IAAAI,cAAA,GAAAC,sBAAA,CAAAL,OAAA;AAAiD,SAAAM,iCAAA;AAE1C,MAAMC,OAAO,OAAAC,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,uCAELC,aAAM,CAACC,QAAQ,SAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA,gvGAC7B;AAACK,OAAA,CAAAV,OAAA,GAAAA,OAAA;AAEK,MAAMW,QAAQ,OAAAV,KAAA,CAAAC,OAAA,cAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,2BAEL,IAAAK,YAAK,EAAE,CAAC,GAAG,CAAE,CAAC,mDAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA,gvGAM7B;AAACK,OAAA,CAAAC,QAAA,GAAAA,QAAA;AAEK,MAAME,WAAW,OAAAZ,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAS,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAlB;AAAA,EAGvB;AAACW,OAAA,CAAAG,WAAA,GAAAA,WAAA;AAEF,MAAMK,SAAS,oBAAGC,UAAG,UACbC,yBAAK,oBACM,IAAAR,YAAK,EAAE,CAAE,CAAC,qBACT,IAAAA,YAAK,EAAE,CAAE,CAAC,4BAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA,+CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,+uGAG7B;AAEM,MAAMgB,UAAU,GAAG,kBAAApB,KAAA,CAAAC,OAAA,EAAQoB,sBAAa,EAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAC7CW,SAAS,aAEF,IAAAN,YAAK,EAAE,CAAE,CAAC,WAEbQ,yBAAK,4BAILG,8BAAU,gFAAApB,OAAA,CAAAC,GAAA,CAAAC,QAAA,gvGAKjB;AAACK,OAAA,CAAAW,UAAA,GAAAA,UAAA;AAEK,MAAMG,aAAa,OAAAvB,KAAA,CAAAC,OAAA,UAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,kBACVC,aAAM,CAACiB,WAAW,aAAYC,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,qBAC7CnB,aAAM,CAACiB,WAAW,aAAYC,aAAM,CAACC,IAAI,CAAE,GAAG,CAAE,8BAE9DnB,aAAM,CAACoB,aAAa,SAAQpB,aAAM,CAACiB,WAAW,uCAAAtB,OAAA,CAAAC,GAAA,CAAAC,QAAA,gvGAGlD;AAACK,OAAA,CAAAc,aAAA,GAAAA,aAAA;AAEK,MAAMK,YAAY,GAAG,kBAAA5B,KAAA,CAAAC,OAAA,EAAQoB,sBAAa,EAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAC/CW,SAAS,aAEF,IAAAN,YAAK,EAAE,CAAE,CAAC,WAEbQ,yBAAK,2BAILG,8BAAU,6EAAApB,OAAA,CAAAC,GAAA,CAAAC,QAAA,gvGAKjB;;AAED;AACA;AAAAK,OAAA,CAAAmB,YAAA,GAAAA,YAAA;AACO,MAAMC,kBAAkB,OAAA7B,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAS,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAlB;AAAA,EAE9B;AAACW,OAAA,CAAAoB,kBAAA,GAAAA,kBAAA;AAEK,MAAMC,QAAQ,GAAG,kBAAA9B,KAAA,CAAAC,OAAA,EAAQoB,sBAAa,EAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAC3CW,SAAS,aAEF,IAAAN,YAAK,EAAE,CAAE,CAAC,SAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA,gvGACpB;AAACK,OAAA,CAAAqB,QAAA,GAAAA,QAAA;AAEK,MAAMC,SAAS,GAAG,kBAAA/B,KAAA,CAAAC,OAAA,EAAQoB,sBAAa,EAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAC5CW,SAAS,aAEF,IAAAN,YAAK,EAAE,EAAG,CAAC,SAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA,gvGACrB;AAACK,OAAA,CAAAsB,SAAA,GAAAA,SAAA;AAEK,MAAMC,QAAQ,OAAAhC,KAAA,CAAAC,OAAA,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAS,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAlB;AAAA,EAEpB;AAACW,OAAA,CAAAuB,QAAA,GAAAA,QAAA"}