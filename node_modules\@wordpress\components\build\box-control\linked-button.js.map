{"version": 3, "names": ["_icons", "require", "_i18n", "_button", "_interopRequireDefault", "_tooltip", "LinkedButton", "isLinked", "props", "label", "__", "_react", "createElement", "default", "text", "className", "size", "icon", "link", "linkOff", "iconSize"], "sources": ["@wordpress/components/src/box-control/linked-button.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { link, linkOff } from '@wordpress/icons';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport Button from '../button';\nimport Tooltip from '../tooltip';\n\nexport default function LinkedButton( {\n\tisLinked,\n\t...props\n}: { isLinked?: boolean } & React.ComponentProps< typeof Button > ) {\n\tconst label = isLinked ? __( 'Unlink sides' ) : __( 'Link sides' );\n\n\treturn (\n\t\t<Tooltip text={ label }>\n\t\t\t<Button\n\t\t\t\t{ ...props }\n\t\t\t\tclassName=\"component-box-control__linked-button\"\n\t\t\t\tsize=\"small\"\n\t\t\t\ticon={ isLinked ? link : linkOff }\n\t\t\t\ticonSize={ 24 }\n\t\t\t\taria-label={ label }\n\t\t\t/>\n\t\t</Tooltip>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAKA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,QAAA,GAAAD,sBAAA,CAAAH,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAIe,SAASK,YAAYA,CAAE;EACrCC,QAAQ;EACR,GAAGC;AAC4D,CAAC,EAAG;EACnE,MAAMC,KAAK,GAAGF,QAAQ,GAAG,IAAAG,QAAE,EAAE,cAAe,CAAC,GAAG,IAAAA,QAAE,EAAE,YAAa,CAAC;EAElE,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACP,QAAA,CAAAQ,OAAO;IAACC,IAAI,EAAGL;EAAO,GACtB,IAAAE,MAAA,CAAAC,aAAA,EAACT,OAAA,CAAAU,OAAM;IAAA,GACDL,KAAK;IACVO,SAAS,EAAC,sCAAsC;IAChDC,IAAI,EAAC,OAAO;IACZC,IAAI,EAAGV,QAAQ,GAAGW,WAAI,GAAGC,cAAS;IAClCC,QAAQ,EAAG,EAAI;IACf,cAAaX;EAAO,CACpB,CACO,CAAC;AAEZ"}