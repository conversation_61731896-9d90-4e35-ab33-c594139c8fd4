"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.MenuItem = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
var _shortcut = _interopRequireDefault(require("../shortcut"));
var _button = _interopRequireDefault(require("../button"));
var _icon = _interopRequireDefault(require("../icon"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function UnforwardedMenuItem(props, ref) {
  let {
    children,
    info,
    className,
    icon,
    iconPosition = 'right',
    shortcut,
    isSelected,
    role = 'menuitem',
    suffix,
    ...buttonProps
  } = props;
  className = (0, _classnames.default)('components-menu-item__button', className);
  if (info) {
    children = (0, _react.createElement)("span", {
      className: "components-menu-item__info-wrapper"
    }, (0, _react.createElement)("span", {
      className: "components-menu-item__item"
    }, children), (0, _react.createElement)("span", {
      className: "components-menu-item__info"
    }, info));
  }
  if (icon && typeof icon !== 'string') {
    icon = (0, _element.cloneElement)(icon, {
      className: (0, _classnames.default)('components-menu-items__item-icon', {
        'has-icon-right': iconPosition === 'right'
      })
    });
  }
  return (0, _react.createElement)(_button.default, {
    ref: ref
    // Make sure aria-checked matches spec https://www.w3.org/TR/wai-aria-1.1/#aria-checked
    ,
    "aria-checked": role === 'menuitemcheckbox' || role === 'menuitemradio' ? isSelected : undefined,
    role: role,
    icon: iconPosition === 'left' ? icon : undefined,
    className: className,
    ...buttonProps
  }, (0, _react.createElement)("span", {
    className: "components-menu-item__item"
  }, children), !suffix && (0, _react.createElement)(_shortcut.default, {
    className: "components-menu-item__shortcut",
    shortcut: shortcut
  }), !suffix && icon && iconPosition === 'right' && (0, _react.createElement)(_icon.default, {
    icon: icon
  }), suffix);
}

/**
 * MenuItem is a component which renders a button intended to be used in combination with the `DropdownMenu` component.
 *
 * ```jsx
 * import { MenuItem } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyMenuItem = () => {
 * 	const [ isActive, setIsActive ] = useState( true );
 *
 * 	return (
 * 		<MenuItem
 * 			icon={ isActive ? 'yes' : 'no' }
 * 			isSelected={ isActive }
 * 			role="menuitemcheckbox"
 * 			onClick={ () => setIsActive( ( state ) => ! state ) }
 * 		>
 * 			Toggle
 * 		</MenuItem>
 * 	);
 * };
 * ```
 */
const MenuItem = (0, _element.forwardRef)(UnforwardedMenuItem);
exports.MenuItem = MenuItem;
var _default = MenuItem;
exports.default = _default;
//# sourceMappingURL=index.js.map