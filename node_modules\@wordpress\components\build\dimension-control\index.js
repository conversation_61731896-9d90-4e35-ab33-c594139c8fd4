"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.DimensionControl = DimensionControl;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _i18n = require("@wordpress/i18n");
var _icon = _interopRequireDefault(require("../icon"));
var _selectControl = _interopRequireDefault(require("../select-control"));
var _sizes = _interopRequireWildcard(require("./sizes"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * `DimensionControl` is a component designed to provide a UI to control spacing and/or dimensions.
 *
 * This feature is still experimental. “Experimental” means this is an early implementation subject to drastic and breaking changes.
 *
 * ```jsx
 * import { __experimentalDimensionControl as DimensionControl } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * export default function MyCustomDimensionControl() {
 * 	const [ paddingSize, setPaddingSize ] = useState( '' );
 *
 * 	return (
 * 		<DimensionControl
 * 			label={ 'Padding' }
 * 			icon={ 'desktop' }
 * 			onChange={ ( value ) => setPaddingSize( value ) }
 * 			value={ paddingSize }
 * 		/>
 * 	);
 * }
 * ```
 */
function DimensionControl(props) {
  const {
    __next40pxDefaultSize = false,
    label,
    value,
    sizes = _sizes.default,
    icon,
    onChange,
    className = ''
  } = props;
  const onChangeSpacingSize = val => {
    const theSize = (0, _sizes.findSizeBySlug)(sizes, val);
    if (!theSize || value === theSize.slug) {
      onChange?.(undefined);
    } else if (typeof onChange === 'function') {
      onChange(theSize.slug);
    }
  };
  const formatSizesAsOptions = theSizes => {
    const options = theSizes.map(({
      name,
      slug
    }) => ({
      label: name,
      value: slug
    }));
    return [{
      label: (0, _i18n.__)('Default'),
      value: ''
    }, ...options];
  };
  const selectLabel = (0, _react.createElement)(_react.Fragment, null, icon && (0, _react.createElement)(_icon.default, {
    icon: icon
  }), label);
  return (0, _react.createElement)(_selectControl.default, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    className: (0, _classnames.default)(className, 'block-editor-dimension-control'),
    label: selectLabel,
    hideLabelFromVision: false,
    value: value,
    onChange: onChangeSpacingSize,
    options: formatSizesAsOptions(sizes)
  });
}
var _default = DimensionControl;
exports.default = _default;
//# sourceMappingURL=index.js.map