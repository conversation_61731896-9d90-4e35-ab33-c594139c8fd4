"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.wrapper = void 0;
var _react = require("@emotion/react");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
/**
 * The z-index for ConfirmDialog is being set here instead of in
 * packages/base-styles/_z-index.scss, because this component uses
 * emotion instead of sass.
 *
 * ConfirmDialog needs this higher z-index to ensure it renders on top of
 * any parent Popover component.
 */
const wrapper = process.env.NODE_ENV === "production" ? {
  name: "7g5ii0",
  styles: "&&{z-index:1000001;}"
} : {
  name: "1gucf3d-wrapper",
  styles: "&&{z-index:1000001;};label:wrapper;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY29uZmlybS1kaWFsb2cvc3R5bGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQWEwQiIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2NvbmZpcm0tZGlhbG9nL3N0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IGNzcyB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcblxuLyoqXG4gKiBUaGUgei1pbmRleCBmb3IgQ29uZmlybURpYWxvZyBpcyBiZWluZyBzZXQgaGVyZSBpbnN0ZWFkIG9mIGluXG4gKiBwYWNrYWdlcy9iYXNlLXN0eWxlcy9fei1pbmRleC5zY3NzLCBiZWNhdXNlIHRoaXMgY29tcG9uZW50IHVzZXNcbiAqIGVtb3Rpb24gaW5zdGVhZCBvZiBzYXNzLlxuICpcbiAqIENvbmZpcm1EaWFsb2cgbmVlZHMgdGhpcyBoaWdoZXIgei1pbmRleCB0byBlbnN1cmUgaXQgcmVuZGVycyBvbiB0b3Agb2ZcbiAqIGFueSBwYXJlbnQgUG9wb3ZlciBjb21wb25lbnQuXG4gKi9cbmV4cG9ydCBjb25zdCB3cmFwcGVyID0gY3NzYFxuXHQmJiB7XG5cdFx0ei1pbmRleDogMTAwMDAwMTtcblx0fVxuYDtcbiJdfQ== */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
exports.wrapper = wrapper;
//# sourceMappingURL=styles.js.map