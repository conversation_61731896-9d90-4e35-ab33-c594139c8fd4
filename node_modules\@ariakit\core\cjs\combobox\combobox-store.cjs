"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _E53JW5BDcjs = require('../__chunks/E53JW5BD.cjs');
require('../__chunks/I3Y4EXEF.cjs');
require('../__chunks/OTVLDMN2.cjs');


var _BFE5R4EZcjs = require('../__chunks/BFE5R4EZ.cjs');
require('../__chunks/MI6NUQYQ.cjs');






var _F6HPKLO2cjs = require('../__chunks/F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('../__chunks/KBNYGXWI.cjs');



var _YIUPKKEKcjs = require('../__chunks/YIUPKKEK.cjs');
require('../__chunks/5F4DVUNS.cjs');
require('../__chunks/ULSPM3Y3.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/combobox/combobox-store.ts
var isSafariOnMobile = _YIUPKKEKcjs.isSafari.call(void 0, ) && _YIUPKKEKcjs.isTouchDevice.call(void 0, );
function createComboboxStore(props = {}) {
  var _a;
  _F6HPKLO2cjs.throwOnConflictingProps.call(void 0, props, props.store);
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const activeId = _KBNYGXWIcjs.defaultValue.call(void 0, 
    props.activeId,
    syncState == null ? void 0 : syncState.activeId,
    props.defaultActiveId,
    null
  );
  const composite = _BFE5R4EZcjs.createCompositeStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    activeId,
    includesBaseElement: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.includesBaseElement,
      syncState == null ? void 0 : syncState.includesBaseElement,
      true
    ),
    orientation: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.orientation,
      syncState == null ? void 0 : syncState.orientation,
      "vertical"
    ),
    focusLoop: _KBNYGXWIcjs.defaultValue.call(void 0, props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true),
    focusWrap: _KBNYGXWIcjs.defaultValue.call(void 0, props.focusWrap, syncState == null ? void 0 : syncState.focusWrap, true),
    virtualFocus: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.virtualFocus,
      syncState == null ? void 0 : syncState.virtualFocus,
      !isSafariOnMobile
    )
  }));
  const popover = _E53JW5BDcjs.createPopoverStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    placement: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.placement,
      syncState == null ? void 0 : syncState.placement,
      "bottom-start"
    )
  }));
  const value = _KBNYGXWIcjs.defaultValue.call(void 0, 
    props.value,
    syncState == null ? void 0 : syncState.value,
    props.defaultValue,
    ""
  );
  const selectedValue = _KBNYGXWIcjs.defaultValue.call(void 0, 
    props.selectedValue,
    syncState == null ? void 0 : syncState.selectedValue,
    props.defaultSelectedValue,
    ""
  );
  const multiSelectable = Array.isArray(selectedValue);
  const initialState = _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite.getState()), popover.getState()), {
    value,
    selectedValue,
    resetValueOnSelect: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.resetValueOnSelect,
      syncState == null ? void 0 : syncState.resetValueOnSelect,
      multiSelectable
    ),
    resetValueOnHide: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.resetValueOnHide,
      syncState == null ? void 0 : syncState.resetValueOnHide,
      multiSelectable
    ),
    activeValue: syncState == null ? void 0 : syncState.activeValue
  });
  const combobox = _F6HPKLO2cjs.createStore.call(void 0, initialState, composite, popover, props.store);
  _F6HPKLO2cjs.setup.call(void 0, 
    combobox,
    () => _F6HPKLO2cjs.sync.call(void 0, combobox, ["resetValueOnHide", "mounted"], (state) => {
      if (!state.resetValueOnHide)
        return;
      if (state.mounted)
        return;
      combobox.setState("value", value);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    combobox,
    () => _F6HPKLO2cjs.sync.call(void 0, combobox, ["resetValueOnSelect", "selectedValue"], (state) => {
      if (!state.resetValueOnSelect)
        return;
      combobox.setState("value", value);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    combobox,
    () => _F6HPKLO2cjs.batch.call(void 0, combobox, ["mounted"], (state) => {
      if (state.mounted)
        return;
      combobox.setState("activeId", activeId);
      combobox.setState("moves", 0);
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    combobox,
    () => _F6HPKLO2cjs.sync.call(void 0, combobox, ["moves", "activeId"], (state, prevState) => {
      if (state.moves === prevState.moves) {
        combobox.setState("activeValue", void 0);
      }
    })
  );
  _F6HPKLO2cjs.setup.call(void 0, 
    combobox,
    () => _F6HPKLO2cjs.batch.call(void 0, combobox, ["moves", "renderedItems"], (state, prev) => {
      if (state.moves === prev.moves)
        return;
      const { activeId: activeId2 } = combobox.getState();
      const activeItem = composite.item(activeId2);
      combobox.setState("activeValue", activeItem == null ? void 0 : activeItem.value);
    })
  );
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, popover), composite), combobox), {
    setValue: (value2) => combobox.setState("value", value2),
    setSelectedValue: (selectedValue2) => combobox.setState("selectedValue", selectedValue2)
  });
}


exports.createComboboxStore = createComboboxStore;
