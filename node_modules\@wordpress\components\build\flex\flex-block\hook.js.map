{"version": 3, "names": ["_context", "require", "_flexItem", "useFlexBlock", "props", "otherProps", "useContextSystem", "flexItemProps", "useFlexItem", "isBlock"], "sources": ["@wordpress/components/src/flex/flex-block/hook.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useFlexItem } from '../flex-item';\nimport type { FlexBlockProps } from '../types';\n\nexport function useFlexBlock(\n\tprops: WordPressComponentProps< FlexBlockProps, 'div' >\n) {\n\tconst otherProps = useContextSystem( props, 'FlexBlock' );\n\tconst flexItemProps = useFlexItem( { isBlock: true, ...otherProps } );\n\n\treturn flexItemProps;\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AALA;AACA;AACA;;AAMO,SAASE,YAAYA,CAC3BC,KAAuD,EACtD;EACD,MAAMC,UAAU,GAAG,IAAAC,yBAAgB,EAAEF,KAAK,EAAE,WAAY,CAAC;EACzD,MAAMG,aAAa,GAAG,IAAAC,qBAAW,EAAE;IAAEC,OAAO,EAAE,IAAI;IAAE,GAAGJ;EAAW,CAAE,CAAC;EAErE,OAAOE,aAAa;AACrB"}