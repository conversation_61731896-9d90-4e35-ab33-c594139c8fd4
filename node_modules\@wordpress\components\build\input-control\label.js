"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = Label;
var _react = require("react");
var _visuallyHidden = require("../visually-hidden");
var _inputControlStyles = require("./styles/input-control-styles");
/**
 * Internal dependencies
 */

function Label({
  children,
  hideLabelFromVision,
  htmlFor,
  ...props
}) {
  if (!children) return null;
  if (hideLabelFromVision) {
    return (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
      as: "label",
      htmlFor: htmlFor
    }, children);
  }
  return (0, _react.createElement)(_inputControlStyles.LabelWrapper, null, (0, _react.createElement)(_inputControlStyles.Label, {
    htmlFor: htmlFor,
    ...props
  }, children));
}
//# sourceMappingURL=label.js.map