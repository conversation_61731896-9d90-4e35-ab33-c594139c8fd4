"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = FocusableIframe;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
/**
 * WordPress dependencies
 */

function FocusableIframe({
  iframeRef,
  ...props
}) {
  const ref = (0, _compose.useMergeRefs)([iframeRef, (0, _compose.useFocusableIframe)()]);
  (0, _deprecated.default)('wp.components.FocusableIframe', {
    since: '5.9',
    alternative: 'wp.compose.useFocusableIframe'
  });
  // Disable reason: The rendered iframe is a pass-through component,
  // assigning props inherited from the rendering parent. It's the
  // responsibility of the parent to assign a title.
  // eslint-disable-next-line jsx-a11y/iframe-has-title
  return (0, _react.createElement)("iframe", {
    ref: ref,
    ...props
  });
}
//# sourceMappingURL=index.js.map