{"version": 3, "names": ["_react", "require", "_utils", "_space", "_baseControlStyles", "_unitControlStyles", "_EMOTION_STRINGIFIED_CSS_ERROR__", "labelStyles", "process", "env", "NODE_ENV", "name", "styles", "map", "toString", "focusBoxShadow", "css", "CONFIG", "controlBoxShadowFocus", "borderControl", "boxSizingReset", "exports", "innerWrapper", "UnitControlWrapper", "UnitSelect", "wrapperWidth", "wrapperHeight", "size", "borderControlDropdown", "rtl", "borderRadius", "borderWidth", "COLORS", "ui", "border", "borderFocus", "colorIndicatorBorder", "color", "style", "fallbackColor", "gray", "undefined", "colorIndicatorWrapper", "space", "swatchSize", "swatchGap", "borderControlPopoverControls", "StyledLabel", "borderControlPopoverContent", "borderColorIndicator", "resetButton", "borderSlider", "marginRight"], "sources": ["@wordpress/components/src/border-control/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { COLORS, CONFIG, boxSizingReset, rtl } from '../utils';\nimport { space } from '../utils/space';\nimport { StyledLabel } from '../base-control/styles/base-control-styles';\nimport {\n\tValueInput as UnitControlWrapper,\n\tUnitSelect,\n} from '../unit-control/styles/unit-control-styles';\n\nimport type { Border } from './types';\n\nconst labelStyles = css`\n\tfont-weight: 500;\n`;\n\nconst focusBoxShadow = css`\n\tbox-shadow: inset ${ CONFIG.controlBoxShadowFocus };\n`;\n\nexport const borderControl = css`\n\tborder: 0;\n\tpadding: 0;\n\tmargin: 0;\n\t${ boxSizingReset }\n`;\n\nexport const innerWrapper = () => css`\n\t${ UnitControlWrapper } {\n\t\tflex: 1 1 40%;\n\t}\n\t&& ${ UnitSelect } {\n\t\t/* Prevent unit select forcing min height larger than its UnitControl */\n\t\tmin-height: 0;\n\t}\n`;\n\n/*\n * This style is only applied to the UnitControl wrapper when the border width\n * field should be a set width. Omitting this allows the UnitControl &\n * RangeControl to share the available width in a 40/60 split respectively.\n */\nexport const wrapperWidth = css`\n\t${ UnitControlWrapper } {\n\t\t/* Force the UnitControl's set width. */\n\t\tflex: 0 0 auto;\n\t}\n`;\n\nexport const wrapperHeight = ( size?: 'default' | '__unstable-large' ) => {\n\treturn css`\n\t\theight: ${ size === '__unstable-large' ? '40px' : '30px' };\n\t`;\n};\n\nexport const borderControlDropdown = css`\n\tbackground: #fff;\n\n\t&& > button {\n\t\taspect-ratio: 1;\n\t\tpadding: 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\t${ rtl(\n\t\t\t{ borderRadius: `2px 0 0 2px` },\n\t\t\t{ borderRadius: `0 2px 2px 0` }\n\t\t)() }\n\t\tborder: ${ CONFIG.borderWidth } solid ${ COLORS.ui.border };\n\n\t\t&:focus,\n\t\t&:hover:not( :disabled ) {\n\t\t\t${ focusBoxShadow }\n\t\t\tborder-color: ${ COLORS.ui.borderFocus };\n\t\t\tz-index: 1;\n\t\t\tposition: relative;\n\t\t}\n\t}\n`;\n\nexport const colorIndicatorBorder = ( border?: Border ) => {\n\tconst { color, style } = border || {};\n\n\tconst fallbackColor =\n\t\t!! style && style !== 'none' ? COLORS.gray[ 300 ] : undefined;\n\n\treturn css`\n\t\tborder-style: ${ style === 'none' ? 'solid' : style };\n\t\tborder-color: ${ color || fallbackColor };\n\t`;\n};\n\nexport const colorIndicatorWrapper = (\n\tborder?: Border,\n\tsize?: 'default' | '__unstable-large'\n) => {\n\tconst { style } = border || {};\n\n\treturn css`\n\t\tborder-radius: 9999px;\n\t\tborder: 2px solid transparent;\n\t\t${ style ? colorIndicatorBorder( border ) : undefined }\n\t\twidth: ${ size === '__unstable-large' ? '24px' : '22px' };\n\t\theight: ${ size === '__unstable-large' ? '24px' : '22px' };\n\t\tpadding: ${ size === '__unstable-large' ? '2px' : '1px' };\n\n\t\t/*\n\t\t * ColorIndicator\n\t\t *\n\t\t * The transparent colors used here ensure visibility of the indicator\n\t\t * over the active state of the border control dropdown's toggle button.\n\t\t */\n\t\t& > span {\n\t\t\theight: ${ space( 4 ) };\n\t\t\twidth: ${ space( 4 ) };\n\t\t\tbackground: linear-gradient(\n\t\t\t\t-45deg,\n\t\t\t\ttransparent 48%,\n\t\t\t\trgb( 0 0 0 / 20% ) 48%,\n\t\t\t\trgb( 0 0 0 / 20% ) 52%,\n\t\t\t\ttransparent 52%\n\t\t\t);\n\t\t}\n\t`;\n};\n\n// Must equal $color-palette-circle-size from:\n// @wordpress/components/src/circular-option-picker/style.scss\nconst swatchSize = 28;\nconst swatchGap = 12;\n\nexport const borderControlPopoverControls = css`\n\twidth: ${ swatchSize * 6 + swatchGap * 5 }px;\n\n\t> div:first-of-type > ${ StyledLabel } {\n\t\tmargin-bottom: 0;\n\t\t${ labelStyles }\n\t}\n\n\t&& ${ StyledLabel } + button:not( .has-text ) {\n\t\tmin-width: 24px;\n\t\tpadding: 0;\n\t}\n`;\n\nexport const borderControlPopoverContent = css``;\nexport const borderColorIndicator = css``;\n\nexport const resetButton = css`\n\tjustify-content: center;\n\twidth: 100%;\n\n\t/* Override button component styling */\n\t&& {\n\t\tborder-top: ${ CONFIG.borderWidth } solid ${ COLORS.gray[ 400 ] };\n\t\tborder-top-left-radius: 0;\n\t\tborder-top-right-radius: 0;\n\t\theight: 40px;\n\t}\n`;\n\nexport const borderSlider = () => css`\n\tflex: 1 1 60%;\n\t${ rtl( { marginRight: space( 3 ) } )() }\n`;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,kBAAA,GAAAH,OAAA;AACA,IAAAI,kBAAA,GAAAJ,OAAA;AAGoD,SAAAK,iCAAA;AAIpD,MAAMC,WAAW,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAEhB;AAED,MAAMS,cAAc,oBAAGC,UAAG,uBACJC,aAAM,CAACC,qBAAqB,SAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA,oDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LACjD;AAEM,MAAMS,aAAa,oBAAGH,UAAG,kCAI5BI,qBAAc,SAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA,mDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LACjB;AAACW,OAAA,CAAAF,aAAA,GAAAA,aAAA;AAEK,MAAMG,YAAY,GAAGA,CAAA,sBAAMN,UAAG,EACjCO,6BAAkB,wBAGfC,6BAAU,uBAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA,kDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LAIhB;;AAED;AACA;AACA;AACA;AACA;AAJAW,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAKO,MAAMG,YAAY,oBAAGT,UAAG,EAC3BO,6BAAkB,wBAAAf,OAAA,CAAAC,GAAA,CAAAC,QAAA,kDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LAIrB;AAACW,OAAA,CAAAI,YAAA,GAAAA,YAAA;AAEK,MAAMC,aAAa,GAAKC,IAAqC,IAAM;EACzE,wBAAOX,UAAG,aACEW,IAAI,KAAK,kBAAkB,GAAG,MAAM,GAAG,MAAM,SAAAnB,OAAA,CAAAC,GAAA,CAAAC,QAAA,mDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAE1D,CAAC;AAACW,OAAA,CAAAK,aAAA,GAAAA,aAAA;AAEK,MAAME,qBAAqB,oBAAGZ,UAAG,gHASnC,IAAAa,UAAG,EACL;EAAEC,YAAY,EAAG;AAAa,CAAC,EAC/B;EAAEA,YAAY,EAAG;AAAa,CAC/B,CAAC,CAAC,CAAC,cACQb,aAAM,CAACc,WAAW,aAAYC,aAAM,CAACC,EAAE,CAACC,MAAM,wCAIrDnB,cAAc,oBACAiB,aAAM,CAACC,EAAE,CAACE,WAAW,uCAAA3B,OAAA,CAAAC,GAAA,CAAAC,QAAA,2DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LAKxC;AAACW,OAAA,CAAAO,qBAAA,GAAAA,qBAAA;AAEK,MAAMQ,oBAAoB,GAAKF,MAAe,IAAM;EAC1D,MAAM;IAAEG,KAAK;IAAEC;EAAM,CAAC,GAAGJ,MAAM,IAAI,CAAC,CAAC;EAErC,MAAMK,aAAa,GAClB,CAAC,CAAED,KAAK,IAAIA,KAAK,KAAK,MAAM,GAAGN,aAAM,CAACQ,IAAI,CAAE,GAAG,CAAE,GAAGC,SAAS;EAE9D,wBAAOzB,UAAG,mBACQsB,KAAK,KAAK,MAAM,GAAG,OAAO,GAAGA,KAAK,oBAClCD,KAAK,IAAIE,aAAa,SAAA/B,OAAA,CAAAC,GAAA,CAAAC,QAAA,0DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAEzC,CAAC;AAACW,OAAA,CAAAe,oBAAA,GAAAA,oBAAA;AAEK,MAAMM,qBAAqB,GAAGA,CACpCR,MAAe,EACfP,IAAqC,KACjC;EACJ,MAAM;IAAEW;EAAM,CAAC,GAAGJ,MAAM,IAAI,CAAC,CAAC;EAE9B,wBAAOlB,UAAG,wDAGNsB,KAAK,GAAGF,oBAAoB,CAAEF,MAAO,CAAC,GAAGO,SAAS,aAC3Cd,IAAI,KAAK,kBAAkB,GAAG,MAAM,GAAG,MAAM,cAC5CA,IAAI,KAAK,kBAAkB,GAAG,MAAM,GAAG,MAAM,eAC5CA,IAAI,KAAK,kBAAkB,GAAG,KAAK,GAAG,KAAK,qBAS3C,IAAAgB,YAAK,EAAE,CAAE,CAAC,aACX,IAAAA,YAAK,EAAE,CAAE,CAAC,qLAAAnC,OAAA,CAAAC,GAAA,CAAAC,QAAA,2DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAUvB,CAAC;;AAED;AACA;AAAAW,OAAA,CAAAqB,qBAAA,GAAAA,qBAAA;AACA,MAAME,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,GAAG,EAAE;AAEb,MAAMC,4BAA4B,oBAAG9B,UAAG,YACpC4B,UAAU,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC,4BAEfE,8BAAW,uBAEhCxC,WAAW,WAGTwC,8BAAW,2DAAAvC,OAAA,CAAAC,GAAA,CAAAC,QAAA,kEAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LAIjB;AAACW,OAAA,CAAAyB,4BAAA,GAAAA,4BAAA;AAEK,MAAME,2BAA2B,oBAAGhC,UAAG,EAAAR,OAAA,CAAAC,GAAA,CAAAC,QAAA,gEAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LAAE;AAACW,OAAA,CAAA2B,2BAAA,GAAAA,2BAAA;AAC1C,MAAMC,oBAAoB,oBAAGjC,UAAG,EAAAR,OAAA,CAAAC,GAAA,CAAAC,QAAA,yDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LAAE;AAACW,OAAA,CAAA4B,oBAAA,GAAAA,oBAAA;AAEnC,MAAMC,WAAW,oBAAGlC,UAAG,sDAMbC,aAAM,CAACc,WAAW,aAAYC,aAAM,CAACQ,IAAI,CAAE,GAAG,CAAE,yEAAAhC,OAAA,CAAAC,GAAA,CAAAC,QAAA,iDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LAKhE;AAACW,OAAA,CAAA6B,WAAA,GAAAA,WAAA;AAEK,MAAMC,YAAY,GAAGA,CAAA,sBAAMnC,UAAG,mBAEjC,IAAAa,UAAG,EAAE;EAAEuB,WAAW,EAAE,IAAAT,YAAK,EAAE,CAAE;AAAE,CAAE,CAAC,CAAC,CAAC,SAAAnC,OAAA,CAAAC,GAAA,CAAAC,QAAA,kDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,27LACvC;AAACW,OAAA,CAAA8B,YAAA,GAAAA,YAAA"}