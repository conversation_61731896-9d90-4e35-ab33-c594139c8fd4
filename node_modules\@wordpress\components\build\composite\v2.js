"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Composite", {
  enumerable: true,
  get: function () {
    return _react.Composite;
  }
});
Object.defineProperty(exports, "CompositeGroup", {
  enumerable: true,
  get: function () {
    return _react.CompositeGroup;
  }
});
Object.defineProperty(exports, "CompositeGroupLabel", {
  enumerable: true,
  get: function () {
    return _react.CompositeGroupLabel;
  }
});
Object.defineProperty(exports, "CompositeItem", {
  enumerable: true,
  get: function () {
    return _react.CompositeItem;
  }
});
Object.defineProperty(exports, "CompositeRow", {
  enumerable: true,
  get: function () {
    return _react.CompositeRow;
  }
});
Object.defineProperty(exports, "useCompositeStore", {
  enumerable: true,
  get: function () {
    return _react.useCompositeStore;
  }
});
var _react = require("@ariakit/react");
//# sourceMappingURL=v2.js.map