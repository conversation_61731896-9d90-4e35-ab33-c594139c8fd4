{"version": 3, "names": [], "sources": ["@wordpress/components/src/duotone-picker/color-list-picker/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\nexport type ColorListPickerProps = {\n\t/**\n\t * A list of predefined colors. Each color is an object with a `name` and a\n\t * `color` value.\n\t * The `name` is a string used to identify the color in the UI.\n\t * The `color` is a valid CSS color string.\n\t */\n\tcolors: Array< {\n\t\tname: string;\n\t\tcolor: NonNullable< CSSProperties[ 'color' ] >;\n\t} >;\n\t/**\n\t * A list of labels for each of the options displayed in the UI.\n\t */\n\tlabels: Array< string >;\n\t/**\n\t * An array containing the currently selected colors.\n\t */\n\tvalue?: Array< string >;\n\t/**\n\t * Controls whether the custom color picker is displayed.\n\t */\n\tdisableCustomColors?: boolean;\n\t/**\n\t * Controls whether the ColorPalette should show an alpha channel control.\n\t */\n\tenableAlpha?: boolean;\n\t/**\n\t * A function that receives the updated color value.\n\t */\n\tonChange: ( newValue: Array< string | undefined > ) => void;\n};\n\nexport type ColorOptionProps = Pick<\n\tColorListPickerProps,\n\t'colors' | 'disableCustomColors' | 'enableAlpha'\n> & {\n\tlabel: ColorListPickerProps[ 'labels' ][ number ];\n\tvalue: string | undefined;\n\tonChange: ( newValue: string | undefined ) => void;\n};\n"], "mappings": ""}