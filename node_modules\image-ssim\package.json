{"name": "image-ssim", "description": "Image structural similarity (SSIM). In TypeScript/JavaScript. For browser/server.", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/darosh"}, "bugs": "https://github.com/darosh/image-ssim-js/issues", "dependencies": {}, "devDependencies": {"browserify": "^10.2.4", "gulp": "^3.9.0", "gulp-gh-pages": "^0.5.2", "gulp-load-plugins": "^0.10.0", "gulp-rename": "^1.2.2", "gulp-sourcemaps": "^1.5.2", "gulp-tslint": "^2.0.0", "gulp-typedoc": "^1.1.0", "gulp-typescript": "^2.7.8", "gulp-uglify": "^1.2.0", "gulp-util": "^3.0.6", "merge2": "^0.3.6", "mocha": "^2.2.5", "pngjs": "^0.4.0", "run-sequence": "^1.1.1", "vinyl-buffer": "^1.0.0", "vinyl-source-stream": "^1.1.0"}, "homepage": "https://github.com/darosh/image-ssim-js/issues", "keywords": ["browser", "compare", "image", "server", "similarity", "ssim", "typescript"], "license": "MIT", "main": "index.js", "repository": "darosh/image-ssim-js", "scripts": {"test": "mocha"}}