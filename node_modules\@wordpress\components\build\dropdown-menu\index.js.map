{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_icons", "_context", "_button", "_dropdown", "_navigableContainer", "mergeProps", "defaultProps", "props", "mergedProps", "className", "classnames", "isFunction", "maybeFunc", "UnconnectedDropdownMenu", "dropdownMenuProps", "children", "controls", "icon", "menu", "label", "popoverProps", "toggleProps", "menuProps", "disableOpenOnArrowDown", "text", "noIcons", "open", "defaultOpen", "onToggle", "onToggleProp", "variant", "useContextSystem", "length", "controlSets", "Array", "isArray", "mergedPopoverProps", "_react", "createElement", "default", "renderToggle", "isOpen", "_toggleProps$showTool", "openOnArrowDown", "event", "code", "preventDefault", "as", "Toggle", "<PERSON><PERSON>", "restToggleProps", "mergedToggleProps", "onClick", "onKeyDown", "showTooltip", "renderContent", "mergedMenuProps", "NavigableMenu", "role", "flatMap", "controlSet", "indexOfSet", "map", "control", "indexOfControl", "key", "join", "stopPropagation", "onClose", "isActive", "title", "undefined", "disabled", "isDisabled", "DropdownMenu", "contextConnectWithoutRef", "exports", "_default"], "sources": ["@wordpress/components/src/dropdown-menu/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { menu } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport { contextConnectWithoutRef, useContextSystem } from '../context';\nimport Button from '../button';\nimport Dropdown from '../dropdown';\nimport { NavigableMenu } from '../navigable-container';\nimport type {\n\tDropdownMenuProps,\n\tDropdownOption,\n\tDropdownMenuInternalContext,\n} from './types';\n\nfunction mergeProps<\n\tT extends { className?: string; [ key: string ]: unknown },\n>( defaultProps: Partial< T > = {}, props: T = {} as T ) {\n\tconst mergedProps: T = {\n\t\t...defaultProps,\n\t\t...props,\n\t};\n\n\tif ( props.className && defaultProps.className ) {\n\t\tmergedProps.className = classnames(\n\t\t\tprops.className,\n\t\t\tdefaultProps.className\n\t\t);\n\t}\n\n\treturn mergedProps;\n}\n\nfunction isFunction( maybeFunc: unknown ): maybeFunc is () => void {\n\treturn typeof maybeFunc === 'function';\n}\n\nfunction UnconnectedDropdownMenu( dropdownMenuProps: DropdownMenuProps ) {\n\tconst {\n\t\tchildren,\n\t\tclassName,\n\t\tcontrols,\n\t\ticon = menu,\n\t\tlabel,\n\t\tpopoverProps,\n\t\ttoggleProps,\n\t\tmenuProps,\n\t\tdisableOpenOnArrowDown = false,\n\t\ttext,\n\t\tnoIcons,\n\n\t\topen,\n\t\tdefaultOpen,\n\t\tonToggle: onToggleProp,\n\n\t\t// Context\n\t\tvariant,\n\t} = useContextSystem< DropdownMenuProps & DropdownMenuInternalContext >(\n\t\tdropdownMenuProps,\n\t\t'DropdownMenu'\n\t);\n\n\tif ( ! controls?.length && ! isFunction( children ) ) {\n\t\treturn null;\n\t}\n\n\t// Normalize controls to nested array of objects (sets of controls)\n\tlet controlSets: DropdownOption[][];\n\tif ( controls?.length ) {\n\t\t// @ts-expect-error The check below is needed because `DropdownMenus`\n\t\t// rendered by `ToolBarGroup` receive controls as a nested array.\n\t\tcontrolSets = controls;\n\t\tif ( ! Array.isArray( controlSets[ 0 ] ) ) {\n\t\t\t// This is not ideal, but at this point we know that `controls` is\n\t\t\t// not a nested array, even if TypeScript doesn't.\n\t\t\tcontrolSets = [ controls as DropdownOption[] ];\n\t\t}\n\t}\n\n\tconst mergedPopoverProps = mergeProps(\n\t\t{\n\t\t\tclassName: 'components-dropdown-menu__popover',\n\t\t\tvariant,\n\t\t},\n\t\tpopoverProps\n\t);\n\n\treturn (\n\t\t<Dropdown\n\t\t\tclassName={ className }\n\t\t\tpopoverProps={ mergedPopoverProps }\n\t\t\trenderToggle={ ( { isOpen, onToggle } ) => {\n\t\t\t\tconst openOnArrowDown = ( event: React.KeyboardEvent ) => {\n\t\t\t\t\tif ( disableOpenOnArrowDown ) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( ! isOpen && event.code === 'ArrowDown' ) {\n\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t\tonToggle();\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tconst { as: Toggle = Button, ...restToggleProps } =\n\t\t\t\t\ttoggleProps ?? {};\n\n\t\t\t\tconst mergedToggleProps = mergeProps(\n\t\t\t\t\t{\n\t\t\t\t\t\tclassName: classnames(\n\t\t\t\t\t\t\t'components-dropdown-menu__toggle',\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t'is-opened': isOpen,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t),\n\t\t\t\t\t},\n\t\t\t\t\trestToggleProps\n\t\t\t\t);\n\n\t\t\t\treturn (\n\t\t\t\t\t<Toggle\n\t\t\t\t\t\t{ ...mergedToggleProps }\n\t\t\t\t\t\ticon={ icon }\n\t\t\t\t\t\tonClick={\n\t\t\t\t\t\t\t( ( event ) => {\n\t\t\t\t\t\t\t\tonToggle();\n\t\t\t\t\t\t\t\tif ( mergedToggleProps.onClick ) {\n\t\t\t\t\t\t\t\t\tmergedToggleProps.onClick( event );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} ) as React.MouseEventHandler< HTMLButtonElement >\n\t\t\t\t\t\t}\n\t\t\t\t\t\tonKeyDown={\n\t\t\t\t\t\t\t( ( event ) => {\n\t\t\t\t\t\t\t\topenOnArrowDown( event );\n\t\t\t\t\t\t\t\tif ( mergedToggleProps.onKeyDown ) {\n\t\t\t\t\t\t\t\t\tmergedToggleProps.onKeyDown( event );\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} ) as React.KeyboardEventHandler< HTMLButtonElement >\n\t\t\t\t\t\t}\n\t\t\t\t\t\taria-haspopup=\"true\"\n\t\t\t\t\t\taria-expanded={ isOpen }\n\t\t\t\t\t\tlabel={ label }\n\t\t\t\t\t\ttext={ text }\n\t\t\t\t\t\tshowTooltip={ toggleProps?.showTooltip ?? true }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ mergedToggleProps.children }\n\t\t\t\t\t</Toggle>\n\t\t\t\t);\n\t\t\t} }\n\t\t\trenderContent={ ( props ) => {\n\t\t\t\tconst mergedMenuProps = mergeProps(\n\t\t\t\t\t{\n\t\t\t\t\t\t'aria-label': label,\n\t\t\t\t\t\tclassName: classnames(\n\t\t\t\t\t\t\t'components-dropdown-menu__menu',\n\t\t\t\t\t\t\t{ 'no-icons': noIcons }\n\t\t\t\t\t\t),\n\t\t\t\t\t},\n\t\t\t\t\tmenuProps\n\t\t\t\t);\n\n\t\t\t\treturn (\n\t\t\t\t\t<NavigableMenu { ...mergedMenuProps } role=\"menu\">\n\t\t\t\t\t\t{ isFunction( children ) ? children( props ) : null }\n\t\t\t\t\t\t{ controlSets?.flatMap( ( controlSet, indexOfSet ) =>\n\t\t\t\t\t\t\tcontrolSet.map( ( control, indexOfControl ) => (\n\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\tkey={ [\n\t\t\t\t\t\t\t\t\t\tindexOfSet,\n\t\t\t\t\t\t\t\t\t\tindexOfControl,\n\t\t\t\t\t\t\t\t\t].join() }\n\t\t\t\t\t\t\t\t\tonClick={ ( event ) => {\n\t\t\t\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t\t\t\t\tprops.onClose();\n\t\t\t\t\t\t\t\t\t\tif ( control.onClick ) {\n\t\t\t\t\t\t\t\t\t\t\tcontrol.onClick();\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\tclassName={ classnames(\n\t\t\t\t\t\t\t\t\t\t'components-dropdown-menu__menu-item',\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t'has-separator':\n\t\t\t\t\t\t\t\t\t\t\t\tindexOfSet > 0 &&\n\t\t\t\t\t\t\t\t\t\t\t\tindexOfControl === 0,\n\t\t\t\t\t\t\t\t\t\t\t'is-active': control.isActive,\n\t\t\t\t\t\t\t\t\t\t\t'is-icon-only': ! control.title,\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\ticon={ control.icon }\n\t\t\t\t\t\t\t\t\tlabel={ control.label }\n\t\t\t\t\t\t\t\t\taria-checked={\n\t\t\t\t\t\t\t\t\t\tcontrol.role === 'menuitemcheckbox' ||\n\t\t\t\t\t\t\t\t\t\tcontrol.role === 'menuitemradio'\n\t\t\t\t\t\t\t\t\t\t\t? control.isActive\n\t\t\t\t\t\t\t\t\t\t\t: undefined\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\trole={\n\t\t\t\t\t\t\t\t\t\tcontrol.role === 'menuitemcheckbox' ||\n\t\t\t\t\t\t\t\t\t\tcontrol.role === 'menuitemradio'\n\t\t\t\t\t\t\t\t\t\t\t? control.role\n\t\t\t\t\t\t\t\t\t\t\t: 'menuitem'\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tdisabled={ control.isDisabled }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ control.title }\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t) )\n\t\t\t\t\t\t) }\n\t\t\t\t\t</NavigableMenu>\n\t\t\t\t);\n\t\t\t} }\n\t\t\topen={ open }\n\t\t\tdefaultOpen={ defaultOpen }\n\t\t\tonToggle={ onToggleProp }\n\t\t/>\n\t);\n}\n\n/**\n *\n * The DropdownMenu displays a list of actions (each contained in a MenuItem,\n * MenuItemsChoice, or MenuGroup) in a compact way. It appears in a Popover\n * after the user has interacted with an element (a button or icon) or when\n * they perform a specific action.\n *\n * Render a Dropdown Menu with a set of controls:\n *\n * ```jsx\n * import { DropdownMenu } from '@wordpress/components';\n * import {\n * \tmore,\n * \tarrowLeft,\n * \tarrowRight,\n * \tarrowUp,\n * \tarrowDown,\n * } from '@wordpress/icons';\n *\n * const MyDropdownMenu = () => (\n * \t<DropdownMenu\n * \t\ticon={ more }\n * \t\tlabel=\"Select a direction\"\n * \t\tcontrols={ [\n * \t\t\t{\n * \t\t\t\ttitle: 'Up',\n * \t\t\t\ticon: arrowUp,\n * \t\t\t\tonClick: () => console.log( 'up' ),\n * \t\t\t},\n * \t\t\t{\n * \t\t\t\ttitle: 'Right',\n * \t\t\t\ticon: arrowRight,\n * \t\t\t\tonClick: () => console.log( 'right' ),\n * \t\t\t},\n * \t\t\t{\n * \t\t\t\ttitle: 'Down',\n * \t\t\t\ticon: arrowDown,\n * \t\t\t\tonClick: () => console.log( 'down' ),\n * \t\t\t},\n * \t\t\t{\n * \t\t\t\ttitle: 'Left',\n * \t\t\t\ticon: arrowLeft,\n * \t\t\t\tonClick: () => console.log( 'left' ),\n * \t\t\t},\n * \t\t] }\n * \t/>\n * );\n * ```\n *\n * Alternatively, specify a `children` function which returns elements valid for\n * use in a DropdownMenu: `MenuItem`, `MenuItemsChoice`, or `MenuGroup`.\n *\n * ```jsx\n * import { DropdownMenu, MenuGroup, MenuItem } from '@wordpress/components';\n * import { more, arrowUp, arrowDown, trash } from '@wordpress/icons';\n *\n * const MyDropdownMenu = () => (\n * \t<DropdownMenu icon={ more } label=\"Select a direction\">\n * \t\t{ ( { onClose } ) => (\n * \t\t\t<>\n * \t\t\t\t<MenuGroup>\n * \t\t\t\t\t<MenuItem icon={ arrowUp } onClick={ onClose }>\n * \t\t\t\t\t\tMove Up\n * \t\t\t\t\t</MenuItem>\n * \t\t\t\t\t<MenuItem icon={ arrowDown } onClick={ onClose }>\n * \t\t\t\t\t\tMove Down\n * \t\t\t\t\t</MenuItem>\n * \t\t\t\t</MenuGroup>\n * \t\t\t\t<MenuGroup>\n * \t\t\t\t\t<MenuItem icon={ trash } onClick={ onClose }>\n * \t\t\t\t\t\tRemove\n * \t\t\t\t\t</MenuItem>\n * \t\t\t\t</MenuGroup>\n * \t\t\t</>\n * \t\t) }\n * \t</DropdownMenu>\n * );\n * ```\n *\n */\nexport const DropdownMenu = contextConnectWithoutRef(\n\tUnconnectedDropdownMenu,\n\t'DropdownMenu'\n);\n\nexport default DropdownMenu;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,mBAAA,GAAAL,OAAA;AAhBA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAWA,SAASM,UAAUA,CAEhBC,YAA0B,GAAG,CAAC,CAAC,EAAEC,KAAQ,GAAG,CAAC,CAAM,EAAG;EACxD,MAAMC,WAAc,GAAG;IACtB,GAAGF,YAAY;IACf,GAAGC;EACJ,CAAC;EAED,IAAKA,KAAK,CAACE,SAAS,IAAIH,YAAY,CAACG,SAAS,EAAG;IAChDD,WAAW,CAACC,SAAS,GAAG,IAAAC,mBAAU,EACjCH,KAAK,CAACE,SAAS,EACfH,YAAY,CAACG,SACd,CAAC;EACF;EAEA,OAAOD,WAAW;AACnB;AAEA,SAASG,UAAUA,CAAEC,SAAkB,EAA4B;EAClE,OAAO,OAAOA,SAAS,KAAK,UAAU;AACvC;AAEA,SAASC,uBAAuBA,CAAEC,iBAAoC,EAAG;EACxE,MAAM;IACLC,QAAQ;IACRN,SAAS;IACTO,QAAQ;IACRC,IAAI,GAAGC,WAAI;IACXC,KAAK;IACLC,YAAY;IACZC,WAAW;IACXC,SAAS;IACTC,sBAAsB,GAAG,KAAK;IAC9BC,IAAI;IACJC,OAAO;IAEPC,IAAI;IACJC,WAAW;IACXC,QAAQ,EAAEC,YAAY;IAEtB;IACAC;EACD,CAAC,GAAG,IAAAC,yBAAgB,EACnBjB,iBAAiB,EACjB,cACD,CAAC;EAED,IAAK,CAAEE,QAAQ,EAAEgB,MAAM,IAAI,CAAErB,UAAU,CAAEI,QAAS,CAAC,EAAG;IACrD,OAAO,IAAI;EACZ;;EAEA;EACA,IAAIkB,WAA+B;EACnC,IAAKjB,QAAQ,EAAEgB,MAAM,EAAG;IACvB;IACA;IACAC,WAAW,GAAGjB,QAAQ;IACtB,IAAK,CAAEkB,KAAK,CAACC,OAAO,CAAEF,WAAW,CAAE,CAAC,CAAG,CAAC,EAAG;MAC1C;MACA;MACAA,WAAW,GAAG,CAAEjB,QAAQ,CAAsB;IAC/C;EACD;EAEA,MAAMoB,kBAAkB,GAAG/B,UAAU,CACpC;IACCI,SAAS,EAAE,mCAAmC;IAC9CqB;EACD,CAAC,EACDV,YACD,CAAC;EAED,OACC,IAAAiB,MAAA,CAAAC,aAAA,EAACnC,SAAA,CAAAoC,OAAQ;IACR9B,SAAS,EAAGA,SAAW;IACvBW,YAAY,EAAGgB,kBAAoB;IACnCI,YAAY,EAAGA,CAAE;MAAEC,MAAM;MAAEb;IAAS,CAAC,KAAM;MAAA,IAAAc,qBAAA;MAC1C,MAAMC,eAAe,GAAKC,KAA0B,IAAM;QACzD,IAAKrB,sBAAsB,EAAG;UAC7B;QACD;QAEA,IAAK,CAAEkB,MAAM,IAAIG,KAAK,CAACC,IAAI,KAAK,WAAW,EAAG;UAC7CD,KAAK,CAACE,cAAc,CAAC,CAAC;UACtBlB,QAAQ,CAAC,CAAC;QACX;MACD,CAAC;MACD,MAAM;QAAEmB,EAAE,EAAEC,MAAM,GAAGC,eAAM;QAAE,GAAGC;MAAgB,CAAC,GAChD7B,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,CAAC,CAAC;MAElB,MAAM8B,iBAAiB,GAAG9C,UAAU,CACnC;QACCI,SAAS,EAAE,IAAAC,mBAAU,EACpB,kCAAkC,EAClC;UACC,WAAW,EAAE+B;QACd,CACD;MACD,CAAC,EACDS,eACD,CAAC;MAED,OACC,IAAAb,MAAA,CAAAC,aAAA,EAACU,MAAM;QAAA,GACDG,iBAAiB;QACtBlC,IAAI,EAAGA,IAAM;QACbmC,OAAO,EACFR,KAAK,IAAM;UACdhB,QAAQ,CAAC,CAAC;UACV,IAAKuB,iBAAiB,CAACC,OAAO,EAAG;YAChCD,iBAAiB,CAACC,OAAO,CAAER,KAAM,CAAC;UACnC;QACD,CACA;QACDS,SAAS,EACJT,KAAK,IAAM;UACdD,eAAe,CAAEC,KAAM,CAAC;UACxB,IAAKO,iBAAiB,CAACE,SAAS,EAAG;YAClCF,iBAAiB,CAACE,SAAS,CAAET,KAAM,CAAC;UACrC;QACD,CACA;QACD,iBAAc,MAAM;QACpB,iBAAgBH,MAAQ;QACxBtB,KAAK,EAAGA,KAAO;QACfK,IAAI,EAAGA,IAAM;QACb8B,WAAW,GAAAZ,qBAAA,GAAGrB,WAAW,EAAEiC,WAAW,cAAAZ,qBAAA,cAAAA,qBAAA,GAAI;MAAM,GAE9CS,iBAAiB,CAACpC,QACb,CAAC;IAEX,CAAG;IACHwC,aAAa,EAAKhD,KAAK,IAAM;MAC5B,MAAMiD,eAAe,GAAGnD,UAAU,CACjC;QACC,YAAY,EAAEc,KAAK;QACnBV,SAAS,EAAE,IAAAC,mBAAU,EACpB,gCAAgC,EAChC;UAAE,UAAU,EAAEe;QAAQ,CACvB;MACD,CAAC,EACDH,SACD,CAAC;MAED,OACC,IAAAe,MAAA,CAAAC,aAAA,EAAClC,mBAAA,CAAAqD,aAAa;QAAA,GAAMD,eAAe;QAAGE,IAAI,EAAC;MAAM,GAC9C/C,UAAU,CAAEI,QAAS,CAAC,GAAGA,QAAQ,CAAER,KAAM,CAAC,GAAG,IAAI,EACjD0B,WAAW,EAAE0B,OAAO,CAAE,CAAEC,UAAU,EAAEC,UAAU,KAC/CD,UAAU,CAACE,GAAG,CAAE,CAAEC,OAAO,EAAEC,cAAc,KACxC,IAAA3B,MAAA,CAAAC,aAAA,EAACpC,OAAA,CAAAqC,OAAM;QACN0B,GAAG,EAAG,CACLJ,UAAU,EACVG,cAAc,CACd,CAACE,IAAI,CAAC,CAAG;QACVd,OAAO,EAAKR,KAAK,IAAM;UACtBA,KAAK,CAACuB,eAAe,CAAC,CAAC;UACvB5D,KAAK,CAAC6D,OAAO,CAAC,CAAC;UACf,IAAKL,OAAO,CAACX,OAAO,EAAG;YACtBW,OAAO,CAACX,OAAO,CAAC,CAAC;UAClB;QACD,CAAG;QACH3C,SAAS,EAAG,IAAAC,mBAAU,EACrB,qCAAqC,EACrC;UACC,eAAe,EACdmD,UAAU,GAAG,CAAC,IACdG,cAAc,KAAK,CAAC;UACrB,WAAW,EAAED,OAAO,CAACM,QAAQ;UAC7B,cAAc,EAAE,CAAEN,OAAO,CAACO;QAC3B,CACD,CAAG;QACHrD,IAAI,EAAG8C,OAAO,CAAC9C,IAAM;QACrBE,KAAK,EAAG4C,OAAO,CAAC5C,KAAO;QACvB,gBACC4C,OAAO,CAACL,IAAI,KAAK,kBAAkB,IACnCK,OAAO,CAACL,IAAI,KAAK,eAAe,GAC7BK,OAAO,CAACM,QAAQ,GAChBE,SACH;QACDb,IAAI,EACHK,OAAO,CAACL,IAAI,KAAK,kBAAkB,IACnCK,OAAO,CAACL,IAAI,KAAK,eAAe,GAC7BK,OAAO,CAACL,IAAI,GACZ,UACH;QACDc,QAAQ,EAAGT,OAAO,CAACU;MAAY,GAE7BV,OAAO,CAACO,KACH,CACP,CACH,CACc,CAAC;IAElB,CAAG;IACH5C,IAAI,EAAGA,IAAM;IACbC,WAAW,EAAGA,WAAa;IAC3BC,QAAQ,EAAGC;EAAc,CACzB,CAAC;AAEJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM6C,YAAY,GAAG,IAAAC,iCAAwB,EACnD9D,uBAAuB,EACvB,cACD,CAAC;AAAC+D,OAAA,CAAAF,YAAA,GAAAA,YAAA;AAAA,IAAAG,QAAA,GAEaH,YAAY;AAAAE,OAAA,CAAArC,OAAA,GAAAsC,QAAA"}