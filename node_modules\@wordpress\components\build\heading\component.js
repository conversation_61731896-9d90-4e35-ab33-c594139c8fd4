"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.Heading = void 0;
var _react = require("react");
var _context = require("../context");
var _view = require("../view");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedHeading(props, forwardedRef) {
  const headerProps = (0, _hook.useHeading)(props);
  return (0, _react.createElement)(_view.View, {
    ...headerProps,
    ref: forwardedRef
  });
}

/**
 * `Heading` renders headings and titles using the library's typography system.
 *
 * ```jsx
 * import { __experimentalHeading as Heading } from "@wordpress/components";
 *
 * function Example() {
 *   return <Heading>Code is Poetry</Heading>;
 * }
 * ```
 */
const Heading = (0, _context.contextConnect)(UnconnectedHeading, 'Heading');
exports.Heading = Heading;
var _default = Heading;
exports.default = _default;
//# sourceMappingURL=component.js.map