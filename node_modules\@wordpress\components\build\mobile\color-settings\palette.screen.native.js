"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _components = require("@wordpress/components");
var _native = require("@react-navigation/native");
var _colorPalette = _interopRequireDefault(require("../../color-palette"));
var _colorIndicator = _interopRequireDefault(require("../../color-indicator"));
var _navBar = _interopRequireDefault(require("../bottom-sheet/nav-bar"));
var _segmentedControl = _interopRequireDefault(require("../segmented-control"));
var _utils = require("./utils");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const HIT_SLOP = {
  top: 8,
  bottom: 8,
  left: 8,
  right: 8
};
const PaletteScreen = () => {
  const route = (0, _native.useRoute)();
  const navigation = (0, _native.useNavigation)();
  const {
    shouldEnableBottomSheetScroll
  } = (0, _element.useContext)(_components.BottomSheetContext);
  const {
    label,
    onColorChange,
    onGradientChange,
    onColorCleared,
    colorValue,
    defaultSettings,
    hideNavigation = false
  } = route.params || {};
  const {
    segments,
    isGradient
  } = _utils.colorsUtils;
  const [currentValue, setCurrentValue] = (0, _element.useState)(colorValue);
  const isGradientColor = isGradient(currentValue);
  const selectedSegmentIndex = isGradientColor ? 1 : 0;
  const [currentSegment, setCurrentSegment] = (0, _element.useState)(segments[selectedSegmentIndex]);
  const isGradientSegment = currentSegment === _utils.colorsUtils.segments[1];
  const currentSegmentColors = !isGradientSegment ? defaultSettings.colors : defaultSettings.gradients;
  const allAvailableColors = (0, _components.useMobileGlobalStylesColors)();
  const allAvailableGradients = currentSegmentColors.flatMap(({
    gradients
  }) => gradients).filter(Boolean);
  const horizontalSeparatorStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.horizontalSeparator, _style.default.horizontalSeparatorDark);
  const clearButtonStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.clearButton, _style.default.clearButtonDark);
  const selectedColorTextStyle = (0, _compose.usePreferredColorSchemeStyle)(_style.default.colorText, _style.default.colorTextDark);
  const isSolidSegment = currentSegment === segments[0];
  const isCustomGadientShown = !isSolidSegment && isGradientColor;
  const setColor = color => {
    setCurrentValue(color);
    if (isSolidSegment && onColorChange && onGradientChange) {
      onColorChange(color);
    } else if (isSolidSegment && onColorChange) {
      onColorChange(color);
    } else if (!isSolidSegment && onGradientChange) {
      onGradientChange(color);
    }
  };
  function onClear() {
    setCurrentValue(undefined);
    if (onColorCleared) {
      onColorCleared();
    }
  }
  function onCustomPress() {
    if (isSolidSegment) {
      navigation.navigate(_utils.colorsUtils.screens.picker, {
        currentValue,
        setColor
      });
    } else {
      navigation.navigate(_utils.colorsUtils.screens.gradientPicker, {
        setColor,
        isGradientColor,
        currentValue
      });
    }
  }
  function getClearButton() {
    return (0, _react.createElement)(_reactNative.TouchableWithoutFeedback, {
      onPress: onClear,
      hitSlop: HIT_SLOP
    }, (0, _react.createElement)(_reactNative.View, {
      style: _style.default.clearButtonContainer
    }, (0, _react.createElement)(_reactNative.Text, {
      style: clearButtonStyle
    }, (0, _i18n.__)('Reset'))));
  }
  function getFooter() {
    if (onGradientChange) {
      return (0, _react.createElement)(_segmentedControl.default, {
        segments: segments,
        segmentHandler: setCurrentSegment,
        selectedIndex: segments.indexOf(currentSegment),
        addonLeft: currentValue && (0, _react.createElement)(_colorIndicator.default, {
          color: currentValue,
          style: _style.default.colorIndicator
        }),
        addonRight: currentValue && getClearButton()
      });
    }
    return (0, _react.createElement)(_reactNative.View, {
      style: _style.default.footer
    }, (0, _react.createElement)(_reactNative.View, {
      style: _style.default.flex
    }, currentValue && (0, _react.createElement)(_colorIndicator.default, {
      color: currentValue,
      style: _style.default.colorIndicator
    })), currentValue ? (0, _react.createElement)(_reactNative.Text, {
      style: selectedColorTextStyle,
      maxFontSizeMultiplier: 2,
      selectable: true
    }, currentValue.toUpperCase()) : (0, _react.createElement)(_reactNative.Text, {
      style: _style.default.selectColorText,
      maxFontSizeMultiplier: 2
    }, (0, _i18n.__)('Select a color above')), (0, _react.createElement)(_reactNative.View, {
      style: _style.default.flex
    }, currentValue && getClearButton()));
  }
  return (0, _react.createElement)(_reactNative.View, null, !hideNavigation && (0, _react.createElement)(_navBar.default, null, (0, _react.createElement)(_navBar.default.BackButton, {
    onPress: navigation.goBack
  }), (0, _react.createElement)(_navBar.default.Heading, null, label, " ")), (0, _react.createElement)(_reactNative.View, {
    style: _style.default.colorPalettes
  }, currentSegmentColors.map((palette, paletteKey) => {
    const paletteSettings = {
      colors: palette.colors,
      gradients: palette.gradients,
      allColors: allAvailableColors,
      allGradients: allAvailableGradients
    };
    // Limit to show the custom indicator to the first available palette
    const enableCustomColor = paletteKey === 0;
    return (0, _react.createElement)(_colorPalette.default, {
      enableCustomColor: enableCustomColor,
      label: palette.name,
      key: paletteKey,
      setColor: setColor,
      activeColor: currentValue,
      isGradientColor: isGradientColor,
      currentSegment: currentSegment,
      onCustomPress: onCustomPress,
      shouldEnableBottomSheetScroll: shouldEnableBottomSheetScroll,
      defaultSettings: paletteSettings
    });
  })), isCustomGadientShown && (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_reactNative.View, {
    style: horizontalSeparatorStyle
  }), (0, _react.createElement)(_components.PanelBody, null, (0, _react.createElement)(_components.ColorControl, {
    label: (0, _i18n.__)('Customize Gradient'),
    onPress: onCustomPress,
    withColorIndicator: false
  }))), (0, _react.createElement)(_reactNative.View, {
    style: horizontalSeparatorStyle
  }), getFooter());
};
var _default = PaletteScreen;
exports.default = _default;
//# sourceMappingURL=palette.screen.native.js.map