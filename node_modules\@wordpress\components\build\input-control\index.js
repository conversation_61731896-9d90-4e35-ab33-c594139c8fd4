"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.InputControl = void 0;
exports.UnforwardedInputControl = UnforwardedInputControl;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _compose = require("@wordpress/compose");
var _element = require("@wordpress/element");
var _inputBase = _interopRequireDefault(require("./input-base"));
var _inputField = _interopRequireDefault(require("./input-field"));
var _space = require("../utils/space");
var _utils = require("./utils");
var _baseControl = _interopRequireDefault(require("../base-control"));
var _useDeprecatedProps = require("../utils/use-deprecated-props");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const noop = () => {};
function useUniqueId(idProp) {
  const instanceId = (0, _compose.useInstanceId)(InputControl);
  const id = `inspector-input-control-${instanceId}`;
  return idProp || id;
}
function UnforwardedInputControl(props, ref) {
  const {
    __next40pxDefaultSize,
    __unstableStateReducer: stateReducer = state => state,
    __unstableInputWidth,
    className,
    disabled = false,
    help,
    hideLabelFromVision = false,
    id: idProp,
    isPressEnterToChange = false,
    label,
    labelPosition = 'top',
    onChange = noop,
    onValidate = noop,
    onKeyDown = noop,
    prefix,
    size = 'default',
    style,
    suffix,
    value,
    ...restProps
  } = (0, _useDeprecatedProps.useDeprecated36pxDefaultSizeProp)(props, 'wp.components.InputControl', '6.4');
  const [isFocused, setIsFocused] = (0, _element.useState)(false);
  const id = useUniqueId(idProp);
  const classes = (0, _classnames.default)('components-input-control', className);
  const draftHookProps = (0, _utils.useDraft)({
    value,
    onBlur: restProps.onBlur,
    onChange
  });

  // ARIA descriptions can only contain plain text, so fall back to aria-details if not.
  const helpPropName = typeof help === 'string' ? 'aria-describedby' : 'aria-details';
  const helpProp = !!help ? {
    [helpPropName]: `${id}__help`
  } : {};
  return (0, _react.createElement)(_baseControl.default, {
    className: classes,
    help: help,
    id: id,
    __nextHasNoMarginBottom: true
  }, (0, _react.createElement)(_inputBase.default, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    __unstableInputWidth: __unstableInputWidth,
    disabled: disabled,
    gap: 3,
    hideLabelFromVision: hideLabelFromVision,
    id: id,
    isFocused: isFocused,
    justify: "left",
    label: label,
    labelPosition: labelPosition,
    prefix: prefix,
    size: size,
    style: style,
    suffix: suffix
  }, (0, _react.createElement)(_inputField.default, {
    ...restProps,
    ...helpProp,
    __next40pxDefaultSize: __next40pxDefaultSize,
    className: "components-input-control__input",
    disabled: disabled,
    id: id,
    isFocused: isFocused,
    isPressEnterToChange: isPressEnterToChange,
    onKeyDown: onKeyDown,
    onValidate: onValidate,
    paddingInlineStart: prefix ? (0, _space.space)(2) : undefined,
    paddingInlineEnd: suffix ? (0, _space.space)(2) : undefined,
    ref: ref,
    setIsFocused: setIsFocused,
    size: size,
    stateReducer: stateReducer,
    ...draftHookProps
  })));
}

/**
 * InputControl components let users enter and edit text. This is an experimental component
 * intended to (in time) merge with or replace `TextControl`.
 *
 * ```jsx
 * import { __experimentalInputControl as InputControl } from '@wordpress/components';
 * import { useState } from '@wordpress/compose';
 *
 * const Example = () => {
 *   const [ value, setValue ] = useState( '' );
 *
 *   return (
 *  	<InputControl
 *  		value={ value }
 *  		onChange={ ( nextValue ) => setValue( nextValue ?? '' ) }
 *  	/>
 *   );
 * };
 * ```
 */
const InputControl = (0, _element.forwardRef)(UnforwardedInputControl);
exports.InputControl = InputControl;
var _default = InputControl;
exports.default = _default;
//# sourceMappingURL=index.js.map