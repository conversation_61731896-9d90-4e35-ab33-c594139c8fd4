{"version": 3, "names": ["serializeGradientColor", "type", "value", "join", "serializeGradientPosition", "position", "serializeGradientColorStop", "length", "serializeGradientOrientation", "orientation", "Array", "isArray", "serializeGradient", "colorStops", "serializedOrientation", "serializedColorStops", "sort", "colorStop1", "colorStop2", "getNumericStopValue", "colorStop", "undefined", "parseInt", "map", "filter", "Boolean"], "sources": ["@wordpress/components/src/custom-gradient-picker/serializer.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type gradientParser from 'gradient-parser';\n\nexport function serializeGradientColor( {\n\ttype,\n\tvalue,\n}: gradientParser.ColorStop ) {\n\tif ( type === 'literal' ) {\n\t\treturn value;\n\t}\n\tif ( type === 'hex' ) {\n\t\treturn `#${ value }`;\n\t}\n\treturn `${ type }(${ value.join( ',' ) })`;\n}\n\nexport function serializeGradientPosition(\n\tposition: gradientParser.ColorStop[ 'length' ]\n) {\n\tif ( ! position ) {\n\t\treturn '';\n\t}\n\tconst { value, type } = position;\n\treturn `${ value }${ type }`;\n}\n\nexport function serializeGradientColorStop( {\n\ttype,\n\tvalue,\n\tlength,\n}: gradientParser.ColorStop ) {\n\treturn `${ serializeGradientColor( {\n\t\ttype,\n\t\tvalue,\n\t} as gradientParser.ColorStop ) } ${ serializeGradientPosition( length ) }`;\n}\n\nexport function serializeGradientOrientation(\n\torientation: gradientParser.GradientNode[ 'orientation' ]\n) {\n\tif (\n\t\tArray.isArray( orientation ) ||\n\t\t! orientation ||\n\t\torientation.type !== 'angular'\n\t) {\n\t\treturn;\n\t}\n\treturn `${ orientation.value }deg`;\n}\n\nexport function serializeGradient( {\n\ttype,\n\torientation,\n\tcolorStops,\n}: gradientParser.GradientNode ) {\n\tconst serializedOrientation = serializeGradientOrientation( orientation );\n\tconst serializedColorStops = colorStops\n\t\t.sort( ( colorStop1, colorStop2 ) => {\n\t\t\tconst getNumericStopValue = (\n\t\t\t\tcolorStop: gradientParser.ColorStop\n\t\t\t) => {\n\t\t\t\treturn colorStop?.length?.value === undefined\n\t\t\t\t\t? 0\n\t\t\t\t\t: parseInt( colorStop.length.value );\n\t\t\t};\n\n\t\t\treturn (\n\t\t\t\tgetNumericStopValue( colorStop1 ) -\n\t\t\t\tgetNumericStopValue( colorStop2 )\n\t\t\t);\n\t\t} )\n\t\t.map( serializeGradientColorStop );\n\treturn `${ type }(${ [ serializedOrientation, ...serializedColorStops ]\n\t\t.filter( Boolean )\n\t\t.join( ',' ) })`;\n}\n"], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;AAGO,SAASA,sBAAsBA,CAAE;EACvCC,IAAI;EACJC;AACyB,CAAC,EAAG;EAC7B,IAAKD,IAAI,KAAK,SAAS,EAAG;IACzB,OAAOC,KAAK;EACb;EACA,IAAKD,IAAI,KAAK,KAAK,EAAG;IACrB,OAAQ,IAAIC,KAAO,EAAC;EACrB;EACA,OAAQ,GAAGD,IAAM,IAAIC,KAAK,CAACC,IAAI,CAAE,GAAI,CAAG,GAAE;AAC3C;AAEO,SAASC,yBAAyBA,CACxCC,QAA8C,EAC7C;EACD,IAAK,CAAEA,QAAQ,EAAG;IACjB,OAAO,EAAE;EACV;EACA,MAAM;IAAEH,KAAK;IAAED;EAAK,CAAC,GAAGI,QAAQ;EAChC,OAAQ,GAAGH,KAAO,GAAGD,IAAM,EAAC;AAC7B;AAEO,SAASK,0BAA0BA,CAAE;EAC3CL,IAAI;EACJC,KAAK;EACLK;AACyB,CAAC,EAAG;EAC7B,OAAQ,GAAGP,sBAAsB,CAAE;IAClCC,IAAI;IACJC;EACD,CAA8B,CAAG,IAAIE,yBAAyB,CAAEG,MAAO,CAAG,EAAC;AAC5E;AAEO,SAASC,4BAA4BA,CAC3CC,WAAyD,EACxD;EACD,IACCC,KAAK,CAACC,OAAO,CAAEF,WAAY,CAAC,IAC5B,CAAEA,WAAW,IACbA,WAAW,CAACR,IAAI,KAAK,SAAS,EAC7B;IACD;EACD;EACA,OAAQ,GAAGQ,WAAW,CAACP,KAAO,KAAI;AACnC;AAEO,SAASU,iBAAiBA,CAAE;EAClCX,IAAI;EACJQ,WAAW;EACXI;AAC4B,CAAC,EAAG;EAChC,MAAMC,qBAAqB,GAAGN,4BAA4B,CAAEC,WAAY,CAAC;EACzE,MAAMM,oBAAoB,GAAGF,UAAU,CACrCG,IAAI,CAAE,CAAEC,UAAU,EAAEC,UAAU,KAAM;IACpC,MAAMC,mBAAmB,GACxBC,SAAmC,IAC/B;MACJ,OAAOA,SAAS,EAAEb,MAAM,EAAEL,KAAK,KAAKmB,SAAS,GAC1C,CAAC,GACDC,QAAQ,CAAEF,SAAS,CAACb,MAAM,CAACL,KAAM,CAAC;IACtC,CAAC;IAED,OACCiB,mBAAmB,CAAEF,UAAW,CAAC,GACjCE,mBAAmB,CAAED,UAAW,CAAC;EAEnC,CAAE,CAAC,CACFK,GAAG,CAAEjB,0BAA2B,CAAC;EACnC,OAAQ,GAAGL,IAAM,IAAI,CAAEa,qBAAqB,EAAE,GAAGC,oBAAoB,CAAE,CACrES,MAAM,CAAEC,OAAQ,CAAC,CACjBtB,IAAI,CAAE,GAAI,CAAG,GAAE;AAClB"}