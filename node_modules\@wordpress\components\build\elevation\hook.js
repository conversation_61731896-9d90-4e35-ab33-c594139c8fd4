"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getBoxShadow = getBoxShadow;
exports.useElevation = useElevation;
var _react = require("@emotion/react");
var _element = require("@wordpress/element");
var _context = require("../context");
var styles = _interopRequireWildcard(require("./styles"));
var _utils = require("../utils");
var _useCx = require("../utils/hooks/use-cx");
var _values = require("../utils/values");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function getBoxShadow(value) {
  const boxShadowColor = `rgba(0, 0, 0, ${value / 20})`;
  const boxShadow = `0 ${value}px ${value * 2}px 0
	${boxShadowColor}`;
  return boxShadow;
}
function useElevation(props) {
  const {
    active,
    borderRadius = 'inherit',
    className,
    focus,
    hover,
    isInteractive = false,
    offset = 0,
    value = 0,
    ...otherProps
  } = (0, _context.useContextSystem)(props, 'Elevation');
  const cx = (0, _useCx.useCx)();
  const classes = (0, _element.useMemo)(() => {
    let hoverValue = (0, _values.isValueDefined)(hover) ? hover : value * 2;
    let activeValue = (0, _values.isValueDefined)(active) ? active : value / 2;
    if (!isInteractive) {
      hoverValue = (0, _values.isValueDefined)(hover) ? hover : undefined;
      activeValue = (0, _values.isValueDefined)(active) ? active : undefined;
    }
    const transition = `box-shadow ${_utils.CONFIG.transitionDuration} ${_utils.CONFIG.transitionTimingFunction}`;
    const sx = {};
    sx.Base = /*#__PURE__*/(0, _react.css)({
      borderRadius,
      bottom: offset,
      boxShadow: getBoxShadow(value),
      opacity: _utils.CONFIG.elevationIntensity,
      left: offset,
      right: offset,
      top: offset,
      transition
    }, (0, _utils.reduceMotion)('transition'), process.env.NODE_ENV === "production" ? "" : ";label:sx-Base;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
    if ((0, _values.isValueDefined)(hoverValue)) {
      sx.hover = /*#__PURE__*/(0, _react.css)("*:hover>&{box-shadow:", getBoxShadow(hoverValue), ";}" + (process.env.NODE_ENV === "production" ? "" : ";label:sx-hover;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
    }
    if ((0, _values.isValueDefined)(activeValue)) {
      sx.active = /*#__PURE__*/(0, _react.css)("*:active>&{box-shadow:", getBoxShadow(activeValue), ";}" + (process.env.NODE_ENV === "production" ? "" : ";label:sx-active;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
    }
    if ((0, _values.isValueDefined)(focus)) {
      sx.focus = /*#__PURE__*/(0, _react.css)("*:focus>&{box-shadow:", getBoxShadow(focus), ";}" + (process.env.NODE_ENV === "production" ? "" : ";label:sx-focus;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
    }
    return cx(styles.Elevation, sx.Base, sx.hover, sx.focus, sx.active, className);
  }, [active, borderRadius, className, cx, focus, hover, isInteractive, offset, value]);
  return {
    ...otherProps,
    className: classes,
    'aria-hidden': true
  };
}
//# sourceMappingURL=hook.js.map