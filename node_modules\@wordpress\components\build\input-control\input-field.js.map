{"version": 3, "names": ["_react2", "require", "_element", "_utils", "_inputControlStyles", "_reducer", "noop", "InputField", "disabled", "dragDirection", "drag<PERSON><PERSON><PERSON><PERSON>", "id", "isDragEnabled", "isFocused", "isPressEnterToChange", "onBlur", "onChange", "onDrag", "onDragEnd", "onDragStart", "onFocus", "onKeyDown", "onValidate", "size", "setIsFocused", "stateReducer", "state", "value", "valueProp", "type", "props", "ref", "change", "commit", "drag", "dragEnd", "dragStart", "invalidate", "pressDown", "pressEnter", "pressUp", "reset", "useInputControlStateReducer", "isDragging", "isDirty", "wasDirtyOnBlur", "useRef", "dragCursor", "useDragCursor", "handleOnBlur", "event", "target", "validity", "valid", "current", "handleOnCommit", "handleOnFocus", "handleOnChange", "nextValue", "currentTarget", "err", "handleOnKeyDown", "key", "preventDefault", "dragGestureProps", "useDrag", "dragProps", "distance", "dragging", "stopPropagation", "axis", "threshold", "enabled", "pointer", "capture", "handleOnMouseDown", "onMouseDown", "ownerDocument", "activeElement", "focus", "_react", "createElement", "Input", "className", "inputSize", "ForwardedComponent", "forwardRef", "_default", "exports", "default"], "sources": ["@wordpress/components/src/input-control/input-field.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { useDrag } from '@use-gesture/react';\nimport type {\n\tSyntheticEvent,\n\tChangeEvent,\n\tKeyboardEvent,\n\tPointerEvent,\n\tFocusEvent,\n\tForwardedRef,\n\tMouseEvent,\n} from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef, useRef } from '@wordpress/element';\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { useDragCursor } from './utils';\nimport { Input } from './styles/input-control-styles';\nimport { useInputControlStateReducer } from './reducer/reducer';\nimport type { InputFieldProps } from './types';\n\nconst noop = () => {};\n\nfunction InputField(\n\t{\n\t\tdisabled = false,\n\t\tdragDirection = 'n',\n\t\tdragThreshold = 10,\n\t\tid,\n\t\tisDragEnabled = false,\n\t\tisFocused,\n\t\tisPressEnterToChange = false,\n\t\tonBlur = noop,\n\t\tonChange = noop,\n\t\tonDrag = noop,\n\t\tonDragEnd = noop,\n\t\tonDragStart = noop,\n\t\tonFocus = noop,\n\t\tonKeyDown = noop,\n\t\tonValidate = noop,\n\t\tsize = 'default',\n\t\tsetIsFocused,\n\t\tstateReducer = ( state: any ) => state,\n\t\tvalue: valueProp,\n\t\ttype,\n\t\t...props\n\t}: WordPressComponentProps< InputFieldProps, 'input', false >,\n\tref: ForwardedRef< HTMLInputElement >\n) {\n\tconst {\n\t\t// State.\n\t\tstate,\n\t\t// Actions.\n\t\tchange,\n\t\tcommit,\n\t\tdrag,\n\t\tdragEnd,\n\t\tdragStart,\n\t\tinvalidate,\n\t\tpressDown,\n\t\tpressEnter,\n\t\tpressUp,\n\t\treset,\n\t} = useInputControlStateReducer(\n\t\tstateReducer,\n\t\t{\n\t\t\tisDragEnabled,\n\t\t\tvalue: valueProp,\n\t\t\tisPressEnterToChange,\n\t\t},\n\t\tonChange\n\t);\n\n\tconst { value, isDragging, isDirty } = state;\n\tconst wasDirtyOnBlur = useRef( false );\n\n\tconst dragCursor = useDragCursor( isDragging, dragDirection );\n\n\tconst handleOnBlur = ( event: FocusEvent< HTMLInputElement > ) => {\n\t\tonBlur( event );\n\t\tsetIsFocused?.( false );\n\n\t\t/**\n\t\t * If isPressEnterToChange is set, this commits the value to\n\t\t * the onChange callback.\n\t\t */\n\t\tif ( isDirty || ! event.target.validity.valid ) {\n\t\t\twasDirtyOnBlur.current = true;\n\t\t\thandleOnCommit( event );\n\t\t}\n\t};\n\n\tconst handleOnFocus = ( event: FocusEvent< HTMLInputElement > ) => {\n\t\tonFocus( event );\n\t\tsetIsFocused?.( true );\n\t};\n\n\tconst handleOnChange = ( event: ChangeEvent< HTMLInputElement > ) => {\n\t\tconst nextValue = event.target.value;\n\t\tchange( nextValue, event );\n\t};\n\n\tconst handleOnCommit = ( event: SyntheticEvent< HTMLInputElement > ) => {\n\t\tconst nextValue = event.currentTarget.value;\n\n\t\ttry {\n\t\t\tonValidate( nextValue );\n\t\t\tcommit( nextValue, event );\n\t\t} catch ( err ) {\n\t\t\tinvalidate( err, event );\n\t\t}\n\t};\n\n\tconst handleOnKeyDown = ( event: KeyboardEvent< HTMLInputElement > ) => {\n\t\tconst { key } = event;\n\t\tonKeyDown( event );\n\n\t\tswitch ( key ) {\n\t\t\tcase 'ArrowUp':\n\t\t\t\tpressUp( event );\n\t\t\t\tbreak;\n\n\t\t\tcase 'ArrowDown':\n\t\t\t\tpressDown( event );\n\t\t\t\tbreak;\n\n\t\t\tcase 'Enter':\n\t\t\t\tpressEnter( event );\n\n\t\t\t\tif ( isPressEnterToChange ) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\thandleOnCommit( event );\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\tcase 'Escape':\n\t\t\t\tif ( isPressEnterToChange && isDirty ) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\treset( valueProp, event );\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t}\n\t};\n\n\tconst dragGestureProps = useDrag< PointerEvent< HTMLInputElement > >(\n\t\t( dragProps ) => {\n\t\t\tconst { distance, dragging, event, target } = dragProps;\n\n\t\t\t// The `target` prop always references the `input` element while, by\n\t\t\t// default, the `dragProps.event.target` property would reference the real\n\t\t\t// event target (i.e. any DOM element that the pointer is hovering while\n\t\t\t// dragging). Ensuring that the `target` is always the `input` element\n\t\t\t// allows consumers of `InputControl` (or any higher-level control) to\n\t\t\t// check the input's validity by accessing `event.target.validity.valid`.\n\t\t\tdragProps.event = {\n\t\t\t\t...dragProps.event,\n\t\t\t\ttarget,\n\t\t\t};\n\n\t\t\tif ( ! distance ) return;\n\t\t\tevent.stopPropagation();\n\n\t\t\t/**\n\t\t\t * Quick return if no longer dragging.\n\t\t\t * This prevents unnecessary value calculations.\n\t\t\t */\n\t\t\tif ( ! dragging ) {\n\t\t\t\tonDragEnd( dragProps );\n\t\t\t\tdragEnd( dragProps );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tonDrag( dragProps );\n\t\t\tdrag( dragProps );\n\n\t\t\tif ( ! isDragging ) {\n\t\t\t\tonDragStart( dragProps );\n\t\t\t\tdragStart( dragProps );\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\taxis: dragDirection === 'e' || dragDirection === 'w' ? 'x' : 'y',\n\t\t\tthreshold: dragThreshold,\n\t\t\tenabled: isDragEnabled,\n\t\t\tpointer: { capture: false },\n\t\t}\n\t);\n\n\tconst dragProps = isDragEnabled ? dragGestureProps() : {};\n\t/*\n\t * Works around the odd UA (e.g. Firefox) that does not focus inputs of\n\t * type=number when their spinner arrows are pressed.\n\t */\n\tlet handleOnMouseDown;\n\tif ( type === 'number' ) {\n\t\thandleOnMouseDown = ( event: MouseEvent< HTMLInputElement > ) => {\n\t\t\tprops.onMouseDown?.( event );\n\t\t\tif (\n\t\t\t\tevent.currentTarget !==\n\t\t\t\tevent.currentTarget.ownerDocument.activeElement\n\t\t\t) {\n\t\t\t\tevent.currentTarget.focus();\n\t\t\t}\n\t\t};\n\t}\n\n\treturn (\n\t\t<Input\n\t\t\t{ ...props }\n\t\t\t{ ...dragProps }\n\t\t\tclassName=\"components-input-control__input\"\n\t\t\tdisabled={ disabled }\n\t\t\tdragCursor={ dragCursor }\n\t\t\tisDragging={ isDragging }\n\t\t\tid={ id }\n\t\t\tonBlur={ handleOnBlur }\n\t\t\tonChange={ handleOnChange }\n\t\t\tonFocus={ handleOnFocus }\n\t\t\tonKeyDown={ handleOnKeyDown }\n\t\t\tonMouseDown={ handleOnMouseDown }\n\t\t\tref={ ref }\n\t\t\tinputSize={ size }\n\t\t\t// Fallback to `''` to avoid \"uncontrolled to controlled\" warning.\n\t\t\t// See https://github.com/WordPress/gutenberg/pull/47250 for details.\n\t\t\tvalue={ value ?? '' }\n\t\t\ttype={ type }\n\t\t/>\n\t);\n}\n\nconst ForwardedComponent = forwardRef( InputField );\n\nexport default ForwardedComponent;\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAcA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,mBAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAxBA;AACA;AACA;;AAYA;AACA;AACA;;AAWA,MAAMK,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAErB,SAASC,UAAUA,CAClB;EACCC,QAAQ,GAAG,KAAK;EAChBC,aAAa,GAAG,GAAG;EACnBC,aAAa,GAAG,EAAE;EAClBC,EAAE;EACFC,aAAa,GAAG,KAAK;EACrBC,SAAS;EACTC,oBAAoB,GAAG,KAAK;EAC5BC,MAAM,GAAGT,IAAI;EACbU,QAAQ,GAAGV,IAAI;EACfW,MAAM,GAAGX,IAAI;EACbY,SAAS,GAAGZ,IAAI;EAChBa,WAAW,GAAGb,IAAI;EAClBc,OAAO,GAAGd,IAAI;EACde,SAAS,GAAGf,IAAI;EAChBgB,UAAU,GAAGhB,IAAI;EACjBiB,IAAI,GAAG,SAAS;EAChBC,YAAY;EACZC,YAAY,GAAKC,KAAU,IAAMA,KAAK;EACtCC,KAAK,EAAEC,SAAS;EAChBC,IAAI;EACJ,GAAGC;AACwD,CAAC,EAC7DC,GAAqC,EACpC;EACD,MAAM;IACL;IACAL,KAAK;IACL;IACAM,MAAM;IACNC,MAAM;IACNC,IAAI;IACJC,OAAO;IACPC,SAAS;IACTC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,OAAO;IACPC;EACD,CAAC,GAAG,IAAAC,oCAA2B,EAC9BjB,YAAY,EACZ;IACCb,aAAa;IACbe,KAAK,EAAEC,SAAS;IAChBd;EACD,CAAC,EACDE,QACD,CAAC;EAED,MAAM;IAAEW,KAAK;IAAEgB,UAAU;IAAEC;EAAQ,CAAC,GAAGlB,KAAK;EAC5C,MAAMmB,cAAc,GAAG,IAAAC,eAAM,EAAE,KAAM,CAAC;EAEtC,MAAMC,UAAU,GAAG,IAAAC,oBAAa,EAAEL,UAAU,EAAElC,aAAc,CAAC;EAE7D,MAAMwC,YAAY,GAAKC,KAAqC,IAAM;IACjEnC,MAAM,CAAEmC,KAAM,CAAC;IACf1B,YAAY,GAAI,KAAM,CAAC;;IAEvB;AACF;AACA;AACA;IACE,IAAKoB,OAAO,IAAI,CAAEM,KAAK,CAACC,MAAM,CAACC,QAAQ,CAACC,KAAK,EAAG;MAC/CR,cAAc,CAACS,OAAO,GAAG,IAAI;MAC7BC,cAAc,CAAEL,KAAM,CAAC;IACxB;EACD,CAAC;EAED,MAAMM,aAAa,GAAKN,KAAqC,IAAM;IAClE9B,OAAO,CAAE8B,KAAM,CAAC;IAChB1B,YAAY,GAAI,IAAK,CAAC;EACvB,CAAC;EAED,MAAMiC,cAAc,GAAKP,KAAsC,IAAM;IACpE,MAAMQ,SAAS,GAAGR,KAAK,CAACC,MAAM,CAACxB,KAAK;IACpCK,MAAM,CAAE0B,SAAS,EAAER,KAAM,CAAC;EAC3B,CAAC;EAED,MAAMK,cAAc,GAAKL,KAAyC,IAAM;IACvE,MAAMQ,SAAS,GAAGR,KAAK,CAACS,aAAa,CAAChC,KAAK;IAE3C,IAAI;MACHL,UAAU,CAAEoC,SAAU,CAAC;MACvBzB,MAAM,CAAEyB,SAAS,EAAER,KAAM,CAAC;IAC3B,CAAC,CAAC,OAAQU,GAAG,EAAG;MACfvB,UAAU,CAAEuB,GAAG,EAAEV,KAAM,CAAC;IACzB;EACD,CAAC;EAED,MAAMW,eAAe,GAAKX,KAAwC,IAAM;IACvE,MAAM;MAAEY;IAAI,CAAC,GAAGZ,KAAK;IACrB7B,SAAS,CAAE6B,KAAM,CAAC;IAElB,QAASY,GAAG;MACX,KAAK,SAAS;QACbtB,OAAO,CAAEU,KAAM,CAAC;QAChB;MAED,KAAK,WAAW;QACfZ,SAAS,CAAEY,KAAM,CAAC;QAClB;MAED,KAAK,OAAO;QACXX,UAAU,CAAEW,KAAM,CAAC;QAEnB,IAAKpC,oBAAoB,EAAG;UAC3BoC,KAAK,CAACa,cAAc,CAAC,CAAC;UACtBR,cAAc,CAAEL,KAAM,CAAC;QACxB;QACA;MAED,KAAK,QAAQ;QACZ,IAAKpC,oBAAoB,IAAI8B,OAAO,EAAG;UACtCM,KAAK,CAACa,cAAc,CAAC,CAAC;UACtBtB,KAAK,CAAEb,SAAS,EAAEsB,KAAM,CAAC;QAC1B;QACA;IACF;EACD,CAAC;EAED,MAAMc,gBAAgB,GAAG,IAAAC,eAAO,EAC7BC,SAAS,IAAM;IAChB,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAElB,KAAK;MAAEC;IAAO,CAAC,GAAGe,SAAS;;IAEvD;IACA;IACA;IACA;IACA;IACA;IACAA,SAAS,CAAChB,KAAK,GAAG;MACjB,GAAGgB,SAAS,CAAChB,KAAK;MAClBC;IACD,CAAC;IAED,IAAK,CAAEgB,QAAQ,EAAG;IAClBjB,KAAK,CAACmB,eAAe,CAAC,CAAC;;IAEvB;AACH;AACA;AACA;IACG,IAAK,CAAED,QAAQ,EAAG;MACjBlD,SAAS,CAAEgD,SAAU,CAAC;MACtB/B,OAAO,CAAE+B,SAAU,CAAC;MACpB;IACD;IAEAjD,MAAM,CAAEiD,SAAU,CAAC;IACnBhC,IAAI,CAAEgC,SAAU,CAAC;IAEjB,IAAK,CAAEvB,UAAU,EAAG;MACnBxB,WAAW,CAAE+C,SAAU,CAAC;MACxB9B,SAAS,CAAE8B,SAAU,CAAC;IACvB;EACD,CAAC,EACD;IACCI,IAAI,EAAE7D,aAAa,KAAK,GAAG,IAAIA,aAAa,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;IAChE8D,SAAS,EAAE7D,aAAa;IACxB8D,OAAO,EAAE5D,aAAa;IACtB6D,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAM;EAC3B,CACD,CAAC;EAED,MAAMR,SAAS,GAAGtD,aAAa,GAAGoD,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;EACzD;AACD;AACA;AACA;EACC,IAAIW,iBAAiB;EACrB,IAAK9C,IAAI,KAAK,QAAQ,EAAG;IACxB8C,iBAAiB,GAAKzB,KAAqC,IAAM;MAChEpB,KAAK,CAAC8C,WAAW,GAAI1B,KAAM,CAAC;MAC5B,IACCA,KAAK,CAACS,aAAa,KACnBT,KAAK,CAACS,aAAa,CAACkB,aAAa,CAACC,aAAa,EAC9C;QACD5B,KAAK,CAACS,aAAa,CAACoB,KAAK,CAAC,CAAC;MAC5B;IACD,CAAC;EACF;EAEA,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC7E,mBAAA,CAAA8E,KAAK;IAAA,GACApD,KAAK;IAAA,GACLoC,SAAS;IACdiB,SAAS,EAAC,iCAAiC;IAC3C3E,QAAQ,EAAGA,QAAU;IACrBuC,UAAU,EAAGA,UAAY;IACzBJ,UAAU,EAAGA,UAAY;IACzBhC,EAAE,EAAGA,EAAI;IACTI,MAAM,EAAGkC,YAAc;IACvBjC,QAAQ,EAAGyC,cAAgB;IAC3BrC,OAAO,EAAGoC,aAAe;IACzBnC,SAAS,EAAGwC,eAAiB;IAC7Be,WAAW,EAAGD,iBAAmB;IACjC5C,GAAG,EAAGA,GAAK;IACXqD,SAAS,EAAG7D;IACZ;IACA;IAAA;IACAI,KAAK,EAAGA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAI;IACrBE,IAAI,EAAGA;EAAM,CACb,CAAC;AAEJ;AAEA,MAAMwD,kBAAkB,GAAG,IAAAC,mBAAU,EAAE/E,UAAW,CAAC;AAAC,IAAAgF,QAAA,GAErCF,kBAAkB;AAAAG,OAAA,CAAAC,OAAA,GAAAF,QAAA"}