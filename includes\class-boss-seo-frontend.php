<?php
/**
 * Gestion de l'affichage frontend des balises SEO.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe de gestion frontend des balises SEO.
 *
 * Cette classe gère l'injection des balises meta SEO dans le head HTML.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Frontend {

    /**
     * Nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Nom du plugin.
     */
    private $plugin_name;

    /**
     * Version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    Version du plugin.
     */
    private $version;

    /**
     * Constructeur.
     *
     * @since    1.2.0
     * @param    string    $plugin_name    Nom du plugin.
     * @param    string    $version        Version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks WordPress.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Injection des balises meta dans le head
        add_action( 'wp_head', array( $this, 'inject_meta_tags' ), 1 );
        
        // Modifier le titre de la page
        add_filter( 'pre_get_document_title', array( $this, 'filter_document_title' ), 10 );
        add_filter( 'wp_title', array( $this, 'filter_wp_title' ), 10, 2 );
    }

    /**
     * Injecte les balises meta SEO dans le head.
     *
     * @since    1.2.0
     */
    public function inject_meta_tags() {
        if ( is_admin() ) {
            return;
        }

        global $post;
        
        if ( ! $post ) {
            return;
        }

        // Récupérer les métadonnées SEO
        $seo_title = get_post_meta( $post->ID, '_boss_seo_title', true );
        $meta_description = get_post_meta( $post->ID, '_boss_seo_meta_description', true );
        $focus_keyword = get_post_meta( $post->ID, '_boss_seo_focus_keyword', true );
        $secondary_keywords = get_post_meta( $post->ID, '_boss_seo_secondary_keywords', true );

        // Injecter la meta description
        if ( ! empty( $meta_description ) ) {
            echo '<meta name="description" content="' . esc_attr( $meta_description ) . '">' . "\n";
        }

        // Injecter les mots-clés
        $all_keywords = array();
        if ( ! empty( $focus_keyword ) ) {
            $all_keywords[] = $focus_keyword;
        }
        if ( ! empty( $secondary_keywords ) ) {
            $secondary_array = is_array( $secondary_keywords ) 
                ? $secondary_keywords 
                : explode( ',', $secondary_keywords );
            $all_keywords = array_merge( $all_keywords, array_map( 'trim', $secondary_array ) );
        }

        if ( ! empty( $all_keywords ) ) {
            echo '<meta name="keywords" content="' . esc_attr( implode( ', ', $all_keywords ) ) . '">' . "\n";
        }

        // Balises Open Graph
        if ( ! empty( $seo_title ) ) {
            echo '<meta property="og:title" content="' . esc_attr( $seo_title ) . '">' . "\n";
        }

        if ( ! empty( $meta_description ) ) {
            echo '<meta property="og:description" content="' . esc_attr( $meta_description ) . '">' . "\n";
        }

        echo '<meta property="og:type" content="article">' . "\n";
        echo '<meta property="og:url" content="' . esc_url( get_permalink( $post->ID ) ) . '">' . "\n";

        // Image Open Graph
        $thumbnail_id = get_post_thumbnail_id( $post->ID );
        if ( $thumbnail_id ) {
            $thumbnail_url = wp_get_attachment_image_url( $thumbnail_id, 'large' );
            if ( $thumbnail_url ) {
                echo '<meta property="og:image" content="' . esc_url( $thumbnail_url ) . '">' . "\n";
            }
        }

        // Balises Twitter Card
        echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
        
        if ( ! empty( $seo_title ) ) {
            echo '<meta name="twitter:title" content="' . esc_attr( $seo_title ) . '">' . "\n";
        }

        if ( ! empty( $meta_description ) ) {
            echo '<meta name="twitter:description" content="' . esc_attr( $meta_description ) . '">' . "\n";
        }

        if ( $thumbnail_id && $thumbnail_url ) {
            echo '<meta name="twitter:image" content="' . esc_url( $thumbnail_url ) . '">' . "\n";
        }

        // Balise robots
        echo '<meta name="robots" content="index, follow">' . "\n";

        // Canonical URL
        echo '<link rel="canonical" href="' . esc_url( get_permalink( $post->ID ) ) . '">' . "\n";
    }

    /**
     * Filtre le titre du document.
     *
     * @since    1.2.0
     * @param    string    $title    Titre par défaut.
     * @return   string              Titre modifié.
     */
    public function filter_document_title( $title ) {
        if ( is_admin() ) {
            return $title;
        }

        global $post;
        
        if ( ! $post ) {
            return $title;
        }

        $seo_title = get_post_meta( $post->ID, '_boss_seo_title', true );
        
        if ( ! empty( $seo_title ) ) {
            return $seo_title;
        }

        return $title;
    }

    /**
     * Filtre le titre WordPress.
     *
     * @since    1.2.0
     * @param    string    $title    Titre par défaut.
     * @param    string    $sep      Séparateur.
     * @return   string              Titre modifié.
     */
    public function filter_wp_title( $title, $sep ) {
        if ( is_admin() ) {
            return $title;
        }

        global $post;
        
        if ( ! $post ) {
            return $title;
        }

        $seo_title = get_post_meta( $post->ID, '_boss_seo_title', true );
        
        if ( ! empty( $seo_title ) ) {
            return $seo_title;
        }

        return $title;
    }
}
