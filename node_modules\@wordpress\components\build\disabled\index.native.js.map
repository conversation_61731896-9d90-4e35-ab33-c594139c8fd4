{"version": 3, "names": ["_reactNative", "require", "Disabled", "children", "_react", "createElement", "View", "pointerEvents", "_default", "exports", "default"], "sources": ["@wordpress/components/src/disabled/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View } from 'react-native';\n\nfunction Disabled( { children } ) {\n\treturn <View pointerEvents=\"none\">{ children }</View>;\n}\n\nexport default Disabled;\n"], "mappings": ";;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA,SAASC,QAAQA,CAAE;EAAEC;AAAS,CAAC,EAAG;EACjC,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACL,YAAA,CAAAM,IAAI;IAACC,aAAa,EAAC;EAAM,GAAGJ,QAAgB,CAAC;AACtD;AAAC,IAAAK,QAAA,GAEcN,QAAQ;AAAAO,OAAA,CAAAC,OAAA,GAAAF,QAAA"}