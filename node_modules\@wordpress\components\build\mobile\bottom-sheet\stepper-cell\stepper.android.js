"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _icons = require("@wordpress/icons");
var _compose = require("@wordpress/compose");
var _style = _interopRequireDefault(require("./style.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function Stepper({
  getStylesFromColorScheme,
  isMaxValue,
  isMinValue,
  onPressInDecrement,
  onPressInIncrement,
  onPressOut,
  value,
  shouldDisplayTextInput,
  children
}) {
  const valueStyle = getStylesFromColorScheme(_style.default.value, _style.default.valueTextDark);
  const buttonIconStyle = getStylesFromColorScheme(_style.default.buttonNoBg, _style.default.buttonNoBgTextDark);
  return (0, _react.createElement)(_reactNative.View, {
    style: _style.default.container
  }, (0, _react.createElement)(_reactNative.TouchableOpacity, {
    disabled: isMinValue,
    onPressIn: onPressInDecrement,
    onPressOut: onPressOut,
    style: [_style.default.buttonNoBg, isMinValue ? {
      opacity: 0.4
    } : null]
  }, (0, _react.createElement)(_icons.Icon, {
    icon: _icons.chevronDown,
    size: 24,
    color: buttonIconStyle.color
  })), !shouldDisplayTextInput && (0, _react.createElement)(_reactNative.Text, {
    style: [valueStyle, _style.default.spacings]
  }, value), children, (0, _react.createElement)(_reactNative.TouchableOpacity, {
    testID: 'Increment',
    disabled: isMaxValue,
    onPressIn: onPressInIncrement,
    onPressOut: onPressOut,
    style: [_style.default.buttonNoBg, isMaxValue ? {
      opacity: 0.4
    } : null]
  }, (0, _react.createElement)(_icons.Icon, {
    icon: _icons.chevronUp,
    size: 24,
    color: buttonIconStyle.color
  })));
}
var _default = (0, _compose.withPreferredColorScheme)(Stepper);
exports.default = _default;
//# sourceMappingURL=stepper.android.js.map