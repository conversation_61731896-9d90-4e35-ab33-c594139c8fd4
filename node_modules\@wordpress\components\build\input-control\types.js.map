{"version": 3, "names": [], "sources": ["@wordpress/components/src/input-control/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type {\n\tCSSProperties,\n\tReactNode,\n\tSyntheticEvent,\n\tHTMLInputTypeAttribute,\n} from 'react';\nimport type { useDrag } from '@use-gesture/react';\n\n/**\n * Internal dependencies\n */\nimport type { StateReducer } from './reducer/state';\nimport type { WordPressComponentProps } from '../context';\nimport type { FlexProps } from '../flex/types';\nimport type { BaseControlProps } from '../base-control/types';\n\nexport type LabelPosition = 'top' | 'bottom' | 'side' | 'edge';\n\nexport type DragDirection = 'n' | 's' | 'e' | 'w';\n\nexport type DragProps = Parameters< Parameters< typeof useDrag >[ 0 ] >[ 0 ];\n\nexport type Size = 'default' | 'small' | 'compact' | '__unstable-large';\n\ninterface BaseProps {\n\t/**\n\t * Deprecated. Use `__next40pxDefaultSize` instead.\n\t *\n\t * @default false\n\t * @deprecated\n\t */\n\t__next36pxDefaultSize?: boolean;\n\t/**\n\t * Start opting into the larger default height that will become the default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n\t__unstableInputWidth?: CSSProperties[ 'width' ];\n\t/**\n\t * If true, the label will only be visible to screen readers.\n\t *\n\t * @default false\n\t */\n\thideLabelFromVision?: boolean;\n\t/**\n\t * Whether the component should be in a focused state.\n\t * Used to coordinate focus states when the actual focused element and the component handling\n\t * visual focus are separate.\n\t *\n\t * @default false\n\t */\n\tisFocused: boolean;\n\t/**\n\t * The position of the label.\n\t *\n\t * @default 'top'\n\t */\n\tlabelPosition?: LabelPosition;\n\t/**\n\t * Adjusts the size of the input.\n\t *\n\t * @default 'default'\n\t */\n\tsize?: Size;\n}\n\nexport type InputChangeCallback< P = {} > = (\n\tnextValue: string | undefined,\n\textra: { event: SyntheticEvent } & P\n) => void;\n\nexport interface InputFieldProps\n\textends Omit< BaseProps, '__next36pxDefaultSize' > {\n\t/**\n\t * Determines the drag axis.\n\t *\n\t * @default 'n'\n\t */\n\tdragDirection?: DragDirection;\n\t/**\n\t * If `isDragEnabled` is true, this controls the amount of `px` to have been dragged before\n\t * the drag gesture is actually triggered.\n\t *\n\t * @default 10\n\t */\n\tdragThreshold?: number;\n\t/**\n\t * If true, enables mouse drag gestures.\n\t *\n\t * @default false\n\t */\n\tisDragEnabled?: boolean;\n\t/**\n\t * If true, the `ENTER` key press is required in order to trigger an `onChange`.\n\t * If enabled, a change is also triggered when tabbing away (`onBlur`).\n\t *\n\t * @default false\n\t */\n\tisPressEnterToChange?: boolean;\n\t/**\n\t * A function that receives the value of the input.\n\t */\n\tonChange?: InputChangeCallback;\n\tonValidate?: (\n\t\tnextValue: string,\n\t\tevent?: SyntheticEvent< HTMLInputElement >\n\t) => void;\n\tpaddingInlineStart?: CSSProperties[ 'paddingInlineStart' ];\n\tpaddingInlineEnd?: CSSProperties[ 'paddingInlineEnd' ];\n\tsetIsFocused: ( isFocused: boolean ) => void;\n\tstateReducer?: StateReducer;\n\t/**\n\t * The current value of the input.\n\t */\n\tvalue?: string;\n\tonDragEnd?: ( dragProps: DragProps ) => void;\n\tonDragStart?: ( dragProps: DragProps ) => void;\n\tonDrag?: ( dragProps: DragProps ) => void;\n\t/**\n\t * Type of the input element to render.\n\t *\n\t * @default 'text'\n\t */\n\ttype?: HTMLInputTypeAttribute;\n}\n\nexport interface InputBaseProps extends BaseProps, FlexProps {\n\tchildren: ReactNode;\n\t/**\n\t * Renders an element on the left side of the input.\n\t *\n\t * By default, the prefix is aligned with the edge of the input border, with no padding.\n\t * If you want to apply standard padding in accordance with the size variant, wrap the element in\n\t * the provided `<InputControlPrefixWrapper>` component.\n\t *\n\t * @example\n\t * import {\n\t *   __experimentalInputControl as InputControl,\n\t *   __experimentalInputControlPrefixWrapper as InputControlPrefixWrapper,\n\t * } from '@wordpress/components';\n\t *\n\t * <InputControl\n\t *   prefix={<InputControlPrefixWrapper>@</InputControlPrefixWrapper>}\n\t * />\n\t */\n\tprefix?: ReactNode;\n\t/**\n\t * Renders an element on the right side of the input.\n\t *\n\t * By default, the suffix is aligned with the edge of the input border, with no padding.\n\t * If you want to apply standard padding in accordance with the size variant, wrap the element in\n\t * the provided `<InputControlSuffixWrapper>` component.\n\t *\n\t * @example\n\t * import {\n\t *   __experimentalInputControl as InputControl,\n\t *   __experimentalInputControlSuffixWrapper as InputControlSuffixWrapper,\n\t * } from '@wordpress/components';\n\t *\n\t * <InputControl\n\t *   suffix={<InputControlSuffixWrapper>%</InputControlSuffixWrapper>}\n\t * />\n\t */\n\tsuffix?: ReactNode;\n\t/**\n\t * If true, the `input` will be disabled.\n\t *\n\t * @default false\n\t */\n\tdisabled?: boolean;\n\t/**\n\t * If this property is added, a label will be generated using label property as the content.\n\t */\n\tlabel?: ReactNode;\n}\n\nexport interface InputControlProps\n\textends Omit< InputBaseProps, 'children' | 'isFocused' | keyof FlexProps >,\n\t\tPick< BaseControlProps, 'help' >,\n\t\t/**\n\t\t * The `prefix` prop in `WordPressComponentProps< InputFieldProps, 'input', false >` comes from the\n\t\t * `HTMLInputAttributes` and clashes with the one from `InputBaseProps`. So we have to omit it from\n\t\t * `WordPressComponentProps< InputFieldProps, 'input', false >` in order that `InputBaseProps[ 'prefix' ]`\n\t\t * be the only prefix prop. Otherwise it tries to do a union of the two prefix properties and you end up\n\t\t * with an unresolvable type.\n\t\t *\n\t\t * `isFocused`, `setIsFocused`, `paddingInlineStart`, and `paddingInlineEnd` are managed internally by\n\t\t * the InputControl, but the rest of the props for InputField are passed through.\n\t\t */\n\t\tOmit<\n\t\t\tWordPressComponentProps< InputFieldProps, 'input', false >,\n\t\t\t| 'stateReducer'\n\t\t\t| 'prefix'\n\t\t\t| 'isFocused'\n\t\t\t| 'setIsFocused'\n\t\t\t| 'paddingInlineStart'\n\t\t\t| 'paddingInlineEnd'\n\t\t> {\n\t__unstableStateReducer?: InputFieldProps[ 'stateReducer' ];\n}\n\nexport interface InputControlLabelProps {\n\tchildren: ReactNode;\n\thideLabelFromVision?: BaseProps[ 'hideLabelFromVision' ];\n\tlabelPosition?: BaseProps[ 'labelPosition' ];\n\tsize?: BaseProps[ 'size' ];\n}\n\nexport type InputControlPrefixWrapperProps = {\n\t/**\n\t * The prefix to be inserted.\n\t */\n\tchildren: ReactNode;\n};\n\nexport type InputControlSuffixWrapperProps = {\n\t/**\n\t * The suffix to be inserted.\n\t */\n\tchildren: ReactNode;\n};\n"], "mappings": ""}