{"version": 3, "names": ["_react", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "disabledStyles", "process", "env", "NODE_ENV", "name", "styles", "map", "toString", "exports"], "sources": ["@wordpress/components/src/disabled/styles/disabled-styles.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\nexport const disabledStyles = css`\n\tposition: relative;\n\tpointer-events: none;\n\n\t&::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t}\n\n\t// Also make nested blocks unselectable.\n\t* {\n\t\tpointer-events: none;\n\t}\n`;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAAqC,SAAAC,iCAAA;AAE9B,MAAMC,cAAc,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAiB1B;AAACS,OAAA,CAAAR,cAAA,GAAAA,cAAA"}