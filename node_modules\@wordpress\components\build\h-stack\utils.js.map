{"version": 3, "names": ["_values", "require", "H_ALIGNMENTS", "bottom", "align", "justify", "bottomLeft", "bottomRight", "center", "edge", "left", "right", "stretch", "top", "topLeft", "topRight", "V_ALIGNMENTS", "getAlignmentProps", "alignment", "direction", "isValueDefined", "isVertical", "props", "alignmentProps"], "sources": ["@wordpress/components/src/h-stack/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n/**\n * Internal dependencies\n */\nimport type { FlexDirection } from '../flex/types';\nimport type { HStackAlignment, AlignmentProps, Alignments } from './types';\nimport { isValueDefined } from '../utils/values';\n\nconst H_ALIGNMENTS: Alignments = {\n\tbottom: { align: 'flex-end', justify: 'center' },\n\tbottomLeft: { align: 'flex-end', justify: 'flex-start' },\n\tbottomRight: { align: 'flex-end', justify: 'flex-end' },\n\tcenter: { align: 'center', justify: 'center' },\n\tedge: { align: 'center', justify: 'space-between' },\n\tleft: { align: 'center', justify: 'flex-start' },\n\tright: { align: 'center', justify: 'flex-end' },\n\tstretch: { align: 'stretch' },\n\ttop: { align: 'flex-start', justify: 'center' },\n\ttopLeft: { align: 'flex-start', justify: 'flex-start' },\n\ttopRight: { align: 'flex-start', justify: 'flex-end' },\n};\n\nconst V_ALIGNMENTS: Alignments = {\n\tbottom: { justify: 'flex-end', align: 'center' },\n\tbottomLeft: { justify: 'flex-end', align: 'flex-start' },\n\tbottomRight: { justify: 'flex-end', align: 'flex-end' },\n\tcenter: { justify: 'center', align: 'center' },\n\tedge: { justify: 'space-between', align: 'center' },\n\tleft: { justify: 'center', align: 'flex-start' },\n\tright: { justify: 'center', align: 'flex-end' },\n\tstretch: { align: 'stretch' },\n\ttop: { justify: 'flex-start', align: 'center' },\n\ttopLeft: { justify: 'flex-start', align: 'flex-start' },\n\ttopRight: { justify: 'flex-start', align: 'flex-end' },\n};\n\nexport function getAlignmentProps(\n\talignment: HStackAlignment | CSSProperties[ 'alignItems' ],\n\tdirection: FlexDirection = 'row'\n): AlignmentProps {\n\tif ( ! isValueDefined( alignment ) ) {\n\t\treturn {};\n\t}\n\tconst isVertical = direction === 'column';\n\tconst props = isVertical ? V_ALIGNMENTS : H_ALIGNMENTS;\n\n\tconst alignmentProps =\n\t\talignment in props\n\t\t\t? props[ alignment as keyof typeof props ]\n\t\t\t: { align: alignment };\n\n\treturn alignmentProps;\n}\n"], "mappings": ";;;;;;AASA,IAAAA,OAAA,GAAAC,OAAA;AATA;AACA;AACA;;AAEA;AACA;AACA;;AAKA,MAAMC,YAAwB,GAAG;EAChCC,MAAM,EAAE;IAAEC,KAAK,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAS,CAAC;EAChDC,UAAU,EAAE;IAAEF,KAAK,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAa,CAAC;EACxDE,WAAW,EAAE;IAAEH,KAAK,EAAE,UAAU;IAAEC,OAAO,EAAE;EAAW,CAAC;EACvDG,MAAM,EAAE;IAAEJ,KAAK,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAS,CAAC;EAC9CI,IAAI,EAAE;IAAEL,KAAK,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAgB,CAAC;EACnDK,IAAI,EAAE;IAAEN,KAAK,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAa,CAAC;EAChDM,KAAK,EAAE;IAAEP,KAAK,EAAE,QAAQ;IAAEC,OAAO,EAAE;EAAW,CAAC;EAC/CO,OAAO,EAAE;IAAER,KAAK,EAAE;EAAU,CAAC;EAC7BS,GAAG,EAAE;IAAET,KAAK,EAAE,YAAY;IAAEC,OAAO,EAAE;EAAS,CAAC;EAC/CS,OAAO,EAAE;IAAEV,KAAK,EAAE,YAAY;IAAEC,OAAO,EAAE;EAAa,CAAC;EACvDU,QAAQ,EAAE;IAAEX,KAAK,EAAE,YAAY;IAAEC,OAAO,EAAE;EAAW;AACtD,CAAC;AAED,MAAMW,YAAwB,GAAG;EAChCb,MAAM,EAAE;IAAEE,OAAO,EAAE,UAAU;IAAED,KAAK,EAAE;EAAS,CAAC;EAChDE,UAAU,EAAE;IAAED,OAAO,EAAE,UAAU;IAAED,KAAK,EAAE;EAAa,CAAC;EACxDG,WAAW,EAAE;IAAEF,OAAO,EAAE,UAAU;IAAED,KAAK,EAAE;EAAW,CAAC;EACvDI,MAAM,EAAE;IAAEH,OAAO,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAS,CAAC;EAC9CK,IAAI,EAAE;IAAEJ,OAAO,EAAE,eAAe;IAAED,KAAK,EAAE;EAAS,CAAC;EACnDM,IAAI,EAAE;IAAEL,OAAO,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAa,CAAC;EAChDO,KAAK,EAAE;IAAEN,OAAO,EAAE,QAAQ;IAAED,KAAK,EAAE;EAAW,CAAC;EAC/CQ,OAAO,EAAE;IAAER,KAAK,EAAE;EAAU,CAAC;EAC7BS,GAAG,EAAE;IAAER,OAAO,EAAE,YAAY;IAAED,KAAK,EAAE;EAAS,CAAC;EAC/CU,OAAO,EAAE;IAAET,OAAO,EAAE,YAAY;IAAED,KAAK,EAAE;EAAa,CAAC;EACvDW,QAAQ,EAAE;IAAEV,OAAO,EAAE,YAAY;IAAED,KAAK,EAAE;EAAW;AACtD,CAAC;AAEM,SAASa,iBAAiBA,CAChCC,SAA0D,EAC1DC,SAAwB,GAAG,KAAK,EACf;EACjB,IAAK,CAAE,IAAAC,sBAAc,EAAEF,SAAU,CAAC,EAAG;IACpC,OAAO,CAAC,CAAC;EACV;EACA,MAAMG,UAAU,GAAGF,SAAS,KAAK,QAAQ;EACzC,MAAMG,KAAK,GAAGD,UAAU,GAAGL,YAAY,GAAGd,YAAY;EAEtD,MAAMqB,cAAc,GACnBL,SAAS,IAAII,KAAK,GACfA,KAAK,CAAEJ,SAAS,CAAwB,GACxC;IAAEd,KAAK,EAAEc;EAAU,CAAC;EAExB,OAAOK,cAAc;AACtB"}