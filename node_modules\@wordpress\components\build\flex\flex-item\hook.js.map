{"version": 3, "names": ["_react", "require", "_context", "_context2", "styles", "_interopRequireWildcard", "_useCx", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useFlexItem", "props", "className", "display", "displayProp", "isBlock", "otherProps", "useContextSystem", "sx", "contextDisplay", "useFlexContext", "flexItemDisplay", "Base", "css", "process", "env", "NODE_ENV", "cx", "useCx", "classes", "<PERSON><PERSON>", "block"], "sources": ["@wordpress/components/src/flex/flex-item/hook.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { SerializedStyles } from '@emotion/react';\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useFlexContext } from '../context';\nimport * as styles from '../styles';\nimport { useCx } from '../../utils/hooks/use-cx';\nimport type { FlexItemProps } from '../types';\n\nexport function useFlexItem(\n\tprops: WordPressComponentProps< FlexItemProps, 'div' >\n) {\n\tconst {\n\t\tclassName,\n\t\tdisplay: displayProp,\n\t\tisBlock = false,\n\t\t...otherProps\n\t} = useContextSystem( props, 'FlexItem' );\n\n\tconst sx: {\n\t\tBase?: SerializedStyles;\n\t} = {};\n\n\tconst contextDisplay = useFlexContext().flexItemDisplay;\n\n\tsx.Base = css( {\n\t\tdisplay: displayProp || contextDisplay,\n\t} );\n\n\tconst cx = useCx();\n\n\tconst classes = cx(\n\t\tstyles.Item,\n\t\tsx.Base,\n\t\tisBlock && styles.block,\n\t\tclassName\n\t);\n\n\treturn {\n\t\t...otherProps,\n\t\tclassName: classes,\n\t};\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAAiD,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAH,wBAAAO,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAbjD;AACA;AACA;;AAcO,SAASW,WAAWA,CAC1BC,KAAsD,EACrD;EACD,MAAM;IACLC,SAAS;IACTC,OAAO,EAAEC,WAAW;IACpBC,OAAO,GAAG,KAAK;IACf,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEN,KAAK,EAAE,UAAW,CAAC;EAEzC,MAAMO,EAEL,GAAG,CAAC,CAAC;EAEN,MAAMC,cAAc,GAAG,IAAAC,wBAAc,EAAC,CAAC,CAACC,eAAe;EAEvDH,EAAE,CAACI,IAAI,gBAAG,IAAAC,UAAG,EAAE;IACdV,OAAO,EAAEC,WAAW,IAAIK;EACzB,CAAC,EAAAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,4CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,kxDAAC,CAAC;EAEH,MAAMC,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAElB,MAAMC,OAAO,GAAGF,EAAE,CACjB1C,MAAM,CAAC6C,IAAI,EACXZ,EAAE,CAACI,IAAI,EACPP,OAAO,IAAI9B,MAAM,CAAC8C,KAAK,EACvBnB,SACD,CAAC;EAED,OAAO;IACN,GAAGI,UAAU;IACbJ,SAAS,EAAEiB;EACZ,CAAC;AACF"}