import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Notice,
  Spinner,
  TextControl,
  Flex,
  FlexItem,
  FlexBlock,
  __experimentalSpacer as Spacer,
  __experimentalHeading as Heading,
  __experimentalText as Text
} from '@wordpress/components';
import { check, warning, info, globe } from '@wordpress/icons';

/**
 * Composant pour l'analyse des liens hreflang
 */
const HreflangAnalysisSection = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisUrl, setAnalysisUrl] = useState('');
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);

  /**
   * Lance l'analyse hreflang
   */
  const handleAnalyzeHreflang = async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch('/wp-json/boss-seo/v1/technical-analysis/hreflang', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': window.bossOptimizer?.nonce || ''
        },
        body: JSON.stringify({
          url: analysisUrl || window.location.origin
        })
      });

      const data = await response.json();

      if (data.success) {
        setResults(data);
      } else {
        setError(data.message || __('Erreur lors de l\'analyse hreflang', 'boss-seo'));
      }
    } catch (err) {
      setError(__('Erreur de connexion lors de l\'analyse', 'boss-seo'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  /**
   * Retourne l'icône et la couleur selon la sévérité
   */
  const getSeverityDisplay = (severity) => {
    switch (severity) {
      case 'error':
        return { icon: warning, color: '#d63638' };
      case 'warning':
        return { icon: info, color: '#dba617' };
      default:
        return { icon: check, color: '#00a32a' };
    }
  };

  /**
   * Retourne la couleur du score
   */
  const getScoreColor = (score) => {
    if (score >= 80) return '#00a32a';
    if (score >= 60) return '#dba617';
    return '#d63638';
  };

  /**
   * Retourne le nom de la langue à partir du code
   */
  const getLanguageName = (code) => {
    const languages = {
      'en': 'English',
      'fr': 'Français',
      'es': 'Español',
      'de': 'Deutsch',
      'it': 'Italiano',
      'pt': 'Português',
      'ru': 'Русский',
      'ja': '日本語',
      'zh': '中文',
      'ar': 'العربية',
      'x-default': 'Par défaut'
    };

    return languages[code] || code.toUpperCase();
  };

  return (
    <Card>
      <CardHeader>
        <Heading level={3}>
          {__('Analyse Hreflang', 'boss-seo')}
        </Heading>
      </CardHeader>
      <CardBody>
        <Text>
          {__('Analysez les liens hreflang de votre site pour optimiser le SEO international et la géolocalisation.', 'boss-seo')}
        </Text>

        <Spacer marginY={4} />

        <Flex>
          <FlexBlock>
            <TextControl
              label={__('URL à analyser', 'boss-seo')}
              value={analysisUrl}
              onChange={setAnalysisUrl}
              placeholder={__('Laissez vide pour analyser la page d\'accueil', 'boss-seo')}
              help={__('Entrez une URL spécifique ou laissez vide pour analyser la page d\'accueil', 'boss-seo')}
            />
          </FlexBlock>
          <FlexItem>
            <Button
              isPrimary
              onClick={handleAnalyzeHreflang}
              disabled={isAnalyzing}
              style={{ marginTop: '28px' }}
            >
              {isAnalyzing ? (
                <>
                  <Spinner />
                  {__('Analyse...', 'boss-seo')}
                </>
              ) : (
                __('Analyser Hreflang', 'boss-seo')
              )}
            </Button>
          </FlexItem>
        </Flex>

        {error && (
          <Notice status="error" isDismissible={false}>
            {error}
          </Notice>
        )}

        {results && (
          <>
            <Spacer marginY={6} />
            
            {/* Score global */}
            <Card>
              <CardBody>
                <Flex align="center" justify="space-between">
                  <FlexItem>
                    <Heading level={4}>
                      {__('Score Hreflang', 'boss-seo')}
                    </Heading>
                    <Text>
                      {sprintf(__('%d liens hreflang trouvés', 'boss-seo'), results.hreflang_count)}
                    </Text>
                  </FlexItem>
                  <FlexItem>
                    <div
                      style={{
                        fontSize: '2em',
                        fontWeight: 'bold',
                        color: getScoreColor(results.score)
                      }}
                    >
                      {results.score}/100
                    </div>
                  </FlexItem>
                </Flex>
              </CardBody>
            </Card>

            <Spacer marginY={4} />

            {/* Langues détectées */}
            {results.hreflang_count > 0 && (
              <Card>
                <CardHeader>
                  <Heading level={4}>
                    {__('Langues détectées', 'boss-seo')}
                  </Heading>
                </CardHeader>
                <CardBody>
                  <div className="boss-grid boss-grid-cols-2 boss-gap-4">
                    {results.languages.map((lang, index) => (
                      <div
                        key={index}
                        className="boss-p-3 boss-border boss-border-gray-200 boss-rounded-lg"
                      >
                        <Flex align="center" gap={2}>
                          <FlexItem>
                            <div style={{ color: '#0073aa' }}>
                              {React.createElement(globe, { size: 16 })}
                            </div>
                          </FlexItem>
                          <FlexBlock>
                            <Text weight="600">
                              {getLanguageName(lang)}
                            </Text>
                            <Text variant="muted" size="small">
                              {lang}
                            </Text>
                          </FlexBlock>
                        </Flex>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}

            <Spacer marginY={4} />

            {/* Liens hreflang détaillés */}
            {results.hreflang_links && Object.keys(results.hreflang_links).length > 0 && (
              <Card>
                <CardHeader>
                  <Heading level={4}>
                    {__('Liens hreflang détaillés', 'boss-seo')}
                  </Heading>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-3">
                    {Object.entries(results.hreflang_links).map(([lang, url], index) => (
                      <div
                        key={index}
                        className="boss-p-3 boss-border boss-border-gray-200 boss-rounded-lg"
                      >
                        <Flex align="center" justify="space-between">
                          <FlexItem>
                            <Text weight="600">
                              {getLanguageName(lang)}
                            </Text>
                            <Text variant="muted" size="small">
                              {lang}
                            </Text>
                          </FlexItem>
                          <FlexBlock>
                            <Text size="small" style={{ wordBreak: 'break-all' }}>
                              {url}
                            </Text>
                          </FlexBlock>
                        </Flex>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            )}

            <Spacer marginY={4} />

            {/* Issues */}
            {results.issues && results.issues.length > 0 && (
              <Card>
                <CardHeader>
                  <Heading level={4}>
                    {__('Problèmes détectés', 'boss-seo')}
                  </Heading>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    {results.issues.map((issue, index) => {
                      const { icon, color } = getSeverityDisplay(issue.severity);
                      return (
                        <div
                          key={index}
                          className="boss-p-4 boss-border boss-border-gray-200 boss-rounded-lg"
                        >
                          <Flex align="flex-start" gap={3}>
                            <FlexItem>
                              <div style={{ color }}>
                                {React.createElement(icon, { size: 20 })}
                              </div>
                            </FlexItem>
                            <FlexBlock>
                              <Text weight="600">
                                {issue.message}
                              </Text>
                              {issue.description && (
                                <Text variant="muted" size="small">
                                  {issue.description}
                                </Text>
                              )}
                              {issue.lang && (
                                <Text size="small">
                                  <strong>{__('Langue:', 'boss-seo')}</strong> {issue.lang}
                                </Text>
                              )}
                              {issue.href && (
                                <Text size="small" style={{ wordBreak: 'break-all' }}>
                                  <strong>{__('URL:', 'boss-seo')}</strong> {issue.href}
                                </Text>
                              )}
                            </FlexBlock>
                          </Flex>
                        </div>
                      );
                    })}
                  </div>
                </CardBody>
              </Card>
            )}

            <Spacer marginY={4} />

            {/* Recommandations */}
            {results.recommendations && results.recommendations.length > 0 && (
              <Card>
                <CardHeader>
                  <Heading level={4}>
                    {__('Recommandations', 'boss-seo')}
                  </Heading>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-4">
                    {results.recommendations.map((recommendation, index) => {
                      const priorityColor = {
                        high: '#d63638',
                        medium: '#dba617',
                        low: '#00a32a'
                      }[recommendation.priority] || '#666';

                      return (
                        <div
                          key={index}
                          className="boss-p-4 boss-border boss-border-gray-200 boss-rounded-lg"
                        >
                          <Flex align="flex-start" gap={3}>
                            <FlexItem>
                              <div
                                style={{
                                  width: '4px',
                                  height: '60px',
                                  backgroundColor: priorityColor,
                                  borderRadius: '2px'
                                }}
                              />
                            </FlexItem>
                            <FlexBlock>
                              <Text weight="600">
                                {recommendation.title}
                              </Text>
                              <Text variant="muted" size="small">
                                {recommendation.description}
                              </Text>
                              <Text size="small">
                                <strong>{__('Action:', 'boss-seo')}</strong> {recommendation.action}
                              </Text>
                              <Text size="small" style={{ color: priorityColor }}>
                                <strong>{__('Priorité:', 'boss-seo')}</strong> {
                                  recommendation.priority === 'high' ? __('Haute', 'boss-seo') :
                                  recommendation.priority === 'medium' ? __('Moyenne', 'boss-seo') :
                                  __('Basse', 'boss-seo')
                                }
                              </Text>
                            </FlexBlock>
                          </Flex>
                        </div>
                      );
                    })}
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Pas de problèmes */}
            {(!results.issues || results.issues.length === 0) && results.hreflang_count > 0 && (
              <Notice status="success" isDismissible={false}>
                {__('Excellent ! Vos liens hreflang sont correctement configurés.', 'boss-seo')}
              </Notice>
            )}

            {/* Aucun hreflang trouvé */}
            {results.hreflang_count === 0 && (
              <Notice status="info" isDismissible={false}>
                {__('Aucun lien hreflang détecté. Si votre site est multilingue, considérez l\'ajout de liens hreflang.', 'boss-seo')}
              </Notice>
            )}
          </>
        )}
      </CardBody>
    </Card>
  );
};

export default HreflangAnalysisSection;
