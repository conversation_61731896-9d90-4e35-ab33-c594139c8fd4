{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_compose", "_i18n", "_circularOptionPickerContext", "_v", "_circularOptionPickerOption", "_circularOptionPickerOptionGroup", "_circularOptionPickerActions", "ListboxCircularOptionPicker", "props", "actions", "options", "baseId", "className", "loop", "children", "additionalProps", "compositeStore", "useCompositeStore", "focusLoop", "rtl", "isRTL", "compositeContext", "_react", "createElement", "CircularOptionPickerContext", "Provider", "value", "Composite", "id", "store", "role", "ButtonsCircularOptionPicker", "CircularOptionPicker", "asButtons", "actionsProp", "optionsProp", "useInstanceId", "OptionPickerImplementation", "undefined", "classnames", "Option", "OptionGroup", "ButtonAction", "DropdownLinkAction", "_default", "exports", "default"], "sources": ["@wordpress/components/src/circular-option-picker/circular-option-picker.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { isRTL } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { CircularOptionPickerContext } from './circular-option-picker-context';\nimport { Composite, useCompositeStore } from '../composite/v2';\nimport type {\n\tCircularOptionPickerProps,\n\tListboxCircularOptionPickerProps,\n\tButtonsCircularOptionPickerProps,\n} from './types';\nimport { Option } from './circular-option-picker-option';\nimport { OptionGroup } from './circular-option-picker-option-group';\nimport {\n\tButtonAction,\n\tDropdownLinkAction,\n} from './circular-option-picker-actions';\n\n/**\n *`CircularOptionPicker` is a component that displays a set of options as circular buttons.\n *\n * ```jsx\n * import { CircularOptionPicker } from '../circular-option-picker';\n * import { useState } from '@wordpress/element';\n *\n * const Example = () => {\n * \tconst [ currentColor, setCurrentColor ] = useState();\n * \tconst colors = [\n * \t\t{ color: '#f00', name: 'Red' },\n * \t\t{ color: '#0f0', name: 'Green' },\n * \t\t{ color: '#00f', name: 'Blue' },\n * \t];\n * \tconst colorOptions = (\n * \t\t<>\n * \t\t\t{ colors.map( ( { color, name }, index ) => {\n * \t\t\t\treturn (\n * \t\t\t\t\t<CircularOptionPicker.Option\n * \t\t\t\t\t\tkey={ `${ color }-${ index }` }\n * \t\t\t\t\t\ttooltipText={ name }\n * \t\t\t\t\t\tstyle={ { backgroundColor: color, color } }\n * \t\t\t\t\t\tisSelected={ index === currentColor }\n * \t\t\t\t\t\tonClick={ () => setCurrentColor( index ) }\n * \t\t\t\t\t\taria-label={ name }\n * \t\t\t\t\t/>\n * \t\t\t\t);\n * \t\t\t} ) }\n * \t\t</>\n * \t);\n * \treturn (\n * \t\t<CircularOptionPicker\n * \t\t\t\toptions={ colorOptions }\n * \t\t\t\tactions={\n * \t\t\t\t\t<CircularOptionPicker.ButtonAction\n * \t\t\t\t\t\tonClick={ () => setCurrentColor( undefined ) }\n * \t\t\t\t\t>\n * \t\t\t\t\t\t{ 'Clear' }\n * \t\t\t\t\t</CircularOptionPicker.ButtonAction>\n * \t\t\t\t}\n * \t\t\t/>\n * \t);\n * };\n * ```\n */\n\nfunction ListboxCircularOptionPicker(\n\tprops: ListboxCircularOptionPickerProps\n) {\n\tconst {\n\t\tactions,\n\t\toptions,\n\t\tbaseId,\n\t\tclassName,\n\t\tloop = true,\n\t\tchildren,\n\t\t...additionalProps\n\t} = props;\n\n\tconst compositeStore = useCompositeStore( {\n\t\tfocusLoop: loop,\n\t\trtl: isRTL(),\n\t} );\n\n\tconst compositeContext = {\n\t\tbaseId,\n\t\tcompositeStore,\n\t};\n\n\treturn (\n\t\t<div className={ className }>\n\t\t\t<CircularOptionPickerContext.Provider value={ compositeContext }>\n\t\t\t\t<Composite\n\t\t\t\t\t{ ...additionalProps }\n\t\t\t\t\tid={ baseId }\n\t\t\t\t\tstore={ compositeStore }\n\t\t\t\t\trole={ 'listbox' }\n\t\t\t\t>\n\t\t\t\t\t{ options }\n\t\t\t\t</Composite>\n\t\t\t\t{ children }\n\t\t\t\t{ actions }\n\t\t\t</CircularOptionPickerContext.Provider>\n\t\t</div>\n\t);\n}\n\nfunction ButtonsCircularOptionPicker(\n\tprops: ButtonsCircularOptionPickerProps\n) {\n\tconst { actions, options, children, baseId, ...additionalProps } = props;\n\n\treturn (\n\t\t<div { ...additionalProps } id={ baseId }>\n\t\t\t<CircularOptionPickerContext.Provider value={ { baseId } }>\n\t\t\t\t{ options }\n\t\t\t\t{ children }\n\t\t\t\t{ actions }\n\t\t\t</CircularOptionPickerContext.Provider>\n\t\t</div>\n\t);\n}\n\nfunction CircularOptionPicker( props: CircularOptionPickerProps ) {\n\tconst {\n\t\tasButtons,\n\t\tactions: actionsProp,\n\t\toptions: optionsProp,\n\t\tchildren,\n\t\tclassName,\n\t\t...additionalProps\n\t} = props;\n\n\tconst baseId = useInstanceId(\n\t\tCircularOptionPicker,\n\t\t'components-circular-option-picker',\n\t\tadditionalProps.id\n\t);\n\n\tconst OptionPickerImplementation = asButtons\n\t\t? ButtonsCircularOptionPicker\n\t\t: ListboxCircularOptionPicker;\n\n\tconst actions = actionsProp ? (\n\t\t<div className=\"components-circular-option-picker__custom-clear-wrapper\">\n\t\t\t{ actionsProp }\n\t\t</div>\n\t) : undefined;\n\n\tconst options = (\n\t\t<div className={ 'components-circular-option-picker__swatches' }>\n\t\t\t{ optionsProp }\n\t\t</div>\n\t);\n\n\treturn (\n\t\t<OptionPickerImplementation\n\t\t\t{ ...additionalProps }\n\t\t\tbaseId={ baseId }\n\t\t\tclassName={ classnames(\n\t\t\t\t'components-circular-option-picker',\n\t\t\t\tclassName\n\t\t\t) }\n\t\t\tactions={ actions }\n\t\t\toptions={ options }\n\t\t>\n\t\t\t{ children }\n\t\t</OptionPickerImplementation>\n\t);\n}\n\nCircularOptionPicker.Option = Option;\nCircularOptionPicker.OptionGroup = OptionGroup;\nCircularOptionPicker.ButtonAction = ButtonAction;\nCircularOptionPicker.DropdownLinkAction = DropdownLinkAction;\n\nexport default CircularOptionPicker;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAKA,IAAAG,4BAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AAMA,IAAAK,2BAAA,GAAAL,OAAA;AACA,IAAAM,gCAAA,GAAAN,OAAA;AACA,IAAAO,4BAAA,GAAAP,OAAA;AAvBA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASQ,2BAA2BA,CACnCC,KAAuC,EACtC;EACD,MAAM;IACLC,OAAO;IACPC,OAAO;IACPC,MAAM;IACNC,SAAS;IACTC,IAAI,GAAG,IAAI;IACXC,QAAQ;IACR,GAAGC;EACJ,CAAC,GAAGP,KAAK;EAET,MAAMQ,cAAc,GAAG,IAAAC,oBAAiB,EAAE;IACzCC,SAAS,EAAEL,IAAI;IACfM,GAAG,EAAE,IAAAC,WAAK,EAAC;EACZ,CAAE,CAAC;EAEH,MAAMC,gBAAgB,GAAG;IACxBV,MAAM;IACNK;EACD,CAAC;EAED,OACC,IAAAM,MAAA,CAAAC,aAAA;IAAKX,SAAS,EAAGA;EAAW,GAC3B,IAAAU,MAAA,CAAAC,aAAA,EAACrB,4BAAA,CAAAsB,2BAA2B,CAACC,QAAQ;IAACC,KAAK,EAAGL;EAAkB,GAC/D,IAAAC,MAAA,CAAAC,aAAA,EAACpB,EAAA,CAAAwB,SAAS;IAAA,GACJZ,eAAe;IACpBa,EAAE,EAAGjB,MAAQ;IACbkB,KAAK,EAAGb,cAAgB;IACxBc,IAAI,EAAG;EAAW,GAEhBpB,OACQ,CAAC,EACVI,QAAQ,EACRL,OACmC,CAClC,CAAC;AAER;AAEA,SAASsB,2BAA2BA,CACnCvB,KAAuC,EACtC;EACD,MAAM;IAAEC,OAAO;IAAEC,OAAO;IAAEI,QAAQ;IAAEH,MAAM;IAAE,GAAGI;EAAgB,CAAC,GAAGP,KAAK;EAExE,OACC,IAAAc,MAAA,CAAAC,aAAA;IAAA,GAAUR,eAAe;IAAGa,EAAE,EAAGjB;EAAQ,GACxC,IAAAW,MAAA,CAAAC,aAAA,EAACrB,4BAAA,CAAAsB,2BAA2B,CAACC,QAAQ;IAACC,KAAK,EAAG;MAAEf;IAAO;EAAG,GACvDD,OAAO,EACPI,QAAQ,EACRL,OACmC,CAClC,CAAC;AAER;AAEA,SAASuB,oBAAoBA,CAAExB,KAAgC,EAAG;EACjE,MAAM;IACLyB,SAAS;IACTxB,OAAO,EAAEyB,WAAW;IACpBxB,OAAO,EAAEyB,WAAW;IACpBrB,QAAQ;IACRF,SAAS;IACT,GAAGG;EACJ,CAAC,GAAGP,KAAK;EAET,MAAMG,MAAM,GAAG,IAAAyB,sBAAa,EAC3BJ,oBAAoB,EACpB,mCAAmC,EACnCjB,eAAe,CAACa,EACjB,CAAC;EAED,MAAMS,0BAA0B,GAAGJ,SAAS,GACzCF,2BAA2B,GAC3BxB,2BAA2B;EAE9B,MAAME,OAAO,GAAGyB,WAAW,GAC1B,IAAAZ,MAAA,CAAAC,aAAA;IAAKX,SAAS,EAAC;EAAyD,GACrEsB,WACE,CAAC,GACHI,SAAS;EAEb,MAAM5B,OAAO,GACZ,IAAAY,MAAA,CAAAC,aAAA;IAAKX,SAAS,EAAG;EAA+C,GAC7DuB,WACE,CACL;EAED,OACC,IAAAb,MAAA,CAAAC,aAAA,EAACc,0BAA0B;IAAA,GACrBtB,eAAe;IACpBJ,MAAM,EAAGA,MAAQ;IACjBC,SAAS,EAAG,IAAA2B,mBAAU,EACrB,mCAAmC,EACnC3B,SACD,CAAG;IACHH,OAAO,EAAGA,OAAS;IACnBC,OAAO,EAAGA;EAAS,GAEjBI,QACyB,CAAC;AAE/B;AAEAkB,oBAAoB,CAACQ,MAAM,GAAGA,kCAAM;AACpCR,oBAAoB,CAACS,WAAW,GAAGA,4CAAW;AAC9CT,oBAAoB,CAACU,YAAY,GAAGA,yCAAY;AAChDV,oBAAoB,CAACW,kBAAkB,GAAGA,+CAAkB;AAAC,IAAAC,QAAA,GAE9CZ,oBAAoB;AAAAa,OAAA,CAAAC,OAAA,GAAAF,QAAA"}