{"version": 3, "names": [], "sources": ["@wordpress/components/src/confirm-dialog/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { <PERSON>E<PERSON>, KeyboardEvent, ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { ModalProps } from '../modal/types';\n\nexport type DialogInputEvent =\n\t| Parameters< ModalProps[ 'onRequestClose' ] >[ 0 ]\n\t| KeyboardEvent< HTMLDivElement >\n\t| MouseEvent< HTMLButtonElement >;\n\nexport type ConfirmDialogProps = {\n\t/**\n\t * The actual message for the dialog. It's passed as children and any valid `ReactNode` is accepted.\n\t */\n\tchildren: ReactNode;\n\t/**\n\t * The callback that's called when the user confirms.\n\t * A confirmation can happen when the `OK` button is clicked or when `Enter` is pressed.\n\t */\n\tonConfirm: ( event: DialogInputEvent ) => void;\n\t/**\n\t * The optional custom text to display as the confirmation button's label.\n\t */\n\tconfirmButtonText?: string;\n\t/**\n\t * The optional custom text to display as the cancellation button's label.\n\t */\n\tcancelButtonText?: string;\n\t/**\n\t * The callback that's called when the user cancels. A cancellation can happen\n\t * when the `Cancel` button is clicked, when the `ESC` key is pressed, or when\n\t * a click outside of the dialog focus is detected (i.e. in the overlay).\n\t *\n\t * It's not required if `isOpen` is not set (uncontrolled mode), as the component\n\t * will take care of closing itself, but you can still pass a callback if something\n\t * must be done upon cancelling (the component will still close itself in this case).\n\t *\n\t * If `isOpen` is set (controlled mode), then it's required, and you need to set\n\t * the state that defines `isOpen` to `false` as part of this callback if you want the\n\t * dialog to close when the user cancels.\n\t */\n\tonCancel?: ( event: DialogInputEvent ) => void;\n\t/**\n\t * Defines if the dialog is open (displayed) or closed (not rendered/displayed).\n\t * It also implicitly toggles the controlled mode if set or the uncontrolled mode if it's not set.\n\t */\n\tisOpen?: boolean;\n};\n"], "mappings": ""}