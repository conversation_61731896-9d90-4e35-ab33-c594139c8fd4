<?php
/**
 * Générateur de suggestions IA pour l'analyse technique Boss SEO.
 *
 * Cette classe utilise l'IA configurée dans les paramètres pour générer
 * des suggestions personnalisées basées sur les résultats d'analyse.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Générateur de suggestions IA pour l'analyse technique.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_AI_Suggestions_Generator {

    /**
     * Le nom du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.1.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Instance du service IA.
     *
     * @since    1.1.0
     * @access   private
     * @var      Boss_AI_Service    $ai_service    Instance du service IA.
     */
    private $ai_service;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        // Charger le service IA
        $this->load_ai_service();
    }

    /**
     * Charge le service IA configuré.
     *
     * @since    1.1.0
     */
    private function load_ai_service() {
        // Charger le service IA existant
        if ( ! class_exists( 'Boss_AI_Service' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-ai-service.php';
        }
        
        $this->ai_service = new Boss_AI_Service( $this->plugin_name, $this->version );
    }

    /**
     * Génère des suggestions IA basées sur les données d'analyse.
     *
     * @since    1.1.0
     * @param    array     $analysis_data    Données d'analyse combinées.
     * @param    string    $url              URL analysée.
     * @return   array                       Suggestions IA générées.
     */
    public function generate_suggestions( $analysis_data, $url ) {
        try {
            // Vérifier que l'IA est configurée
            if ( ! $this->is_ai_configured() ) {
                return array(
                    'error' => __( 'L\'IA n\'est pas configurée. Veuillez configurer votre API IA dans les paramètres.', 'boss-seo' ),
                    'suggestions' => array(),
                );
            }

            // Préparer le contexte pour l'IA
            $context = $this->prepare_ai_context( $analysis_data, $url );

            // Générer les suggestions par catégorie
            $suggestions = array(
                'performance' => $this->generate_performance_suggestions( $context ),
                'seo'         => $this->generate_seo_suggestions( $context ),
                'accessibility' => $this->generate_accessibility_suggestions( $context ),
                'security'    => $this->generate_security_suggestions( $context ),
                'technical'   => $this->generate_technical_suggestions( $context ),
                'priority_actions' => $this->generate_priority_actions( $context ),
                'quick_wins'  => $this->generate_quick_wins( $context ),
            );

            // Ajouter des métadonnées
            $suggestions['metadata'] = array(
                'generated_at' => current_time( 'mysql' ),
                'url'          => $url,
                'ai_model'     => $this->ai_service->get_current_model(),
                'total_suggestions' => $this->count_total_suggestions( $suggestions ),
            );

            return $suggestions;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la génération des suggestions IA: ' . $e->getMessage() );
            
            return array(
                'error' => __( 'Erreur lors de la génération des suggestions IA: ', 'boss-seo' ) . $e->getMessage(),
                'suggestions' => array(),
            );
        }
    }

    /**
     * Prépare le contexte pour l'IA.
     *
     * @since    1.1.0
     * @param    array     $analysis_data    Données d'analyse.
     * @param    string    $url              URL analysée.
     * @return   array                       Contexte préparé.
     */
    private function prepare_ai_context( $analysis_data, $url ) {
        // Extraire les informations clés
        $pagespeed_scores = $analysis_data['pagespeed_data']['scores'] ?? array();
        $core_web_vitals = $analysis_data['pagespeed_data']['core_web_vitals'] ?? array();
        $opportunities = $analysis_data['pagespeed_data']['opportunities'] ?? array();
        $diagnostics = $analysis_data['pagespeed_data']['diagnostics'] ?? array();
        
        $meta_tags = $analysis_data['technical_data']['meta_tags'] ?? array();
        $headings = $analysis_data['technical_data']['headings'] ?? array();
        $images = $analysis_data['technical_data']['images'] ?? array();
        $links = $analysis_data['technical_data']['links'] ?? array();

        // Identifier les problèmes principaux
        $main_issues = array();
        
        // Problèmes de performance
        if ( isset( $pagespeed_scores['performance'] ) && $pagespeed_scores['performance'] < 50 ) {
            $main_issues[] = 'performance_critique';
        }
        
        // Problèmes SEO
        if ( isset( $pagespeed_scores['seo'] ) && $pagespeed_scores['seo'] < 80 ) {
            $main_issues[] = 'seo_problemes';
        }
        
        // Problèmes de meta tags
        if ( isset( $meta_tags['title']['status'] ) && $meta_tags['title']['status'] === 'missing' ) {
            $main_issues[] = 'titre_manquant';
        }
        
        if ( isset( $meta_tags['description']['status'] ) && $meta_tags['description']['status'] === 'missing' ) {
            $main_issues[] = 'description_manquante';
        }

        // Préparer le contexte structuré
        $context = array(
            'url' => $url,
            'domain' => parse_url( $url, PHP_URL_HOST ),
            'scores' => $pagespeed_scores,
            'core_web_vitals' => $core_web_vitals,
            'main_issues' => $main_issues,
            'opportunities_count' => count( $opportunities ),
            'diagnostics_count' => count( $diagnostics ),
            'meta_status' => array(
                'title' => $meta_tags['title']['status'] ?? 'unknown',
                'description' => $meta_tags['description']['status'] ?? 'unknown',
                'title_length' => $meta_tags['title']['length'] ?? 0,
                'description_length' => $meta_tags['description']['length'] ?? 0,
            ),
            'content_analysis' => array(
                'headings_count' => count( $headings ),
                'images_count' => count( $images ),
                'links_count' => count( $links ),
            ),
            'site_type' => $this->detect_site_type( $url ),
            'industry' => $this->detect_industry( $url ),
        );

        return $context;
    }

    /**
     * Génère des suggestions de performance.
     *
     * @since    1.1.0
     * @param    array    $context    Contexte d'analyse.
     * @return   array                Suggestions de performance.
     */
    private function generate_performance_suggestions( $context ) {
        $performance_score = $context['scores']['performance'] ?? 0;
        $core_web_vitals = $context['core_web_vitals'];

        $prompt = $this->build_performance_prompt( $context, $performance_score, $core_web_vitals );
        
        try {
            $ai_response = $this->ai_service->generate_content( $prompt );
            return $this->parse_ai_suggestions( $ai_response, 'performance' );
        } catch ( Exception $e ) {
            return $this->get_fallback_performance_suggestions( $context );
        }
    }

    /**
     * Génère des suggestions SEO.
     *
     * @since    1.1.0
     * @param    array    $context    Contexte d'analyse.
     * @return   array                Suggestions SEO.
     */
    private function generate_seo_suggestions( $context ) {
        $seo_score = $context['scores']['seo'] ?? 0;
        $meta_status = $context['meta_status'];

        $prompt = $this->build_seo_prompt( $context, $seo_score, $meta_status );
        
        try {
            $ai_response = $this->ai_service->generate_content( $prompt );
            return $this->parse_ai_suggestions( $ai_response, 'seo' );
        } catch ( Exception $e ) {
            return $this->get_fallback_seo_suggestions( $context );
        }
    }

    /**
     * Construit le prompt pour les suggestions de performance.
     *
     * @since    1.1.0
     * @param    array    $context             Contexte d'analyse.
     * @param    int      $performance_score   Score de performance.
     * @param    array    $core_web_vitals     Core Web Vitals.
     * @return   string                        Prompt construit.
     */
    private function build_performance_prompt( $context, $performance_score, $core_web_vitals ) {
        $prompt = "En tant qu'expert SEO technique, analysez les données de performance suivantes et fournissez 3-5 suggestions concrètes et actionnables pour améliorer les performances du site.\n\n";
        
        $prompt .= "DONNÉES D'ANALYSE:\n";
        $prompt .= "- URL: {$context['url']}\n";
        $prompt .= "- Score de performance: {$performance_score}/100\n";
        $prompt .= "- Type de site: {$context['site_type']}\n";
        $prompt .= "- Secteur: {$context['industry']}\n\n";

        if ( ! empty( $core_web_vitals ) ) {
            $prompt .= "CORE WEB VITALS:\n";
            foreach ( $core_web_vitals as $metric => $data ) {
                $value = $data['value'] ?? 'N/A';
                $unit = $data['unit'] ?? '';
                $status = $data['status'] ?? 'unknown';
                $prompt .= "- {$metric}: {$value}{$unit} ({$status})\n";
            }
            $prompt .= "\n";
        }

        $prompt .= "INSTRUCTIONS:\n";
        $prompt .= "1. Fournissez des suggestions spécifiques et techniques\n";
        $prompt .= "2. Priorisez les actions par impact/effort\n";
        $prompt .= "3. Incluez des métriques cibles quand possible\n";
        $prompt .= "4. Adaptez les conseils au type de site et secteur\n";
        $prompt .= "5. Répondez en français avec un format structuré\n\n";

        $prompt .= "FORMAT DE RÉPONSE:\n";
        $prompt .= "Titre: [Titre de la suggestion]\n";
        $prompt .= "Priorité: [Haute/Moyenne/Basse]\n";
        $prompt .= "Impact: [Description de l'impact attendu]\n";
        $prompt .= "Action: [Étapes concrètes à suivre]\n";
        $prompt .= "Métrique: [Amélioration attendue]\n";
        $prompt .= "---\n";

        return $prompt;
    }

    /**
     * Construit le prompt pour les suggestions SEO.
     *
     * @since    1.1.0
     * @param    array    $context       Contexte d'analyse.
     * @param    int      $seo_score     Score SEO.
     * @param    array    $meta_status   Statut des balises meta.
     * @return   string                  Prompt construit.
     */
    private function build_seo_prompt( $context, $seo_score, $meta_status ) {
        $prompt = "En tant qu'expert SEO, analysez les données suivantes et fournissez 3-5 suggestions concrètes pour améliorer le référencement naturel.\n\n";
        
        $prompt .= "DONNÉES D'ANALYSE:\n";
        $prompt .= "- URL: {$context['url']}\n";
        $prompt .= "- Score SEO: {$seo_score}/100\n";
        $prompt .= "- Type de site: {$context['site_type']}\n";
        $prompt .= "- Secteur: {$context['industry']}\n\n";

        $prompt .= "BALISES META:\n";
        $prompt .= "- Titre: {$meta_status['title']} (longueur: {$meta_status['title_length']} caractères)\n";
        $prompt .= "- Description: {$meta_status['description']} (longueur: {$meta_status['description_length']} caractères)\n\n";

        $prompt .= "CONTENU:\n";
        $prompt .= "- Nombre de titres: {$context['content_analysis']['headings_count']}\n";
        $prompt .= "- Nombre d'images: {$context['content_analysis']['images_count']}\n";
        $prompt .= "- Nombre de liens: {$context['content_analysis']['links_count']}\n\n";

        $prompt .= "INSTRUCTIONS:\n";
        $prompt .= "1. Fournissez des suggestions SEO spécifiques et actionnables\n";
        $prompt .= "2. Priorisez les optimisations par impact SEO\n";
        $prompt .= "3. Adaptez les conseils au secteur d'activité\n";
        $prompt .= "4. Incluez des bonnes pratiques modernes\n";
        $prompt .= "5. Répondez en français avec un format structuré\n\n";

        $prompt .= "FORMAT DE RÉPONSE:\n";
        $prompt .= "Titre: [Titre de la suggestion]\n";
        $prompt .= "Priorité: [Haute/Moyenne/Basse]\n";
        $prompt .= "Impact: [Impact SEO attendu]\n";
        $prompt .= "Action: [Étapes concrètes à suivre]\n";
        $prompt .= "Conseil: [Conseil d'expert supplémentaire]\n";
        $prompt .= "---\n";

        return $prompt;
    }

    /**
     * Parse les suggestions IA et les structure.
     *
     * @since    1.1.0
     * @param    string    $ai_response    Réponse de l'IA.
     * @param    string    $category       Catégorie des suggestions.
     * @return   array                     Suggestions structurées.
     */
    private function parse_ai_suggestions( $ai_response, $category ) {
        $suggestions = array();
        
        // Diviser la réponse en suggestions individuelles
        $raw_suggestions = explode( '---', $ai_response );
        
        foreach ( $raw_suggestions as $index => $raw_suggestion ) {
            $raw_suggestion = trim( $raw_suggestion );
            if ( empty( $raw_suggestion ) ) {
                continue;
            }
            
            $suggestion = $this->parse_single_suggestion( $raw_suggestion, $category, $index );
            if ( $suggestion ) {
                $suggestions[] = $suggestion;
            }
        }
        
        return $suggestions;
    }

    /**
     * Parse une suggestion individuelle.
     *
     * @since    1.1.0
     * @param    string    $raw_suggestion    Suggestion brute.
     * @param    string    $category          Catégorie.
     * @param    int       $index             Index de la suggestion.
     * @return   array|null                   Suggestion structurée ou null.
     */
    private function parse_single_suggestion( $raw_suggestion, $category, $index ) {
        $lines = explode( "\n", $raw_suggestion );
        $suggestion = array(
            'id'       => $category . '_ai_' . ( $index + 1 ),
            'category' => $category,
            'source'   => 'ai',
            'title'    => '',
            'priority' => 'medium',
            'impact'   => '',
            'action'   => '',
            'details'  => array(),
        );
        
        foreach ( $lines as $line ) {
            $line = trim( $line );
            if ( empty( $line ) ) {
                continue;
            }
            
            if ( strpos( $line, 'Titre:' ) === 0 ) {
                $suggestion['title'] = trim( str_replace( 'Titre:', '', $line ) );
            } elseif ( strpos( $line, 'Priorité:' ) === 0 ) {
                $priority = strtolower( trim( str_replace( 'Priorité:', '', $line ) ) );
                $suggestion['priority'] = $this->normalize_priority( $priority );
            } elseif ( strpos( $line, 'Impact:' ) === 0 ) {
                $suggestion['impact'] = trim( str_replace( 'Impact:', '', $line ) );
            } elseif ( strpos( $line, 'Action:' ) === 0 ) {
                $suggestion['action'] = trim( str_replace( 'Action:', '', $line ) );
            } else {
                // Autres détails
                $parts = explode( ':', $line, 2 );
                if ( count( $parts ) === 2 ) {
                    $key = strtolower( trim( $parts[0] ) );
                    $value = trim( $parts[1] );
                    $suggestion['details'][ $key ] = $value;
                }
            }
        }
        
        // Valider que la suggestion a les champs requis
        if ( empty( $suggestion['title'] ) || empty( $suggestion['action'] ) ) {
            return null;
        }
        
        return $suggestion;
    }

    /**
     * Normalise la priorité.
     *
     * @since    1.1.0
     * @param    string    $priority    Priorité brute.
     * @return   string                 Priorité normalisée.
     */
    private function normalize_priority( $priority ) {
        $priority = strtolower( $priority );
        
        if ( in_array( $priority, array( 'haute', 'high', 'élevée', 'critique' ) ) ) {
            return 'high';
        } elseif ( in_array( $priority, array( 'basse', 'low', 'faible' ) ) ) {
            return 'low';
        } else {
            return 'medium';
        }
    }

    /**
     * Vérifie si l'IA est configurée.
     *
     * @since    1.1.0
     * @return   bool    True si l'IA est configurée.
     */
    private function is_ai_configured() {
        return $this->ai_service && $this->ai_service->is_configured();
    }

    /**
     * Détecte le type de site.
     *
     * @since    1.1.0
     * @param    string    $url    URL du site.
     * @return   string            Type de site détecté.
     */
    private function detect_site_type( $url ) {
        // Logique simple de détection basée sur l'URL et les plugins actifs
        if ( class_exists( 'WooCommerce' ) ) {
            return 'e-commerce';
        } elseif ( function_exists( 'is_blog' ) && is_blog() ) {
            return 'blog';
        } elseif ( strpos( $url, '/shop' ) !== false || strpos( $url, '/store' ) !== false ) {
            return 'e-commerce';
        } else {
            return 'vitrine';
        }
    }

    /**
     * Détecte le secteur d'activité.
     *
     * @since    1.1.0
     * @param    string    $url    URL du site.
     * @return   string            Secteur détecté.
     */
    private function detect_industry( $url ) {
        // Logique simple basée sur l'URL et le contenu
        $domain = parse_url( $url, PHP_URL_HOST );
        
        // Mots-clés dans le domaine
        if ( preg_match( '/restaurant|food|cuisine|chef/i', $domain ) ) {
            return 'restauration';
        } elseif ( preg_match( '/shop|store|boutique|commerce/i', $domain ) ) {
            return 'commerce';
        } elseif ( preg_match( '/tech|digital|web|dev/i', $domain ) ) {
            return 'technologie';
        } elseif ( preg_match( '/health|medical|sante/i', $domain ) ) {
            return 'santé';
        } else {
            return 'général';
        }
    }
}
