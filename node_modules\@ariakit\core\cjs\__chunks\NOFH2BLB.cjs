"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";


var _BFE5R4EZcjs = require('./BFE5R4EZ.cjs');


var _F6HPKLO2cjs = require('./F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('./KBNYGXWI.cjs');



var _AV6KTKLEcjs = require('./AV6KTKLE.cjs');

// src/menubar/menubar-store.ts
function createMenubarStore(props = {}) {
  var _a;
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const composite = _BFE5R4EZcjs.createCompositeStore.call(void 0, _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, props), {
    orientation: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.orientation,
      syncState == null ? void 0 : syncState.orientation,
      "horizontal"
    ),
    focusLoop: _KBNYGXWIcjs.defaultValue.call(void 0, props.focusLoop, syncState == null ? void 0 : syncState.focusLoop, true)
  }));
  const initialState = _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite.getState());
  const menubar = _F6HPKLO2cjs.createStore.call(void 0, initialState, composite, props.store);
  return _AV6KTKLEcjs.__spreadValues.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, composite), menubar);
}



exports.createMenubarStore = createMenubarStore;
