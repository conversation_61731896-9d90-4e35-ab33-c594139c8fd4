{"version": 3, "names": ["_reactNative", "require", "_i18n", "_element", "_compose", "_style", "_interopRequireDefault", "_colorIndicator", "_utils", "ANIMATION_DURATION", "contentWidth", "scrollPosition", "customIndicatorWidth", "ColorPalette", "enableCustomColor", "setColor", "activeColor", "isGradientColor", "defaultSettings", "currentSegment", "onCustomPress", "shouldEnableBottomSheetScroll", "shouldShowCustomIndicatorOption", "shouldShowCustomLabel", "shouldShowCustomVerticalSeparator", "customColorIndicatorStyles", "customIndicatorWrapperStyles", "label", "_defaultSettings$colo", "_defaultSettings$grad", "_defaultSettings$allC", "_defaultSettings$allG", "customSwatchGradients", "scrollViewRef", "useRef", "isIOS", "Platform", "OS", "isGradientSegment", "colorsUtils", "segments", "scale", "Animated", "Value", "current", "opacity", "mergedColors", "Set", "colors", "map", "color", "mergedGradients", "gradients", "gradient", "allAvailableColors", "allColors", "allAvailableGradients", "allGradients", "customIndicatorColor", "isCustomGradientColor", "isSelectedCustom", "shouldShowCustomIndicator", "accessibilityHint", "__", "customText", "useEffect", "scrollToEndWithDelay", "scrollTo", "x", "y", "isWithinColors", "includes", "isSelected", "timingAnimation", "property", "toValue", "timing", "duration", "easing", "Easing", "ease", "useNativeDriver", "performAnimation", "setValue", "parallel", "start", "scaleInterpolation", "interpolate", "inputRange", "outputRange", "deselectCustomGradient", "width", "Dimensions", "get", "isVisible", "getColorGradientName", "value", "fallback<PERSON><PERSON>", "sprintf", "foundColorName", "find", "name", "onColorPress", "onContentSizeChange", "delayedScroll", "setTimeout", "scrollToEnd", "clearTimeout", "onCustomIndicatorLayout", "nativeEvent", "layout", "onScroll", "contentOffset", "verticalSeparatorStyle", "usePreferredColorSchemeStyle", "styles", "verticalSeparator", "verticalSeparatorDark", "customTextStyle", "customTextAndroid", "customTextDark", "customIndicatorWrapperStyle", "customIndicatorWrapper", "_react", "createElement", "Fragment", "Text", "accessibilityRole", "style", "headerText", "ScrollView", "contentContainerStyle", "contentContainer", "horizontal", "showsHorizontalScrollIndicator", "keyboardShouldPersistTaps", "disableScrollViewPanResponder", "scrollEventThrottle", "onScrollBeginDrag", "onScrollEndDrag", "ref", "testID", "scaleValue", "colorName", "View", "key", "TouchableWithoutFeedback", "onPress", "accessibilityState", "selected", "accessibilityLabel", "transform", "default", "colorIndicator", "onLayout", "withCustomPicker", "toUpperCase", "_default", "exports"], "sources": ["@wordpress/components/src/color-palette/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tScrollView,\n\tTouchableWithoutFeedback,\n\tView,\n\tAnimated,\n\tEasing,\n\tDimensions,\n\tPlatform,\n\tText,\n} from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { __, sprintf } from '@wordpress/i18n';\nimport { useRef, useEffect } from '@wordpress/element';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport styles from './style.scss';\nimport ColorIndicator from '../color-indicator';\nimport { colorsUtils } from '../mobile/color-settings/utils';\n\nconst ANIMATION_DURATION = 200;\n\nlet contentWidth = 0;\nlet scrollPosition = 0;\nlet customIndicatorWidth = 0;\n\nfunction ColorPalette( {\n\tenableCustomColor = false,\n\tsetColor,\n\tactiveColor,\n\tisGradientColor,\n\tdefaultSettings,\n\tcurrentSegment,\n\tonCustomPress,\n\tshouldEnableBottomSheetScroll,\n\tshouldShowCustomIndicatorOption = true,\n\tshouldShowCustomLabel = true,\n\tshouldShowCustomVerticalSeparator = true,\n\tcustomColorIndicatorStyles,\n\tcustomIndicatorWrapperStyles,\n\tlabel,\n} ) {\n\tconst customSwatchGradients = [\n\t\t'linear-gradient(120deg, rgba(255,0,0,.8) 0%, rgba(255,255,255,1) 70.71%)',\n\t\t'linear-gradient(240deg, rgba(0,255,0,.8) 0%, rgba(0,255,0,0) 70.71%)',\n\t\t'linear-gradient(360deg, rgba(0,0,255,.8) 0%, rgba(0,0,255,0) 70.71%)',\n\t];\n\n\tconst scrollViewRef = useRef();\n\tconst isIOS = Platform.OS === 'ios';\n\n\tconst isGradientSegment = currentSegment === colorsUtils.segments[ 1 ];\n\n\tconst scale = useRef( new Animated.Value( 1 ) ).current;\n\tconst opacity = useRef( new Animated.Value( 1 ) ).current;\n\n\tconst mergedColors = [\n\t\t...new Set(\n\t\t\t( defaultSettings.colors ?? [] ).map( ( { color } ) => color )\n\t\t),\n\t];\n\tconst mergedGradients = [\n\t\t...new Set(\n\t\t\t( defaultSettings.gradients ?? [] ).map(\n\t\t\t\t( { gradient } ) => gradient\n\t\t\t)\n\t\t),\n\t];\n\tconst allAvailableColors = [\n\t\t...new Set(\n\t\t\t( defaultSettings.allColors ?? [] ).map( ( { color } ) => color )\n\t\t),\n\t];\n\tconst allAvailableGradients = [\n\t\t...new Set(\n\t\t\t( defaultSettings.allGradients ?? [] ).map(\n\t\t\t\t( { gradient } ) => gradient\n\t\t\t)\n\t\t),\n\t];\n\n\tconst colors = isGradientSegment ? mergedGradients : mergedColors;\n\tconst allColors = isGradientSegment\n\t\t? allAvailableGradients\n\t\t: allAvailableColors;\n\n\tconst customIndicatorColor = isGradientSegment\n\t\t? activeColor\n\t\t: customSwatchGradients;\n\tconst isCustomGradientColor = isGradientColor && isSelectedCustom();\n\tconst shouldShowCustomIndicator =\n\t\tenableCustomColor &&\n\t\tshouldShowCustomIndicatorOption &&\n\t\t( ! isGradientSegment || isCustomGradientColor );\n\n\tconst accessibilityHint = isGradientSegment\n\t\t? __( 'Navigates to customize the gradient' )\n\t\t: __( 'Navigates to custom color picker' );\n\tconst customText = __( 'Custom' );\n\n\tuseEffect( () => {\n\t\tif ( scrollViewRef.current ) {\n\t\t\tif ( isSelectedCustom() ) {\n\t\t\t\tscrollToEndWithDelay();\n\t\t\t} else {\n\t\t\t\tscrollViewRef.current.scrollTo( { x: 0, y: 0 } );\n\t\t\t}\n\t\t}\n\t\t// Temporarily disabling exhuastive-deps until the component can be refactored and updated safely.\n\t\t// Please see https://github.com/WordPress/gutenberg/pull/41253 for discussion and details.\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [ currentSegment ] );\n\n\tfunction isSelectedCustom() {\n\t\tconst isWithinColors =\n\t\t\tactiveColor && allColors?.includes( activeColor );\n\t\tif ( enableCustomColor && activeColor ) {\n\t\t\tif ( isGradientSegment ) {\n\t\t\t\treturn isGradientColor && ! isWithinColors;\n\t\t\t}\n\t\t\treturn ! isGradientColor && ! isWithinColors;\n\t\t}\n\t\treturn false;\n\t}\n\n\tfunction isSelected( color ) {\n\t\treturn ! isSelectedCustom() && activeColor === color;\n\t}\n\n\tfunction timingAnimation( property, toValue ) {\n\t\treturn Animated.timing( property, {\n\t\t\ttoValue,\n\t\t\tduration: ANIMATION_DURATION,\n\t\t\teasing: Easing.ease,\n\t\t\tuseNativeDriver: true,\n\t\t} );\n\t}\n\n\tfunction performAnimation( color ) {\n\t\tif ( ! isSelected( color ) ) {\n\t\t\topacity.setValue( 0 );\n\t\t}\n\n\t\tAnimated.parallel( [\n\t\t\ttimingAnimation( scale, 2 ),\n\t\t\ttimingAnimation( opacity, 1 ),\n\t\t] ).start( () => {\n\t\t\topacity.setValue( 1 );\n\t\t\tscale.setValue( 1 );\n\t\t} );\n\t}\n\n\tconst scaleInterpolation = scale.interpolate( {\n\t\tinputRange: [ 1, 1.5, 2 ],\n\t\toutputRange: [ 1, 0.7, 1 ],\n\t} );\n\n\tfunction deselectCustomGradient() {\n\t\tconst { width } = Dimensions.get( 'window' );\n\t\tconst isVisible =\n\t\t\tcontentWidth - scrollPosition - customIndicatorWidth < width;\n\n\t\tif ( isCustomGradientColor ) {\n\t\t\tif ( ! isIOS ) {\n\t\t\t\t// Scroll position on Android doesn't adjust automatically when removing the last item from the horizontal list.\n\t\t\t\t// https://github.com/facebook/react-native/issues/27504\n\t\t\t\t// Workaround: Force the scroll when deselecting custom gradient color and when custom indicator is visible on layout.\n\t\t\t\tif (\n\t\t\t\t\tisCustomGradientColor &&\n\t\t\t\t\tisVisible &&\n\t\t\t\t\tscrollViewRef.current\n\t\t\t\t) {\n\t\t\t\t\tscrollViewRef.current.scrollTo( {\n\t\t\t\t\t\tx: scrollPosition - customIndicatorWidth,\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction getColorGradientName( value ) {\n\t\tconst fallbackName = sprintf(\n\t\t\t/* translators: %s: the hex color value */\n\t\t\t__( 'Unlabeled color. %s' ),\n\t\t\tvalue\n\t\t);\n\t\tconst foundColorName = isGradientSegment\n\t\t\t? defaultSettings.gradients?.find(\n\t\t\t\t\t( gradient ) => gradient.gradient === value\n\t\t\t  )\n\t\t\t: defaultSettings.allColors?.find(\n\t\t\t\t\t( color ) => color.color === value\n\t\t\t  );\n\t\treturn foundColorName ? foundColorName?.name : fallbackName;\n\t}\n\n\tfunction onColorPress( color ) {\n\t\tdeselectCustomGradient();\n\t\tperformAnimation( color );\n\t\tsetColor( color );\n\t}\n\n\tfunction onContentSizeChange( width ) {\n\t\tcontentWidth = width;\n\t\tif ( isSelectedCustom() && scrollViewRef.current ) {\n\t\t\tscrollToEndWithDelay();\n\t\t}\n\t}\n\n\tfunction scrollToEndWithDelay() {\n\t\tconst delayedScroll = setTimeout( () => {\n\t\t\tscrollViewRef.current.scrollToEnd();\n\t\t}, ANIMATION_DURATION );\n\t\treturn () => {\n\t\t\tclearTimeout( delayedScroll );\n\t\t};\n\t}\n\n\tfunction onCustomIndicatorLayout( { nativeEvent } ) {\n\t\tconst { width } = nativeEvent.layout;\n\t\tif ( width !== customIndicatorWidth ) {\n\t\t\tcustomIndicatorWidth = width;\n\t\t}\n\t}\n\n\tfunction onScroll( { nativeEvent } ) {\n\t\tscrollPosition = nativeEvent.contentOffset.x;\n\t}\n\n\tconst verticalSeparatorStyle = usePreferredColorSchemeStyle(\n\t\tstyles.verticalSeparator,\n\t\tstyles.verticalSeparatorDark\n\t);\n\n\tconst customTextStyle = usePreferredColorSchemeStyle(\n\t\t[ styles.customText, ! isIOS && styles.customTextAndroid ],\n\t\tstyles.customTextDark\n\t);\n\n\tconst customIndicatorWrapperStyle = [\n\t\tstyles.customIndicatorWrapper,\n\t\tcustomIndicatorWrapperStyles,\n\t];\n\n\treturn (\n\t\t<>\n\t\t\t{ label && (\n\t\t\t\t<Text accessibilityRole=\"header\" style={ styles.headerText }>\n\t\t\t\t\t{ label }\n\t\t\t\t</Text>\n\t\t\t) }\n\n\t\t\t<ScrollView\n\t\t\t\tcontentContainerStyle={ styles.contentContainer }\n\t\t\t\thorizontal\n\t\t\t\tshowsHorizontalScrollIndicator={ false }\n\t\t\t\tkeyboardShouldPersistTaps=\"always\"\n\t\t\t\tdisableScrollViewPanResponder\n\t\t\t\tscrollEventThrottle={ 16 }\n\t\t\t\tonScroll={ onScroll }\n\t\t\t\tonContentSizeChange={ onContentSizeChange }\n\t\t\t\tonScrollBeginDrag={ () =>\n\t\t\t\t\tshouldEnableBottomSheetScroll( false )\n\t\t\t\t}\n\t\t\t\tonScrollEndDrag={ () => shouldEnableBottomSheetScroll( true ) }\n\t\t\t\tref={ scrollViewRef }\n\t\t\t\ttestID={ `color-palette${ label ? '-' + label : '' }` }\n\t\t\t>\n\t\t\t\t{ colors.map( ( color ) => {\n\t\t\t\t\tconst scaleValue = isSelected( color )\n\t\t\t\t\t\t? scaleInterpolation\n\t\t\t\t\t\t: 1;\n\t\t\t\t\tconst colorName = getColorGradientName( color );\n\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<View key={ `${ color }-${ isSelected( color ) }` }>\n\t\t\t\t\t\t\t<TouchableWithoutFeedback\n\t\t\t\t\t\t\t\tonPress={ () => onColorPress( color ) }\n\t\t\t\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\t\t\t\taccessibilityState={ {\n\t\t\t\t\t\t\t\t\tselected: isSelected( color ),\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\taccessibilityHint={ color }\n\t\t\t\t\t\t\t\taccessibilityLabel={ colorName }\n\t\t\t\t\t\t\t\ttestID={ color }\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<Animated.View\n\t\t\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\t\t\ttransform: [\n\t\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t\tscale: scaleValue,\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<ColorIndicator\n\t\t\t\t\t\t\t\t\t\tcolor={ color }\n\t\t\t\t\t\t\t\t\t\tisSelected={ isSelected( color ) }\n\t\t\t\t\t\t\t\t\t\topacity={ opacity }\n\t\t\t\t\t\t\t\t\t\tstyle={ [\n\t\t\t\t\t\t\t\t\t\t\tstyles.colorIndicator,\n\t\t\t\t\t\t\t\t\t\t\tcustomColorIndicatorStyles,\n\t\t\t\t\t\t\t\t\t\t] }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</Animated.View>\n\t\t\t\t\t\t\t</TouchableWithoutFeedback>\n\t\t\t\t\t\t</View>\n\t\t\t\t\t);\n\t\t\t\t} ) }\n\t\t\t\t{ shouldShowCustomIndicator && (\n\t\t\t\t\t<View\n\t\t\t\t\t\tstyle={ customIndicatorWrapperStyle }\n\t\t\t\t\t\tonLayout={ onCustomIndicatorLayout }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ shouldShowCustomVerticalSeparator && (\n\t\t\t\t\t\t\t<View style={ verticalSeparatorStyle } />\n\t\t\t\t\t\t) }\n\t\t\t\t\t\t<TouchableWithoutFeedback\n\t\t\t\t\t\t\tonPress={ onCustomPress }\n\t\t\t\t\t\t\taccessibilityRole={ 'button' }\n\t\t\t\t\t\t\taccessibilityState={ {\n\t\t\t\t\t\t\t\tselected: isSelectedCustom(),\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\taccessibilityHint={ accessibilityHint }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<View style={ customIndicatorWrapperStyle }>\n\t\t\t\t\t\t\t\t<ColorIndicator\n\t\t\t\t\t\t\t\t\twithCustomPicker={ ! isGradientSegment }\n\t\t\t\t\t\t\t\t\tcolor={ customIndicatorColor }\n\t\t\t\t\t\t\t\t\tisSelected={ isSelectedCustom() }\n\t\t\t\t\t\t\t\t\tstyle={ [\n\t\t\t\t\t\t\t\t\t\tstyles.colorIndicator,\n\t\t\t\t\t\t\t\t\t\tcustomColorIndicatorStyles,\n\t\t\t\t\t\t\t\t\t] }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{ shouldShowCustomLabel && (\n\t\t\t\t\t\t\t\t\t<Text style={ customTextStyle }>\n\t\t\t\t\t\t\t\t\t\t{ isIOS\n\t\t\t\t\t\t\t\t\t\t\t? customText\n\t\t\t\t\t\t\t\t\t\t\t: customText.toUpperCase() }\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t</View>\n\t\t\t\t\t\t</TouchableWithoutFeedback>\n\t\t\t\t\t</View>\n\t\t\t\t) }\n\t\t\t</ScrollView>\n\t\t</>\n\t);\n}\n\nexport default ColorPalette;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAcA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAKA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,eAAA,GAAAD,sBAAA,CAAAL,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AA1BA;AACA;AACA;;AAYA;AACA;AACA;;AAKA;AACA;AACA;;AAKA,MAAMQ,kBAAkB,GAAG,GAAG;AAE9B,IAAIC,YAAY,GAAG,CAAC;AACpB,IAAIC,cAAc,GAAG,CAAC;AACtB,IAAIC,oBAAoB,GAAG,CAAC;AAE5B,SAASC,YAAYA,CAAE;EACtBC,iBAAiB,GAAG,KAAK;EACzBC,QAAQ;EACRC,WAAW;EACXC,eAAe;EACfC,eAAe;EACfC,cAAc;EACdC,aAAa;EACbC,6BAA6B;EAC7BC,+BAA+B,GAAG,IAAI;EACtCC,qBAAqB,GAAG,IAAI;EAC5BC,iCAAiC,GAAG,IAAI;EACxCC,0BAA0B;EAC1BC,4BAA4B;EAC5BC;AACD,CAAC,EAAG;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACH,MAAMC,qBAAqB,GAAG,CAC7B,0EAA0E,EAC1E,sEAAsE,EACtE,sEAAsE,CACtE;EAED,MAAMC,aAAa,GAAG,IAAAC,eAAM,EAAC,CAAC;EAC9B,MAAMC,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;EAEnC,MAAMC,iBAAiB,GAAGnB,cAAc,KAAKoB,kBAAW,CAACC,QAAQ,CAAE,CAAC,CAAE;EAEtE,MAAMC,KAAK,GAAG,IAAAP,eAAM,EAAE,IAAIQ,qBAAQ,CAACC,KAAK,CAAE,CAAE,CAAE,CAAC,CAACC,OAAO;EACvD,MAAMC,OAAO,GAAG,IAAAX,eAAM,EAAE,IAAIQ,qBAAQ,CAACC,KAAK,CAAE,CAAE,CAAE,CAAC,CAACC,OAAO;EAEzD,MAAME,YAAY,GAAG,CACpB,GAAG,IAAIC,GAAG,CACT,EAAAnB,qBAAA,GAAEV,eAAe,CAAC8B,MAAM,cAAApB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAAGqB,GAAG,CAAE,CAAE;IAAEC;EAAM,CAAC,KAAMA,KAAM,CAC9D,CAAC,CACD;EACD,MAAMC,eAAe,GAAG,CACvB,GAAG,IAAIJ,GAAG,CACT,EAAAlB,qBAAA,GAAEX,eAAe,CAACkC,SAAS,cAAAvB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAAGoB,GAAG,CACtC,CAAE;IAAEI;EAAS,CAAC,KAAMA,QACrB,CACD,CAAC,CACD;EACD,MAAMC,kBAAkB,GAAG,CAC1B,GAAG,IAAIP,GAAG,CACT,EAAAjB,qBAAA,GAAEZ,eAAe,CAACqC,SAAS,cAAAzB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAAGmB,GAAG,CAAE,CAAE;IAAEC;EAAM,CAAC,KAAMA,KAAM,CACjE,CAAC,CACD;EACD,MAAMM,qBAAqB,GAAG,CAC7B,GAAG,IAAIT,GAAG,CACT,EAAAhB,qBAAA,GAAEb,eAAe,CAACuC,YAAY,cAAA1B,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAAGkB,GAAG,CACzC,CAAE;IAAEI;EAAS,CAAC,KAAMA,QACrB,CACD,CAAC,CACD;EAED,MAAML,MAAM,GAAGV,iBAAiB,GAAGa,eAAe,GAAGL,YAAY;EACjE,MAAMS,SAAS,GAAGjB,iBAAiB,GAChCkB,qBAAqB,GACrBF,kBAAkB;EAErB,MAAMI,oBAAoB,GAAGpB,iBAAiB,GAC3CtB,WAAW,GACXgB,qBAAqB;EACxB,MAAM2B,qBAAqB,GAAG1C,eAAe,IAAI2C,gBAAgB,CAAC,CAAC;EACnE,MAAMC,yBAAyB,GAC9B/C,iBAAiB,IACjBQ,+BAA+B,KAC7B,CAAEgB,iBAAiB,IAAIqB,qBAAqB,CAAE;EAEjD,MAAMG,iBAAiB,GAAGxB,iBAAiB,GACxC,IAAAyB,QAAE,EAAE,qCAAsC,CAAC,GAC3C,IAAAA,QAAE,EAAE,kCAAmC,CAAC;EAC3C,MAAMC,UAAU,GAAG,IAAAD,QAAE,EAAE,QAAS,CAAC;EAEjC,IAAAE,kBAAS,EAAE,MAAM;IAChB,IAAKhC,aAAa,CAACW,OAAO,EAAG;MAC5B,IAAKgB,gBAAgB,CAAC,CAAC,EAAG;QACzBM,oBAAoB,CAAC,CAAC;MACvB,CAAC,MAAM;QACNjC,aAAa,CAACW,OAAO,CAACuB,QAAQ,CAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE,CAAC;MACjD;IACD;IACA;IACA;IACA;EACD,CAAC,EAAE,CAAElD,cAAc,CAAG,CAAC;EAEvB,SAASyC,gBAAgBA,CAAA,EAAG;IAC3B,MAAMU,cAAc,GACnBtD,WAAW,IAAIuC,SAAS,EAAEgB,QAAQ,CAAEvD,WAAY,CAAC;IAClD,IAAKF,iBAAiB,IAAIE,WAAW,EAAG;MACvC,IAAKsB,iBAAiB,EAAG;QACxB,OAAOrB,eAAe,IAAI,CAAEqD,cAAc;MAC3C;MACA,OAAO,CAAErD,eAAe,IAAI,CAAEqD,cAAc;IAC7C;IACA,OAAO,KAAK;EACb;EAEA,SAASE,UAAUA,CAAEtB,KAAK,EAAG;IAC5B,OAAO,CAAEU,gBAAgB,CAAC,CAAC,IAAI5C,WAAW,KAAKkC,KAAK;EACrD;EAEA,SAASuB,eAAeA,CAAEC,QAAQ,EAAEC,OAAO,EAAG;IAC7C,OAAOjC,qBAAQ,CAACkC,MAAM,CAAEF,QAAQ,EAAE;MACjCC,OAAO;MACPE,QAAQ,EAAEpE,kBAAkB;MAC5BqE,MAAM,EAAEC,mBAAM,CAACC,IAAI;MACnBC,eAAe,EAAE;IAClB,CAAE,CAAC;EACJ;EAEA,SAASC,gBAAgBA,CAAEhC,KAAK,EAAG;IAClC,IAAK,CAAEsB,UAAU,CAAEtB,KAAM,CAAC,EAAG;MAC5BL,OAAO,CAACsC,QAAQ,CAAE,CAAE,CAAC;IACtB;IAEAzC,qBAAQ,CAAC0C,QAAQ,CAAE,CAClBX,eAAe,CAAEhC,KAAK,EAAE,CAAE,CAAC,EAC3BgC,eAAe,CAAE5B,OAAO,EAAE,CAAE,CAAC,CAC5B,CAAC,CAACwC,KAAK,CAAE,MAAM;MAChBxC,OAAO,CAACsC,QAAQ,CAAE,CAAE,CAAC;MACrB1C,KAAK,CAAC0C,QAAQ,CAAE,CAAE,CAAC;IACpB,CAAE,CAAC;EACJ;EAEA,MAAMG,kBAAkB,GAAG7C,KAAK,CAAC8C,WAAW,CAAE;IAC7CC,UAAU,EAAE,CAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAE;IACzBC,WAAW,EAAE,CAAE,CAAC,EAAE,GAAG,EAAE,CAAC;EACzB,CAAE,CAAC;EAEH,SAASC,sBAAsBA,CAAA,EAAG;IACjC,MAAM;MAAEC;IAAM,CAAC,GAAGC,uBAAU,CAACC,GAAG,CAAE,QAAS,CAAC;IAC5C,MAAMC,SAAS,GACdpF,YAAY,GAAGC,cAAc,GAAGC,oBAAoB,GAAG+E,KAAK;IAE7D,IAAKhC,qBAAqB,EAAG;MAC5B,IAAK,CAAExB,KAAK,EAAG;QACd;QACA;QACA;QACA,IACCwB,qBAAqB,IACrBmC,SAAS,IACT7D,aAAa,CAACW,OAAO,EACpB;UACDX,aAAa,CAACW,OAAO,CAACuB,QAAQ,CAAE;YAC/BC,CAAC,EAAEzD,cAAc,GAAGC;UACrB,CAAE,CAAC;QACJ;MACD;IACD;EACD;EAEA,SAASmF,oBAAoBA,CAAEC,KAAK,EAAG;IACtC,MAAMC,YAAY,GAAG,IAAAC,aAAO,GAC3B;IACA,IAAAnC,QAAE,EAAE,qBAAsB,CAAC,EAC3BiC,KACD,CAAC;IACD,MAAMG,cAAc,GAAG7D,iBAAiB,GACrCpB,eAAe,CAACkC,SAAS,EAAEgD,IAAI,CAC7B/C,QAAQ,IAAMA,QAAQ,CAACA,QAAQ,KAAK2C,KACtC,CAAC,GACD9E,eAAe,CAACqC,SAAS,EAAE6C,IAAI,CAC7BlD,KAAK,IAAMA,KAAK,CAACA,KAAK,KAAK8C,KAC7B,CAAC;IACJ,OAAOG,cAAc,GAAGA,cAAc,EAAEE,IAAI,GAAGJ,YAAY;EAC5D;EAEA,SAASK,YAAYA,CAAEpD,KAAK,EAAG;IAC9BwC,sBAAsB,CAAC,CAAC;IACxBR,gBAAgB,CAAEhC,KAAM,CAAC;IACzBnC,QAAQ,CAAEmC,KAAM,CAAC;EAClB;EAEA,SAASqD,mBAAmBA,CAAEZ,KAAK,EAAG;IACrCjF,YAAY,GAAGiF,KAAK;IACpB,IAAK/B,gBAAgB,CAAC,CAAC,IAAI3B,aAAa,CAACW,OAAO,EAAG;MAClDsB,oBAAoB,CAAC,CAAC;IACvB;EACD;EAEA,SAASA,oBAAoBA,CAAA,EAAG;IAC/B,MAAMsC,aAAa,GAAGC,UAAU,CAAE,MAAM;MACvCxE,aAAa,CAACW,OAAO,CAAC8D,WAAW,CAAC,CAAC;IACpC,CAAC,EAAEjG,kBAAmB,CAAC;IACvB,OAAO,MAAM;MACZkG,YAAY,CAAEH,aAAc,CAAC;IAC9B,CAAC;EACF;EAEA,SAASI,uBAAuBA,CAAE;IAAEC;EAAY,CAAC,EAAG;IACnD,MAAM;MAAElB;IAAM,CAAC,GAAGkB,WAAW,CAACC,MAAM;IACpC,IAAKnB,KAAK,KAAK/E,oBAAoB,EAAG;MACrCA,oBAAoB,GAAG+E,KAAK;IAC7B;EACD;EAEA,SAASoB,QAAQA,CAAE;IAAEF;EAAY,CAAC,EAAG;IACpClG,cAAc,GAAGkG,WAAW,CAACG,aAAa,CAAC5C,CAAC;EAC7C;EAEA,MAAM6C,sBAAsB,GAAG,IAAAC,qCAA4B,EAC1DC,cAAM,CAACC,iBAAiB,EACxBD,cAAM,CAACE,qBACR,CAAC;EAED,MAAMC,eAAe,GAAG,IAAAJ,qCAA4B,EACnD,CAAEC,cAAM,CAACnD,UAAU,EAAE,CAAE7B,KAAK,IAAIgF,cAAM,CAACI,iBAAiB,CAAE,EAC1DJ,cAAM,CAACK,cACR,CAAC;EAED,MAAMC,2BAA2B,GAAG,CACnCN,cAAM,CAACO,sBAAsB,EAC7BhG,4BAA4B,CAC5B;EAED,OACC,IAAAiG,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGlG,KAAK,IACN,IAAAgG,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAA8H,IAAI;IAACC,iBAAiB,EAAC,QAAQ;IAACC,KAAK,EAAGb,cAAM,CAACc;EAAY,GACzDtG,KACG,CACN,EAED,IAAAgG,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAAkI,UAAU;IACVC,qBAAqB,EAAGhB,cAAM,CAACiB,gBAAkB;IACjDC,UAAU;IACVC,8BAA8B,EAAG,KAAO;IACxCC,yBAAyB,EAAC,QAAQ;IAClCC,6BAA6B;IAC7BC,mBAAmB,EAAG,EAAI;IAC1B1B,QAAQ,EAAGA,QAAU;IACrBR,mBAAmB,EAAGA,mBAAqB;IAC3CmC,iBAAiB,EAAGA,CAAA,KACnBrH,6BAA6B,CAAE,KAAM,CACrC;IACDsH,eAAe,EAAGA,CAAA,KAAMtH,6BAA6B,CAAE,IAAK,CAAG;IAC/DuH,GAAG,EAAG3G,aAAe;IACrB4G,MAAM,EAAI,gBAAgBlH,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,EAAI;EAAG,GAErDqB,MAAM,CAACC,GAAG,CAAIC,KAAK,IAAM;IAC1B,MAAM4F,UAAU,GAAGtE,UAAU,CAAEtB,KAAM,CAAC,GACnCoC,kBAAkB,GAClB,CAAC;IACJ,MAAMyD,SAAS,GAAGhD,oBAAoB,CAAE7C,KAAM,CAAC;IAE/C,OACC,IAAAyE,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAAgJ,IAAI;MAACC,GAAG,EAAI,GAAG/F,KAAO,IAAIsB,UAAU,CAAEtB,KAAM,CAAG;IAAG,GAClD,IAAAyE,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAAkJ,wBAAwB;MACxBC,OAAO,EAAGA,CAAA,KAAM7C,YAAY,CAAEpD,KAAM,CAAG;MACvC6E,iBAAiB,EAAG,QAAU;MAC9BqB,kBAAkB,EAAG;QACpBC,QAAQ,EAAE7E,UAAU,CAAEtB,KAAM;MAC7B,CAAG;MACHY,iBAAiB,EAAGZ,KAAO;MAC3BoG,kBAAkB,EAAGP,SAAW;MAChCF,MAAM,EAAG3F;IAAO,GAEhB,IAAAyE,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAA0C,QAAQ,CAACsG,IAAI;MACbhB,KAAK,EAAG;QACPuB,SAAS,EAAE,CACV;UACC9G,KAAK,EAAEqG;QACR,CAAC;MAEH;IAAG,GAEH,IAAAnB,MAAA,CAAAC,aAAA,EAACrH,eAAA,CAAAiJ,OAAc;MACdtG,KAAK,EAAGA,KAAO;MACfsB,UAAU,EAAGA,UAAU,CAAEtB,KAAM,CAAG;MAClCL,OAAO,EAAGA,OAAS;MACnBmF,KAAK,EAAG,CACPb,cAAM,CAACsC,cAAc,EACrBhI,0BAA0B;IACxB,CACH,CACa,CACU,CACrB,CAAC;EAET,CAAE,CAAC,EACDoC,yBAAyB,IAC1B,IAAA8D,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAAgJ,IAAI;IACJhB,KAAK,EAAGP,2BAA6B;IACrCiC,QAAQ,EAAG9C;EAAyB,GAElCpF,iCAAiC,IAClC,IAAAmG,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAAgJ,IAAI;IAAChB,KAAK,EAAGf;EAAwB,CAAE,CACxC,EACD,IAAAU,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAAkJ,wBAAwB;IACxBC,OAAO,EAAG/H,aAAe;IACzB2G,iBAAiB,EAAG,QAAU;IAC9BqB,kBAAkB,EAAG;MACpBC,QAAQ,EAAEzF,gBAAgB,CAAC;IAC5B,CAAG;IACHE,iBAAiB,EAAGA;EAAmB,GAEvC,IAAA6D,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAAgJ,IAAI;IAAChB,KAAK,EAAGP;EAA6B,GAC1C,IAAAE,MAAA,CAAAC,aAAA,EAACrH,eAAA,CAAAiJ,OAAc;IACdG,gBAAgB,EAAG,CAAErH,iBAAmB;IACxCY,KAAK,EAAGQ,oBAAsB;IAC9Bc,UAAU,EAAGZ,gBAAgB,CAAC,CAAG;IACjCoE,KAAK,EAAG,CACPb,cAAM,CAACsC,cAAc,EACrBhI,0BAA0B;EACxB,CACH,CAAC,EACAF,qBAAqB,IACtB,IAAAoG,MAAA,CAAAC,aAAA,EAAC5H,YAAA,CAAA8H,IAAI;IAACE,KAAK,EAAGV;EAAiB,GAC5BnF,KAAK,GACJ6B,UAAU,GACVA,UAAU,CAAC4F,WAAW,CAAC,CACrB,CAEF,CACmB,CACrB,CAEI,CACX,CAAC;AAEL;AAAC,IAAAC,QAAA,GAEchJ,YAAY;AAAAiJ,OAAA,CAAAN,OAAA,GAAAK,QAAA"}