"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.CardMedia = void 0;
var _react = require("react");
var _context = require("../../context");
var _view = require("../../view");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedCardMedia(props, forwardedRef) {
  const cardMediaProps = (0, _hook.useCardMedia)(props);
  return (0, _react.createElement)(_view.View, {
    ...cardMediaProps,
    ref: forwardedRef
  });
}

/**
 * `CardMedia` provides a container for full-bleed content within a `Card`,
 * such as images, video, or even just a background color.
 *
 * @example
 * ```jsx
 * import { Card, CardBody, CardMedia } from '@wordpress/components';
 *
 * const Example = () => (
 *  <Card>
 *	  <CardMedia>
 *		  <img src="..." />
 *    </CardMedia>
 *    <CardBody>...</CardBody>
 *  </Card>
 * );
 * ```
 */
const CardMedia = (0, _context.contextConnect)(UnconnectedCardMedia, 'CardMedia');
exports.CardMedia = CardMedia;
var _default = CardMedia;
exports.default = _default;
//# sourceMappingURL=component.js.map