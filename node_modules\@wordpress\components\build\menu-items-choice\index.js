"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _icons = require("@wordpress/icons");
var _menuItem = _interopRequireDefault(require("../menu-item"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const noop = () => {};

/**
 * `MenuItemsChoice` functions similarly to a set of `MenuItem`s, but allows the user to select one option from a set of multiple choices.
 *
 *
 * ```jsx
 * import { MenuGroup, MenuItemsChoice } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyMenuItemsChoice = () => {
 * 	const [ mode, setMode ] = useState( 'visual' );
 * 	const choices = [
 * 		{
 * 			value: 'visual',
 * 			label: 'Visual editor',
 * 		},
 * 		{
 * 			value: 'text',
 * 			label: 'Code editor',
 * 		},
 * 	];
 *
 * 	return (
 * 		<MenuGroup label="Editor">
 * 			<MenuItemsChoice
 * 				choices={ choices }
 * 				value={ mode }
 * 				onSelect={ ( newMode ) => setMode( newMode ) }
 * 			/>
 * 		</MenuGroup>
 * 	);
 * };
 * ```
 */
function MenuItemsChoice({
  choices = [],
  onHover = noop,
  onSelect,
  value
}) {
  return (0, _react.createElement)(_react.Fragment, null, choices.map(item => {
    const isSelected = value === item.value;
    return (0, _react.createElement)(_menuItem.default, {
      key: item.value,
      role: "menuitemradio",
      disabled: item.disabled,
      icon: isSelected ? _icons.check : null,
      info: item.info,
      isSelected: isSelected,
      shortcut: item.shortcut,
      className: "components-menu-items-choice",
      onClick: () => {
        if (!isSelected) {
          onSelect(item.value);
        }
      },
      onMouseEnter: () => onHover(item.value),
      onMouseLeave: () => onHover(null),
      "aria-label": item['aria-label']
    }, item.label);
  }));
}
var _default = MenuItemsChoice;
exports.default = _default;
//# sourceMappingURL=index.js.map