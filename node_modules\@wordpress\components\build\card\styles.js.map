{"version": 3, "names": ["_react", "require", "_utils", "_EMOTION_STRINGIFIED_CSS_ERROR__", "adjustedBorderRadius", "CONFIG", "cardBorderRadius", "Card", "css", "surfaceBorderColor", "process", "env", "NODE_ENV", "exports", "Header", "name", "styles", "map", "toString", "Footer", "Content", "Body", "Media", "Divider", "borderRadius", "borderColor", "colorDivider", "boxShadowless", "borderless", "rounded", "xSmallCardPadding", "cardPaddingXSmall", "cardPaddings", "large", "cardPaddingLarge", "medium", "cardPaddingMedium", "small", "cardPaddingSmall", "xSmall", "extraSmall", "shady", "COLORS", "ui", "backgroundDisabled"], "sources": ["@wordpress/components/src/card/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\n/**\n * Internal dependencies\n */\nimport { COLORS, CONFIG } from '../utils';\n\n// Since the border for `Card` is rendered via the `box-shadow` property\n// (as opposed to the `border` property), the value of the border radius needs\n// to be adjusted by removing 1px (this is because the `box-shadow` renders\n// as an \"outer radius\").\nconst adjustedBorderRadius = `calc(${ CONFIG.cardBorderRadius } - 1px)`;\n\nexport const Card = css`\n\tbox-shadow: 0 0 0 1px ${ CONFIG.surfaceBorderColor };\n\toutline: none;\n`;\n\nexport const Header = css`\n\tborder-bottom: 1px solid;\n\tbox-sizing: border-box;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n`;\n\nexport const Footer = css`\n\tborder-top: 1px solid;\n\tbox-sizing: border-box;\n\n\t&:first-of-type {\n\t\tborder-top: none;\n\t}\n`;\n\nexport const Content = css`\n\theight: 100%;\n`;\n\nexport const Body = css`\n\tbox-sizing: border-box;\n\theight: auto;\n\tmax-height: 100%;\n`;\n\nexport const Media = css`\n\tbox-sizing: border-box;\n\toverflow: hidden;\n\n\t& > img,\n\t& > iframe {\n\t\tdisplay: block;\n\t\theight: auto;\n\t\tmax-width: 100%;\n\t\twidth: 100%;\n\t}\n`;\n\nexport const Divider = css`\n\tbox-sizing: border-box;\n\tdisplay: block;\n\twidth: 100%;\n`;\n\nexport const borderRadius = css`\n\t&:first-of-type {\n\t\tborder-top-left-radius: ${ adjustedBorderRadius };\n\t\tborder-top-right-radius: ${ adjustedBorderRadius };\n\t}\n\n\t&:last-of-type {\n\t\tborder-bottom-left-radius: ${ adjustedBorderRadius };\n\t\tborder-bottom-right-radius: ${ adjustedBorderRadius };\n\t}\n`;\n\nexport const borderColor = css`\n\tborder-color: ${ CONFIG.colorDivider };\n`;\n\nexport const boxShadowless = css`\n\tbox-shadow: none;\n`;\n\nexport const borderless = css`\n\tborder: none;\n`;\n\nexport const rounded = css`\n\tborder-radius: ${ adjustedBorderRadius };\n`;\n\nconst xSmallCardPadding = css`\n\tpadding: ${ CONFIG.cardPaddingXSmall };\n`;\n\nexport const cardPaddings = {\n\tlarge: css`\n\t\tpadding: ${ CONFIG.cardPaddingLarge };\n\t`,\n\tmedium: css`\n\t\tpadding: ${ CONFIG.cardPaddingMedium };\n\t`,\n\tsmall: css`\n\t\tpadding: ${ CONFIG.cardPaddingSmall };\n\t`,\n\txSmall: xSmallCardPadding,\n\t// The `extraSmall` size is not officially documented, but the following styles\n\t// are kept for legacy reasons to support older values of the `size` prop.\n\textraSmall: xSmallCardPadding,\n};\n\nexport const shady = css`\n\tbackground-color: ${ COLORS.ui.backgroundDisabled };\n`;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AAA0C,SAAAE,iCAAA;AAE1C;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAI,QAAQC,aAAM,CAACC,gBAAkB,SAAQ;AAEhE,MAAMC,IAAI,oBAAGC,UAAG,2BACGH,aAAM,CAACI,kBAAkB,sBAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,0CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mpHAElD;AAACC,OAAA,CAAAN,IAAA,GAAAA,IAAA;AAEK,MAAMO,MAAM,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAOlB;AAACU,OAAA,CAAAC,MAAA,GAAAA,MAAA;AAEK,MAAMK,MAAM,GAAAT,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAOlB;AAACU,OAAA,CAAAM,MAAA,GAAAA,MAAA;AAEK,MAAMC,OAAO,GAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAEnB;AAACU,OAAA,CAAAO,OAAA,GAAAA,OAAA;AAEK,MAAMC,IAAI,GAAAX,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAIhB;AAACU,OAAA,CAAAQ,IAAA,GAAAA,IAAA;AAEK,MAAMC,KAAK,GAAAZ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAWjB;AAACU,OAAA,CAAAS,KAAA,GAAAA,KAAA;AAEK,MAAMC,OAAO,GAAAb,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAInB;AAACU,OAAA,CAAAU,OAAA,GAAAA,OAAA;AAEK,MAAMC,YAAY,oBAAGhB,UAAG,6CAEFJ,oBAAoB,+BACnBA,oBAAoB,iDAIlBA,oBAAoB,kCACnBA,oBAAoB,UAAAM,OAAA,CAAAC,GAAA,CAAAC,QAAA,kDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mpHAEpD;AAACC,OAAA,CAAAW,YAAA,GAAAA,YAAA;AAEK,MAAMC,WAAW,oBAAGjB,UAAG,mBACZH,aAAM,CAACqB,YAAY,SAAAhB,OAAA,CAAAC,GAAA,CAAAC,QAAA,iDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mpHACpC;AAACC,OAAA,CAAAY,WAAA,GAAAA,WAAA;AAEK,MAAME,aAAa,GAAAjB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAEzB;AAACU,OAAA,CAAAc,aAAA,GAAAA,aAAA;AAEK,MAAMC,UAAU,GAAAlB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAG,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAf;AAAA,CAEtB;AAACU,OAAA,CAAAe,UAAA,GAAAA,UAAA;AAEK,MAAMC,OAAO,oBAAGrB,UAAG,oBACPJ,oBAAoB,SAAAM,OAAA,CAAAC,GAAA,CAAAC,QAAA,6CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mpHACtC;AAACC,OAAA,CAAAgB,OAAA,GAAAA,OAAA;AAEF,MAAMC,iBAAiB,oBAAGtB,UAAG,cAChBH,aAAM,CAAC0B,iBAAiB,SAAArB,OAAA,CAAAC,GAAA,CAAAC,QAAA,uDAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mpHACpC;AAEM,MAAMoB,YAAY,GAAG;EAC3BC,KAAK,mBAAEzB,UAAG,cACGH,aAAM,CAAC6B,gBAAgB,SAAAxB,OAAA,CAAAC,GAAA,CAAAC,QAAA,2CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,+oHACnC;EACDuB,MAAM,mBAAE3B,UAAG,cACEH,aAAM,CAAC+B,iBAAiB,SAAA1B,OAAA,CAAAC,GAAA,CAAAC,QAAA,4CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,+oHACpC;EACDyB,KAAK,mBAAE7B,UAAG,cACGH,aAAM,CAACiC,gBAAgB,SAAA5B,OAAA,CAAAC,GAAA,CAAAC,QAAA,2CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,+oHACnC;EACD2B,MAAM,EAAET,iBAAiB;EACzB;EACA;EACAU,UAAU,EAAEV;AACb,CAAC;AAACjB,OAAA,CAAAmB,YAAA,GAAAA,YAAA;AAEK,MAAMS,KAAK,oBAAGjC,UAAG,uBACFkC,aAAM,CAACC,EAAE,CAACC,kBAAkB,SAAAlC,OAAA,CAAAC,GAAA,CAAAC,QAAA,2CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,mpHACjD;AAACC,OAAA,CAAA4B,KAAA,GAAAA,KAAA"}