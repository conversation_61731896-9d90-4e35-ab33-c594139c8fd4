{"version": 3, "names": ["_reactNative", "require", "_compose", "_rippleNative", "_interopRequireDefault", "ANDROID_VERSION_LOLLIPOP", "ANDROID_VERSION_PIE", "TouchableRipple", "style", "onPress", "disabled", "disabledProp", "children", "activeOpacity", "getStylesFromColorScheme", "borderless", "touchableProps", "isTouchableNativeSupported", "Platform", "OS", "Version", "rippleColor", "rippleStyles", "ripple", "rippleDark", "backgroundColor", "useForeground", "_react", "createElement", "TouchableNativeFeedback", "background", "<PERSON><PERSON><PERSON>", "View", "overflow", "TouchableOpacity", "_default", "withPreferredColorScheme", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/ripple.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {\n\tPlatform,\n\tTouchableOpacity,\n\tTouchableNativeFeedback,\n\tView,\n} from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { withPreferredColorScheme } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport rippleStyles from './ripple.native.scss';\n\nconst ANDROID_VERSION_LOLLIPOP = 21;\nconst ANDROID_VERSION_PIE = 28;\n\nconst TouchableRipple = ( {\n\tstyle,\n\tonPress,\n\tdisabled: disabledProp,\n\tchildren,\n\tactiveOpacity,\n\tgetStylesFromColorScheme,\n\tborderless = false,\n\t...touchableProps\n} ) => {\n\tconst isTouchableNativeSupported =\n\t\tPlatform.OS === 'android' &&\n\t\tPlatform.Version >= ANDROID_VERSION_LOLLIPOP;\n\n\tconst disabled = disabledProp || ! onPress;\n\tconst rippleColor = getStylesFromColorScheme(\n\t\trippleStyles.ripple,\n\t\trippleStyles.rippleDark\n\t).backgroundColor;\n\n\tif ( isTouchableNativeSupported ) {\n\t\t// A workaround for ripple on Android P is to use useForeground + overflow: 'hidden'\n\t\t// https://github.com/facebook/react-native/issues/6480\n\t\tconst useForeground =\n\t\t\tPlatform.OS === 'android' &&\n\t\t\tPlatform.Version >= ANDROID_VERSION_PIE &&\n\t\t\tborderless;\n\n\t\treturn (\n\t\t\t<TouchableNativeFeedback\n\t\t\t\t{ ...touchableProps }\n\t\t\t\tonPress={ onPress }\n\t\t\t\tdisabled={ disabled }\n\t\t\t\tuseForeground={ useForeground }\n\t\t\t\tbackground={ TouchableNativeFeedback.Ripple(\n\t\t\t\t\trippleColor,\n\t\t\t\t\tborderless\n\t\t\t\t) }\n\t\t\t>\n\t\t\t\t<View style={ [ borderless && rippleStyles.overflow, style ] }>\n\t\t\t\t\t{ children }\n\t\t\t\t</View>\n\t\t\t</TouchableNativeFeedback>\n\t\t);\n\t}\n\n\treturn (\n\t\t<TouchableOpacity\n\t\t\t{ ...touchableProps }\n\t\t\tonPress={ onPress }\n\t\t\tdisabled={ disabled }\n\t\t\tactiveOpacity={ activeOpacity }\n\t\t\tstyle={ style }\n\t\t>\n\t\t\t{ children }\n\t\t</TouchableOpacity>\n\t);\n};\n\nexport default withPreferredColorScheme( TouchableRipple );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAUA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,aAAA,GAAAC,sBAAA,CAAAH,OAAA;AAlBA;AACA;AACA;;AAQA;AACA;AACA;;AAGA;AACA;AACA;;AAGA,MAAMI,wBAAwB,GAAG,EAAE;AACnC,MAAMC,mBAAmB,GAAG,EAAE;AAE9B,MAAMC,eAAe,GAAGA,CAAE;EACzBC,KAAK;EACLC,OAAO;EACPC,QAAQ,EAAEC,YAAY;EACtBC,QAAQ;EACRC,aAAa;EACbC,wBAAwB;EACxBC,UAAU,GAAG,KAAK;EAClB,GAAGC;AACJ,CAAC,KAAM;EACN,MAAMC,0BAA0B,GAC/BC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBD,qBAAQ,CAACE,OAAO,IAAIf,wBAAwB;EAE7C,MAAMK,QAAQ,GAAGC,YAAY,IAAI,CAAEF,OAAO;EAC1C,MAAMY,WAAW,GAAGP,wBAAwB,CAC3CQ,qBAAY,CAACC,MAAM,EACnBD,qBAAY,CAACE,UACd,CAAC,CAACC,eAAe;EAEjB,IAAKR,0BAA0B,EAAG;IACjC;IACA;IACA,MAAMS,aAAa,GAClBR,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBD,qBAAQ,CAACE,OAAO,IAAId,mBAAmB,IACvCS,UAAU;IAEX,OACC,IAAAY,MAAA,CAAAC,aAAA,EAAC5B,YAAA,CAAA6B,uBAAuB;MAAA,GAClBb,cAAc;MACnBP,OAAO,EAAGA,OAAS;MACnBC,QAAQ,EAAGA,QAAU;MACrBgB,aAAa,EAAGA,aAAe;MAC/BI,UAAU,EAAGD,oCAAuB,CAACE,MAAM,CAC1CV,WAAW,EACXN,UACD;IAAG,GAEH,IAAAY,MAAA,CAAAC,aAAA,EAAC5B,YAAA,CAAAgC,IAAI;MAACxB,KAAK,EAAG,CAAEO,UAAU,IAAIO,qBAAY,CAACW,QAAQ,EAAEzB,KAAK;IAAI,GAC3DI,QACG,CACkB,CAAC;EAE5B;EAEA,OACC,IAAAe,MAAA,CAAAC,aAAA,EAAC5B,YAAA,CAAAkC,gBAAgB;IAAA,GACXlB,cAAc;IACnBP,OAAO,EAAGA,OAAS;IACnBC,QAAQ,EAAGA,QAAU;IACrBG,aAAa,EAAGA,aAAe;IAC/BL,KAAK,EAAGA;EAAO,GAEbI,QACe,CAAC;AAErB,CAAC;AAAC,IAAAuB,QAAA,GAEa,IAAAC,iCAAwB,EAAE7B,eAAgB,CAAC;AAAA8B,OAAA,CAAAC,OAAA,GAAAH,QAAA"}