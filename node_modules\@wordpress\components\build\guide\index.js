"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _i18n = require("@wordpress/i18n");
var _modal = _interopRequireDefault(require("../modal"));
var _button = _interopRequireDefault(require("../button"));
var _pageControl = _interopRequireDefault(require("./page-control"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * `Guide` is a React component that renders a _user guide_ in a modal. The guide consists of several pages which the user can step through one by one. The guide is finished when the modal is closed or when the user clicks _Finish_ on the last page of the guide.
 *
 * ```jsx
 * function MyTutorial() {
 * 	const [ isOpen, setIsOpen ] = useState( true );
 *
 * 	if ( ! isOpen ) {
 * 		return null;
 * 	}
 *
 * 	return (
 * 		<Guide
 * 			onFinish={ () => setIsOpen( false ) }
 * 			pages={ [
 * 				{
 * 					content: <p>Welcome to the ACME Store!</p>,
 * 				},
 * 				{
 * 					image: <img src="https://acmestore.com/add-to-cart.png" />,
 * 					content: (
 * 						<p>
 * 							Click <i>Add to Cart</i> to buy a product.
 * 						</p>
 * 					),
 * 				},
 * 			] }
 * 		/>
 * 	);
 * }
 * ```
 */
function Guide({
  children,
  className,
  contentLabel,
  finishButtonText = (0, _i18n.__)('Finish'),
  onFinish,
  pages = []
}) {
  const ref = (0, _element.useRef)(null);
  const [currentPage, setCurrentPage] = (0, _element.useState)(0);
  (0, _element.useEffect)(() => {
    // Place focus at the top of the guide on mount and when the page changes.
    const frame = ref.current?.querySelector('.components-guide');
    if (frame instanceof HTMLElement) {
      frame.focus();
    }
  }, [currentPage]);
  (0, _element.useEffect)(() => {
    if (_element.Children.count(children)) {
      (0, _deprecated.default)('Passing children to <Guide>', {
        since: '5.5',
        alternative: 'the `pages` prop'
      });
    }
  }, [children]);
  if (_element.Children.count(children)) {
    var _Children$map;
    pages = (_Children$map = _element.Children.map(children, child => ({
      content: child
    }))) !== null && _Children$map !== void 0 ? _Children$map : [];
  }
  const canGoBack = currentPage > 0;
  const canGoForward = currentPage < pages.length - 1;
  const goBack = () => {
    if (canGoBack) {
      setCurrentPage(currentPage - 1);
    }
  };
  const goForward = () => {
    if (canGoForward) {
      setCurrentPage(currentPage + 1);
    }
  };
  if (pages.length === 0) {
    return null;
  }
  return (0, _react.createElement)(_modal.default, {
    className: (0, _classnames.default)('components-guide', className),
    contentLabel: contentLabel,
    isDismissible: pages.length > 1,
    onRequestClose: onFinish,
    onKeyDown: event => {
      if (event.code === 'ArrowLeft') {
        goBack();
        // Do not scroll the modal's contents.
        event.preventDefault();
      } else if (event.code === 'ArrowRight') {
        goForward();
        // Do not scroll the modal's contents.
        event.preventDefault();
      }
    },
    ref: ref
  }, (0, _react.createElement)("div", {
    className: "components-guide__container"
  }, (0, _react.createElement)("div", {
    className: "components-guide__page"
  }, pages[currentPage].image, pages.length > 1 && (0, _react.createElement)(_pageControl.default, {
    currentPage: currentPage,
    numberOfPages: pages.length,
    setCurrentPage: setCurrentPage
  }), pages[currentPage].content), (0, _react.createElement)("div", {
    className: "components-guide__footer"
  }, canGoBack && (0, _react.createElement)(_button.default, {
    className: "components-guide__back-button",
    variant: "tertiary",
    onClick: goBack
  }, (0, _i18n.__)('Previous')), canGoForward && (0, _react.createElement)(_button.default, {
    className: "components-guide__forward-button",
    variant: "primary",
    onClick: goForward
  }, (0, _i18n.__)('Next')), !canGoForward && (0, _react.createElement)(_button.default, {
    className: "components-guide__finish-button",
    variant: "primary",
    onClick: onFinish
  }, finishButtonText))));
}
var _default = Guide;
exports.default = _default;
//# sourceMappingURL=index.js.map