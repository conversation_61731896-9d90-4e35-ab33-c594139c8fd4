{"version": 3, "names": ["_reactNative", "require", "_i18n", "_element", "_compose", "_components", "_native", "_colorPalette", "_interopRequireDefault", "_colorIndicator", "_navBar", "_segmentedControl", "_utils", "_style", "HIT_SLOP", "top", "bottom", "left", "right", "PaletteScreen", "route", "useRoute", "navigation", "useNavigation", "shouldEnableBottomSheetScroll", "useContext", "BottomSheetContext", "label", "onColorChange", "onGradientChange", "onColorCleared", "colorValue", "defaultSettings", "hideNavigation", "params", "segments", "isGradient", "colorsUtils", "currentValue", "setCurrentValue", "useState", "isGradientColor", "selectedSegmentIndex", "currentSegment", "setCurrentSegment", "isGradientSegment", "currentSegmentColors", "colors", "gradients", "allAvailableColors", "useMobileGlobalStylesColors", "allAvailableGradients", "flatMap", "filter", "Boolean", "horizontalSeparatorStyle", "usePreferredColorSchemeStyle", "styles", "horizontalSeparator", "horizontalSeparatorDark", "clearButtonStyle", "clearButton", "clearButtonDark", "selectedColorTextStyle", "colorText", "colorTextDark", "isSolidSegment", "isCustomGadientShown", "setColor", "color", "onClear", "undefined", "onCustomPress", "navigate", "screens", "picker", "gradientPicker", "getClearButton", "_react", "createElement", "TouchableWithoutFeedback", "onPress", "hitSlop", "View", "style", "clearButtonContainer", "Text", "__", "getFooter", "default", "segmentHandler", "selectedIndex", "indexOf", "addonLeft", "colorIndicator", "addonRight", "footer", "flex", "maxFontSizeMultiplier", "selectable", "toUpperCase", "selectColorText", "BackButton", "goBack", "Heading", "colorPalettes", "map", "palette", "paletteKey", "paletteSettings", "allColors", "allGradients", "enableCustomColor", "name", "key", "activeColor", "Fragment", "PanelBody", "ColorControl", "withColorIndicator", "_default", "exports"], "sources": ["@wordpress/components/src/mobile/color-settings/palette.screen.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View, Text, TouchableWithoutFeedback } from 'react-native';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { useState, useContext } from '@wordpress/element';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\nimport {\n\tColorControl,\n\tPanelBody,\n\tBottomSheetContext,\n\tuseMobileGlobalStylesColors,\n} from '@wordpress/components';\nimport { useRoute, useNavigation } from '@react-navigation/native';\n\n/**\n * Internal dependencies\n */\nimport ColorPalette from '../../color-palette';\nimport ColorIndicator from '../../color-indicator';\nimport NavBar from '../bottom-sheet/nav-bar';\nimport SegmentedControls from '../segmented-control';\nimport { colorsUtils } from './utils';\n\nimport styles from './style.scss';\n\nconst HIT_SLOP = { top: 8, bottom: 8, left: 8, right: 8 };\n\nconst PaletteScreen = () => {\n\tconst route = useRoute();\n\tconst navigation = useNavigation();\n\tconst { shouldEnableBottomSheetScroll } = useContext( BottomSheetContext );\n\tconst {\n\t\tlabel,\n\t\tonColorChange,\n\t\tonGradientChange,\n\t\tonColorCleared,\n\t\tcolorValue,\n\t\tdefaultSettings,\n\t\thideNavigation = false,\n\t} = route.params || {};\n\tconst { segments, isGradient } = colorsUtils;\n\tconst [ currentValue, setCurrentValue ] = useState( colorValue );\n\tconst isGradientColor = isGradient( currentValue );\n\tconst selectedSegmentIndex = isGradientColor ? 1 : 0;\n\n\tconst [ currentSegment, setCurrentSegment ] = useState(\n\t\tsegments[ selectedSegmentIndex ]\n\t);\n\tconst isGradientSegment = currentSegment === colorsUtils.segments[ 1 ];\n\tconst currentSegmentColors = ! isGradientSegment\n\t\t? defaultSettings.colors\n\t\t: defaultSettings.gradients;\n\tconst allAvailableColors = useMobileGlobalStylesColors();\n\tconst allAvailableGradients = currentSegmentColors\n\t\t.flatMap( ( { gradients } ) => gradients )\n\t\t.filter( Boolean );\n\n\tconst horizontalSeparatorStyle = usePreferredColorSchemeStyle(\n\t\tstyles.horizontalSeparator,\n\t\tstyles.horizontalSeparatorDark\n\t);\n\tconst clearButtonStyle = usePreferredColorSchemeStyle(\n\t\tstyles.clearButton,\n\t\tstyles.clearButtonDark\n\t);\n\tconst selectedColorTextStyle = usePreferredColorSchemeStyle(\n\t\tstyles.colorText,\n\t\tstyles.colorTextDark\n\t);\n\n\tconst isSolidSegment = currentSegment === segments[ 0 ];\n\tconst isCustomGadientShown = ! isSolidSegment && isGradientColor;\n\n\tconst setColor = ( color ) => {\n\t\tsetCurrentValue( color );\n\t\tif ( isSolidSegment && onColorChange && onGradientChange ) {\n\t\t\tonColorChange( color );\n\t\t} else if ( isSolidSegment && onColorChange ) {\n\t\t\tonColorChange( color );\n\t\t} else if ( ! isSolidSegment && onGradientChange ) {\n\t\t\tonGradientChange( color );\n\t\t}\n\t};\n\n\tfunction onClear() {\n\t\tsetCurrentValue( undefined );\n\n\t\tif ( onColorCleared ) {\n\t\t\tonColorCleared();\n\t\t}\n\t}\n\n\tfunction onCustomPress() {\n\t\tif ( isSolidSegment ) {\n\t\t\tnavigation.navigate( colorsUtils.screens.picker, {\n\t\t\t\tcurrentValue,\n\t\t\t\tsetColor,\n\t\t\t} );\n\t\t} else {\n\t\t\tnavigation.navigate( colorsUtils.screens.gradientPicker, {\n\t\t\t\tsetColor,\n\t\t\t\tisGradientColor,\n\t\t\t\tcurrentValue,\n\t\t\t} );\n\t\t}\n\t}\n\n\tfunction getClearButton() {\n\t\treturn (\n\t\t\t<TouchableWithoutFeedback onPress={ onClear } hitSlop={ HIT_SLOP }>\n\t\t\t\t<View style={ styles.clearButtonContainer }>\n\t\t\t\t\t<Text style={ clearButtonStyle }>{ __( 'Reset' ) }</Text>\n\t\t\t\t</View>\n\t\t\t</TouchableWithoutFeedback>\n\t\t);\n\t}\n\n\tfunction getFooter() {\n\t\tif ( onGradientChange ) {\n\t\t\treturn (\n\t\t\t\t<SegmentedControls\n\t\t\t\t\tsegments={ segments }\n\t\t\t\t\tsegmentHandler={ setCurrentSegment }\n\t\t\t\t\tselectedIndex={ segments.indexOf( currentSegment ) }\n\t\t\t\t\taddonLeft={\n\t\t\t\t\t\tcurrentValue && (\n\t\t\t\t\t\t\t<ColorIndicator\n\t\t\t\t\t\t\t\tcolor={ currentValue }\n\t\t\t\t\t\t\t\tstyle={ styles.colorIndicator }\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t)\n\t\t\t\t\t}\n\t\t\t\t\taddonRight={ currentValue && getClearButton() }\n\t\t\t\t/>\n\t\t\t);\n\t\t}\n\t\treturn (\n\t\t\t<View style={ styles.footer }>\n\t\t\t\t<View style={ styles.flex }>\n\t\t\t\t\t{ currentValue && (\n\t\t\t\t\t\t<ColorIndicator\n\t\t\t\t\t\t\tcolor={ currentValue }\n\t\t\t\t\t\t\tstyle={ styles.colorIndicator }\n\t\t\t\t\t\t/>\n\t\t\t\t\t) }\n\t\t\t\t</View>\n\t\t\t\t{ currentValue ? (\n\t\t\t\t\t<Text\n\t\t\t\t\t\tstyle={ selectedColorTextStyle }\n\t\t\t\t\t\tmaxFontSizeMultiplier={ 2 }\n\t\t\t\t\t\tselectable\n\t\t\t\t\t>\n\t\t\t\t\t\t{ currentValue.toUpperCase() }\n\t\t\t\t\t</Text>\n\t\t\t\t) : (\n\t\t\t\t\t<Text\n\t\t\t\t\t\tstyle={ styles.selectColorText }\n\t\t\t\t\t\tmaxFontSizeMultiplier={ 2 }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ __( 'Select a color above' ) }\n\t\t\t\t\t</Text>\n\t\t\t\t) }\n\t\t\t\t<View style={ styles.flex }>\n\t\t\t\t\t{ currentValue && getClearButton() }\n\t\t\t\t</View>\n\t\t\t</View>\n\t\t);\n\t}\n\treturn (\n\t\t<View>\n\t\t\t{ ! hideNavigation && (\n\t\t\t\t<NavBar>\n\t\t\t\t\t<NavBar.BackButton onPress={ navigation.goBack } />\n\t\t\t\t\t<NavBar.Heading>{ label } </NavBar.Heading>\n\t\t\t\t</NavBar>\n\t\t\t) }\n\n\t\t\t<View style={ styles.colorPalettes }>\n\t\t\t\t{ currentSegmentColors.map( ( palette, paletteKey ) => {\n\t\t\t\t\tconst paletteSettings = {\n\t\t\t\t\t\tcolors: palette.colors,\n\t\t\t\t\t\tgradients: palette.gradients,\n\t\t\t\t\t\tallColors: allAvailableColors,\n\t\t\t\t\t\tallGradients: allAvailableGradients,\n\t\t\t\t\t};\n\t\t\t\t\t// Limit to show the custom indicator to the first available palette\n\t\t\t\t\tconst enableCustomColor = paletteKey === 0;\n\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<ColorPalette\n\t\t\t\t\t\t\tenableCustomColor={ enableCustomColor }\n\t\t\t\t\t\t\tlabel={ palette.name }\n\t\t\t\t\t\t\tkey={ paletteKey }\n\t\t\t\t\t\t\tsetColor={ setColor }\n\t\t\t\t\t\t\tactiveColor={ currentValue }\n\t\t\t\t\t\t\tisGradientColor={ isGradientColor }\n\t\t\t\t\t\t\tcurrentSegment={ currentSegment }\n\t\t\t\t\t\t\tonCustomPress={ onCustomPress }\n\t\t\t\t\t\t\tshouldEnableBottomSheetScroll={\n\t\t\t\t\t\t\t\tshouldEnableBottomSheetScroll\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdefaultSettings={ paletteSettings }\n\t\t\t\t\t\t/>\n\t\t\t\t\t);\n\t\t\t\t} ) }\n\t\t\t</View>\n\n\t\t\t{ isCustomGadientShown && (\n\t\t\t\t<>\n\t\t\t\t\t<View style={ horizontalSeparatorStyle } />\n\t\t\t\t\t<PanelBody>\n\t\t\t\t\t\t<ColorControl\n\t\t\t\t\t\t\tlabel={ __( 'Customize Gradient' ) }\n\t\t\t\t\t\t\tonPress={ onCustomPress }\n\t\t\t\t\t\t\twithColorIndicator={ false }\n\t\t\t\t\t\t/>\n\t\t\t\t\t</PanelBody>\n\t\t\t\t</>\n\t\t\t) }\n\t\t\t<View style={ horizontalSeparatorStyle } />\n\t\t\t{ getFooter() }\n\t\t</View>\n\t);\n};\n\nexport default PaletteScreen;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAMA,IAAAK,OAAA,GAAAL,OAAA;AAKA,IAAAM,aAAA,GAAAC,sBAAA,CAAAP,OAAA;AACA,IAAAQ,eAAA,GAAAD,sBAAA,CAAAP,OAAA;AACA,IAAAS,OAAA,GAAAF,sBAAA,CAAAP,OAAA;AACA,IAAAU,iBAAA,GAAAH,sBAAA,CAAAP,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AAEA,IAAAY,MAAA,GAAAL,sBAAA,CAAAP,OAAA;AA5BA;AACA;AACA;;AAGA;AACA;AACA;;AAYA;AACA;AACA;;AASA,MAAMa,QAAQ,GAAG;EAAEC,GAAG,EAAE,CAAC;EAAEC,MAAM,EAAE,CAAC;EAAEC,IAAI,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAE,CAAC;AAEzD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC3B,MAAMC,KAAK,GAAG,IAAAC,gBAAQ,EAAC,CAAC;EACxB,MAAMC,UAAU,GAAG,IAAAC,qBAAa,EAAC,CAAC;EAClC,MAAM;IAAEC;EAA8B,CAAC,GAAG,IAAAC,mBAAU,EAAEC,8BAAmB,CAAC;EAC1E,MAAM;IACLC,KAAK;IACLC,aAAa;IACbC,gBAAgB;IAChBC,cAAc;IACdC,UAAU;IACVC,eAAe;IACfC,cAAc,GAAG;EAClB,CAAC,GAAGb,KAAK,CAACc,MAAM,IAAI,CAAC,CAAC;EACtB,MAAM;IAAEC,QAAQ;IAAEC;EAAW,CAAC,GAAGC,kBAAW;EAC5C,MAAM,CAAEC,YAAY,EAAEC,eAAe,CAAE,GAAG,IAAAC,iBAAQ,EAAET,UAAW,CAAC;EAChE,MAAMU,eAAe,GAAGL,UAAU,CAAEE,YAAa,CAAC;EAClD,MAAMI,oBAAoB,GAAGD,eAAe,GAAG,CAAC,GAAG,CAAC;EAEpD,MAAM,CAAEE,cAAc,EAAEC,iBAAiB,CAAE,GAAG,IAAAJ,iBAAQ,EACrDL,QAAQ,CAAEO,oBAAoB,CAC/B,CAAC;EACD,MAAMG,iBAAiB,GAAGF,cAAc,KAAKN,kBAAW,CAACF,QAAQ,CAAE,CAAC,CAAE;EACtE,MAAMW,oBAAoB,GAAG,CAAED,iBAAiB,GAC7Cb,eAAe,CAACe,MAAM,GACtBf,eAAe,CAACgB,SAAS;EAC5B,MAAMC,kBAAkB,GAAG,IAAAC,uCAA2B,EAAC,CAAC;EACxD,MAAMC,qBAAqB,GAAGL,oBAAoB,CAChDM,OAAO,CAAE,CAAE;IAAEJ;EAAU,CAAC,KAAMA,SAAU,CAAC,CACzCK,MAAM,CAAEC,OAAQ,CAAC;EAEnB,MAAMC,wBAAwB,GAAG,IAAAC,qCAA4B,EAC5DC,cAAM,CAACC,mBAAmB,EAC1BD,cAAM,CAACE,uBACR,CAAC;EACD,MAAMC,gBAAgB,GAAG,IAAAJ,qCAA4B,EACpDC,cAAM,CAACI,WAAW,EAClBJ,cAAM,CAACK,eACR,CAAC;EACD,MAAMC,sBAAsB,GAAG,IAAAP,qCAA4B,EAC1DC,cAAM,CAACO,SAAS,EAChBP,cAAM,CAACQ,aACR,CAAC;EAED,MAAMC,cAAc,GAAGvB,cAAc,KAAKR,QAAQ,CAAE,CAAC,CAAE;EACvD,MAAMgC,oBAAoB,GAAG,CAAED,cAAc,IAAIzB,eAAe;EAEhE,MAAM2B,QAAQ,GAAKC,KAAK,IAAM;IAC7B9B,eAAe,CAAE8B,KAAM,CAAC;IACxB,IAAKH,cAAc,IAAItC,aAAa,IAAIC,gBAAgB,EAAG;MAC1DD,aAAa,CAAEyC,KAAM,CAAC;IACvB,CAAC,MAAM,IAAKH,cAAc,IAAItC,aAAa,EAAG;MAC7CA,aAAa,CAAEyC,KAAM,CAAC;IACvB,CAAC,MAAM,IAAK,CAAEH,cAAc,IAAIrC,gBAAgB,EAAG;MAClDA,gBAAgB,CAAEwC,KAAM,CAAC;IAC1B;EACD,CAAC;EAED,SAASC,OAAOA,CAAA,EAAG;IAClB/B,eAAe,CAAEgC,SAAU,CAAC;IAE5B,IAAKzC,cAAc,EAAG;MACrBA,cAAc,CAAC,CAAC;IACjB;EACD;EAEA,SAAS0C,aAAaA,CAAA,EAAG;IACxB,IAAKN,cAAc,EAAG;MACrB5C,UAAU,CAACmD,QAAQ,CAAEpC,kBAAW,CAACqC,OAAO,CAACC,MAAM,EAAE;QAChDrC,YAAY;QACZ8B;MACD,CAAE,CAAC;IACJ,CAAC,MAAM;MACN9C,UAAU,CAACmD,QAAQ,CAAEpC,kBAAW,CAACqC,OAAO,CAACE,cAAc,EAAE;QACxDR,QAAQ;QACR3B,eAAe;QACfH;MACD,CAAE,CAAC;IACJ;EACD;EAEA,SAASuC,cAAcA,CAAA,EAAG;IACzB,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAgF,wBAAwB;MAACC,OAAO,EAAGX,OAAS;MAACY,OAAO,EAAGpE;IAAU,GACjE,IAAAgE,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI;MAACC,KAAK,EAAG3B,cAAM,CAAC4B;IAAsB,GAC1C,IAAAP,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAsF,IAAI;MAACF,KAAK,EAAGxB;IAAkB,GAAG,IAAA2B,QAAE,EAAE,OAAQ,CAAS,CACnD,CACmB,CAAC;EAE7B;EAEA,SAASC,SAASA,CAAA,EAAG;IACpB,IAAK3D,gBAAgB,EAAG;MACvB,OACC,IAAAiD,MAAA,CAAAC,aAAA,EAACpE,iBAAA,CAAA8E,OAAiB;QACjBtD,QAAQ,EAAGA,QAAU;QACrBuD,cAAc,EAAG9C,iBAAmB;QACpC+C,aAAa,EAAGxD,QAAQ,CAACyD,OAAO,CAAEjD,cAAe,CAAG;QACpDkD,SAAS,EACRvD,YAAY,IACX,IAAAwC,MAAA,CAAAC,aAAA,EAACtE,eAAA,CAAAgF,OAAc;UACdpB,KAAK,EAAG/B,YAAc;UACtB8C,KAAK,EAAG3B,cAAM,CAACqC;QAAgB,CAC/B,CAEF;QACDC,UAAU,EAAGzD,YAAY,IAAIuC,cAAc,CAAC;MAAG,CAC/C,CAAC;IAEJ;IACA,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI;MAACC,KAAK,EAAG3B,cAAM,CAACuC;IAAQ,GAC5B,IAAAlB,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI;MAACC,KAAK,EAAG3B,cAAM,CAACwC;IAAM,GACxB3D,YAAY,IACb,IAAAwC,MAAA,CAAAC,aAAA,EAACtE,eAAA,CAAAgF,OAAc;MACdpB,KAAK,EAAG/B,YAAc;MACtB8C,KAAK,EAAG3B,cAAM,CAACqC;IAAgB,CAC/B,CAEG,CAAC,EACLxD,YAAY,GACb,IAAAwC,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAsF,IAAI;MACJF,KAAK,EAAGrB,sBAAwB;MAChCmC,qBAAqB,EAAG,CAAG;MAC3BC,UAAU;IAAA,GAER7D,YAAY,CAAC8D,WAAW,CAAC,CACtB,CAAC,GAEP,IAAAtB,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAsF,IAAI;MACJF,KAAK,EAAG3B,cAAM,CAAC4C,eAAiB;MAChCH,qBAAqB,EAAG;IAAG,GAEzB,IAAAX,QAAE,EAAE,sBAAuB,CACxB,CACN,EACD,IAAAT,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI;MAACC,KAAK,EAAG3B,cAAM,CAACwC;IAAM,GACxB3D,YAAY,IAAIuC,cAAc,CAAC,CAC5B,CACD,CAAC;EAET;EACA,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI,QACF,CAAElD,cAAc,IACjB,IAAA6C,MAAA,CAAAC,aAAA,EAACrE,OAAA,CAAA+E,OAAM,QACN,IAAAX,MAAA,CAAAC,aAAA,EAACrE,OAAA,CAAA+E,OAAM,CAACa,UAAU;IAACrB,OAAO,EAAG3D,UAAU,CAACiF;EAAQ,CAAE,CAAC,EACnD,IAAAzB,MAAA,CAAAC,aAAA,EAACrE,OAAA,CAAA+E,OAAM,CAACe,OAAO,QAAG7E,KAAK,EAAE,GAAiB,CACnC,CACR,EAED,IAAAmD,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI;IAACC,KAAK,EAAG3B,cAAM,CAACgD;EAAe,GACjC3D,oBAAoB,CAAC4D,GAAG,CAAE,CAAEC,OAAO,EAAEC,UAAU,KAAM;IACtD,MAAMC,eAAe,GAAG;MACvB9D,MAAM,EAAE4D,OAAO,CAAC5D,MAAM;MACtBC,SAAS,EAAE2D,OAAO,CAAC3D,SAAS;MAC5B8D,SAAS,EAAE7D,kBAAkB;MAC7B8D,YAAY,EAAE5D;IACf,CAAC;IACD;IACA,MAAM6D,iBAAiB,GAAGJ,UAAU,KAAK,CAAC;IAE1C,OACC,IAAA9B,MAAA,CAAAC,aAAA,EAACxE,aAAA,CAAAkF,OAAY;MACZuB,iBAAiB,EAAGA,iBAAmB;MACvCrF,KAAK,EAAGgF,OAAO,CAACM,IAAM;MACtBC,GAAG,EAAGN,UAAY;MAClBxC,QAAQ,EAAGA,QAAU;MACrB+C,WAAW,EAAG7E,YAAc;MAC5BG,eAAe,EAAGA,eAAiB;MACnCE,cAAc,EAAGA,cAAgB;MACjC6B,aAAa,EAAGA,aAAe;MAC/BhD,6BAA6B,EAC5BA,6BACA;MACDQ,eAAe,EAAG6E;IAAiB,CACnC,CAAC;EAEJ,CAAE,CACG,CAAC,EAEL1C,oBAAoB,IACrB,IAAAW,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAsC,QAAA,QACC,IAAAtC,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI;IAACC,KAAK,EAAG7B;EAA0B,CAAE,CAAC,EAC3C,IAAAuB,MAAA,CAAAC,aAAA,EAAC1E,WAAA,CAAAgH,SAAS,QACT,IAAAvC,MAAA,CAAAC,aAAA,EAAC1E,WAAA,CAAAiH,YAAY;IACZ3F,KAAK,EAAG,IAAA4D,QAAE,EAAE,oBAAqB,CAAG;IACpCN,OAAO,EAAGT,aAAe;IACzB+C,kBAAkB,EAAG;EAAO,CAC5B,CACS,CACV,CACF,EACD,IAAAzC,MAAA,CAAAC,aAAA,EAAC/E,YAAA,CAAAmF,IAAI;IAACC,KAAK,EAAG7B;EAA0B,CAAE,CAAC,EACzCiC,SAAS,CAAC,CACP,CAAC;AAET,CAAC;AAAC,IAAAgC,QAAA,GAEarG,aAAa;AAAAsG,OAAA,CAAAhC,OAAA,GAAA+B,QAAA"}