"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.FlexItem = void 0;
var _react = require("react");
var _context = require("../../context");
var _view = require("../../view");
var _hook = require("./hook");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function UnconnectedFlexItem(props, forwardedRef) {
  const flexItemProps = (0, _hook.useFlexItem)(props);
  return (0, _react.createElement)(_view.View, {
    ...flexItemProps,
    ref: forwardedRef
  });
}

/**
 * `FlexItem` is a primitive layout component that aligns content within layout
 * containers like `Flex`.
 *
 * ```jsx
 * import { Flex, FlexItem } from '@wordpress/components';
 *
 * function Example() {
 *   return (
 *     <Flex>
 *       <FlexItem>...</FlexItem>
 *     </Flex>
 *   );
 * }
 * ```
 */
const FlexItem = (0, _context.contextConnect)(UnconnectedFlexItem, 'FlexItem');
exports.FlexItem = FlexItem;
var _default = FlexItem;
exports.default = _default;
//# sourceMappingURL=component.js.map