"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _i18n = require("@wordpress/i18n");
var _icons = require("@wordpress/icons");
var _compose = require("@wordpress/compose");
var _cell = _interopRequireDefault(require("./cell"));
var _styles = _interopRequireDefault(require("./styles.scss"));
var _linkSuggestionStyles = _interopRequireDefault(require("./link-suggestion-styles.scss"));
var _gridicons = require("../gridicons");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const icons = {
  URL: _icons.globe,
  clipboard: _gridicons.clipboard,
  post: _gridicons.posts,
  page: _gridicons.pages
};
const getSummaryForType = type => {
  switch (type) {
    case 'clipboard':
      return (0, _i18n.__)('From clipboard');
    case 'mailto':
      return (0, _i18n.__)('Add this email link');
    case 'tel':
      return (0, _i18n.__)('Add this telephone link');
    default:
      return (0, _i18n.__)('Add this link');
  }
};

// We use some Cell styles here with a column flex-direction.
function LinkSuggestionItemCell({
  suggestion,
  onLinkPicked,
  ...props
}) {
  const {
    title: contentTitle,
    url,
    type,
    isDirectEntry
  } = suggestion;
  const title = isDirectEntry ? url : contentTitle;
  const summary = isDirectEntry ? getSummaryForType(type) : url;
  const pickLink = () => onLinkPicked(suggestion);
  const cellTitleStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.cellLabel, _styles.default.cellTextDark);
  const cellSummaryStyle = (0, _compose.usePreferredColorSchemeStyle)(_styles.default.cellValue, _styles.default.cellTextDark);
  const titleStyle = [cellTitleStyle, _linkSuggestionStyles.default.titleStyle];
  const summaryStyle = [cellSummaryStyle, _linkSuggestionStyles.default.summaryStyle];
  return (0, _react.createElement)(_cell.default, {
    ...props,
    icon: icons[type] || _gridicons.empty,
    onPress: pickLink,
    separatorType: 'none',
    cellContainerStyle: _linkSuggestionStyles.default.itemContainerStyle,
    labelStyle: _linkSuggestionStyles.default.hidden,
    valueStyle: _linkSuggestionStyles.default.hidden
  }, (0, _react.createElement)(_reactNative.View, {
    style: _linkSuggestionStyles.default.containerStyle
  }, (0, _react.createElement)(_reactNative.Text, {
    style: titleStyle,
    numberOfLines: 1,
    ellipsizeMode: 'middle'
  }, title), (0, _react.createElement)(_reactNative.Text, {
    style: summaryStyle,
    numberOfLines: 1,
    ellipsizeMode: 'middle'
  }, summary)));
}
var _default = LinkSuggestionItemCell;
exports.default = _default;
//# sourceMappingURL=link-suggestion-item-cell.native.js.map