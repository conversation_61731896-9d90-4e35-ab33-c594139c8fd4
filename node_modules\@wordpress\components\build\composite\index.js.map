{"version": 3, "names": ["_reakit", "require"], "sources": ["@wordpress/components/src/composite/index.ts"], "sourcesContent": ["/**\n * Composite is a component that may contain navigable items represented by\n * CompositeItem. It's inspired by the WAI-ARIA Composite Role and implements\n * all the keyboard navigation mechanisms to ensure that there's only one\n * tab stop for the whole Composite element. This means that it can behave as\n * a roving tabindex or aria-activedescendant container.\n *\n * @see https://reakit.io/docs/composite/\n *\n * The plan is to build own API that accounts for future breaking changes\n * in Reakit (https://github.com/WordPress/gutenberg/pull/28085).\n */\n/* eslint-disable-next-line no-restricted-imports */\nexport {\n\tComposite,\n\tCompositeGroup,\n\tCompositeItem,\n\tuseCompositeState,\n} from 'reakit';\n\n/* eslint-disable-next-line no-restricted-imports */\nexport type { CompositeStateReturn as CompositeState } from 'reakit';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAAA,OAAA,GAAAC,OAAA"}