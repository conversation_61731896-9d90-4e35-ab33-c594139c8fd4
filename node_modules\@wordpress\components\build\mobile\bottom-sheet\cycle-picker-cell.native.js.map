{"version": 3, "names": ["_cell", "_interopRequireDefault", "require", "BottomSheetCyclePickerCell", "props", "value", "options", "onChangeValue", "cellProps", "nextOptionValue", "selectedOptionIndex", "length", "findIndex", "option", "optionsContainsValue", "_react", "createElement", "default", "onPress", "editable", "name"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/cycle-picker-cell.native.js"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport Cell from './cell';\n\nexport default function BottomSheetCyclePickerCell( props ) {\n\tconst { value, options, onChangeValue, ...cellProps } = props;\n\n\tconst nextOptionValue = () => {\n\t\treturn options[ ( selectedOptionIndex + 1 ) % options.length ].value;\n\t};\n\n\tconst selectedOptionIndex = options.findIndex(\n\t\t( option ) => option.value === value\n\t);\n\tconst optionsContainsValue =\n\t\toptions.length > 0 && selectedOptionIndex !== -1;\n\n\treturn (\n\t\t<Cell\n\t\t\tonPress={ () =>\n\t\t\t\toptionsContainsValue && onChangeValue( nextOptionValue() )\n\t\t\t}\n\t\t\teditable={ false }\n\t\t\tvalue={\n\t\t\t\toptionsContainsValue && options[ selectedOptionIndex ].name\n\t\t\t}\n\t\t\t{ ...cellProps }\n\t\t/>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AAHA;AACA;AACA;;AAGe,SAASC,0BAA0BA,CAAEC,KAAK,EAAG;EAC3D,MAAM;IAAEC,KAAK;IAAEC,OAAO;IAAEC,aAAa;IAAE,GAAGC;EAAU,CAAC,GAAGJ,KAAK;EAE7D,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC7B,OAAOH,OAAO,CAAE,CAAEI,mBAAmB,GAAG,CAAC,IAAKJ,OAAO,CAACK,MAAM,CAAE,CAACN,KAAK;EACrE,CAAC;EAED,MAAMK,mBAAmB,GAAGJ,OAAO,CAACM,SAAS,CAC1CC,MAAM,IAAMA,MAAM,CAACR,KAAK,KAAKA,KAChC,CAAC;EACD,MAAMS,oBAAoB,GACzBR,OAAO,CAACK,MAAM,GAAG,CAAC,IAAID,mBAAmB,KAAK,CAAC,CAAC;EAEjD,OACC,IAAAK,MAAA,CAAAC,aAAA,EAAChB,KAAA,CAAAiB,OAAI;IACJC,OAAO,EAAGA,CAAA,KACTJ,oBAAoB,IAAIP,aAAa,CAAEE,eAAe,CAAC,CAAE,CACzD;IACDU,QAAQ,EAAG,KAAO;IAClBd,KAAK,EACJS,oBAAoB,IAAIR,OAAO,CAAEI,mBAAmB,CAAE,CAACU,IACvD;IAAA,GACIZ;EAAS,CACd,CAAC;AAEJ"}