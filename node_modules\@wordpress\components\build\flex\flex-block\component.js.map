{"version": 3, "names": ["_context", "require", "_view", "_hook", "UnconnectedFlexBlock", "props", "forwardedRef", "flexBlockProps", "useFlexBlock", "_react", "createElement", "View", "ref", "FlexBlock", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/flex/flex-block/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { View } from '../../view';\nimport type { FlexBlockProps } from '../types';\nimport { useFlexBlock } from './hook';\n\nfunction UnconnectedFlexBlock(\n\tprops: WordPressComponentProps< FlexBlockProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst flexBlockProps = useFlexBlock( props );\n\n\treturn <View { ...flexBlockProps } ref={ forwardedRef } />;\n}\n\n/**\n * `FlexBlock` is a primitive layout component that adaptively resizes content\n * within layout containers like `Flex`.\n *\n * ```jsx\n * import { Flex, FlexBlock } from '@wordpress/components';\n *\n * function Example() {\n *   return (\n *     <Flex>\n *       <FlexBlock>...</FlexBlock>\n *     </Flex>\n *   );\n * }\n * ```\n */\nexport const FlexBlock = contextConnect( UnconnectedFlexBlock, 'FlexBlock' );\n\nexport default FlexBlock;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,KAAA,GAAAF,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,oBAAoBA,CAC5BC,KAAuD,EACvDC,YAAiC,EAChC;EACD,MAAMC,cAAc,GAAG,IAAAC,kBAAY,EAAEH,KAAM,CAAC;EAE5C,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,cAAc;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,SAAS,GAAG,IAAAC,uBAAc,EAAEV,oBAAoB,EAAE,WAAY,CAAC;AAACW,OAAA,CAAAF,SAAA,GAAAA,SAAA;AAAA,IAAAG,QAAA,GAE9DH,SAAS;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}