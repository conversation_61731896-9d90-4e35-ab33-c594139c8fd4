"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "applyValueToSides", {
  enumerable: true,
  get: function () {
    return _utils2.applyValueToSides;
  }
});
exports.default = void 0;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _element = require("@wordpress/element");
var _i18n = require("@wordpress/i18n");
var _baseControl = require("../base-control");
var _allInputControl = _interopRequireDefault(require("./all-input-control"));
var _inputControls = _interopRequireDefault(require("./input-controls"));
var _axialInputControls = _interopRequireDefault(require("./axial-input-controls"));
var _linkedButton = _interopRequireDefault(require("./linked-button"));
var _grid = require("../grid");
var _boxControlStyles = require("./styles/box-control-styles");
var _utils = require("../unit-control/utils");
var _utils2 = require("./utils");
var _hooks = require("../utils/hooks");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const defaultInputProps = {
  min: 0
};
const noop = () => {};
function useUniqueId(idProp) {
  const instanceId = (0, _compose.useInstanceId)(BoxControl, 'inspector-box-control');
  return idProp || instanceId;
}

/**
 * BoxControl components let users set values for Top, Right, Bottom, and Left.
 * This can be used as an input control for values like `padding` or `margin`.
 *
 * ```jsx
 * import { __experimentalBoxControl as BoxControl } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const Example = () => {
 * 	const [ values, setValues ] = useState( {
 * 		top: '50px',
 * 		left: '10%',
 * 		right: '10%',
 * 		bottom: '50px',
 * 	} );
 *
 * 	return (
 * 		<BoxControl
 * 			values={ values }
 * 			onChange={ ( nextValues ) => setValues( nextValues ) }
 * 		/>
 * 	);
 * };
 * ```
 */
function BoxControl({
  __next40pxDefaultSize = false,
  id: idProp,
  inputProps = defaultInputProps,
  onChange = noop,
  label = (0, _i18n.__)('Box Control'),
  values: valuesProp,
  units,
  sides,
  splitOnAxis = false,
  allowReset = true,
  resetValues = _utils2.DEFAULT_VALUES,
  onMouseOver,
  onMouseOut
}) {
  const [values, setValues] = (0, _hooks.useControlledState)(valuesProp, {
    fallback: _utils2.DEFAULT_VALUES
  });
  const inputValues = values || _utils2.DEFAULT_VALUES;
  const hasInitialValue = (0, _utils2.isValuesDefined)(valuesProp);
  const hasOneSide = sides?.length === 1;
  const [isDirty, setIsDirty] = (0, _element.useState)(hasInitialValue);
  const [isLinked, setIsLinked] = (0, _element.useState)(!hasInitialValue || !(0, _utils2.isValuesMixed)(inputValues) || hasOneSide);
  const [side, setSide] = (0, _element.useState)((0, _utils2.getInitialSide)(isLinked, splitOnAxis));

  // Tracking selected units via internal state allows filtering of CSS unit
  // only values from being saved while maintaining preexisting unit selection
  // behaviour. Filtering CSS only values prevents invalid style values.
  const [selectedUnits, setSelectedUnits] = (0, _element.useState)({
    top: (0, _utils.parseQuantityAndUnitFromRawValue)(valuesProp?.top)[1],
    right: (0, _utils.parseQuantityAndUnitFromRawValue)(valuesProp?.right)[1],
    bottom: (0, _utils.parseQuantityAndUnitFromRawValue)(valuesProp?.bottom)[1],
    left: (0, _utils.parseQuantityAndUnitFromRawValue)(valuesProp?.left)[1]
  });
  const id = useUniqueId(idProp);
  const headingId = `${id}-heading`;
  const toggleLinked = () => {
    setIsLinked(!isLinked);
    setSide((0, _utils2.getInitialSide)(!isLinked, splitOnAxis));
  };
  const handleOnFocus = (_event, {
    side: nextSide
  }) => {
    setSide(nextSide);
  };
  const handleOnChange = nextValues => {
    onChange(nextValues);
    setValues(nextValues);
    setIsDirty(true);
  };
  const handleOnReset = () => {
    onChange(resetValues);
    setValues(resetValues);
    setSelectedUnits(resetValues);
    setIsDirty(false);
  };
  const inputControlProps = {
    ...inputProps,
    onChange: handleOnChange,
    onFocus: handleOnFocus,
    isLinked,
    units,
    selectedUnits,
    setSelectedUnits,
    sides,
    values: inputValues,
    onMouseOver,
    onMouseOut,
    __next40pxDefaultSize
  };
  return (0, _react.createElement)(_grid.Grid, {
    id: id,
    columns: 3,
    templateColumns: "1fr min-content min-content",
    role: "group",
    "aria-labelledby": headingId
  }, (0, _react.createElement)(_baseControl.BaseControl.VisualLabel, {
    id: headingId
  }, label), isLinked && (0, _react.createElement)(_boxControlStyles.InputWrapper, null, (0, _react.createElement)(_boxControlStyles.FlexedBoxControlIcon, {
    side: side,
    sides: sides
  }), (0, _react.createElement)(_allInputControl.default, {
    ...inputControlProps
  })), !hasOneSide && (0, _react.createElement)(_boxControlStyles.LinkedButtonWrapper, null, (0, _react.createElement)(_linkedButton.default, {
    onClick: toggleLinked,
    isLinked: isLinked
  })), !isLinked && splitOnAxis && (0, _react.createElement)(_axialInputControls.default, {
    ...inputControlProps
  }), !isLinked && !splitOnAxis && (0, _react.createElement)(_inputControls.default, {
    ...inputControlProps
  }), allowReset && (0, _react.createElement)(_boxControlStyles.ResetButton, {
    className: "component-box-control__reset-button",
    variant: "secondary",
    size: "small",
    onClick: handleOnReset,
    disabled: !isDirty
  }, (0, _i18n.__)('Reset')));
}
var _default = BoxControl;
exports.default = _default;
//# sourceMappingURL=index.js.map