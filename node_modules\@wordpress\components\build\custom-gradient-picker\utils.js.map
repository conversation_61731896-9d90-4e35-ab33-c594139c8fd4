{"version": 3, "names": ["_gradient<PERSON><PERSON>er", "_interopRequireDefault", "require", "_colord", "_names", "_constants", "_serializer", "extend", "namesPlugin", "getLinearGradientRepresentation", "gradientAST", "serializeGradient", "type", "orientation", "HORIZONTAL_GRADIENT_ORIENTATION", "colorStops", "hasUnsupportedLength", "item", "length", "undefined", "getGradientAstWithDefault", "value", "hasGradient", "valueToParse", "DEFAULT_GRADIENT", "gradient<PERSON><PERSON><PERSON>", "parse", "error", "console", "warn", "Array", "isArray", "DIRECTIONAL_ORIENTATION_ANGLE_MAP", "toString", "some", "step", "for<PERSON>ach", "stop", "index", "getGradientAstWithControlPoints", "newControlPoints", "map", "position", "color", "r", "g", "b", "a", "colord", "toRgb", "getStopCssColor", "colorStop", "join"], "sources": ["@wordpress/components/src/custom-gradient-picker/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport gradientParser from 'gradient-parser';\nimport { colord, extend } from 'colord';\nimport namesPlugin from 'colord/plugins/names';\n\n/**\n * Internal dependencies\n */\nimport {\n\tDEFAULT_GRADIENT,\n\tHORIZONTAL_GRADIENT_ORIENTATION,\n\tDIRECTIONAL_ORIENTATION_ANGLE_MAP,\n} from './constants';\nimport { serializeGradient } from './serializer';\nimport type { ControlPoint } from './types';\n\nextend( [ namesPlugin ] );\n\nexport function getLinearGradientRepresentation(\n\tgradientAST: gradientParser.GradientNode\n) {\n\treturn serializeGradient( {\n\t\ttype: 'linear-gradient',\n\t\torientation: HORIZONTAL_GRADIENT_ORIENTATION,\n\t\tcolorStops: gradientAST.colorStops,\n\t} );\n}\n\nfunction hasUnsupportedLength( item: gradientParser.ColorStop ) {\n\treturn item.length === undefined || item.length.type !== '%';\n}\n\nexport function getGradientAstWithDefault( value?: string | null ) {\n\t// gradientAST will contain the gradient AST as parsed by gradient-parser npm module.\n\t// More information of its structure available at https://www.npmjs.com/package/gradient-parser#ast.\n\tlet gradientAST: gradientParser.GradientNode | undefined;\n\tlet hasGradient = !! value;\n\n\tconst valueToParse = value ?? DEFAULT_GRADIENT;\n\n\ttry {\n\t\tgradientAST = gradientParser.parse( valueToParse )[ 0 ];\n\t} catch ( error ) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.warn(\n\t\t\t'wp.components.CustomGradientPicker failed to parse the gradient with error',\n\t\t\terror\n\t\t);\n\n\t\tgradientAST = gradientParser.parse( DEFAULT_GRADIENT )[ 0 ];\n\t\thasGradient = false;\n\t}\n\n\tif (\n\t\t! Array.isArray( gradientAST.orientation ) &&\n\t\tgradientAST.orientation?.type === 'directional'\n\t) {\n\t\tgradientAST.orientation = {\n\t\t\ttype: 'angular',\n\t\t\tvalue: DIRECTIONAL_ORIENTATION_ANGLE_MAP[\n\t\t\t\tgradientAST.orientation.value\n\t\t\t].toString(),\n\t\t};\n\t}\n\n\tif ( gradientAST.colorStops.some( hasUnsupportedLength ) ) {\n\t\tconst { colorStops } = gradientAST;\n\t\tconst step = 100 / ( colorStops.length - 1 );\n\t\tcolorStops.forEach( ( stop, index ) => {\n\t\t\tstop.length = {\n\t\t\t\tvalue: `${ step * index }`,\n\t\t\t\ttype: '%',\n\t\t\t};\n\t\t} );\n\t}\n\n\treturn { gradientAST, hasGradient };\n}\n\nexport function getGradientAstWithControlPoints(\n\tgradientAST: gradientParser.GradientNode,\n\tnewControlPoints: ControlPoint[]\n) {\n\treturn {\n\t\t...gradientAST,\n\t\tcolorStops: newControlPoints.map( ( { position, color } ) => {\n\t\t\tconst { r, g, b, a } = colord( color ).toRgb();\n\t\t\treturn {\n\t\t\t\tlength: {\n\t\t\t\t\ttype: '%',\n\t\t\t\t\tvalue: position?.toString(),\n\t\t\t\t},\n\t\t\t\ttype: a < 1 ? 'rgba' : 'rgb',\n\t\t\t\tvalue:\n\t\t\t\t\ta < 1\n\t\t\t\t\t\t? [ `${ r }`, `${ g }`, `${ b }`, `${ a }` ]\n\t\t\t\t\t\t: [ `${ r }`, `${ g }`, `${ b }` ],\n\t\t\t};\n\t\t} ),\n\t} as gradientParser.GradientNode;\n}\n\nexport function getStopCssColor( colorStop: gradientParser.ColorStop ) {\n\tswitch ( colorStop.type ) {\n\t\tcase 'hex':\n\t\t\treturn `#${ colorStop.value }`;\n\t\tcase 'literal':\n\t\t\treturn colorStop.value;\n\t\tcase 'rgb':\n\t\tcase 'rgba':\n\t\t\treturn `${ colorStop.type }(${ colorStop.value.join( ',' ) })`;\n\t\tdefault:\n\t\t\t// Should be unreachable if passing an AST from gradient-parser.\n\t\t\t// See https://github.com/rafaelcaricio/gradient-parser#ast.\n\t\t\treturn 'transparent';\n\t}\n}\n"], "mappings": ";;;;;;;;;;AAGA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AAKA,IAAAG,UAAA,GAAAH,OAAA;AAKA,IAAAI,WAAA,GAAAJ,OAAA;AAfA;AACA;AACA;;AAKA;AACA;AACA;;AASA,IAAAK,cAAM,EAAE,CAAEC,cAAW,CAAG,CAAC;AAElB,SAASC,+BAA+BA,CAC9CC,WAAwC,EACvC;EACD,OAAO,IAAAC,6BAAiB,EAAE;IACzBC,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAEC,0CAA+B;IAC5CC,UAAU,EAAEL,WAAW,CAACK;EACzB,CAAE,CAAC;AACJ;AAEA,SAASC,oBAAoBA,CAAEC,IAA8B,EAAG;EAC/D,OAAOA,IAAI,CAACC,MAAM,KAAKC,SAAS,IAAIF,IAAI,CAACC,MAAM,CAACN,IAAI,KAAK,GAAG;AAC7D;AAEO,SAASQ,yBAAyBA,CAAEC,KAAqB,EAAG;EAClE;EACA;EACA,IAAIX,WAAoD;EACxD,IAAIY,WAAW,GAAG,CAAC,CAAED,KAAK;EAE1B,MAAME,YAAY,GAAGF,KAAK,aAALA,KAAK,cAALA,KAAK,GAAIG,2BAAgB;EAE9C,IAAI;IACHd,WAAW,GAAGe,uBAAc,CAACC,KAAK,CAAEH,YAAa,CAAC,CAAE,CAAC,CAAE;EACxD,CAAC,CAAC,OAAQI,KAAK,EAAG;IACjB;IACAC,OAAO,CAACC,IAAI,CACX,4EAA4E,EAC5EF,KACD,CAAC;IAEDjB,WAAW,GAAGe,uBAAc,CAACC,KAAK,CAAEF,2BAAiB,CAAC,CAAE,CAAC,CAAE;IAC3DF,WAAW,GAAG,KAAK;EACpB;EAEA,IACC,CAAEQ,KAAK,CAACC,OAAO,CAAErB,WAAW,CAACG,WAAY,CAAC,IAC1CH,WAAW,CAACG,WAAW,EAAED,IAAI,KAAK,aAAa,EAC9C;IACDF,WAAW,CAACG,WAAW,GAAG;MACzBD,IAAI,EAAE,SAAS;MACfS,KAAK,EAAEW,4CAAiC,CACvCtB,WAAW,CAACG,WAAW,CAACQ,KAAK,CAC7B,CAACY,QAAQ,CAAC;IACZ,CAAC;EACF;EAEA,IAAKvB,WAAW,CAACK,UAAU,CAACmB,IAAI,CAAElB,oBAAqB,CAAC,EAAG;IAC1D,MAAM;MAAED;IAAW,CAAC,GAAGL,WAAW;IAClC,MAAMyB,IAAI,GAAG,GAAG,IAAKpB,UAAU,CAACG,MAAM,GAAG,CAAC,CAAE;IAC5CH,UAAU,CAACqB,OAAO,CAAE,CAAEC,IAAI,EAAEC,KAAK,KAAM;MACtCD,IAAI,CAACnB,MAAM,GAAG;QACbG,KAAK,EAAG,GAAGc,IAAI,GAAGG,KAAO,EAAC;QAC1B1B,IAAI,EAAE;MACP,CAAC;IACF,CAAE,CAAC;EACJ;EAEA,OAAO;IAAEF,WAAW;IAAEY;EAAY,CAAC;AACpC;AAEO,SAASiB,+BAA+BA,CAC9C7B,WAAwC,EACxC8B,gBAAgC,EAC/B;EACD,OAAO;IACN,GAAG9B,WAAW;IACdK,UAAU,EAAEyB,gBAAgB,CAACC,GAAG,CAAE,CAAE;MAAEC,QAAQ;MAAEC;IAAM,CAAC,KAAM;MAC5D,MAAM;QAAEC,CAAC;QAAEC,CAAC;QAAEC,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAAC,cAAM,EAAEL,KAAM,CAAC,CAACM,KAAK,CAAC,CAAC;MAC9C,OAAO;QACN/B,MAAM,EAAE;UACPN,IAAI,EAAE,GAAG;UACTS,KAAK,EAAEqB,QAAQ,EAAET,QAAQ,CAAC;QAC3B,CAAC;QACDrB,IAAI,EAAEmC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK;QAC5B1B,KAAK,EACJ0B,CAAC,GAAG,CAAC,GACF,CAAG,GAAGH,CAAG,EAAC,EAAG,GAAGC,CAAG,EAAC,EAAG,GAAGC,CAAG,EAAC,EAAG,GAAGC,CAAG,EAAC,CAAE,GAC1C,CAAG,GAAGH,CAAG,EAAC,EAAG,GAAGC,CAAG,EAAC,EAAG,GAAGC,CAAG,EAAC;MACnC,CAAC;IACF,CAAE;EACH,CAAC;AACF;AAEO,SAASI,eAAeA,CAAEC,SAAmC,EAAG;EACtE,QAASA,SAAS,CAACvC,IAAI;IACtB,KAAK,KAAK;MACT,OAAQ,IAAIuC,SAAS,CAAC9B,KAAO,EAAC;IAC/B,KAAK,SAAS;MACb,OAAO8B,SAAS,CAAC9B,KAAK;IACvB,KAAK,KAAK;IACV,KAAK,MAAM;MACV,OAAQ,GAAG8B,SAAS,CAACvC,IAAM,IAAIuC,SAAS,CAAC9B,KAAK,CAAC+B,IAAI,CAAE,GAAI,CAAG,GAAE;IAC/D;MACC;MACA;MACA,OAAO,aAAa;EACtB;AACD"}