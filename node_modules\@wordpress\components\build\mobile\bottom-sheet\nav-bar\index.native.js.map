{"version": 3, "names": ["_reactNative", "require", "_applyButton", "_interopRequireDefault", "_back<PERSON><PERSON>on", "_heading", "_styles", "NavBar", "children", "_react", "createElement", "View", "style", "styles", "App<PERSON><PERSON><PERSON><PERSON>", "BackButton", "<PERSON><PERSON>", "Back", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Heading", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/nav-bar/index.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { View } from 'react-native';\n\n/**\n * Internal dependencies\n */\nimport ApplyButton from './apply-button';\nimport Button from './back-button';\nimport Heading from './heading';\nimport styles from './styles.scss';\nfunction NavBar( { children } ) {\n\treturn <View style={ styles[ 'nav-bar' ] }>{ children }</View>;\n}\n\nNavBar.ApplyButton = ApplyButton;\nNavBar.BackButton = Button.Back;\nNavBar.DismissButton = Button.Dismiss;\n\nNavBar.Heading = Heading;\n\nexport default NavBar;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AAKA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,QAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,OAAA,GAAAH,sBAAA,CAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAKA,SAASM,MAAMA,CAAE;EAAEC;AAAS,CAAC,EAAG;EAC/B,OAAO,IAAAC,MAAA,CAAAC,aAAA,EAACV,YAAA,CAAAW,IAAI;IAACC,KAAK,EAAGC,eAAM,CAAE,SAAS;EAAI,GAAGL,QAAgB,CAAC;AAC/D;AAEAD,MAAM,CAACO,WAAW,GAAGA,oBAAW;AAChCP,MAAM,CAACQ,UAAU,GAAGC,mBAAM,CAACC,IAAI;AAC/BV,MAAM,CAACW,aAAa,GAAGF,mBAAM,CAACG,OAAO;AAErCZ,MAAM,CAACa,OAAO,GAAGA,gBAAO;AAAC,IAAAC,QAAA,GAEVd,MAAM;AAAAe,OAAA,CAAAC,OAAA,GAAAF,QAAA"}