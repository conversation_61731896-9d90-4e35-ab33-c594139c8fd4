"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ColorInput = void 0;
var _react = require("react");
var _rgbInput = require("./rgb-input");
var _hslInput = require("./hsl-input");
var _hexInput = require("./hex-input");
/**
 * Internal dependencies
 */

const ColorInput = ({
  colorType,
  color,
  onChange,
  enableAlpha
}) => {
  const props = {
    color,
    onChange,
    enableAlpha
  };
  switch (colorType) {
    case 'hsl':
      return (0, _react.createElement)(_hslInput.HslInput, {
        ...props
      });
    case 'rgb':
      return (0, _react.createElement)(_rgbInput.RgbInput, {
        ...props
      });
    default:
    case 'hex':
      return (0, _react.createElement)(_hexInput.HexInput, {
        ...props
      });
  }
};
exports.ColorInput = ColorInput;
//# sourceMappingURL=color-input.js.map