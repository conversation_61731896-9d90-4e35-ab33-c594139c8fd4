"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BottomSheetPickerCell;
var _react = require("react");
var _cell = _interopRequireDefault(require("./cell"));
var _picker = _interopRequireDefault(require("../picker"));
/**
 * Internal dependencies
 */

function BottomSheetPickerCell(props) {
  const {
    options,
    hideCancelButton,
    onChangeValue,
    value,
    ...cellProps
  } = props;
  let picker;
  const onCellPress = () => {
    picker.presentPicker();
  };
  const onChange = newValue => {
    onChangeValue(newValue);
  };
  const option = options.find(opt => opt.value === value);
  const label = option ? option.label : value;
  return (0, _react.createElement)(_cell.default, {
    onPress: onCellPress,
    editable: false,
    value: label,
    ...cellProps
  }, (0, _react.createElement)(_picker.default, {
    leftAlign: true,
    hideCancelButton: hideCancelButton,
    ref: instance => picker = instance,
    options: options,
    onChange: onChange
  }));
}
//# sourceMappingURL=picker-cell.native.js.map