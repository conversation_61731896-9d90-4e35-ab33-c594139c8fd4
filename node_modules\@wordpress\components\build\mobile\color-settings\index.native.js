"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _native = require("@react-navigation/native");
var _element = require("@wordpress/element");
var _components = require("@wordpress/components");
var _pickerScreen = _interopRequireDefault(require("./picker-screen"));
var _gradientPickerScreen = _interopRequireDefault(require("./gradient-picker-screen"));
var _palette = _interopRequireDefault(require("./palette.screen"));
var _utils = require("./utils");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const ColorSettingsMemo = (0, _element.memo)(({
  defaultSettings,
  onHandleClosingBottomSheet,
  shouldEnableBottomSheetMaxHeight,
  onColorChange,
  colorValue,
  gradientValue,
  onGradientChange,
  onColorCleared,
  label,
  hideNavigation
}) => {
  (0, _element.useEffect)(() => {
    shouldEnableBottomSheetMaxHeight(true);
    onHandleClosingBottomSheet(null);
    // Disable reason: deferring this refactor to the native team.
    // see https://github.com/WordPress/gutenberg/pull/41166
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (0, _react.createElement)(_components.BottomSheet.NavigationContainer, null, (0, _react.createElement)(_components.BottomSheet.NavigationScreen, {
    name: _utils.colorsUtils.screens.palette,
    initialParams: {
      defaultSettings,
      onColorChange,
      colorValue,
      gradientValue,
      onGradientChange,
      onColorCleared,
      label,
      hideNavigation
    }
  }, (0, _react.createElement)(_palette.default, null)), (0, _react.createElement)(_components.BottomSheet.NavigationScreen, {
    name: _utils.colorsUtils.screens.picker
  }, (0, _react.createElement)(_pickerScreen.default, null)), (0, _react.createElement)(_components.BottomSheet.NavigationScreen, {
    name: _utils.colorsUtils.screens.gradientPicker
  }, (0, _react.createElement)(_gradientPickerScreen.default, null)));
});
function ColorSettings(props) {
  const route = (0, _native.useRoute)();
  const {
    onHandleClosingBottomSheet,
    shouldEnableBottomSheetMaxHeight
  } = (0, _element.useContext)(_components.BottomSheetContext);
  return (0, _react.createElement)(ColorSettingsMemo, {
    onHandleClosingBottomSheet: onHandleClosingBottomSheet,
    shouldEnableBottomSheetMaxHeight: shouldEnableBottomSheetMaxHeight,
    ...props,
    ...route.params
  });
}
var _default = ColorSettings;
exports.default = _default;
//# sourceMappingURL=index.native.js.map