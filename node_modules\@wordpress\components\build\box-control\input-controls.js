"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = BoxInputControls;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _tooltip = _interopRequireDefault(require("../tooltip"));
var _utils = require("../unit-control/utils");
var _utils2 = require("./utils");
var _boxControlStyles = require("./styles/box-control-styles");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const noop = () => {};
function BoxInputControls({
  __next40pxDefaultSize,
  onChange = noop,
  onFocus = noop,
  values,
  selectedUnits,
  setSelectedUnits,
  sides,
  ...props
}) {
  const generatedId = (0, _compose.useInstanceId)(BoxInputControls, 'box-control-input');
  const createHandleOnFocus = side => event => {
    onFocus(event, {
      side
    });
  };
  const handleOnChange = nextValues => {
    onChange(nextValues);
  };
  const handleOnValueChange = (side, next, extra) => {
    const nextValues = {
      ...values
    };
    const isNumeric = next !== undefined && !isNaN(parseFloat(next));
    const nextValue = isNumeric ? next : undefined;
    nextValues[side] = nextValue;

    /**
     * Supports changing pair sides. For example, holding the ALT key
     * when changing the TOP will also update BOTTOM.
     */
    // @ts-expect-error - TODO: event.altKey is only present when the change event was
    // triggered by a keyboard event. Should this feature be implemented differently so
    // it also works with drag events?
    if (extra?.event.altKey) {
      switch (side) {
        case 'top':
          nextValues.bottom = nextValue;
          break;
        case 'bottom':
          nextValues.top = nextValue;
          break;
        case 'left':
          nextValues.right = nextValue;
          break;
        case 'right':
          nextValues.left = nextValue;
          break;
      }
    }
    handleOnChange(nextValues);
  };
  const createHandleOnUnitChange = side => next => {
    const newUnits = {
      ...selectedUnits
    };
    newUnits[side] = next;
    setSelectedUnits(newUnits);
  };

  // Filter sides if custom configuration provided, maintaining default order.
  const filteredSides = sides?.length ? _utils2.ALL_SIDES.filter(side => sides.includes(side)) : _utils2.ALL_SIDES;
  return (0, _react.createElement)(_react.Fragment, null, filteredSides.map(side => {
    var _CUSTOM_VALUE_SETTING, _CUSTOM_VALUE_SETTING2;
    const [parsedQuantity, parsedUnit] = (0, _utils.parseQuantityAndUnitFromRawValue)(values[side]);
    const computedUnit = values[side] ? parsedUnit : selectedUnits[side];
    const inputId = [generatedId, side].join('-');
    return (0, _react.createElement)(_boxControlStyles.InputWrapper, {
      key: `box-control-${side}`,
      expanded: true
    }, (0, _react.createElement)(_boxControlStyles.FlexedBoxControlIcon, {
      side: side,
      sides: sides
    }), (0, _react.createElement)(_tooltip.default, {
      placement: "top-end",
      text: _utils2.LABELS[side]
    }, (0, _react.createElement)(_boxControlStyles.StyledUnitControl, {
      ...props,
      __next40pxDefaultSize: __next40pxDefaultSize,
      className: "component-box-control__unit-control",
      id: inputId,
      isPressEnterToChange: true,
      value: [parsedQuantity, computedUnit].join(''),
      onChange: (nextValue, extra) => handleOnValueChange(side, nextValue, extra),
      onUnitChange: createHandleOnUnitChange(side),
      onFocus: createHandleOnFocus(side),
      label: _utils2.LABELS[side],
      hideLabelFromVision: true
    })), (0, _react.createElement)(_boxControlStyles.FlexedRangeControl, {
      __nextHasNoMarginBottom: true,
      __next40pxDefaultSize: __next40pxDefaultSize,
      "aria-controls": inputId,
      label: _utils2.LABELS[side],
      hideLabelFromVision: true,
      onChange: newValue => {
        handleOnValueChange(side, newValue !== undefined ? [newValue, computedUnit].join('') : undefined);
      },
      min: 0,
      max: (_CUSTOM_VALUE_SETTING = _utils2.CUSTOM_VALUE_SETTINGS[computedUnit !== null && computedUnit !== void 0 ? computedUnit : 'px']?.max) !== null && _CUSTOM_VALUE_SETTING !== void 0 ? _CUSTOM_VALUE_SETTING : 10,
      step: (_CUSTOM_VALUE_SETTING2 = _utils2.CUSTOM_VALUE_SETTINGS[computedUnit !== null && computedUnit !== void 0 ? computedUnit : 'px']?.step) !== null && _CUSTOM_VALUE_SETTING2 !== void 0 ? _CUSTOM_VALUE_SETTING2 : 0.1,
      value: parsedQuantity !== null && parsedQuantity !== void 0 ? parsedQuantity : 0,
      withInputField: false
    }));
  }));
}
//# sourceMappingURL=input-controls.js.map