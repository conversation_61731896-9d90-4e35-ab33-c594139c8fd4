"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.ColorIndicator = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

function UnforwardedColorIndicator(props, forwardedRef) {
  const {
    className,
    colorValue,
    ...additionalProps
  } = props;
  return (0, _react.createElement)("span", {
    className: (0, _classnames.default)('component-color-indicator', className),
    style: {
      background: colorValue
    },
    ref: forwardedRef,
    ...additionalProps
  });
}

/**
 * ColorIndicator is a React component that renders a specific color in a
 * circle. It's often used to summarize a collection of used colors in a child
 * component.
 *
 * ```jsx
 * import { ColorIndicator } from '@wordpress/components';
 *
 * const MyColorIndicator = () => <ColorIndicator colorValue="#0073aa" />;
 * ```
 */
const ColorIndicator = (0, _element.forwardRef)(UnforwardedColorIndicator);
exports.ColorIndicator = ColorIndicator;
var _default = ColorIndicator;
exports.default = _default;
//# sourceMappingURL=index.js.map