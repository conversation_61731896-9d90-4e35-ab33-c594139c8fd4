{"version": 3, "names": [], "sources": ["@wordpress/components/src/combobox-control/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { BaseControlProps } from '../base-control/types';\n\nexport type ComboboxControlOption = {\n\tlabel: string;\n\tvalue: string;\n\t[ key: string ]: any;\n};\n\nexport type ComboboxControlProps = Pick<\n\tBaseControlProps,\n\t| '__nextHasNoMarginBottom'\n\t| 'className'\n\t| 'label'\n\t| 'hideLabelFromVision'\n\t| 'help'\n> & {\n\t/**\n\t * Custom renderer invoked for each option in the suggestion list.\n\t * The render prop receives as its argument an object containing, under the `item` key,\n\t * the single option's data (directly from the array of data passed to the `options` prop).\n\t */\n\t__experimentalRenderItem?: ( args: {\n\t\titem: ComboboxControlOption;\n\t} ) => React.ReactNode;\n\t/**\n\t * Deprecated. Use `__next40pxDefaultSize` instead.\n\t *\n\t * @default false\n\t * @deprecated\n\t */\n\t__next36pxDefaultSize?: boolean;\n\t/**\n\t * Start opting into the larger default height that will become the default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n\t/**\n\t * Show a reset button to clear the input.\n\t *\n\t * @default true\n\t */\n\tallowReset?: boolean;\n\t/**\n\t * Customizable UI messages.\n\t */\n\tmessages?: {\n\t\t/**\n\t\t * The message to announce to screen readers when a suggestion is selected.\n\t\t *\n\t\t * @default `__( 'Item selected.' )`\n\t\t */\n\t\tselected: string;\n\t};\n\t/**\n\t * Function called with the selected value changes.\n\t */\n\tonChange?: ( value: ComboboxControlProps[ 'value' ] ) => void;\n\t/**\n\t * Function called when the control's search input value changes. The argument contains the next input value.\n\t *\n\t * @default noop\n\t */\n\tonFilterValueChange?: ( value: string ) => void;\n\t/**\n\t * The options that can be chosen from.\n\t */\n\toptions: ComboboxControlOption[];\n\t/**\n\t * The current value of the control.\n\t */\n\tvalue?: string | null;\n};\n"], "mappings": ""}