<?php
/**
 * La classe de gestion des paramètres du module Boss Optimizer.
 *
 * Cette classe gère les paramètres du module, notamment les clés API
 * pour les services d'IA et les préférences d'optimisation.
 *
 * @link       https://bossseo.com
 * @since      1.1.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * La classe de gestion des paramètres du module Boss Optimizer.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_Optimizer_Settings {

    /**
     * L'identifiant unique de ce plugin.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $plugin_name    La chaîne utilisée pour identifier ce plugin.
     */
    protected $plugin_name;

    /**
     * Le nom de l'option dans la base de données.
     *
     * @since    1.1.0
     * @access   protected
     * @var      string    $option_name    Le nom de l'option dans la base de données.
     */
    protected $option_name;

    /**
     * Les paramètres par défaut.
     *
     * @since    1.1.0
     * @access   protected
     * @var      array    $defaults    Les paramètres par défaut.
     */
    protected $defaults;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.1.0
     * @param    string    $plugin_name       Le nom du plugin.
     */
    public function __construct( $plugin_name ) {
        $this->plugin_name = $plugin_name;
        $this->option_name = $plugin_name . '_settings';

        $this->defaults = array(
            'ai' => array(
                'provider' => 'openai',
                'openai_api_key' => '',
                'openai_model' => 'gpt-4',
                'openai_temperature' => 0.7,
                'anthropic_api_key' => '',
                'anthropic_model' => 'claude-3-opus',
                'anthropic_temperature' => 0.7,
                'gemini_api_key' => '',
                'gemini_model' => 'gemini-1.5-pro',
                'gemini_temperature' => 0.7,
                'use_ai_for_titles' => true,
                'use_ai_for_descriptions' => true,
                'use_ai_for_content' => true,
                'use_ai_for_alt_text' => true
            ),
            'optimizer' => array(
                'analyze_title' => true,
                'analyze_content' => true,
                'analyze_headings' => true,
                'analyze_images' => true,
                'analyze_links' => true,
                'analyze_keywords' => true,
                'analyze_readability' => true,
                'min_content_length' => 300,
                'min_title_length' => 20,
                'max_title_length' => 60,
                'min_description_length' => 120,
                'max_description_length' => 160,
                'items_per_page' => 10
            )
        );
    }

    /**
     * Récupère tous les paramètres.
     *
     * @since    1.1.0
     * @return   array    Les paramètres.
     */
    public function get_all() {
        $options = get_option( $this->option_name, array() );
        return wp_parse_args( $options, $this->defaults );
    }

    /**
     * Récupère un groupe de paramètres.
     *
     * @since    1.1.0
     * @param    string    $group    Le groupe de paramètres à récupérer.
     * @return   array               Les paramètres du groupe.
     */
    public function get_group( $group ) {
        $options = $this->get_all();

        if ( isset( $options[ $group ] ) ) {
            return $options[ $group ];
        }

        return isset( $this->defaults[ $group ] ) ? $this->defaults[ $group ] : array();
    }

    /**
     * Récupère un paramètre spécifique.
     *
     * @since    1.1.0
     * @param    string    $group     Le groupe du paramètre.
     * @param    string    $key       La clé du paramètre.
     * @param    mixed     $default   La valeur par défaut si le paramètre n'existe pas.
     * @return   mixed                La valeur du paramètre.
     */
    public function get( $group, $key, $default = null ) {
        $group_options = $this->get_group( $group );

        if ( isset( $group_options[ $key ] ) ) {
            return $group_options[ $key ];
        }

        if ( $default !== null ) {
            return $default;
        }

        return isset( $this->defaults[ $group ][ $key ] ) ? $this->defaults[ $group ][ $key ] : null;
    }

    /**
     * Vérifie si l'IA est configurée et disponible.
     *
     * @since    1.1.0
     * @return   boolean    True si l'IA est configurée, false sinon.
     */
    public function is_ai_configured() {
        $ai_settings = $this->get_group( 'ai' );
        $provider = $ai_settings['provider'];

        if ( $provider === 'openai' ) {
            return ! empty( $ai_settings['openai_api_key'] );
        } elseif ( $provider === 'anthropic' ) {
            return ! empty( $ai_settings['anthropic_api_key'] );
        } elseif ( $provider === 'gemini' ) {
            return ! empty( $ai_settings['gemini_api_key'] );
        }

        return false;
    }

    /**
     * Récupère le fournisseur d'IA actuel.
     *
     * @since    1.1.0
     * @return   string    Le fournisseur d'IA actuel.
     */
    public function get_ai_provider() {
        return $this->get( 'ai', 'provider', 'openai' );
    }

    /**
     * Récupère le modèle d'IA actuel.
     *
     * @since    1.1.0
     * @return   string    Le modèle d'IA actuel.
     */
    public function get_ai_model() {
        $provider = $this->get_ai_provider();

        if ( $provider === 'openai' ) {
            return $this->get( 'ai', 'openai_model', 'gpt-4' );
        } elseif ( $provider === 'anthropic' ) {
            return $this->get( 'ai', 'anthropic_model', 'claude-3-opus' );
        } elseif ( $provider === 'gemini' ) {
            return $this->get( 'ai', 'gemini_model', 'gemini-1.5-pro' );
        }

        return '';
    }

    /**
     * Récupère la clé API pour le fournisseur d'IA actuel.
     *
     * @since    1.1.0
     * @return   string    La clé API.
     */
    public function get_ai_api_key() {
        $provider = $this->get_ai_provider();

        if ( $provider === 'openai' ) {
            return $this->get( 'ai', 'openai_api_key', '' );
        } elseif ( $provider === 'anthropic' ) {
            return $this->get( 'ai', 'anthropic_api_key', '' );
        } elseif ( $provider === 'gemini' ) {
            return $this->get( 'ai', 'gemini_api_key', '' );
        }

        return '';
    }

    /**
     * Enregistre les paramètres.
     *
     * @since    1.1.0
     * @param    array     $settings    Les paramètres à enregistrer.
     * @return   boolean               True si l'enregistrement a réussi, false sinon.
     */
    public function save( $settings ) {
        try {
            error_log( 'Boss SEO Settings: Début save() avec: ' . print_r( $settings, true ) );

            // Récupérer les paramètres actuels
            $current_settings = $this->get_all();
            error_log( 'Boss SEO Settings: Paramètres actuels: ' . print_r( $current_settings, true ) );

            // CORRECTION: Fusion plus simple et sûre
            if ( empty( $current_settings ) || ! is_array( $current_settings ) ) {
                // Première sauvegarde - utiliser directement les nouveaux paramètres
                $merged_settings = $settings;
                error_log( 'Boss SEO Settings: Première sauvegarde (INSERT)' );
            } else {
                // Mise à jour - fusionner intelligemment
                $merged_settings = $current_settings;

                foreach ( $settings as $group_key => $group_values ) {
                    if ( is_array( $group_values ) ) {
                        // Si le groupe n'existe pas, le créer
                        if ( ! isset( $merged_settings[$group_key] ) ) {
                            $merged_settings[$group_key] = array();
                        }

                        // Fusionner les valeurs du groupe
                        foreach ( $group_values as $setting_key => $setting_value ) {
                            $merged_settings[$group_key][$setting_key] = $setting_value;
                        }
                    } else {
                        // Valeur simple
                        $merged_settings[$group_key] = $group_values;
                    }
                }
                error_log( 'Boss SEO Settings: Mise à jour (UPDATE)' );
            }

            error_log( 'Boss SEO Settings: Paramètres finaux à sauvegarder: ' . print_r( $merged_settings, true ) );

            // Enregistrer les paramètres avec gestion d'erreur
            $result = update_option( $this->option_name, $merged_settings );

            error_log( 'Boss SEO Settings: Résultat update_option: ' . ( $result ? 'SUCCESS' : 'FAILED' ) );

            // Vérifier que la sauvegarde a bien fonctionné
            if ( $result ) {
                $saved_settings = get_option( $this->option_name );
                error_log( 'Boss SEO Settings: Vérification post-sauvegarde: ' . ( $saved_settings ? 'OK' : 'ECHEC' ) );
            }

            return $result;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO Settings: Erreur dans save(): ' . $e->getMessage() );
            error_log( 'Boss SEO Settings: Stack trace: ' . $e->getTraceAsString() );
            return false;
        }
    }

    /**
     * Fusionne deux tableaux de manière récursive.
     * Contrairement à array_merge_recursive, cette fonction ne crée pas de tableaux pour les valeurs dupliquées.
     *
     * @since    1.1.0
     * @param    array    $array1    Premier tableau.
     * @param    array    $array2    Deuxième tableau.
     * @return   array               Tableau fusionné.
     */
    private function array_merge_recursive_distinct( array $array1, array $array2 ) {
        $merged = $array1;

        foreach ( $array2 as $key => $value ) {
            if ( is_array( $value ) && isset( $merged[$key] ) && is_array( $merged[$key] ) ) {
                $merged[$key] = $this->array_merge_recursive_distinct( $merged[$key], $value );
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }
}
