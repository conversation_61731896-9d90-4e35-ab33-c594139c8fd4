<?php
/**
 * Contrôleur pour la gestion des robots.txt et sitemaps.
 *
 * Cette classe gère les routes API pour les fonctionnalités de robots.txt et sitemaps.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Robots_Sitemap_Controller {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * L'option pour le contenu du robots.txt.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $robots_option    L'option pour le contenu du robots.txt.
     */
    protected $robots_option;

    /**
     * L'option pour les paramètres du sitemap.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $sitemap_option    L'option pour les paramètres du sitemap.
     */
    protected $sitemap_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->robots_option = $plugin_name . '_robots_content';
        $this->sitemap_option = $plugin_name . '_advanced_sitemap_settings';

        // Charger les nouvelles classes
        $this->load_dependencies();
    }

    /**
     * Charge les dépendances nécessaires.
     *
     * @since    1.2.0
     */
    private function load_dependencies() {
        $base_path = plugin_dir_path( dirname( __FILE__ ) ) . 'technical/';

        // Charger les nouvelles classes si elles n'existent pas
        if ( ! class_exists( 'Boss_Sitemap_Dashboard' ) ) {
            require_once $base_path . 'class-boss-sitemap-dashboard.php';
        }

        if ( ! class_exists( 'Boss_Sitemap_Ping' ) ) {
            require_once $base_path . 'class-boss-sitemap-ping.php';
        }

        if ( ! class_exists( 'Boss_Custom_Urls' ) ) {
            require_once $base_path . 'class-boss-custom-urls.php';
        }
    }

    /**
     * Enregistre les routes REST API.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        // Route pour le contenu du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/robots',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_robots_content' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour la validation du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/validate-robots',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'validate_robots_content' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les règles prédéfinies du robots.txt
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/robots-rules',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_robots_rules' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les paramètres du sitemap avancé
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/settings',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_advanced_sitemap_settings' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour régénérer le sitemap
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/regenerate',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'regenerate_advanced_sitemap' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour l'historique des générations de sitemaps
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_sitemap_generation_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour ping les moteurs de recherche
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/ping',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'ping_search_engines' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les URLs personnalisées
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/advanced-sitemap/custom-urls',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'save_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour les types de contenu
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/content-types',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_content_types' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour les taxonomies
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/taxonomies',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_taxonomies' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // ===== NOUVELLES ROUTES API PHASE 1 =====

        // Route pour les statistiques du dashboard
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/dashboard/stats',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_dashboard_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour actualiser les statistiques
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/dashboard/refresh',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'refresh_dashboard_stats' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour ping les moteurs de recherche (nouvelle version)
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/ping-engines',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'ping_all_search_engines' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Route pour l'historique des pings
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/ping-history',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'get_ping_history' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        // Routes pour les URLs personnalisées (CRUD)
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls',
            array(
                array(
                    'methods'             => 'GET',
                    'callback'            => array( $this, 'get_all_custom_urls' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'POST',
                    'callback'            => array( $this, 'add_custom_url' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour une URL personnalisée spécifique
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls/(?P<id>[a-zA-Z0-9_]+)',
            array(
                array(
                    'methods'             => 'PUT',
                    'callback'            => array( $this, 'update_custom_url' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
                array(
                    'methods'             => 'DELETE',
                    'callback'            => array( $this, 'delete_custom_url' ),
                    'permission_callback' => array( $this, 'check_permissions' ),
                ),
            )
        );

        // Route pour import/export CSV
        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls/import',
            array(
                'methods'             => 'POST',
                'callback'            => array( $this, 'import_custom_urls_csv' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );

        register_rest_route(
            'boss-seo/v1',
            '/robots-sitemap/custom-urls/export',
            array(
                'methods'             => 'GET',
                'callback'            => array( $this, 'export_custom_urls_csv' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            )
        );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_robots_content( $request ) {
        $content = get_option( $this->robots_option, $this->get_default_robots_content() );
        return rest_ensure_response( array( 'content' => $content ) );
    }

    /**
     * Enregistre le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_robots_content( $request ) {
        $content = $request->get_param( 'content' );

        if ( $content === null ) {
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Contenu manquant', 'boss-seo' )
            ) );
        }

        $content = sanitize_textarea_field( $content );
        $result = update_option( $this->robots_option, $content );

        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Contenu robots.txt sauvegardé avec succès', 'boss-seo' )
            ) );
        } else {
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la sauvegarde', 'boss-seo' )
            ) );
        }
    }

    /**
     * Valide le contenu du robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function validate_robots_content( $request ) {
        $content = sanitize_textarea_field( $request->get_param( 'content' ) );
        
        // Inclure la classe de validation
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-robots-validator.php';
        
        // Valider le contenu
        $errors = Boss_Robots_Validator::validate( $content );
        
        return rest_ensure_response( array( 'errors' => $errors ) );
    }

    /**
     * Récupère des règles prédéfinies pour le fichier robots.txt.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_robots_rules( $request ) {
        $type = sanitize_text_field( $request->get_param( 'type' ) );
        
        // Inclure la classe de validation
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-robots-validator.php';
        
        // Récupérer les règles
        $rules = Boss_Robots_Validator::generate_rules( $type );
        
        return rest_ensure_response( array( 'rules' => $rules ) );
    }

    /**
     * Récupère les paramètres du sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_advanced_sitemap_settings( $request ) {
        $settings = get_option( $this->sitemap_option, array() );
        return rest_ensure_response( array( 'settings' => $settings ) );
    }

    /**
     * Enregistre les paramètres du sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_advanced_sitemap_settings( $request ) {
        try {
            $settings = $request->get_param( 'settings' );

            if ( $settings === null ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Paramètres manquants', 'boss-seo' )
                ) );
            }

            // Log pour débogage
            error_log( 'Boss SEO: Sauvegarde paramètres sitemap - ' . print_r( $settings, true ) );

            // Sanitize les paramètres
            $sanitized_settings = $this->sanitize_sitemap_settings( $settings );

            if ( empty( $sanitized_settings ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la sanitisation des paramètres', 'boss-seo' )
                ) );
            }

            // Log des paramètres sanitisés
            error_log( 'Boss SEO: Paramètres sanitisés - ' . print_r( $sanitized_settings, true ) );

            // Enregistrer les paramètres
            $result = update_option( $this->sitemap_option, $sanitized_settings );

            if ( $result !== false ) {
                try {
                    // Planifier la mise à jour automatique si activée
                    if ( isset( $sanitized_settings['enableAutoUpdate'] ) && $sanitized_settings['enableAutoUpdate'] ) {
                        $frequency = isset( $sanitized_settings['autoUpdateFrequency'] ) ? $sanitized_settings['autoUpdateFrequency'] : 'daily';
                        $this->schedule_sitemap_update( $frequency );
                    } else {
                        $this->unschedule_sitemap_update();
                    }
                } catch ( Exception $e ) {
                    error_log( 'Boss SEO: Erreur planification - ' . $e->getMessage() );
                    // Continue même si la planification échoue
                }

                // Forcer la régénération des règles de réécriture
                delete_option( 'boss_seo_sitemap_rules_flushed' );

                return rest_ensure_response( array(
                    'success' => true,
                    'message' => __( 'Paramètres du sitemap sauvegardés avec succès', 'boss-seo' )
                ) );
            } else {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Erreur lors de la sauvegarde des paramètres', 'boss-seo' )
                ) );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Exception sauvegarde paramètres - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => sprintf( __( 'Erreur lors de la sauvegarde: %s', 'boss-seo' ), $e->getMessage() )
            ) );
        } catch ( Error $e ) {
            error_log( 'Boss SEO: Erreur fatale sauvegarde paramètres - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => sprintf( __( 'Erreur fatale lors de la sauvegarde: %s', 'boss-seo' ), $e->getMessage() )
            ) );
        }
    }

    /**
     * Régénère le sitemap avancé.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function regenerate_advanced_sitemap( $request ) {
        try {
            // Vérifier si les classes existent déjà
            if ( ! class_exists( 'Boss_Advanced_Sitemaps' ) ) {
                $sitemap_file = plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-advanced-sitemaps.php';
                if ( ! file_exists( $sitemap_file ) ) {
                    return rest_ensure_response( array(
                        'success' => false,
                        'message' => __( 'Fichier de sitemap non trouvé', 'boss-seo' )
                    ) );
                }
                require_once $sitemap_file;
            }

            if ( ! class_exists( 'Boss_Sitemap_Utils' ) ) {
                $utils_file = plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-sitemap-utils.php';
                if ( ! file_exists( $utils_file ) ) {
                    return rest_ensure_response( array(
                        'success' => false,
                        'message' => __( 'Fichier utilitaire de sitemap non trouvé', 'boss-seo' )
                    ) );
                }
                require_once $utils_file;
            }

            // Créer une instance de la classe
            $advanced_sitemaps = new Boss_Advanced_Sitemaps( $this->plugin_name, $this->version );

            // Vérifier les paramètres du sitemap
            $settings = get_option( $this->sitemap_option, array() );
            if ( empty( $settings ) ) {
                // Créer des paramètres par défaut
                $default_settings = array(
                    'enabled' => true,
                    'includedPostTypes' => array( 'post', 'page' ),
                    'includedTaxonomies' => array( 'category', 'post_tag' ),
                    'defaultChangeFreq' => 'weekly',
                    'defaultPriority' => 0.7,
                    'includeImages' => true,
                    'includeLastMod' => true,
                    'enableAutoUpdate' => false,
                    'enableImageSitemap' => false,
                    'enableVideoSitemap' => false,
                    'enableStoriesSitemap' => false,
                    'enableNewsSitemap' => false,
                    'enableCustomSitemap' => false,
                    'enableTaxonomySitemaps' => false,
                    'enablePostTypeSitemaps' => array(
                        'post' => true,
                        'page' => true
                    )
                );
                update_option( $this->sitemap_option, $default_settings );
            }

            // Régénérer les sitemaps
            $advanced_sitemaps->update_all_sitemaps();

            // Forcer la régénération des règles de réécriture
            delete_option( 'boss_seo_sitemap_rules_flushed' );

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Sitemaps régénérés avec succès', 'boss-seo' )
            ) );

        } catch ( Exception $e ) {
            return rest_ensure_response( array(
                'success' => false,
                'message' => sprintf( __( 'Erreur lors de la régénération: %s', 'boss-seo' ), $e->getMessage() )
            ) );
        } catch ( Error $e ) {
            return rest_ensure_response( array(
                'success' => false,
                'message' => sprintf( __( 'Erreur fatale lors de la régénération: %s', 'boss-seo' ), $e->getMessage() )
            ) );
        }
    }

    /**
     * Récupère l'historique des générations de sitemaps.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_sitemap_generation_history( $request ) {
        // Inclure la classe utilitaire
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-sitemap-utils.php';
        
        // Récupérer l'historique
        $history = Boss_Sitemap_Utils::get_sitemap_generation_history();
        
        return rest_ensure_response( array( 'history' => $history ) );
    }

    /**
     * Ping les moteurs de recherche pour les informer de la mise à jour du sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function ping_search_engines( $request ) {
        // Inclure la classe utilitaire
        require_once plugin_dir_path( dirname( __FILE__ ) ) . 'technical/class-boss-sitemap-utils.php';
        
        // Ping les moteurs de recherche
        $results = Boss_Sitemap_Utils::ping_search_engines();
        
        return rest_ensure_response( array( 'results' => $results ) );
    }

    /**
     * Récupère les URLs personnalisées pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_custom_urls( $request ) {
        $settings = get_option( $this->sitemap_option, array() );
        $urls = isset( $settings['customUrls'] ) ? $settings['customUrls'] : array();
        
        return rest_ensure_response( array( 'urls' => $urls ) );
    }

    /**
     * Enregistre les URLs personnalisées pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function save_custom_urls( $request ) {
        $urls = $request->get_param( 'urls' );
        $settings = get_option( $this->sitemap_option, array() );
        
        // Sanitize les URLs
        $sanitized_urls = array();
        foreach ( $urls as $url ) {
            $sanitized_url = array(
                'loc'        => esc_url_raw( $url['loc'] ),
                'lastmod'    => sanitize_text_field( $url['lastmod'] ),
                'changefreq' => sanitize_text_field( $url['changefreq'] ),
                'priority'   => sanitize_text_field( $url['priority'] ),
            );
            $sanitized_urls[] = $sanitized_url;
        }
        
        // Mettre à jour les paramètres
        $settings['customUrls'] = $sanitized_urls;
        update_option( $this->sitemap_option, $settings );
        
        return rest_ensure_response( array( 'success' => true ) );
    }

    /**
     * Récupère les types de contenu disponibles pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_content_types( $request ) {
        $post_types = get_post_types( array( 'public' => true ), 'objects' );
        $content_types = array();
        
        foreach ( $post_types as $post_type ) {
            $content_types[] = array(
                'name'  => $post_type->name,
                'label' => $post_type->label,
            );
        }
        
        return rest_ensure_response( array( 'contentTypes' => $content_types ) );
    }

    /**
     * Récupère les taxonomies disponibles pour le sitemap.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_taxonomies( $request ) {
        $taxonomies_objects = get_taxonomies( array( 'public' => true ), 'objects' );
        $taxonomies = array();
        
        foreach ( $taxonomies_objects as $taxonomy ) {
            $taxonomies[] = array(
                'name'  => $taxonomy->name,
                'label' => $taxonomy->label,
            );
        }
        
        return rest_ensure_response( array( 'taxonomies' => $taxonomies ) );
    }

    /**
     * Sanitize les paramètres du sitemap.
     *
     * @since    1.2.0
     * @param    array    $settings    Les paramètres du sitemap.
     * @return   array                 Les paramètres sanitizés.
     */
    private function sanitize_sitemap_settings( $settings ) {
        if ( ! is_array( $settings ) ) {
            error_log( 'Boss SEO: sanitize_sitemap_settings - paramètres non valides: ' . gettype( $settings ) );
            return array();
        }

        $sanitized_settings = array();

        try {
            // Sanitize les paramètres booléens
            $boolean_fields = array(
                'enabled',
                'includeImages',
                'includeLastMod',
                'enableAutoUpdate',
                'enableImageSitemap',
                'enableVideoSitemap',
                'enableStoriesSitemap',
                'enableNewsSitemap',
                'enableCustomSitemap',
                'enableTaxonomySitemaps',
            );

            foreach ( $boolean_fields as $field ) {
                $sanitized_settings[$field] = isset( $settings[$field] ) ? (bool) $settings[$field] : false;
            }

            // Sanitize les paramètres de texte
            $text_fields = array(
                'defaultChangeFreq',
                'autoUpdateFrequency',
            );

            foreach ( $text_fields as $field ) {
                $sanitized_settings[$field] = isset( $settings[$field] ) ? sanitize_text_field( $settings[$field] ) : '';
            }

            // Sanitize les paramètres numériques
            $sanitized_settings['defaultPriority'] = isset( $settings['defaultPriority'] ) ? floatval( $settings['defaultPriority'] ) : 0.7;

            // Sanitize les tableaux
            $array_fields = array(
                'includedPostTypes',
                'includedTaxonomies',
                'newsPostTypes',
            );

            foreach ( $array_fields as $field ) {
                if ( isset( $settings[$field] ) && is_array( $settings[$field] ) ) {
                    $sanitized_settings[$field] = array_map( 'sanitize_text_field', $settings[$field] );
                } else {
                    $sanitized_settings[$field] = array();
                }
            }

            // Sanitize enablePostTypeSitemaps (structure spéciale)
            if ( isset( $settings['enablePostTypeSitemaps'] ) && is_array( $settings['enablePostTypeSitemaps'] ) ) {
                $sanitized_settings['enablePostTypeSitemaps'] = array();
                foreach ( $settings['enablePostTypeSitemaps'] as $post_type => $enabled ) {
                    $sanitized_post_type = sanitize_text_field( $post_type );
                    $sanitized_settings['enablePostTypeSitemaps'][$sanitized_post_type] = (bool) $enabled;
                }
            } else {
                $sanitized_settings['enablePostTypeSitemaps'] = array();
            }

            // Sanitize les URLs personnalisées
            if ( isset( $settings['customUrls'] ) && is_array( $settings['customUrls'] ) ) {
                $sanitized_settings['customUrls'] = array();

                foreach ( $settings['customUrls'] as $url ) {
                    if ( is_array( $url ) && isset( $url['loc'] ) ) {
                        $sanitized_url = array(
                            'loc'        => esc_url_raw( $url['loc'] ),
                            'lastmod'    => isset( $url['lastmod'] ) ? sanitize_text_field( $url['lastmod'] ) : '',
                            'changefreq' => isset( $url['changefreq'] ) ? sanitize_text_field( $url['changefreq'] ) : 'weekly',
                            'priority'   => isset( $url['priority'] ) ? sanitize_text_field( $url['priority'] ) : '0.5',
                        );

                        $sanitized_settings['customUrls'][] = $sanitized_url;
                    }
                }
            } else {
                $sanitized_settings['customUrls'] = array();
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur sanitisation - ' . $e->getMessage() );
            return array();
        }

        return $sanitized_settings;
    }

    /**
     * Planifie la mise à jour automatique du sitemap.
     *
     * @since    1.2.0
     * @param    string    $frequency    La fréquence de mise à jour.
     */
    private function schedule_sitemap_update( $frequency ) {
        try {
            // Supprimer la planification existante
            $this->unschedule_sitemap_update();

            // Valider la fréquence
            $valid_frequencies = array( 'hourly', 'daily', 'weekly' );
            if ( ! in_array( $frequency, $valid_frequencies ) ) {
                error_log( 'Boss SEO: Fréquence de planification invalide - ' . $frequency );
                $frequency = 'daily'; // Valeur par défaut
            }

            // Planifier la mise à jour
            $result = wp_schedule_event( time(), $frequency, 'boss_seo_update_sitemaps' );

            if ( $result === false ) {
                error_log( 'Boss SEO: Échec de la planification pour la fréquence - ' . $frequency );
            } else {
                error_log( 'Boss SEO: Planification réussie pour la fréquence - ' . $frequency );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la planification - ' . $e->getMessage() );
        }
    }

    /**
     * Supprime la planification de mise à jour automatique du sitemap.
     *
     * @since    1.2.0
     */
    private function unschedule_sitemap_update() {
        try {
            $timestamp = wp_next_scheduled( 'boss_seo_update_sitemaps' );

            if ( $timestamp ) {
                $result = wp_unschedule_event( $timestamp, 'boss_seo_update_sitemaps' );
                if ( $result === false ) {
                    error_log( 'Boss SEO: Échec de la suppression de planification' );
                } else {
                    error_log( 'Boss SEO: Suppression de planification réussie' );
                }
            }
        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur lors de la suppression de planification - ' . $e->getMessage() );
        }
    }

    /**
     * Récupère le contenu par défaut du robots.txt.
     *
     * @since    1.2.0
     * @return   string    Le contenu par défaut du robots.txt.
     */
    private function get_default_robots_content() {
        $site_url = get_site_url();

        $content = "User-agent: *\n";
        $content .= "Disallow: /wp-admin/\n";
        $content .= "Allow: /wp-admin/admin-ajax.php\n";
        $content .= "Disallow: /wp-includes/\n";
        $content .= "Disallow: /wp-content/plugins/\n";
        $content .= "Disallow: /wp-login.php\n";
        $content .= "Disallow: /xmlrpc.php\n";
        $content .= "Disallow: /readme.html\n\n";
        $content .= "Sitemap: {$site_url}/sitemap.xml\n";

        return $content;
    }

    // ===== NOUVELLES MÉTHODES API PHASE 1 =====

    /**
     * Récupère les statistiques du dashboard.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_dashboard_stats( $request ) {
        try {
            $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
            $stats = $dashboard->get_dashboard_stats();

            return rest_ensure_response( array(
                'success' => true,
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération stats dashboard - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des statistiques', 'boss-seo' ),
                'stats' => array()
            ) );
        }
    }

    /**
     * Actualise les statistiques du dashboard.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function refresh_dashboard_stats( $request ) {
        try {
            $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
            $stats = $dashboard->refresh_stats();

            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Statistiques actualisées avec succès', 'boss-seo' ),
                'stats' => $stats
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur actualisation stats dashboard - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'actualisation des statistiques', 'boss-seo' )
            ) );
        }
    }

    /**
     * Ping tous les moteurs de recherche.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function ping_all_search_engines( $request ) {
        try {
            $ping_manager = new Boss_Sitemap_Ping( $this->plugin_name, $this->version );
            $results = $ping_manager->ping_all_engines();

            // Mettre à jour le timestamp de dernière génération
            $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
            $dashboard->update_last_generation();

            return rest_ensure_response( array(
                'success' => $results['success'],
                'message' => $results['message'],
                'results' => $results
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur ping moteurs - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors du ping des moteurs de recherche', 'boss-seo' )
            ) );
        }
    }

    /**
     * Récupère l'historique des pings.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_ping_history( $request ) {
        try {
            $limit = $request->get_param( 'limit' ) ? intval( $request->get_param( 'limit' ) ) : 10;

            $ping_manager = new Boss_Sitemap_Ping( $this->plugin_name, $this->version );
            $history = $ping_manager->get_ping_history( $limit );

            return rest_ensure_response( array(
                'success' => true,
                'history' => $history
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération historique ping - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération de l\'historique', 'boss-seo' ),
                'history' => array()
            ) );
        }
    }

    /**
     * Récupère toutes les URLs personnalisées.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_all_custom_urls( $request ) {
        try {
            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $urls = $custom_urls_manager->get_all_custom_urls();

            return rest_ensure_response( array(
                'success' => true,
                'urls' => $urls
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération URLs personnalisées - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la récupération des URLs', 'boss-seo' ),
                'urls' => array()
            ) );
        }
    }

    /**
     * Ajoute une nouvelle URL personnalisée.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function add_custom_url( $request ) {
        try {
            $url_data = $request->get_json_params();

            if ( empty( $url_data ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Données manquantes', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->add_custom_url( $url_data );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur ajout URL personnalisée - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'ajout de l\'URL', 'boss-seo' )
            ) );
        }
    }

    /**
     * Met à jour une URL personnalisée.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_custom_url( $request ) {
        try {
            $url_id = $request->get_param( 'id' );
            $url_data = $request->get_json_params();

            if ( empty( $url_id ) || empty( $url_data ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'ID ou données manquantes', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->update_custom_url( $url_id, $url_data );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur mise à jour URL personnalisée - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la mise à jour de l\'URL', 'boss-seo' )
            ) );
        }
    }

    /**
     * Supprime une URL personnalisée.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function delete_custom_url( $request ) {
        try {
            $url_id = $request->get_param( 'id' );

            if ( empty( $url_id ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'ID manquant', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->delete_custom_url( $url_id );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur suppression URL personnalisée - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de la suppression de l\'URL', 'boss-seo' )
            ) );
        }
    }

    /**
     * Importe des URLs personnalisées depuis un CSV.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function import_custom_urls_csv( $request ) {
        try {
            $csv_content = $request->get_param( 'csv_content' );

            if ( empty( $csv_content ) ) {
                return rest_ensure_response( array(
                    'success' => false,
                    'message' => __( 'Contenu CSV manquant', 'boss-seo' )
                ) );
            }

            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $result = $custom_urls_manager->import_from_csv( $csv_content );

            return rest_ensure_response( $result );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur import CSV - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'import CSV', 'boss-seo' )
            ) );
        }
    }

    /**
     * Exporte les URLs personnalisées en CSV.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function export_custom_urls_csv( $request ) {
        try {
            $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
            $csv_content = $custom_urls_manager->export_to_csv();

            return rest_ensure_response( array(
                'success' => true,
                'csv_content' => $csv_content,
                'filename' => 'boss-seo-custom-urls-' . date( 'Y-m-d' ) . '.csv'
            ) );

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur export CSV - ' . $e->getMessage() );
            return rest_ensure_response( array(
                'success' => false,
                'message' => __( 'Erreur lors de l\'export CSV', 'boss-seo' )
            ) );
        }
    }
}
