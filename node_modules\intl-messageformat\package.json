{"name": "intl-messageformat", "version": "4.4.0", "description": "Formats ICU Message strings with number, date, plural, and select placeholders to create localized messages.", "keywords": ["i18n", "intl", "internationalization", "localization", "globalization", "messageformat", "parser", "plural", "icu"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "Caridy <PERSON>ino <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>"], "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "repository": {"type": "git", "url": "**************:formatjs/formatjs.git"}, "main": "index.js", "module": "lib/index.js", "typings": "lib/index.d.ts", "dependencies": {"intl-messageformat-parser": "^1.8.1"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist lib", "benchmark": "ts-node --project ./tsconfig.cjs.json tests/benchmark", "build": "tsc && tsc -p tsconfig.cjs.json && rollup -c rollup.config.js", "test": "mocha --opts ../../mocha.opts tests/index.ts", "karma:local": "karma start karma.conf.js", "karma:ci": "karma start karma.conf-ci.js"}, "homepage": "https://github.com/formatjs/formatjs", "directories": {"test": "tests"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "60ddf374ed659ffe2f3807c3c7df411c80492761"}