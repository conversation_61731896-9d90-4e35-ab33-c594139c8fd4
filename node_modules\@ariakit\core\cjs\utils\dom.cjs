"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";



















var _5F4DVUNScjs = require('../__chunks/5F4DVUNS.cjs');
require('../__chunks/AV6KTKLE.cjs');



















exports.canUseDOM = _5F4DVUNScjs.canUseDOM; exports.closest = _5F4DVUNScjs.closest; exports.contains = _5F4DVUNScjs.contains; exports.getActiveElement = _5F4DVUNScjs.getActiveElement; exports.getDocument = _5F4DVUNScjs.getDocument; exports.getPopupItemRole = _5F4DVUNScjs.getPopupItemRole; exports.getPopupRole = _5F4DVUNScjs.getPopupRole; exports.getScrollingElement = _5F4DVUNScjs.getScrollingElement; exports.getTextboxSelection = _5F4DVUNScjs.getTextboxSelection; exports.getWindow = _5F4DVUNScjs.getWindow; exports.isButton = _5F4DVUNScjs.isButton; exports.isFrame = _5F4DVUNScjs.isFrame; exports.isPartiallyHidden = _5F4DVUNScjs.isPartiallyHidden; exports.isTextField = _5F4DVUNScjs.isTextField; exports.isVisible = _5F4DVUNScjs.isVisible; exports.matches = _5F4DVUNScjs.matches; exports.scrollIntoViewIfNeeded = _5F4DVUNScjs.scrollIntoViewIfNeeded; exports.setSelectionRange = _5F4DVUNScjs.setSelectionRange;
