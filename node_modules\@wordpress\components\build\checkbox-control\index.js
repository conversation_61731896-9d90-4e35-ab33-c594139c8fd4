"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CheckboxControl = CheckboxControl;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _icons = require("@wordpress/icons");
var _baseControl = _interopRequireDefault(require("../base-control"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * Checkboxes allow the user to select one or more items from a set.
 *
 * ```jsx
 * import { CheckboxControl } from '@wordpress/components';
 * import { useState } from '@wordpress/element';
 *
 * const MyCheckboxControl = () => {
 *   const [ isChecked, setChecked ] = useState( true );
 *   return (
 *     <CheckboxControl
 *       label="Is author"
 *       help="Is the user a author or not?"
 *       checked={ isChecked }
 *       onChange={ setChecked }
 *     />
 *   );
 * };
 * ```
 */
function CheckboxControl(props) {
  const {
    __nextHasNoMarginBottom,
    label,
    className,
    heading,
    checked,
    indeterminate,
    help,
    id: idProp,
    onChange,
    ...additionalProps
  } = props;
  if (heading) {
    (0, _deprecated.default)('`heading` prop in `CheckboxControl`', {
      alternative: 'a separate element to implement a heading',
      since: '5.8'
    });
  }
  const [showCheckedIcon, setShowCheckedIcon] = (0, _element.useState)(false);
  const [showIndeterminateIcon, setShowIndeterminateIcon] = (0, _element.useState)(false);

  // Run the following callback every time the `ref` (and the additional
  // dependencies) change.
  const ref = (0, _compose.useRefEffect)(node => {
    if (!node) {
      return;
    }

    // It cannot be set using an HTML attribute.
    node.indeterminate = !!indeterminate;
    setShowCheckedIcon(node.matches(':checked'));
    setShowIndeterminateIcon(node.matches(':indeterminate'));
  }, [checked, indeterminate]);
  const id = (0, _compose.useInstanceId)(CheckboxControl, 'inspector-checkbox-control', idProp);
  const onChangeValue = event => onChange(event.target.checked);
  return (0, _react.createElement)(_baseControl.default, {
    __nextHasNoMarginBottom: __nextHasNoMarginBottom,
    label: heading,
    id: id,
    help: help,
    className: (0, _classnames.default)('components-checkbox-control', className)
  }, (0, _react.createElement)("span", {
    className: "components-checkbox-control__input-container"
  }, (0, _react.createElement)("input", {
    ref: ref,
    id: id,
    className: "components-checkbox-control__input",
    type: "checkbox",
    value: "1",
    onChange: onChangeValue,
    checked: checked,
    "aria-describedby": !!help ? id + '__help' : undefined,
    ...additionalProps
  }), showIndeterminateIcon ? (0, _react.createElement)(_icons.Icon, {
    icon: _icons.reset,
    className: "components-checkbox-control__indeterminate",
    role: "presentation"
  }) : null, showCheckedIcon ? (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check,
    className: "components-checkbox-control__checked",
    role: "presentation"
  }) : null), label && (0, _react.createElement)("label", {
    className: "components-checkbox-control__label",
    htmlFor: id
  }, label));
}
var _default = CheckboxControl;
exports.default = _default;
//# sourceMappingURL=index.js.map