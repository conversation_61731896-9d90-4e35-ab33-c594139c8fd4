"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _reactNative = require("react-native");
var _components = require("@wordpress/components");
var _compose = require("@wordpress/compose");
var _icons = require("@wordpress/icons");
var _button = _interopRequireDefault(require("../button"));
var _dropdown = _interopRequireDefault(require("../dropdown"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function mergeProps(defaultProps = {}, props = {}) {
  const mergedProps = {
    ...defaultProps,
    ...props
  };
  if (props.className && defaultProps.className) {
    mergedProps.className = (0, _classnames.default)(props.className, defaultProps.className);
  }
  return mergedProps;
}

/**
 * Whether the argument is a function.
 *
 * @param {*} maybeFunc The argument to check.
 * @return {boolean} True if the argument is a function, false otherwise.
 */
function isFunction(maybeFunc) {
  return typeof maybeFunc === 'function';
}
function DropdownMenu({
  children,
  className,
  controls,
  icon = _icons.menu,
  label,
  popoverProps,
  toggleProps
}) {
  if (!controls?.length && !isFunction(children)) {
    return null;
  }

  // Normalize controls to nested array of objects (sets of controls)
  let controlSets;
  if (controls?.length) {
    controlSets = controls;
    if (!Array.isArray(controlSets[0])) {
      controlSets = [controlSets];
    }
  }
  const mergedPopoverProps = mergeProps({
    className: 'components-dropdown-menu__popover'
  }, popoverProps);
  return (0, _react.createElement)(_dropdown.default, {
    className: (0, _classnames.default)('components-dropdown-menu', className),
    popoverProps: mergedPopoverProps,
    renderToggle: ({
      isOpen,
      onToggle
    }) => {
      const mergedToggleProps = mergeProps({
        className: (0, _classnames.default)('components-dropdown-menu__toggle', {
          'is-opened': isOpen
        })
      }, toggleProps);
      return (0, _react.createElement)(_button.default, {
        ...mergedToggleProps,
        icon: icon,
        onClick: event => {
          onToggle(event);
          if (mergedToggleProps.onClick) {
            mergedToggleProps.onClick(event);
          }
        },
        "aria-haspopup": "true",
        "aria-expanded": isOpen,
        label: label
      }, mergedToggleProps.children);
    },
    renderContent: ({
      isOpen,
      onClose,
      ...props
    }) => {
      return (0, _react.createElement)(_components.BottomSheet, {
        hideHeader: true,
        isVisible: isOpen,
        onClose: onClose
      }, isFunction(children) ? children(props) : null, (0, _react.createElement)(_components.PanelBody, {
        title: label,
        style: {
          paddingLeft: 0,
          paddingRight: 0
        }
      }, controlSets?.flatMap((controlSet, indexOfSet) => controlSet.map((control, indexOfControl) => (0, _react.createElement)(_components.BottomSheet.Cell, {
        key: [indexOfSet, indexOfControl].join(),
        label: control.title,
        onPress: () => {
          onClose();
          if (control.onClick) {
            control.onClick();
          }
        },
        editable: false,
        icon: control.icon,
        leftAlign: true,
        isSelected: control.isActive,
        separatorType: _reactNative.Platform.OS === 'android' ? 'none' : 'leftMargin'
      })))));
    }
  });
}
var _default = (0, _compose.withPreferredColorScheme)(DropdownMenu);
exports.default = _default;
//# sourceMappingURL=index.native.js.map