"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = FocalPoint;
var _react = require("react");
var _focalPointStyle = require("./styles/focal-point-style");
var _classnames = _interopRequireDefault(require("classnames"));
/**
 * Internal dependencies
 */

/**
 * External dependencies
 */

function FocalPoint({
  left = '50%',
  top = '50%',
  ...props
}) {
  const classes = (0, _classnames.default)('components-focal-point-picker__icon_container');
  const style = {
    left,
    top
  };
  return (0, _react.createElement)(_focalPointStyle.PointerCircle, {
    ...props,
    className: classes,
    style: style
  });
}
//# sourceMappingURL=focal-point.js.map