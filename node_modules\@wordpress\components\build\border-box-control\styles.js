"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.wrapper = exports.rightBorderControl = exports.linkedBorderControl = exports.centeredBorderControl = exports.borderBoxControlVisualizer = exports.borderBoxControlSplitControls = exports.borderBoxControlLinkedButton = exports.borderBoxControl = void 0;
var _react = require("@emotion/react");
var _utils = require("../utils");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const borderBoxControl = /*#__PURE__*/(0, _react.css)(process.env.NODE_ENV === "production" ? "" : ";label:borderBoxControl;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderBoxControl = borderBoxControl;
const linkedBorderControl = () => /*#__PURE__*/(0, _react.css)("flex:1;", (0, _utils.rtl)({
  marginRight: '24px'
})(), ";" + (process.env.NODE_ENV === "production" ? "" : ";label:linkedBorderControl;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.linkedBorderControl = linkedBorderControl;
const wrapper = process.env.NODE_ENV === "production" ? {
  name: "bjn8wh",
  styles: "position:relative"
} : {
  name: "memc06-wrapper",
  styles: "position:relative;label:wrapper;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
exports.wrapper = wrapper;
const borderBoxControlLinkedButton = size => {
  return /*#__PURE__*/(0, _react.css)("position:absolute;top:", size === '__unstable-large' ? '8px' : '3px', ";", (0, _utils.rtl)({
    right: 0
  })(), " line-height:0;" + (process.env.NODE_ENV === "production" ? "" : ";label:borderBoxControlLinkedButton;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
exports.borderBoxControlLinkedButton = borderBoxControlLinkedButton;
const borderBoxStyleWithFallback = border => {
  const {
    color = _utils.COLORS.gray[200],
    style = 'solid',
    width = _utils.CONFIG.borderWidth
  } = border || {};
  const clampedWidth = width !== _utils.CONFIG.borderWidth ? `clamp(1px, ${width}, 10px)` : width;
  const hasVisibleBorder = !!width && width !== '0' || !!color;
  const borderStyle = hasVisibleBorder ? style || 'solid' : style;
  return `${color} ${borderStyle} ${clampedWidth}`;
};
const borderBoxControlVisualizer = (borders, size) => {
  return /*#__PURE__*/(0, _react.css)("position:absolute;top:", size === '__unstable-large' ? '20px' : '15px', ";right:", size === '__unstable-large' ? '39px' : '29px', ";bottom:", size === '__unstable-large' ? '20px' : '15px', ";left:", size === '__unstable-large' ? '39px' : '29px', ";border-top:", borderBoxStyleWithFallback(borders?.top), ";border-bottom:", borderBoxStyleWithFallback(borders?.bottom), ";", (0, _utils.rtl)({
    borderLeft: borderBoxStyleWithFallback(borders?.left)
  })(), " ", (0, _utils.rtl)({
    borderRight: borderBoxStyleWithFallback(borders?.right)
  })(), ";" + (process.env.NODE_ENV === "production" ? "" : ";label:borderBoxControlVisualizer;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
exports.borderBoxControlVisualizer = borderBoxControlVisualizer;
const borderBoxControlSplitControls = size => /*#__PURE__*/(0, _react.css)("position:relative;flex:1;width:", size === '__unstable-large' ? undefined : '80%', ";" + (process.env.NODE_ENV === "production" ? "" : ";label:borderBoxControlSplitControls;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.borderBoxControlSplitControls = borderBoxControlSplitControls;
const centeredBorderControl = process.env.NODE_ENV === "production" ? {
  name: "1nwbfnf",
  styles: "grid-column:span 2;margin:0 auto"
} : {
  name: "gedmrr-centeredBorderControl",
  styles: "grid-column:span 2;margin:0 auto;label:centeredBorderControl;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
exports.centeredBorderControl = centeredBorderControl;
const rightBorderControl = () => /*#__PURE__*/(0, _react.css)((0, _utils.rtl)({
  marginLeft: 'auto'
})(), ";" + (process.env.NODE_ENV === "production" ? "" : ";label:rightBorderControl;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
exports.rightBorderControl = rightBorderControl;
//# sourceMappingURL=styles.js.map