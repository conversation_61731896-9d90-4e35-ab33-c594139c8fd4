/**
 * Script de débogage pour les erreurs API Boss SEO
 * À exécuter dans la console du navigateur
 */

console.log('🔍 Débogage des Erreurs API Boss SEO');
console.log('====================================');

// Fonction pour tester une API avec débogage détaillé
async function debugAPI(url, method = 'POST', data = null) {
    console.log(`\n🔍 Test API: ${method} ${url}`);
    
    try {
        const options = {
            method: method,
            headers: {
                'X-WP-Nonce': wpApiSettings.nonce
            }
        };
        
        if (data && method !== 'GET') {
            options.headers['Content-Type'] = 'application/json';
            options.body = JSON.stringify(data);
        }
        
        console.log('📤 Requête:', options);
        
        const response = await fetch(url, options);
        
        console.log(`📥 Réponse: ${response.status} ${response.statusText}`);
        console.log('📋 Headers:', Object.fromEntries(response.headers.entries()));
        
        // Lire la réponse comme texte d'abord
        const responseText = await response.text();
        console.log('📄 Réponse brute:', responseText);
        
        // Essayer de parser en JSON
        let jsonData = null;
        try {
            jsonData = JSON.parse(responseText);
            console.log('✅ JSON valide:', jsonData);
        } catch (jsonError) {
            console.log('❌ JSON invalide:', jsonError.message);
            console.log('🔍 Contenu:', responseText.substring(0, 500) + '...');
        }
        
        return {
            success: response.ok,
            status: response.status,
            headers: Object.fromEntries(response.headers.entries()),
            rawResponse: responseText,
            jsonData: jsonData,
            error: !response.ok ? `${response.status} ${response.statusText}` : null
        };
        
    } catch (error) {
        console.log('💥 Erreur de requête:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Tester les APIs qui posent problème
async function testProblematicAPIs() {
    console.log('\n🚨 Test des APIs problématiques...\n');
    
    const problematicAPIs = [
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/regenerate',
            method: 'POST',
            name: 'Régénération Sitemap'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/advanced-sitemap/settings',
            method: 'POST',
            data: { test: true },
            name: 'Sauvegarde Paramètres'
        },
        {
            url: '/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats',
            method: 'GET',
            name: 'Stats Dashboard'
        }
    ];
    
    const results = {};
    
    for (const api of problematicAPIs) {
        console.log(`\n📋 Test: ${api.name}`);
        const result = await debugAPI(api.url, api.method, api.data);
        results[api.name] = result;
        
        if (result.success) {
            console.log(`✅ ${api.name}: OK`);
        } else {
            console.log(`❌ ${api.name}: ERREUR - ${result.error}`);
            
            // Analyser les erreurs communes
            if (result.rawResponse) {
                if (result.rawResponse.includes('Fatal error')) {
                    console.log('🚨 ERREUR FATALE PHP détectée');
                }
                if (result.rawResponse.includes('Parse error')) {
                    console.log('🚨 ERREUR DE SYNTAXE PHP détectée');
                }
                if (result.rawResponse.includes('Call to undefined')) {
                    console.log('🚨 FONCTION/MÉTHODE INEXISTANTE détectée');
                }
                if (result.rawResponse.includes('Class') && result.rawResponse.includes('not found')) {
                    console.log('🚨 CLASSE NON TROUVÉE détectée');
                }
            }
        }
    }
    
    return results;
}

// Vérifier la configuration WordPress
function checkWordPressConfig() {
    console.log('\n⚙️ Vérification Configuration WordPress...\n');
    
    // Vérifier les variables globales WordPress
    const checks = [
        { name: 'wpApiSettings', check: () => typeof wpApiSettings !== 'undefined' },
        { name: 'wpApiSettings.nonce', check: () => wpApiSettings && wpApiSettings.nonce },
        { name: 'wpApiSettings.root', check: () => wpApiSettings && wpApiSettings.root },
        { name: 'wp.apiFetch', check: () => typeof wp !== 'undefined' && wp.apiFetch },
        { name: 'wp.components', check: () => typeof wp !== 'undefined' && wp.components }
    ];
    
    checks.forEach(check => {
        const result = check.check();
        console.log(`${result ? '✅' : '❌'} ${check.name}: ${result ? 'OK' : 'MANQUANT'}`);
    });
    
    // Vérifier les permissions
    if (wpApiSettings && wpApiSettings.nonce) {
        console.log(`\n🔑 Nonce: ${wpApiSettings.nonce.substring(0, 10)}...`);
        console.log(`🌐 Root API: ${wpApiSettings.root || 'Non défini'}`);
    }
}

// Tester les routes de base
async function testBasicRoutes() {
    console.log('\n🔧 Test des Routes de Base...\n');
    
    const basicRoutes = [
        '/wp-json/',
        '/wp-json/wp/v2/',
        '/wp-json/boss-seo/v1/',
        '/wp-json/boss-seo/v1/robots-sitemap/'
    ];
    
    for (const route of basicRoutes) {
        console.log(`🔍 Test: ${route}`);
        const result = await debugAPI(route, 'GET');
        
        if (result.success) {
            console.log(`✅ ${route}: Accessible`);
        } else {
            console.log(`❌ ${route}: Inaccessible (${result.status})`);
        }
    }
}

// Fonction pour générer un rapport de débogage
function generateDebugReport(results) {
    console.log('\n📊 === RAPPORT DE DÉBOGAGE ===\n');
    
    const errors = Object.entries(results).filter(([name, result]) => !result.success);
    const successes = Object.entries(results).filter(([name, result]) => result.success);
    
    console.log(`✅ APIs fonctionnelles: ${successes.length}`);
    console.log(`❌ APIs en erreur: ${errors.length}`);
    
    if (errors.length > 0) {
        console.log('\n🚨 ERREURS DÉTECTÉES:');
        errors.forEach(([name, result]) => {
            console.log(`\n❌ ${name}:`);
            console.log(`   Status: ${result.status || 'Inconnu'}`);
            console.log(`   Erreur: ${result.error || 'Inconnue'}`);
            
            if (result.rawResponse) {
                // Extraire les informations utiles de la réponse
                const response = result.rawResponse;
                
                if (response.includes('Fatal error')) {
                    const fatalMatch = response.match(/Fatal error: (.*?) in/);
                    if (fatalMatch) {
                        console.log(`   🚨 Erreur fatale: ${fatalMatch[1]}`);
                    }
                }
                
                if (response.includes('Call to undefined')) {
                    const undefinedMatch = response.match(/Call to undefined (.*?) in/);
                    if (undefinedMatch) {
                        console.log(`   🚨 Fonction inexistante: ${undefinedMatch[1]}`);
                    }
                }
                
                if (response.includes('Class') && response.includes('not found')) {
                    const classMatch = response.match(/Class '([^']+)' not found/);
                    if (classMatch) {
                        console.log(`   🚨 Classe manquante: ${classMatch[1]}`);
                    }
                }
            }
        });
        
        console.log('\n💡 SOLUTIONS RECOMMANDÉES:');
        console.log('1. 🔍 Vérifier les logs PHP du serveur');
        console.log('2. 🔧 Vérifier que toutes les classes Boss SEO sont chargées');
        console.log('3. 🔄 Réactiver le plugin Boss SEO');
        console.log('4. 📝 Vérifier les permissions WordPress');
        console.log('5. 🧹 Vider le cache si activé');
    } else {
        console.log('\n🎉 Toutes les APIs fonctionnent correctement !');
    }
}

// Fonction principale de débogage
async function runDebugSession() {
    console.log('🚀 Démarrage de la session de débogage...\n');
    
    try {
        // 1. Vérifier la configuration WordPress
        checkWordPressConfig();
        
        // 2. Tester les routes de base
        await testBasicRoutes();
        
        // 3. Tester les APIs problématiques
        const results = await testProblematicAPIs();
        
        // 4. Générer le rapport
        generateDebugReport(results);
        
        console.log('\n🎯 Session de débogage terminée.');
        console.log('📋 Consultez les détails ci-dessus pour identifier les problèmes.');
        
        return results;
        
    } catch (error) {
        console.log('\n💥 Erreur lors du débogage:', error);
        return null;
    }
}

// Fonction pour tester une API spécifique
async function testSpecificAPI(url, method = 'GET', data = null) {
    console.log(`\n🎯 Test spécifique: ${method} ${url}\n`);
    return await debugAPI(url, method, data);
}

// Rendre les fonctions disponibles globalement
window.debugBossSEO = runDebugSession;
window.testAPI = testSpecificAPI;
window.debugAPI = debugAPI;

console.log('\n🛠️ Fonctions de débogage disponibles:');
console.log('- debugBossSEO() - Session complète de débogage');
console.log('- testAPI(url, method, data) - Tester une API spécifique');
console.log('- debugAPI(url, method, data) - Débogage détaillé d\'une API');

console.log('\n🎯 Pour commencer le débogage, tapez: debugBossSEO()');
console.log('💡 Ou testez une API spécifique: testAPI("/wp-json/boss-seo/v1/robots-sitemap/dashboard/stats")');
