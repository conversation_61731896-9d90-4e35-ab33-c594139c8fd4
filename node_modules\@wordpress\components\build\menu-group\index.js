"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MenuGroup = MenuGroup;
exports.default = void 0;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _element = require("@wordpress/element");
var _compose = require("@wordpress/compose");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * `MenuGroup` wraps a series of related `MenuItem` components into a common
 * section.
 *
 * ```jsx
 * import { MenuGroup, MenuItem } from '@wordpress/components';
 *
 * const MyMenuGroup = () => (
 *   <MenuGroup label="Settings">
 *     <MenuItem>Setting 1</MenuItem>
 *     <MenuItem>Setting 2</MenuItem>
 *   </MenuGroup>
 * );
 * ```
 */
function MenuGroup(props) {
  const {
    children,
    className = '',
    label,
    hideSeparator
  } = props;
  const instanceId = (0, _compose.useInstanceId)(MenuGroup);
  if (!_element.Children.count(children)) {
    return null;
  }
  const labelId = `components-menu-group-label-${instanceId}`;
  const classNames = (0, _classnames.default)(className, 'components-menu-group', {
    'has-hidden-separator': hideSeparator
  });
  return (0, _react.createElement)("div", {
    className: classNames
  }, label && (0, _react.createElement)("div", {
    className: "components-menu-group__label",
    id: labelId,
    "aria-hidden": "true"
  }, label), (0, _react.createElement)("div", {
    role: "group",
    "aria-labelledby": label ? labelId : undefined
  }, children));
}
var _default = MenuGroup;
exports.default = _default;
//# sourceMappingURL=index.js.map