{"version": 3, "names": [], "sources": ["@wordpress/components/src/dropdown/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ComponentPropsWithoutRef, CSSProperties, ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type Popover from '../popover';\nimport type { PopoverProps } from '../popover/types';\n\ntype CallbackProps = {\n\tisOpen: boolean;\n\tonToggle: () => void;\n\tonClose: () => void;\n};\n\nexport type DropdownContentWrapperProps = {\n\t/**\n\t * Amount of padding to apply on the dropdown content.\n\t *\n\t * @default 'small'\n\t */\n\tpaddingSize?: 'none' | 'small' | 'medium';\n};\n\nexport type DropdownProps = {\n\t/**\n\t * The className of the global container.\n\t */\n\tclassName?: string;\n\t/**\n\t * If you want to target the dropdown menu for styling purposes,\n\t * you need to provide a contentClassName because it's not being rendered\n\t * as a child of the container node.\n\t */\n\tcontentClassName?: string;\n\t/**\n\t * Opt-in prop to show popovers fullscreen on mobile.\n\t *\n\t * @default false\n\t */\n\texpandOnMobile?: boolean;\n\t/**\n\t * By default, the first tabbable element in the popover will receive focus\n\t * when it mounts. This is the same as setting this prop to \"firstElement\".\n\t * Specifying a true value will focus the container instead.\n\t * Specifying a false value disables the focus handling entirely\n\t * (this should only be done when an appropriately accessible\n\t * substitute behavior exists).\n\t *\n\t * @default 'firstElement'\n\t */\n\tfocusOnMount?: 'firstElement' | boolean;\n\t/**\n\t * Set this to customize the text that is shown in the dropdown's header\n\t * when it is fullscreen on mobile.\n\t */\n\theaderTitle?: string;\n\t/**\n\t * A callback invoked when the popover should be closed.\n\t */\n\tonClose?: () => void;\n\t/**\n\t * A callback invoked when the state of the dropdown changes\n\t * from open to closed and vice versa.\n\t */\n\tonToggle?: ( willOpen: boolean ) => void;\n\t/**\n\t * Properties of popoverProps object will be passed as props\n\t * to the Popover component.\n\t * Use this object to access properties/features\n\t * of the Popover component that are not already exposed\n\t * in the Dropdown component,\n\t * e.g.: the ability to have the popover without an arrow.\n\t */\n\tpopoverProps?: Omit<\n\t\tComponentPropsWithoutRef< typeof Popover >,\n\t\t'children'\n\t>;\n\t/**\n\t * A callback invoked to render the content of the dropdown menu.\n\t * Its first argument is the same as the renderToggle prop.\n\t */\n\trenderContent: ( props: CallbackProps ) => ReactNode;\n\t/**\n\t * A callback invoked to render the Dropdown Toggle Button.\n\t *\n\t * The first argument of the callback is an object\n\t * containing the following properties:\n\t *\n\t * - isOpen: whether the dropdown menu is opened or not\n\t * - onToggle: A function switching the dropdown menu's state\n\t * from open to closed and vice versa\n\t * - onClose: A function that closes the menu if invoked\n\t */\n\trenderToggle: ( props: CallbackProps ) => ReactNode;\n\t/**\n\t * The style of the global container.\n\t */\n\tstyle?: CSSProperties;\n\t/**\n\t * Legacy way to specify the popover's position with respect to its anchor.\n\t * For details about the possible values, see the `Popover` component's docs.\n\t * _Note: this prop is deprecated. Use the `popoverProps.placement` prop\n\t * instead._\n\t *\n\t * @deprecated\n\t */\n\tposition?: PopoverProps[ 'position' ];\n\t/**\n\t * The controlled open state of the dropdown.\n\t * Must be used in conjunction with `onToggle`.\n\t */\n\topen?: boolean;\n\t/**\n\t * The open state of the dropdown when initially rendered.\n\t * Use when you do not need to control its open state. It will be overridden\n\t * by the `open` prop if it is specified on the component's first render.\n\t */\n\tdefaultOpen?: boolean;\n};\n\nexport type DropdownInternalContext = {\n\t/**\n\t * This variant can be used to change the appearance of the component in\n\t * specific contexts, ie. when rendered inside the `Toolbar` component.\n\t */\n\tvariant?: 'toolbar';\n};\n"], "mappings": ""}