<?php
/**
 * Classe pour le monitoring avancé des sitemaps
 *
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 * @since      1.2.0
 */

// Empêcher l'accès direct
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Boss_Sitemap_Monitoring
 *
 * Gère le monitoring avancé et les analytics des sitemaps
 *
 * @since      1.2.0
 * @package    Boss_Seo
 * @subpackage Boss_Seo/includes/technical
 */
class Boss_Sitemap_Monitoring {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $plugin_name    Le nom du plugin.
     */
    private $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $version    La version du plugin.
     */
    private $version;

    /**
     * Option pour stocker les données de monitoring.
     *
     * @since    1.2.0
     * @access   private
     * @var      string    $monitoring_option    Le nom de l'option.
     */
    private $monitoring_option;

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        $this->monitoring_option = 'boss-seo_sitemap_monitoring_data';
    }

    /**
     * Récupère les statistiques d'indexation détaillées.
     *
     * @since    1.2.0
     * @return   array    Les statistiques d'indexation.
     */
    public function get_indexation_stats() {
        try {
            // Récupérer les données en cache
            $cached_stats = get_transient( 'boss_seo_indexation_stats' );
            
            if ( $cached_stats !== false ) {
                return $cached_stats;
            }

            // Calculer les nouvelles statistiques
            $stats = $this->calculate_indexation_stats();
            
            // Mettre en cache pour 15 minutes
            set_transient( 'boss_seo_indexation_stats', $stats, 15 * MINUTE_IN_SECONDS );
            
            return $stats;

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur récupération stats indexation - ' . $e->getMessage() );
            return $this->get_default_indexation_stats();
        }
    }

    /**
     * Calcule les statistiques d'indexation.
     *
     * @since    1.2.0
     * @return   array    Les statistiques calculées.
     */
    private function calculate_indexation_stats() {
        $stats = array();

        // 1. Statistiques globales
        $dashboard = new Boss_Sitemap_Dashboard( $this->plugin_name, $this->version );
        $dashboard_stats = $dashboard->get_dashboard_stats();

        $stats['global'] = array(
            'submitted' => $dashboard_stats['totalUrls'],
            'indexed' => $dashboard_stats['indexedUrls'],
            'pending' => $dashboard_stats['pendingUrls'],
            'errors' => $dashboard_stats['errorUrls'],
            'rate' => $dashboard_stats['indexationRate']
        );

        // 2. Statistiques par type de contenu
        $stats['by_content_type'] = $this->get_indexation_by_content_type();

        // 3. Évolution dans le temps (7 derniers jours)
        $stats['evolution'] = $this->get_indexation_evolution();

        // 4. Erreurs détectées
        $stats['errors_detail'] = $this->detect_sitemap_errors();

        // 5. Performance des sitemaps
        $stats['performance'] = $this->get_sitemap_performance();

        // 6. Comparaison avec les concurrents (simulé)
        $stats['benchmark'] = $this->get_indexation_benchmark();

        return $stats;
    }

    /**
     * Récupère les statistiques d'indexation par type de contenu.
     *
     * @since    1.2.0
     * @return   array    Les statistiques par type.
     */
    private function get_indexation_by_content_type() {
        $stats = array();
        
        // Types de contenu publics
        $post_types = get_post_types( array( 'public' => true ), 'objects' );
        
        foreach ( $post_types as $post_type ) {
            if ( $post_type->name === 'attachment' ) {
                continue;
            }
            
            $count = wp_count_posts( $post_type->name );
            $published = isset( $count->publish ) ? $count->publish : 0;
            
            // Simulation de l'indexation (en réalité, cela viendrait de Search Console)
            $indexed = round( $published * ( 0.85 + ( rand( 0, 20 ) / 100 ) ) );
            $pending = round( $published * 0.05 );
            $errors = $published - $indexed - $pending;
            
            $stats[] = array(
                'type' => $post_type->name,
                'label' => $post_type->label,
                'total' => $published,
                'indexed' => $indexed,
                'pending' => $pending,
                'errors' => max( 0, $errors ),
                'rate' => $published > 0 ? round( ( $indexed / $published ) * 100, 1 ) : 0
            );
        }
        
        return $stats;
    }

    /**
     * Récupère l'évolution de l'indexation sur 7 jours.
     *
     * @since    1.2.0
     * @return   array    L'évolution de l'indexation.
     */
    private function get_indexation_evolution() {
        $evolution = array();
        
        // Récupérer les données historiques ou les simuler
        $base_indexed = 1000;
        
        for ( $i = 6; $i >= 0; $i-- ) {
            $date = date( 'Y-m-d', strtotime( "-{$i} days" ) );
            $indexed = $base_indexed + ( $i * 20 ) + rand( -10, 30 );
            $submitted = $indexed + rand( 20, 100 );
            
            $evolution[] = array(
                'date' => $date,
                'submitted' => $submitted,
                'indexed' => $indexed,
                'rate' => round( ( $indexed / $submitted ) * 100, 1 )
            );
        }
        
        return $evolution;
    }

    /**
     * Détecte les erreurs dans les sitemaps.
     *
     * @since    1.2.0
     * @return   array    Les erreurs détectées.
     */
    public function detect_sitemap_errors() {
        $errors = array();
        
        try {
            // 1. Vérifier l'accessibilité du sitemap principal
            $sitemap_url = get_site_url() . '/sitemap.xml';
            $response = wp_remote_get( $sitemap_url, array( 'timeout' => 10 ) );
            
            if ( is_wp_error( $response ) ) {
                $errors[] = array(
                    'type' => 'accessibility',
                    'severity' => 'high',
                    'url' => $sitemap_url,
                    'message' => __( 'Sitemap principal inaccessible', 'boss-seo' ),
                    'details' => $response->get_error_message(),
                    'detected_at' => current_time( 'mysql' )
                );
            } else {
                $response_code = wp_remote_retrieve_response_code( $response );
                if ( $response_code !== 200 ) {
                    $errors[] = array(
                        'type' => 'http_error',
                        'severity' => 'high',
                        'url' => $sitemap_url,
                        'message' => sprintf( __( 'Erreur HTTP %d sur le sitemap principal', 'boss-seo' ), $response_code ),
                        'details' => wp_remote_retrieve_response_message( $response ),
                        'detected_at' => current_time( 'mysql' )
                    );
                }
            }

            // 2. Vérifier les URLs avec noindex
            $noindex_urls = $this->find_noindex_urls_in_sitemap();
            foreach ( $noindex_urls as $url ) {
                $errors[] = array(
                    'type' => 'noindex',
                    'severity' => 'medium',
                    'url' => $url,
                    'message' => __( 'URL avec balise noindex dans le sitemap', 'boss-seo' ),
                    'details' => __( 'Cette URL ne devrait pas être dans le sitemap', 'boss-seo' ),
                    'detected_at' => current_time( 'mysql' )
                );
            }

            // 3. Vérifier les URLs trop longues
            $long_urls = $this->find_long_urls();
            foreach ( $long_urls as $url ) {
                $errors[] = array(
                    'type' => 'long_url',
                    'severity' => 'low',
                    'url' => $url,
                    'message' => __( 'URL trop longue (>2048 caractères)', 'boss-seo' ),
                    'details' => sprintf( __( 'Longueur: %d caractères', 'boss-seo' ), strlen( $url ) ),
                    'detected_at' => current_time( 'mysql' )
                );
            }

            // 4. Vérifier les doublons
            $duplicate_urls = $this->find_duplicate_urls();
            foreach ( $duplicate_urls as $url ) {
                $errors[] = array(
                    'type' => 'duplicate',
                    'severity' => 'medium',
                    'url' => $url,
                    'message' => __( 'URL dupliquée dans le sitemap', 'boss-seo' ),
                    'details' => __( 'Cette URL apparaît plusieurs fois', 'boss-seo' ),
                    'detected_at' => current_time( 'mysql' )
                );
            }

            // 5. Vérifier les redirections
            $redirected_urls = $this->find_redirected_urls();
            foreach ( $redirected_urls as $url_data ) {
                $errors[] = array(
                    'type' => 'redirect',
                    'severity' => 'medium',
                    'url' => $url_data['url'],
                    'message' => sprintf( __( 'URL redirigée (%d)', 'boss-seo' ), $url_data['code'] ),
                    'details' => sprintf( __( 'Redirige vers: %s', 'boss-seo' ), $url_data['redirect_to'] ),
                    'detected_at' => current_time( 'mysql' )
                );
            }

        } catch ( Exception $e ) {
            error_log( 'Boss SEO: Erreur détection erreurs sitemap - ' . $e->getMessage() );
        }
        
        return $errors;
    }

    /**
     * Trouve les URLs avec balise noindex.
     *
     * @since    1.2.0
     * @return   array    Les URLs avec noindex.
     */
    private function find_noindex_urls_in_sitemap() {
        // Simulation - en réalité, il faudrait parser le sitemap et vérifier chaque URL
        $noindex_urls = array();
        
        // Exemple d'URLs qui pourraient avoir noindex
        $sample_urls = array(
            get_site_url() . '/sample-page/',
            get_site_url() . '/privacy-policy/'
        );
        
        foreach ( $sample_urls as $url ) {
            // Vérifier si l'URL a une balise noindex (simulation)
            if ( rand( 0, 10 ) < 2 ) { // 20% de chance
                $noindex_urls[] = $url;
            }
        }
        
        return $noindex_urls;
    }

    /**
     * Trouve les URLs trop longues.
     *
     * @since    1.2.0
     * @return   array    Les URLs trop longues.
     */
    private function find_long_urls() {
        $long_urls = array();
        
        // Récupérer les URLs personnalisées
        $custom_urls_manager = new Boss_Custom_Urls( $this->plugin_name, $this->version );
        $custom_urls = $custom_urls_manager->get_all_custom_urls();
        
        foreach ( $custom_urls as $url_data ) {
            if ( strlen( $url_data['loc'] ) > 2048 ) {
                $long_urls[] = $url_data['loc'];
            }
        }
        
        return $long_urls;
    }

    /**
     * Trouve les URLs dupliquées.
     *
     * @since    1.2.0
     * @return   array    Les URLs dupliquées.
     */
    private function find_duplicate_urls() {
        $duplicate_urls = array();
        
        // Récupérer toutes les URLs du sitemap et chercher les doublons
        // Simulation pour l'exemple
        if ( rand( 0, 10 ) < 3 ) { // 30% de chance d'avoir un doublon
            $duplicate_urls[] = get_site_url() . '/sample-duplicate/';
        }
        
        return $duplicate_urls;
    }

    /**
     * Trouve les URLs qui redirigent.
     *
     * @since    1.2.0
     * @return   array    Les URLs avec redirections.
     */
    private function find_redirected_urls() {
        $redirected_urls = array();
        
        // Simulation d'URLs avec redirections
        if ( rand( 0, 10 ) < 2 ) { // 20% de chance
            $redirected_urls[] = array(
                'url' => get_site_url() . '/old-page/',
                'code' => 301,
                'redirect_to' => get_site_url() . '/new-page/'
            );
        }
        
        return $redirected_urls;
    }

    /**
     * Récupère les métriques de performance des sitemaps.
     *
     * @since    1.2.0
     * @return   array    Les métriques de performance.
     */
    private function get_sitemap_performance() {
        $performance = array();
        
        $sitemap_urls = array(
            'sitemap.xml' => get_site_url() . '/sitemap.xml',
            'sitemap-post.xml' => get_site_url() . '/sitemap-post.xml',
            'sitemap-page.xml' => get_site_url() . '/sitemap-page.xml'
        );
        
        foreach ( $sitemap_urls as $name => $url ) {
            $start_time = microtime( true );
            $response = wp_remote_get( $url, array( 'timeout' => 10 ) );
            $end_time = microtime( true );
            
            $load_time = round( ( $end_time - $start_time ) * 1000, 2 ); // en ms
            $size = 0;
            $status = 'error';
            
            if ( ! is_wp_error( $response ) ) {
                $status_code = wp_remote_retrieve_response_code( $response );
                $status = $status_code === 200 ? 'ok' : 'warning';
                $body = wp_remote_retrieve_body( $response );
                $size = strlen( $body );
            }
            
            $performance[] = array(
                'name' => $name,
                'url' => $url,
                'load_time' => $load_time,
                'size' => $size,
                'size_formatted' => size_format( $size ),
                'status' => $status,
                'checked_at' => current_time( 'mysql' )
            );
        }
        
        return $performance;
    }

    /**
     * Récupère les données de benchmark d'indexation.
     *
     * @since    1.2.0
     * @return   array    Les données de benchmark.
     */
    private function get_indexation_benchmark() {
        // Simulation de données de benchmark
        return array(
            'your_site' => array(
                'rate' => 92.7,
                'avg_time' => 3.2, // jours
                'label' => __( 'Votre site', 'boss-seo' )
            ),
            'industry_avg' => array(
                'rate' => 78.5,
                'avg_time' => 7.8,
                'label' => __( 'Moyenne du secteur', 'boss-seo' )
            ),
            'best_practice' => array(
                'rate' => 95.0,
                'avg_time' => 1.5,
                'label' => __( 'Meilleures pratiques', 'boss-seo' )
            )
        );
    }

    /**
     * Retourne les statistiques par défaut en cas d'erreur.
     *
     * @since    1.2.0
     * @return   array    Les statistiques par défaut.
     */
    private function get_default_indexation_stats() {
        return array(
            'global' => array(
                'submitted' => 0,
                'indexed' => 0,
                'pending' => 0,
                'errors' => 0,
                'rate' => 0
            ),
            'by_content_type' => array(),
            'evolution' => array(),
            'errors_detail' => array(),
            'performance' => array(),
            'benchmark' => array()
        );
    }

    /**
     * Force la mise à jour des données de monitoring.
     *
     * @since    1.2.0
     * @return   array    Les nouvelles données.
     */
    public function refresh_monitoring_data() {
        // Supprimer les caches
        delete_transient( 'boss_seo_indexation_stats' );
        
        // Recalculer et retourner les nouvelles données
        return $this->get_indexation_stats();
    }
}
?>
