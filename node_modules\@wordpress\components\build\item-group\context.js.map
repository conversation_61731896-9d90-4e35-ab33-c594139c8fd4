{"version": 3, "names": ["_element", "require", "ItemGroupContext", "createContext", "size", "exports", "useItemGroupContext", "useContext"], "sources": ["@wordpress/components/src/item-group/context.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { createContext, useContext } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { ItemGroupContext as Context } from './types';\n\nexport const ItemGroupContext = createContext( {\n\tsize: 'medium',\n} as Context );\n\nexport const useItemGroupContext = () => useContext( ItemGroupContext );\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAQO,MAAMC,gBAAgB,GAAG,IAAAC,sBAAa,EAAE;EAC9CC,IAAI,EAAE;AACP,CAAa,CAAC;AAACC,OAAA,CAAAH,gBAAA,GAAAA,gBAAA;AAER,MAAMI,mBAAmB,GAAGA,CAAA,KAAM,IAAAC,mBAAU,EAAEL,gBAAiB,CAAC;AAACG,OAAA,CAAAC,mBAAA,GAAAA,mBAAA"}