{"version": 3, "names": ["_colord", "require", "_inputWithSlider", "RgbInput", "color", "onChange", "enableAlpha", "r", "g", "b", "a", "toRgb", "_react", "createElement", "Fragment", "InputWithSlider", "min", "max", "label", "abbreviation", "value", "nextR", "colord", "nextG", "nextB", "Math", "trunc", "nextA", "exports"], "sources": ["@wordpress/components/src/color-picker/rgb-input.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { colord } from 'colord';\n\n/**\n * Internal dependencies\n */\nimport { InputWithSlider } from './input-with-slider';\nimport type { RgbInputProps } from './types';\n\nexport const RgbInput = ( { color, onChange, enableAlpha }: RgbInputProps ) => {\n\tconst { r, g, b, a } = color.toRgb();\n\n\treturn (\n\t\t<>\n\t\t\t<InputWithSlider\n\t\t\t\tmin={ 0 }\n\t\t\t\tmax={ 255 }\n\t\t\t\tlabel=\"Red\"\n\t\t\t\tabbreviation=\"R\"\n\t\t\t\tvalue={ r }\n\t\t\t\tonChange={ ( nextR: number ) =>\n\t\t\t\t\tonChange( colord( { r: nextR, g, b, a } ) )\n\t\t\t\t}\n\t\t\t/>\n\t\t\t<InputWithSlider\n\t\t\t\tmin={ 0 }\n\t\t\t\tmax={ 255 }\n\t\t\t\tlabel=\"Green\"\n\t\t\t\tabbreviation=\"G\"\n\t\t\t\tvalue={ g }\n\t\t\t\tonChange={ ( nextG: number ) =>\n\t\t\t\t\tonChange( colord( { r, g: nextG, b, a } ) )\n\t\t\t\t}\n\t\t\t/>\n\t\t\t<InputWithSlider\n\t\t\t\tmin={ 0 }\n\t\t\t\tmax={ 255 }\n\t\t\t\tlabel=\"Blue\"\n\t\t\t\tabbreviation=\"B\"\n\t\t\t\tvalue={ b }\n\t\t\t\tonChange={ ( nextB: number ) =>\n\t\t\t\t\tonChange( colord( { r, g, b: nextB, a } ) )\n\t\t\t\t}\n\t\t\t/>\n\t\t\t{ enableAlpha && (\n\t\t\t\t<InputWithSlider\n\t\t\t\t\tmin={ 0 }\n\t\t\t\t\tmax={ 100 }\n\t\t\t\t\tlabel=\"Alpha\"\n\t\t\t\t\tabbreviation=\"A\"\n\t\t\t\t\tvalue={ Math.trunc( a * 100 ) }\n\t\t\t\t\tonChange={ ( nextA: number ) =>\n\t\t\t\t\t\tonChange(\n\t\t\t\t\t\t\tcolord( {\n\t\t\t\t\t\t\t\tr,\n\t\t\t\t\t\t\t\tg,\n\t\t\t\t\t\t\t\tb,\n\t\t\t\t\t\t\t\ta: nextA / 100,\n\t\t\t\t\t\t\t} )\n\t\t\t\t\t\t)\n\t\t\t\t\t}\n\t\t\t\t/>\n\t\t\t) }\n\t\t</>\n\t);\n};\n"], "mappings": ";;;;;;;AAGA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,gBAAA,GAAAD,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAIO,MAAME,QAAQ,GAAGA,CAAE;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAA2B,CAAC,KAAM;EAC9E,MAAM;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC;EAAE,CAAC,GAAGN,KAAK,CAACO,KAAK,CAAC,CAAC;EAEpC,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAACX,gBAAA,CAAAa,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,KAAK;IACXC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGb,CAAG;IACXF,QAAQ,EAAKgB,KAAa,IACzBhB,QAAQ,CAAE,IAAAiB,cAAM,EAAE;MAAEf,CAAC,EAAEc,KAAK;MAAEb,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAE,CAAE;EAC1C,CACD,CAAC,EACF,IAAAE,MAAA,CAAAC,aAAA,EAACX,gBAAA,CAAAa,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,OAAO;IACbC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGZ,CAAG;IACXH,QAAQ,EAAKkB,KAAa,IACzBlB,QAAQ,CAAE,IAAAiB,cAAM,EAAE;MAAEf,CAAC;MAAEC,CAAC,EAAEe,KAAK;MAAEd,CAAC;MAAEC;IAAE,CAAE,CAAE;EAC1C,CACD,CAAC,EACF,IAAAE,MAAA,CAAAC,aAAA,EAACX,gBAAA,CAAAa,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,MAAM;IACZC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGX,CAAG;IACXJ,QAAQ,EAAKmB,KAAa,IACzBnB,QAAQ,CAAE,IAAAiB,cAAM,EAAE;MAAEf,CAAC;MAAEC,CAAC;MAAEC,CAAC,EAAEe,KAAK;MAAEd;IAAE,CAAE,CAAE;EAC1C,CACD,CAAC,EACAJ,WAAW,IACZ,IAAAM,MAAA,CAAAC,aAAA,EAACX,gBAAA,CAAAa,eAAe;IACfC,GAAG,EAAG,CAAG;IACTC,GAAG,EAAG,GAAK;IACXC,KAAK,EAAC,OAAO;IACbC,YAAY,EAAC,GAAG;IAChBC,KAAK,EAAGK,IAAI,CAACC,KAAK,CAAEhB,CAAC,GAAG,GAAI,CAAG;IAC/BL,QAAQ,EAAKsB,KAAa,IACzBtB,QAAQ,CACP,IAAAiB,cAAM,EAAE;MACPf,CAAC;MACDC,CAAC;MACDC,CAAC;MACDC,CAAC,EAAEiB,KAAK,GAAG;IACZ,CAAE,CACH;EACA,CACD,CAED,CAAC;AAEL,CAAC;AAACC,OAAA,CAAAzB,QAAA,GAAAA,QAAA"}