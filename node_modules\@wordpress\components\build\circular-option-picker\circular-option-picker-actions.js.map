{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_button", "_dropdown", "DropdownLinkAction", "buttonProps", "className", "dropdownProps", "linkText", "_react", "createElement", "default", "classnames", "renderToggle", "isOpen", "onToggle", "onClick", "variant", "ButtonAction", "children", "additionalProps"], "sources": ["@wordpress/components/src/circular-option-picker/circular-option-picker-actions.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * Internal dependencies\n */\nimport Button from '../button';\nimport Dropdown from '../dropdown';\nimport type { DropdownLinkActionProps } from './types';\nimport type { WordPressComponentProps } from '../context';\nimport type { ButtonAsButtonProps } from '../button/types';\n\nexport function DropdownLinkAction( {\n\tbuttonProps,\n\tclassName,\n\tdropdownProps,\n\tlinkText,\n}: DropdownLinkActionProps ) {\n\treturn (\n\t\t<Dropdown\n\t\t\tclassName={ classnames(\n\t\t\t\t'components-circular-option-picker__dropdown-link-action',\n\t\t\t\tclassName\n\t\t\t) }\n\t\t\trenderToggle={ ( { isOpen, onToggle } ) => (\n\t\t\t\t<Button\n\t\t\t\t\taria-expanded={ isOpen }\n\t\t\t\t\taria-haspopup=\"true\"\n\t\t\t\t\tonClick={ onToggle }\n\t\t\t\t\tvariant=\"link\"\n\t\t\t\t\t{ ...buttonProps }\n\t\t\t\t>\n\t\t\t\t\t{ linkText }\n\t\t\t\t</Button>\n\t\t\t) }\n\t\t\t{ ...dropdownProps }\n\t\t/>\n\t);\n}\n\nexport function ButtonAction( {\n\tclassName,\n\tchildren,\n\t...additionalProps\n}: WordPressComponentProps< ButtonAsButtonProps, 'button', false > ) {\n\treturn (\n\t\t<Button\n\t\t\tclassName={ classnames(\n\t\t\t\t'components-circular-option-picker__clear',\n\t\t\t\tclassName\n\t\t\t) }\n\t\t\tvariant=\"tertiary\"\n\t\t\t{ ...additionalProps }\n\t\t>\n\t\t\t{ children }\n\t\t</Button>\n\t);\n}\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AAOO,SAASG,kBAAkBA,CAAE;EACnCC,WAAW;EACXC,SAAS;EACTC,aAAa;EACbC;AACwB,CAAC,EAAG;EAC5B,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACP,SAAA,CAAAQ,OAAQ;IACRL,SAAS,EAAG,IAAAM,mBAAU,EACrB,yDAAyD,EACzDN,SACD,CAAG;IACHO,YAAY,EAAGA,CAAE;MAAEC,MAAM;MAAEC;IAAS,CAAC,KACpC,IAAAN,MAAA,CAAAC,aAAA,EAACR,OAAA,CAAAS,OAAM;MACN,iBAAgBG,MAAQ;MACxB,iBAAc,MAAM;MACpBE,OAAO,EAAGD,QAAU;MACpBE,OAAO,EAAC,MAAM;MAAA,GACTZ;IAAW,GAEdG,QACK,CACN;IAAA,GACED;EAAa,CAClB,CAAC;AAEJ;AAEO,SAASW,YAAYA,CAAE;EAC7BZ,SAAS;EACTa,QAAQ;EACR,GAAGC;AAC6D,CAAC,EAAG;EACpE,OACC,IAAAX,MAAA,CAAAC,aAAA,EAACR,OAAA,CAAAS,OAAM;IACNL,SAAS,EAAG,IAAAM,mBAAU,EACrB,0CAA0C,EAC1CN,SACD,CAAG;IACHW,OAAO,EAAC,UAAU;IAAA,GACbG;EAAe,GAElBD,QACK,CAAC;AAEX"}