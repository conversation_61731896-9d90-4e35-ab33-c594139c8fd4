<?php
/**
 * Script de débogage pour les paramètres de sitemap
 * À placer dans le répertoire racine de WordPress et à exécuter via le navigateur
 */

// Vérifier que nous sommes dans WordPress
if (!file_exists('./wp-config.php')) {
    die('Ce script doit être placé dans le répertoire racine de WordPress');
}

// Charger WordPress
require_once('./wp-config.php');
require_once('./wp-load.php');

// Activer le débogage pour cette session
if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', true);
}
if (!defined('WP_DEBUG_LOG')) {
    define('WP_DEBUG_LOG', true);
}

echo "<h1>🔧 Débogage des Paramètres de Sitemap Boss SEO</h1>";

// 1. Vérifier si le plugin est actif
echo "<h2>1. Vérification du plugin</h2>";
if (is_plugin_active('Bossseov1.1/boss-seo.php')) {
    echo "<p>✅ Plugin Boss SEO actif</p>";
} else {
    echo "<p>❌ Plugin Boss SEO non actif</p>";
    die();
}

// 2. Vérifier les classes
echo "<h2>2. Vérification des classes</h2>";
$classes_to_check = [
    'Boss_Robots_Sitemap_Controller',
    'Boss_Advanced_Sitemaps',
    'Boss_Sitemap_Utils'
];

foreach ($classes_to_check as $class) {
    if (class_exists($class)) {
        echo "<p>✅ Classe {$class} chargée</p>";
    } else {
        echo "<p>❌ Classe {$class} non trouvée</p>";
    }
}

// 3. Tester l'instanciation du contrôleur
echo "<h2>3. Test d'instanciation du contrôleur</h2>";
try {
    $controller = new Boss_Robots_Sitemap_Controller('boss-seo', '1.2.0');
    echo "<p>✅ Contrôleur instancié avec succès</p>";
} catch (Exception $e) {
    echo "<p>❌ Erreur d'instanciation: " . $e->getMessage() . "</p>";
    die();
}

// 4. Tester les paramètres actuels
echo "<h2>4. Paramètres actuels</h2>";
$current_settings = get_option('boss-seo_advanced_sitemap_settings', array());
if (empty($current_settings)) {
    echo "<p>⚠️ Aucun paramètre trouvé</p>";
} else {
    echo "<p>✅ Paramètres trouvés:</p>";
    echo "<pre>" . print_r($current_settings, true) . "</pre>";
}

// 5. Tester la sanitisation
echo "<h2>5. Test de sanitisation</h2>";
$test_settings = array(
    'enabled' => true,
    'includeImages' => false,
    'includeLastMod' => true,
    'enableAutoUpdate' => false,
    'defaultChangeFreq' => 'weekly',
    'defaultPriority' => 0.7,
    'includedPostTypes' => array('post', 'page'),
    'enablePostTypeSitemaps' => array(
        'post' => true,
        'page' => true
    ),
    'customUrls' => array()
);

echo "<p>📤 Paramètres de test:</p>";
echo "<pre>" . print_r($test_settings, true) . "</pre>";

// Utiliser la réflexion pour accéder à la méthode privée
try {
    $reflection = new ReflectionClass($controller);
    $sanitize_method = $reflection->getMethod('sanitize_sitemap_settings');
    $sanitize_method->setAccessible(true);
    
    $sanitized = $sanitize_method->invoke($controller, $test_settings);
    
    echo "<p>✅ Sanitisation réussie:</p>";
    echo "<pre>" . print_r($sanitized, true) . "</pre>";
} catch (Exception $e) {
    echo "<p>❌ Erreur de sanitisation: " . $e->getMessage() . "</p>";
}

// 6. Tester la sauvegarde directe
echo "<h2>6. Test de sauvegarde directe</h2>";
try {
    $result = update_option('boss-seo_advanced_sitemap_settings', $test_settings);
    if ($result) {
        echo "<p>✅ Sauvegarde directe réussie</p>";
        
        // Vérifier la récupération
        $retrieved = get_option('boss-seo_advanced_sitemap_settings');
        if ($retrieved === $test_settings) {
            echo "<p>✅ Récupération vérifiée</p>";
        } else {
            echo "<p>⚠️ Données récupérées différentes</p>";
            echo "<pre>" . print_r($retrieved, true) . "</pre>";
        }
    } else {
        echo "<p>❌ Échec de la sauvegarde directe</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Erreur de sauvegarde: " . $e->getMessage() . "</p>";
}

// 7. Simuler une requête REST
echo "<h2>7. Simulation de requête REST</h2>";

// Créer une fausse requête
class Fake_WP_REST_Request {
    private $params = array();
    
    public function __construct($params) {
        $this->params = $params;
    }
    
    public function get_param($key) {
        return isset($this->params[$key]) ? $this->params[$key] : null;
    }
}

try {
    $fake_request = new Fake_WP_REST_Request(array('settings' => $test_settings));
    
    // Tester la méthode save_advanced_sitemap_settings
    $response = $controller->save_advanced_sitemap_settings($fake_request);
    
    echo "<p>✅ Méthode save_advanced_sitemap_settings exécutée</p>";
    echo "<p>📤 Réponse:</p>";
    echo "<pre>" . print_r($response, true) . "</pre>";
    
} catch (Exception $e) {
    echo "<p>❌ Erreur lors de la simulation: " . $e->getMessage() . "</p>";
    echo "<p>📍 Trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// 8. Vérifier les logs d'erreur récents
echo "<h2>8. Logs d'erreur récents</h2>";
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -20); // 20 dernières lignes
    
    $boss_seo_logs = array_filter($recent_lines, function($line) {
        return strpos($line, 'Boss SEO') !== false;
    });
    
    if (!empty($boss_seo_logs)) {
        echo "<p>📋 Logs Boss SEO récents:</p>";
        echo "<pre>" . implode("\n", $boss_seo_logs) . "</pre>";
    } else {
        echo "<p>ℹ️ Aucun log Boss SEO récent trouvé</p>";
    }
} else {
    echo "<p>⚠️ Fichier de log non trouvé: {$log_file}</p>";
    echo "<p>Activez WP_DEBUG_LOG dans wp-config.php</p>";
}

// 9. Informations système
echo "<h2>9. Informations système</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>WordPress Version:</strong> " . get_bloginfo('version') . "</p>";
echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . "</p>";

// 10. Instructions
echo "<h2>10. Instructions de test</h2>";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h3>🧪 Pour tester l'API REST :</h3>";
echo "<ol>";
echo "<li>Ouvrez la console du navigateur (F12)</li>";
echo "<li>Copiez et exécutez le contenu de <code>test-sitemap-settings-save.js</code></li>";
echo "<li>Observez les résultats et les logs d'erreur</li>";
echo "<li>Rechargez cette page pour voir les nouveaux logs</li>";
echo "</ol>";
echo "</div>";

echo "<h3>✅ Débogage terminé !</h3>";
echo "<p>Si des erreurs persistent, vérifiez :</p>";
echo "<ul>";
echo "<li>Les permissions de fichiers</li>";
echo "<li>La configuration du serveur</li>";
echo "<li>Les conflits avec d'autres plugins</li>";
echo "<li>Les logs d'erreur PHP du serveur</li>";
echo "</ul>";
?>
