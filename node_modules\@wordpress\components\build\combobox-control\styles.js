"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.InputWrapperFlex = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react = require("@emotion/react");
var _flex = require("../flex");
var _space = require("../utils/space");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const deprecatedDefaultSize = ({
  __next40pxDefaultSize
}) => !__next40pxDefaultSize && /*#__PURE__*/(0, _react.css)("height:28px;padding-left:", (0, _space.space)(1), ";padding-right:", (0, _space.space)(1), ";" + (process.env.NODE_ENV === "production" ? "" : ";label:deprecatedDefaultSize;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY29tYm9ib3gtY29udHJvbC9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBaUJJIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY29tYm9ib3gtY29udHJvbC9zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5pbXBvcnQgeyBjc3MgfSBmcm9tICdAZW1vdGlvbi9yZWFjdCc7XG5cbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IEZsZXggfSBmcm9tICcuLi9mbGV4JztcbmltcG9ydCB7IHNwYWNlIH0gZnJvbSAnLi4vdXRpbHMvc3BhY2UnO1xuaW1wb3J0IHR5cGUgeyBDb21ib2JveENvbnRyb2xQcm9wcyB9IGZyb20gJy4vdHlwZXMnO1xuXG5jb25zdCBkZXByZWNhdGVkRGVmYXVsdFNpemUgPSAoIHtcblx0X19uZXh0NDBweERlZmF1bHRTaXplLFxufTogUGljazwgQ29tYm9ib3hDb250cm9sUHJvcHMsICdfX25leHQ0MHB4RGVmYXVsdFNpemUnID4gKSA9PlxuXHQhIF9fbmV4dDQwcHhEZWZhdWx0U2l6ZSAmJlxuXHRjc3NgXG5cdFx0aGVpZ2h0OiAyOHB4OyAvLyAzMHB4IC0gMnB4IHZlcnRpY2FsIGJvcmRlcnMgb24gcGFyZW50IGNvbnRhaW5lclxuXHRcdHBhZGRpbmctbGVmdDogJHsgc3BhY2UoIDEgKSB9O1xuXHRcdHBhZGRpbmctcmlnaHQ6ICR7IHNwYWNlKCAxICkgfTtcblx0YDtcblxuZXhwb3J0IGNvbnN0IElucHV0V3JhcHBlckZsZXggPSBzdHlsZWQoIEZsZXggKWBcblx0aGVpZ2h0OiAzOHB4OyAvLyA0MHB4IC0gMnB4IHZlcnRpY2FsIGJvcmRlcnMgb24gcGFyZW50IGNvbnRhaW5lclxuXHRwYWRkaW5nLWxlZnQ6ICR7IHNwYWNlKCAyICkgfTtcblx0cGFkZGluZy1yaWdodDogJHsgc3BhY2UoIDIgKSB9O1xuXG5cdCR7IGRlcHJlY2F0ZWREZWZhdWx0U2l6ZSB9XG5gO1xuIl19 */");
const InputWrapperFlex = ( /*#__PURE__*/0, _base.default)(_flex.Flex, process.env.NODE_ENV === "production" ? {
  target: "evuatpg0"
} : {
  target: "evuatpg0",
  label: "InputWrapperFlex"
})("height:38px;padding-left:", (0, _space.space)(2), ";padding-right:", (0, _space.space)(2), ";", deprecatedDefaultSize, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY29tYm9ib3gtY29udHJvbC9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBdUI4QyIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2NvbWJvYm94LWNvbnRyb2wvc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuaW1wb3J0IHsgY3NzIH0gZnJvbSAnQGVtb3Rpb24vcmVhY3QnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBGbGV4IH0gZnJvbSAnLi4vZmxleCc7XG5pbXBvcnQgeyBzcGFjZSB9IGZyb20gJy4uL3V0aWxzL3NwYWNlJztcbmltcG9ydCB0eXBlIHsgQ29tYm9ib3hDb250cm9sUHJvcHMgfSBmcm9tICcuL3R5cGVzJztcblxuY29uc3QgZGVwcmVjYXRlZERlZmF1bHRTaXplID0gKCB7XG5cdF9fbmV4dDQwcHhEZWZhdWx0U2l6ZSxcbn06IFBpY2s8IENvbWJvYm94Q29udHJvbFByb3BzLCAnX19uZXh0NDBweERlZmF1bHRTaXplJyA+ICkgPT5cblx0ISBfX25leHQ0MHB4RGVmYXVsdFNpemUgJiZcblx0Y3NzYFxuXHRcdGhlaWdodDogMjhweDsgLy8gMzBweCAtIDJweCB2ZXJ0aWNhbCBib3JkZXJzIG9uIHBhcmVudCBjb250YWluZXJcblx0XHRwYWRkaW5nLWxlZnQ6ICR7IHNwYWNlKCAxICkgfTtcblx0XHRwYWRkaW5nLXJpZ2h0OiAkeyBzcGFjZSggMSApIH07XG5cdGA7XG5cbmV4cG9ydCBjb25zdCBJbnB1dFdyYXBwZXJGbGV4ID0gc3R5bGVkKCBGbGV4IClgXG5cdGhlaWdodDogMzhweDsgLy8gNDBweCAtIDJweCB2ZXJ0aWNhbCBib3JkZXJzIG9uIHBhcmVudCBjb250YWluZXJcblx0cGFkZGluZy1sZWZ0OiAkeyBzcGFjZSggMiApIH07XG5cdHBhZGRpbmctcmlnaHQ6ICR7IHNwYWNlKCAyICkgfTtcblxuXHQkeyBkZXByZWNhdGVkRGVmYXVsdFNpemUgfVxuYDtcbiJdfQ== */"));
exports.InputWrapperFlex = InputWrapperFlex;
//# sourceMappingURL=styles.js.map