{"version": 3, "names": ["_view", "require", "_context", "_hook", "BorderBoxControlVisualizer", "props", "forwardedRef", "value", "otherProps", "useBorderBoxControlVisualizer", "_react", "createElement", "View", "ref", "ConnectedBorderBoxControlVisualizer", "contextConnect", "_default", "exports", "default"], "sources": ["@wordpress/components/src/border-box-control/border-box-control-visualizer/component.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { View } from '../../view';\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useBorderBoxControlVisualizer } from './hook';\n\nimport type { VisualizerProps } from '../types';\n\nconst BorderBoxControlVisualizer = (\n\tprops: WordPressComponentProps< VisualizerProps, 'div' >,\n\tforwardedRef: React.ForwardedRef< any >\n) => {\n\tconst { value, ...otherProps } = useBorderBoxControlVisualizer( props );\n\n\treturn <View { ...otherProps } ref={ forwardedRef } />;\n};\n\nconst ConnectedBorderBoxControlVisualizer = contextConnect(\n\tBorderBoxControlVisualizer,\n\t'BorderBoxControlVisualizer'\n);\nexport default ConnectedBorderBoxControlVisualizer;\n"], "mappings": ";;;;;;;AAQA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAQA,MAAMG,0BAA0B,GAAGA,CAClCC,KAAwD,EACxDC,YAAuC,KACnC;EACJ,MAAM;IAAEC,KAAK;IAAE,GAAGC;EAAW,CAAC,GAAG,IAAAC,mCAA6B,EAAEJ,KAAM,CAAC;EAEvE,OAAO,IAAAK,MAAA,CAAAC,aAAA,EAACX,KAAA,CAAAY,IAAI;IAAA,GAAMJ,UAAU;IAAGK,GAAG,EAAGP;EAAc,CAAE,CAAC;AACvD,CAAC;AAED,MAAMQ,mCAAmC,GAAG,IAAAC,uBAAc,EACzDX,0BAA0B,EAC1B,4BACD,CAAC;AAAC,IAAAY,QAAA,GACaF,mCAAmC;AAAAG,OAAA,CAAAC,OAAA,GAAAF,QAAA"}