{"version": 3, "names": [], "sources": ["@wordpress/components/src/h-stack/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { FlexProps } from '../flex/types';\n\nexport type HStackAlignment =\n\t| 'bottom'\n\t| 'bottomLeft'\n\t| 'bottomRight'\n\t| 'center'\n\t| 'edge'\n\t| 'left'\n\t| 'right'\n\t| 'stretch'\n\t| 'top'\n\t| 'topLeft'\n\t| 'topRight';\n\nexport type AlignmentProps = {\n\tjustify?: CSSProperties[ 'justifyContent' ];\n\talign?: CSSProperties[ 'alignItems' ];\n};\n\nexport type Alignments = Record< HStackAlignment, AlignmentProps >;\n\nexport type Props = Omit< FlexProps, 'align' | 'gap' > & {\n\t/**\n\t * Determines how the child elements are aligned.\n\t *\n\t * * `top`: Aligns content to the top.\n\t * * `topLeft`: Aligns content to the top/left.\n\t * * `topRight`: Aligns content to the top/right.\n\t * * `left`: Aligns content to the left.\n\t * * `center`: Aligns content to the center.\n\t * * `right`: Aligns content to the right.\n\t * * `bottom`: Aligns content to the bottom.\n\t * * `bottomLeft`: Aligns content to the bottom/left.\n\t * * `bottomRight`: Aligns content to the bottom/right.\n\t * * `edge`: Justifies content to be evenly spread out up to the main axis edges of the container.\n\t * * `stretch`: Stretches content to the cross axis edges of the container.\n\t *\n\t * @default 'edge'\n\t */\n\talignment?: HStackAlignment | CSSProperties[ 'alignItems' ];\n\t/**\n\t * The amount of space between each child element. Spacing in between each child can be adjusted by using `spacing`.\n\t * The value of `spacing` works as a multiplier to the library's grid system (base of `4px`).\n\t *\n\t * @default 2\n\t */\n\tspacing?: CSSProperties[ 'width' ];\n};\n"], "mappings": ""}