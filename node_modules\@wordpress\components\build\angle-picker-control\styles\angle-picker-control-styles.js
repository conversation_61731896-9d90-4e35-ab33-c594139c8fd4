"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UnitText = exports.Root = exports.CircleRoot = exports.CircleIndicatorWrapper = exports.CircleIndicator = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react = require("@emotion/react");
var _flex = require("../../flex");
var _utils = require("../../utils");
var _space = require("../../utils/space");
var _text = require("../../text");
var _configValues = _interopRequireDefault(require("../../utils/config-values"));
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const CIRCLE_SIZE = 32;
const INNER_CIRCLE_SIZE = 6;
const deprecatedBottomMargin = ({
  __nextHasNoMarginBottom
}) => {
  return !__nextHasNoMarginBottom ? /*#__PURE__*/(0, _react.css)("margin-bottom:", (0, _space.space)(2), ";" + (process.env.NODE_ENV === "production" ? "" : ";label:deprecatedBottomMargin;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */") : '';
};
const Root = ( /*#__PURE__*/0, _base.default)(_flex.Flex, process.env.NODE_ENV === "production" ? {
  target: "eln3bjz4"
} : {
  target: "eln3bjz4",
  label: "Root"
})(deprecatedBottomMargin, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.Root = Root;
const CircleRoot = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "eln3bjz3"
} : {
  target: "eln3bjz3",
  label: "CircleRoot"
})("border-radius:50%;border:", _configValues.default.borderWidth, " solid ", _utils.COLORS.ui.border, ";box-sizing:border-box;cursor:grab;height:", CIRCLE_SIZE, "px;overflow:hidden;width:", CIRCLE_SIZE, "px;:active{cursor:grabbing;}" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.CircleRoot = CircleRoot;
const CircleIndicatorWrapper = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "eln3bjz2"
} : {
  target: "eln3bjz2",
  label: "CircleIndicatorWrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "1r307gh",
  styles: "box-sizing:border-box;position:relative;width:100%;height:100%;:focus-visible{outline:none;}"
} : {
  name: "1r307gh",
  styles: "box-sizing:border-box;position:relative;width:100%;height:100%;:focus-visible{outline:none;}",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.CircleIndicatorWrapper = CircleIndicatorWrapper;
const CircleIndicator = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "eln3bjz1"
} : {
  target: "eln3bjz1",
  label: "CircleIndicator"
})("background:", _utils.COLORS.theme.accent, ";border-radius:50%;box-sizing:border-box;display:block;left:50%;top:4px;transform:translateX( -50% );position:absolute;width:", INNER_CIRCLE_SIZE, "px;height:", INNER_CIRCLE_SIZE, "px;" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYW5nbGUtcGlja2VyLWNvbnRyb2wvc3R5bGVzL2FuZ2xlLXBpY2tlci1jb250cm9sLXN0eWxlcy50c3giXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBMkR5QyIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2FuZ2xlLXBpY2tlci1jb250cm9sL3N0eWxlcy9hbmdsZS1waWNrZXItY29udHJvbC1zdHlsZXMudHN4Iiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHsgY3NzIH0gZnJvbSAnQGVtb3Rpb24vcmVhY3QnO1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgeyBGbGV4IH0gZnJvbSAnLi4vLi4vZmxleCc7XG5pbXBvcnQgeyBDT0xPUlMgfSBmcm9tICcuLi8uLi91dGlscyc7XG5pbXBvcnQgeyBzcGFjZSB9IGZyb20gJy4uLy4uL3V0aWxzL3NwYWNlJztcbmltcG9ydCB7IFRleHQgfSBmcm9tICcuLi8uLi90ZXh0JztcbmltcG9ydCBDT05GSUcgZnJvbSAnLi4vLi4vdXRpbHMvY29uZmlnLXZhbHVlcyc7XG5cbmltcG9ydCB0eXBlIHsgQW5nbGVQaWNrZXJDb250cm9sUHJvcHMgfSBmcm9tICcuLi90eXBlcyc7XG5cbmNvbnN0IENJUkNMRV9TSVpFID0gMzI7XG5jb25zdCBJTk5FUl9DSVJDTEVfU0laRSA9IDY7XG5cbmNvbnN0IGRlcHJlY2F0ZWRCb3R0b21NYXJnaW4gPSAoIHtcblx0X19uZXh0SGFzTm9NYXJnaW5Cb3R0b20sXG59OiBQaWNrPCBBbmdsZVBpY2tlckNvbnRyb2xQcm9wcywgJ19fbmV4dEhhc05vTWFyZ2luQm90dG9tJyA+ICkgPT4ge1xuXHRyZXR1cm4gISBfX25leHRIYXNOb01hcmdpbkJvdHRvbVxuXHRcdD8gY3NzYFxuXHRcdFx0XHRtYXJnaW4tYm90dG9tOiAkeyBzcGFjZSggMiApIH07XG5cdFx0ICBgXG5cdFx0OiAnJztcbn07XG5cbmV4cG9ydCBjb25zdCBSb290ID0gc3R5bGVkKCBGbGV4IClgXG5cdCR7IGRlcHJlY2F0ZWRCb3R0b21NYXJnaW4gfVxuYDtcblxuZXhwb3J0IGNvbnN0IENpcmNsZVJvb3QgPSBzdHlsZWQuZGl2YFxuXHRib3JkZXItcmFkaXVzOiA1MCU7XG5cdGJvcmRlcjogJHsgQ09ORklHLmJvcmRlcldpZHRoIH0gc29saWQgJHsgQ09MT1JTLnVpLmJvcmRlciB9O1xuXHRib3gtc2l6aW5nOiBib3JkZXItYm94O1xuXHRjdXJzb3I6IGdyYWI7XG5cdGhlaWdodDogJHsgQ0lSQ0xFX1NJWkUgfXB4O1xuXHRvdmVyZmxvdzogaGlkZGVuO1xuXHR3aWR0aDogJHsgQ0lSQ0xFX1NJWkUgfXB4O1xuXG5cdDphY3RpdmUge1xuXHRcdGN1cnNvcjogZ3JhYmJpbmc7XG5cdH1cbmA7XG5cbmV4cG9ydCBjb25zdCBDaXJjbGVJbmRpY2F0b3JXcmFwcGVyID0gc3R5bGVkLmRpdmBcblx0Ym94LXNpemluZzogYm9yZGVyLWJveDtcblx0cG9zaXRpb246IHJlbGF0aXZlO1xuXHR3aWR0aDogMTAwJTtcblx0aGVpZ2h0OiAxMDAlO1xuXG5cdDpmb2N1cy12aXNpYmxlIHtcblx0XHRvdXRsaW5lOiBub25lO1xuXHR9XG5gO1xuXG5leHBvcnQgY29uc3QgQ2lyY2xlSW5kaWNhdG9yID0gc3R5bGVkLmRpdmBcblx0YmFja2dyb3VuZDogJHsgQ09MT1JTLnRoZW1lLmFjY2VudCB9O1xuXHRib3JkZXItcmFkaXVzOiA1MCU7XG5cdGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG5cdGRpc3BsYXk6IGJsb2NrO1xuXHRsZWZ0OiA1MCU7XG5cdHRvcDogNHB4O1xuXHR0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoIC01MCUgKTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHR3aWR0aDogJHsgSU5ORVJfQ0lSQ0xFX1NJWkUgfXB4O1xuXHRoZWlnaHQ6ICR7IElOTkVSX0NJUkNMRV9TSVpFIH1weDtcbmA7XG5cbmV4cG9ydCBjb25zdCBVbml0VGV4dCA9IHN0eWxlZCggVGV4dCApYFxuXHRjb2xvcjogJHsgQ09MT1JTLnRoZW1lLmFjY2VudCB9O1xuXHRtYXJnaW4tcmlnaHQ6ICR7IHNwYWNlKCAzICkgfTtcbmA7XG4iXX0= */"));
exports.CircleIndicator = CircleIndicator;
const UnitText = ( /*#__PURE__*/0, _base.default)(_text.Text, process.env.NODE_ENV === "production" ? {
  target: "eln3bjz0"
} : {
  target: "eln3bjz0",
  label: "UnitText"
})("color:", _utils.COLORS.theme.accent, ";margin-right:", (0, _space.space)(3), ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.UnitText = UnitText;
//# sourceMappingURL=angle-picker-control-styles.js.map