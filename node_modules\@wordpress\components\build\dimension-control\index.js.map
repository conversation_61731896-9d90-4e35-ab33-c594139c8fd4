{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_i18n", "_icon", "_selectControl", "_sizes", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "DimensionControl", "props", "__next40pxDefaultSize", "label", "value", "sizes", "sizesTable", "icon", "onChange", "className", "onChangeSpacingSize", "val", "theSize", "findSizeBySlug", "slug", "undefined", "formatSizesAsOptions", "theSizes", "options", "map", "name", "__", "selectLabel", "_react", "createElement", "Fragment", "classnames", "hideLabelFromVision", "_default", "exports"], "sources": ["@wordpress/components/src/dimension-control/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport Icon from '../icon';\nimport SelectControl from '../select-control';\nimport sizesTable, { findSizeBySlug } from './sizes';\nimport type { DimensionControlProps, Size } from './types';\nimport type { SelectControlSingleSelectionProps } from '../select-control/types';\n\n/**\n * `DimensionControl` is a component designed to provide a UI to control spacing and/or dimensions.\n *\n * This feature is still experimental. “Experimental” means this is an early implementation subject to drastic and breaking changes.\n *\n * ```jsx\n * import { __experimentalDimensionControl as DimensionControl } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * export default function MyCustomDimensionControl() {\n * \tconst [ paddingSize, setPaddingSize ] = useState( '' );\n *\n * \treturn (\n * \t\t<DimensionControl\n * \t\t\tlabel={ 'Padding' }\n * \t\t\ticon={ 'desktop' }\n * \t\t\tonChange={ ( value ) => setPaddingSize( value ) }\n * \t\t\tvalue={ paddingSize }\n * \t\t/>\n * \t);\n * }\n * ```\n */\nexport function DimensionControl( props: DimensionControlProps ) {\n\tconst {\n\t\t__next40pxDefaultSize = false,\n\t\tlabel,\n\t\tvalue,\n\t\tsizes = sizesTable,\n\t\ticon,\n\t\tonChange,\n\t\tclassName = '',\n\t} = props;\n\n\tconst onChangeSpacingSize: SelectControlSingleSelectionProps[ 'onChange' ] =\n\t\t( val ) => {\n\t\t\tconst theSize = findSizeBySlug( sizes, val );\n\n\t\t\tif ( ! theSize || value === theSize.slug ) {\n\t\t\t\tonChange?.( undefined );\n\t\t\t} else if ( typeof onChange === 'function' ) {\n\t\t\t\tonChange( theSize.slug );\n\t\t\t}\n\t\t};\n\n\tconst formatSizesAsOptions = ( theSizes: Size[] ) => {\n\t\tconst options = theSizes.map( ( { name, slug } ) => ( {\n\t\t\tlabel: name,\n\t\t\tvalue: slug,\n\t\t} ) );\n\n\t\treturn [\n\t\t\t{\n\t\t\t\tlabel: __( 'Default' ),\n\t\t\t\tvalue: '',\n\t\t\t},\n\t\t\t...options,\n\t\t];\n\t};\n\n\tconst selectLabel = (\n\t\t<>\n\t\t\t{ icon && <Icon icon={ icon } /> }\n\t\t\t{ label }\n\t\t</>\n\t);\n\n\treturn (\n\t\t<SelectControl\n\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\tclassName={ classnames(\n\t\t\t\tclassName,\n\t\t\t\t'block-editor-dimension-control'\n\t\t\t) }\n\t\t\tlabel={ selectLabel }\n\t\t\thideLabelFromVision={ false }\n\t\t\tvalue={ value }\n\t\t\tonChange={ onChangeSpacingSize }\n\t\t\toptions={ formatSizesAsOptions( sizes ) }\n\t\t/>\n\t);\n}\n\nexport default DimensionControl;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,KAAA,GAAAD,OAAA;AAKA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAC,uBAAA,CAAAL,OAAA;AAAqD,SAAAM,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAfrD;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASW,gBAAgBA,CAAEC,KAA4B,EAAG;EAChE,MAAM;IACLC,qBAAqB,GAAG,KAAK;IAC7BC,KAAK;IACLC,KAAK;IACLC,KAAK,GAAGC,cAAU;IAClBC,IAAI;IACJC,QAAQ;IACRC,SAAS,GAAG;EACb,CAAC,GAAGR,KAAK;EAET,MAAMS,mBAAoE,GACvEC,GAAG,IAAM;IACV,MAAMC,OAAO,GAAG,IAAAC,qBAAc,EAAER,KAAK,EAAEM,GAAI,CAAC;IAE5C,IAAK,CAAEC,OAAO,IAAIR,KAAK,KAAKQ,OAAO,CAACE,IAAI,EAAG;MAC1CN,QAAQ,GAAIO,SAAU,CAAC;IACxB,CAAC,MAAM,IAAK,OAAOP,QAAQ,KAAK,UAAU,EAAG;MAC5CA,QAAQ,CAAEI,OAAO,CAACE,IAAK,CAAC;IACzB;EACD,CAAC;EAEF,MAAME,oBAAoB,GAAKC,QAAgB,IAAM;IACpD,MAAMC,OAAO,GAAGD,QAAQ,CAACE,GAAG,CAAE,CAAE;MAAEC,IAAI;MAAEN;IAAK,CAAC,MAAQ;MACrDX,KAAK,EAAEiB,IAAI;MACXhB,KAAK,EAAEU;IACR,CAAC,CAAG,CAAC;IAEL,OAAO,CACN;MACCX,KAAK,EAAE,IAAAkB,QAAE,EAAE,SAAU,CAAC;MACtBjB,KAAK,EAAE;IACR,CAAC,EACD,GAAGc,OAAO,CACV;EACF,CAAC;EAED,MAAMI,WAAW,GAChB,IAAAC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGlB,IAAI,IAAI,IAAAgB,MAAA,CAAAC,aAAA,EAAClD,KAAA,CAAAW,OAAI;IAACsB,IAAI,EAAGA;EAAM,CAAE,CAAC,EAC9BJ,KACD,CACF;EAED,OACC,IAAAoB,MAAA,CAAAC,aAAA,EAACjD,cAAA,CAAAU,OAAa;IACbiB,qBAAqB,EAAGA,qBAAuB;IAC/CO,SAAS,EAAG,IAAAiB,mBAAU,EACrBjB,SAAS,EACT,gCACD,CAAG;IACHN,KAAK,EAAGmB,WAAa;IACrBK,mBAAmB,EAAG,KAAO;IAC7BvB,KAAK,EAAGA,KAAO;IACfI,QAAQ,EAAGE,mBAAqB;IAChCQ,OAAO,EAAGF,oBAAoB,CAAEX,KAAM;EAAG,CACzC,CAAC;AAEJ;AAAC,IAAAuB,QAAA,GAEc5B,gBAAgB;AAAA6B,OAAA,CAAA5C,OAAA,GAAA2C,QAAA"}