{"version": 3, "names": ["_components", "require", "dashicons", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "Dashicon", "icon", "extraProps", "_react", "createElement", "Icon", "empty", "_default", "exports"], "sources": ["@wordpress/components/src/dashicon/index.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { Icon } from '@wordpress/components';\n\n/**\n * Internal dependencies\n */\nimport * as dashicons from '../mobile/dashicons';\n\n// A predefined SVG icon is rendered instead of Dashicon because it's not supported in the native version.\nfunction Dashicon( { icon, ...extraProps } ) {\n\treturn (\n\t\t<Icon icon={ dashicons[ icon ] || dashicons.empty } { ...extraProps } />\n\t);\n}\n\nexport default Dashicon;\n"], "mappings": ";;;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AAKA,IAAAC,SAAA,GAAAC,uBAAA,CAAAF,OAAA;AAAiD,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AARjD;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA,SAASW,QAAQA,CAAE;EAAEC,IAAI;EAAE,GAAGC;AAAW,CAAC,EAAG;EAC5C,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC9B,WAAA,CAAA+B,IAAI;IAACJ,IAAI,EAAGzB,SAAS,CAAEyB,IAAI,CAAE,IAAIzB,SAAS,CAAC8B,KAAO;IAAA,GAAMJ;EAAU,CAAI,CAAC;AAE1E;AAAC,IAAAK,QAAA,GAEcP,QAAQ;AAAAQ,OAAA,CAAAvB,OAAA,GAAAsB,QAAA"}