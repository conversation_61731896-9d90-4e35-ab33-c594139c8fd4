"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LegacyAdapter = void 0;
var _react = require("react");
var _component = _interopRequireDefault(require("./component"));
var _useDeprecatedProps = require("./use-deprecated-props");
/**
 * Internal dependencies
 */

const LegacyAdapter = props => {
  return (0, _react.createElement)(_component.default, {
    ...(0, _useDeprecatedProps.useDeprecatedProps)(props)
  });
};
exports.LegacyAdapter = LegacyAdapter;
//# sourceMappingURL=legacy-adapter.js.map