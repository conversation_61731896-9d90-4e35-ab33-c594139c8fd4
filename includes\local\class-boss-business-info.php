<?php
/**
 * Classe pour la gestion des informations d'entreprise.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 */

/**
 * Classe pour la gestion des informations d'entreprise.
 *
 * Cette classe gère toutes les fonctionnalités liées aux informations d'entreprise.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/local
 * <AUTHOR> SEO Team
 */
class Boss_Business_Info {

    /**
     * Le nom du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $plugin_name    Le nom du plugin.
     */
    protected $plugin_name;

    /**
     * La version du plugin.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $version    La version actuelle du plugin.
     */
    protected $version;

    /**
     * Le préfixe pour les options.
     *
     * @since    1.2.0
     * @access   protected
     * @var      string    $option_prefix    Le préfixe pour les options.
     */
    protected $option_prefix = 'boss_business_';

    /**
     * Initialise la classe et définit ses propriétés.
     *
     * @since    1.2.0
     * @param    string    $plugin_name       Le nom du plugin.
     * @param    string    $version           La version du plugin.
     */
    public function __construct( $plugin_name, $version ) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Enregistre les hooks pour ce module.
     *
     * @since    1.2.0
     */
    public function register_hooks() {
        // Ajouter les actions AJAX
        add_action( 'wp_ajax_boss_seo_save_business_info', array( $this, 'ajax_save_business_info' ) );
        add_action( 'wp_ajax_boss_seo_get_business_info', array( $this, 'ajax_get_business_info' ) );
    }

    /**
     * Enregistre les routes REST API pour ce module.
     *
     * @since    1.2.0
     */
    public function register_rest_routes() {
        register_rest_route( 'boss-seo/v1', '/local/business-info', array(
            array(
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => array( $this, 'get_business_info' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array( $this, 'update_business_info' ),
                'permission_callback' => array( $this, 'check_permissions' ),
                'args'                => array(
                    'business_info' => array(
                        'required'    => true,
                        'type'        => 'object',
                        'description' => __( 'Les informations d\'entreprise à enregistrer.', 'boss-seo' ),
                    ),
                ),
            ),
        ) );
    }

    /**
     * Vérifie les permissions de l'utilisateur.
     *
     * @since    1.2.0
     * @return   bool    True si l'utilisateur a les permissions, false sinon.
     */
    public function check_permissions() {
        return current_user_can( 'manage_options' );
    }

    /**
     * Récupère les informations d'entreprise via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function get_business_info( $request ) {
        $business_info = $this->get_all_business_info();
        return rest_ensure_response( $business_info );
    }

    /**
     * Met à jour les informations d'entreprise via l'API REST.
     *
     * @since    1.2.0
     * @param    WP_REST_Request    $request    Les données de la requête.
     * @return   WP_REST_Response               La réponse.
     */
    public function update_business_info( $request ) {
        $params = $request->get_params();

        // Récupérer les données d'entreprise depuis le paramètre business_info
        $business_info_data = isset( $params['business_info'] ) ? $params['business_info'] : $params;

        // Mettre à jour les informations d'entreprise
        $result = $this->update_all_business_info( $business_info_data );

        if ( $result === false ) {
            return new WP_Error( 'update_failed', __( 'Erreur lors de la mise à jour des informations d\'entreprise.', 'boss-seo' ), array( 'status' => 500 ) );
        }

        // Récupérer les informations mises à jour
        $business_info = $this->get_all_business_info();

        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Informations d\'entreprise mises à jour avec succès.', 'boss-seo' ),
            'data'    => $business_info
        ) );
    }

    /**
     * Gère les requêtes AJAX pour enregistrer les informations d'entreprise.
     *
     * @since    1.2.0
     */
    public function ajax_save_business_info() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les données
        $data = isset( $_POST['data'] ) ? $_POST['data'] : array();

        // Mettre à jour les informations d'entreprise
        $this->update_all_business_info( $data );

        wp_send_json_success( array( 'message' => __( 'Informations d\'entreprise enregistrées avec succès.', 'boss-seo' ) ) );
    }

    /**
     * Gère les requêtes AJAX pour récupérer les informations d'entreprise.
     *
     * @since    1.2.0
     */
    public function ajax_get_business_info() {
        // Vérifier le nonce
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( $_POST['nonce'], 'boss_seo_local_nonce' ) ) {
            wp_send_json_error( array( 'message' => __( 'Erreur de sécurité.', 'boss-seo' ) ) );
        }

        // Vérifier les autorisations
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Vous n\'avez pas les autorisations nécessaires.', 'boss-seo' ) ) );
        }

        // Récupérer les informations d'entreprise
        $business_info = $this->get_all_business_info();

        wp_send_json_success( $business_info );
    }

    /**
     * Récupère toutes les informations d'entreprise.
     *
     * @since    1.2.0
     * @return   array    Les informations d'entreprise.
     */
    public function get_all_business_info() {
        $business_info = array(
            'name'              => $this->get_business_info_option( 'name' ),
            'legalName'         => $this->get_business_info_option( 'legal_name' ),
            'type'              => $this->get_business_info_option( 'type' ),
            'description'       => $this->get_business_info_option( 'description' ),
            'logo'              => $this->get_business_info_option( 'logo' ),
            'foundingDate'      => $this->get_business_info_option( 'founding_date' ),
            'address'           => array(
                'street'        => $this->get_business_info_option( 'address_street' ),
                'city'          => $this->get_business_info_option( 'address_city' ),
                'postalCode'    => $this->get_business_info_option( 'address_postal_code' ),
                'region'        => $this->get_business_info_option( 'address_region' ),
                'country'       => $this->get_business_info_option( 'address_country' ),
            ),
            'contact'           => array(
                'phone'         => $this->get_business_info_option( 'contact_phone' ),
                'email'         => $this->get_business_info_option( 'contact_email' ),
                'website'       => $this->get_business_info_option( 'contact_website' ),
            ),
            'socialProfiles'    => array(
                'facebook'      => $this->get_business_info_option( 'social_facebook' ),
                'twitter'       => $this->get_business_info_option( 'social_twitter' ),
                'instagram'     => $this->get_business_info_option( 'social_instagram' ),
                'linkedin'      => $this->get_business_info_option( 'social_linkedin' ),
                'youtube'       => $this->get_business_info_option( 'social_youtube' ),
            ),
            'openingHours'      => $this->get_business_info_option( 'opening_hours' ),
            'paymentAccepted'   => $this->get_business_info_option( 'payment_accepted', array() ),
            'priceRange'        => $this->get_business_info_option( 'price_range' ),
            'hasMap'            => $this->get_business_info_option( 'has_map', true ),
            'geo'               => array(
                'latitude'      => $this->get_business_info_option( 'geo_latitude' ),
                'longitude'     => $this->get_business_info_option( 'geo_longitude' ),
            ),
        );

        return $business_info;
    }

    /**
     * Met à jour toutes les informations d'entreprise.
     *
     * @since    1.2.0
     * @param    array    $data    Les données à mettre à jour.
     */
    public function update_all_business_info( $data ) {
        // Informations générales
        $this->update_business_info_option( 'name', isset( $data['name'] ) ? sanitize_text_field( $data['name'] ) : '' );
        $this->update_business_info_option( 'legal_name', isset( $data['legalName'] ) ? sanitize_text_field( $data['legalName'] ) : '' );
        $this->update_business_info_option( 'type', isset( $data['type'] ) ? sanitize_text_field( $data['type'] ) : '' );
        $this->update_business_info_option( 'description', isset( $data['description'] ) ? sanitize_textarea_field( $data['description'] ) : '' );
        $this->update_business_info_option( 'logo', isset( $data['logo'] ) ? sanitize_text_field( $data['logo'] ) : '' );
        $this->update_business_info_option( 'founding_date', isset( $data['foundingDate'] ) ? sanitize_text_field( $data['foundingDate'] ) : '' );

        // Adresse
        if ( isset( $data['address'] ) && is_array( $data['address'] ) ) {
            $this->update_business_info_option( 'address_street', isset( $data['address']['street'] ) ? sanitize_text_field( $data['address']['street'] ) : '' );
            $this->update_business_info_option( 'address_city', isset( $data['address']['city'] ) ? sanitize_text_field( $data['address']['city'] ) : '' );
            $this->update_business_info_option( 'address_postal_code', isset( $data['address']['postalCode'] ) ? sanitize_text_field( $data['address']['postalCode'] ) : '' );
            $this->update_business_info_option( 'address_region', isset( $data['address']['region'] ) ? sanitize_text_field( $data['address']['region'] ) : '' );
            $this->update_business_info_option( 'address_country', isset( $data['address']['country'] ) ? sanitize_text_field( $data['address']['country'] ) : '' );
        }

        // Contact
        if ( isset( $data['contact'] ) && is_array( $data['contact'] ) ) {
            $this->update_business_info_option( 'contact_phone', isset( $data['contact']['phone'] ) ? sanitize_text_field( $data['contact']['phone'] ) : '' );
            $this->update_business_info_option( 'contact_email', isset( $data['contact']['email'] ) ? sanitize_email( $data['contact']['email'] ) : '' );
            $this->update_business_info_option( 'contact_website', isset( $data['contact']['website'] ) ? esc_url_raw( $data['contact']['website'] ) : '' );
        }

        // Réseaux sociaux
        if ( isset( $data['socialProfiles'] ) && is_array( $data['socialProfiles'] ) ) {
            $this->update_business_info_option( 'social_facebook', isset( $data['socialProfiles']['facebook'] ) ? esc_url_raw( $data['socialProfiles']['facebook'] ) : '' );
            $this->update_business_info_option( 'social_twitter', isset( $data['socialProfiles']['twitter'] ) ? esc_url_raw( $data['socialProfiles']['twitter'] ) : '' );
            $this->update_business_info_option( 'social_instagram', isset( $data['socialProfiles']['instagram'] ) ? esc_url_raw( $data['socialProfiles']['instagram'] ) : '' );
            $this->update_business_info_option( 'social_linkedin', isset( $data['socialProfiles']['linkedin'] ) ? esc_url_raw( $data['socialProfiles']['linkedin'] ) : '' );
            $this->update_business_info_option( 'social_youtube', isset( $data['socialProfiles']['youtube'] ) ? esc_url_raw( $data['socialProfiles']['youtube'] ) : '' );
        }

        // Horaires d'ouverture
        if ( isset( $data['openingHours'] ) ) {
            $this->update_business_info_option( 'opening_hours', sanitize_text_field( $data['openingHours'] ) );
        }

        // Moyens de paiement acceptés
        if ( isset( $data['paymentAccepted'] ) && is_array( $data['paymentAccepted'] ) ) {
            $this->update_business_info_option( 'payment_accepted', $data['paymentAccepted'] );
        }

        // Fourchette de prix
        $this->update_business_info_option( 'price_range', isset( $data['priceRange'] ) ? sanitize_text_field( $data['priceRange'] ) : '' );

        // Carte
        $this->update_business_info_option( 'has_map', isset( $data['hasMap'] ) ? (bool) $data['hasMap'] : true );

        // Géolocalisation
        if ( isset( $data['geo'] ) && is_array( $data['geo'] ) ) {
            $this->update_business_info_option( 'geo_latitude', isset( $data['geo']['latitude'] ) ? sanitize_text_field( $data['geo']['latitude'] ) : '' );
            $this->update_business_info_option( 'geo_longitude', isset( $data['geo']['longitude'] ) ? sanitize_text_field( $data['geo']['longitude'] ) : '' );
        }

        return true;
    }

    /**
     * Récupère une option d'information d'entreprise.
     *
     * @since    1.2.0
     * @param    string    $key      La clé de l'option.
     * @param    mixed     $default  La valeur par défaut.
     * @return   mixed                La valeur de l'option.
     */
    private function get_business_info_option( $key, $default = '' ) {
        return get_option( $this->option_prefix . $key, $default );
    }

    /**
     * Met à jour une option d'information d'entreprise.
     *
     * @since    1.2.0
     * @param    string    $key    La clé de l'option.
     * @param    mixed     $value  La valeur de l'option.
     * @return   bool              True si l'option a été mise à jour, false sinon.
     */
    private function update_business_info_option( $key, $value ) {
        return update_option( $this->option_prefix . $key, $value );
    }
}
