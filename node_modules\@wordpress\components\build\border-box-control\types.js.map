{"version": 3, "names": [], "sources": ["@wordpress/components/src/border-box-control/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type {\n\tBorder,\n\tColorProps,\n\tLabelProps,\n\tBorderControlProps,\n} from '../border-control/types';\nimport type { PopoverProps } from '../popover/types';\n\nexport type Borders = {\n\ttop?: Border;\n\tright?: Border;\n\tbottom?: Border;\n\tleft?: Border;\n};\n\nexport type AnyBorder = Border | Borders | undefined;\nexport type BorderProp = keyof Border;\nexport type BorderSide = keyof Borders;\n\nexport type BorderBoxControlProps = ColorProps &\n\tLabelProps &\n\tPick< BorderControlProps, 'enableStyle' | 'size' > & {\n\t\t/**\n\t\t * A callback function invoked when any border value is changed. The value\n\t\t * received may be a \"flat\" border object, one that has properties defining\n\t\t * individual side borders, or `undefined`.\n\t\t */\n\t\tonChange: ( value: AnyBorder ) => void;\n\t\t/**\n\t\t * The position of the color popovers compared to the control wrapper.\n\t\t */\n\t\tpopoverPlacement?: PopoverProps[ 'placement' ];\n\t\t/**\n\t\t * The space between the popover and the control wrapper.\n\t\t */\n\t\tpopoverOffset?: PopoverProps[ 'offset' ];\n\t\t/**\n\t\t * An object representing the current border configuration.\n\t\t *\n\t\t * This may be a \"flat\" border where the object has `color`, `style`, and\n\t\t * `width` properties or a \"split\" border which defines the previous\n\t\t * properties but for each side; `top`, `right`, `bottom`, and `left`.\n\t\t */\n\t\tvalue: AnyBorder;\n\t\t/**\n\t\t * Start opting into the larger default height that will become the default size in a future version.\n\t\t *\n\t\t * @default false\n\t\t */\n\t\t__next40pxDefaultSize?: boolean;\n\t};\n\nexport type LinkedButtonProps = Pick< BorderBoxControlProps, 'size' > & {\n\t/**\n\t * This prop allows the `LinkedButton` to reflect whether the parent\n\t * `BorderBoxControl` is currently displaying \"linked\" or \"unlinked\"\n\t * border controls.\n\t */\n\tisLinked: boolean;\n\t/**\n\t * A callback invoked when this `LinkedButton` is clicked. It is used to\n\t * toggle display between linked or split border controls within the parent\n\t * `BorderBoxControl`.\n\t */\n\tonClick: () => void;\n};\n\nexport type VisualizerProps = Pick< BorderBoxControlProps, 'size' > & {\n\t/**\n\t * An object representing the current border configuration. It contains\n\t * properties for each side, with each side an object reflecting the border\n\t * color, style, and width.\n\t */\n\tvalue?: Borders;\n};\n\nexport type SplitControlsProps = ColorProps &\n\tPick< BorderBoxControlProps, 'enableStyle' | 'size' > & {\n\t\t/**\n\t\t * A callback that is invoked whenever an individual side's border has\n\t\t * changed.\n\t\t */\n\t\tonChange: ( value: Border | undefined, side: BorderSide ) => void;\n\t\t/**\n\t\t * The position of the color popovers compared to the control wrapper.\n\t\t */\n\t\tpopoverPlacement?: PopoverProps[ 'placement' ];\n\t\t/**\n\t\t * The space between the popover and the control wrapper.\n\t\t */\n\t\tpopoverOffset?: PopoverProps[ 'offset' ];\n\t\t/**\n\t\t * An object representing the current border configuration. It contains\n\t\t * properties for each side, with each side an object reflecting the border\n\t\t * color, style, and width.\n\t\t */\n\t\tvalue?: Borders;\n\t};\n"], "mappings": ""}