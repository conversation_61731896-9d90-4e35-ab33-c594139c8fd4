{"version": 3, "names": ["_boxControlIconStyles", "require", "BASE_ICON_SIZE", "BoxControlIcon", "size", "side", "sides", "props", "isSideDisabled", "value", "length", "includes", "hasSide", "top", "right", "bottom", "left", "scale", "_react", "createElement", "Root", "style", "transform", "Viewbox", "TopStroke", "isFocused", "RightStroke", "BottomStroke", "LeftStroke"], "sources": ["@wordpress/components/src/box-control/icon.tsx"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport {\n\tRoot,\n\tViewbox,\n\tTopStroke,\n\tRightStroke,\n\tBottomStroke,\n\tLeftStroke,\n} from './styles/box-control-icon-styles';\nimport type { BoxControlIconProps, BoxControlProps } from './types';\n\nconst BASE_ICON_SIZE = 24;\n\nexport default function BoxControlIcon( {\n\tsize = 24,\n\tside = 'all',\n\tsides,\n\t...props\n}: WordPressComponentProps< BoxControlIconProps, 'span' > ) {\n\tconst isSideDisabled = (\n\t\tvalue: NonNullable< BoxControlProps[ 'sides' ] >[ number ]\n\t) => sides?.length && ! sides.includes( value );\n\n\tconst hasSide = (\n\t\tvalue: NonNullable< BoxControlProps[ 'sides' ] >[ number ]\n\t) => {\n\t\tif ( isSideDisabled( value ) ) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn side === 'all' || side === value;\n\t};\n\n\tconst top = hasSide( 'top' ) || hasSide( 'vertical' );\n\tconst right = hasSide( 'right' ) || hasSide( 'horizontal' );\n\tconst bottom = hasSide( 'bottom' ) || hasSide( 'vertical' );\n\tconst left = hasSide( 'left' ) || hasSide( 'horizontal' );\n\n\t// Simulates SVG Icon scaling.\n\tconst scale = size / BASE_ICON_SIZE;\n\n\treturn (\n\t\t<Root style={ { transform: `scale(${ scale })` } } { ...props }>\n\t\t\t<Viewbox>\n\t\t\t\t<TopStroke isFocused={ top } />\n\t\t\t\t<RightStroke isFocused={ right } />\n\t\t\t\t<BottomStroke isFocused={ bottom } />\n\t\t\t\t<LeftStroke isFocused={ left } />\n\t\t\t</Viewbox>\n\t\t</Root>\n\t);\n}\n"], "mappings": ";;;;;;;AAIA,IAAAA,qBAAA,GAAAC,OAAA;AAJA;AACA;AACA;;AAYA,MAAMC,cAAc,GAAG,EAAE;AAEV,SAASC,cAAcA,CAAE;EACvCC,IAAI,GAAG,EAAE;EACTC,IAAI,GAAG,KAAK;EACZC,KAAK;EACL,GAAGC;AACoD,CAAC,EAAG;EAC3D,MAAMC,cAAc,GACnBC,KAA0D,IACtDH,KAAK,EAAEI,MAAM,IAAI,CAAEJ,KAAK,CAACK,QAAQ,CAAEF,KAAM,CAAC;EAE/C,MAAMG,OAAO,GACZH,KAA0D,IACtD;IACJ,IAAKD,cAAc,CAAEC,KAAM,CAAC,EAAG;MAC9B,OAAO,KAAK;IACb;IAEA,OAAOJ,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAKI,KAAK;EACxC,CAAC;EAED,MAAMI,GAAG,GAAGD,OAAO,CAAE,KAAM,CAAC,IAAIA,OAAO,CAAE,UAAW,CAAC;EACrD,MAAME,KAAK,GAAGF,OAAO,CAAE,OAAQ,CAAC,IAAIA,OAAO,CAAE,YAAa,CAAC;EAC3D,MAAMG,MAAM,GAAGH,OAAO,CAAE,QAAS,CAAC,IAAIA,OAAO,CAAE,UAAW,CAAC;EAC3D,MAAMI,IAAI,GAAGJ,OAAO,CAAE,MAAO,CAAC,IAAIA,OAAO,CAAE,YAAa,CAAC;;EAEzD;EACA,MAAMK,KAAK,GAAGb,IAAI,GAAGF,cAAc;EAEnC,OACC,IAAAgB,MAAA,CAAAC,aAAA,EAACnB,qBAAA,CAAAoB,IAAI;IAACC,KAAK,EAAG;MAAEC,SAAS,EAAG,SAASL,KAAO;IAAG,CAAG;IAAA,GAAMV;EAAK,GAC5D,IAAAW,MAAA,CAAAC,aAAA,EAACnB,qBAAA,CAAAuB,OAAO,QACP,IAAAL,MAAA,CAAAC,aAAA,EAACnB,qBAAA,CAAAwB,SAAS;IAACC,SAAS,EAAGZ;EAAK,CAAE,CAAC,EAC/B,IAAAK,MAAA,CAAAC,aAAA,EAACnB,qBAAA,CAAA0B,WAAW;IAACD,SAAS,EAAGX;EAAO,CAAE,CAAC,EACnC,IAAAI,MAAA,CAAAC,aAAA,EAACnB,qBAAA,CAAA2B,YAAY;IAACF,SAAS,EAAGV;EAAQ,CAAE,CAAC,EACrC,IAAAG,MAAA,CAAAC,aAAA,EAACnB,qBAAA,CAAA4B,UAAU;IAACH,SAAS,EAAGT;EAAM,CAAE,CACxB,CACJ,CAAC;AAET"}