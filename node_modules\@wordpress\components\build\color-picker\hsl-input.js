"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.HslInput = void 0;
var _react = require("react");
var _colord = require("colord");
var _element = require("@wordpress/element");
var _inputWithSlider = require("./input-with-slider");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const HslInput = ({
  color,
  onChange,
  enableAlpha
}) => {
  const colorPropHSLA = (0, _element.useMemo)(() => color.toHsl(), [color]);
  const [internalHSLA, setInternalHSLA] = (0, _element.useState)({
    ...colorPropHSLA
  });
  const isInternalColorSameAsReceivedColor = color.isEqual((0, _colord.colord)(internalHSLA));
  (0, _element.useEffect)(() => {
    if (!isInternalColorSameAsReceivedColor) {
      // Keep internal HSLA color up to date with the received color prop
      setInternalHSLA(colorPropHSLA);
    }
  }, [colorPropHSLA, isInternalColorSameAsReceivedColor]);

  // If the internal color is equal to the received color prop, we can use the
  // HSLA values from the local state which, compared to the received color prop,
  // retain more details about the actual H and S values that the user selected,
  // and thus allow for better UX when interacting with the H and S sliders.
  const colorValue = isInternalColorSameAsReceivedColor ? internalHSLA : colorPropHSLA;
  const updateHSLAValue = partialNewValue => {
    const nextOnChangeValue = (0, _colord.colord)({
      ...colorValue,
      ...partialNewValue
    });

    // Fire `onChange` only if the resulting color is different from the
    // current one.
    // Otherwise, update the internal HSLA color to cause a re-render.
    if (!color.isEqual(nextOnChangeValue)) {
      onChange(nextOnChangeValue);
    } else {
      setInternalHSLA(prevHSLA => ({
        ...prevHSLA,
        ...partialNewValue
      }));
    }
  };
  return (0, _react.createElement)(_react.Fragment, null, (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 359,
    label: "Hue",
    abbreviation: "H",
    value: colorValue.h,
    onChange: nextH => {
      updateHSLAValue({
        h: nextH
      });
    }
  }), (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 100,
    label: "Saturation",
    abbreviation: "S",
    value: colorValue.s,
    onChange: nextS => {
      updateHSLAValue({
        s: nextS
      });
    }
  }), (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 100,
    label: "Lightness",
    abbreviation: "L",
    value: colorValue.l,
    onChange: nextL => {
      updateHSLAValue({
        l: nextL
      });
    }
  }), enableAlpha && (0, _react.createElement)(_inputWithSlider.InputWithSlider, {
    min: 0,
    max: 100,
    label: "Alpha",
    abbreviation: "A",
    value: Math.trunc(100 * colorValue.a),
    onChange: nextA => {
      updateHSLAValue({
        a: nextA / 100
      });
    }
  }));
};
exports.HslInput = HslInput;
//# sourceMappingURL=hsl-input.js.map