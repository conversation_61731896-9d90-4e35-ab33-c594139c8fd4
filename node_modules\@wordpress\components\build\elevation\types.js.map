{"version": 3, "names": [], "sources": ["@wordpress/components/src/elevation/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\nexport type ElevationProps = {\n\t/**\n\t * Size of the shadow value when active (see the `value` and `isInteractive`\n\t * props).\n\t */\n\tactive?: number;\n\t/**\n\t * Renders the border-radius of the shadow.\n\t *\n\t * @default 'inherit'\n\t */\n\tborderRadius?: CSSProperties[ 'borderRadius' ];\n\t/**\n\t * Size of the shadow value when focused (see the `value` and\n\t * `isInteractive` props).\n\t */\n\tfocus?: number;\n\t/**\n\t * Size of the shadow value when hovered (see the `value` and\n\t * `isInteractive` props).\n\t */\n\thover?: number;\n\t/**\n\t * Determines if `hover`, `active`, and `focus` shadow values should be\n\t * automatically calculated and rendered.\n\t *\n\t * @default false\n\t */\n\tisInteractive?: boolean;\n\t/**\n\t * Dimensional offsets (margin) for the shadow.\n\t *\n\t * @default 0\n\t */\n\toffset?: number;\n\t/**\n\t * Size of the shadow, based on the Style system's elevation system. The\n\t * `value` determines the strength of the shadow, which creates the sense of\n\t * depth.\n\t *\n\t * @default 0\n\t */\n\tvalue?: number;\n};\n"], "mappings": ""}