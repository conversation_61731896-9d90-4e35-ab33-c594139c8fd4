{"version": 3, "names": ["COMPONENT_NAMESPACE", "exports", "CONNECTED_NAMESPACE", "CONNECT_STATIC_NAMESPACE"], "sources": ["@wordpress/components/src/context/constants.js"], "sourcesContent": ["export const COMPONENT_NAMESPACE = 'data-wp-component';\nexport const CONNECTED_NAMESPACE = 'data-wp-c16t';\n\n/**\n * Special key where the connected namespaces are stored.\n * This is attached to Context connected components as a static property.\n */\nexport const CONNECT_STATIC_NAMESPACE = '__contextSystemKey__';\n"], "mappings": ";;;;;;AAAO,MAAMA,mBAAmB,GAAG,mBAAmB;AAACC,OAAA,CAAAD,mBAAA,GAAAA,mBAAA;AAChD,MAAME,mBAAmB,GAAG,cAAc;;AAEjD;AACA;AACA;AACA;AAHAD,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAIO,MAAMC,wBAAwB,GAAG,sBAAsB;AAACF,OAAA,CAAAE,wBAAA,GAAAA,wBAAA"}