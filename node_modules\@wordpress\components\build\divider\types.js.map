{"version": 3, "names": [], "sources": ["@wordpress/components/src/divider/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport type { SeparatorProps } from '@ariakit/react';\n\n/**\n * Internal dependencies\n */\nimport type { SpaceInput } from '../utils/space';\n\nexport type DividerProps = Omit<\n\tSeparatorProps,\n\t'children' | 'unstable_system' | 'orientation' | 'as' | 'render'\n> & {\n\t/**\n\t * Adjusts all margins on the inline dimension.\n\t *\n\t * Can either be a number (which will act as a multiplier to the library's grid system base of 4px),\n\t * or a literal CSS value string.\n\t */\n\tmargin?: SpaceInput;\n\t/**\n\t * Adjusts the inline-end margin.\n\t *\n\t * Can either be a number (which will act as a multiplier to the library's grid system base of 4px),\n\t * or a literal CSS value string.\n\t */\n\tmarginEnd?: SpaceInput;\n\t/**\n\t * Adjusts the inline-start margin.\n\t *\n\t * Can either be a number (which will act as a multiplier to the library's grid system base of 4px),\n\t * or a literal CSS value string.\n\t */\n\tmarginStart?: SpaceInput;\n\t/**\n\t * Divider's orientation. When using inside a flex container, you may need\n\t * to make sure the divider is `stretch` aligned in order for it to be\n\t * visible.\n\t *\n\t * @default 'horizontal'\n\t */\n\torientation?: SeparatorProps[ 'orientation' ];\n};\n"], "mappings": ""}