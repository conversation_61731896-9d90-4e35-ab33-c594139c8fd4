"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useItemGroupContext = exports.ItemGroupContext = void 0;
var _element = require("@wordpress/element");
/**
 * WordPress dependencies
 */

const ItemGroupContext = (0, _element.createContext)({
  size: 'medium'
});
exports.ItemGroupContext = ItemGroupContext;
const useItemGroupContext = () => (0, _element.useContext)(ItemGroupContext);
exports.useItemGroupContext = useItemGroupContext;
//# sourceMappingURL=context.js.map