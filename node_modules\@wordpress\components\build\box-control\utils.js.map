{"version": 3, "names": ["_i18n", "require", "_utils", "CUSTOM_VALUE_SETTINGS", "px", "max", "step", "vw", "vh", "em", "rm", "svw", "lvw", "dvw", "svh", "lvh", "dvh", "vi", "svi", "lvi", "dvi", "vb", "svb", "lvb", "dvb", "vmin", "svmin", "lvmin", "dvmin", "vmax", "svmax", "lvmax", "dvmax", "exports", "LABELS", "all", "__", "top", "bottom", "left", "right", "mixed", "vertical", "horizontal", "DEFAULT_VALUES", "undefined", "ALL_SIDES", "mode", "arr", "sort", "a", "b", "filter", "v", "length", "pop", "getAllValue", "values", "selected<PERSON><PERSON><PERSON>", "availableSides", "sides", "normalizeSides", "parsedQuantitiesAndUnits", "map", "side", "parseQuantityAndUnitFromRawValue", "allParsedQuantities", "value", "_value$", "allParsedUnits", "commonQuantity", "every", "commonUnit", "_getAllUnitFallback", "getAllUnitFallback", "join", "filteredUnits", "Object", "Boolean", "isValuesMixed", "allValue", "isMixed", "isNaN", "parseFloat", "isValuesDefined", "test", "getInitialSide", "isLinked", "splitOnAxis", "initialSide", "filteredSides", "includes", "push", "newSides", "applyValueToSides", "currentV<PERSON>ues", "newValue", "newValues", "for<PERSON>ach"], "sources": ["@wordpress/components/src/box-control/utils.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { parseQuantityAndUnitFromRawValue } from '../unit-control/utils';\nimport type {\n\tBoxControlProps,\n\tBoxControlValue,\n\tCustomValueUnits,\n} from './types';\n\nexport const CUSTOM_VALUE_SETTINGS: CustomValueUnits = {\n\tpx: { max: 300, step: 1 },\n\t'%': { max: 100, step: 1 },\n\tvw: { max: 100, step: 1 },\n\tvh: { max: 100, step: 1 },\n\tem: { max: 10, step: 0.1 },\n\trm: { max: 10, step: 0.1 },\n\tsvw: { max: 100, step: 1 },\n\tlvw: { max: 100, step: 1 },\n\tdvw: { max: 100, step: 1 },\n\tsvh: { max: 100, step: 1 },\n\tlvh: { max: 100, step: 1 },\n\tdvh: { max: 100, step: 1 },\n\tvi: { max: 100, step: 1 },\n\tsvi: { max: 100, step: 1 },\n\tlvi: { max: 100, step: 1 },\n\tdvi: { max: 100, step: 1 },\n\tvb: { max: 100, step: 1 },\n\tsvb: { max: 100, step: 1 },\n\tlvb: { max: 100, step: 1 },\n\tdvb: { max: 100, step: 1 },\n\tvmin: { max: 100, step: 1 },\n\tsvmin: { max: 100, step: 1 },\n\tlvmin: { max: 100, step: 1 },\n\tdvmin: { max: 100, step: 1 },\n\tvmax: { max: 100, step: 1 },\n\tsvmax: { max: 100, step: 1 },\n\tlvmax: { max: 100, step: 1 },\n\tdvmax: { max: 100, step: 1 },\n};\n\nexport const LABELS = {\n\tall: __( 'All sides' ),\n\ttop: __( 'Top side' ),\n\tbottom: __( 'Bottom side' ),\n\tleft: __( 'Left side' ),\n\tright: __( 'Right side' ),\n\tmixed: __( 'Mixed' ),\n\tvertical: __( 'Top and bottom sides' ),\n\thorizontal: __( 'Left and right sides' ),\n};\n\nexport const DEFAULT_VALUES = {\n\ttop: undefined,\n\tright: undefined,\n\tbottom: undefined,\n\tleft: undefined,\n};\n\nexport const ALL_SIDES = [ 'top', 'right', 'bottom', 'left' ] as const;\n\n/**\n * Gets an items with the most occurrence within an array\n * https://stackoverflow.com/a/20762713\n *\n * @param arr Array of items to check.\n * @return The item with the most occurrences.\n */\nfunction mode< T >( arr: T[] ) {\n\treturn arr\n\t\t.sort(\n\t\t\t( a, b ) =>\n\t\t\t\tarr.filter( ( v ) => v === a ).length -\n\t\t\t\tarr.filter( ( v ) => v === b ).length\n\t\t)\n\t\t.pop();\n}\n\n/**\n * Gets the 'all' input value and unit from values data.\n *\n * @param values         Box values.\n * @param selectedUnits  Box units.\n * @param availableSides Available box sides to evaluate.\n *\n * @return A value + unit for the 'all' input.\n */\nexport function getAllValue(\n\tvalues: BoxControlValue = {},\n\tselectedUnits?: BoxControlValue,\n\tavailableSides: BoxControlProps[ 'sides' ] = ALL_SIDES\n) {\n\tconst sides = normalizeSides( availableSides );\n\tconst parsedQuantitiesAndUnits = sides.map( ( side ) =>\n\t\tparseQuantityAndUnitFromRawValue( values[ side ] )\n\t);\n\tconst allParsedQuantities = parsedQuantitiesAndUnits.map(\n\t\t( value ) => value[ 0 ] ?? ''\n\t);\n\tconst allParsedUnits = parsedQuantitiesAndUnits.map(\n\t\t( value ) => value[ 1 ]\n\t);\n\n\tconst commonQuantity = allParsedQuantities.every(\n\t\t( v ) => v === allParsedQuantities[ 0 ]\n\t)\n\t\t? allParsedQuantities[ 0 ]\n\t\t: '';\n\n\t/**\n\t * The typeof === 'number' check is important. On reset actions, the incoming value\n\t * may be null or an empty string.\n\t *\n\t * Also, the value may also be zero (0), which is considered a valid unit value.\n\t *\n\t * typeof === 'number' is more specific for these cases, rather than relying on a\n\t * simple truthy check.\n\t */\n\tlet commonUnit;\n\tif ( typeof commonQuantity === 'number' ) {\n\t\tcommonUnit = mode( allParsedUnits );\n\t} else {\n\t\t// Set meaningful unit selection if no commonQuantity and user has previously\n\t\t// selected units without assigning values while controls were unlinked.\n\t\tcommonUnit =\n\t\t\tgetAllUnitFallback( selectedUnits ) ?? mode( allParsedUnits );\n\t}\n\n\treturn [ commonQuantity, commonUnit ].join( '' );\n}\n\n/**\n * Determine the most common unit selection to use as a fallback option.\n *\n * @param selectedUnits Current unit selections for individual sides.\n * @return  Most common unit selection.\n */\nexport function getAllUnitFallback( selectedUnits?: BoxControlValue ) {\n\tif ( ! selectedUnits || typeof selectedUnits !== 'object' ) {\n\t\treturn undefined;\n\t}\n\n\tconst filteredUnits = Object.values( selectedUnits ).filter( Boolean );\n\n\treturn mode( filteredUnits );\n}\n\n/**\n * Checks to determine if values are mixed.\n *\n * @param values        Box values.\n * @param selectedUnits Box units.\n * @param sides         Available box sides to evaluate.\n *\n * @return Whether values are mixed.\n */\nexport function isValuesMixed(\n\tvalues: BoxControlValue = {},\n\tselectedUnits?: BoxControlValue,\n\tsides: BoxControlProps[ 'sides' ] = ALL_SIDES\n) {\n\tconst allValue = getAllValue( values, selectedUnits, sides );\n\tconst isMixed = isNaN( parseFloat( allValue ) );\n\n\treturn isMixed;\n}\n\n/**\n * Checks to determine if values are defined.\n *\n * @param values Box values.\n *\n * @return  Whether values are mixed.\n */\nexport function isValuesDefined( values?: BoxControlValue ) {\n\treturn (\n\t\tvalues !== undefined &&\n\t\tObject.values( values ).filter(\n\t\t\t// Switching units when input is empty causes values only\n\t\t\t// containing units. This gives false positive on mixed values\n\t\t\t// unless filtered.\n\t\t\t( value ) => !! value && /\\d/.test( value )\n\t\t).length > 0\n\t);\n}\n\n/**\n * Get initial selected side, factoring in whether the sides are linked,\n * and whether the vertical / horizontal directions are grouped via splitOnAxis.\n *\n * @param isLinked    Whether the box control's fields are linked.\n * @param splitOnAxis Whether splitting by horizontal or vertical axis.\n * @return The initial side.\n */\nexport function getInitialSide( isLinked: boolean, splitOnAxis: boolean ) {\n\tlet initialSide: keyof typeof LABELS = 'all';\n\n\tif ( ! isLinked ) {\n\t\tinitialSide = splitOnAxis ? 'vertical' : 'top';\n\t}\n\n\treturn initialSide;\n}\n\n/**\n * Normalizes provided sides configuration to an array containing only top,\n * right, bottom and left. This essentially just maps `horizontal` or `vertical`\n * to their appropriate sides to facilitate correctly determining value for\n * all input control.\n *\n * @param sides Available sides for box control.\n * @return Normalized sides configuration.\n */\nexport function normalizeSides( sides: BoxControlProps[ 'sides' ] ) {\n\tconst filteredSides: ( keyof BoxControlValue )[] = [];\n\n\tif ( ! sides?.length ) {\n\t\treturn ALL_SIDES;\n\t}\n\n\tif ( sides.includes( 'vertical' ) ) {\n\t\tfilteredSides.push( ...( [ 'top', 'bottom' ] as const ) );\n\t} else if ( sides.includes( 'horizontal' ) ) {\n\t\tfilteredSides.push( ...( [ 'left', 'right' ] as const ) );\n\t} else {\n\t\tconst newSides = ALL_SIDES.filter( ( side ) => sides.includes( side ) );\n\t\tfilteredSides.push( ...newSides );\n\t}\n\n\treturn filteredSides;\n}\n\n/**\n * Applies a value to an object representing top, right, bottom and left sides\n * while taking into account any custom side configuration.\n *\n * @param currentValues The current values for each side.\n * @param newValue      The value to apply to the sides object.\n * @param sides         Array defining valid sides.\n *\n * @return Object containing the updated values for each side.\n */\nexport function applyValueToSides(\n\tcurrentValues: BoxControlValue,\n\tnewValue?: string,\n\tsides?: BoxControlProps[ 'sides' ]\n): BoxControlValue {\n\tconst newValues = { ...currentValues };\n\n\tif ( sides?.length ) {\n\t\tsides.forEach( ( side ) => {\n\t\t\tif ( side === 'vertical' ) {\n\t\t\t\tnewValues.top = newValue;\n\t\t\t\tnewValues.bottom = newValue;\n\t\t\t} else if ( side === 'horizontal' ) {\n\t\t\t\tnewValues.left = newValue;\n\t\t\t\tnewValues.right = newValue;\n\t\t\t} else {\n\t\t\t\tnewValues[ side ] = newValue;\n\t\t\t}\n\t\t} );\n\t} else {\n\t\tALL_SIDES.forEach( ( side ) => ( newValues[ side ] = newValue ) );\n\t}\n\n\treturn newValues;\n}\n"], "mappings": ";;;;;;;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAD,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAQO,MAAME,qBAAuC,GAAG;EACtDC,EAAE,EAAE;IAAEC,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EACzB,GAAG,EAAE;IAAED,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BC,EAAE,EAAE;IAAEF,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EACzBE,EAAE,EAAE;IAAEH,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EACzBG,EAAE,EAAE;IAAEJ,GAAG,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAI,CAAC;EAC1BI,EAAE,EAAE;IAAEL,GAAG,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAI,CAAC;EAC1BK,GAAG,EAAE;IAAEN,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BM,GAAG,EAAE;IAAEP,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BO,GAAG,EAAE;IAAER,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BQ,GAAG,EAAE;IAAET,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BS,GAAG,EAAE;IAAEV,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BU,GAAG,EAAE;IAAEX,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BW,EAAE,EAAE;IAAEZ,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EACzBY,GAAG,EAAE;IAAEb,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1Ba,GAAG,EAAE;IAAEd,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1Bc,GAAG,EAAE;IAAEf,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1Be,EAAE,EAAE;IAAEhB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EACzBgB,GAAG,EAAE;IAAEjB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BiB,GAAG,EAAE;IAAElB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BkB,GAAG,EAAE;IAAEnB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC1BmB,IAAI,EAAE;IAAEpB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC3BoB,KAAK,EAAE;IAAErB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC5BqB,KAAK,EAAE;IAAEtB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC5BsB,KAAK,EAAE;IAAEvB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC5BuB,IAAI,EAAE;IAAExB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC3BwB,KAAK,EAAE;IAAEzB,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC5ByB,KAAK,EAAE;IAAE1B,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE,CAAC;EAC5B0B,KAAK,EAAE;IAAE3B,GAAG,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAE;AAC5B,CAAC;AAAC2B,OAAA,CAAA9B,qBAAA,GAAAA,qBAAA;AAEK,MAAM+B,MAAM,GAAG;EACrBC,GAAG,EAAE,IAAAC,QAAE,EAAE,WAAY,CAAC;EACtBC,GAAG,EAAE,IAAAD,QAAE,EAAE,UAAW,CAAC;EACrBE,MAAM,EAAE,IAAAF,QAAE,EAAE,aAAc,CAAC;EAC3BG,IAAI,EAAE,IAAAH,QAAE,EAAE,WAAY,CAAC;EACvBI,KAAK,EAAE,IAAAJ,QAAE,EAAE,YAAa,CAAC;EACzBK,KAAK,EAAE,IAAAL,QAAE,EAAE,OAAQ,CAAC;EACpBM,QAAQ,EAAE,IAAAN,QAAE,EAAE,sBAAuB,CAAC;EACtCO,UAAU,EAAE,IAAAP,QAAE,EAAE,sBAAuB;AACxC,CAAC;AAACH,OAAA,CAAAC,MAAA,GAAAA,MAAA;AAEK,MAAMU,cAAc,GAAG;EAC7BP,GAAG,EAAEQ,SAAS;EACdL,KAAK,EAAEK,SAAS;EAChBP,MAAM,EAAEO,SAAS;EACjBN,IAAI,EAAEM;AACP,CAAC;AAACZ,OAAA,CAAAW,cAAA,GAAAA,cAAA;AAEK,MAAME,SAAS,GAAG,CAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAW;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AANAb,OAAA,CAAAa,SAAA,GAAAA,SAAA;AAOA,SAASC,IAAIA,CAAOC,GAAQ,EAAG;EAC9B,OAAOA,GAAG,CACRC,IAAI,CACJ,CAAEC,CAAC,EAAEC,CAAC,KACLH,GAAG,CAACI,MAAM,CAAIC,CAAC,IAAMA,CAAC,KAAKH,CAAE,CAAC,CAACI,MAAM,GACrCN,GAAG,CAACI,MAAM,CAAIC,CAAC,IAAMA,CAAC,KAAKF,CAAE,CAAC,CAACG,MACjC,CAAC,CACAC,GAAG,CAAC,CAAC;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CAC1BC,MAAuB,GAAG,CAAC,CAAC,EAC5BC,aAA+B,EAC/BC,cAA0C,GAAGb,SAAS,EACrD;EACD,MAAMc,KAAK,GAAGC,cAAc,CAAEF,cAAe,CAAC;EAC9C,MAAMG,wBAAwB,GAAGF,KAAK,CAACG,GAAG,CAAIC,IAAI,IACjD,IAAAC,uCAAgC,EAAER,MAAM,CAAEO,IAAI,CAAG,CAClD,CAAC;EACD,MAAME,mBAAmB,GAAGJ,wBAAwB,CAACC,GAAG,CACrDI,KAAK;IAAA,IAAAC,OAAA;IAAA,QAAAA,OAAA,GAAMD,KAAK,CAAE,CAAC,CAAE,cAAAC,OAAA,cAAAA,OAAA,GAAI,EAAE;EAAA,CAC9B,CAAC;EACD,MAAMC,cAAc,GAAGP,wBAAwB,CAACC,GAAG,CAChDI,KAAK,IAAMA,KAAK,CAAE,CAAC,CACtB,CAAC;EAED,MAAMG,cAAc,GAAGJ,mBAAmB,CAACK,KAAK,CAC7ClB,CAAC,IAAMA,CAAC,KAAKa,mBAAmB,CAAE,CAAC,CACtC,CAAC,GACEA,mBAAmB,CAAE,CAAC,CAAE,GACxB,EAAE;;EAEL;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIM,UAAU;EACd,IAAK,OAAOF,cAAc,KAAK,QAAQ,EAAG;IACzCE,UAAU,GAAGzB,IAAI,CAAEsB,cAAe,CAAC;EACpC,CAAC,MAAM;IAAA,IAAAI,mBAAA;IACN;IACA;IACAD,UAAU,IAAAC,mBAAA,GACTC,kBAAkB,CAAEhB,aAAc,CAAC,cAAAe,mBAAA,cAAAA,mBAAA,GAAI1B,IAAI,CAAEsB,cAAe,CAAC;EAC/D;EAEA,OAAO,CAAEC,cAAc,EAAEE,UAAU,CAAE,CAACG,IAAI,CAAE,EAAG,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASD,kBAAkBA,CAAEhB,aAA+B,EAAG;EACrE,IAAK,CAAEA,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAG;IAC3D,OAAOb,SAAS;EACjB;EAEA,MAAM+B,aAAa,GAAGC,MAAM,CAACpB,MAAM,CAAEC,aAAc,CAAC,CAACN,MAAM,CAAE0B,OAAQ,CAAC;EAEtE,OAAO/B,IAAI,CAAE6B,aAAc,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,aAAaA,CAC5BtB,MAAuB,GAAG,CAAC,CAAC,EAC5BC,aAA+B,EAC/BE,KAAiC,GAAGd,SAAS,EAC5C;EACD,MAAMkC,QAAQ,GAAGxB,WAAW,CAAEC,MAAM,EAAEC,aAAa,EAAEE,KAAM,CAAC;EAC5D,MAAMqB,OAAO,GAAGC,KAAK,CAAEC,UAAU,CAAEH,QAAS,CAAE,CAAC;EAE/C,OAAOC,OAAO;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,eAAeA,CAAE3B,MAAwB,EAAG;EAC3D,OACCA,MAAM,KAAKZ,SAAS,IACpBgC,MAAM,CAACpB,MAAM,CAAEA,MAAO,CAAC,CAACL,MAAM;EAC7B;EACA;EACA;EACEe,KAAK,IAAM,CAAC,CAAEA,KAAK,IAAI,IAAI,CAACkB,IAAI,CAAElB,KAAM,CAC3C,CAAC,CAACb,MAAM,GAAG,CAAC;AAEd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgC,cAAcA,CAAEC,QAAiB,EAAEC,WAAoB,EAAG;EACzE,IAAIC,WAAgC,GAAG,KAAK;EAE5C,IAAK,CAAEF,QAAQ,EAAG;IACjBE,WAAW,GAAGD,WAAW,GAAG,UAAU,GAAG,KAAK;EAC/C;EAEA,OAAOC,WAAW;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS5B,cAAcA,CAAED,KAAiC,EAAG;EACnE,MAAM8B,aAA0C,GAAG,EAAE;EAErD,IAAK,CAAE9B,KAAK,EAAEN,MAAM,EAAG;IACtB,OAAOR,SAAS;EACjB;EAEA,IAAKc,KAAK,CAAC+B,QAAQ,CAAE,UAAW,CAAC,EAAG;IACnCD,aAAa,CAACE,IAAI,CAAE,GAAK,CAAE,KAAK,EAAE,QAAQ,CAAc,CAAC;EAC1D,CAAC,MAAM,IAAKhC,KAAK,CAAC+B,QAAQ,CAAE,YAAa,CAAC,EAAG;IAC5CD,aAAa,CAACE,IAAI,CAAE,GAAK,CAAE,MAAM,EAAE,OAAO,CAAc,CAAC;EAC1D,CAAC,MAAM;IACN,MAAMC,QAAQ,GAAG/C,SAAS,CAACM,MAAM,CAAIY,IAAI,IAAMJ,KAAK,CAAC+B,QAAQ,CAAE3B,IAAK,CAAE,CAAC;IACvE0B,aAAa,CAACE,IAAI,CAAE,GAAGC,QAAS,CAAC;EAClC;EAEA,OAAOH,aAAa;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,iBAAiBA,CAChCC,aAA8B,EAC9BC,QAAiB,EACjBpC,KAAkC,EAChB;EAClB,MAAMqC,SAAS,GAAG;IAAE,GAAGF;EAAc,CAAC;EAEtC,IAAKnC,KAAK,EAAEN,MAAM,EAAG;IACpBM,KAAK,CAACsC,OAAO,CAAIlC,IAAI,IAAM;MAC1B,IAAKA,IAAI,KAAK,UAAU,EAAG;QAC1BiC,SAAS,CAAC5D,GAAG,GAAG2D,QAAQ;QACxBC,SAAS,CAAC3D,MAAM,GAAG0D,QAAQ;MAC5B,CAAC,MAAM,IAAKhC,IAAI,KAAK,YAAY,EAAG;QACnCiC,SAAS,CAAC1D,IAAI,GAAGyD,QAAQ;QACzBC,SAAS,CAACzD,KAAK,GAAGwD,QAAQ;MAC3B,CAAC,MAAM;QACNC,SAAS,CAAEjC,IAAI,CAAE,GAAGgC,QAAQ;MAC7B;IACD,CAAE,CAAC;EACJ,CAAC,MAAM;IACNlD,SAAS,CAACoD,OAAO,CAAIlC,IAAI,IAAQiC,SAAS,CAAEjC,IAAI,CAAE,GAAGgC,QAAW,CAAC;EAClE;EAEA,OAAOC,SAAS;AACjB"}