{"version": 3, "names": [], "sources": ["@wordpress/components/src/border-control/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { ColorPaletteProps } from '../color-palette/types';\nimport type { PopoverProps } from '../popover/types';\nimport type { ToggleGroupControlProps } from '../toggle-group-control/types';\n\nexport type Border = {\n\tcolor?: CSSProperties[ 'borderColor' ];\n\tstyle?: CSSProperties[ 'borderStyle' ];\n\twidth?: CSSProperties[ 'borderWidth' ];\n};\n\nexport type ColorProps = Pick<\n\tColorPaletteProps,\n\t'colors' | 'enableAlpha' | '__experimentalIsRenderedInSidebar'\n> & {\n\t/**\n\t * This toggles the ability to choose custom colors.\n\t */\n\tdisableCustomColors?: boolean;\n};\n\nexport type LabelProps = {\n\t/**\n\t * Provides control over whether the label will only be visible to\n\t * screen readers.\n\t */\n\thideLabelFromVision?: boolean;\n\t/**\n\t * If provided, a label will be generated using this as the content.\n\t */\n\tlabel?: string;\n};\n\nexport type BorderControlProps = ColorProps &\n\tLabelProps & {\n\t\t/**\n\t\t * This controls whether unit selection should be disabled.\n\t\t */\n\t\tdisableUnits?: boolean;\n\t\t/**\n\t\t * This controls whether to support border style selection.\n\t\t *\n\t\t * @default true\n\t\t */\n\t\tenableStyle?: boolean;\n\t\t/**\n\t\t * This flags the `BorderControl` to render with a more compact\n\t\t * appearance. It restricts the width of the control and prevents it\n\t\t * from expanding to take up additional space.\n\t\t */\n\t\tisCompact?: boolean;\n\t\t/**\n\t\t * A callback function invoked when the border value is changed via an\n\t\t * interaction that selects or clears, border color, style, or width.\n\t\t */\n\t\tonChange: ( value?: Border ) => void;\n\t\t/**\n\t\t * An internal prop used to control the visibility of the dropdown.\n\t\t */\n\t\t__unstablePopoverProps?: Omit< PopoverProps, 'children' >;\n\t\t/**\n\t\t * If opted into, sanitizing the border means that if no width or color\n\t\t * have been selected, the border style is also cleared and `undefined`\n\t\t * is returned as the new border value.\n\t\t *\n\t\t * @default true\n\t\t */\n\t\tshouldSanitizeBorder?: boolean;\n\t\t/**\n\t\t * Whether or not to show the header for the border color and style\n\t\t * picker dropdown. The header includes a label for the color picker\n\t\t * and a close button.\n\t\t */\n\t\tshowDropdownHeader?: boolean;\n\t\t/**\n\t\t * Size of the control.\n\t\t *\n\t\t * @default 'default'\n\t\t */\n\t\tsize?: 'default' | '__unstable-large';\n\t\t/**\n\t\t * An object representing a border or `undefined`. Used to set the\n\t\t * current border configuration for this component.\n\t\t */\n\t\tvalue?: Border;\n\t\t/**\n\t\t * Controls the visual width of the `BorderControl`. It has no effect if\n\t\t * the `isCompact` prop is set to `true`.\n\t\t */\n\t\twidth?: CSSProperties[ 'width' ];\n\t\t/**\n\t\t * Flags whether this `BorderControl` should also render a\n\t\t * `RangeControl` for additional control over a border's width.\n\t\t */\n\t\twithSlider?: boolean;\n\t\t/**\n\t\t * Start opting into the larger default height that will become the default size in a future version.\n\t\t *\n\t\t * @default false\n\t\t */\n\t\t__next40pxDefaultSize?: boolean;\n\t};\n\nexport type DropdownProps = ColorProps &\n\tPick< BorderControlProps, 'enableStyle' | 'size' > & {\n\t\t/**\n\t\t * An object representing a border or `undefined`. This component will\n\t\t * extract the border color and style selections from this object to use as\n\t\t * values for its popover controls.\n\t\t */\n\t\tborder?: Border;\n\t\t/**\n\t\t * Whether a border style can be set, based on the border sanitization settings.\n\t\t */\n\t\tisStyleSettable: boolean;\n\t\t/**\n\t\t * An internal prop used to control the visibility of the dropdown.\n\t\t */\n\t\t__unstablePopoverProps?: Omit< PopoverProps, 'children' >;\n\t\t/**\n\t\t * A callback invoked when the border color or style selections change.\n\t\t */\n\t\tonChange: ( newBorder?: Border ) => void;\n\t\t/**\n\t\t * Any previous style selection made by the user. This can be used to\n\t\t * reapply that previous selection when, for example, a zero border width is\n\t\t * to a non-zero value.\n\t\t */\n\t\tpreviousStyleSelection?: string;\n\t\t/**\n\t\t * Whether or not to render a header for the border color and style picker\n\t\t * dropdown. The header includes a label for the color picker and a\n\t\t * close button.\n\t\t */\n\t\tshowDropdownHeader?: boolean;\n\t};\n\nexport type StylePickerProps = Omit<\n\tToggleGroupControlProps,\n\t'value' | 'onChange' | 'children'\n> & {\n\t/**\n\t * A callback function invoked when a border style is selected or cleared.\n\t */\n\tonChange: ( style?: string ) => void;\n\t/**\n\t * The currently selected border style if there is one. Styles available via\n\t * this control are `solid`, `dashed` & `dotted`, however the possibility\n\t * to store other valid CSS values is maintained e.g. `none`, `inherit` etc.\n\t */\n\tvalue?: string;\n};\n"], "mappings": ""}