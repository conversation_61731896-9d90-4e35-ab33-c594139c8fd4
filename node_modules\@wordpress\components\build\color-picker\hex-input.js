"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.HexInput = void 0;
var _react = require("react");
var _colord = require("colord");
var _i18n = require("@wordpress/i18n");
var _inputControl = require("../input-control");
var _text = require("../text");
var _spacer = require("../spacer");
var _space = require("../utils/space");
var _colorsValues = require("../utils/colors-values");
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const HexInput = ({
  color,
  onChange,
  enableAlpha
}) => {
  const handleChange = nextValue => {
    if (!nextValue) return;
    const hexValue = nextValue.startsWith('#') ? nextValue : '#' + nextValue;
    onChange((0, _colord.colord)(hexValue));
  };
  const stateReducer = (state, action) => {
    const nativeEvent = action.payload?.event?.nativeEvent;
    if ('insertFromPaste' !== nativeEvent?.inputType) {
      return {
        ...state
      };
    }
    const value = state.value?.startsWith('#') ? state.value.slice(1).toUpperCase() : state.value?.toUpperCase();
    return {
      ...state,
      value
    };
  };
  return (0, _react.createElement)(_inputControl.InputControl, {
    prefix: (0, _react.createElement)(_spacer.Spacer, {
      as: _text.Text,
      marginLeft: (0, _space.space)(4),
      color: _colorsValues.COLORS.theme.accent,
      lineHeight: 1
    }, "#"),
    value: color.toHex().slice(1).toUpperCase(),
    onChange: handleChange,
    maxLength: enableAlpha ? 9 : 7,
    label: (0, _i18n.__)('Hex color'),
    hideLabelFromVision: true,
    size: "__unstable-large",
    __unstableStateReducer: stateReducer,
    __unstableInputWidth: "9em"
  });
};
exports.HexInput = HexInput;
//# sourceMappingURL=hex-input.js.map