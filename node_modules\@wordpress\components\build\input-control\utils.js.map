{"version": 3, "names": ["_element", "require", "getDragCursor", "dragDirection", "dragCursor", "useDragCursor", "isDragging", "useEffect", "document", "documentElement", "style", "cursor", "useDraft", "props", "refPreviousV<PERSON>ue", "useRef", "value", "draft", "setDraft", "useState", "undefined", "useLayoutEffect", "current", "previousValue", "isStale", "onChange", "nextValue", "extra", "Object", "assign", "onBlur", "event"], "sources": ["@wordpress/components/src/input-control/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { FocusEventHandler } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseRef,\n\tuseState,\n} from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport type { InputChangeCallback } from './types';\n\n/**\n * Gets a CSS cursor value based on a drag direction.\n *\n * @param dragDirection The drag direction.\n * @return  The CSS cursor value.\n */\nexport function getDragCursor( dragDirection: string ): string {\n\tlet dragCursor = 'ns-resize';\n\n\tswitch ( dragDirection ) {\n\t\tcase 'n':\n\t\tcase 's':\n\t\t\tdragCursor = 'ns-resize';\n\t\t\tbreak;\n\n\t\tcase 'e':\n\t\tcase 'w':\n\t\t\tdragCursor = 'ew-resize';\n\t\t\tbreak;\n\t}\n\n\treturn dragCursor;\n}\n\n/**\n * Custom hook that renders a drag cursor when dragging.\n *\n * @param {boolean} isDragging    The dragging state.\n * @param {string}  dragDirection The drag direction.\n *\n * @return {string} The CSS cursor value.\n */\nexport function useDragCursor(\n\tisDragging: boolean,\n\tdragDirection: string\n): string {\n\tconst dragCursor = getDragCursor( dragDirection );\n\n\tuseEffect( () => {\n\t\tif ( isDragging ) {\n\t\t\tdocument.documentElement.style.cursor = dragCursor;\n\t\t} else {\n\t\t\t// @ts-expect-error\n\t\t\tdocument.documentElement.style.cursor = null;\n\t\t}\n\t}, [ isDragging, dragCursor ] );\n\n\treturn dragCursor;\n}\n\nexport function useDraft( props: {\n\tvalue: string | undefined;\n\tonBlur?: FocusEventHandler;\n\tonChange: InputChangeCallback;\n} ) {\n\tconst refPreviousValue = useRef( props.value );\n\tconst [ draft, setDraft ] = useState< {\n\t\tvalue?: string;\n\t\tisStale?: boolean;\n\t} >( {} );\n\tconst value = draft.value !== undefined ? draft.value : props.value;\n\n\t// Determines when to discard the draft value to restore controlled status.\n\t// To do so, it tracks the previous value and marks the draft value as stale\n\t// after each render.\n\tuseLayoutEffect( () => {\n\t\tconst { current: previousValue } = refPreviousValue;\n\t\trefPreviousValue.current = props.value;\n\t\tif ( draft.value !== undefined && ! draft.isStale )\n\t\t\tsetDraft( { ...draft, isStale: true } );\n\t\telse if ( draft.isStale && props.value !== previousValue )\n\t\t\tsetDraft( {} );\n\t}, [ props.value, draft ] );\n\n\tconst onChange: InputChangeCallback = ( nextValue, extra ) => {\n\t\t// Mutates the draft value to avoid an extra effect run.\n\t\tsetDraft( ( current ) =>\n\t\t\tObject.assign( current, { value: nextValue, isStale: false } )\n\t\t);\n\t\tprops.onChange( nextValue, extra );\n\t};\n\tconst onBlur: FocusEventHandler = ( event ) => {\n\t\tsetDraft( {} );\n\t\tprops.onBlur?.( event );\n\t};\n\n\treturn { value, onBlur, onChange };\n}\n"], "mappings": ";;;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAaA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAAEC,aAAqB,EAAW;EAC9D,IAAIC,UAAU,GAAG,WAAW;EAE5B,QAASD,aAAa;IACrB,KAAK,GAAG;IACR,KAAK,GAAG;MACPC,UAAU,GAAG,WAAW;MACxB;IAED,KAAK,GAAG;IACR,KAAK,GAAG;MACPA,UAAU,GAAG,WAAW;MACxB;EACF;EAEA,OAAOA,UAAU;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAC5BC,UAAmB,EACnBH,aAAqB,EACZ;EACT,MAAMC,UAAU,GAAGF,aAAa,CAAEC,aAAc,CAAC;EAEjD,IAAAI,kBAAS,EAAE,MAAM;IAChB,IAAKD,UAAU,EAAG;MACjBE,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,MAAM,GAAGP,UAAU;IACnD,CAAC,MAAM;MACN;MACAI,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,MAAM,GAAG,IAAI;IAC7C;EACD,CAAC,EAAE,CAAEL,UAAU,EAAEF,UAAU,CAAG,CAAC;EAE/B,OAAOA,UAAU;AAClB;AAEO,SAASQ,QAAQA,CAAEC,KAIzB,EAAG;EACH,MAAMC,gBAAgB,GAAG,IAAAC,eAAM,EAAEF,KAAK,CAACG,KAAM,CAAC;EAC9C,MAAM,CAAEC,KAAK,EAAEC,QAAQ,CAAE,GAAG,IAAAC,iBAAQ,EAG/B,CAAC,CAAE,CAAC;EACT,MAAMH,KAAK,GAAGC,KAAK,CAACD,KAAK,KAAKI,SAAS,GAAGH,KAAK,CAACD,KAAK,GAAGH,KAAK,CAACG,KAAK;;EAEnE;EACA;EACA;EACA,IAAAK,wBAAe,EAAE,MAAM;IACtB,MAAM;MAAEC,OAAO,EAAEC;IAAc,CAAC,GAAGT,gBAAgB;IACnDA,gBAAgB,CAACQ,OAAO,GAAGT,KAAK,CAACG,KAAK;IACtC,IAAKC,KAAK,CAACD,KAAK,KAAKI,SAAS,IAAI,CAAEH,KAAK,CAACO,OAAO,EAChDN,QAAQ,CAAE;MAAE,GAAGD,KAAK;MAAEO,OAAO,EAAE;IAAK,CAAE,CAAC,CAAC,KACpC,IAAKP,KAAK,CAACO,OAAO,IAAIX,KAAK,CAACG,KAAK,KAAKO,aAAa,EACvDL,QAAQ,CAAE,CAAC,CAAE,CAAC;EAChB,CAAC,EAAE,CAAEL,KAAK,CAACG,KAAK,EAAEC,KAAK,CAAG,CAAC;EAE3B,MAAMQ,QAA6B,GAAGA,CAAEC,SAAS,EAAEC,KAAK,KAAM;IAC7D;IACAT,QAAQ,CAAII,OAAO,IAClBM,MAAM,CAACC,MAAM,CAAEP,OAAO,EAAE;MAAEN,KAAK,EAAEU,SAAS;MAAEF,OAAO,EAAE;IAAM,CAAE,CAC9D,CAAC;IACDX,KAAK,CAACY,QAAQ,CAAEC,SAAS,EAAEC,KAAM,CAAC;EACnC,CAAC;EACD,MAAMG,MAAyB,GAAKC,KAAK,IAAM;IAC9Cb,QAAQ,CAAE,CAAC,CAAE,CAAC;IACdL,KAAK,CAACiB,MAAM,GAAIC,KAAM,CAAC;EACxB,CAAC;EAED,OAAO;IAAEf,KAAK;IAAEc,MAAM;IAAEL;EAAS,CAAC;AACnC"}