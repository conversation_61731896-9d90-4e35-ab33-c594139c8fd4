"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SelectControl = exports.RangeControl = exports.NumberControlWrapper = exports.CopyButton = exports.ColorfulWrapper = exports.ColorInputWrapper = exports.AuxiliaryColorArtefactWrapper = exports.AuxiliaryColorArtefactHStackHeader = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _numberControl = _interopRequireDefault(require("../number-control"));
var _selectControl = _interopRequireDefault(require("../select-control"));
var _rangeControl = _interopRequireDefault(require("../range-control"));
var _space = require("../utils/space");
var _utils = require("../utils");
var _button = _interopRequireDefault(require("../button"));
var _flex = require("../flex");
var _hStack = require("../h-stack");
var _inputControlStyles = require("../input-control/styles/input-control-styles");
var _configValues = _interopRequireDefault(require("../utils/config-values"));
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const NumberControlWrapper = ( /*#__PURE__*/0, _base.default)(_numberControl.default, process.env.NODE_ENV === "production" ? {
  target: "ez9hsf47"
} : {
  target: "ez9hsf47",
  label: "NumberControlWrapper"
})(_inputControlStyles.Container, "{width:", (0, _space.space)(24), ";}" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.NumberControlWrapper = NumberControlWrapper;
const SelectControl = ( /*#__PURE__*/0, _base.default)(_selectControl.default, process.env.NODE_ENV === "production" ? {
  target: "ez9hsf46"
} : {
  target: "ez9hsf46",
  label: "SelectControl"
})("margin-left:", (0, _space.space)(-2), ";width:5em;select:not( :focus )~", _inputControlStyles.BackdropUI, _inputControlStyles.BackdropUI, _inputControlStyles.BackdropUI, "{border-color:transparent;}" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.SelectControl = SelectControl;
const RangeControl = ( /*#__PURE__*/0, _base.default)(_rangeControl.default, process.env.NODE_ENV === "production" ? {
  target: "ez9hsf45"
} : {
  target: "ez9hsf45",
  label: "RangeControl"
})("flex:1;margin-right:", (0, _space.space)(2), ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));

// Make the Hue circle picker not go out of the bar.
exports.RangeControl = RangeControl;
const interactiveHueStyles = `
.react-colorful__interactive {
	width: calc( 100% - ${(0, _space.space)(2)} );
	margin-left: ${(0, _space.space)(1)};
}`;
const AuxiliaryColorArtefactWrapper = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "ez9hsf44"
} : {
  target: "ez9hsf44",
  label: "AuxiliaryColorArtefactWrapper"
})("padding-top:", (0, _space.space)(2), ";padding-right:0;padding-left:0;padding-bottom:0;" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.AuxiliaryColorArtefactWrapper = AuxiliaryColorArtefactWrapper;
const AuxiliaryColorArtefactHStackHeader = ( /*#__PURE__*/0, _base.default)(_hStack.HStack, process.env.NODE_ENV === "production" ? {
  target: "ez9hsf43"
} : {
  target: "ez9hsf43",
  label: "AuxiliaryColorArtefactHStackHeader"
})("padding-left:", (0, _space.space)(4), ";padding-right:", (0, _space.space)(4), ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.AuxiliaryColorArtefactHStackHeader = AuxiliaryColorArtefactHStackHeader;
const ColorInputWrapper = ( /*#__PURE__*/0, _base.default)(_flex.Flex, process.env.NODE_ENV === "production" ? {
  target: "ez9hsf42"
} : {
  target: "ez9hsf42",
  label: "ColorInputWrapper"
})("padding-top:", (0, _space.space)(4), ";padding-left:", (0, _space.space)(4), ";padding-right:", (0, _space.space)(3), ";padding-bottom:", (0, _space.space)(5), ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.ColorInputWrapper = ColorInputWrapper;
const ColorfulWrapper = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "ez9hsf41"
} : {
  target: "ez9hsf41",
  label: "ColorfulWrapper"
})(_utils.boxSizingReset, ";width:216px;.react-colorful{display:flex;flex-direction:column;align-items:center;width:216px;height:auto;}.react-colorful__saturation{width:100%;border-radius:0;height:216px;margin-bottom:", (0, _space.space)(4), ";border-bottom:none;}.react-colorful__hue,.react-colorful__alpha{width:184px;height:16px;border-radius:16px;margin-bottom:", (0, _space.space)(2), ";}.react-colorful__pointer{height:16px;width:16px;border:none;box-shadow:0 0 2px 0 rgba( 0, 0, 0, 0.25 );outline:2px solid transparent;}.react-colorful__pointer-fill{box-shadow:inset 0 0 0 ", _configValues.default.borderWidthFocus, " #fff;}", interactiveHueStyles, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.ColorfulWrapper = ColorfulWrapper;
const CopyButton = ( /*#__PURE__*/0, _base.default)(_button.default, process.env.NODE_ENV === "production" ? {
  target: "ez9hsf40"
} : {
  target: "ez9hsf40",
  label: "CopyButton"
})("&&&&&{min-width:", (0, _space.space)(6), ";padding:0;>svg{margin-right:0;}}" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.CopyButton = CopyButton;
//# sourceMappingURL=styles.js.map