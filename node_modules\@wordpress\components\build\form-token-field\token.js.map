{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_compose", "_i18n", "_icons", "_button", "_visually<PERSON><PERSON>den", "noop", "Token", "value", "status", "title", "displayTransform", "isBorderless", "disabled", "onClickRemove", "onMouseEnter", "onMouseLeave", "messages", "termPosition", "termsCount", "instanceId", "useInstanceId", "tokenClasses", "classnames", "onClick", "transformedValue", "termPositionAndCount", "sprintf", "__", "_react", "createElement", "className", "id", "VisuallyHidden", "as", "default", "icon", "closeSmall", "undefined", "label", "remove"], "sources": ["@wordpress/components/src/form-token-field/token.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { __, sprintf } from '@wordpress/i18n';\nimport { closeSmall } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport Button from '../button';\nimport { VisuallyHidden } from '../visually-hidden';\nimport type { TokenProps } from './types';\n\nconst noop = () => {};\n\nexport default function Token( {\n\tvalue,\n\tstatus,\n\ttitle,\n\tdisplayTransform,\n\tisBorderless = false,\n\tdisabled = false,\n\tonClickRemove = noop,\n\tonMouseEnter,\n\tonMouseLeave,\n\tmessages,\n\ttermPosition,\n\ttermsCount,\n}: TokenProps ) {\n\tconst instanceId = useInstanceId( Token );\n\tconst tokenClasses = classnames( 'components-form-token-field__token', {\n\t\t'is-error': 'error' === status,\n\t\t'is-success': 'success' === status,\n\t\t'is-validating': 'validating' === status,\n\t\t'is-borderless': isBorderless,\n\t\t'is-disabled': disabled,\n\t} );\n\n\tconst onClick = () => onClickRemove( { value } );\n\n\tconst transformedValue = displayTransform( value );\n\tconst termPositionAndCount = sprintf(\n\t\t/* translators: 1: term name, 2: term position in a set of terms, 3: total term set count. */\n\t\t__( '%1$s (%2$s of %3$s)' ),\n\t\ttransformedValue,\n\t\ttermPosition,\n\t\ttermsCount\n\t);\n\n\treturn (\n\t\t<span\n\t\t\tclassName={ tokenClasses }\n\t\t\tonMouseEnter={ onMouseEnter }\n\t\t\tonMouseLeave={ onMouseLeave }\n\t\t\ttitle={ title }\n\t\t>\n\t\t\t<span\n\t\t\t\tclassName=\"components-form-token-field__token-text\"\n\t\t\t\tid={ `components-form-token-field__token-text-${ instanceId }` }\n\t\t\t>\n\t\t\t\t<VisuallyHidden as=\"span\">\n\t\t\t\t\t{ termPositionAndCount }\n\t\t\t\t</VisuallyHidden>\n\t\t\t\t<span aria-hidden=\"true\">{ transformedValue }</span>\n\t\t\t</span>\n\n\t\t\t<Button\n\t\t\t\tclassName=\"components-form-token-field__remove-token\"\n\t\t\t\ticon={ closeSmall }\n\t\t\t\tonClick={ ! disabled ? onClick : undefined }\n\t\t\t\tdisabled={ disabled }\n\t\t\t\tlabel={ messages.remove }\n\t\t\t\taria-describedby={ `components-form-token-field__token-text-${ instanceId }` }\n\t\t\t/>\n\t\t</span>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAKA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,eAAA,GAAAL,OAAA;AAhBA;AACA;AACA;;AAGA;AACA;AACA;;AAKA;AACA;AACA;;AAKA,MAAMM,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAEN,SAASC,KAAKA,CAAE;EAC9BC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,gBAAgB;EAChBC,YAAY,GAAG,KAAK;EACpBC,QAAQ,GAAG,KAAK;EAChBC,aAAa,GAAGR,IAAI;EACpBS,YAAY;EACZC,YAAY;EACZC,QAAQ;EACRC,YAAY;EACZC;AACW,CAAC,EAAG;EACf,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEd,KAAM,CAAC;EACzC,MAAMe,YAAY,GAAG,IAAAC,mBAAU,EAAE,oCAAoC,EAAE;IACtE,UAAU,EAAE,OAAO,KAAKd,MAAM;IAC9B,YAAY,EAAE,SAAS,KAAKA,MAAM;IAClC,eAAe,EAAE,YAAY,KAAKA,MAAM;IACxC,eAAe,EAAEG,YAAY;IAC7B,aAAa,EAAEC;EAChB,CAAE,CAAC;EAEH,MAAMW,OAAO,GAAGA,CAAA,KAAMV,aAAa,CAAE;IAAEN;EAAM,CAAE,CAAC;EAEhD,MAAMiB,gBAAgB,GAAGd,gBAAgB,CAAEH,KAAM,CAAC;EAClD,MAAMkB,oBAAoB,GAAG,IAAAC,aAAO,GACnC;EACA,IAAAC,QAAE,EAAE,qBAAsB,CAAC,EAC3BH,gBAAgB,EAChBP,YAAY,EACZC,UACD,CAAC;EAED,OACC,IAAAU,MAAA,CAAAC,aAAA;IACCC,SAAS,EAAGT,YAAc;IAC1BP,YAAY,EAAGA,YAAc;IAC7BC,YAAY,EAAGA,YAAc;IAC7BN,KAAK,EAAGA;EAAO,GAEf,IAAAmB,MAAA,CAAAC,aAAA;IACCC,SAAS,EAAC,yCAAyC;IACnDC,EAAE,EAAI,2CAA2CZ,UAAY;EAAG,GAEhE,IAAAS,MAAA,CAAAC,aAAA,EAACzB,eAAA,CAAA4B,cAAc;IAACC,EAAE,EAAC;EAAM,GACtBR,oBACa,CAAC,EACjB,IAAAG,MAAA,CAAAC,aAAA;IAAM,eAAY;EAAM,GAAGL,gBAAwB,CAC9C,CAAC,EAEP,IAAAI,MAAA,CAAAC,aAAA,EAAC1B,OAAA,CAAA+B,OAAM;IACNJ,SAAS,EAAC,2CAA2C;IACrDK,IAAI,EAAGC,iBAAY;IACnBb,OAAO,EAAG,CAAEX,QAAQ,GAAGW,OAAO,GAAGc,SAAW;IAC5CzB,QAAQ,EAAGA,QAAU;IACrB0B,KAAK,EAAGtB,QAAQ,CAACuB,MAAQ;IACzB,oBAAoB,2CAA2CpB,UAAY;EAAG,CAC9E,CACI,CAAC;AAET"}