"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Option = Option;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _compose = require("@wordpress/compose");
var _element = require("@wordpress/element");
var _icons = require("@wordpress/icons");
var _circularOptionPickerContext = require("./circular-option-picker-context");
var _button = _interopRequireDefault(require("../button"));
var _v = require("../composite/v2");
var _tooltip = _interopRequireDefault(require("../tooltip"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function UnforwardedOptionAsButton(props, forwardedRef) {
  const {
    isPressed,
    ...additionalProps
  } = props;
  return (0, _react.createElement)(_button.default, {
    ...additionalProps,
    "aria-pressed": isPressed,
    ref: forwardedRef
  });
}
const OptionAsButton = (0, _element.forwardRef)(UnforwardedOptionAsButton);
function UnforwardedOptionAsOption(props, forwardedRef) {
  const {
    id,
    isSelected,
    compositeStore,
    ...additionalProps
  } = props;
  const activeId = compositeStore.useState('activeId');
  if (isSelected && !activeId) {
    compositeStore.setActiveId(id);
  }
  return (0, _react.createElement)(_v.CompositeItem, {
    render: (0, _react.createElement)(_button.default, {
      ...additionalProps,
      role: "option",
      "aria-selected": !!isSelected,
      ref: forwardedRef
    }),
    store: compositeStore,
    id: id
  });
}
const OptionAsOption = (0, _element.forwardRef)(UnforwardedOptionAsOption);
function Option({
  className,
  isSelected,
  selectedIconProps = {},
  tooltipText,
  ...additionalProps
}) {
  const {
    baseId,
    compositeStore
  } = (0, _element.useContext)(_circularOptionPickerContext.CircularOptionPickerContext);
  const id = (0, _compose.useInstanceId)(Option, baseId || 'components-circular-option-picker__option');
  const commonProps = {
    id,
    className: 'components-circular-option-picker__option',
    ...additionalProps
  };
  const optionControl = compositeStore ? (0, _react.createElement)(OptionAsOption, {
    ...commonProps,
    compositeStore: compositeStore,
    isSelected: isSelected
  }) : (0, _react.createElement)(OptionAsButton, {
    ...commonProps,
    isPressed: isSelected
  });
  return (0, _react.createElement)("div", {
    className: (0, _classnames.default)(className, 'components-circular-option-picker__option-wrapper')
  }, tooltipText ? (0, _react.createElement)(_tooltip.default, {
    text: tooltipText
  }, optionControl) : optionControl, isSelected && (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check,
    ...selectedIconProps
  }));
}
//# sourceMappingURL=circular-option-picker-option.js.map