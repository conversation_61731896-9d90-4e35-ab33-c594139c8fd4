"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "BorderBoxControl", {
  enumerable: true,
  get: function () {
    return _component.default;
  }
});
Object.defineProperty(exports, "hasSplitBorders", {
  enumerable: true,
  get: function () {
    return _utils.hasSplitBorders;
  }
});
Object.defineProperty(exports, "isDefinedBorder", {
  enumerable: true,
  get: function () {
    return _utils.isDefinedBorder;
  }
});
Object.defineProperty(exports, "isEmptyBorder", {
  enumerable: true,
  get: function () {
    return _utils.isEmptyBorder;
  }
});
Object.defineProperty(exports, "useBorderBoxControl", {
  enumerable: true,
  get: function () {
    return _hook.useBorderBoxControl;
  }
});
var _component = _interopRequireDefault(require("./border-box-control/component"));
var _hook = require("./border-box-control/hook");
var _utils = require("./utils");
//# sourceMappingURL=index.js.map