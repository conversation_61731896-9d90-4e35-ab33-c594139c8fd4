{"version": 3, "names": ["_i18n", "require", "_toggleGroupControl", "_constants", "FontSizePickerToggleGroup", "props", "fontSizes", "value", "__nextHasNoMarginBottom", "__next40pxDefaultSize", "size", "onChange", "_react", "createElement", "ToggleGroupControl", "label", "__", "hideLabelFromVision", "isBlock", "map", "fontSize", "index", "ToggleGroupControlOption", "key", "slug", "T_SHIRT_ABBREVIATIONS", "name", "T_SHIRT_NAMES", "showTooltip", "_default", "exports", "default"], "sources": ["@wordpress/components/src/font-size-picker/font-size-picker-toggle-group.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport {\n\tToggleGroupControl,\n\tToggleGroupControlOption,\n} from '../toggle-group-control';\nimport { T_SHIRT_ABBREVIATIONS, T_SHIRT_NAMES } from './constants';\nimport type { FontSizePickerToggleGroupProps } from './types';\n\nconst FontSizePickerToggleGroup = ( props: FontSizePickerToggleGroupProps ) => {\n\tconst {\n\t\tfontSizes,\n\t\tvalue,\n\t\t__nextHasNoMarginBottom,\n\t\t__next40pxDefaultSize,\n\t\tsize,\n\t\tonChange,\n\t} = props;\n\treturn (\n\t\t<ToggleGroupControl\n\t\t\t__nextHasNoMarginBottom={ __nextHasNoMarginBottom }\n\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\tlabel={ __( 'Font size' ) }\n\t\t\thideLabelFromVision\n\t\t\tvalue={ value }\n\t\t\tonChange={ onChange }\n\t\t\tisBlock\n\t\t\tsize={ size }\n\t\t>\n\t\t\t{ fontSizes.map( ( fontSize, index ) => (\n\t\t\t\t<ToggleGroupControlOption\n\t\t\t\t\tkey={ fontSize.slug }\n\t\t\t\t\tvalue={ fontSize.size }\n\t\t\t\t\tlabel={ T_SHIRT_ABBREVIATIONS[ index ] }\n\t\t\t\t\taria-label={ fontSize.name || T_SHIRT_NAMES[ index ] }\n\t\t\t\t\tshowTooltip\n\t\t\t\t/>\n\t\t\t) ) }\n\t\t</ToggleGroupControl>\n\t);\n};\n\nexport default FontSizePickerToggleGroup;\n"], "mappings": ";;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,mBAAA,GAAAD,OAAA;AAIA,IAAAE,UAAA,GAAAF,OAAA;AAZA;AACA;AACA;;AAGA;AACA;AACA;;AAQA,MAAMG,yBAAyB,GAAKC,KAAqC,IAAM;EAC9E,MAAM;IACLC,SAAS;IACTC,KAAK;IACLC,uBAAuB;IACvBC,qBAAqB;IACrBC,IAAI;IACJC;EACD,CAAC,GAAGN,KAAK;EACT,OACC,IAAAO,MAAA,CAAAC,aAAA,EAACX,mBAAA,CAAAY,kBAAkB;IAClBN,uBAAuB,EAAGA,uBAAyB;IACnDC,qBAAqB,EAAGA,qBAAuB;IAC/CM,KAAK,EAAG,IAAAC,QAAE,EAAE,WAAY,CAAG;IAC3BC,mBAAmB;IACnBV,KAAK,EAAGA,KAAO;IACfI,QAAQ,EAAGA,QAAU;IACrBO,OAAO;IACPR,IAAI,EAAGA;EAAM,GAEXJ,SAAS,CAACa,GAAG,CAAE,CAAEC,QAAQ,EAAEC,KAAK,KACjC,IAAAT,MAAA,CAAAC,aAAA,EAACX,mBAAA,CAAAoB,wBAAwB;IACxBC,GAAG,EAAGH,QAAQ,CAACI,IAAM;IACrBjB,KAAK,EAAGa,QAAQ,CAACV,IAAM;IACvBK,KAAK,EAAGU,gCAAqB,CAAEJ,KAAK,CAAI;IACxC,cAAaD,QAAQ,CAACM,IAAI,IAAIC,wBAAa,CAAEN,KAAK,CAAI;IACtDO,WAAW;EAAA,CACX,CACA,CACiB,CAAC;AAEvB,CAAC;AAAC,IAAAC,QAAA,GAEazB,yBAAyB;AAAA0B,OAAA,CAAAC,OAAA,GAAAF,QAAA"}