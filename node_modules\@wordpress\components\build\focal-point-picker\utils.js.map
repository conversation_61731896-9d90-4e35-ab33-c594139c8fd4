{"version": 3, "names": ["INITIAL_BOUNDS", "width", "height", "exports", "VIDEO_EXTENSIONS", "getExtension", "filename", "parts", "split", "length", "isVideoType", "startsWith", "includes", "fractionToPercentage", "fraction", "Math", "round"], "sources": ["@wordpress/components/src/focal-point-picker/utils.ts"], "sourcesContent": ["export const INITIAL_BOUNDS = {\n\twidth: 200,\n\theight: 170,\n};\n\nconst VIDEO_EXTENSIONS = [\n\t'avi',\n\t'mpg',\n\t'mpeg',\n\t'mov',\n\t'mp4',\n\t'm4v',\n\t'ogg',\n\t'ogv',\n\t'webm',\n\t'wmv',\n];\n\n/**\n * Gets the extension of a file name.\n *\n * @param filename The file name.\n * @return  The extension of the file name.\n */\nexport function getExtension( filename = '' ): string {\n\tconst parts = filename.split( '.' );\n\treturn parts[ parts.length - 1 ];\n}\n\n/**\n * Checks if a file is a video.\n *\n * @param filename The file name.\n * @return Whether the file is a video.\n */\nexport function isVideoType( filename: string = '' ): boolean {\n\tif ( ! filename ) return false;\n\treturn (\n\t\tfilename.startsWith( 'data:video/' ) ||\n\t\tVIDEO_EXTENSIONS.includes( getExtension( filename ) )\n\t);\n}\n\n/**\n * Transforms a fraction value to a percentage value.\n *\n * @param fraction The fraction value.\n * @return A percentage value.\n */\nexport function fractionToPercentage( fraction: number ): number {\n\treturn Math.round( fraction * 100 );\n}\n"], "mappings": ";;;;;;;;;AAAO,MAAMA,cAAc,GAAG;EAC7BC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE;AACT,CAAC;AAACC,OAAA,CAAAH,cAAA,GAAAA,cAAA;AAEF,MAAMI,gBAAgB,GAAG,CACxB,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,CACL;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,YAAYA,CAAEC,QAAQ,GAAG,EAAE,EAAW;EACrD,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAE,GAAI,CAAC;EACnC,OAAOD,KAAK,CAAEA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAE;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CAAEJ,QAAgB,GAAG,EAAE,EAAY;EAC7D,IAAK,CAAEA,QAAQ,EAAG,OAAO,KAAK;EAC9B,OACCA,QAAQ,CAACK,UAAU,CAAE,aAAc,CAAC,IACpCP,gBAAgB,CAACQ,QAAQ,CAAEP,YAAY,CAAEC,QAAS,CAAE,CAAC;AAEvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASO,oBAAoBA,CAAEC,QAAgB,EAAW;EAChE,OAAOC,IAAI,CAACC,KAAK,CAAEF,QAAQ,GAAG,GAAI,CAAC;AACpC"}