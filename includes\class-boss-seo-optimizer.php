<?php
/**
 * Optimiseur SEO intelligent pour Boss SEO.
 *
 * @link       https://bossseo.com
 * @since      1.2.0
 *
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 */

/**
 * Classe d'optimisation SEO intelligente.
 *
 * Cette classe fournit toutes les méthodes d'optimisation automatique,
 * de validation et de génération de schémas.
 *
 * @since      1.2.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes
 * <AUTHOR> SEO Team
 */
class Boss_SEO_Optimizer {

    /**
     * Instance du service IA.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_AI    $ai    Instance du service IA.
     */
    private $ai;

    /**
     * Instance des paramètres.
     *
     * @since    1.2.0
     * @access   private
     * @var      Boss_Optimizer_Settings    $settings    Instance des paramètres.
     */
    private $settings;

    /**
     * Constructeur.
     *
     * @since    1.2.0
     */
    public function __construct() {
        // Charger les dépendances
        if ( ! class_exists( 'Boss_Optimizer_AI' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-ai.php';
        }
        if ( ! class_exists( 'Boss_Optimizer_Settings' ) ) {
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-boss-optimizer-settings.php';
        }

        $this->settings = new Boss_Optimizer_Settings( 'boss-seo' );
        $this->ai = new Boss_Optimizer_AI( 'boss-seo', $this->settings );
    }

    /**
     * Effectue l'optimisation automatique du contenu avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id    ID du post.
     * @param    string    $content    Contenu à optimiser.
     * @param    array     $keywords   Mots-clés à utiliser.
     * @return   array                 Suggestions d'optimisation.
     */
    public function perform_content_optimization( $post_id, $content, $keywords ) {
        $optimizations = array();

        // Générer les mots-clés si pas fournis
        if ( empty( $keywords ) ) {
            $keywords = $this->generate_keywords_from_content( $post_id, $content );
        }

        $primary_keyword = ! empty( $keywords ) ? $keywords[0] : '';
        $secondary_keywords = ! empty( $keywords ) ? array_slice( $keywords, 1, 3 ) : array();

        // Optimiser le titre
        $title_optimization = $this->optimize_title( $post_id, $primary_keyword );
        if ( $title_optimization ) {
            $optimizations['title'] = $title_optimization;
        }

        // Optimiser la meta description
        $desc_optimization = $this->optimize_meta_description( $post_id, $content, $primary_keyword );
        if ( $desc_optimization ) {
            $optimizations['meta_description'] = $desc_optimization;
        }

        // Ajouter les mots-clés
        if ( ! empty( $primary_keyword ) ) {
            $optimizations['focus_keyword'] = $primary_keyword;
        }

        if ( ! empty( $secondary_keywords ) ) {
            $optimizations['secondary_keywords'] = $secondary_keywords;
        }

        // Optimiser l'URL (slug)
        $slug_optimization = $this->optimize_slug( $post_id, $primary_keyword );
        if ( $slug_optimization ) {
            $optimizations['slug'] = $slug_optimization;
        }

        // Suggestions pour le contenu
        $content_suggestions = $this->generate_content_suggestions( $content, $keywords );
        if ( ! empty( $content_suggestions ) ) {
            $optimizations['content_suggestions'] = $content_suggestions;
        }

        // Suggestions pour les images
        $image_suggestions = $this->generate_image_suggestions( $post_id, $primary_keyword );
        if ( ! empty( $image_suggestions ) ) {
            $optimizations['image_suggestions'] = $image_suggestions;
        }

        return array(
            'optimizations' => $optimizations,
            'seo_score' => $this->calculate_seo_score( $optimizations ),
            'message' => 'Métadonnées optimisées avec IA'
        );
    }

    /**
     * Optimise le titre SEO avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Titre optimisé ou false.
     */
    private function optimize_title( $post_id, $primary_keyword ) {
        $current_title = get_post_meta( $post_id, '_boss_seo_title', true );
        $post_title = get_the_title( $post_id );

        $title_to_optimize = ! empty( $current_title ) ? $current_title : $post_title;

        if ( empty( $primary_keyword ) ) {
            return false;
        }

        // Vérifier si le mot-clé est déjà présent
        if ( stripos( $title_to_optimize, $primary_keyword ) !== false ) {
            return false; // Déjà optimisé
        }

        // Vérifier si l'IA est configurée et activée pour les titres
        if ( $this->settings->is_ai_configured() && $this->settings->get( 'ai', 'use_ai_for_titles', true ) ) {
            error_log( 'Boss SEO: Utilisation de l\'IA pour générer le titre' );
            return $this->generate_ai_optimized_title( $title_to_optimize, $primary_keyword, $post_id );
        }

        error_log( 'Boss SEO: IA non configurée, utilisation du fallback pour le titre' );

        // Fallback : génération avec templates
        return $this->generate_template_title( $title_to_optimize, $primary_keyword );
    }

    /**
     * Génère un titre optimisé avec IA.
     *
     * @since    1.2.0
     * @param    string    $original_title  Titre original.
     * @param    string    $keyword         Mot-clé principal.
     * @param    int       $post_id         ID du post.
     * @return   string|false               Titre optimisé ou false.
     */
    private function generate_ai_optimized_title( $original_title, $keyword, $post_id ) {
        // Récupérer le contenu pour le contexte
        $post = get_post( $post_id );
        $content_excerpt = wp_trim_words( wp_strip_all_tags( $post->post_content ), 30 );

        // Construire le prompt pour l'IA
        $prompt = sprintf(
            'INSTRUCTIONS STRICTES: Réponds UNIQUEMENT avec le titre optimisé. Aucun texte explicatif, aucun préambule, aucun "Voici".

Titre actuel: "%s"
Mot-clé: "%s"
Contenu: "%s"

RÈGLES:
- Maximum 60 caractères
- Inclure le mot-clé naturellement
- Pas de guillemets
- Pas de préfixe comme "Titre:" ou "Voici"

RÉPONSE ATTENDUE (exemple): Hello World - Guide Complet

TON TITRE OPTIMISÉ:',
            $original_title,
            $keyword,
            $content_excerpt
        );

        // Générer avec l'IA
        $ai_result = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.7,
            'max_tokens' => 100
        ) );

        if ( $ai_result['success'] && ! empty( $ai_result['content'] ) ) {
            // Nettoyer AGRESSIVEMENT la réponse IA
            $optimized_title = $this->clean_ai_response( $ai_result['content'] );

            // Extraire seulement le titre (première ligne non vide)
            $lines = explode( "\n", $optimized_title );
            foreach ( $lines as $line ) {
                $line = trim( $line );
                if ( ! empty( $line ) && ! $this->is_conversational_text( $line ) ) {
                    $optimized_title = $line;
                    break;
                }
            }

            // Limiter à 60 caractères
            if ( mb_strlen( $optimized_title ) > 60 ) {
                $optimized_title = mb_substr( $optimized_title, 0, 57 ) . '...';
            }

            return $optimized_title;
        }

        // Fallback si l'IA échoue
        return $this->generate_template_title( $original_title, $keyword );
    }

    /**
     * Génère un titre optimisé avec templates (fallback).
     *
     * @since    1.2.0
     * @param    string    $original_title  Titre original.
     * @param    string    $keyword         Mot-clé à intégrer.
     * @return   string                     Titre optimisé.
     */
    private function generate_template_title( $original_title, $keyword ) {
        $templates = array(
            '%keyword% : %title%',
            '%title% - %keyword%',
            '%keyword% | %title%',
            'Guide %keyword% : %title%',
            '%title% (%keyword%)',
        );

        $template = $templates[ array_rand( $templates ) ];
        $optimized = str_replace(
            array( '%keyword%', '%title%' ),
            array( ucfirst( $keyword ), $original_title ),
            $template
        );

        // Limiter à 60 caractères
        if ( mb_strlen( $optimized ) > 60 ) {
            $optimized = mb_substr( $optimized, 0, 57 ) . '...';
        }

        return $optimized;
    }

    /**
     * Optimise la meta description avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $content         Contenu du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Meta description optimisée ou false.
     */
    private function optimize_meta_description( $post_id, $content, $primary_keyword ) {
        $current_desc = get_post_meta( $post_id, '_boss_seo_meta_description', true );

        if ( ! empty( $current_desc ) && stripos( $current_desc, $primary_keyword ) !== false ) {
            return false; // Déjà optimisée
        }

        if ( empty( $primary_keyword ) ) {
            return false;
        }

        // Vérifier si l'IA est configurée et activée pour les descriptions
        if ( $this->settings->is_ai_configured() && $this->settings->get( 'ai', 'use_ai_for_descriptions', true ) ) {
            error_log( 'Boss SEO: Utilisation de l\'IA pour générer la meta description' );
            return $this->generate_ai_meta_description( $post_id, $content, $primary_keyword );
        }

        error_log( 'Boss SEO: IA non configurée, utilisation du fallback pour la meta description' );

        // Fallback : génération avec templates
        return $this->generate_template_meta_description( $content, $primary_keyword );
    }

    /**
     * Génère une meta description optimisée avec IA.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $content         Contenu du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Meta description optimisée ou false.
     */
    private function generate_ai_meta_description( $post_id, $content, $primary_keyword ) {
        // Récupérer le titre et un extrait du contenu
        $title = get_the_title( $post_id );
        $clean_content = wp_strip_all_tags( $content );
        $excerpt = wp_trim_words( $clean_content, 50 );

        // Construire le prompt pour l'IA
        $prompt = sprintf(
            'INSTRUCTIONS STRICTES: Réponds UNIQUEMENT avec la meta description optimisée. Aucun texte explicatif, aucun préambule.

Titre: "%s"
Mot-clé: "%s"
Contenu: "%s"

RÈGLES:
- 120-160 caractères maximum
- Inclure le mot-clé naturellement
- Inciter au clic
- Pas de guillemets
- Pas de préfixe comme "Meta description:" ou "Voici"

RÉPONSE ATTENDUE (exemple): Découvrez Hello World dans ce guide complet. Apprenez les bases et maîtrisez les concepts essentiels.

TA META DESCRIPTION:',
            $title,
            $primary_keyword,
            $excerpt
        );

        // Générer avec l'IA
        $ai_result = $this->ai->generate_content( $prompt, array(
            'temperature' => 0.8,
            'max_tokens' => 150
        ) );

        if ( $ai_result['success'] && ! empty( $ai_result['content'] ) ) {
            // Nettoyer AGRESSIVEMENT la réponse IA
            $optimized_desc = $this->clean_ai_response( $ai_result['content'] );

            // Extraire seulement la description (première ligne non vide)
            $lines = explode( "\n", $optimized_desc );
            foreach ( $lines as $line ) {
                $line = trim( $line );
                if ( ! empty( $line ) && ! $this->is_conversational_text( $line ) ) {
                    $optimized_desc = $line;
                    break;
                }
            }

            // Limiter à 160 caractères
            if ( mb_strlen( $optimized_desc ) > 160 ) {
                $optimized_desc = mb_substr( $optimized_desc, 0, 157 ) . '...';
            }

            return $optimized_desc;
        }

        // Fallback si l'IA échoue
        return $this->generate_template_meta_description( $content, $primary_keyword );
    }

    /**
     * Génère une meta description avec templates (fallback).
     *
     * @since    1.2.0
     * @param    string    $content         Contenu du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string                     Meta description optimisée.
     */
    private function generate_template_meta_description( $content, $primary_keyword ) {
        // Extraire un extrait du contenu
        $clean_content = wp_strip_all_tags( $content );
        $excerpt = wp_trim_words( $clean_content, 20 );

        // Générer une meta description optimisée
        $templates = array(
            'Découvrez tout sur %keyword%. %excerpt%',
            'Guide complet sur %keyword% : %excerpt%',
            '%excerpt% Apprenez-en plus sur %keyword%.',
            'Tout ce que vous devez savoir sur %keyword%. %excerpt%',
        );

        $template = $templates[ array_rand( $templates ) ];
        $optimized = str_replace(
            array( '%keyword%', '%excerpt%' ),
            array( $primary_keyword, $excerpt ),
            $template
        );

        // Limiter à 160 caractères
        if ( mb_strlen( $optimized ) > 160 ) {
            $optimized = mb_substr( $optimized, 0, 157 ) . '...';
        }

        return $optimized;
    }

    /**
     * Optimise le slug (URL).
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   string|false               Slug optimisé ou false.
     */
    private function optimize_slug( $post_id, $primary_keyword ) {
        if ( empty( $primary_keyword ) ) {
            return false;
        }

        $post = get_post( $post_id );
        $current_slug = $post->post_name;

        // Vérifier si le mot-clé est déjà dans le slug
        if ( strpos( $current_slug, sanitize_title( $primary_keyword ) ) !== false ) {
            return false; // Déjà optimisé
        }

        // Générer un slug optimisé
        $keyword_slug = sanitize_title( $primary_keyword );
        $optimized_slug = $keyword_slug . '-' . $current_slug;

        // Vérifier l'unicité
        $existing = get_page_by_path( $optimized_slug, OBJECT, $post->post_type );
        if ( $existing && $existing->ID !== $post_id ) {
            $optimized_slug = $keyword_slug . '-' . $current_slug . '-' . $post_id;
        }

        return $optimized_slug;
    }

    /**
     * Génère des suggestions pour le contenu.
     *
     * @since    1.2.0
     * @param    string    $content    Contenu actuel.
     * @param    array     $keywords   Mots-clés.
     * @return   array                 Suggestions de contenu.
     */
    private function generate_content_suggestions( $content, $keywords ) {
        $suggestions = array();

        if ( empty( $keywords ) ) {
            return $suggestions;
        }

        $primary_keyword = $keywords[0];
        $clean_content = wp_strip_all_tags( $content );
        $word_count = str_word_count( $clean_content );

        // Suggestions basées sur la longueur
        if ( $word_count < 300 ) {
            $suggestions[] = array(
                'type' => 'content_length',
                'text' => 'Développez votre contenu pour atteindre au moins 300 mots.',
                'action' => 'expand_content'
            );
        }

        // Suggestions pour les mots-clés
        $keyword_density = $word_count > 0 ? ( substr_count( strtolower( $clean_content ), strtolower( $primary_keyword ) ) / $word_count ) * 100 : 0;

        if ( $keyword_density < 0.5 ) {
            $suggestions[] = array(
                'type' => 'keyword_density',
                'text' => sprintf( 'Utilisez plus souvent le mot-clé "%s" dans votre contenu.', $primary_keyword ),
                'action' => 'increase_keyword_density'
            );
        }

        // Suggestions pour la structure
        if ( strpos( $content, '<h2' ) === false && $word_count > 500 ) {
            $suggestions[] = array(
                'type' => 'structure',
                'text' => 'Ajoutez des sous-titres H2 pour améliorer la structure.',
                'action' => 'add_headings'
            );
        }

        return $suggestions;
    }

    /**
     * Génère des suggestions pour les images.
     *
     * @since    1.2.0
     * @param    int       $post_id         ID du post.
     * @param    string    $primary_keyword Mot-clé principal.
     * @return   array                      Suggestions d'images.
     */
    private function generate_image_suggestions( $post_id, $primary_keyword ) {
        $suggestions = array();

        // Récupérer les images attachées
        $images = get_attached_media( 'image', $post_id );

        if ( empty( $images ) ) {
            $suggestions[] = array(
                'type' => 'add_images',
                'text' => 'Ajoutez des images pour enrichir votre contenu.',
                'action' => 'add_images'
            );
        } else {
            // Vérifier les attributs alt
            foreach ( $images as $image ) {
                $alt_text = get_post_meta( $image->ID, '_wp_attachment_image_alt', true );
                if ( empty( $alt_text ) ) {
                    $suggestions[] = array(
                        'type' => 'image_alt',
                        'text' => sprintf( 'Ajoutez un attribut alt à l\'image "%s".', $image->post_title ),
                        'action' => 'add_alt_text',
                        'image_id' => $image->ID
                    );
                } elseif ( ! empty( $primary_keyword ) && stripos( $alt_text, $primary_keyword ) === false ) {
                    $suggestions[] = array(
                        'type' => 'image_alt_keyword',
                        'text' => sprintf( 'Incluez le mot-clé "%s" dans l\'attribut alt de l\'image "%s".', $primary_keyword, $image->post_title ),
                        'action' => 'optimize_alt_text',
                        'image_id' => $image->ID
                    );
                }
            }
        }

        return $suggestions;
    }

    /**
     * Valide les métadonnées.
     *
     * @since    1.2.0
     * @param    array    $metadata    Métadonnées à valider.
     * @return   array                 Métadonnées validées.
     */
    public function validate_metadata_fields( $metadata ) {
        $validated = array();

        $fields = array(
            'boss_seo_title' => 'sanitize_text_field',
            'boss_seo_meta_description' => 'sanitize_textarea_field',
            'boss_seo_focus_keyword' => 'sanitize_text_field',
            'boss_seo_secondary_keywords' => 'sanitize_text_field',
            'boss_seo_canonical_url' => 'esc_url_raw',
            'boss_seo_robots_index' => array( $this, 'validate_robots_index' ),
            'boss_seo_robots_follow' => array( $this, 'validate_robots_follow' ),
            'boss_seo_og_title' => 'sanitize_text_field',
            'boss_seo_og_description' => 'sanitize_textarea_field',
            'boss_seo_og_image' => 'esc_url_raw',
            'boss_seo_twitter_card_type' => array( $this, 'validate_twitter_card_type' ),
            'boss_seo_twitter_title' => 'sanitize_text_field',
            'boss_seo_twitter_description' => 'sanitize_textarea_field',
            'boss_seo_twitter_image' => 'esc_url_raw',
        );

        foreach ( $fields as $field => $sanitizer ) {
            if ( isset( $metadata[ $field ] ) ) {
                if ( is_callable( $sanitizer ) ) {
                    $validated[ $field ] = call_user_func( $sanitizer, $metadata[ $field ] );
                } else {
                    $validated[ $field ] = $metadata[ $field ];
                }
            }
        }

        return $validated;
    }

    /**
     * Valide la directive robots index.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   string              Valeur validée.
     */
    public function validate_robots_index( $value ) {
        return in_array( $value, array( 'index', 'noindex' ) ) ? $value : 'index';
    }

    /**
     * Valide la directive robots follow.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   string              Valeur validée.
     */
    public function validate_robots_follow( $value ) {
        return in_array( $value, array( 'follow', 'nofollow' ) ) ? $value : 'follow';
    }

    /**
     * Valide le type de carte Twitter.
     *
     * @since    1.2.0
     * @param    string    $value    Valeur à valider.
     * @return   string              Valeur validée.
     */
    public function validate_twitter_card_type( $value ) {
        return in_array( $value, array( 'summary', 'summary_large_image' ) ) ? $value : 'summary_large_image';
    }

    /**
     * Valide un champ individuel.
     *
     * @since    1.2.0
     * @param    string    $field_name     Nom du champ.
     * @param    string    $field_value    Valeur du champ.
     * @return   array                     Résultat de la validation.
     */
    public function validate_single_field( $field_name, $field_value ) {
        $result = array(
            'valid' => true,
            'message' => '',
            'suggestions' => array()
        );

        switch ( $field_name ) {
            case 'boss_seo_title':
                $length = mb_strlen( $field_value );
                if ( $length > 60 ) {
                    $result['valid'] = false;
                    $result['message'] = 'Titre trop long (max 60 caractères)';
                } elseif ( $length < 30 && $length > 0 ) {
                    $result['message'] = 'Titre court, idéalement 50-60 caractères';
                }
                break;

            case 'boss_seo_meta_description':
                $length = mb_strlen( $field_value );
                if ( $length > 160 ) {
                    $result['valid'] = false;
                    $result['message'] = 'Description trop longue (max 160 caractères)';
                } elseif ( $length < 120 && $length > 0 ) {
                    $result['message'] = 'Description courte, idéalement 120-160 caractères';
                }
                break;

            case 'boss_seo_canonical_url':
                if ( ! empty( $field_value ) && ! filter_var( $field_value, FILTER_VALIDATE_URL ) ) {
                    $result['valid'] = false;
                    $result['message'] = 'URL invalide';
                }
                break;
        }

        return $result;
    }

    /**
     * Nettoie agressivement la réponse de l'IA.
     *
     * @since    1.2.0
     * @param    string    $response    Réponse brute de l'IA.
     * @return   string                 Réponse nettoyée.
     */
    private function clean_ai_response( $response ) {
        // Supprimer les guillemets et apostrophes
        $response = trim( $response, '"\'' );

        // Supprimer les balises markdown
        $response = preg_replace( '/```[a-z]*\n?/', '', $response );
        $response = preg_replace( '/```/', '', $response );

        // Supprimer les puces et numérotations
        $response = preg_replace( '/^[\d\-\*\+]\s*/', '', $response );

        // Supprimer les préfixes courants
        $prefixes_to_remove = array(
            'Titre SEO:',
            'Meta description:',
            'Titre optimisé:',
            'Description optimisée:',
            'Voici le titre:',
            'Voici la description:',
            'Titre:',
            'Description:',
        );

        foreach ( $prefixes_to_remove as $prefix ) {
            if ( stripos( $response, $prefix ) === 0 ) {
                $response = trim( substr( $response, strlen( $prefix ) ) );
            }
        }

        return trim( $response );
    }

    /**
     * Vérifie si le texte est conversationnel (à ignorer).
     *
     * @since    1.2.0
     * @param    string    $text    Texte à vérifier.
     * @return   bool               True si conversationnel.
     */
    private function is_conversational_text( $text ) {
        $conversational_patterns = array(
            '/^(bien sûr|voici|voilà|je vous propose|je suggère)/i',
            '/^(voici quelques|voici des|voici une|voici un)/i',
            '/^(pour optimiser|pour améliorer|pour le titre)/i',
            '/^(options pour|suggestions pour|idées pour)/i',
            '/^(vous pouvez|il est possible|il serait bien)/i',
            '/^(n\'hésitez pas|pensez à|assurez-vous)/i',
        );

        foreach ( $conversational_patterns as $pattern ) {
            if ( preg_match( $pattern, $text ) ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Génère des mots-clés à partir du contenu.
     *
     * @since    1.2.0
     * @param    int       $post_id    ID du post.
     * @param    string    $content    Contenu du post.
     * @return   array                 Mots-clés générés.
     */
    private function generate_keywords_from_content( $post_id, $content ) {
        $post = get_post( $post_id );
        $title = $post->post_title;
        $clean_content = wp_strip_all_tags( $content );

        // Extraire les mots importants du titre et du contenu
        $text = strtolower( $title . ' ' . $clean_content );
        $words = str_word_count( $text, 1 );

        // Filtrer les mots courts et les mots vides
        $stop_words = array( 'le', 'la', 'les', 'un', 'une', 'des', 'de', 'du', 'et', 'ou', 'mais', 'donc', 'car', 'ni', 'or', 'dans', 'sur', 'avec', 'sans', 'pour', 'par', 'vers', 'chez', 'que', 'qui', 'quoi', 'dont', 'où' );

        $keywords = array_filter( $words, function( $word ) use ( $stop_words ) {
            return strlen( $word ) > 3 && ! in_array( $word, $stop_words );
        } );

        // Compter les occurrences et prendre les plus fréquents
        $word_counts = array_count_values( $keywords );
        arsort( $word_counts );

        return array_slice( array_keys( $word_counts ), 0, 5 );
    }

    /**
     * Calcule un score SEO basé sur les optimisations.
     *
     * @since    1.2.0
     * @param    array    $optimizations    Optimisations effectuées.
     * @return   int                        Score SEO (0-100).
     */
    private function calculate_seo_score( $optimizations ) {
        $score = 50; // Score de base

        if ( ! empty( $optimizations['title'] ) ) {
            $score += 15;
        }

        if ( ! empty( $optimizations['meta_description'] ) ) {
            $score += 15;
        }

        if ( ! empty( $optimizations['focus_keyword'] ) ) {
            $score += 10;
        }

        if ( ! empty( $optimizations['secondary_keywords'] ) ) {
            $score += 10;
        }

        return min( 100, $score );
    }
}
