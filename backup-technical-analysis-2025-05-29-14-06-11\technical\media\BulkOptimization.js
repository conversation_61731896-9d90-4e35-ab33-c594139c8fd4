import { useState } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  CheckboxControl,
  SelectControl,
  RangeControl,
  ProgressBar,
  Notice
} from '@wordpress/components';

const BulkOptimization = ({ 
  stats, 
  onStartBulkOptimization, 
  isProcessing, 
  progress 
}) => {
  // États
  const [options, setOptions] = useState({
    mediaTypes: {
      jpeg: true,
      png: true,
      gif: true,
      webp: true,
      svg: false
    },
    compressionLevel: 'medium',
    convertToWebP: true,
    generateAltText: true,
    applyLazyLoading: true,
    skipOptimized: true,
    batchSize: 10
  });
  
  // Fonction pour mettre à jour les options
  const updateOption = (key, value) => {
    if (key.includes('.')) {
      const [parent, child] = key.split('.');
      setOptions({
        ...options,
        [parent]: {
          ...options[parent],
          [child]: value
        }
      });
    } else {
      setOptions({
        ...options,
        [key]: value
      });
    }
  };
  
  // Fonction pour démarrer l'optimisation par lot
  const startOptimization = () => {
    onStartBulkOptimization(options);
  };
  
  // Calculer le nombre d'images à optimiser
  const calculateImagesToOptimize = () => {
    let count = 0;
    
    if (options.mediaTypes.jpeg) count += stats.mediaTypes.jpeg;
    if (options.mediaTypes.png) count += stats.mediaTypes.png;
    if (options.mediaTypes.gif) count += stats.mediaTypes.gif;
    if (options.mediaTypes.webp) count += stats.mediaTypes.webp;
    if (options.mediaTypes.svg) count += stats.mediaTypes.svg;
    
    if (options.skipOptimized) {
      count -= stats.optimizedMedia;
    }
    
    return Math.max(0, count);
  };
  
  const imagesToOptimize = calculateImagesToOptimize();

  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Optimisation par lot', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        {isProcessing ? (
          <div className="boss-space-y-4">
            <div className="boss-text-center boss-mb-4">
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                {__('Optimisation en cours...', 'boss-seo')}
              </h3>
              <p className="boss-text-boss-gray boss-mb-4">
                {__('Veuillez ne pas quitter cette page pendant l\'optimisation.', 'boss-seo')}
              </p>
            </div>
            
            <ProgressBar
              percent={progress.percent}
              className="boss-mb-4"
            />
            
            <div className="boss-flex boss-justify-between boss-text-sm boss-text-boss-gray">
              <span>
                {progress.current} / {progress.total} {__('images', 'boss-seo')}
              </span>
              <span>
                {progress.percent}%
              </span>
            </div>
            
            {progress.currentImage && (
              <div className="boss-mt-4 boss-p-4 boss-bg-gray-50 boss-rounded-lg">
                <div className="boss-flex boss-items-center">
                  <div className="boss-w-12 boss-h-12 boss-bg-gray-100 boss-rounded boss-flex boss-items-center boss-justify-center boss-mr-4">
                    {progress.currentImage.thumbnail ? (
                      <img 
                        src={progress.currentImage.thumbnail} 
                        alt={progress.currentImage.name} 
                        className="boss-max-w-full boss-max-h-full boss-rounded"
                      />
                    ) : (
                      <span className="dashicons dashicons-format-image boss-text-gray-400"></span>
                    )}
                  </div>
                  <div>
                    <div className="boss-font-medium boss-text-boss-dark">{progress.currentImage.name}</div>
                    <div className="boss-text-sm boss-text-boss-gray">
                      {progress.currentImage.status === 'optimizing' 
                        ? __('Optimisation en cours...', 'boss-seo')
                        : progress.currentImage.status === 'optimized'
                          ? __('Optimisé avec succès', 'boss-seo')
                          : __('En attente', 'boss-seo')
                      }
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
            <div>
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                {__('Types de médias à optimiser', 'boss-seo')}
              </h3>
              
              <div className="boss-space-y-3">
                <CheckboxControl
                  label={`JPEG (${stats.mediaTypes.jpeg})`}
                  checked={options.mediaTypes.jpeg}
                  onChange={(checked) => updateOption('mediaTypes.jpeg', checked)}
                />
                
                <CheckboxControl
                  label={`PNG (${stats.mediaTypes.png})`}
                  checked={options.mediaTypes.png}
                  onChange={(checked) => updateOption('mediaTypes.png', checked)}
                />
                
                <CheckboxControl
                  label={`GIF (${stats.mediaTypes.gif})`}
                  checked={options.mediaTypes.gif}
                  onChange={(checked) => updateOption('mediaTypes.gif', checked)}
                />
                
                <CheckboxControl
                  label={`WebP (${stats.mediaTypes.webp})`}
                  checked={options.mediaTypes.webp}
                  onChange={(checked) => updateOption('mediaTypes.webp', checked)}
                />
                
                <CheckboxControl
                  label={`SVG (${stats.mediaTypes.svg})`}
                  checked={options.mediaTypes.svg}
                  onChange={(checked) => updateOption('mediaTypes.svg', checked)}
                />
              </div>
              
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mt-6 boss-mb-3">
                {__('Options d\'optimisation', 'boss-seo')}
              </h3>
              
              <div className="boss-space-y-4">
                <SelectControl
                  label={__('Niveau de compression', 'boss-seo')}
                  value={options.compressionLevel}
                  options={[
                    { label: __('Faible (meilleure qualité)', 'boss-seo'), value: 'low' },
                    { label: __('Moyen (équilibré)', 'boss-seo'), value: 'medium' },
                    { label: __('Élevé (taille minimale)', 'boss-seo'), value: 'high' }
                  ]}
                  onChange={(value) => updateOption('compressionLevel', value)}
                />
                
                <CheckboxControl
                  label={__('Convertir en WebP', 'boss-seo')}
                  help={__('Créer des versions WebP des images (format moderne plus léger)', 'boss-seo')}
                  checked={options.convertToWebP}
                  onChange={(checked) => updateOption('convertToWebP', checked)}
                />
              </div>
            </div>
            
            <div>
              <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
                {__('Options supplémentaires', 'boss-seo')}
              </h3>
              
              <div className="boss-space-y-4">
                <CheckboxControl
                  label={__('Générer le texte alternatif', 'boss-seo')}
                  help={__('Utiliser l\'IA pour générer du texte alternatif pour les images sans alt text', 'boss-seo')}
                  checked={options.generateAltText}
                  onChange={(checked) => updateOption('generateAltText', checked)}
                />
                
                <CheckboxControl
                  label={__('Appliquer le lazy loading', 'boss-seo')}
                  help={__('Ajouter l\'attribut loading="lazy" aux images', 'boss-seo')}
                  checked={options.applyLazyLoading}
                  onChange={(checked) => updateOption('applyLazyLoading', checked)}
                />
                
                <CheckboxControl
                  label={__('Ignorer les images déjà optimisées', 'boss-seo')}
                  help={__('Ne pas retraiter les images déjà optimisées', 'boss-seo')}
                  checked={options.skipOptimized}
                  onChange={(checked) => updateOption('skipOptimized', checked)}
                />
                
                <RangeControl
                  label={__('Taille du lot', 'boss-seo')}
                  value={options.batchSize}
                  onChange={(value) => updateOption('batchSize', value)}
                  min={5}
                  max={50}
                  step={5}
                  help={__('Nombre d\'images à traiter simultanément', 'boss-seo')}
                />
              </div>
              
              <div className="boss-mt-6 boss-p-4 boss-bg-blue-50 boss-rounded-lg">
                <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-2">
                  {__('Résumé', 'boss-seo')}
                </h3>
                
                <div className="boss-space-y-2 boss-text-sm">
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-boss-gray">{__('Images à optimiser:', 'boss-seo')}</span>
                    <span className="boss-font-medium boss-text-boss-dark">{imagesToOptimize}</span>
                  </div>
                  
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-boss-gray">{__('Économie d\'espace estimée:', 'boss-seo')}</span>
                    <span className="boss-font-medium boss-text-boss-dark">
                      {(imagesToOptimize * 0.5).toFixed(1)} MB
                    </span>
                  </div>
                  
                  <div className="boss-flex boss-justify-between">
                    <span className="boss-text-boss-gray">{__('Temps estimé:', 'boss-seo')}</span>
                    <span className="boss-font-medium boss-text-boss-dark">
                      {Math.ceil(imagesToOptimize / options.batchSize)} {__('minutes', 'boss-seo')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {!isProcessing && imagesToOptimize === 0 && (
          <Notice status="warning" isDismissible={false} className="boss-mt-6">
            <p>
              {__('Aucune image à optimiser avec les paramètres actuels. Veuillez modifier vos sélections.', 'boss-seo')}
            </p>
          </Notice>
        )}
      </CardBody>
      <CardFooter className="boss-border-t boss-border-gray-200">
        <div className="boss-flex boss-justify-end">
          {isProcessing ? (
            <Button
              isDestructive
              onClick={() => onStartBulkOptimization(null)}
            >
              {__('Annuler l\'optimisation', 'boss-seo')}
            </Button>
          ) : (
            <Button
              isPrimary
              onClick={startOptimization}
              disabled={imagesToOptimize === 0}
            >
              {__('Démarrer l\'optimisation', 'boss-seo')}
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default BulkOptimization;
