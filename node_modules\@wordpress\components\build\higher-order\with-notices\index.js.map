{"version": 3, "names": ["_uuid", "require", "_element", "_compose", "_list", "_interopRequireDefault", "_default", "createHigherOrderComponent", "OriginalComponent", "Component", "props", "ref", "noticeList", "setNoticeList", "useState", "noticeOperations", "useMemo", "createNotice", "notice", "noticeToAdd", "id", "uuid", "current", "createErrorNotice", "msg", "status", "content", "removeNotice", "filter", "removeAllNotices", "propsOut", "noticeUI", "length", "_react", "createElement", "default", "className", "notices", "onRemove", "isForwardRef", "render", "forwardRef", "exports"], "sources": ["@wordpress/components/src/higher-order/with-notices/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { v4 as uuid } from 'uuid';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef, useState, useMemo } from '@wordpress/element';\nimport { createHigherOrderComponent } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport NoticeList from '../../notice/list';\nimport type { WithNoticeProps } from './types';\n\n/**\n * Override the default edit UI to include notices if supported.\n *\n * Wrapping the original component with `withNotices` encapsulates the component\n * with the additional props `noticeOperations` and `noticeUI`.\n *\n * ```jsx\n * import { withNotices, Button } from '@wordpress/components';\n *\n * const MyComponentWithNotices = withNotices(\n * \t( { noticeOperations, noticeUI } ) => {\n * \t\tconst addError = () =>\n * \t\t\tnoticeOperations.createErrorNotice( 'Error message' );\n * \t\treturn (\n * \t\t\t<div>\n * \t\t\t\t{ noticeUI }\n * \t\t\t\t<Button variant=\"secondary\" onClick={ addError }>\n * \t\t\t\t\tAdd error\n * \t\t\t\t</Button>\n * \t\t\t</div>\n * \t\t);\n * \t}\n * );\n * ```\n *\n * @param OriginalComponent Original component.\n *\n * @return Wrapped component.\n */\nexport default createHigherOrderComponent( ( OriginalComponent ) => {\n\tfunction Component(\n\t\tprops: { [ key: string ]: any },\n\t\tref: React.ForwardedRef< any >\n\t) {\n\t\tconst [ noticeList, setNoticeList ] = useState<\n\t\t\tWithNoticeProps[ 'noticeList' ]\n\t\t>( [] );\n\n\t\tconst noticeOperations = useMemo<\n\t\t\tWithNoticeProps[ 'noticeOperations' ]\n\t\t>( () => {\n\t\t\tconst createNotice: WithNoticeProps[ 'noticeOperations' ][ 'createNotice' ] =\n\t\t\t\t( notice ) => {\n\t\t\t\t\tconst noticeToAdd = notice.id\n\t\t\t\t\t\t? notice\n\t\t\t\t\t\t: { ...notice, id: uuid() };\n\t\t\t\t\tsetNoticeList( ( current ) => [ ...current, noticeToAdd ] );\n\t\t\t\t};\n\n\t\t\treturn {\n\t\t\t\tcreateNotice,\n\t\t\t\tcreateErrorNotice: ( msg ) => {\n\t\t\t\t\t// @ts-expect-error TODO: Missing `id`, potentially a bug\n\t\t\t\t\tcreateNotice( {\n\t\t\t\t\t\tstatus: 'error',\n\t\t\t\t\t\tcontent: msg,\n\t\t\t\t\t} );\n\t\t\t\t},\n\t\t\t\tremoveNotice: ( id ) => {\n\t\t\t\t\tsetNoticeList( ( current ) =>\n\t\t\t\t\t\tcurrent.filter( ( notice ) => notice.id !== id )\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\tremoveAllNotices: () => {\n\t\t\t\t\tsetNoticeList( [] );\n\t\t\t\t},\n\t\t\t};\n\t\t}, [] );\n\n\t\tconst propsOut = {\n\t\t\t...props,\n\t\t\tnoticeList,\n\t\t\tnoticeOperations,\n\t\t\tnoticeUI: noticeList.length > 0 && (\n\t\t\t\t<NoticeList\n\t\t\t\t\tclassName=\"components-with-notices-ui\"\n\t\t\t\t\tnotices={ noticeList }\n\t\t\t\t\tonRemove={ noticeOperations.removeNotice }\n\t\t\t\t/>\n\t\t\t),\n\t\t};\n\n\t\treturn isForwardRef ? (\n\t\t\t<OriginalComponent { ...propsOut } ref={ ref } />\n\t\t) : (\n\t\t\t<OriginalComponent { ...propsOut } />\n\t\t);\n\t}\n\n\tlet isForwardRef: boolean;\n\t// @ts-expect-error - `render` will only be present when OriginalComponent was wrapped with forwardRef().\n\tconst { render } = OriginalComponent;\n\t// Returns a forwardRef if OriginalComponent appears to be a forwardRef.\n\tif ( typeof render === 'function' ) {\n\t\tisForwardRef = true;\n\t\treturn forwardRef( Component );\n\t}\n\treturn Component;\n}, 'withNotices' );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,KAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAdA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA5BA,IAAAK,QAAA,GA6Be,IAAAC,mCAA0B,EAAIC,iBAAiB,IAAM;EACnE,SAASC,SAASA,CACjBC,KAA+B,EAC/BC,GAA8B,EAC7B;IACD,MAAM,CAAEC,UAAU,EAAEC,aAAa,CAAE,GAAG,IAAAC,iBAAQ,EAE3C,EAAG,CAAC;IAEP,MAAMC,gBAAgB,GAAG,IAAAC,gBAAO,EAE7B,MAAM;MACR,MAAMC,YAAqE,GACxEC,MAAM,IAAM;QACb,MAAMC,WAAW,GAAGD,MAAM,CAACE,EAAE,GAC1BF,MAAM,GACN;UAAE,GAAGA,MAAM;UAAEE,EAAE,EAAE,IAAAC,QAAI,EAAC;QAAE,CAAC;QAC5BR,aAAa,CAAIS,OAAO,IAAM,CAAE,GAAGA,OAAO,EAAEH,WAAW,CAAG,CAAC;MAC5D,CAAC;MAEF,OAAO;QACNF,YAAY;QACZM,iBAAiB,EAAIC,GAAG,IAAM;UAC7B;UACAP,YAAY,CAAE;YACbQ,MAAM,EAAE,OAAO;YACfC,OAAO,EAAEF;UACV,CAAE,CAAC;QACJ,CAAC;QACDG,YAAY,EAAIP,EAAE,IAAM;UACvBP,aAAa,CAAIS,OAAO,IACvBA,OAAO,CAACM,MAAM,CAAIV,MAAM,IAAMA,MAAM,CAACE,EAAE,KAAKA,EAAG,CAChD,CAAC;QACF,CAAC;QACDS,gBAAgB,EAAEA,CAAA,KAAM;UACvBhB,aAAa,CAAE,EAAG,CAAC;QACpB;MACD,CAAC;IACF,CAAC,EAAE,EAAG,CAAC;IAEP,MAAMiB,QAAQ,GAAG;MAChB,GAAGpB,KAAK;MACRE,UAAU;MACVG,gBAAgB;MAChBgB,QAAQ,EAAEnB,UAAU,CAACoB,MAAM,GAAG,CAAC,IAC9B,IAAAC,MAAA,CAAAC,aAAA,EAAC9B,KAAA,CAAA+B,OAAU;QACVC,SAAS,EAAC,4BAA4B;QACtCC,OAAO,EAAGzB,UAAY;QACtB0B,QAAQ,EAAGvB,gBAAgB,CAACY;MAAc,CAC1C;IAEH,CAAC;IAED,OAAOY,YAAY,GAClB,IAAAN,MAAA,CAAAC,aAAA,EAAC1B,iBAAiB;MAAA,GAAMsB,QAAQ;MAAGnB,GAAG,EAAGA;IAAK,CAAE,CAAC,GAEjD,IAAAsB,MAAA,CAAAC,aAAA,EAAC1B,iBAAiB;MAAA,GAAMsB;IAAQ,CAAI,CACpC;EACF;EAEA,IAAIS,YAAqB;EACzB;EACA,MAAM;IAAEC;EAAO,CAAC,GAAGhC,iBAAiB;EACpC;EACA,IAAK,OAAOgC,MAAM,KAAK,UAAU,EAAG;IACnCD,YAAY,GAAG,IAAI;IACnB,OAAO,IAAAE,mBAAU,EAAEhC,SAAU,CAAC;EAC/B;EACA,OAAOA,SAAS;AACjB,CAAC,EAAE,aAAc,CAAC;AAAAiC,OAAA,CAAAP,OAAA,GAAA7B,QAAA"}