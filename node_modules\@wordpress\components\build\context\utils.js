"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getConnectedNamespace = getConnectedNamespace;
exports.getNamespace = getNamespace;
var _constants = require("./constants");
/**
 * Internal dependencies
 */

/**
 * Creates a dedicated context namespace HTML attribute for components.
 * ns is short for "namespace"
 *
 * @example
 * ```jsx
 * <div {...ns('Container')} />
 * ```
 *
 * @param {string} componentName The name for the component.
 * @return {Record<string, any>} A props object with the namespaced HTML attribute.
 */
function getNamespace(componentName) {
  return {
    [_constants.COMPONENT_NAMESPACE]: componentName
  };
}

/**
 * Creates a dedicated connected context namespace HTML attribute for components.
 * ns is short for "namespace"
 *
 * @example
 * ```jsx
 * <div {...cns()} />
 * ```
 *
 * @return {Record<string, any>} A props object with the namespaced HTML attribute.
 */
function getConnectedNamespace() {
  return {
    [_constants.CONNECTED_NAMESPACE]: true
  };
}
//# sourceMappingURL=utils.js.map