{"version": 3, "names": ["_primitives", "require", "chevronBack", "_react", "createElement", "SVG", "width", "height", "viewBox", "xmlns", "Path", "d", "_default", "exports", "default"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/chevron-back.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { SVG, Path } from '@wordpress/primitives';\n\n// Used for the back button of the iOS Bottom Sheet\nconst chevronBack = (\n\t<SVG\n\t\twidth=\"12\"\n\t\theight=\"21\"\n\t\tviewBox=\"0 0 12 21\"\n\t\txmlns=\"http://www.w3.org/2000/SVG\"\n\t>\n\t\t<Path d=\"M9.62586 20.5975C9.89618 20.8579 10.2253 21 10.6014 21C11.3888 21 12 20.3844 12 19.6032C12 19.2125 11.8472 18.8574 11.5769 18.5851L3.34966 10.4882L11.5769 2.41488C11.8472 2.14262 12 1.77565 12 1.39684C12 0.615558 11.3888 0 10.6014 0C10.2253 0 9.89618 0.142052 9.63761 0.40248L0.493634 9.3991C0.164545 9.70688 0 10.0857 0 10.5C0 10.9143 0.164545 11.2694 0.48188 11.5891L9.62586 20.5975Z\" />\n\t</SVG>\n);\n\nexport default chevronBack;\n"], "mappings": ";;;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGA;AACA,MAAMC,WAAW,GAChB,IAAAC,MAAA,CAAAC,aAAA,EAACJ,WAAA,CAAAK,GAAG;EACHC,KAAK,EAAC,IAAI;EACVC,MAAM,EAAC,IAAI;EACXC,OAAO,EAAC,WAAW;EACnBC,KAAK,EAAC;AAA4B,GAElC,IAAAN,MAAA,CAAAC,aAAA,EAACJ,WAAA,CAAAU,IAAI;EAACC,CAAC,EAAC;AAAmY,CAAE,CACzY,CACL;AAAC,IAAAC,QAAA,GAEaV,WAAW;AAAAW,OAAA,CAAAC,OAAA,GAAAF,QAAA"}