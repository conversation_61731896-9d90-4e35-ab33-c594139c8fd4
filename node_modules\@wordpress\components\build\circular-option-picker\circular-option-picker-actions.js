"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ButtonAction = ButtonAction;
exports.DropdownLinkAction = DropdownLinkAction;
var _react = require("react");
var _classnames = _interopRequireDefault(require("classnames"));
var _button = _interopRequireDefault(require("../button"));
var _dropdown = _interopRequireDefault(require("../dropdown"));
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function DropdownLinkAction({
  buttonProps,
  className,
  dropdownProps,
  linkText
}) {
  return (0, _react.createElement)(_dropdown.default, {
    className: (0, _classnames.default)('components-circular-option-picker__dropdown-link-action', className),
    renderToggle: ({
      isOpen,
      onToggle
    }) => (0, _react.createElement)(_button.default, {
      "aria-expanded": isOpen,
      "aria-haspopup": "true",
      onClick: onToggle,
      variant: "link",
      ...buttonProps
    }, linkText),
    ...dropdownProps
  });
}
function ButtonAction({
  className,
  children,
  ...additionalProps
}) {
  return (0, _react.createElement)(_button.default, {
    className: (0, _classnames.default)('components-circular-option-picker__clear', className),
    variant: "tertiary",
    ...additionalProps
  }, children);
}
//# sourceMappingURL=circular-option-picker-actions.js.map