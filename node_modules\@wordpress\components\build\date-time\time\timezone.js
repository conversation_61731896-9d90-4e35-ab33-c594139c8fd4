"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _date = require("@wordpress/date");
var _tooltip = _interopRequireDefault(require("../../tooltip"));
var _styles = require("./styles");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

/**
 * Displays timezone information when user timezone is different from site
 * timezone.
 */
const TimeZone = () => {
  const {
    timezone
  } = (0, _date.getSettings)();

  // Convert timezone offset to hours.
  const userTimezoneOffset = -1 * (new Date().getTimezoneOffset() / 60);

  // System timezone and user timezone match, nothing needed.
  // Compare as numbers because it comes over as string.
  if (Number(timezone.offset) === userTimezoneOffset) {
    return null;
  }
  const offsetSymbol = Number(timezone.offset) >= 0 ? '+' : '';
  const zoneAbbr = '' !== timezone.abbr && isNaN(Number(timezone.abbr)) ? timezone.abbr : `UTC${offsetSymbol}${timezone.offset}`;

  // Replace underscore with space in strings like `America/Costa_Rica`.
  const prettyTimezoneString = timezone.string.replace('_', ' ');
  const timezoneDetail = 'UTC' === timezone.string ? (0, _i18n.__)('Coordinated Universal Time') : `(${zoneAbbr}) ${prettyTimezoneString}`;

  // When the prettyTimezoneString is empty, there is no additional timezone
  // detail information to show in a Tooltip.
  const hasNoAdditionalTimezoneDetail = prettyTimezoneString.trim().length === 0;
  return hasNoAdditionalTimezoneDetail ? (0, _react.createElement)(_styles.TimeZone, {
    className: "components-datetime__timezone"
  }, zoneAbbr) : (0, _react.createElement)(_tooltip.default, {
    placement: "top",
    text: timezoneDetail
  }, (0, _react.createElement)(_styles.TimeZone, {
    className: "components-datetime__timezone"
  }, zoneAbbr));
};
var _default = TimeZone;
exports.default = _default;
//# sourceMappingURL=timezone.js.map