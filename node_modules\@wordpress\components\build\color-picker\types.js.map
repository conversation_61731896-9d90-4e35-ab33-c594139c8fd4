{"version": 3, "names": [], "sources": ["@wordpress/components/src/color-picker/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type {\n\tColord,\n\tHslColor,\n\tHsvaColor,\n\tHsvColor,\n\tRgbaColor,\n\tRgbColor,\n} from 'colord';\nimport type { HslaColor } from 'react-colorful';\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport type { useDeprecatedProps } from './use-deprecated-props';\n\nexport type ColorType = 'rgb' | 'hsl' | 'hex';\nexport type ColorCopyButtonProps = {\n\tcolor: Colord;\n\tcolorType: ColorType;\n};\n\nexport type LegacyAdapterProps = Parameters< typeof useDeprecatedProps >[ 0 ];\n\nexport type ColorPickerProps = WordPressComponentProps<\n\t{\n\t\t/**\n\t\t * When `true` the color picker will display the alpha channel both in\n\t\t * the bottom inputs as well as in the color picker itself.\n\t\t *\n\t\t * @default false\n\t\t */\n\t\tenableAlpha?: boolean;\n\t\t/**\n\t\t * The current color value to display in the picker.\n\t\t * Must be a hex or hex8 string.\n\t\t */\n\t\tcolor?: string;\n\t\t/**\n\t\t * Fired when the color changes. Always passes a hex or hex8 color string.\n\t\t */\n\t\tonChange?: ( color: string ) => void;\n\t\t/**\n\t\t * An optional default value to use for the color picker.\n\t\t */\n\t\tdefaultValue?: string;\n\t\t/**\n\t\t * The format to copy when clicking the displayed color format.\n\t\t */\n\t\tcopyFormat?: ColorType;\n\t},\n\t'div',\n\tfalse\n>;\n\nexport interface PickerProps {\n\tcolor: Colord;\n\tenableAlpha: boolean;\n\tonChange: ( nextColor: Colord ) => void;\n\tcontainerEl: HTMLElement | null;\n\tonDragStart?: ( event: MouseEvent ) => void;\n\tonDragEnd?: ( event: MouseEvent ) => void;\n}\n\nexport interface ColorInputProps {\n\tcolorType: 'hsl' | 'hex' | 'rgb';\n\tcolor: Colord;\n\tonChange: ( nextColor: Colord ) => void;\n\tenableAlpha: boolean;\n}\n\nexport interface InputWithSliderProps {\n\tmin: number;\n\tmax: number;\n\tvalue: number;\n\tlabel: string;\n\tabbreviation: string;\n\tonChange: ( value: number ) => void;\n}\n\nexport interface HexInputProps {\n\tcolor: Colord;\n\tonChange: ( nextColor: Colord ) => void;\n\tenableAlpha: boolean;\n}\n\nexport interface HslInputProps {\n\tcolor: Colord;\n\tonChange: ( nextColor: Colord ) => void;\n\tenableAlpha: boolean;\n}\n\nexport interface RgbInputProps {\n\tcolor: Colord;\n\tonChange: ( nextColor: Colord ) => void;\n\tenableAlpha: boolean;\n}\n\n/**\n * @deprecated\n */\nexport type LegacyColor =\n\t| string\n\t| {\n\t\t\thex: string;\n\t\t\thsl: HslColor | HslaColor;\n\t\t\thsv: HsvColor | HsvaColor;\n\t\t\trgb: RgbColor | RgbaColor;\n\t\t\t/**\n\t\t\t * @deprecated\n\t\t\t */\n\t\t\toldHue: number;\n\t\t\t/**\n\t\t\t * @deprecated\n\t\t\t */\n\t\t\tsource: 'hex';\n\t  };\n\n/**\n * @deprecated\n */\nexport interface LegacyProps {\n\tcolor?: LegacyColor;\n\t/**\n\t * @deprecated\n\t */\n\tonChangeComplete: ( colors: LegacyColor ) => void;\n\t/**\n\t * @deprecated\n\t */\n\toldHue: string;\n\tclassName: string;\n\t/**\n\t * @deprecated\n\t */\n\tdisableAlpha: boolean;\n\tonChange?: never;\n}\n"], "mappings": ""}