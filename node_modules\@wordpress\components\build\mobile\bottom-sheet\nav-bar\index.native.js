"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _applyButton = _interopRequireDefault(require("./apply-button"));
var _backButton = _interopRequireDefault(require("./back-button"));
var _heading = _interopRequireDefault(require("./heading"));
var _styles = _interopRequireDefault(require("./styles.scss"));
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

function NavBar({
  children
}) {
  return (0, _react.createElement)(_reactNative.View, {
    style: _styles.default['nav-bar']
  }, children);
}
NavBar.ApplyButton = _applyButton.default;
NavBar.BackButton = _backButton.default.Back;
NavBar.DismissButton = _backButton.default.Dismiss;
NavBar.Heading = _heading.default;
var _default = NavBar;
exports.default = _default;
//# sourceMappingURL=index.native.js.map