{"version": 3, "names": ["_context", "require", "_view", "_hook", "UnconnectedHeading", "props", "forwardedRef", "headerProps", "useHeading", "_react", "createElement", "View", "ref", "Heading", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/heading/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../context';\nimport { contextConnect } from '../context';\nimport { View } from '../view';\nimport { useHeading } from './hook';\nimport type { HeadingProps } from './types';\n\nfunction UnconnectedHeading(\n\tprops: WordPressComponentProps< HeadingProps, 'h1' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst headerProps = useHeading( props );\n\n\treturn <View { ...headerProps } ref={ forwardedRef } />;\n}\n\n/**\n * `Heading` renders headings and titles using the library's typography system.\n *\n * ```jsx\n * import { __experimentalHeading as Heading } from \"@wordpress/components\";\n *\n * function Example() {\n *   return <Heading>Code is Poetry</Heading>;\n * }\n * ```\n */\nexport const Heading = contextConnect( UnconnectedHeading, 'Heading' );\n\nexport default Heading;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,kBAAkBA,CAC1BC,KAAoD,EACpDC,YAAiC,EAChC;EACD,MAAMC,WAAW,GAAG,IAAAC,gBAAU,EAAEH,KAAM,CAAC;EAEvC,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,WAAW;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,OAAO,GAAG,IAAAC,uBAAc,EAAEV,kBAAkB,EAAE,SAAU,CAAC;AAACW,OAAA,CAAAF,OAAA,GAAAA,OAAA;AAAA,IAAAG,QAAA,GAExDH,OAAO;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}