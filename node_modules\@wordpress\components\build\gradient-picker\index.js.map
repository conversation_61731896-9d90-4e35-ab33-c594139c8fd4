{"version": 3, "names": ["_i18n", "require", "_compose", "_element", "_deprecated", "_interopRequireDefault", "_circularOptionPicker", "_customGradientPicker", "_vStack", "_styles", "_spacer", "isMultipleOriginObject", "obj", "Array", "isArray", "gradients", "isMultipleOriginArray", "arr", "length", "every", "gradientObj", "SingleOrigin", "className", "clearGradient", "onChange", "value", "additionalProps", "gradientOptions", "useMemo", "map", "gradient", "name", "slug", "index", "_react", "createElement", "default", "Option", "key", "isSelected", "tooltipText", "sprintf", "__", "style", "color", "background", "onClick", "OptionGroup", "options", "Multiple<PERSON>rigin", "headingLevel", "instanceId", "useInstanceId", "VStack", "spacing", "gradientSet", "id", "ColorHeading", "level", "Component", "props", "asButtons", "loop", "actions", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metaProps", "_metaProps", "GradientPicker", "__nextHasNoMargin", "clearable", "disableCustomGradients", "__experimentalIsRenderedInSidebar", "useCallback", "undefined", "deprecated", "since", "version", "hint", "deprecatedMarginSpacerProps", "marginTop", "marginBottom", "Spacer", "ButtonAction", "_default", "exports"], "sources": ["@wordpress/components/src/gradient-picker/index.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __, sprintf } from '@wordpress/i18n';\nimport { useInstanceId } from '@wordpress/compose';\nimport { useCallback, useMemo } from '@wordpress/element';\nimport deprecated from '@wordpress/deprecated';\n\n/**\n * Internal dependencies\n */\nimport CircularOptionPicker from '../circular-option-picker';\nimport CustomGradientPicker from '../custom-gradient-picker';\nimport { VStack } from '../v-stack';\nimport { ColorHeading } from '../color-palette/styles';\nimport { Spacer } from '../spacer';\nimport type {\n\tGradientPickerComponentProps,\n\tPickerProps,\n\tOriginObject,\n\tGradientObject,\n} from './types';\n\n// The Multiple Origin Gradients have a `gradients` property (an array of\n// gradient objects), while Single Origin ones have a `gradient` property.\nconst isMultipleOriginObject = (\n\tobj: Record< string, any >\n): obj is OriginObject =>\n\tArray.isArray( obj.gradients ) && ! ( 'gradient' in obj );\n\nconst isMultipleOriginArray = ( arr: any[] ): arr is OriginObject[] => {\n\treturn (\n\t\tarr.length > 0 &&\n\t\tarr.every( ( gradientObj ) => isMultipleOriginObject( gradientObj ) )\n\t);\n};\n\nfunction SingleOrigin( {\n\tclassName,\n\tclearGradient,\n\tgradients,\n\tonChange,\n\tvalue,\n\t...additionalProps\n}: PickerProps< GradientObject > ) {\n\tconst gradientOptions = useMemo( () => {\n\t\treturn gradients.map( ( { gradient, name, slug }, index ) => (\n\t\t\t<CircularOptionPicker.Option\n\t\t\t\tkey={ slug }\n\t\t\t\tvalue={ gradient }\n\t\t\t\tisSelected={ value === gradient }\n\t\t\t\ttooltipText={\n\t\t\t\t\tname ||\n\t\t\t\t\t// translators: %s: gradient code e.g: \"linear-gradient(90deg, rgba(98,16,153,1) 0%, rgba(172,110,22,1) 100%);\".\n\t\t\t\t\tsprintf( __( 'Gradient code: %s' ), gradient )\n\t\t\t\t}\n\t\t\t\tstyle={ { color: 'rgba( 0,0,0,0 )', background: gradient } }\n\t\t\t\tonClick={\n\t\t\t\t\tvalue === gradient\n\t\t\t\t\t\t? clearGradient\n\t\t\t\t\t\t: () => onChange( gradient, index )\n\t\t\t\t}\n\t\t\t\taria-label={\n\t\t\t\t\tname\n\t\t\t\t\t\t? // translators: %s: The name of the gradient e.g: \"Angular red to blue\".\n\t\t\t\t\t\t  sprintf( __( 'Gradient: %s' ), name )\n\t\t\t\t\t\t: // translators: %s: gradient code e.g: \"linear-gradient(90deg, rgba(98,16,153,1) 0%, rgba(172,110,22,1) 100%);\".\n\t\t\t\t\t\t  sprintf( __( 'Gradient code: %s' ), gradient )\n\t\t\t\t}\n\t\t\t/>\n\t\t) );\n\t}, [ gradients, value, onChange, clearGradient ] );\n\treturn (\n\t\t<CircularOptionPicker.OptionGroup\n\t\t\tclassName={ className }\n\t\t\toptions={ gradientOptions }\n\t\t\t{ ...additionalProps }\n\t\t/>\n\t);\n}\n\nfunction MultipleOrigin( {\n\tclassName,\n\tclearGradient,\n\tgradients,\n\tonChange,\n\tvalue,\n\theadingLevel,\n}: PickerProps< OriginObject > ) {\n\tconst instanceId = useInstanceId( MultipleOrigin );\n\n\treturn (\n\t\t<VStack spacing={ 3 } className={ className }>\n\t\t\t{ gradients.map( ( { name, gradients: gradientSet }, index ) => {\n\t\t\t\tconst id = `color-palette-${ instanceId }-${ index }`;\n\t\t\t\treturn (\n\t\t\t\t\t<VStack spacing={ 2 } key={ index }>\n\t\t\t\t\t\t<ColorHeading level={ headingLevel } id={ id }>\n\t\t\t\t\t\t\t{ name }\n\t\t\t\t\t\t</ColorHeading>\n\t\t\t\t\t\t<SingleOrigin\n\t\t\t\t\t\t\tclearGradient={ clearGradient }\n\t\t\t\t\t\t\tgradients={ gradientSet }\n\t\t\t\t\t\t\tonChange={ ( gradient ) =>\n\t\t\t\t\t\t\t\tonChange( gradient, index )\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\t\taria-labelledby={ id }\n\t\t\t\t\t\t/>\n\t\t\t\t\t</VStack>\n\t\t\t\t);\n\t\t\t} ) }\n\t\t</VStack>\n\t);\n}\n\nfunction Component( props: PickerProps< any > ) {\n\tconst {\n\t\tasButtons,\n\t\tloop,\n\t\tactions,\n\t\theadingLevel,\n\t\t'aria-label': ariaLabel,\n\t\t'aria-labelledby': ariaLabelledby,\n\t\t...additionalProps\n\t} = props;\n\tconst options = isMultipleOriginArray( props.gradients ) ? (\n\t\t<MultipleOrigin headingLevel={ headingLevel } { ...additionalProps } />\n\t) : (\n\t\t<SingleOrigin { ...additionalProps } />\n\t);\n\n\tlet metaProps:\n\t\t| { asButtons: false; loop?: boolean; 'aria-label': string }\n\t\t| { asButtons: false; loop?: boolean; 'aria-labelledby': string }\n\t\t| { asButtons: true };\n\n\tif ( asButtons ) {\n\t\tmetaProps = { asButtons: true };\n\t} else {\n\t\tconst _metaProps: { asButtons: false; loop?: boolean } = {\n\t\t\tasButtons: false,\n\t\t\tloop,\n\t\t};\n\n\t\tif ( ariaLabel ) {\n\t\t\tmetaProps = { ..._metaProps, 'aria-label': ariaLabel };\n\t\t} else if ( ariaLabelledby ) {\n\t\t\tmetaProps = {\n\t\t\t\t..._metaProps,\n\t\t\t\t'aria-labelledby': ariaLabelledby,\n\t\t\t};\n\t\t} else {\n\t\t\tmetaProps = {\n\t\t\t\t..._metaProps,\n\t\t\t\t'aria-label': __( 'Custom color picker.' ),\n\t\t\t};\n\t\t}\n\t}\n\n\treturn (\n\t\t<CircularOptionPicker\n\t\t\t{ ...metaProps }\n\t\t\tactions={ actions }\n\t\t\toptions={ options }\n\t\t/>\n\t);\n}\n\n/**\n *  GradientPicker is a React component that renders a color gradient picker to\n * define a multi step gradient. There's either a _linear_ or a _radial_ type\n * available.\n *\n * ```jsx\n *import { GradientPicker } from '@wordpress/components';\n *import { useState } from '@wordpress/element';\n *\n *const myGradientPicker = () => {\n *\tconst [ gradient, setGradient ] = useState( null );\n *\n *\treturn (\n *\t\t<GradientPicker\n *\t\t\t__nextHasNoMargin\n *\t\t\tvalue={ gradient }\n *\t\t\tonChange={ ( currentGradient ) => setGradient( currentGradient ) }\n *\t\t\tgradients={ [\n *\t\t\t\t{\n *\t\t\t\t\tname: 'JShine',\n *\t\t\t\t\tgradient:\n *\t\t\t\t\t\t'linear-gradient(135deg,#12c2e9 0%,#c471ed 50%,#f64f59 100%)',\n *\t\t\t\t\tslug: 'jshine',\n *\t\t\t\t},\n *\t\t\t\t{\n *\t\t\t\t\tname: 'Moonlit Asteroid',\n *\t\t\t\t\tgradient:\n *\t\t\t\t\t\t'linear-gradient(135deg,#0F2027 0%, #203A43 0%, #2c5364 100%)',\n *\t\t\t\t\tslug: 'moonlit-asteroid',\n *\t\t\t\t},\n *\t\t\t\t{\n *\t\t\t\t\tname: 'Rastafarie',\n *\t\t\t\t\tgradient:\n *\t\t\t\t\t\t'linear-gradient(135deg,#1E9600 0%, #FFF200 0%, #FF0000 100%)',\n *\t\t\t\t\tslug: 'rastafari',\n *\t\t\t\t},\n *\t\t\t] }\n *\t\t/>\n *\t);\n *};\n *```\n *\n */\nexport function GradientPicker( {\n\t/** Start opting into the new margin-free styles that will become the default in a future version. */\n\t__nextHasNoMargin = false,\n\tclassName,\n\tgradients = [],\n\tonChange,\n\tvalue,\n\tclearable = true,\n\tdisableCustomGradients = false,\n\t__experimentalIsRenderedInSidebar,\n\theadingLevel = 2,\n\t...additionalProps\n}: GradientPickerComponentProps ) {\n\tconst clearGradient = useCallback(\n\t\t() => onChange( undefined ),\n\t\t[ onChange ]\n\t);\n\n\tif ( ! __nextHasNoMargin ) {\n\t\tdeprecated( 'Outer margin styles for wp.components.GradientPicker', {\n\t\t\tsince: '6.1',\n\t\t\tversion: '6.4',\n\t\t\thint: 'Set the `__nextHasNoMargin` prop to true to start opting into the new styles, which will become the default in a future version',\n\t\t} );\n\t}\n\n\tconst deprecatedMarginSpacerProps = ! __nextHasNoMargin\n\t\t? {\n\t\t\t\tmarginTop: ! gradients.length ? 3 : undefined,\n\t\t\t\tmarginBottom: ! clearable ? 6 : 0,\n\t\t  }\n\t\t: {};\n\n\treturn (\n\t\t// Outmost Spacer wrapper can be removed when deprecation period is over\n\t\t<Spacer marginBottom={ 0 } { ...deprecatedMarginSpacerProps }>\n\t\t\t<VStack spacing={ gradients.length ? 4 : 0 }>\n\t\t\t\t{ ! disableCustomGradients && (\n\t\t\t\t\t<CustomGradientPicker\n\t\t\t\t\t\t__nextHasNoMargin\n\t\t\t\t\t\t__experimentalIsRenderedInSidebar={\n\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t\t{ ( gradients.length > 0 || clearable ) && (\n\t\t\t\t\t<Component\n\t\t\t\t\t\t{ ...additionalProps }\n\t\t\t\t\t\tclassName={ className }\n\t\t\t\t\t\tclearGradient={ clearGradient }\n\t\t\t\t\t\tgradients={ gradients }\n\t\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\t\tvalue={ value }\n\t\t\t\t\t\tactions={\n\t\t\t\t\t\t\tclearable &&\n\t\t\t\t\t\t\t! disableCustomGradients && (\n\t\t\t\t\t\t\t\t<CircularOptionPicker.ButtonAction\n\t\t\t\t\t\t\t\t\tonClick={ clearGradient }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ __( 'Clear' ) }\n\t\t\t\t\t\t\t\t</CircularOptionPicker.ButtonAction>\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t}\n\t\t\t\t\t\theadingLevel={ headingLevel }\n\t\t\t\t\t/>\n\t\t\t\t) }\n\t\t\t</VStack>\n\t\t</Spacer>\n\t);\n}\n\nexport default GradientPicker;\n"], "mappings": ";;;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAKA,IAAAK,qBAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,qBAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AACA,IAAAQ,OAAA,GAAAR,OAAA;AACA,IAAAS,OAAA,GAAAT,OAAA;AAfA;AACA;AACA;;AAMA;AACA;AACA;;AAaA;AACA;AACA,MAAMU,sBAAsB,GAC3BC,GAA0B,IAE1BC,KAAK,CAACC,OAAO,CAAEF,GAAG,CAACG,SAAU,CAAC,IAAI,EAAI,UAAU,IAAIH,GAAG,CAAE;AAE1D,MAAMI,qBAAqB,GAAKC,GAAU,IAA6B;EACtE,OACCA,GAAG,CAACC,MAAM,GAAG,CAAC,IACdD,GAAG,CAACE,KAAK,CAAIC,WAAW,IAAMT,sBAAsB,CAAES,WAAY,CAAE,CAAC;AAEvE,CAAC;AAED,SAASC,YAAYA,CAAE;EACtBC,SAAS;EACTC,aAAa;EACbR,SAAS;EACTS,QAAQ;EACRC,KAAK;EACL,GAAGC;AAC2B,CAAC,EAAG;EAClC,MAAMC,eAAe,GAAG,IAAAC,gBAAO,EAAE,MAAM;IACtC,OAAOb,SAAS,CAACc,GAAG,CAAE,CAAE;MAAEC,QAAQ;MAAEC,IAAI;MAAEC;IAAK,CAAC,EAAEC,KAAK,KACtD,IAAAC,MAAA,CAAAC,aAAA,EAAC7B,qBAAA,CAAA8B,OAAoB,CAACC,MAAM;MAC3BC,GAAG,EAAGN,IAAM;MACZP,KAAK,EAAGK,QAAU;MAClBS,UAAU,EAAGd,KAAK,KAAKK,QAAU;MACjCU,WAAW,EACVT,IAAI;MACJ;MACA,IAAAU,aAAO,EAAE,IAAAC,QAAE,EAAE,mBAAoB,CAAC,EAAEZ,QAAS,CAC7C;MACDa,KAAK,EAAG;QAAEC,KAAK,EAAE,iBAAiB;QAAEC,UAAU,EAAEf;MAAS,CAAG;MAC5DgB,OAAO,EACNrB,KAAK,KAAKK,QAAQ,GACfP,aAAa,GACb,MAAMC,QAAQ,CAAEM,QAAQ,EAAEG,KAAM,CACnC;MACD,cACCF,IAAI;MACD;MACA,IAAAU,aAAO,EAAE,IAAAC,QAAE,EAAE,cAAe,CAAC,EAAEX,IAAK,CAAC;MACrC;MACA,IAAAU,aAAO,EAAE,IAAAC,QAAE,EAAE,mBAAoB,CAAC,EAAEZ,QAAS;IAChD,CACD,CACA,CAAC;EACJ,CAAC,EAAE,CAAEf,SAAS,EAAEU,KAAK,EAAED,QAAQ,EAAED,aAAa,CAAG,CAAC;EAClD,OACC,IAAAW,MAAA,CAAAC,aAAA,EAAC7B,qBAAA,CAAA8B,OAAoB,CAACW,WAAW;IAChCzB,SAAS,EAAGA,SAAW;IACvB0B,OAAO,EAAGrB,eAAiB;IAAA,GACtBD;EAAe,CACpB,CAAC;AAEJ;AAEA,SAASuB,cAAcA,CAAE;EACxB3B,SAAS;EACTC,aAAa;EACbR,SAAS;EACTS,QAAQ;EACRC,KAAK;EACLyB;AAC4B,CAAC,EAAG;EAChC,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEH,cAAe,CAAC;EAElD,OACC,IAAAf,MAAA,CAAAC,aAAA,EAAC3B,OAAA,CAAA6C,MAAM;IAACC,OAAO,EAAG,CAAG;IAAChC,SAAS,EAAGA;EAAW,GAC1CP,SAAS,CAACc,GAAG,CAAE,CAAE;IAAEE,IAAI;IAAEhB,SAAS,EAAEwC;EAAY,CAAC,EAAEtB,KAAK,KAAM;IAC/D,MAAMuB,EAAE,GAAI,iBAAiBL,UAAY,IAAIlB,KAAO,EAAC;IACrD,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC3B,OAAA,CAAA6C,MAAM;MAACC,OAAO,EAAG,CAAG;MAAChB,GAAG,EAAGL;IAAO,GAClC,IAAAC,MAAA,CAAAC,aAAA,EAAC1B,OAAA,CAAAgD,YAAY;MAACC,KAAK,EAAGR,YAAc;MAACM,EAAE,EAAGA;IAAI,GAC3CzB,IACW,CAAC,EACf,IAAAG,MAAA,CAAAC,aAAA,EAACd,YAAY;MACZE,aAAa,EAAGA,aAAe;MAC/BR,SAAS,EAAGwC,WAAa;MACzB/B,QAAQ,EAAKM,QAAQ,IACpBN,QAAQ,CAAEM,QAAQ,EAAEG,KAAM,CAC1B;MACDR,KAAK,EAAGA,KAAO;MACf,mBAAkB+B;IAAI,CACtB,CACM,CAAC;EAEX,CAAE,CACK,CAAC;AAEX;AAEA,SAASG,SAASA,CAAEC,KAAyB,EAAG;EAC/C,MAAM;IACLC,SAAS;IACTC,IAAI;IACJC,OAAO;IACPb,YAAY;IACZ,YAAY,EAAEc,SAAS;IACvB,iBAAiB,EAAEC,cAAc;IACjC,GAAGvC;EACJ,CAAC,GAAGkC,KAAK;EACT,MAAMZ,OAAO,GAAGhC,qBAAqB,CAAE4C,KAAK,CAAC7C,SAAU,CAAC,GACvD,IAAAmB,MAAA,CAAAC,aAAA,EAACc,cAAc;IAACC,YAAY,EAAGA,YAAc;IAAA,GAAMxB;EAAe,CAAI,CAAC,GAEvE,IAAAQ,MAAA,CAAAC,aAAA,EAACd,YAAY;IAAA,GAAMK;EAAe,CAAI,CACtC;EAED,IAAIwC,SAGkB;EAEtB,IAAKL,SAAS,EAAG;IAChBK,SAAS,GAAG;MAAEL,SAAS,EAAE;IAAK,CAAC;EAChC,CAAC,MAAM;IACN,MAAMM,UAAgD,GAAG;MACxDN,SAAS,EAAE,KAAK;MAChBC;IACD,CAAC;IAED,IAAKE,SAAS,EAAG;MAChBE,SAAS,GAAG;QAAE,GAAGC,UAAU;QAAE,YAAY,EAAEH;MAAU,CAAC;IACvD,CAAC,MAAM,IAAKC,cAAc,EAAG;MAC5BC,SAAS,GAAG;QACX,GAAGC,UAAU;QACb,iBAAiB,EAAEF;MACpB,CAAC;IACF,CAAC,MAAM;MACNC,SAAS,GAAG;QACX,GAAGC,UAAU;QACb,YAAY,EAAE,IAAAzB,QAAE,EAAE,sBAAuB;MAC1C,CAAC;IACF;EACD;EAEA,OACC,IAAAR,MAAA,CAAAC,aAAA,EAAC7B,qBAAA,CAAA8B,OAAoB;IAAA,GACf8B,SAAS;IACdH,OAAO,EAAGA,OAAS;IACnBf,OAAO,EAAGA;EAAS,CACnB,CAAC;AAEJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASoB,cAAcA,CAAE;EAC/B;EACAC,iBAAiB,GAAG,KAAK;EACzB/C,SAAS;EACTP,SAAS,GAAG,EAAE;EACdS,QAAQ;EACRC,KAAK;EACL6C,SAAS,GAAG,IAAI;EAChBC,sBAAsB,GAAG,KAAK;EAC9BC,iCAAiC;EACjCtB,YAAY,GAAG,CAAC;EAChB,GAAGxB;AAC0B,CAAC,EAAG;EACjC,MAAMH,aAAa,GAAG,IAAAkD,oBAAW,EAChC,MAAMjD,QAAQ,CAAEkD,SAAU,CAAC,EAC3B,CAAElD,QAAQ,CACX,CAAC;EAED,IAAK,CAAE6C,iBAAiB,EAAG;IAC1B,IAAAM,mBAAU,EAAE,sDAAsD,EAAE;MACnEC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE;IACP,CAAE,CAAC;EACJ;EAEA,MAAMC,2BAA2B,GAAG,CAAEV,iBAAiB,GACpD;IACAW,SAAS,EAAE,CAAEjE,SAAS,CAACG,MAAM,GAAG,CAAC,GAAGwD,SAAS;IAC7CO,YAAY,EAAE,CAAEX,SAAS,GAAG,CAAC,GAAG;EAChC,CAAC,GACD,CAAC,CAAC;EAEL;IACC;IACA,IAAApC,MAAA,CAAAC,aAAA,EAACzB,OAAA,CAAAwE,MAAM;MAACD,YAAY,EAAG,CAAG;MAAA,GAAMF;IAA2B,GAC1D,IAAA7C,MAAA,CAAAC,aAAA,EAAC3B,OAAA,CAAA6C,MAAM;MAACC,OAAO,EAAGvC,SAAS,CAACG,MAAM,GAAG,CAAC,GAAG;IAAG,GACzC,CAAEqD,sBAAsB,IACzB,IAAArC,MAAA,CAAAC,aAAA,EAAC5B,qBAAA,CAAA6B,OAAoB;MACpBiC,iBAAiB;MACjBG,iCAAiC,EAChCA,iCACA;MACD/C,KAAK,EAAGA,KAAO;MACfD,QAAQ,EAAGA;IAAU,CACrB,CACD,EACC,CAAET,SAAS,CAACG,MAAM,GAAG,CAAC,IAAIoD,SAAS,KACpC,IAAApC,MAAA,CAAAC,aAAA,EAACwB,SAAS;MAAA,GACJjC,eAAe;MACpBJ,SAAS,EAAGA,SAAW;MACvBC,aAAa,EAAGA,aAAe;MAC/BR,SAAS,EAAGA,SAAW;MACvBS,QAAQ,EAAGA,QAAU;MACrBC,KAAK,EAAGA,KAAO;MACfsC,OAAO,EACNO,SAAS,IACT,CAAEC,sBAAsB,IACvB,IAAArC,MAAA,CAAAC,aAAA,EAAC7B,qBAAA,CAAA8B,OAAoB,CAAC+C,YAAY;QACjCrC,OAAO,EAAGvB;MAAe,GAEvB,IAAAmB,QAAE,EAAE,OAAQ,CACoB,CAEpC;MACDQ,YAAY,EAAGA;IAAc,CAC7B,CAEK,CACD;EAAC;AAEX;AAAC,IAAAkC,QAAA,GAEchB,cAAc;AAAAiB,OAAA,CAAAjD,OAAA,GAAAgD,QAAA"}