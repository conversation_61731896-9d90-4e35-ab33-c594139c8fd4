"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CONNECT_STATIC_NAMESPACE = exports.CONNECTED_NAMESPACE = exports.COMPONENT_NAMESPACE = void 0;
const COMPONENT_NAMESPACE = 'data-wp-component';
exports.COMPONENT_NAMESPACE = COMPONENT_NAMESPACE;
const CONNECTED_NAMESPACE = 'data-wp-c16t';

/**
 * Special key where the connected namespaces are stored.
 * This is attached to Context connected components as a static property.
 */
exports.CONNECTED_NAMESPACE = CONNECTED_NAMESPACE;
const CONNECT_STATIC_NAMESPACE = '__contextSystemKey__';
exports.CONNECT_STATIC_NAMESPACE = CONNECT_STATIC_NAMESPACE;
//# sourceMappingURL=constants.js.map