import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  ToggleControl,
  SelectControl,
  CheckboxControl,
  RangeControl,
  TextControl,
  TabPanel,
  Notice,
  Spinner,
  Dashicon,
  Flex,
  FlexItem,
  FlexBlock,
  Modal,
  __experimentalNumberControl as NumberControl
} from '@wordpress/components';

// Importer le service
import RobotsSitemapService from '../../services/RobotsSitemapService';

// Importer les styles
import './AdvancedSitemapConfigPro.css';

/**
 * Composant Dashboard de Statut
 */
const SitemapDashboard = ({ sitemapSettings, onRegenerate, onPing, isLoading }) => {
  const [stats, setStats] = useState({
    totalUrls: 0,
    indexedUrls: 0,
    pendingUrls: 0,
    errorUrls: 0,
    lastUpdate: 'Chargement...',
    indexationRate: 0
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Charger les statistiques au montage du composant
  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    setIsLoadingStats(true);
    try {
      const response = await RobotsSitemapService.getDashboardStats();
      if (response.success && response.stats) {
        setStats({
          totalUrls: response.stats.totalUrls || 0,
          indexedUrls: response.stats.indexedUrls || 0,
          pendingUrls: response.stats.pendingUrls || 0,
          errorUrls: response.stats.errorUrls || 0,
          lastUpdate: response.stats.lastUpdate || 'Jamais',
          indexationRate: response.stats.indexationRate || 0
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const handleRefreshStats = async () => {
    try {
      const response = await RobotsSitemapService.refreshDashboardStats();
      if (response.success && response.stats) {
        setStats({
          totalUrls: response.stats.totalUrls || 0,
          indexedUrls: response.stats.indexedUrls || 0,
          pendingUrls: response.stats.pendingUrls || 0,
          errorUrls: response.stats.errorUrls || 0,
          lastUpdate: response.stats.lastUpdate || 'Jamais',
          indexationRate: response.stats.indexationRate || 0
        });
      }
    } catch (error) {
      console.error('Erreur lors de l\'actualisation des statistiques:', error);
    }
  };

  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-bg-gradient-to-r boss-from-blue-50 boss-to-indigo-50 boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <div className="boss-flex boss-items-center">
            <Dashicon icon="chart-bar" className="boss-text-blue-600 boss-mr-2" />
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('📊 Statut des Sitemaps', 'boss-seo')}
            </h2>
          </div>
          <div className="boss-flex boss-space-x-2">
            <Button
              isPrimary
              onClick={onRegenerate}
              isBusy={isLoading}
              disabled={isLoading}
              className="boss-bg-blue-600 hover:boss-bg-blue-700"
            >
              <Dashicon icon="update" className="boss-mr-1" />
              {__('🔄 Régénérer Tout', 'boss-seo')}
            </Button>
            <Button
              isSecondary
              onClick={onPing}
              disabled={isLoading}
              className="boss-border-blue-600 boss-text-blue-600 hover:boss-bg-blue-50"
            >
              <Dashicon icon="share" className="boss-mr-1" />
              {__('📤 Ping Moteurs', 'boss-seo')}
            </Button>
            <Button
              isSecondary
              onClick={handleRefreshStats}
              disabled={isLoadingStats}
              className="boss-border-green-600 boss-text-green-600 hover:boss-bg-green-50"
            >
              <Dashicon icon="update" className="boss-mr-1" />
              {__('📊 Actualiser', 'boss-seo')}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4 boss-mb-4">
          <div className="boss-bg-green-50 boss-p-4 boss-rounded-lg boss-border boss-border-green-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-green-600 boss-font-medium">✅ Sitemap Principal</p>
                <p className="boss-text-xs boss-text-green-500">📅 Dernière MAJ: {stats.lastUpdate}</p>
              </div>
              <Dashicon icon="yes-alt" className="boss-text-green-600 boss-text-xl" />
            </div>
          </div>
          
          <div className="boss-bg-blue-50 boss-p-4 boss-rounded-lg boss-border boss-border-blue-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-blue-600 boss-font-medium">📈 URLs Totales</p>
                <p className="boss-text-lg boss-font-bold boss-text-blue-700">{stats.totalUrls.toLocaleString()}</p>
              </div>
              <Dashicon icon="admin-links" className="boss-text-blue-600 boss-text-xl" />
            </div>
          </div>

          <div className="boss-bg-yellow-50 boss-p-4 boss-rounded-lg boss-border boss-border-yellow-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-yellow-600 boss-font-medium">⏳ En attente</p>
                <p className="boss-text-lg boss-font-bold boss-text-yellow-700">{stats.pendingUrls}</p>
              </div>
              <Dashicon icon="clock" className="boss-text-yellow-600 boss-text-xl" />
            </div>
          </div>

          <div className="boss-bg-red-50 boss-p-4 boss-rounded-lg boss-border boss-border-red-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-red-600 boss-font-medium">❌ Erreurs</p>
                <p className="boss-text-lg boss-font-bold boss-text-red-700">{stats.errorUrls}</p>
              </div>
              <Dashicon icon="warning" className="boss-text-red-600 boss-text-xl" />
            </div>
          </div>
        </div>

        <div className="boss-bg-gradient-to-r boss-from-green-50 boss-to-blue-50 boss-p-4 boss-rounded-lg boss-border boss-border-gray-200">
          <div className="boss-flex boss-items-center boss-justify-between">
            <div>
              <p className="boss-text-sm boss-text-gray-600">📊 Taux d'indexation Google</p>
              <div className="boss-flex boss-items-center boss-mt-1">
                <div className="boss-w-32 boss-bg-gray-200 boss-rounded-full boss-h-2 boss-mr-3">
                  <div 
                    className="boss-bg-green-600 boss-h-2 boss-rounded-full" 
                    style={{ width: `${stats.indexationRate}%` }}
                  ></div>
                </div>
                <span className="boss-text-lg boss-font-bold boss-text-green-600">{stats.indexationRate}%</span>
              </div>
            </div>
            <div className="boss-text-right">
              <p className="boss-text-sm boss-text-gray-600">✅ Indexées: {stats.indexedUrls.toLocaleString()}</p>
              <p className="boss-text-xs boss-text-gray-500">📊 Sur {stats.totalUrls.toLocaleString()} soumises</p>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

/**
 * Composant Configuration Globale
 */
const GlobalConfiguration = ({ sitemapSettings, setSitemapSettings }) => {
  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-bg-gray-50 boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-items-center">
          <Dashicon icon="admin-settings" className="boss-text-gray-600 boss-mr-2" />
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('⚙️ Configuration Globale', 'boss-seo')}
          </h3>
        </div>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
          <div className="boss-space-y-4">
            <ToggleControl
              label={__('🔘 Activer les sitemaps XML', 'boss-seo')}
              checked={sitemapSettings.enabled}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, enabled: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
            
            <ToggleControl
              label={__('🔘 Mise à jour automatique', 'boss-seo')}
              checked={sitemapSettings.enableAutoUpdate}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableAutoUpdate: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
            
            <ToggleControl
              label={__('🔘 Ping automatique des moteurs de recherche', 'boss-seo')}
              checked={sitemapSettings.autoPing || false}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, autoPing: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
          </div>

          <div className="boss-space-y-4">
            <ToggleControl
              label={__('🔘 Inclure les images dans les sitemaps', 'boss-seo')}
              checked={sitemapSettings.includeImages}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeImages: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
            
            <ToggleControl
              label={__('🔘 Inclure les dates de modification', 'boss-seo')}
              checked={sitemapSettings.includeLastMod}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeLastMod: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
          </div>
        </div>

        {sitemapSettings.enableAutoUpdate && (
          <div className="boss-mt-6 boss-p-4 boss-bg-blue-50 boss-rounded-lg boss-border boss-border-blue-200">
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
              <SelectControl
                label={__('Fréquence de mise à jour', 'boss-seo')}
                value={sitemapSettings.autoUpdateFrequency || 'daily'}
                options={[
                  { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                  { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                  { label: __('Mensuelle', 'boss-seo'), value: 'monthly' }
                ]}
                onChange={(value) => setSitemapSettings({ ...sitemapSettings, autoUpdateFrequency: value })}
              />
              
              <NumberControl
                label={__('Limite d\'URLs par sitemap', 'boss-seo')}
                value={sitemapSettings.maxUrlsPerSitemap || 50000}
                onChange={(value) => setSitemapSettings({ ...sitemapSettings, maxUrlsPerSitemap: value })}
                min={1000}
                max={50000}
                step={1000}
              />
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

/**
 * Composant Gestion des Types de Contenu
 */
const ContentTypesManager = ({ sitemapSettings, setSitemapSettings, contentTypes, taxonomies }) => {
  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-blue-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('📄 Gestion par Type de Contenu', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-4">
            {contentTypes.map((type) => (
              <Card key={type.name} className="boss-border boss-border-gray-200 boss-shadow-sm">
                <CardHeader className="boss-bg-gradient-to-r boss-from-gray-50 boss-to-blue-50 boss-border-b boss-border-gray-200">
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <h4 className="boss-text-sm boss-font-semibold boss-text-boss-dark">
                      {type.label}
                    </h4>
                    <div className="boss-flex boss-items-center">
                      <span className="boss-text-xs boss-bg-blue-100 boss-text-blue-800 boss-px-2 boss-py-1 boss-rounded-full">
                        {type.name === 'post' ? '1,156 URLs' :
                         type.name === 'page' ? '89 URLs' :
                         type.name === 'product' ? '234 URLs' : '0 URLs'}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-3">
                    <ToggleControl
                      label={__('✅ Inclure dans le sitemap', 'boss-seo')}
                      checked={sitemapSettings.enablePostTypeSitemaps?.[type.name] || false}
                      onChange={(value) => {
                        const enablePostTypeSitemaps = { ...(sitemapSettings.enablePostTypeSitemaps || {}) };
                        enablePostTypeSitemaps[type.name] = value;
                        setSitemapSettings({ ...sitemapSettings, enablePostTypeSitemaps });
                      }}
                    />

                    {sitemapSettings.enablePostTypeSitemaps?.[type.name] && (
                      <>
                        <div className="boss-grid boss-grid-cols-2 boss-gap-2">
                          <SelectControl
                            label={__('Priorité', 'boss-seo')}
                            value={sitemapSettings.contentTypePriorities?.[type.name] || '0.7'}
                            options={[
                              { label: '0.9', value: '0.9' },
                              { label: '0.8', value: '0.8' },
                              { label: '0.7', value: '0.7' },
                              { label: '0.6', value: '0.6' },
                              { label: '0.5', value: '0.5' }
                            ]}
                            onChange={(value) => {
                              const contentTypePriorities = { ...(sitemapSettings.contentTypePriorities || {}) };
                              contentTypePriorities[type.name] = value;
                              setSitemapSettings({ ...sitemapSettings, contentTypePriorities });
                            }}
                          />

                          <SelectControl
                            label={__('Fréquence', 'boss-seo')}
                            value={sitemapSettings.contentTypeFrequencies?.[type.name] || 'weekly'}
                            options={[
                              { label: __('Daily', 'boss-seo'), value: 'daily' },
                              { label: __('Weekly', 'boss-seo'), value: 'weekly' },
                              { label: __('Monthly', 'boss-seo'), value: 'monthly' }
                            ]}
                            onChange={(value) => {
                              const contentTypeFrequencies = { ...(sitemapSettings.contentTypeFrequencies || {}) };
                              contentTypeFrequencies[type.name] = value;
                              setSitemapSettings({ ...sitemapSettings, contentTypeFrequencies });
                            }}
                          />
                        </div>

                        <div className="boss-bg-green-50 boss-p-3 boss-rounded boss-border boss-border-green-200">
                          <p className="boss-text-xs boss-text-green-700 boss-mb-2">🎯 Règles Intelligentes:</p>
                          <ul className="boss-text-xs boss-text-green-600 boss-space-y-1">
                            <li>• Contenu récent (&lt; 30j): Priorité +0.2</li>
                            <li>• Contenu populaire: Priorité +0.1</li>
                            <li>• Contenu archivé (&gt; 1an): Priorité -0.2</li>
                          </ul>
                        </div>

                        <a
                          href={`${window.location.origin}/sitemap-${type.name}.xml`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="boss-text-xs boss-text-blue-600 boss-font-medium boss-underline boss-flex boss-items-center"
                        >
                          <Dashicon icon="external" className="boss-mr-1" />
                          {__('🌐 Voir le sitemap', 'boss-seo')}
                        </a>
                      </>
                    )}
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant Sitemaps Spécialisés
 */
const SpecializedSitemaps = ({ sitemapSettings, setSitemapSettings }) => {
  const [specializedStats, setSpecializedStats] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRegenerating, setIsRegenerating] = useState({});

  // Charger les statistiques au montage du composant
  useEffect(() => {
    loadSpecializedStats();
  }, []);

  const loadSpecializedStats = async () => {
    setIsLoading(true);
    try {
      const response = await RobotsSitemapService.getSpecializedStats();
      if (response.success && response.stats) {
        setSpecializedStats(response.stats);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des stats spécialisés:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegenerateSitemap = async (type) => {
    setIsRegenerating(prev => ({ ...prev, [type]: true }));
    try {
      const response = await RobotsSitemapService.regenerateSpecializedSitemap(type);
      if (response.success) {
        await loadSpecializedStats(); // Recharger les stats
      }
    } catch (error) {
      console.error('Erreur lors de la régénération:', error);
    } finally {
      setIsRegenerating(prev => ({ ...prev, [type]: false }));
    }
  };

  const handleRefreshStats = async () => {
    try {
      const response = await RobotsSitemapService.refreshSpecializedStats();
      if (response.success && response.stats) {
        setSpecializedStats(response.stats);
      }
    } catch (error) {
      console.error('Erreur lors de l\'actualisation:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="boss-text-center boss-py-8">
        <Spinner />
        <p className="boss-text-gray-500 boss-mt-4">
          {__('Chargement des sitemaps spécialisés...', 'boss-seo')}
        </p>
      </div>
    );
  }

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-purple-50 boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
              {__('🎯 Sitemaps Spécialisés', 'boss-seo')}
            </h3>
            <Button
              isSecondary
              onClick={handleRefreshStats}
              className="boss-border-purple-600 boss-text-purple-600 hover:boss-bg-purple-50"
            >
              <Dashicon icon="update" className="boss-mr-1" />
              {__('🔄 Actualiser', 'boss-seo')}
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
            {specializedStats.map((type) => (
              <Card key={type.type} className={`boss-border boss-border-${type.color}-200 boss-shadow-sm`}>
                <CardHeader className={`boss-bg-${type.color}-50 boss-border-b boss-border-${type.color}-200`}>
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <h4 className="boss-text-sm boss-font-semibold boss-text-boss-dark">
                      {type.icon} {type.name}
                    </h4>
                    <div className="boss-flex boss-items-center boss-space-x-2">
                      <span className={`boss-text-xs boss-bg-${type.color}-100 boss-text-${type.color}-800 boss-px-2 boss-py-1 boss-rounded-full`}>
                        📊 {type.count} {type.type === 'image' ? 'images' : type.type === 'video' ? 'vidéos' : type.type === 'news' ? 'articles' : 'stories'}
                      </span>
                      {type.status === 'active' && (
                        <span className="boss-text-xs boss-bg-green-100 boss-text-green-800 boss-px-2 boss-py-1 boss-rounded-full">
                          ✅ Actif
                        </span>
                      )}
                      {type.status === 'error' && (
                        <span className="boss-text-xs boss-bg-red-100 boss-text-red-800 boss-px-2 boss-py-1 boss-rounded-full">
                          ❌ Erreur
                        </span>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-3">
                    <ToggleControl
                      label={__('✅ Activer', 'boss-seo')}
                      checked={type.enabled}
                      onChange={(value) => {
                        // Mettre à jour les paramètres locaux
                        const settingKey = type.type === 'image' ? 'enableImageSitemap' :
                                         type.type === 'video' ? 'enableVideoSitemap' :
                                         type.type === 'news' ? 'enableNewsSitemap' :
                                         'enableStoriesSitemap';
                        setSitemapSettings({ ...sitemapSettings, [settingKey]: value });
                      }}
                    />

                    <p className="boss-text-xs boss-text-gray-600">
                      {type.description}
                    </p>

                    {type.enabled && (
                      <div className={`boss-bg-${type.color}-50 boss-p-3 boss-rounded boss-border boss-border-${type.color}-200`}>
                        <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
                          <a
                            href={type.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`boss-text-xs boss-text-${type.color}-600 boss-font-medium boss-underline boss-flex boss-items-center`}
                          >
                            <Dashicon icon="external" className="boss-mr-1" />
                            {__('🌐 Voir le sitemap', 'boss-seo')}
                          </a>
                          {type.size_formatted && (
                            <span className="boss-text-xs boss-text-gray-500">
                              📦 {type.size_formatted}
                            </span>
                          )}
                        </div>
                        <div className="boss-flex boss-space-x-2">
                          <Button
                            isSecondary
                            size="small"
                            onClick={() => handleRegenerateSitemap(type.type)}
                            isBusy={isRegenerating[type.type]}
                            disabled={isRegenerating[type.type]}
                          >
                            <Dashicon icon="update" className="boss-mr-1" />
                            {__('🔄 Régénérer', 'boss-seo')}
                          </Button>
                          <Button
                            isSecondary
                            size="small"
                            onClick={() => {/* Configuration avancée */}}
                          >
                            <Dashicon icon="admin-settings" className="boss-mr-1" />
                            {__('⚙️ Configurer', 'boss-seo')}
                          </Button>
                        </div>
                        {type.last_generated && (
                          <p className="boss-text-xs boss-text-gray-500 boss-mt-2">
                            📅 Dernière génération: {new Date(type.last_generated).toLocaleString()}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant Optimisation Intelligente
 */
const SmartOptimization = ({ sitemapSettings, setSitemapSettings }) => {
  const [priorities, setPriorities] = useState([]);
  const [algorithms, setAlgorithms] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [isCalculating, setIsCalculating] = useState(false);

  useEffect(() => {
    loadSmartOptimizationData();
  }, []);

  const loadSmartOptimizationData = async () => {
    setIsLoading(true);
    try {
      const [prioritiesResponse, algorithmsResponse] = await Promise.all([
        RobotsSitemapService.getSmartPriorities(),
        RobotsSitemapService.getAlgorithmsConfig()
      ]);

      if (prioritiesResponse.success) {
        setPriorities(prioritiesResponse.priorities);
      }
      if (algorithmsResponse.success) {
        setAlgorithms(algorithmsResponse.config);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données d\'optimisation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCalculatePriorities = async () => {
    setIsCalculating(true);
    try {
      const response = await RobotsSitemapService.calculateSmartPriorities();
      if (response.success) {
        setPriorities(response.priorities);
      }
    } catch (error) {
      console.error('Erreur lors du calcul des priorités:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleConfigureAlgorithms = async (newConfig) => {
    try {
      const response = await RobotsSitemapService.configureSmartAlgorithms(newConfig);
      if (response.success) {
        setAlgorithms(newConfig);
      }
    } catch (error) {
      console.error('Erreur lors de la configuration des algorithmes:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="boss-text-center boss-py-8">
        <Spinner />
        <p className="boss-text-gray-500 boss-mt-4">
          {__('Chargement de l\'optimisation intelligente...', 'boss-seo')}
        </p>
      </div>
    );
  }

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-purple-50 boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
              {__('🧠 Algorithmes de Priorités Intelligentes', 'boss-seo')}
            </h3>
            <Button
              isPrimary
              onClick={handleCalculatePriorities}
              isBusy={isCalculating}
              disabled={isCalculating}
              className="boss-bg-purple-600 hover:boss-bg-purple-700"
            >
              <Dashicon icon="performance" className="boss-mr-1" />
              {__('🔄 Recalculer', 'boss-seo')}
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          <p className="boss-text-sm boss-text-gray-600 boss-mb-4">
            {__('Les algorithmes d\'IA analysent automatiquement vos contenus pour optimiser les priorités et fréquences des sitemaps.', 'boss-seo')}
          </p>

          {Object.keys(algorithms).length > 0 && (
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4 boss-mb-6">
              {Object.entries(algorithms).map(([key, algorithm]) => (
                <div key={key} className="boss-border boss-border-gray-200 boss-rounded boss-p-4">
                  <div className="boss-flex boss-justify-between boss-items-center boss-mb-2">
                    <h4 className="boss-font-medium">{algorithm.name}</h4>
                    <ToggleControl
                      checked={algorithm.enabled}
                      onChange={(enabled) => {
                        const newAlgorithms = {
                          ...algorithms,
                          [key]: { ...algorithm, enabled }
                        };
                        handleConfigureAlgorithms(newAlgorithms);
                      }}
                    />
                  </div>
                  <RangeControl
                    label={__('Poids', 'boss-seo')}
                    value={algorithm.weight * 100}
                    onChange={(value) => {
                      const newAlgorithms = {
                        ...algorithms,
                        [key]: { ...algorithm, weight: value / 100 }
                      };
                      handleConfigureAlgorithms(newAlgorithms);
                    }}
                    min={0}
                    max={100}
                    disabled={!algorithm.enabled}
                  />
                </div>
              ))}
            </div>
          )}

          {Object.keys(priorities).length > 0 && (
            <div className="boss-mt-6">
              <h4 className="boss-font-medium boss-mb-4">{__('📊 Aperçu des Priorités Calculées', 'boss-seo')}</h4>
              <div className="boss-max-h-64 boss-overflow-y-auto">
                {Object.entries(priorities).map(([postType, typePriorities]) => (
                  <div key={postType} className="boss-mb-4">
                    <h5 className="boss-font-medium boss-text-sm boss-mb-2 boss-capitalize">{postType}</h5>
                    <div className="boss-space-y-1">
                      {Object.values(typePriorities).slice(0, 5).map((item, index) => (
                        <div key={index} className="boss-flex boss-justify-between boss-items-center boss-text-xs boss-bg-gray-50 boss-p-2 boss-rounded">
                          <span className="boss-truncate boss-flex-1">{item.title}</span>
                          <span className="boss-font-medium boss-text-purple-600">{item.priority}</span>
                          <span className="boss-text-gray-500 boss-ml-2">{item.changefreq}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant Gestion des URLs Personnalisées
 */
const CustomUrlsManager = ({ sitemapSettings, setSitemapSettings }) => {
  const [customUrls, setCustomUrls] = useState([]);
  const [newUrl, setNewUrl] = useState({
    loc: '',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: '0.5'
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Charger les URLs au montage du composant
  useEffect(() => {
    loadCustomUrls();
  }, []);

  const loadCustomUrls = async () => {
    setIsLoading(true);
    try {
      const response = await RobotsSitemapService.getAllCustomUrls();
      if (response.success && response.urls) {
        setCustomUrls(response.urls);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des URLs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addCustomUrl = async () => {
    if (!newUrl.loc) return;

    setIsSaving(true);
    try {
      const response = await RobotsSitemapService.addCustomUrl(newUrl);
      if (response.success) {
        await loadCustomUrls(); // Recharger la liste
        setNewUrl({
          loc: '',
          lastmod: new Date().toISOString().split('T')[0],
          changefreq: 'monthly',
          priority: '0.5'
        });
        setShowAddModal(false);
      } else {
        console.error('Erreur lors de l\'ajout:', response.message);
        // Ici on pourrait afficher un message d'erreur à l'utilisateur
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'URL:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const removeCustomUrl = async (id) => {
    try {
      const response = await RobotsSitemapService.deleteCustomUrl(id);
      if (response.success) {
        await loadCustomUrls(); // Recharger la liste
      } else {
        console.error('Erreur lors de la suppression:', response.message);
      }
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'URL:', error);
    }
  };

  const handleImportCsv = async (csvContent) => {
    setIsSaving(true);
    try {
      const response = await RobotsSitemapService.importCustomUrlsCsv(csvContent);
      if (response.success) {
        await loadCustomUrls(); // Recharger la liste
        console.log(`Import réussi: ${response.imported} URLs importées`);
      } else {
        console.error('Erreur lors de l\'import:', response.message);
      }
    } catch (error) {
      console.error('Erreur lors de l\'import CSV:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleExportCsv = async () => {
    try {
      const response = await RobotsSitemapService.exportCustomUrlsCsv();
      if (response.success) {
        // Créer un lien de téléchargement
        const blob = new Blob([response.csv_content], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = response.filename || 'custom-urls.csv';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error('Erreur lors de l\'export:', response.message);
      }
    } catch (error) {
      console.error('Erreur lors de l\'export CSV:', error);
    }
  };

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-indigo-50 boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-items-center boss-justify-between">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
              {__('🔗 URLs Personnalisées', 'boss-seo')}
            </h3>
            <div className="boss-flex boss-space-x-2">
              <Button
                isPrimary
                onClick={() => setShowAddModal(true)}
                className="boss-bg-indigo-600 hover:boss-bg-indigo-700"
              >
                <Dashicon icon="plus-alt" className="boss-mr-1" />
                {__('➕ Ajouter URL', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.csv';
                  input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                      const reader = new FileReader();
                      reader.onload = (e) => handleImportCsv(e.target.result);
                      reader.readAsText(file);
                    }
                  };
                  input.click();
                }}
                disabled={isSaving}
              >
                <Dashicon icon="upload" className="boss-mr-1" />
                {__('📤 Import CSV', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                onClick={handleExportCsv}
                disabled={customUrls.length === 0}
              >
                <Dashicon icon="download" className="boss-mr-1" />
                {__('💾 Export CSV', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          {isLoading ? (
            <div className="boss-text-center boss-py-8">
              <Spinner />
              <p className="boss-text-gray-500 boss-mt-4">
                {__('Chargement des URLs personnalisées...', 'boss-seo')}
              </p>
            </div>
          ) : customUrls.length === 0 ? (
            <div className="boss-text-center boss-py-8">
              <Dashicon icon="admin-links" className="boss-text-gray-400 boss-text-4xl boss-mb-4" />
              <p className="boss-text-gray-500 boss-mb-4">
                {__('Aucune URL personnalisée configurée', 'boss-seo')}
              </p>
              <Button
                isPrimary
                onClick={() => setShowAddModal(true)}
              >
                {__('Ajouter votre première URL', 'boss-seo')}
              </Button>
            </div>
          ) : (
            <div className="boss-overflow-x-auto">
              <table className="boss-w-full boss-border-collapse">
                <thead>
                  <tr className="boss-bg-gray-50">
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('URL', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('Priorité', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('Fréquence', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('Dernière MAJ', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center boss-text-sm boss-font-semibold">
                      {__('Actions', 'boss-seo')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {customUrls.map((url) => (
                    <tr key={url.id} className="hover:boss-bg-gray-50">
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        <a href={url.loc} target="_blank" rel="noopener noreferrer" className="boss-text-blue-600 boss-underline">
                          {url.loc}
                        </a>
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        <span className="boss-bg-blue-100 boss-text-blue-800 boss-px-2 boss-py-1 boss-rounded-full boss-text-xs">
                          {url.priority}
                        </span>
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        {url.changefreq}
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        {url.lastmod}
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center">
                        <div className="boss-flex boss-justify-center boss-space-x-2">
                          <Button
                            isSmall
                            isSecondary
                            onClick={() => {/* Éditer */}}
                          >
                            <Dashicon icon="edit" />
                          </Button>
                          <Button
                            isSmall
                            isDestructive
                            onClick={() => removeCustomUrl(url.id)}
                          >
                            <Dashicon icon="trash" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Modal d'ajout d'URL */}
      {showAddModal && (
        <Modal
          title={__('Ajouter une URL personnalisée', 'boss-seo')}
          onRequestClose={() => setShowAddModal(false)}
          className="boss-w-full boss-max-w-md"
        >
          <div className="boss-space-y-4">
            <TextControl
              label={__('URL complète', 'boss-seo')}
              value={newUrl.loc}
              onChange={(value) => setNewUrl({ ...newUrl, loc: value })}
              placeholder="https://example.com/page-importante"
            />

            <div className="boss-grid boss-grid-cols-2 boss-gap-4">
              <SelectControl
                label={__('Priorité', 'boss-seo')}
                value={newUrl.priority}
                options={[
                  { label: '0.9 (Très important)', value: '0.9' },
                  { label: '0.8 (Important)', value: '0.8' },
                  { label: '0.7 (Normal)', value: '0.7' },
                  { label: '0.6 (Moins important)', value: '0.6' },
                  { label: '0.5 (Peu important)', value: '0.5' }
                ]}
                onChange={(value) => setNewUrl({ ...newUrl, priority: value })}
              />

              <SelectControl
                label={__('Fréquence', 'boss-seo')}
                value={newUrl.changefreq}
                options={[
                  { label: __('Quotidien', 'boss-seo'), value: 'daily' },
                  { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                  { label: __('Mensuel', 'boss-seo'), value: 'monthly' },
                  { label: __('Annuel', 'boss-seo'), value: 'yearly' }
                ]}
                onChange={(value) => setNewUrl({ ...newUrl, changefreq: value })}
              />
            </div>

            <TextControl
              label={__('Date de dernière modification', 'boss-seo')}
              type="date"
              value={newUrl.lastmod}
              onChange={(value) => setNewUrl({ ...newUrl, lastmod: value })}
            />

            <div className="boss-flex boss-justify-end boss-space-x-2 boss-pt-4">
              <Button
                isSecondary
                onClick={() => setShowAddModal(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                onClick={addCustomUrl}
                disabled={!newUrl.loc || isSaving}
                isBusy={isSaving}
              >
                {__('Ajouter', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

/**
 * Composant Monitoring et Analytics
 */
const SitemapMonitoring = () => {
  const [monitoringData, setMonitoringData] = useState({
    indexationStats: {
      submitted: 0,
      indexed: 0,
      pending: 0,
      errors: 0,
      rate: 0
    },
    submissionHistory: [],
    errors: [],
    byContentType: [],
    evolution: [],
    performance: [],
    benchmark: {}
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isDetectingErrors, setIsDetectingErrors] = useState(false);

  // Charger les données au montage du composant
  useEffect(() => {
    loadMonitoringData();
  }, []);

  const loadMonitoringData = async () => {
    setIsLoading(true);
    try {
      // Charger les statistiques d'indexation
      const indexationResponse = await RobotsSitemapService.getIndexationStats();
      if (indexationResponse.success && indexationResponse.stats) {
        const stats = indexationResponse.stats;

        setMonitoringData({
          indexationStats: stats.global || {},
          byContentType: stats.by_content_type || [],
          evolution: stats.evolution || [],
          performance: stats.performance || [],
          benchmark: stats.benchmark || {},
          errors: stats.errors_detail || [],
          submissionHistory: []
        });
      }

      // Charger l'historique des pings
      const historyResponse = await RobotsSitemapService.getPingHistory(10);
      if (historyResponse.success && historyResponse.history) {
        setMonitoringData(prev => ({
          ...prev,
          submissionHistory: historyResponse.history.map(entry => ({
            date: entry.timestamp,
            google: entry.results?.google?.success || false,
            bing: entry.results?.bing?.success || false,
            urls: entry.total_sitemaps || 0
          }))
        }));
      }

    } catch (error) {
      console.error('Erreur lors du chargement des données de monitoring:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshData = async () => {
    setIsRefreshing(true);
    try {
      const response = await RobotsSitemapService.refreshMonitoringData();
      if (response.success) {
        await loadMonitoringData();
      }
    } catch (error) {
      console.error('Erreur lors de l\'actualisation:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleDetectErrors = async () => {
    setIsDetectingErrors(true);
    try {
      const response = await RobotsSitemapService.detectSitemapErrors();
      if (response.success) {
        setMonitoringData(prev => ({
          ...prev,
          errors: response.errors || []
        }));
      }
    } catch (error) {
      console.error('Erreur lors de la détection d\'erreurs:', error);
    } finally {
      setIsDetectingErrors(false);
    }
  };

  if (isLoading) {
    return (
      <div className="boss-text-center boss-py-8">
        <Spinner />
        <p className="boss-text-gray-500 boss-mt-4">
          {__('Chargement des données de monitoring...', 'boss-seo')}
        </p>
      </div>
    );
  }

  return (
    <div className="boss-space-y-6">
      {/* Statistiques d'indexation */}
      <Card>
        <CardHeader className="boss-bg-green-50 boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
              {__('📈 Indexation Google', 'boss-seo')}
            </h3>
            <div className="boss-flex boss-space-x-2">
              <Button
                isSecondary
                onClick={handleRefreshData}
                isBusy={isRefreshing}
                disabled={isRefreshing}
                className="boss-border-green-600 boss-text-green-600 hover:boss-bg-green-50"
              >
                <Dashicon icon="update" className="boss-mr-1" />
                {__('🔄 Actualiser', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                onClick={handleDetectErrors}
                isBusy={isDetectingErrors}
                disabled={isDetectingErrors}
                className="boss-border-red-600 boss-text-red-600 hover:boss-bg-red-50"
              >
                <Dashicon icon="search" className="boss-mr-1" />
                {__('🔍 Détecter Erreurs', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4 boss-mb-6">
            <div className="boss-text-center boss-p-4 boss-bg-blue-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-blue-600">
                {monitoringData.indexationStats.submitted.toLocaleString()}
              </p>
              <p className="boss-text-sm boss-text-blue-500">📊 URLs soumises</p>
            </div>
            <div className="boss-text-center boss-p-4 boss-bg-green-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-green-600">
                {monitoringData.indexationStats.indexed.toLocaleString()}
              </p>
              <p className="boss-text-sm boss-text-green-500">✅ Indexées</p>
            </div>
            <div className="boss-text-center boss-p-4 boss-bg-yellow-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-yellow-600">
                {monitoringData.indexationStats.pending}
              </p>
              <p className="boss-text-sm boss-text-yellow-500">⏳ En attente</p>
            </div>
            <div className="boss-text-center boss-p-4 boss-bg-red-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-red-600">
                {monitoringData.indexationStats.errors}
              </p>
              <p className="boss-text-sm boss-text-red-500">❌ Erreurs</p>
            </div>
          </div>

          <div className="boss-bg-gradient-to-r boss-from-green-50 boss-to-blue-50 boss-p-4 boss-rounded-lg">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-gray-600">📈 Taux d'indexation</p>
                <div className="boss-flex boss-items-center boss-mt-2">
                  <div className="boss-w-48 boss-bg-gray-200 boss-rounded-full boss-h-3 boss-mr-3">
                    <div
                      className="boss-bg-green-600 boss-h-3 boss-rounded-full"
                      style={{ width: `${monitoringData.indexationStats.rate}%` }}
                    ></div>
                  </div>
                  <span className="boss-text-xl boss-font-bold boss-text-green-600">
                    {monitoringData.indexationStats.rate}%
                  </span>
                </div>
              </div>
              <Button isPrimary>
                <Dashicon icon="update" className="boss-mr-1" />
                {__('🔄 Actualiser', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Historique des soumissions */}
      <Card>
        <CardHeader className="boss-bg-blue-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('📅 Historique des Soumissions', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-overflow-x-auto">
            <table className="boss-w-full boss-border-collapse">
              <thead>
                <tr className="boss-bg-gray-50">
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                    {__('📅 Date', 'boss-seo')}
                  </th>
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center boss-text-sm boss-font-semibold">
                    {__('Google', 'boss-seo')}
                  </th>
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center boss-text-sm boss-font-semibold">
                    {__('Bing', 'boss-seo')}
                  </th>
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-right boss-text-sm boss-font-semibold">
                    {__('URLs', 'boss-seo')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {monitoringData.submissionHistory.map((entry, index) => (
                  <tr key={index} className="hover:boss-bg-gray-50">
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                      {entry.date}
                    </td>
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center">
                      {entry.google ? '✅' : '❌'}
                    </td>
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center">
                      {entry.bing ? '✅' : '❌'}
                    </td>
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-right boss-text-sm">
                      {entry.urls.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant principal - Configuration Avancée du Sitemap
 */
const AdvancedSitemapConfigPro = ({
  sitemapSettings,
  setSitemapSettings,
  onSave,
  isSaving,
  contentTypes,
  taxonomies
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isPinging, setIsPinging] = useState(false);
  const [pingResults, setPingResults] = useState(null);

  // Fonction pour régénérer les sitemaps
  const handleRegenerate = async () => {
    setIsRegenerating(true);
    try {
      await RobotsSitemapService.regenerateSitemap();
      // Afficher un message de succès
    } catch (error) {
      console.error('Erreur lors de la régénération:', error);
    } finally {
      setIsRegenerating(false);
    }
  };

  // Fonction pour ping les moteurs de recherche
  const handlePing = async () => {
    setIsPinging(true);
    try {
      const response = await RobotsSitemapService.pingAllSearchEngines();
      if (response.success && response.results && response.results.engines) {
        setPingResults({
          google: response.results.engines.google || { success: false },
          bing: response.results.engines.bing || { success: false }
        });
      } else {
        setPingResults({
          google: { success: false, message: 'Erreur inconnue' },
          bing: { success: false, message: 'Erreur inconnue' }
        });
      }
      setTimeout(() => setPingResults(null), 5000);
    } catch (error) {
      console.error('Erreur lors du ping:', error);
      setPingResults({
        google: { success: false, message: 'Erreur de connexion' },
        bing: { success: false, message: 'Erreur de connexion' }
      });
      setTimeout(() => setPingResults(null), 5000);
    } finally {
      setIsPinging(false);
    }
  };

  return (
    <div className="boss-advanced-sitemap-config-pro">
      {pingResults && (
        <Notice status="success" isDismissible={false} className="boss-mb-4">
          <p>{__('🎯 Résultats du ping:', 'boss-seo')}</p>
          <ul className="boss-list-disc boss-pl-5 boss-mt-2">
            <li>Google: {pingResults.google?.success ? '✅ Succès' : '❌ Échec'}</li>
            <li>Bing: {pingResults.bing?.success ? '✅ Succès' : '❌ Échec'}</li>
          </ul>
        </Notice>
      )}

      <SitemapDashboard 
        sitemapSettings={sitemapSettings}
        onRegenerate={handleRegenerate}
        onPing={handlePing}
        isLoading={isRegenerating || isPinging}
      />

      <GlobalConfiguration
        sitemapSettings={sitemapSettings}
        setSitemapSettings={setSitemapSettings}
      />

      {/* Onglets pour les fonctionnalités avancées */}
      <TabPanel
        className="boss-mb-6"
        activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
        onSelect={(tabName) => setActiveTab(tabName)}
        tabs={[
          {
            name: 'content-types',
            title: __('📄 Types de Contenu', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'specialized',
            title: __('🎯 Sitemaps Spécialisés', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'custom-urls',
            title: __('🔗 URLs Personnalisées', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'monitoring',
            title: __('📈 Monitoring', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'smart-optimization',
            title: __('🧠 Optimisation IA', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'search-console',
            title: __('🔍 Search Console', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'cache-performance',
            title: __('⚡ Cache & Performance', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'reports',
            title: __('📋 Rapports PDF', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          }
        ]}
      >
        {(tab) => {
          if (tab.name === 'content-types') {
            return (
              <ContentTypesManager
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
                contentTypes={contentTypes}
                taxonomies={taxonomies}
              />
            );
          } else if (tab.name === 'specialized') {
            return (
              <SpecializedSitemaps
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
              />
            );
          } else if (tab.name === 'custom-urls') {
            return (
              <CustomUrlsManager
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
              />
            );
          } else if (tab.name === 'monitoring') {
            return (
              <SitemapMonitoring />
            );
          } else if (tab.name === 'smart-optimization') {
            return (
              <SmartOptimization
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
              />
            );
          } else if (tab.name === 'search-console') {
            return (
              <SearchConsoleIntegration />
            );
          } else if (tab.name === 'cache-performance') {
            return (
              <CachePerformance />
            );
          } else if (tab.name === 'reports') {
            return (
              <PdfReports />
            );
          }
          return null;
        }}
      </TabPanel>

      {/* Bouton de sauvegarde */}
      <div className="boss-flex boss-justify-end boss-mt-6">
        <Button
          isPrimary
          onClick={() => onSave(sitemapSettings)}
          isBusy={isSaving}
          disabled={isSaving}
          className="boss-bg-green-600 hover:boss-bg-green-700"
        >
          <Dashicon icon="saved" className="boss-mr-1" />
          {__('💾 Enregistrer la Configuration', 'boss-seo')}
        </Button>
      </div>
    </div>
  );
};

/**
 * Composant Intégration Search Console
 */
const SearchConsoleIntegration = () => {
  const [searchConsoleData, setSearchConsoleData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [config, setConfig] = useState({
    site_url: get_site_url(),
    api_key: '',
    client_id: '',
    enabled: false
  });

  useEffect(() => {
    loadSearchConsoleData();
  }, []);

  const loadSearchConsoleData = async () => {
    setIsLoading(true);
    try {
      const response = await RobotsSitemapService.getSearchConsoleData();
      if (response.success) {
        setSearchConsoleData(response.data);
        setIsConnected(response.connected);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données Search Console:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setIsConfiguring(true);
    try {
      const response = await RobotsSitemapService.testSearchConsoleConnection();
      if (response.success) {
        setIsConnected(true);
        await loadSearchConsoleData();
      }
    } catch (error) {
      console.error('Erreur lors du test de connexion:', error);
    } finally {
      setIsConfiguring(false);
    }
  };

  const handleSubmitSitemap = async (sitemapType) => {
    try {
      const response = await RobotsSitemapService.submitSitemapToSearchConsole(sitemapType);
      if (response.success) {
        console.log('Sitemap soumis avec succès');
      }
    } catch (error) {
      console.error('Erreur lors de la soumission:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="boss-text-center boss-py-8">
        <Spinner />
        <p className="boss-text-gray-500 boss-mt-4">
          {__('Chargement des données Search Console...', 'boss-seo')}
        </p>
      </div>
    );
  }

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-blue-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('🔍 Intégration Google Search Console', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-flex boss-items-center boss-justify-between boss-mb-4">
            <div>
              <h4 className="boss-font-medium">{__('Statut de la connexion', 'boss-seo')}</h4>
              <p className="boss-text-sm boss-text-gray-600">
                {isConnected ?
                  __('✅ Connecté à Search Console', 'boss-seo') :
                  __('❌ Non connecté', 'boss-seo')
                }
              </p>
            </div>
            <Button
              isPrimary
              onClick={handleTestConnection}
              isBusy={isConfiguring}
              disabled={isConfiguring}
            >
              {isConnected ? __('🔄 Tester', 'boss-seo') : __('🔗 Connecter', 'boss-seo')}
            </Button>
          </div>

          {searchConsoleData && (
            <div className="boss-space-y-4">
              <div className="boss-grid boss-grid-cols-2 md:boss-grid-cols-4 boss-gap-4">
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-blue-600">
                    {searchConsoleData.indexation?.total_pages || 0}
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Pages totales</div>
                </div>
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-green-600">
                    {searchConsoleData.indexation?.indexed_pages || 0}
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Pages indexées</div>
                </div>
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-orange-600">
                    {searchConsoleData.indexation?.indexation_rate || 0}%
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Taux d'indexation</div>
                </div>
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-red-600">
                    {searchConsoleData.indexation?.crawl_errors || 0}
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Erreurs</div>
                </div>
              </div>

              {searchConsoleData.sitemaps && (
                <div className="boss-mt-6">
                  <h4 className="boss-font-medium boss-mb-4">{__('📋 Sitemaps dans Search Console', 'boss-seo')}</h4>
                  <div className="boss-space-y-2">
                    {searchConsoleData.sitemaps.map((sitemap, index) => (
                      <div key={index} className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-border boss-border-gray-200 boss-rounded">
                        <div>
                          <div className="boss-font-medium">{sitemap.path}</div>
                          <div className="boss-text-sm boss-text-gray-600">
                            {sitemap.submitted} soumises, {sitemap.indexed} indexées
                          </div>
                        </div>
                        <div className="boss-flex boss-space-x-2">
                          {sitemap.errors > 0 && (
                            <span className="boss-text-xs boss-bg-red-100 boss-text-red-800 boss-px-2 boss-py-1 boss-rounded">
                              {sitemap.errors} erreurs
                            </span>
                          )}
                          <Button
                            isSecondary
                            size="small"
                            onClick={() => handleSubmitSitemap(sitemap.path.replace('/sitemap-', '').replace('.xml', ''))}
                          >
                            {__('🔄 Resoumettre', 'boss-seo')}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant Cache et Performance
 */
const CachePerformance = () => {
  const [cacheStats, setCacheStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isClearing, setIsClearing] = useState(false);
  const [isPreloading, setIsPreloading] = useState(false);

  useEffect(() => {
    loadCacheStats();
  }, []);

  const loadCacheStats = async () => {
    setIsLoading(true);
    try {
      const response = await RobotsSitemapService.getCacheStats();
      if (response.success) {
        setCacheStats(response.stats);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des stats du cache:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearCache = async () => {
    setIsClearing(true);
    try {
      const response = await RobotsSitemapService.clearSitemapCache();
      if (response.success) {
        await loadCacheStats();
      }
    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
    } finally {
      setIsClearing(false);
    }
  };

  const handlePreloadCache = async () => {
    setIsPreloading(true);
    try {
      const response = await RobotsSitemapService.preloadSitemapCache();
      if (response.success) {
        await loadCacheStats();
      }
    } catch (error) {
      console.error('Erreur lors du préchargement:', error);
    } finally {
      setIsPreloading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="boss-text-center boss-py-8">
        <Spinner />
        <p className="boss-text-gray-500 boss-mt-4">
          {__('Chargement des statistiques du cache...', 'boss-seo')}
        </p>
      </div>
    );
  }

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-orange-50 boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
              {__('⚡ Cache et Performance des Sitemaps', 'boss-seo')}
            </h3>
            <div className="boss-flex boss-space-x-2">
              <Button
                isSecondary
                onClick={handlePreloadCache}
                isBusy={isPreloading}
                disabled={isPreloading}
                className="boss-border-green-600 boss-text-green-600 hover:boss-bg-green-50"
              >
                <Dashicon icon="download" className="boss-mr-1" />
                {__('📥 Précharger', 'boss-seo')}
              </Button>
              <Button
                isSecondary
                onClick={handleClearCache}
                isBusy={isClearing}
                disabled={isClearing}
                className="boss-border-red-600 boss-text-red-600 hover:boss-bg-red-50"
              >
                <Dashicon icon="trash" className="boss-mr-1" />
                {__('🗑️ Vider Cache', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          {cacheStats && (
            <div className="boss-space-y-6">
              <div className="boss-grid boss-grid-cols-2 md:boss-grid-cols-4 boss-gap-4">
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-orange-600">
                    {cacheStats.total_files}
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Fichiers en cache</div>
                </div>
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-blue-600">
                    {cacheStats.total_size_formatted}
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Taille totale</div>
                </div>
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-green-600">
                    {cacheStats.usage_percentage}%
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Utilisation</div>
                </div>
                <div className="boss-text-center boss-p-4 boss-bg-gray-50 boss-rounded">
                  <div className="boss-text-2xl boss-font-bold boss-text-purple-600">
                    {cacheStats.compression_enabled ? '✅' : '❌'}
                  </div>
                  <div className="boss-text-xs boss-text-gray-600">Compression</div>
                </div>
              </div>

              {cacheStats.sitemaps && cacheStats.sitemaps.length > 0 && (
                <div>
                  <h4 className="boss-font-medium boss-mb-4">{__('📋 Sitemaps en Cache', 'boss-seo')}</h4>
                  <div className="boss-space-y-2">
                    {cacheStats.sitemaps.map((sitemap, index) => (
                      <div key={index} className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-border boss-border-gray-200 boss-rounded">
                        <div>
                          <div className="boss-font-medium">{sitemap.type}</div>
                          <div className="boss-text-sm boss-text-gray-600">
                            {sitemap.urls_count} URLs • {sitemap.size_formatted}
                            {sitemap.compressed && (
                              <span className="boss-ml-2 boss-text-green-600">
                                📦 Compressé ({sitemap.compression_ratio}%)
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="boss-text-xs boss-text-gray-500">
                          {new Date(sitemap.generated_at).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant Rapports PDF
 */
const PdfReports = () => {
  const [templates, setTemplates] = useState([]);
  const [history, setHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    loadReportsData();
  }, []);

  const loadReportsData = async () => {
    setIsLoading(true);
    try {
      const [templatesResponse, historyResponse] = await Promise.all([
        RobotsSitemapService.getReportTemplates(),
        RobotsSitemapService.getReportsHistory(10)
      ]);

      if (templatesResponse.success) {
        setTemplates(Object.entries(templatesResponse.templates));
      }
      if (historyResponse.success) {
        setHistory(historyResponse.history);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données de rapports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateReport = async (templateType) => {
    setIsGenerating(true);
    try {
      const response = await RobotsSitemapService.generatePdfReport(templateType);
      if (response.success) {
        await loadReportsData();
        // Ouvrir le rapport généré
        if (response.download_url) {
          window.open(response.download_url, '_blank');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="boss-text-center boss-py-8">
        <Spinner />
        <p className="boss-text-gray-500 boss-mt-4">
          {__('Chargement des rapports...', 'boss-seo')}
        </p>
      </div>
    );
  }

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-red-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('📋 Génération de Rapports PDF', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
            {templates.map(([key, template]) => (
              <div key={key} className="boss-border boss-border-gray-200 boss-rounded boss-p-4">
                <h4 className="boss-font-medium boss-mb-2">{template.name}</h4>
                <p className="boss-text-sm boss-text-gray-600 boss-mb-4">{template.description}</p>
                <div className="boss-flex boss-justify-between boss-items-center">
                  <span className="boss-text-xs boss-text-gray-500">
                    📄 {template.pages} pages
                  </span>
                  <Button
                    isPrimary
                    size="small"
                    onClick={() => handleGenerateReport(key)}
                    isBusy={isGenerating}
                    disabled={isGenerating}
                  >
                    {__('📥 Générer', 'boss-seo')}
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {history.length > 0 && (
            <div className="boss-mt-8">
              <h4 className="boss-font-medium boss-mb-4">{__('📚 Historique des Rapports', 'boss-seo')}</h4>
              <div className="boss-space-y-2">
                {history.map((report, index) => (
                  <div key={index} className="boss-flex boss-justify-between boss-items-center boss-p-3 boss-border boss-border-gray-200 boss-rounded">
                    <div>
                      <div className="boss-font-medium">{report.template_type}</div>
                      <div className="boss-text-sm boss-text-gray-600">
                        Généré le {new Date(report.generated_at).toLocaleString()}
                      </div>
                    </div>
                    <Button
                      isSecondary
                      size="small"
                      onClick={() => window.open(report.download_url, '_blank')}
                    >
                      {__('📥 Télécharger', 'boss-seo')}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default AdvancedSitemapConfigPro;
