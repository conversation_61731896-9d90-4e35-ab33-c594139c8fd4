import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  Card<PERSON>ody,
  CardHeader,
  CardFooter,
  Button,
  ToggleControl,
  SelectControl,
  CheckboxControl,
  RangeControl,
  TextControl,
  TabPanel,
  Notice,
  Spinner,
  Dashicon,
  Flex,
  FlexItem,
  FlexBlock,
  Modal,
  __experimentalNumberControl as NumberControl
} from '@wordpress/components';

// Importer le service
import RobotsSitemapService from '../../services/RobotsSitemapService';

// Importer les styles
import './AdvancedSitemapConfigPro.css';

/**
 * Composant Dashboard de Statut
 */
const SitemapDashboard = ({ sitemapSettings, onRegenerate, onPing, isLoading }) => {
  const [stats, setStats] = useState({
    totalUrls: 1247,
    indexedUrls: 1156,
    pendingUrls: 45,
    errorUrls: 46,
    lastUpdate: '2h ago',
    indexationRate: 92.7
  });

  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-bg-gradient-to-r boss-from-blue-50 boss-to-indigo-50 boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <div className="boss-flex boss-items-center">
            <Dashicon icon="chart-bar" className="boss-text-blue-600 boss-mr-2" />
            <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
              {__('📊 Statut des Sitemaps', 'boss-seo')}
            </h2>
          </div>
          <div className="boss-flex boss-space-x-2">
            <Button
              isPrimary
              onClick={onRegenerate}
              isBusy={isLoading}
              disabled={isLoading}
              className="boss-bg-blue-600 hover:boss-bg-blue-700"
            >
              <Dashicon icon="update" className="boss-mr-1" />
              {__('🔄 Régénérer Tout', 'boss-seo')}
            </Button>
            <Button
              isSecondary
              onClick={onPing}
              disabled={isLoading}
              className="boss-border-blue-600 boss-text-blue-600 hover:boss-bg-blue-50"
            >
              <Dashicon icon="share" className="boss-mr-1" />
              {__('📤 Ping Moteurs', 'boss-seo')}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4 boss-mb-4">
          <div className="boss-bg-green-50 boss-p-4 boss-rounded-lg boss-border boss-border-green-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-green-600 boss-font-medium">✅ Sitemap Principal</p>
                <p className="boss-text-xs boss-text-green-500">📅 Dernière MAJ: {stats.lastUpdate}</p>
              </div>
              <Dashicon icon="yes-alt" className="boss-text-green-600 boss-text-xl" />
            </div>
          </div>
          
          <div className="boss-bg-blue-50 boss-p-4 boss-rounded-lg boss-border boss-border-blue-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-blue-600 boss-font-medium">📈 URLs Totales</p>
                <p className="boss-text-lg boss-font-bold boss-text-blue-700">{stats.totalUrls.toLocaleString()}</p>
              </div>
              <Dashicon icon="admin-links" className="boss-text-blue-600 boss-text-xl" />
            </div>
          </div>

          <div className="boss-bg-yellow-50 boss-p-4 boss-rounded-lg boss-border boss-border-yellow-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-yellow-600 boss-font-medium">⏳ En attente</p>
                <p className="boss-text-lg boss-font-bold boss-text-yellow-700">{stats.pendingUrls}</p>
              </div>
              <Dashicon icon="clock" className="boss-text-yellow-600 boss-text-xl" />
            </div>
          </div>

          <div className="boss-bg-red-50 boss-p-4 boss-rounded-lg boss-border boss-border-red-200">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-red-600 boss-font-medium">❌ Erreurs</p>
                <p className="boss-text-lg boss-font-bold boss-text-red-700">{stats.errorUrls}</p>
              </div>
              <Dashicon icon="warning" className="boss-text-red-600 boss-text-xl" />
            </div>
          </div>
        </div>

        <div className="boss-bg-gradient-to-r boss-from-green-50 boss-to-blue-50 boss-p-4 boss-rounded-lg boss-border boss-border-gray-200">
          <div className="boss-flex boss-items-center boss-justify-between">
            <div>
              <p className="boss-text-sm boss-text-gray-600">📊 Taux d'indexation Google</p>
              <div className="boss-flex boss-items-center boss-mt-1">
                <div className="boss-w-32 boss-bg-gray-200 boss-rounded-full boss-h-2 boss-mr-3">
                  <div 
                    className="boss-bg-green-600 boss-h-2 boss-rounded-full" 
                    style={{ width: `${stats.indexationRate}%` }}
                  ></div>
                </div>
                <span className="boss-text-lg boss-font-bold boss-text-green-600">{stats.indexationRate}%</span>
              </div>
            </div>
            <div className="boss-text-right">
              <p className="boss-text-sm boss-text-gray-600">✅ Indexées: {stats.indexedUrls.toLocaleString()}</p>
              <p className="boss-text-xs boss-text-gray-500">📊 Sur {stats.totalUrls.toLocaleString()} soumises</p>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

/**
 * Composant Configuration Globale
 */
const GlobalConfiguration = ({ sitemapSettings, setSitemapSettings }) => {
  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-bg-gray-50 boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-items-center">
          <Dashicon icon="admin-settings" className="boss-text-gray-600 boss-mr-2" />
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('⚙️ Configuration Globale', 'boss-seo')}
          </h3>
        </div>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
          <div className="boss-space-y-4">
            <ToggleControl
              label={__('🔘 Activer les sitemaps XML', 'boss-seo')}
              checked={sitemapSettings.enabled}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, enabled: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
            
            <ToggleControl
              label={__('🔘 Mise à jour automatique', 'boss-seo')}
              checked={sitemapSettings.enableAutoUpdate}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, enableAutoUpdate: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
            
            <ToggleControl
              label={__('🔘 Ping automatique des moteurs de recherche', 'boss-seo')}
              checked={sitemapSettings.autoPing || false}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, autoPing: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
          </div>

          <div className="boss-space-y-4">
            <ToggleControl
              label={__('🔘 Inclure les images dans les sitemaps', 'boss-seo')}
              checked={sitemapSettings.includeImages}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeImages: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
            
            <ToggleControl
              label={__('🔘 Inclure les dates de modification', 'boss-seo')}
              checked={sitemapSettings.includeLastMod}
              onChange={(value) => setSitemapSettings({ ...sitemapSettings, includeLastMod: value })}
              className="boss-border-b boss-border-gray-100 boss-pb-3"
            />
          </div>
        </div>

        {sitemapSettings.enableAutoUpdate && (
          <div className="boss-mt-6 boss-p-4 boss-bg-blue-50 boss-rounded-lg boss-border boss-border-blue-200">
            <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-4">
              <SelectControl
                label={__('Fréquence de mise à jour', 'boss-seo')}
                value={sitemapSettings.autoUpdateFrequency || 'daily'}
                options={[
                  { label: __('Quotidienne', 'boss-seo'), value: 'daily' },
                  { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                  { label: __('Mensuelle', 'boss-seo'), value: 'monthly' }
                ]}
                onChange={(value) => setSitemapSettings({ ...sitemapSettings, autoUpdateFrequency: value })}
              />
              
              <NumberControl
                label={__('Limite d\'URLs par sitemap', 'boss-seo')}
                value={sitemapSettings.maxUrlsPerSitemap || 50000}
                onChange={(value) => setSitemapSettings({ ...sitemapSettings, maxUrlsPerSitemap: value })}
                min={1000}
                max={50000}
                step={1000}
              />
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

/**
 * Composant Gestion des Types de Contenu
 */
const ContentTypesManager = ({ sitemapSettings, setSitemapSettings, contentTypes, taxonomies }) => {
  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-blue-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('📄 Gestion par Type de Contenu', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 lg:boss-grid-cols-3 boss-gap-4">
            {contentTypes.map((type) => (
              <Card key={type.name} className="boss-border boss-border-gray-200 boss-shadow-sm">
                <CardHeader className="boss-bg-gradient-to-r boss-from-gray-50 boss-to-blue-50 boss-border-b boss-border-gray-200">
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <h4 className="boss-text-sm boss-font-semibold boss-text-boss-dark">
                      {type.label}
                    </h4>
                    <div className="boss-flex boss-items-center">
                      <span className="boss-text-xs boss-bg-blue-100 boss-text-blue-800 boss-px-2 boss-py-1 boss-rounded-full">
                        {type.name === 'post' ? '1,156 URLs' :
                         type.name === 'page' ? '89 URLs' :
                         type.name === 'product' ? '234 URLs' : '0 URLs'}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-3">
                    <ToggleControl
                      label={__('✅ Inclure dans le sitemap', 'boss-seo')}
                      checked={sitemapSettings.enablePostTypeSitemaps?.[type.name] || false}
                      onChange={(value) => {
                        const enablePostTypeSitemaps = { ...(sitemapSettings.enablePostTypeSitemaps || {}) };
                        enablePostTypeSitemaps[type.name] = value;
                        setSitemapSettings({ ...sitemapSettings, enablePostTypeSitemaps });
                      }}
                    />

                    {sitemapSettings.enablePostTypeSitemaps?.[type.name] && (
                      <>
                        <div className="boss-grid boss-grid-cols-2 boss-gap-2">
                          <SelectControl
                            label={__('Priorité', 'boss-seo')}
                            value={sitemapSettings.contentTypePriorities?.[type.name] || '0.7'}
                            options={[
                              { label: '0.9', value: '0.9' },
                              { label: '0.8', value: '0.8' },
                              { label: '0.7', value: '0.7' },
                              { label: '0.6', value: '0.6' },
                              { label: '0.5', value: '0.5' }
                            ]}
                            onChange={(value) => {
                              const contentTypePriorities = { ...(sitemapSettings.contentTypePriorities || {}) };
                              contentTypePriorities[type.name] = value;
                              setSitemapSettings({ ...sitemapSettings, contentTypePriorities });
                            }}
                          />

                          <SelectControl
                            label={__('Fréquence', 'boss-seo')}
                            value={sitemapSettings.contentTypeFrequencies?.[type.name] || 'weekly'}
                            options={[
                              { label: __('Daily', 'boss-seo'), value: 'daily' },
                              { label: __('Weekly', 'boss-seo'), value: 'weekly' },
                              { label: __('Monthly', 'boss-seo'), value: 'monthly' }
                            ]}
                            onChange={(value) => {
                              const contentTypeFrequencies = { ...(sitemapSettings.contentTypeFrequencies || {}) };
                              contentTypeFrequencies[type.name] = value;
                              setSitemapSettings({ ...sitemapSettings, contentTypeFrequencies });
                            }}
                          />
                        </div>

                        <div className="boss-bg-green-50 boss-p-3 boss-rounded boss-border boss-border-green-200">
                          <p className="boss-text-xs boss-text-green-700 boss-mb-2">🎯 Règles Intelligentes:</p>
                          <ul className="boss-text-xs boss-text-green-600 boss-space-y-1">
                            <li>• Contenu récent (&lt; 30j): Priorité +0.2</li>
                            <li>• Contenu populaire: Priorité +0.1</li>
                            <li>• Contenu archivé (&gt; 1an): Priorité -0.2</li>
                          </ul>
                        </div>

                        <a
                          href={`${window.location.origin}/sitemap-${type.name}.xml`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="boss-text-xs boss-text-blue-600 boss-font-medium boss-underline boss-flex boss-items-center"
                        >
                          <Dashicon icon="external" className="boss-mr-1" />
                          {__('🌐 Voir le sitemap', 'boss-seo')}
                        </a>
                      </>
                    )}
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant Sitemaps Spécialisés
 */
const SpecializedSitemaps = ({ sitemapSettings, setSitemapSettings }) => {
  const specializedTypes = [
    {
      key: 'enableImageSitemap',
      title: __('🖼️ Sitemap d\'Images', 'boss-seo'),
      description: __('Inclut toutes les images de vos contenus dans un sitemap dédié pour améliorer leur indexation.', 'boss-seo'),
      url: '/sitemap-image.xml',
      stats: '2,341 images',
      color: 'green'
    },
    {
      key: 'enableVideoSitemap',
      title: __('🎥 Sitemap de Vidéos', 'boss-seo'),
      description: __('Référence toutes les vidéos intégrées (YouTube, Vimeo, etc.) pour une meilleure visibilité.', 'boss-seo'),
      url: '/sitemap-video.xml',
      stats: '89 vidéos',
      color: 'purple'
    },
    {
      key: 'enableNewsSitemap',
      title: __('📰 Sitemap News', 'boss-seo'),
      description: __('Sitemap spécialisé pour Google News (articles des dernières 48h).', 'boss-seo'),
      url: '/sitemap-news.xml',
      stats: '12 articles',
      color: 'blue'
    },
    {
      key: 'enableStoriesSitemap',
      title: __('📱 Sitemap Stories', 'boss-seo'),
      description: __('Sitemap pour les Web Stories et contenus AMP.', 'boss-seo'),
      url: '/sitemap-stories.xml',
      stats: '0 stories',
      color: 'orange'
    }
  ];

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-purple-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('🎯 Sitemaps Spécialisés', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
            {specializedTypes.map((type) => (
              <Card key={type.key} className={`boss-border boss-border-${type.color}-200 boss-shadow-sm`}>
                <CardHeader className={`boss-bg-${type.color}-50 boss-border-b boss-border-${type.color}-200`}>
                  <div className="boss-flex boss-items-center boss-justify-between">
                    <h4 className="boss-text-sm boss-font-semibold boss-text-boss-dark">
                      {type.title}
                    </h4>
                    <span className={`boss-text-xs boss-bg-${type.color}-100 boss-text-${type.color}-800 boss-px-2 boss-py-1 boss-rounded-full`}>
                      📊 {type.stats}
                    </span>
                  </div>
                </CardHeader>
                <CardBody>
                  <div className="boss-space-y-3">
                    <ToggleControl
                      label={__('✅ Activer', 'boss-seo')}
                      checked={sitemapSettings[type.key] || false}
                      onChange={(value) => setSitemapSettings({ ...sitemapSettings, [type.key]: value })}
                    />

                    <p className="boss-text-xs boss-text-gray-600">
                      {type.description}
                    </p>

                    {sitemapSettings[type.key] && (
                      <div className={`boss-bg-${type.color}-50 boss-p-3 boss-rounded boss-border boss-border-${type.color}-200`}>
                        <a
                          href={`${window.location.origin}${type.url}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`boss-text-xs boss-text-${type.color}-600 boss-font-medium boss-underline boss-flex boss-items-center`}
                        >
                          <Dashicon icon="external" className="boss-mr-1" />
                          {__('🌐 Voir le sitemap', 'boss-seo')}
                        </a>
                        <Button
                          isSecondary
                          size="small"
                          className="boss-mt-2"
                          onClick={() => {/* Configuration avancée */}}
                        >
                          <Dashicon icon="admin-settings" className="boss-mr-1" />
                          {__('⚙️ Configurer', 'boss-seo')}
                        </Button>
                      </div>
                    )}
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant Gestion des URLs Personnalisées
 */
const CustomUrlsManager = ({ sitemapSettings, setSitemapSettings }) => {
  const [customUrls, setCustomUrls] = useState(sitemapSettings.customUrls || []);
  const [newUrl, setNewUrl] = useState({
    loc: '',
    lastmod: new Date().toISOString().split('T')[0],
    changefreq: 'monthly',
    priority: '0.5'
  });
  const [showAddModal, setShowAddModal] = useState(false);

  const addCustomUrl = () => {
    if (!newUrl.loc) return;

    const updatedUrls = [...customUrls, { ...newUrl, id: Date.now() }];
    setCustomUrls(updatedUrls);
    setSitemapSettings({ ...sitemapSettings, customUrls: updatedUrls });
    setNewUrl({
      loc: '',
      lastmod: new Date().toISOString().split('T')[0],
      changefreq: 'monthly',
      priority: '0.5'
    });
    setShowAddModal(false);
  };

  const removeCustomUrl = (id) => {
    const updatedUrls = customUrls.filter(url => url.id !== id);
    setCustomUrls(updatedUrls);
    setSitemapSettings({ ...sitemapSettings, customUrls: updatedUrls });
  };

  return (
    <div className="boss-space-y-6">
      <Card>
        <CardHeader className="boss-bg-indigo-50 boss-border-b boss-border-gray-200">
          <div className="boss-flex boss-items-center boss-justify-between">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
              {__('🔗 URLs Personnalisées', 'boss-seo')}
            </h3>
            <div className="boss-flex boss-space-x-2">
              <Button
                isPrimary
                onClick={() => setShowAddModal(true)}
                className="boss-bg-indigo-600 hover:boss-bg-indigo-700"
              >
                <Dashicon icon="plus-alt" className="boss-mr-1" />
                {__('➕ Ajouter URL', 'boss-seo')}
              </Button>
              <Button isSecondary>
                <Dashicon icon="upload" className="boss-mr-1" />
                {__('📤 Import CSV', 'boss-seo')}
              </Button>
              <Button isSecondary>
                <Dashicon icon="download" className="boss-mr-1" />
                {__('💾 Export CSV', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          {customUrls.length === 0 ? (
            <div className="boss-text-center boss-py-8">
              <Dashicon icon="admin-links" className="boss-text-gray-400 boss-text-4xl boss-mb-4" />
              <p className="boss-text-gray-500 boss-mb-4">
                {__('Aucune URL personnalisée configurée', 'boss-seo')}
              </p>
              <Button
                isPrimary
                onClick={() => setShowAddModal(true)}
              >
                {__('Ajouter votre première URL', 'boss-seo')}
              </Button>
            </div>
          ) : (
            <div className="boss-overflow-x-auto">
              <table className="boss-w-full boss-border-collapse">
                <thead>
                  <tr className="boss-bg-gray-50">
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('URL', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('Priorité', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('Fréquence', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                      {__('Dernière MAJ', 'boss-seo')}
                    </th>
                    <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center boss-text-sm boss-font-semibold">
                      {__('Actions', 'boss-seo')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {customUrls.map((url) => (
                    <tr key={url.id} className="hover:boss-bg-gray-50">
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        <a href={url.loc} target="_blank" rel="noopener noreferrer" className="boss-text-blue-600 boss-underline">
                          {url.loc}
                        </a>
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        <span className="boss-bg-blue-100 boss-text-blue-800 boss-px-2 boss-py-1 boss-rounded-full boss-text-xs">
                          {url.priority}
                        </span>
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        {url.changefreq}
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                        {url.lastmod}
                      </td>
                      <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center">
                        <div className="boss-flex boss-justify-center boss-space-x-2">
                          <Button
                            isSmall
                            isSecondary
                            onClick={() => {/* Éditer */}}
                          >
                            <Dashicon icon="edit" />
                          </Button>
                          <Button
                            isSmall
                            isDestructive
                            onClick={() => removeCustomUrl(url.id)}
                          >
                            <Dashicon icon="trash" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Modal d'ajout d'URL */}
      {showAddModal && (
        <Modal
          title={__('Ajouter une URL personnalisée', 'boss-seo')}
          onRequestClose={() => setShowAddModal(false)}
          className="boss-w-full boss-max-w-md"
        >
          <div className="boss-space-y-4">
            <TextControl
              label={__('URL complète', 'boss-seo')}
              value={newUrl.loc}
              onChange={(value) => setNewUrl({ ...newUrl, loc: value })}
              placeholder="https://example.com/page-importante"
            />

            <div className="boss-grid boss-grid-cols-2 boss-gap-4">
              <SelectControl
                label={__('Priorité', 'boss-seo')}
                value={newUrl.priority}
                options={[
                  { label: '0.9 (Très important)', value: '0.9' },
                  { label: '0.8 (Important)', value: '0.8' },
                  { label: '0.7 (Normal)', value: '0.7' },
                  { label: '0.6 (Moins important)', value: '0.6' },
                  { label: '0.5 (Peu important)', value: '0.5' }
                ]}
                onChange={(value) => setNewUrl({ ...newUrl, priority: value })}
              />

              <SelectControl
                label={__('Fréquence', 'boss-seo')}
                value={newUrl.changefreq}
                options={[
                  { label: __('Quotidien', 'boss-seo'), value: 'daily' },
                  { label: __('Hebdomadaire', 'boss-seo'), value: 'weekly' },
                  { label: __('Mensuel', 'boss-seo'), value: 'monthly' },
                  { label: __('Annuel', 'boss-seo'), value: 'yearly' }
                ]}
                onChange={(value) => setNewUrl({ ...newUrl, changefreq: value })}
              />
            </div>

            <TextControl
              label={__('Date de dernière modification', 'boss-seo')}
              type="date"
              value={newUrl.lastmod}
              onChange={(value) => setNewUrl({ ...newUrl, lastmod: value })}
            />

            <div className="boss-flex boss-justify-end boss-space-x-2 boss-pt-4">
              <Button
                isSecondary
                onClick={() => setShowAddModal(false)}
              >
                {__('Annuler', 'boss-seo')}
              </Button>
              <Button
                isPrimary
                onClick={addCustomUrl}
                disabled={!newUrl.loc}
              >
                {__('Ajouter', 'boss-seo')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

/**
 * Composant Monitoring et Analytics
 */
const SitemapMonitoring = () => {
  const [monitoringData, setMonitoringData] = useState({
    indexationStats: {
      submitted: 1247,
      indexed: 1156,
      pending: 45,
      errors: 46,
      rate: 92.7
    },
    submissionHistory: [
      { date: '2024-01-15 14:30', google: true, bing: true, urls: 1247 },
      { date: '2024-01-14 09:15', google: true, bing: false, urls: 1203 },
      { date: '2024-01-13 16:45', google: true, bing: true, urls: 1198 }
    ],
    errors: [
      { url: '/page-404', error: 'Page non trouvée (404)', date: '2024-01-15' },
      { url: '/redirect-loop', error: 'Boucle de redirection', date: '2024-01-14' }
    ]
  });

  return (
    <div className="boss-space-y-6">
      {/* Statistiques d'indexation */}
      <Card>
        <CardHeader className="boss-bg-green-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('📈 Indexation Google', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-4 boss-mb-6">
            <div className="boss-text-center boss-p-4 boss-bg-blue-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-blue-600">
                {monitoringData.indexationStats.submitted.toLocaleString()}
              </p>
              <p className="boss-text-sm boss-text-blue-500">📊 URLs soumises</p>
            </div>
            <div className="boss-text-center boss-p-4 boss-bg-green-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-green-600">
                {monitoringData.indexationStats.indexed.toLocaleString()}
              </p>
              <p className="boss-text-sm boss-text-green-500">✅ Indexées</p>
            </div>
            <div className="boss-text-center boss-p-4 boss-bg-yellow-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-yellow-600">
                {monitoringData.indexationStats.pending}
              </p>
              <p className="boss-text-sm boss-text-yellow-500">⏳ En attente</p>
            </div>
            <div className="boss-text-center boss-p-4 boss-bg-red-50 boss-rounded-lg">
              <p className="boss-text-2xl boss-font-bold boss-text-red-600">
                {monitoringData.indexationStats.errors}
              </p>
              <p className="boss-text-sm boss-text-red-500">❌ Erreurs</p>
            </div>
          </div>

          <div className="boss-bg-gradient-to-r boss-from-green-50 boss-to-blue-50 boss-p-4 boss-rounded-lg">
            <div className="boss-flex boss-items-center boss-justify-between">
              <div>
                <p className="boss-text-sm boss-text-gray-600">📈 Taux d'indexation</p>
                <div className="boss-flex boss-items-center boss-mt-2">
                  <div className="boss-w-48 boss-bg-gray-200 boss-rounded-full boss-h-3 boss-mr-3">
                    <div
                      className="boss-bg-green-600 boss-h-3 boss-rounded-full"
                      style={{ width: `${monitoringData.indexationStats.rate}%` }}
                    ></div>
                  </div>
                  <span className="boss-text-xl boss-font-bold boss-text-green-600">
                    {monitoringData.indexationStats.rate}%
                  </span>
                </div>
              </div>
              <Button isPrimary>
                <Dashicon icon="update" className="boss-mr-1" />
                {__('🔄 Actualiser', 'boss-seo')}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Historique des soumissions */}
      <Card>
        <CardHeader className="boss-bg-blue-50 boss-border-b boss-border-gray-200">
          <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark">
            {__('📅 Historique des Soumissions', 'boss-seo')}
          </h3>
        </CardHeader>
        <CardBody>
          <div className="boss-overflow-x-auto">
            <table className="boss-w-full boss-border-collapse">
              <thead>
                <tr className="boss-bg-gray-50">
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-left boss-text-sm boss-font-semibold">
                    {__('📅 Date', 'boss-seo')}
                  </th>
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center boss-text-sm boss-font-semibold">
                    {__('Google', 'boss-seo')}
                  </th>
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center boss-text-sm boss-font-semibold">
                    {__('Bing', 'boss-seo')}
                  </th>
                  <th className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-right boss-text-sm boss-font-semibold">
                    {__('URLs', 'boss-seo')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {monitoringData.submissionHistory.map((entry, index) => (
                  <tr key={index} className="hover:boss-bg-gray-50">
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-sm">
                      {entry.date}
                    </td>
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center">
                      {entry.google ? '✅' : '❌'}
                    </td>
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-center">
                      {entry.bing ? '✅' : '❌'}
                    </td>
                    <td className="boss-border boss-border-gray-200 boss-px-4 boss-py-2 boss-text-right boss-text-sm">
                      {entry.urls.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

/**
 * Composant principal - Configuration Avancée du Sitemap
 */
const AdvancedSitemapConfigPro = ({
  sitemapSettings,
  setSitemapSettings,
  onSave,
  isSaving,
  contentTypes,
  taxonomies
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isPinging, setIsPinging] = useState(false);
  const [pingResults, setPingResults] = useState(null);

  // Fonction pour régénérer les sitemaps
  const handleRegenerate = async () => {
    setIsRegenerating(true);
    try {
      await RobotsSitemapService.regenerateSitemap();
      // Afficher un message de succès
    } catch (error) {
      console.error('Erreur lors de la régénération:', error);
    } finally {
      setIsRegenerating(false);
    }
  };

  // Fonction pour ping les moteurs de recherche
  const handlePing = async () => {
    setIsPinging(true);
    try {
      const response = await RobotsSitemapService.pingSearchEngines();
      setPingResults(response.results);
      setTimeout(() => setPingResults(null), 5000);
    } catch (error) {
      console.error('Erreur lors du ping:', error);
    } finally {
      setIsPinging(false);
    }
  };

  return (
    <div className="boss-advanced-sitemap-config-pro">
      {pingResults && (
        <Notice status="success" isDismissible={false} className="boss-mb-4">
          <p>{__('🎯 Résultats du ping:', 'boss-seo')}</p>
          <ul className="boss-list-disc boss-pl-5 boss-mt-2">
            <li>Google: {pingResults.google?.success ? '✅ Succès' : '❌ Échec'}</li>
            <li>Bing: {pingResults.bing?.success ? '✅ Succès' : '❌ Échec'}</li>
          </ul>
        </Notice>
      )}

      <SitemapDashboard 
        sitemapSettings={sitemapSettings}
        onRegenerate={handleRegenerate}
        onPing={handlePing}
        isLoading={isRegenerating || isPinging}
      />

      <GlobalConfiguration
        sitemapSettings={sitemapSettings}
        setSitemapSettings={setSitemapSettings}
      />

      {/* Onglets pour les fonctionnalités avancées */}
      <TabPanel
        className="boss-mb-6"
        activeClass="boss-bg-white boss-border-t boss-border-l boss-border-r boss-border-gray-200 boss-rounded-t-lg"
        onSelect={(tabName) => setActiveTab(tabName)}
        tabs={[
          {
            name: 'content-types',
            title: __('📄 Types de Contenu', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'specialized',
            title: __('🎯 Sitemaps Spécialisés', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'custom-urls',
            title: __('🔗 URLs Personnalisées', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          },
          {
            name: 'monitoring',
            title: __('📈 Monitoring', 'boss-seo'),
            className: 'boss-font-medium boss-px-4 boss-py-2'
          }
        ]}
      >
        {(tab) => {
          if (tab.name === 'content-types') {
            return (
              <ContentTypesManager
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
                contentTypes={contentTypes}
                taxonomies={taxonomies}
              />
            );
          } else if (tab.name === 'specialized') {
            return (
              <SpecializedSitemaps
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
              />
            );
          } else if (tab.name === 'custom-urls') {
            return (
              <CustomUrlsManager
                sitemapSettings={sitemapSettings}
                setSitemapSettings={setSitemapSettings}
              />
            );
          } else if (tab.name === 'monitoring') {
            return (
              <SitemapMonitoring />
            );
          }
          return null;
        }}
      </TabPanel>

      {/* Bouton de sauvegarde */}
      <div className="boss-flex boss-justify-end boss-mt-6">
        <Button
          isPrimary
          onClick={() => onSave(sitemapSettings)}
          isBusy={isSaving}
          disabled={isSaving}
          className="boss-bg-green-600 hover:boss-bg-green-700"
        >
          <Dashicon icon="saved" className="boss-mr-1" />
          {__('💾 Enregistrer la Configuration', 'boss-seo')}
        </Button>
      </div>
    </div>
  );
};

export default AdvancedSitemapConfigPro;
