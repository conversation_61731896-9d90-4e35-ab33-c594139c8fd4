{"version": 3, "names": ["_icons", "require", "_i18n", "_button", "_interopRequireDefault", "_tooltip", "_view", "_context", "_hook", "BorderBoxControlLinkedButton", "props", "forwardedRef", "className", "isLinked", "buttonProps", "useBorderBoxControlLinkedButton", "label", "__", "_react", "createElement", "default", "text", "View", "size", "icon", "link", "linkOff", "iconSize", "ref", "ConnectedBorderBoxControlLinkedButton", "contextConnect", "_default", "exports"], "sources": ["@wordpress/components/src/border-box-control/border-box-control-linked-button/component.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { link, linkOff } from '@wordpress/icons';\nimport { __ } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport Button from '../../button';\nimport Tooltip from '../../tooltip';\nimport { View } from '../../view';\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { useBorderBoxControlLinkedButton } from './hook';\n\nimport type { LinkedButtonProps } from '../types';\n\nconst BorderBoxControlLinkedButton = (\n\tprops: WordPressComponentProps< LinkedButtonProps, 'button' >,\n\tforwardedRef: React.ForwardedRef< any >\n) => {\n\tconst { className, isLinked, ...buttonProps } =\n\t\tuseBorderBoxControlLinkedButton( props );\n\tconst label = isLinked ? __( 'Unlink sides' ) : __( 'Link sides' );\n\n\treturn (\n\t\t<Tooltip text={ label }>\n\t\t\t<View className={ className }>\n\t\t\t\t<Button\n\t\t\t\t\t{ ...buttonProps }\n\t\t\t\t\tsize=\"small\"\n\t\t\t\t\ticon={ isLinked ? link : linkOff }\n\t\t\t\t\ticonSize={ 24 }\n\t\t\t\t\taria-label={ label }\n\t\t\t\t\tref={ forwardedRef }\n\t\t\t\t/>\n\t\t\t</View>\n\t\t</Tooltip>\n\t);\n};\n\nconst ConnectedBorderBoxControlLinkedButton = contextConnect(\n\tBorderBoxControlLinkedButton,\n\t'BorderBoxControlLinkedButton'\n);\nexport default ConnectedBorderBoxControlLinkedButton;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAKA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,QAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AAEA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AAdA;AACA;AACA;;AAIA;AACA;AACA;;AAUA,MAAMQ,4BAA4B,GAAGA,CACpCC,KAA6D,EAC7DC,YAAuC,KACnC;EACJ,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAE,GAAGC;EAAY,CAAC,GAC5C,IAAAC,qCAA+B,EAAEL,KAAM,CAAC;EACzC,MAAMM,KAAK,GAAGH,QAAQ,GAAG,IAAAI,QAAE,EAAE,cAAe,CAAC,GAAG,IAAAA,QAAE,EAAE,YAAa,CAAC;EAElE,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACd,QAAA,CAAAe,OAAO;IAACC,IAAI,EAAGL;EAAO,GACtB,IAAAE,MAAA,CAAAC,aAAA,EAACb,KAAA,CAAAgB,IAAI;IAACV,SAAS,EAAGA;EAAW,GAC5B,IAAAM,MAAA,CAAAC,aAAA,EAAChB,OAAA,CAAAiB,OAAM;IAAA,GACDN,WAAW;IAChBS,IAAI,EAAC,OAAO;IACZC,IAAI,EAAGX,QAAQ,GAAGY,WAAI,GAAGC,cAAS;IAClCC,QAAQ,EAAG,EAAI;IACf,cAAaX,KAAO;IACpBY,GAAG,EAAGjB;EAAc,CACpB,CACI,CACE,CAAC;AAEZ,CAAC;AAED,MAAMkB,qCAAqC,GAAG,IAAAC,uBAAc,EAC3DrB,4BAA4B,EAC5B,8BACD,CAAC;AAAC,IAAAsB,QAAA,GACaF,qCAAqC;AAAAG,OAAA,CAAAZ,OAAA,GAAAW,QAAA"}