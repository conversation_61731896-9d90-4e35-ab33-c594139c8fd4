import { useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';
import {
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Button,
  SelectControl,
  TextControl,
  Dashicon,
  Spinner
} from '@wordpress/components';

const RedirectionLogs = ({ logs, onClearLogs, isLoading }) => {
  // États
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Réinitialiser la page lorsque les filtres changent
  useEffect(() => {
    setCurrentPage(1);
  }, [filterStatus, searchQuery]);
  
  // Fonction pour obtenir le texte du statut
  const getStatusText = (status) => {
    switch (status) {
      case 'success':
        return __('Succès', 'boss-seo');
      case 'error':
        return __('Erreur', 'boss-seo');
      case 'not_found':
        return __('Non trouvé', 'boss-seo');
      default:
        return status;
    }
  };
  
  // Fonction pour obtenir la classe de couleur en fonction du statut
  const getStatusColorClass = (status) => {
    switch (status) {
      case 'success':
        return 'boss-bg-green-100 boss-text-green-800';
      case 'error':
        return 'boss-bg-red-100 boss-text-red-800';
      case 'not_found':
        return 'boss-bg-yellow-100 boss-text-yellow-800';
      default:
        return 'boss-bg-gray-100 boss-text-gray-800';
    }
  };
  
  // Filtrer les logs
  const filteredLogs = logs.filter(log => {
    // Filtrer par statut
    const matchesStatus = filterStatus === 'all' || log.status === filterStatus;
    
    // Filtrer par recherche
    const matchesSearch = searchQuery === '' || 
      log.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.userAgent.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.ip.includes(searchQuery);
    
    return matchesStatus && matchesSearch;
  });
  
  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredLogs.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);
  
  // Fonction pour changer de page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  
  // Fonction pour aller à la page précédente
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  // Fonction pour aller à la page suivante
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <Card>
      <CardHeader className="boss-border-b boss-border-gray-200">
        <div className="boss-flex boss-justify-between boss-items-center">
          <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
            {__('Logs de redirections', 'boss-seo')}
          </h2>
          <Button
            isDestructive
            onClick={onClearLogs}
          >
            {__('Effacer les logs', 'boss-seo')}
          </Button>
        </div>
      </CardHeader>
      <CardBody>
        <div className="boss-flex boss-flex-col md:boss-flex-row boss-gap-4 boss-mb-6">
          <TextControl
            placeholder={__('Rechercher dans les logs...', 'boss-seo')}
            value={searchQuery}
            onChange={setSearchQuery}
            className="boss-flex-1"
          />
          
          <SelectControl
            label=""
            value={filterStatus}
            options={[
              { label: __('Tous les statuts', 'boss-seo'), value: 'all' },
              { label: __('Succès', 'boss-seo'), value: 'success' },
              { label: __('Erreur', 'boss-seo'), value: 'error' },
              { label: __('Non trouvé', 'boss-seo'), value: 'not_found' }
            ]}
            onChange={setFilterStatus}
            className="boss-w-48"
          />
        </div>
        
        {isLoading ? (
          <div className="boss-flex boss-justify-center boss-items-center boss-p-12">
            <Spinner />
          </div>
        ) : (
          <div className="boss-overflow-x-auto">
            <table className="boss-min-w-full boss-divide-y boss-divide-gray-200">
              <thead className="boss-bg-gray-50">
                <tr>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Date', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('URL', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('Statut', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('IP', 'boss-seo')}
                  </th>
                  <th className="boss-px-6 boss-py-3 boss-text-left boss-text-xs boss-font-medium boss-text-boss-gray boss-uppercase boss-tracking-wider">
                    {__('User Agent', 'boss-seo')}
                  </th>
                </tr>
              </thead>
              <tbody className="boss-bg-white boss-divide-y boss-divide-gray-200">
                {currentItems.length === 0 ? (
                  <tr>
                    <td colSpan="5" className="boss-px-6 boss-py-4 boss-text-center boss-text-boss-gray">
                      {__('Aucun log trouvé.', 'boss-seo')}
                    </td>
                  </tr>
                ) : (
                  currentItems.map((log, index) => (
                    <tr key={index} className="boss-hover:boss-bg-gray-50">
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{log.date}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4">
                        <div className="boss-font-medium boss-text-boss-dark">{log.url}</div>
                        {log.redirectedTo && (
                          <div className="boss-text-sm boss-text-boss-gray boss-flex boss-items-center">
                            <Dashicon icon="arrow-right-alt" className="boss-mr-1" />
                            {log.redirectedTo}
                          </div>
                        )}
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <span className={`boss-px-2 boss-py-1 boss-text-xs boss-rounded-full ${getStatusColorClass(log.status)}`}>
                          {getStatusText(log.status)}
                        </span>
                      </td>
                      <td className="boss-px-6 boss-py-4 boss-whitespace-nowrap">
                        <div className="boss-text-boss-gray">{log.ip}</div>
                      </td>
                      <td className="boss-px-6 boss-py-4">
                        <div className="boss-text-boss-gray boss-truncate boss-max-w-xs">{log.userAgent}</div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </CardBody>
      {totalPages > 1 && (
        <CardFooter className="boss-border-t boss-border-gray-200">
          <div className="boss-flex boss-justify-between boss-items-center">
            <div className="boss-text-boss-gray boss-text-sm">
              {__('Affichage de', 'boss-seo')} {indexOfFirstItem + 1} {__('à', 'boss-seo')} {Math.min(indexOfLastItem, filteredLogs.length)} {__('sur', 'boss-seo')} {filteredLogs.length} {__('entrées', 'boss-seo')}
            </div>
            <div className="boss-flex boss-space-x-2">
              <Button
                isSecondary
                isSmall
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
              >
                <Dashicon icon="arrow-left-alt2" />
              </Button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(number => (
                <Button
                  key={number}
                  isSecondary
                  isSmall
                  isPrimary={currentPage === number}
                  onClick={() => paginate(number)}
                >
                  {number}
                </Button>
              ))}
              <Button
                isSecondary
                isSmall
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
              >
                <Dashicon icon="arrow-right-alt2" />
              </Button>
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
};

export default RedirectionLogs;
