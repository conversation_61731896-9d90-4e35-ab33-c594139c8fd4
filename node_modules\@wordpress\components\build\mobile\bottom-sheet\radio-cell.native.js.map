{"version": 3, "names": ["_i18n", "require", "_icons", "_compose", "_cell", "_interopRequireDefault", "_styles", "BottomSheetRadioCell", "props", "selected", "cellProps", "selectedIconStyle", "usePreferredColorSchemeStyle", "styles", "selectedIcon", "selectedIconDark", "_react", "createElement", "default", "accessibilityRole", "accessibilityState", "accessibilityHint", "__", "editable", "value", "showLockIcon", "Icon", "icon", "check", "style"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/radio-cell.native.js"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { Icon, check } from '@wordpress/icons';\nimport { usePreferredColorSchemeStyle } from '@wordpress/compose';\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport styles from './styles.scss';\n\nexport default function BottomSheetRadioCell( props ) {\n\tconst { selected, ...cellProps } = props;\n\n\tconst selectedIconStyle = usePreferredColorSchemeStyle(\n\t\tstyles.selectedIcon,\n\t\tstyles.selectedIconDark\n\t);\n\n\treturn (\n\t\t<Cell\n\t\t\t{ ...cellProps }\n\t\t\taccessibilityRole={ 'radio' }\n\t\t\taccessibilityState={ { selected } }\n\t\t\taccessibilityHint={\n\t\t\t\t/* translators: accessibility text (hint for selecting option) */\n\t\t\t\t__( 'Double tap to select the option' )\n\t\t\t}\n\t\t\teditable={ false }\n\t\t\tvalue={ '' }\n\t\t\tshowLockIcon={ selected }\n\t\t>\n\t\t\t{ selected && (\n\t\t\t\t<Icon icon={ check } style={ selectedIconStyle }></Icon>\n\t\t\t) }\n\t\t</Cell>\n\t);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAIA,IAAAG,KAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,OAAA,GAAAD,sBAAA,CAAAJ,OAAA;AAVA;AACA;AACA;;AAIA;AACA;AACA;;AAIe,SAASM,oBAAoBA,CAAEC,KAAK,EAAG;EACrD,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAU,CAAC,GAAGF,KAAK;EAExC,MAAMG,iBAAiB,GAAG,IAAAC,qCAA4B,EACrDC,eAAM,CAACC,YAAY,EACnBD,eAAM,CAACE,gBACR,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACb,KAAA,CAAAc,OAAI;IAAA,GACCR,SAAS;IACdS,iBAAiB,EAAG,OAAS;IAC7BC,kBAAkB,EAAG;MAAEX;IAAS,CAAG;IACnCY,iBAAiB,EAChB;IACA,IAAAC,QAAE,EAAE,iCAAkC,CACtC;IACDC,QAAQ,EAAG,KAAO;IAClBC,KAAK,EAAG,EAAI;IACZC,YAAY,EAAGhB;EAAU,GAEvBA,QAAQ,IACT,IAAAO,MAAA,CAAAC,aAAA,EAACf,MAAA,CAAAwB,IAAI;IAACC,IAAI,EAAGC,YAAO;IAACC,KAAK,EAAGlB;EAAmB,CAAO,CAEnD,CAAC;AAET"}