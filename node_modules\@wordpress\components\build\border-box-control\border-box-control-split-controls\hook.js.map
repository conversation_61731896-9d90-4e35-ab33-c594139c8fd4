{"version": 3, "names": ["_element", "require", "styles", "_interopRequireWildcard", "_context", "_utils", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useBorderBoxControlSplitControls", "props", "className", "colors", "enableAlpha", "enableStyle", "size", "__experimentalIsRenderedInSidebar", "otherProps", "useContextSystem", "cx", "useCx", "classes", "useMemo", "borderBoxControlSplitControls", "centeredClassName", "centeredBorderControl", "rightAlignedClassName", "rightBorderControl"], "sources": ["@wordpress/components/src/border-box-control/border-box-control-split-controls/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport * as styles from '../styles';\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useCx } from '../../utils/';\n\nimport type { SplitControlsProps } from '../types';\n\nexport function useBorderBoxControlSplitControls(\n\tprops: WordPressComponentProps< SplitControlsProps, 'div' >\n) {\n\tconst {\n\t\tclassName,\n\t\tcolors = [],\n\t\tenableAlpha = false,\n\t\tenableStyle = true,\n\t\tsize = 'default',\n\t\t__experimentalIsRenderedInSidebar = false,\n\t\t...otherProps\n\t} = useContextSystem( props, 'BorderBoxControlSplitControls' );\n\n\t// Generate class names.\n\tconst cx = useCx();\n\tconst classes = useMemo( () => {\n\t\treturn cx( styles.borderBoxControlSplitControls( size ), className );\n\t}, [ cx, className, size ] );\n\n\tconst centeredClassName = useMemo( () => {\n\t\treturn cx( styles.centeredBorderControl, className );\n\t}, [ cx, className ] );\n\n\tconst rightAlignedClassName = useMemo( () => {\n\t\treturn cx( styles.rightBorderControl(), className );\n\t}, [ cx, className ] );\n\n\treturn {\n\t\t...otherProps,\n\t\tcenteredClassName,\n\t\tclassName: classes,\n\t\tcolors,\n\t\tenableAlpha,\n\t\tenableStyle,\n\t\trightAlignedClassName,\n\t\tsize,\n\t\t__experimentalIsRenderedInSidebar,\n\t};\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAqC,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAXrC;AACA;AACA;;AAGA;AACA;AACA;;AAQO,SAASW,gCAAgCA,CAC/CC,KAA2D,EAC1D;EACD,MAAM;IACLC,SAAS;IACTC,MAAM,GAAG,EAAE;IACXC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAG,IAAI;IAClBC,IAAI,GAAG,SAAS;IAChBC,iCAAiC,GAAG,KAAK;IACzC,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAER,KAAK,EAAE,+BAAgC,CAAC;;EAE9D;EACA,MAAMS,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,OAAOH,EAAE,CAAEpC,MAAM,CAACwC,6BAA6B,CAAER,IAAK,CAAC,EAAEJ,SAAU,CAAC;EACrE,CAAC,EAAE,CAAEQ,EAAE,EAAER,SAAS,EAAEI,IAAI,CAAG,CAAC;EAE5B,MAAMS,iBAAiB,GAAG,IAAAF,gBAAO,EAAE,MAAM;IACxC,OAAOH,EAAE,CAAEpC,MAAM,CAAC0C,qBAAqB,EAAEd,SAAU,CAAC;EACrD,CAAC,EAAE,CAAEQ,EAAE,EAAER,SAAS,CAAG,CAAC;EAEtB,MAAMe,qBAAqB,GAAG,IAAAJ,gBAAO,EAAE,MAAM;IAC5C,OAAOH,EAAE,CAAEpC,MAAM,CAAC4C,kBAAkB,CAAC,CAAC,EAAEhB,SAAU,CAAC;EACpD,CAAC,EAAE,CAAEQ,EAAE,EAAER,SAAS,CAAG,CAAC;EAEtB,OAAO;IACN,GAAGM,UAAU;IACbO,iBAAiB;IACjBb,SAAS,EAAEU,OAAO;IAClBT,MAAM;IACNC,WAAW;IACXC,WAAW;IACXY,qBAAqB;IACrBX,IAAI;IACJC;EACD,CAAC;AACF"}