{"version": 3, "names": ["_element", "require", "_date", "_interopRequireDefault", "_time", "_styles", "noop", "UnforwardedDateTimePicker", "currentDate", "is12Hour", "isInvalidDate", "onMonthPreviewed", "onChange", "events", "startOfWeek", "ref", "_react", "createElement", "Wrapper", "className", "spacing", "Fragment", "default", "currentTime", "DateTimePicker", "forwardRef", "exports", "_default"], "sources": ["@wordpress/components/src/date-time/date-time/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { forwardRef } from '@wordpress/element';\nimport { __, _x } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport { default as DatePicker } from '../date';\nimport { default as TimePicker } from '../time';\nimport type { DateTimePickerProps } from '../types';\nimport { Wrapper } from './styles';\n\nexport { DatePicker, TimePicker };\n\nconst noop = () => {};\n\nfunction UnforwardedDateTimePicker(\n\t{\n\t\tcurrentDate,\n\t\tis12Hour,\n\t\tisInvalidDate,\n\t\tonMonthPreviewed = noop,\n\t\tonChange,\n\t\tevents,\n\t\tstartOfWeek,\n\t}: DateTimePickerProps,\n\tref: ForwardedRef< any >\n) {\n\treturn (\n\t\t<Wrapper ref={ ref } className=\"components-datetime\" spacing={ 4 }>\n\t\t\t<>\n\t\t\t\t<TimePicker\n\t\t\t\t\tcurrentTime={ currentDate }\n\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\tis12Hour={ is12Hour }\n\t\t\t\t/>\n\t\t\t\t<DatePicker\n\t\t\t\t\tcurrentDate={ currentDate }\n\t\t\t\t\tonChange={ onChange }\n\t\t\t\t\tisInvalidDate={ isInvalidDate }\n\t\t\t\t\tevents={ events }\n\t\t\t\t\tonMonthPreviewed={ onMonthPreviewed }\n\t\t\t\t\tstartOfWeek={ startOfWeek }\n\t\t\t\t/>\n\t\t\t</>\n\t\t</Wrapper>\n\t);\n}\n\n/**\n * DateTimePicker is a React component that renders a calendar and clock for\n * date and time selection. The calendar and clock components can be accessed\n * individually using the `DatePicker` and `TimePicker` components respectively.\n *\n * ```jsx\n * import { DateTimePicker } from '@wordpress/components';\n * import { useState } from '@wordpress/element';\n *\n * const MyDateTimePicker = () => {\n *   const [ date, setDate ] = useState( new Date() );\n *\n *   return (\n *     <DateTimePicker\n *       currentDate={ date }\n *       onChange={ ( newDate ) => setDate( newDate ) }\n *       is12Hour\n *     />\n *   );\n * };\n * ```\n */\nexport const DateTimePicker = forwardRef( UnforwardedDateTimePicker );\n\nexport default DateTimePicker;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AAMA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAAI,OAAA,GAAAJ,OAAA;AAjBA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAQA,MAAMK,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAErB,SAASC,yBAAyBA,CACjC;EACCC,WAAW;EACXC,QAAQ;EACRC,aAAa;EACbC,gBAAgB,GAAGL,IAAI;EACvBM,QAAQ;EACRC,MAAM;EACNC;AACoB,CAAC,EACtBC,GAAwB,EACvB;EACD,OACC,IAAAC,MAAA,CAAAC,aAAA,EAACZ,OAAA,CAAAa,OAAO;IAACH,GAAG,EAAGA,GAAK;IAACI,SAAS,EAAC,qBAAqB;IAACC,OAAO,EAAG;EAAG,GACjE,IAAAJ,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAK,QAAA,QACC,IAAAL,MAAA,CAAAC,aAAA,EAACb,KAAA,CAAAkB,OAAU;IACVC,WAAW,EAAGf,WAAa;IAC3BI,QAAQ,EAAGA,QAAU;IACrBH,QAAQ,EAAGA;EAAU,CACrB,CAAC,EACF,IAAAO,MAAA,CAAAC,aAAA,EAACf,KAAA,CAAAoB,OAAU;IACVd,WAAW,EAAGA,WAAa;IAC3BI,QAAQ,EAAGA,QAAU;IACrBF,aAAa,EAAGA,aAAe;IAC/BG,MAAM,EAAGA,MAAQ;IACjBF,gBAAgB,EAAGA,gBAAkB;IACrCG,WAAW,EAAGA;EAAa,CAC3B,CACA,CACM,CAAC;AAEZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMU,cAAc,GAAG,IAAAC,mBAAU,EAAElB,yBAA0B,CAAC;AAACmB,OAAA,CAAAF,cAAA,GAAAA,cAAA;AAAA,IAAAG,QAAA,GAEvDH,cAAc;AAAAE,OAAA,CAAAJ,OAAA,GAAAK,QAAA"}