"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _element = require("@wordpress/element");
var _ = _interopRequireDefault(require("."));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function UnforwardedIconButton({
  label,
  labelPosition,
  size,
  tooltip,
  ...props
}, ref) {
  (0, _deprecated.default)('wp.components.IconButton', {
    since: '5.4',
    alternative: 'wp.components.Button',
    version: '6.2'
  });
  return (0, _react.createElement)(_.default, {
    ...props,
    ref: ref,
    tooltipPosition: labelPosition,
    iconSize: size,
    showTooltip: tooltip !== undefined ? !!tooltip : undefined,
    label: tooltip || label
  });
}
var _default = (0, _element.forwardRef)(UnforwardedIconButton);
exports.default = _default;
//# sourceMappingURL=deprecated.js.map