"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _i18n = require("@wordpress/i18n");
var _customSelectControl = _interopRequireDefault(require("../custom-select-control"));
var _unitControl = require("../unit-control");
var _utils = require("./utils");
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const DEFAULT_OPTION = {
  key: 'default',
  name: (0, _i18n.__)('Default'),
  value: undefined
};
const CUSTOM_OPTION = {
  key: 'custom',
  name: (0, _i18n.__)('Custom')
};
const FontSizePickerSelect = props => {
  var _options$find;
  const {
    __next40pxDefaultSize,
    fontSizes,
    value,
    disableCustomFontSizes,
    size,
    onChange,
    onSelectCustom
  } = props;
  const areAllSizesSameUnit = !!(0, _utils.getCommonSizeUnit)(fontSizes);
  const options = [DEFAULT_OPTION, ...fontSizes.map(fontSize => {
    let hint;
    if (areAllSizesSameUnit) {
      const [quantity] = (0, _unitControl.parseQuantityAndUnitFromRawValue)(fontSize.size);
      if (quantity !== undefined) {
        hint = String(quantity);
      }
    } else if ((0, _utils.isSimpleCssValue)(fontSize.size)) {
      hint = String(fontSize.size);
    }
    return {
      key: fontSize.slug,
      name: fontSize.name || fontSize.slug,
      value: fontSize.size,
      __experimentalHint: hint
    };
  }), ...(disableCustomFontSizes ? [] : [CUSTOM_OPTION])];
  const selectedOption = value ? (_options$find = options.find(option => option.value === value)) !== null && _options$find !== void 0 ? _options$find : CUSTOM_OPTION : DEFAULT_OPTION;
  return (0, _react.createElement)(_customSelectControl.default, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    __nextUnconstrainedWidth: true,
    className: "components-font-size-picker__select",
    label: (0, _i18n.__)('Font size'),
    hideLabelFromVision: true,
    describedBy: (0, _i18n.sprintf)(
    // translators: %s: Currently selected font size.
    (0, _i18n.__)('Currently selected font size: %s'), selectedOption.name),
    options: options,
    value: selectedOption,
    __experimentalShowSelectedHint: true,
    onChange: ({
      selectedItem
    }) => {
      if (selectedItem === CUSTOM_OPTION) {
        onSelectCustom();
      } else {
        onChange(selectedItem.value);
      }
    },
    size: size
  });
};
var _default = FontSizePickerSelect;
exports.default = _default;
//# sourceMappingURL=font-size-picker-select.js.map