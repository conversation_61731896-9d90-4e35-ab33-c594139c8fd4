{"version": 3, "names": ["_reactNative", "require", "_slider", "_interopRequireDefault", "_i18n", "_element", "_compose", "_cell", "_lockIcon", "_rangeCell", "_rangeTextInput", "_utils", "isIOS", "Platform", "OS", "BottomSheetRangeCell", "Component", "constructor", "props", "onSliderChange", "bind", "onCompleteSliderChange", "onTextInputChange", "a11yIncrementValue", "a11yDecrementValue", "a11yUpdateValue", "value", "defaultValue", "minimumValue", "initialValue", "Number", "state", "inputValue", "slider<PERSON><PERSON><PERSON>", "componentWillUnmount", "clearTimeout", "timeoutAnnounceValue", "decimalNum", "onChange", "toFixed", "setState", "nextValue", "onComplete", "step", "maximumValue", "newValue", "undefined", "announceValue", "label", "unitLabel", "setTimeout", "AccessibilityInfo", "announceForAccessibility", "render", "disabled", "preferredColorScheme", "minimumTrackTintColor", "maximumTrackTintColor", "thumbTintColor", "preview", "cellContainerStyle", "shouldDisplayTextInput", "<PERSON><PERSON><PERSON><PERSON>", "openUnitPicker", "children", "cellProps", "getAccessibilityHint", "__", "getAccessibilityLabel", "sprintf", "containerStyle", "styles", "container", "containerIOS", "containerAndroid", "_react", "createElement", "View", "accessible", "accessibilityRole", "accessibilityActions", "name", "onAccessibilityAction", "event", "nativeEvent", "actionName", "accessibilityLabel", "accessibilityHint", "importantForAccessibility", "default", "cellContainerStyles", "cellRowContainerStyle", "leftAlign", "editable", "activeOpacity", "valueStyle", "showLockIcon", "style", "testID", "onValueChange", "onSlidingComplete", "ref", "slider", "sliderRef", "sliderIOS", "sliderAndroid", "min", "max", "_default", "withPreferredColorScheme", "exports"], "sources": ["@wordpress/components/src/mobile/bottom-sheet/range-cell.native.js"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { Platform, AccessibilityInfo, View } from 'react-native';\nimport Slider from '@react-native-community/slider';\n\n/**\n * WordPress dependencies\n */\nimport { __, sprintf } from '@wordpress/i18n';\nimport { Component } from '@wordpress/element';\nimport { withPreferredColorScheme } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport Cell from './cell';\nimport LockIcon from './lock-icon';\nimport styles from './range-cell.scss';\nimport RangeTextInput from './range-text-input';\nimport { toFixed } from '../utils';\n\nconst isIOS = Platform.OS === 'ios';\n\nclass BottomSheetRangeCell extends Component {\n\tconstructor( props ) {\n\t\tsuper( props );\n\t\tthis.onSliderChange = this.onSliderChange.bind( this );\n\t\tthis.onCompleteSliderChange = this.onCompleteSliderChange.bind( this );\n\t\tthis.onTextInputChange = this.onTextInputChange.bind( this );\n\t\tthis.a11yIncrementValue = this.a11yIncrementValue.bind( this );\n\t\tthis.a11yDecrementValue = this.a11yDecrementValue.bind( this );\n\t\tthis.a11yUpdateValue = this.a11yUpdateValue.bind( this );\n\n\t\tconst { value, defaultValue, minimumValue } = props;\n\t\tconst initialValue = Number( value || defaultValue || minimumValue );\n\n\t\tthis.state = {\n\t\t\tinputValue: initialValue,\n\t\t\tsliderValue: initialValue,\n\t\t};\n\t}\n\n\tcomponentWillUnmount() {\n\t\tclearTimeout( this.timeoutAnnounceValue );\n\t}\n\n\tonSliderChange( initialValue ) {\n\t\tconst { decimalNum, onChange } = this.props;\n\t\tinitialValue = toFixed( initialValue, decimalNum );\n\t\tthis.setState( { inputValue: initialValue } );\n\t\tonChange( initialValue );\n\t}\n\n\tonTextInputChange( nextValue ) {\n\t\tconst { onChange, onComplete } = this.props;\n\t\tthis.setState( {\n\t\t\tsliderValue: nextValue,\n\t\t} );\n\t\tonChange( nextValue );\n\t\tif ( onComplete ) {\n\t\t\tonComplete( nextValue );\n\t\t}\n\t}\n\n\tonCompleteSliderChange( nextValue ) {\n\t\tconst { decimalNum, onComplete } = this.props;\n\t\tnextValue = toFixed( nextValue, decimalNum );\n\t\tif ( onComplete ) {\n\t\t\tonComplete( nextValue );\n\t\t}\n\t}\n\n\t/*\n\t * Only used with screenreaders like VoiceOver and TalkBack. Increments the\n\t * value of this setting programmatically.\n\t */\n\ta11yIncrementValue() {\n\t\tconst { step = 5, maximumValue, decimalNum } = this.props;\n\t\tconst { inputValue } = this.state;\n\n\t\tconst newValue = toFixed( inputValue + step, decimalNum );\n\n\t\tif ( newValue <= maximumValue || maximumValue === undefined ) {\n\t\t\tthis.a11yUpdateValue( newValue );\n\t\t}\n\t}\n\n\t/*\n\t * Only used with screenreaders like VoiceOver and TalkBack. Decrements the\n\t * value of this setting programmatically.\n\t */\n\ta11yDecrementValue() {\n\t\tconst { step = 5, minimumValue, decimalNum } = this.props;\n\t\tconst { sliderValue } = this.state;\n\n\t\tconst newValue = toFixed( sliderValue - step, decimalNum );\n\n\t\tif ( newValue >= minimumValue ) {\n\t\t\tthis.a11yUpdateValue( newValue );\n\t\t}\n\t}\n\n\ta11yUpdateValue( newValue ) {\n\t\tconst { onChange, onComplete } = this.props;\n\t\tthis.setState( {\n\t\t\tsliderValue: newValue,\n\t\t\tinputValue: newValue,\n\t\t} );\n\t\tonChange( newValue );\n\t\tif ( onComplete ) {\n\t\t\tonComplete( newValue );\n\t\t}\n\t\tthis.announceValue( newValue );\n\t}\n\n\t/*\n\t * Only used with screenreaders like VoiceOver and TalkBack.\n\t */\n\tannounceValue( value ) {\n\t\tconst { label, unitLabel = '' } = this.props;\n\n\t\tif ( isIOS ) {\n\t\t\t// On Android it triggers the accessibilityLabel with the value change, but\n\t\t\t// on iOS we need to do this manually.\n\t\t\tclearTimeout( this.timeoutAnnounceValue );\n\t\t\tthis.timeoutAnnounceValue = setTimeout( () => {\n\t\t\t\tAccessibilityInfo.announceForAccessibility(\n\t\t\t\t\t`${ value } ${ unitLabel },  ${ label }`\n\t\t\t\t);\n\t\t\t}, 300 );\n\t\t}\n\t}\n\n\trender() {\n\t\tconst {\n\t\t\tvalue,\n\t\t\tdefaultValue,\n\t\t\tminimumValue = 0,\n\t\t\tmaximumValue = 10,\n\t\t\tdisabled,\n\t\t\tstep = 1,\n\t\t\tpreferredColorScheme,\n\t\t\tminimumTrackTintColor = preferredColorScheme === 'light'\n\t\t\t\t? '#00669b'\n\t\t\t\t: '#5198d9',\n\t\t\tmaximumTrackTintColor = isIOS ? '#e9eff3' : '#909090',\n\t\t\tthumbTintColor = ! isIOS && '#00669b',\n\t\t\tpreview,\n\t\t\tcellContainerStyle,\n\t\t\tshouldDisplayTextInput = true,\n\t\t\tunitLabel = '',\n\t\t\tsettingLabel = 'Value',\n\t\t\topenUnitPicker,\n\t\t\tchildren,\n\t\t\tdecimalNum,\n\t\t\t...cellProps\n\t\t} = this.props;\n\n\t\tconst { inputValue, sliderValue } = this.state;\n\n\t\tconst getAccessibilityHint = () => {\n\t\t\treturn openUnitPicker ? __( 'double-tap to change unit' ) : '';\n\t\t};\n\n\t\tconst getAccessibilityLabel = () => {\n\t\t\treturn sprintf(\n\t\t\t\t/* translators: accessibility text. Inform about current value. %1$s: Control label %2$s: setting label (example: width), %3$s: Current value. %4$s: value measurement unit (example: pixels) */\n\t\t\t\t__( '%1$s. %2$s is %3$s %4$s.' ),\n\t\t\t\tcellProps.label,\n\t\t\t\tsettingLabel,\n\t\t\t\ttoFixed( value, decimalNum ),\n\t\t\t\tunitLabel\n\t\t\t);\n\t\t};\n\n\t\tconst containerStyle = [\n\t\t\tstyles.container,\n\t\t\tisIOS ? styles.containerIOS : styles.containerAndroid,\n\t\t];\n\n\t\treturn (\n\t\t\t<View\n\t\t\t\taccessible={ true }\n\t\t\t\taccessibilityRole=\"adjustable\"\n\t\t\t\taccessibilityActions={ [\n\t\t\t\t\t{ name: 'increment' },\n\t\t\t\t\t{ name: 'decrement' },\n\t\t\t\t\t{ name: 'activate' },\n\t\t\t\t] }\n\t\t\t\tonAccessibilityAction={ ( event ) => {\n\t\t\t\t\tswitch ( event.nativeEvent.actionName ) {\n\t\t\t\t\t\tcase 'increment':\n\t\t\t\t\t\t\tthis.a11yIncrementValue();\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 'decrement':\n\t\t\t\t\t\t\tthis.a11yDecrementValue();\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 'activate':\n\t\t\t\t\t\t\tif ( openUnitPicker ) {\n\t\t\t\t\t\t\t\topenUnitPicker();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t} }\n\t\t\t\taccessibilityLabel={ getAccessibilityLabel() }\n\t\t\t\taccessibilityHint={ getAccessibilityHint() }\n\t\t\t>\n\t\t\t\t<View importantForAccessibility=\"no-hide-descendants\">\n\t\t\t\t\t<Cell\n\t\t\t\t\t\t{ ...cellProps }\n\t\t\t\t\t\tcellContainerStyle={ [\n\t\t\t\t\t\t\tstyles.cellContainerStyles,\n\t\t\t\t\t\t\tcellContainerStyle,\n\t\t\t\t\t\t] }\n\t\t\t\t\t\tcellRowContainerStyle={ containerStyle }\n\t\t\t\t\t\tleftAlign\n\t\t\t\t\t\teditable={ false }\n\t\t\t\t\t\tactiveOpacity={ 1 }\n\t\t\t\t\t\taccessible={ false }\n\t\t\t\t\t\tvalueStyle={ styles.valueStyle }\n\t\t\t\t\t\tdisabled={ disabled }\n\t\t\t\t\t\tshowLockIcon={ false }\n\t\t\t\t\t>\n\t\t\t\t\t\t<View style={ containerStyle }>\n\t\t\t\t\t\t\t{ preview }\n\t\t\t\t\t\t\t<Slider\n\t\t\t\t\t\t\t\ttestID={ `Slider ${ cellProps.label }` }\n\t\t\t\t\t\t\t\tvalue={ sliderValue }\n\t\t\t\t\t\t\t\tdefaultValue={ defaultValue }\n\t\t\t\t\t\t\t\tdisabled={ disabled && ! isIOS }\n\t\t\t\t\t\t\t\tstep={ step }\n\t\t\t\t\t\t\t\tminimumValue={ minimumValue }\n\t\t\t\t\t\t\t\tmaximumValue={ maximumValue }\n\t\t\t\t\t\t\t\tminimumTrackTintColor={ minimumTrackTintColor }\n\t\t\t\t\t\t\t\tmaximumTrackTintColor={ maximumTrackTintColor }\n\t\t\t\t\t\t\t\tthumbTintColor={ thumbTintColor }\n\t\t\t\t\t\t\t\tonValueChange={ this.onSliderChange }\n\t\t\t\t\t\t\t\tonSlidingComplete={\n\t\t\t\t\t\t\t\t\tthis.onCompleteSliderChange\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tref={ ( slider ) => {\n\t\t\t\t\t\t\t\t\tthis.sliderRef = slider;\n\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\tstyle={\n\t\t\t\t\t\t\t\t\tisIOS\n\t\t\t\t\t\t\t\t\t\t? styles.sliderIOS\n\t\t\t\t\t\t\t\t\t\t: styles.sliderAndroid\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{ shouldDisplayTextInput && (\n\t\t\t\t\t\t\t\t<RangeTextInput\n\t\t\t\t\t\t\t\t\tlabel={ cellProps.label }\n\t\t\t\t\t\t\t\t\tonChange={ this.onTextInputChange }\n\t\t\t\t\t\t\t\t\tdefaultValue={ `${ inputValue }` }\n\t\t\t\t\t\t\t\t\tvalue={ inputValue }\n\t\t\t\t\t\t\t\t\tmin={ minimumValue }\n\t\t\t\t\t\t\t\t\tmax={ maximumValue }\n\t\t\t\t\t\t\t\t\tstep={ step }\n\t\t\t\t\t\t\t\t\tdecimalNum={ decimalNum }\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{ children }\n\t\t\t\t\t\t\t\t</RangeTextInput>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t{ disabled && <LockIcon /> }\n\t\t\t\t\t\t</View>\n\t\t\t\t\t</Cell>\n\t\t\t\t</View>\n\t\t\t</View>\n\t\t);\n\t}\n}\n\nexport default withPreferredColorScheme( BottomSheetRangeCell );\n"], "mappings": ";;;;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAKA,IAAAM,KAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,UAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,eAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AApBA;AACA;AACA;;AAIA;AACA;AACA;;AAKA;AACA;AACA;;AAOA,MAAMW,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;AAEnC,MAAMC,oBAAoB,SAASC,kBAAS,CAAC;EAC5CC,WAAWA,CAAEC,KAAK,EAAG;IACpB,KAAK,CAAEA,KAAM,CAAC;IACd,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAE,IAAK,CAAC;IACtD,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACD,IAAI,CAAE,IAAK,CAAC;IACtE,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACF,IAAI,CAAE,IAAK,CAAC;IAC5D,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACH,IAAI,CAAE,IAAK,CAAC;IAC9D,IAAI,CAACI,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACJ,IAAI,CAAE,IAAK,CAAC;IAC9D,IAAI,CAACK,eAAe,GAAG,IAAI,CAACA,eAAe,CAACL,IAAI,CAAE,IAAK,CAAC;IAExD,MAAM;MAAEM,KAAK;MAAEC,YAAY;MAAEC;IAAa,CAAC,GAAGV,KAAK;IACnD,MAAMW,YAAY,GAAGC,MAAM,CAAEJ,KAAK,IAAIC,YAAY,IAAIC,YAAa,CAAC;IAEpE,IAAI,CAACG,KAAK,GAAG;MACZC,UAAU,EAAEH,YAAY;MACxBI,WAAW,EAAEJ;IACd,CAAC;EACF;EAEAK,oBAAoBA,CAAA,EAAG;IACtBC,YAAY,CAAE,IAAI,CAACC,oBAAqB,CAAC;EAC1C;EAEAjB,cAAcA,CAAEU,YAAY,EAAG;IAC9B,MAAM;MAAEQ,UAAU;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACpB,KAAK;IAC3CW,YAAY,GAAG,IAAAU,cAAO,EAAEV,YAAY,EAAEQ,UAAW,CAAC;IAClD,IAAI,CAACG,QAAQ,CAAE;MAAER,UAAU,EAAEH;IAAa,CAAE,CAAC;IAC7CS,QAAQ,CAAET,YAAa,CAAC;EACzB;EAEAP,iBAAiBA,CAAEmB,SAAS,EAAG;IAC9B,MAAM;MAAEH,QAAQ;MAAEI;IAAW,CAAC,GAAG,IAAI,CAACxB,KAAK;IAC3C,IAAI,CAACsB,QAAQ,CAAE;MACdP,WAAW,EAAEQ;IACd,CAAE,CAAC;IACHH,QAAQ,CAAEG,SAAU,CAAC;IACrB,IAAKC,UAAU,EAAG;MACjBA,UAAU,CAAED,SAAU,CAAC;IACxB;EACD;EAEApB,sBAAsBA,CAAEoB,SAAS,EAAG;IACnC,MAAM;MAAEJ,UAAU;MAAEK;IAAW,CAAC,GAAG,IAAI,CAACxB,KAAK;IAC7CuB,SAAS,GAAG,IAAAF,cAAO,EAAEE,SAAS,EAAEJ,UAAW,CAAC;IAC5C,IAAKK,UAAU,EAAG;MACjBA,UAAU,CAAED,SAAU,CAAC;IACxB;EACD;;EAEA;AACD;AACA;AACA;EACClB,kBAAkBA,CAAA,EAAG;IACpB,MAAM;MAAEoB,IAAI,GAAG,CAAC;MAAEC,YAAY;MAAEP;IAAW,CAAC,GAAG,IAAI,CAACnB,KAAK;IACzD,MAAM;MAAEc;IAAW,CAAC,GAAG,IAAI,CAACD,KAAK;IAEjC,MAAMc,QAAQ,GAAG,IAAAN,cAAO,EAAEP,UAAU,GAAGW,IAAI,EAAEN,UAAW,CAAC;IAEzD,IAAKQ,QAAQ,IAAID,YAAY,IAAIA,YAAY,KAAKE,SAAS,EAAG;MAC7D,IAAI,CAACrB,eAAe,CAAEoB,QAAS,CAAC;IACjC;EACD;;EAEA;AACD;AACA;AACA;EACCrB,kBAAkBA,CAAA,EAAG;IACpB,MAAM;MAAEmB,IAAI,GAAG,CAAC;MAAEf,YAAY;MAAES;IAAW,CAAC,GAAG,IAAI,CAACnB,KAAK;IACzD,MAAM;MAAEe;IAAY,CAAC,GAAG,IAAI,CAACF,KAAK;IAElC,MAAMc,QAAQ,GAAG,IAAAN,cAAO,EAAEN,WAAW,GAAGU,IAAI,EAAEN,UAAW,CAAC;IAE1D,IAAKQ,QAAQ,IAAIjB,YAAY,EAAG;MAC/B,IAAI,CAACH,eAAe,CAAEoB,QAAS,CAAC;IACjC;EACD;EAEApB,eAAeA,CAAEoB,QAAQ,EAAG;IAC3B,MAAM;MAAEP,QAAQ;MAAEI;IAAW,CAAC,GAAG,IAAI,CAACxB,KAAK;IAC3C,IAAI,CAACsB,QAAQ,CAAE;MACdP,WAAW,EAAEY,QAAQ;MACrBb,UAAU,EAAEa;IACb,CAAE,CAAC;IACHP,QAAQ,CAAEO,QAAS,CAAC;IACpB,IAAKH,UAAU,EAAG;MACjBA,UAAU,CAAEG,QAAS,CAAC;IACvB;IACA,IAAI,CAACE,aAAa,CAAEF,QAAS,CAAC;EAC/B;;EAEA;AACD;AACA;EACCE,aAAaA,CAAErB,KAAK,EAAG;IACtB,MAAM;MAAEsB,KAAK;MAAEC,SAAS,GAAG;IAAG,CAAC,GAAG,IAAI,CAAC/B,KAAK;IAE5C,IAAKN,KAAK,EAAG;MACZ;MACA;MACAuB,YAAY,CAAE,IAAI,CAACC,oBAAqB,CAAC;MACzC,IAAI,CAACA,oBAAoB,GAAGc,UAAU,CAAE,MAAM;QAC7CC,8BAAiB,CAACC,wBAAwB,CACxC,GAAG1B,KAAO,IAAIuB,SAAW,MAAMD,KAAO,EACxC,CAAC;MACF,CAAC,EAAE,GAAI,CAAC;IACT;EACD;EAEAK,MAAMA,CAAA,EAAG;IACR,MAAM;MACL3B,KAAK;MACLC,YAAY;MACZC,YAAY,GAAG,CAAC;MAChBgB,YAAY,GAAG,EAAE;MACjBU,QAAQ;MACRX,IAAI,GAAG,CAAC;MACRY,oBAAoB;MACpBC,qBAAqB,GAAGD,oBAAoB,KAAK,OAAO,GACrD,SAAS,GACT,SAAS;MACZE,qBAAqB,GAAG7C,KAAK,GAAG,SAAS,GAAG,SAAS;MACrD8C,cAAc,GAAG,CAAE9C,KAAK,IAAI,SAAS;MACrC+C,OAAO;MACPC,kBAAkB;MAClBC,sBAAsB,GAAG,IAAI;MAC7BZ,SAAS,GAAG,EAAE;MACda,YAAY,GAAG,OAAO;MACtBC,cAAc;MACdC,QAAQ;MACR3B,UAAU;MACV,GAAG4B;IACJ,CAAC,GAAG,IAAI,CAAC/C,KAAK;IAEd,MAAM;MAAEc,UAAU;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACF,KAAK;IAE9C,MAAMmC,oBAAoB,GAAGA,CAAA,KAAM;MAClC,OAAOH,cAAc,GAAG,IAAAI,QAAE,EAAE,2BAA4B,CAAC,GAAG,EAAE;IAC/D,CAAC;IAED,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MACnC,OAAO,IAAAC,aAAO,GACb;MACA,IAAAF,QAAE,EAAE,0BAA2B,CAAC,EAChCF,SAAS,CAACjB,KAAK,EACfc,YAAY,EACZ,IAAAvB,cAAO,EAAEb,KAAK,EAAEW,UAAW,CAAC,EAC5BY,SACD,CAAC;IACF,CAAC;IAED,MAAMqB,cAAc,GAAG,CACtBC,kBAAM,CAACC,SAAS,EAChB5D,KAAK,GAAG2D,kBAAM,CAACE,YAAY,GAAGF,kBAAM,CAACG,gBAAgB,CACrD;IAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC5E,YAAA,CAAA6E,IAAI;MACJC,UAAU,EAAG,IAAM;MACnBC,iBAAiB,EAAC,YAAY;MAC9BC,oBAAoB,EAAG,CACtB;QAAEC,IAAI,EAAE;MAAY,CAAC,EACrB;QAAEA,IAAI,EAAE;MAAY,CAAC,EACrB;QAAEA,IAAI,EAAE;MAAW,CAAC,CAClB;MACHC,qBAAqB,EAAKC,KAAK,IAAM;QACpC,QAASA,KAAK,CAACC,WAAW,CAACC,UAAU;UACpC,KAAK,WAAW;YACf,IAAI,CAAC9D,kBAAkB,CAAC,CAAC;YACzB;UACD,KAAK,WAAW;YACf,IAAI,CAACC,kBAAkB,CAAC,CAAC;YACzB;UACD,KAAK,UAAU;YACd,IAAKuC,cAAc,EAAG;cACrBA,cAAc,CAAC,CAAC;YACjB;YACA;QACF;MACD,CAAG;MACHuB,kBAAkB,EAAGlB,qBAAqB,CAAC,CAAG;MAC9CmB,iBAAiB,EAAGrB,oBAAoB,CAAC;IAAG,GAE5C,IAAAS,MAAA,CAAAC,aAAA,EAAC5E,YAAA,CAAA6E,IAAI;MAACW,yBAAyB,EAAC;IAAqB,GACpD,IAAAb,MAAA,CAAAC,aAAA,EAACrE,KAAA,CAAAkF,OAAI;MAAA,GACCxB,SAAS;MACdL,kBAAkB,EAAG,CACpBW,kBAAM,CAACmB,mBAAmB,EAC1B9B,kBAAkB,CAChB;MACH+B,qBAAqB,EAAGrB,cAAgB;MACxCsB,SAAS;MACTC,QAAQ,EAAG,KAAO;MAClBC,aAAa,EAAG,CAAG;MACnBhB,UAAU,EAAG,KAAO;MACpBiB,UAAU,EAAGxB,kBAAM,CAACwB,UAAY;MAChCzC,QAAQ,EAAGA,QAAU;MACrB0C,YAAY,EAAG;IAAO,GAEtB,IAAArB,MAAA,CAAAC,aAAA,EAAC5E,YAAA,CAAA6E,IAAI;MAACoB,KAAK,EAAG3B;IAAgB,GAC3BX,OAAO,EACT,IAAAgB,MAAA,CAAAC,aAAA,EAAC1E,OAAA,CAAAuF,OAAM;MACNS,MAAM,EAAI,UAAUjC,SAAS,CAACjB,KAAO,EAAG;MACxCtB,KAAK,EAAGO,WAAa;MACrBN,YAAY,EAAGA,YAAc;MAC7B2B,QAAQ,EAAGA,QAAQ,IAAI,CAAE1C,KAAO;MAChC+B,IAAI,EAAGA,IAAM;MACbf,YAAY,EAAGA,YAAc;MAC7BgB,YAAY,EAAGA,YAAc;MAC7BY,qBAAqB,EAAGA,qBAAuB;MAC/CC,qBAAqB,EAAGA,qBAAuB;MAC/CC,cAAc,EAAGA,cAAgB;MACjCyC,aAAa,EAAG,IAAI,CAAChF,cAAgB;MACrCiF,iBAAiB,EAChB,IAAI,CAAC/E,sBACL;MACDgF,GAAG,EAAKC,MAAM,IAAM;QACnB,IAAI,CAACC,SAAS,GAAGD,MAAM;MACxB,CAAG;MACHL,KAAK,EACJrF,KAAK,GACF2D,kBAAM,CAACiC,SAAS,GAChBjC,kBAAM,CAACkC;IACV,CACD,CAAC,EACA5C,sBAAsB,IACvB,IAAAc,MAAA,CAAAC,aAAA,EAAClE,eAAA,CAAA+E,OAAc;MACdzC,KAAK,EAAGiB,SAAS,CAACjB,KAAO;MACzBV,QAAQ,EAAG,IAAI,CAAChB,iBAAmB;MACnCK,YAAY,EAAI,GAAGK,UAAY,EAAG;MAClCN,KAAK,EAAGM,UAAY;MACpB0E,GAAG,EAAG9E,YAAc;MACpB+E,GAAG,EAAG/D,YAAc;MACpBD,IAAI,EAAGA,IAAM;MACbN,UAAU,EAAGA;IAAY,GAEvB2B,QACa,CAChB,EACCV,QAAQ,IAAI,IAAAqB,MAAA,CAAAC,aAAA,EAACpE,SAAA,CAAAiF,OAAQ,MAAE,CACpB,CACD,CACD,CACD,CAAC;EAET;AACD;AAAC,IAAAmB,QAAA,GAEc,IAAAC,iCAAwB,EAAE9F,oBAAqB,CAAC;AAAA+F,OAAA,CAAArB,OAAA,GAAAmB,QAAA"}