{"version": 3, "names": ["_i18n", "require", "findSizeBySlug", "sizes", "slug", "find", "size", "exports", "_default", "name", "_x", "default"], "sources": ["@wordpress/components/src/dimension-control/sizes.ts"], "sourcesContent": ["/**\n * Sizes\n *\n * defines the sizes used in dimension controls\n * all hardcoded `size` values are based on the value of\n * the Sass variable `$block-padding` from\n * `packages/block-editor/src/components/dimension-control/sizes.js`.\n */\n\n/**\n * WordPress dependencies\n */\nimport { _x } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport type { Size } from './types';\n\n/**\n * Finds the correct size object from the provided sizes\n * table by size slug (eg: `medium`)\n *\n * @param sizes containing objects for each size definition.\n * @param slug  a string representation of the size (eg: `medium`).\n */\nexport const findSizeBySlug = ( sizes: Size[], slug: string ) =>\n\tsizes.find( ( size ) => slug === size.slug );\n\nexport default [\n\t{\n\t\tname: _x( 'None', 'Size of a UI element' ),\n\t\tslug: 'none',\n\t},\n\t{\n\t\tname: _x( 'Small', 'Size of a UI element' ),\n\t\tslug: 'small',\n\t},\n\t{\n\t\tname: _x( 'Medium', 'Size of a UI element' ),\n\t\tslug: 'medium',\n\t},\n\t{\n\t\tname: _x( 'Large', 'Size of a UI element' ),\n\t\tslug: 'large',\n\t},\n\t{\n\t\tname: _x( 'Extra Large', 'Size of a UI element' ),\n\t\tslug: 'xlarge',\n\t},\n];\n"], "mappings": ";;;;;;AAYA,IAAAA,KAAA,GAAAC,OAAA;AAZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,cAAc,GAAGA,CAAEC,KAAa,EAAEC,IAAY,KAC1DD,KAAK,CAACE,IAAI,CAAIC,IAAI,IAAMF,IAAI,KAAKE,IAAI,CAACF,IAAK,CAAC;AAACG,OAAA,CAAAL,cAAA,GAAAA,cAAA;AAAA,IAAAM,QAAA,GAE/B,CACd;EACCC,IAAI,EAAE,IAAAC,QAAE,EAAE,MAAM,EAAE,sBAAuB,CAAC;EAC1CN,IAAI,EAAE;AACP,CAAC,EACD;EACCK,IAAI,EAAE,IAAAC,QAAE,EAAE,OAAO,EAAE,sBAAuB,CAAC;EAC3CN,IAAI,EAAE;AACP,CAAC,EACD;EACCK,IAAI,EAAE,IAAAC,QAAE,EAAE,QAAQ,EAAE,sBAAuB,CAAC;EAC5CN,IAAI,EAAE;AACP,CAAC,EACD;EACCK,IAAI,EAAE,IAAAC,QAAE,EAAE,OAAO,EAAE,sBAAuB,CAAC;EAC3CN,IAAI,EAAE;AACP,CAAC,EACD;EACCK,IAAI,EAAE,IAAAC,QAAE,EAAE,aAAa,EAAE,sBAAuB,CAAC;EACjDN,IAAI,EAAE;AACP,CAAC,CACD;AAAAG,OAAA,CAAAI,OAAA,GAAAH,QAAA"}