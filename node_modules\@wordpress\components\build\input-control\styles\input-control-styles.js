"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getSizeConfig = exports.fontSizeStyles = exports.Suffix = exports.Root = exports.Prefix = exports.LabelWrapper = exports.Label = exports.Input = exports.Container = exports.BackdropUI = void 0;
var _react = require("react");
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react2 = require("@emotion/react");
var _flex = require("../../flex");
var _text = require("../../text");
var _utils = require("../../utils");
var _space = require("../../utils/space");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
var _ref2 = process.env.NODE_ENV === "production" ? {
  name: "1739oy8",
  styles: "z-index:1"
} : {
  name: "1x8rmag-rootFocusedStyles",
  styles: "z-index:1;label:rootFocusedStyles;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
const rootFocusedStyles = ({
  isFocused
}) => {
  if (!isFocused) return '';
  return _ref2;
};
const Root = ( /*#__PURE__*/0, _base.default)(_flex.Flex, process.env.NODE_ENV === "production" ? {
  target: "em5sgkm7"
} : {
  target: "em5sgkm7",
  label: "Root"
})("box-sizing:border-box;position:relative;border-radius:2px;padding-top:0;", rootFocusedStyles, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.Root = Root;
const containerDisabledStyles = ({
  disabled
}) => {
  const backgroundColor = disabled ? _utils.COLORS.ui.backgroundDisabled : _utils.COLORS.ui.background;
  return /*#__PURE__*/(0, _react2.css)({
    backgroundColor
  }, process.env.NODE_ENV === "production" ? "" : ";label:containerDisabledStyles;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
var _ref = process.env.NODE_ENV === "production" ? {
  name: "1d3w5wq",
  styles: "width:100%"
} : {
  name: "uo2pd2-containerWidthStyles",
  styles: "width:100%;label:containerWidthStyles;",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
};
const containerWidthStyles = ({
  __unstableInputWidth,
  labelPosition
}) => {
  if (!__unstableInputWidth) return _ref;
  if (labelPosition === 'side') return '';
  if (labelPosition === 'edge') {
    return /*#__PURE__*/(0, _react2.css)({
      flex: `0 0 ${__unstableInputWidth}`
    }, process.env.NODE_ENV === "production" ? "" : ";label:containerWidthStyles;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
  }
  return /*#__PURE__*/(0, _react2.css)({
    width: __unstableInputWidth
  }, process.env.NODE_ENV === "production" ? "" : ";label:containerWidthStyles;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const Container = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "em5sgkm6"
} : {
  target: "em5sgkm6",
  label: "Container"
})("align-items:center;box-sizing:border-box;border-radius:inherit;display:flex;flex:1;position:relative;", containerDisabledStyles, " ", containerWidthStyles, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.Container = Container;
const disabledStyles = ({
  disabled
}) => {
  if (!disabled) return '';
  return /*#__PURE__*/(0, _react2.css)({
    color: _utils.COLORS.ui.textDisabled
  }, process.env.NODE_ENV === "production" ? "" : ";label:disabledStyles;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const fontSizeStyles = ({
  inputSize: size
}) => {
  const sizes = {
    default: '13px',
    small: '11px',
    compact: '13px',
    '__unstable-large': '13px'
  };
  const fontSize = sizes[size] || sizes.default;
  const fontSizeMobile = '16px';
  if (!fontSize) return '';
  return /*#__PURE__*/(0, _react2.css)("font-size:", fontSizeMobile, ";@media ( min-width: 600px ){font-size:", fontSize, ";}" + (process.env.NODE_ENV === "production" ? "" : ";label:fontSizeStyles;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
exports.fontSizeStyles = fontSizeStyles;
const getSizeConfig = ({
  inputSize: size,
  __next40pxDefaultSize
}) => {
  // Paddings may be overridden by the custom paddings props.
  const sizes = {
    default: {
      height: 40,
      lineHeight: 1,
      minHeight: 40,
      paddingLeft: (0, _space.space)(4),
      paddingRight: (0, _space.space)(4)
    },
    small: {
      height: 24,
      lineHeight: 1,
      minHeight: 24,
      paddingLeft: (0, _space.space)(2),
      paddingRight: (0, _space.space)(2)
    },
    compact: {
      height: 32,
      lineHeight: 1,
      minHeight: 32,
      paddingLeft: (0, _space.space)(2),
      paddingRight: (0, _space.space)(2)
    },
    '__unstable-large': {
      height: 40,
      lineHeight: 1,
      minHeight: 40,
      paddingLeft: (0, _space.space)(4),
      paddingRight: (0, _space.space)(4)
    }
  };
  if (!__next40pxDefaultSize) {
    sizes.default = sizes.compact;
  }
  return sizes[size] || sizes.default;
};
exports.getSizeConfig = getSizeConfig;
const sizeStyles = props => {
  return /*#__PURE__*/(0, _react2.css)(getSizeConfig(props), process.env.NODE_ENV === "production" ? "" : ";label:sizeStyles;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const customPaddings = ({
  paddingInlineStart,
  paddingInlineEnd
}) => {
  return /*#__PURE__*/(0, _react2.css)({
    paddingInlineStart,
    paddingInlineEnd
  }, process.env.NODE_ENV === "production" ? "" : ";label:customPaddings;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const dragStyles = ({
  isDragging,
  dragCursor
}) => {
  let defaultArrowStyles;
  let activeDragCursorStyles;
  if (isDragging) {
    defaultArrowStyles = /*#__PURE__*/(0, _react2.css)("cursor:", dragCursor, ";user-select:none;&::-webkit-outer-spin-button,&::-webkit-inner-spin-button{-webkit-appearance:none!important;margin:0!important;}" + (process.env.NODE_ENV === "production" ? "" : ";label:defaultArrowStyles;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
  }
  if (isDragging && dragCursor) {
    activeDragCursorStyles = /*#__PURE__*/(0, _react2.css)("&:active{cursor:", dragCursor, ";}" + (process.env.NODE_ENV === "production" ? "" : ";label:activeDragCursorStyles;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
  }
  return /*#__PURE__*/(0, _react2.css)(defaultArrowStyles, " ", activeDragCursorStyles, ";" + (process.env.NODE_ENV === "production" ? "" : ";label:dragStyles;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};

// TODO: Resolve need to use &&& to increase specificity
// https://github.com/WordPress/gutenberg/issues/18483

const Input = (0, _base.default)("input", process.env.NODE_ENV === "production" ? {
  target: "em5sgkm5"
} : {
  target: "em5sgkm5",
  label: "Input"
})("&&&{background-color:transparent;box-sizing:border-box;border:none;box-shadow:none!important;color:", _utils.COLORS.gray[900], ";display:block;font-family:inherit;margin:0;outline:none;width:100%;", dragStyles, " ", disabledStyles, " ", fontSizeStyles, " ", sizeStyles, " ", customPaddings, " &::-webkit-input-placeholder{line-height:normal;}}" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.Input = Input;
const BaseLabel = ( /*#__PURE__*/0, _base.default)(_text.Text, process.env.NODE_ENV === "production" ? {
  target: "em5sgkm4"
} : {
  target: "em5sgkm4",
  label: "BaseLabel"
})("&&&{", _utils.baseLabelTypography, ";box-sizing:border-box;display:block;padding-top:0;padding-bottom:0;max-width:100%;z-index:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
const Label = props => (0, _react.createElement)(BaseLabel, {
  ...props,
  as: "label"
});
exports.Label = Label;
const LabelWrapper = ( /*#__PURE__*/0, _base.default)(_flex.FlexItem, process.env.NODE_ENV === "production" ? {
  target: "em5sgkm3"
} : {
  target: "em5sgkm3",
  label: "LabelWrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "1b6uupn",
  styles: "max-width:calc( 100% - 10px )"
} : {
  name: "1b6uupn",
  styles: "max-width:calc( 100% - 10px )",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.LabelWrapper = LabelWrapper;
const backdropFocusedStyles = ({
  disabled,
  isFocused
}) => {
  let borderColor = isFocused ? _utils.COLORS.ui.borderFocus : _utils.COLORS.ui.border;
  let boxShadow;
  let outline;
  let outlineOffset;
  if (isFocused) {
    boxShadow = _utils.CONFIG.controlBoxShadowFocus;
    // Windows High Contrast mode will show this outline, but not the box-shadow.
    outline = `2px solid transparent`;
    outlineOffset = `-2px`;
  }
  if (disabled) {
    borderColor = _utils.COLORS.ui.borderDisabled;
  }
  return /*#__PURE__*/(0, _react2.css)({
    boxShadow,
    borderColor,
    borderStyle: 'solid',
    borderWidth: 1,
    outline,
    outlineOffset
  }, process.env.NODE_ENV === "production" ? "" : ";label:backdropFocusedStyles;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const BackdropUI = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "em5sgkm2"
} : {
  target: "em5sgkm2",
  label: "BackdropUI"
})("&&&{box-sizing:border-box;border-radius:inherit;bottom:0;left:0;margin:0;padding:0;pointer-events:none;position:absolute;right:0;top:0;", backdropFocusedStyles, " ", (0, _utils.rtl)({
  paddingLeft: 2
}), ";}" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.BackdropUI = BackdropUI;
const Prefix = (0, _base.default)("span", process.env.NODE_ENV === "production" ? {
  target: "em5sgkm1"
} : {
  target: "em5sgkm1",
  label: "Prefix"
})(process.env.NODE_ENV === "production" ? {
  name: "pvvbxf",
  styles: "box-sizing:border-box;display:block"
} : {
  name: "pvvbxf",
  styles: "box-sizing:border-box;display:block",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.Prefix = Prefix;
const Suffix = (0, _base.default)("span", process.env.NODE_ENV === "production" ? {
  target: "em5sgkm0"
} : {
  target: "em5sgkm0",
  label: "Suffix"
})(process.env.NODE_ENV === "production" ? {
  name: "jgf79h",
  styles: "align-items:center;align-self:stretch;box-sizing:border-box;display:flex"
} : {
  name: "jgf79h",
  styles: "align-items:center;align-self:stretch;box-sizing:border-box;display:flex",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.Suffix = Suffix;
//# sourceMappingURL=input-control-styles.js.map