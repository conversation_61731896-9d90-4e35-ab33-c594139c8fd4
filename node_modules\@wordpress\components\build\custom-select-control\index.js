"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StableCustomSelectControl = StableCustomSelectControl;
exports.default = CustomSelectControl;
var _react = require("react");
var _downshift = require("downshift");
var _classnames = _interopRequireDefault(require("classnames"));
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _element = require("@wordpress/element");
var _deprecated = _interopRequireDefault(require("@wordpress/deprecated"));
var _visuallyHidden = require("../visually-hidden");
var _selectControlStyles = require("../select-control/styles/select-control-styles");
var _chevronDown = _interopRequireDefault(require("../select-control/chevron-down"));
var _styles = require("./styles");
var _baseControlStyles = require("../base-control/styles/base-control-styles");
var _useDeprecatedProps = require("../utils/use-deprecated-props");
// @ts-nocheck
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const itemToString = item => item?.name;
// This is needed so that in Windows, where
// the menu does not necessarily open on
// key up/down, you can still switch between
// options with the menu closed.
const stateReducer = ({
  selectedItem
}, {
  type,
  changes,
  props: {
    items
  }
}) => {
  switch (type) {
    case _downshift.useSelect.stateChangeTypes.ToggleButtonKeyDownArrowDown:
      // If we already have a selected item, try to select the next one,
      // without circular navigation. Otherwise, select the first item.
      return {
        selectedItem: items[selectedItem ? Math.min(items.indexOf(selectedItem) + 1, items.length - 1) : 0]
      };
    case _downshift.useSelect.stateChangeTypes.ToggleButtonKeyDownArrowUp:
      // If we already have a selected item, try to select the previous one,
      // without circular navigation. Otherwise, select the last item.
      return {
        selectedItem: items[selectedItem ? Math.max(items.indexOf(selectedItem) - 1, 0) : items.length - 1]
      };
    default:
      return changes;
  }
};
function CustomSelectControl(props) {
  const {
    /** Start opting into the larger default height that will become the default size in a future version. */
    __next40pxDefaultSize = false,
    /** Start opting into the unconstrained width that will become the default in a future version. */
    __nextUnconstrainedWidth = false,
    className,
    hideLabelFromVision,
    label,
    describedBy,
    options: items,
    onChange: onSelectedItemChange,
    /** @type {import('../select-control/types').SelectControlProps.size} */
    size = 'default',
    value: _selectedItem,
    onMouseOver,
    onMouseOut,
    onFocus,
    onBlur,
    __experimentalShowSelectedHint = false
  } = (0, _useDeprecatedProps.useDeprecated36pxDefaultSizeProp)(props, 'wp.components.CustomSelectControl', '6.4');
  const {
    getLabelProps,
    getToggleButtonProps,
    getMenuProps,
    getItemProps,
    isOpen,
    highlightedIndex,
    selectedItem
  } = (0, _downshift.useSelect)({
    initialSelectedItem: items[0],
    items,
    itemToString,
    onSelectedItemChange,
    ...(typeof _selectedItem !== 'undefined' && _selectedItem !== null ? {
      selectedItem: _selectedItem
    } : undefined),
    stateReducer
  });
  const [isFocused, setIsFocused] = (0, _element.useState)(false);
  function handleOnFocus(e) {
    setIsFocused(true);
    onFocus?.(e);
  }
  function handleOnBlur(e) {
    setIsFocused(false);
    onBlur?.(e);
  }
  if (!__nextUnconstrainedWidth) {
    (0, _deprecated.default)('Constrained width styles for wp.components.CustomSelectControl', {
      since: '6.1',
      version: '6.4',
      hint: 'Set the `__nextUnconstrainedWidth` prop to true to start opting into the new styles, which will become the default in a future version'
    });
  }
  function getDescribedBy() {
    if (describedBy) {
      return describedBy;
    }
    if (!selectedItem) {
      return (0, _i18n.__)('No selection');
    }

    // translators: %s: The selected option.
    return (0, _i18n.sprintf)((0, _i18n.__)('Currently selected: %s'), selectedItem.name);
  }
  const menuProps = getMenuProps({
    className: 'components-custom-select-control__menu',
    'aria-hidden': !isOpen
  });
  const onKeyDownHandler = (0, _element.useCallback)(e => {
    e.stopPropagation();
    menuProps?.onKeyDown?.(e);
  }, [menuProps]);

  // We need this here, because the null active descendant is not fully ARIA compliant.
  if (menuProps['aria-activedescendant']?.startsWith('downshift-null')) {
    delete menuProps['aria-activedescendant'];
  }
  return (0, _react.createElement)("div", {
    className: (0, _classnames.default)('components-custom-select-control', className)
  }, hideLabelFromVision ? (0, _react.createElement)(_visuallyHidden.VisuallyHidden, {
    as: "label",
    ...getLabelProps()
  }, label) : /* eslint-disable-next-line jsx-a11y/label-has-associated-control, jsx-a11y/label-has-for */
  (0, _react.createElement)(_baseControlStyles.StyledLabel, {
    ...getLabelProps({
      className: 'components-custom-select-control__label'
    })
  }, label), (0, _react.createElement)(_styles.InputBaseWithBackCompatMinWidth, {
    __next40pxDefaultSize: __next40pxDefaultSize,
    __nextUnconstrainedWidth: __nextUnconstrainedWidth,
    isFocused: isOpen || isFocused,
    __unstableInputWidth: __nextUnconstrainedWidth ? undefined : 'auto',
    labelPosition: __nextUnconstrainedWidth ? undefined : 'top',
    size: size,
    suffix: (0, _react.createElement)(_chevronDown.default, null)
  }, (0, _react.createElement)(_selectControlStyles.Select, {
    onMouseOver: onMouseOver,
    onMouseOut: onMouseOut,
    as: "button",
    onFocus: handleOnFocus,
    onBlur: handleOnBlur,
    selectSize: size,
    __next40pxDefaultSize: __next40pxDefaultSize,
    ...getToggleButtonProps({
      // This is needed because some speech recognition software don't support `aria-labelledby`.
      'aria-label': label,
      'aria-labelledby': undefined,
      className: 'components-custom-select-control__button',
      describedBy: getDescribedBy()
    })
  }, itemToString(selectedItem), __experimentalShowSelectedHint && selectedItem.__experimentalHint && (0, _react.createElement)("span", {
    className: "components-custom-select-control__hint"
  }, selectedItem.__experimentalHint))), (0, _react.createElement)("ul", {
    ...menuProps,
    onKeyDown: onKeyDownHandler
  }, isOpen && items.map((item, index) =>
  // eslint-disable-next-line react/jsx-key
  (0, _react.createElement)("li", {
    ...getItemProps({
      item,
      index,
      key: item.key,
      className: (0, _classnames.default)(item.className, 'components-custom-select-control__item', {
        'is-highlighted': index === highlightedIndex,
        'has-hint': !!item.__experimentalHint,
        'is-next-40px-default-size': __next40pxDefaultSize
      }),
      style: item.style
    })
  }, item.name, item.__experimentalHint && (0, _react.createElement)("span", {
    className: "components-custom-select-control__item-hint"
  }, item.__experimentalHint), item === selectedItem && (0, _react.createElement)(_icons.Icon, {
    icon: _icons.check,
    className: "components-custom-select-control__item-icon"
  })))));
}
function StableCustomSelectControl(props) {
  return (0, _react.createElement)(CustomSelectControl, {
    ...props,
    __experimentalShowSelectedHint: false
  });
}
//# sourceMappingURL=index.js.map