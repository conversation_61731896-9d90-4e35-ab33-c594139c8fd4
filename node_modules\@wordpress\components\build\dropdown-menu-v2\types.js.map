{"version": 3, "names": [], "sources": ["@wordpress/components/src/dropdown-menu-v2/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport type * as Ariakit from '@ariakit/react';\nimport type { Placement } from '@floating-ui/react-dom';\n\nexport interface DropdownMenuContext {\n\t/**\n\t * The ariakit store shared across all DropdownMenu subcomponents.\n\t */\n\tstore: Ariakit.MenuStore;\n\t/**\n\t * The variant used by the underlying menu popover.\n\t */\n\tvariant?: 'toolbar';\n}\n\nexport interface DropdownMenuProps {\n\t/**\n\t * The trigger button.\n\t */\n\ttrigger: React.ReactElement;\n\t/**\n\t * The contents of the dropdown.\n\t */\n\tchildren?: React.ReactNode;\n\t/**\n\t * The open state of the dropdown menu when it is initially rendered. Use when\n\t * not wanting to control its open state.\n\t *\n\t * @default false\n\t */\n\tdefaultOpen?: boolean;\n\t/**\n\t * The controlled open state of the dropdown menu. Must be used in conjunction\n\t * with `onOpenChange`.\n\t */\n\topen?: boolean;\n\t/**\n\t * Event handler called when the open state of the dropdown menu changes.\n\t */\n\tonOpenChange?: ( open: boolean ) => void;\n\t/**\n\t * The modality of the dropdown menu. When set to true, interaction with\n\t * outside elements will be disabled and only menu content will be visible to\n\t * screen readers.\n\t *\n\t * @default true\n\t */\n\tmodal?: boolean;\n\t/**\n\t * The placement of the dropdown menu popover.\n\t *\n\t * @default 'bottom-start' for root-level menus, 'right-start' for nested menus\n\t */\n\tplacement?: Placement;\n\t/**\n\t * The distance between the popover and the anchor element.\n\t *\n\t * @default 8 for root-level menus, 16 for nested menus\n\t */\n\tgutter?: number;\n\t/**\n\t * The skidding of the popover along the anchor element. Can be set to\n\t * negative values to make the popover shift to the opposite side.\n\t *\n\t * @default 0 for root-level menus, -8 for nested menus\n\t */\n\tshift?: number;\n\t/**\n\t * Determines whether the menu popover will be hidden when the user presses\n\t * the Escape key.\n\t *\n\t * @default `( event ) => { event.preventDefault(); return true; }`\n\t */\n\thideOnEscape?:\n\t\t| boolean\n\t\t| ( (\n\t\t\t\tevent: KeyboardEvent | React.KeyboardEvent< Element >\n\t\t  ) => boolean );\n}\n\nexport interface DropdownMenuGroupProps {\n\t/**\n\t * The contents of the dropdown menu group.\n\t */\n\tchildren: React.ReactNode;\n}\n\nexport interface DropdownMenuItemProps {\n\t/**\n\t * The contents of the menu item.\n\t */\n\tchildren: React.ReactNode;\n\t/**\n\t * The contents of the menu item's prefix.\n\t */\n\tprefix?: React.ReactNode;\n\t/**\n\t * The contents of the menu item's suffix.\n\t */\n\tsuffix?: React.ReactNode;\n\t/**\n\t * Whether to hide the parent menu when the item is clicked.\n\t *\n\t * @default true\n\t */\n\thideOnClick?: boolean;\n\t/**\n\t * Determines if the element is disabled.\n\t */\n\tdisabled?: boolean;\n}\n\nexport interface DropdownMenuCheckboxItemProps\n\textends Omit< DropdownMenuItemProps, 'prefix' | 'hideOnClick' > {\n\t/**\n\t * Whether to hide the dropdown menu when the item is clicked.\n\t *\n\t * @default false\n\t */\n\thideOnClick?: boolean;\n\t/**\n\t * The checkbox menu item's name.\n\t */\n\tname: string;\n\t/**\n\t * The checkbox item's value, useful when using multiple checkbox menu items\n\t * associated to the same `name`.\n\t */\n\tvalue?: string;\n\t/**\n\t * The controlled checked state of the checkbox menu item.\n\t */\n\tchecked?: boolean;\n\t/**\n\t * The checked state of the checkbox menu item when it is initially rendered.\n\t * Use when not wanting to control its checked state.\n\t */\n\tdefaultChecked?: boolean;\n\t/**\n\t * Event handler called when the checked state of the checkbox menu item changes.\n\t */\n\tonChange?: ( event: React.ChangeEvent< HTMLInputElement > ) => void;\n}\n\nexport interface DropdownMenuRadioItemProps\n\textends Omit< DropdownMenuItemProps, 'prefix' | 'hideOnClick' > {\n\t/**\n\t * Whether to hide the dropdown menu when the item is clicked.\n\t *\n\t * @default false\n\t */\n\thideOnClick?: boolean;\n\t/**\n\t * The radio item's name.\n\t */\n\tname: string;\n\t/**\n\t * The radio item's value.\n\t */\n\tvalue: string | number;\n\t/**\n\t * The controlled checked state of the radio menu item.\n\t */\n\tchecked?: boolean;\n\t/**\n\t * The checked state of the radio menu item when it is initially rendered.\n\t * Use when not wanting to control its checked state.\n\t */\n\tdefaultChecked?: boolean;\n\t/**\n\t * Event handler called when the checked radio menu item changes.\n\t */\n\tonChange?: ( event: React.ChangeEvent< HTMLInputElement > ) => void;\n}\n\nexport interface DropdownMenuSeparatorProps {}\n"], "mappings": ""}