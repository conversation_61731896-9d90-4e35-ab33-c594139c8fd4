{"version": 3, "names": ["_element", "require", "styles", "_interopRequireWildcard", "_context", "_utils", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "useBorderBoxControlVisualizer", "props", "className", "value", "size", "otherProps", "useContextSystem", "cx", "useCx", "classes", "useMemo", "borderBoxControlVisualizer"], "sources": ["@wordpress/components/src/border-box-control/border-box-control-visualizer/hook.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport * as styles from '../styles';\nimport type { WordPressComponentProps } from '../../context';\nimport { useContextSystem } from '../../context';\nimport { useCx } from '../../utils';\n\nimport type { VisualizerProps } from '../types';\n\nexport function useBorderBoxControlVisualizer(\n\tprops: WordPressComponentProps< VisualizerProps, 'div' >\n) {\n\tconst {\n\t\tclassName,\n\t\tvalue,\n\t\tsize = 'default',\n\t\t...otherProps\n\t} = useContextSystem( props, 'BorderBoxControlVisualizer' );\n\n\t// Generate class names.\n\tconst cx = useCx();\n\tconst classes = useMemo( () => {\n\t\treturn cx(\n\t\t\tstyles.borderBoxControlVisualizer( value, size ),\n\t\t\tclassName\n\t\t);\n\t}, [ cx, className, value, size ] );\n\n\treturn { ...otherProps, className: classes, value };\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,MAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAoC,SAAAK,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAJ,wBAAAQ,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAXpC;AACA;AACA;;AAGA;AACA;AACA;;AAQO,SAASW,6BAA6BA,CAC5CC,KAAwD,EACvD;EACD,MAAM;IACLC,SAAS;IACTC,KAAK;IACLC,IAAI,GAAG,SAAS;IAChB,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAAEL,KAAK,EAAE,4BAA6B,CAAC;;EAE3D;EACA,MAAMM,EAAE,GAAG,IAAAC,YAAK,EAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC9B,OAAOH,EAAE,CACRjC,MAAM,CAACqC,0BAA0B,CAAER,KAAK,EAAEC,IAAK,CAAC,EAChDF,SACD,CAAC;EACF,CAAC,EAAE,CAAEK,EAAE,EAAEL,SAAS,EAAEC,KAAK,EAAEC,IAAI,CAAG,CAAC;EAEnC,OAAO;IAAE,GAAGC,UAAU;IAAEH,SAAS,EAAEO,OAAO;IAAEN;EAAM,CAAC;AACpD"}