{"version": 3, "names": ["_primitives", "require", "PageControlIcon", "_react", "createElement", "SVG", "width", "height", "fill", "xmlns", "Circle", "cx", "cy", "r", "exports"], "sources": ["@wordpress/components/src/guide/icons.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { SVG, Circle } from '@wordpress/primitives';\n\nexport const PageControlIcon = () => (\n\t<SVG width=\"8\" height=\"8\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t<Circle cx=\"4\" cy=\"4\" r=\"4\" />\n\t</SVG>\n);\n"], "mappings": ";;;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AAHA;AACA;AACA;;AAGO,MAAMC,eAAe,GAAGA,CAAA,KAC9B,IAAAC,MAAA,CAAAC,aAAA,EAACJ,WAAA,CAAAK,GAAG;EAACC,KAAK,EAAC,GAAG;EAACC,MAAM,EAAC,GAAG;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC;AAA4B,GACvE,IAAAN,MAAA,CAAAC,aAAA,EAACJ,WAAA,CAAAU,MAAM;EAACC,EAAE,EAAC,GAAG;EAACC,EAAE,EAAC,GAAG;EAACC,CAAC,EAAC;AAAG,CAAE,CACzB,CACL;AAACC,OAAA,CAAAZ,eAAA,GAAAA,eAAA"}