"use strict";Object.defineProperty(exports, "__esModule", {value: true});"use client";



var _F6HPKLO2cjs = require('../__chunks/F6HPKLO2.cjs');


var _KBNYGXWIcjs = require('../__chunks/KBNYGXWI.cjs');



var _AV6KTKLEcjs = require('../__chunks/AV6KTKLE.cjs');

// src/checkbox/checkbox-store.ts
function createCheckboxStore(props = {}) {
  var _a;
  _F6HPKLO2cjs.throwOnConflictingProps.call(void 0, props, props.store);
  const syncState = (_a = props.store) == null ? void 0 : _a.getState();
  const initialState = {
    value: _KBNYGXWIcjs.defaultValue.call(void 0, 
      props.value,
      syncState == null ? void 0 : syncState.value,
      props.defaultValue,
      false
    )
  };
  const checkbox = _F6HPKLO2cjs.createStore.call(void 0, initialState, props.store);
  return _AV6KTKLEcjs.__spreadProps.call(void 0, _AV6KTKLEcjs.__spreadValues.call(void 0, {}, checkbox), {
    setValue: (value) => checkbox.setState("value", value)
  });
}


exports.createCheckboxStore = createCheckboxStore;
