{"version": 3, "names": ["_react", "require", "_EMOTION_STRINGIFIED_CSS_ERROR__", "wrapper", "process", "env", "NODE_ENV", "name", "styles", "map", "toString", "exports"], "sources": ["@wordpress/components/src/confirm-dialog/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\n\n/**\n * The z-index for ConfirmDialog is being set here instead of in\n * packages/base-styles/_z-index.scss, because this component uses\n * emotion instead of sass.\n *\n * ConfirmDialog needs this higher z-index to ensure it renders on top of\n * any parent Popover component.\n */\nexport const wrapper = css`\n\t&& {\n\t\tz-index: 1000001;\n\t}\n`;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAAqC,SAAAC,iCAAA;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,OAAO,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAC,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAAR;AAAA,CAInB;AAACS,OAAA,CAAAR,OAAA,GAAAA,OAAA"}