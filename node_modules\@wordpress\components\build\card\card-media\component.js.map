{"version": 3, "names": ["_context", "require", "_view", "_hook", "UnconnectedCardMedia", "props", "forwardedRef", "cardMediaProps", "useCardMedia", "_react", "createElement", "View", "ref", "CardMedia", "contextConnect", "exports", "_default", "default"], "sources": ["@wordpress/components/src/card/card-media/component.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { WordPressComponentProps } from '../../context';\nimport { contextConnect } from '../../context';\nimport { View } from '../../view';\nimport { useCardMedia } from './hook';\nimport type { MediaProps } from '../types';\n\nfunction UnconnectedCardMedia(\n\tprops: WordPressComponentProps< MediaProps, 'div' >,\n\tforwardedRef: ForwardedRef< any >\n) {\n\tconst cardMediaProps = useCardMedia( props );\n\n\treturn <View { ...cardMediaProps } ref={ forwardedRef } />;\n}\n\n/**\n * `CardMedia` provides a container for full-bleed content within a `Card`,\n * such as images, video, or even just a background color.\n *\n * @example\n * ```jsx\n * import { Card, CardBody, CardMedia } from '@wordpress/components';\n *\n * const Example = () => (\n *  <Card>\n *\t  <CardMedia>\n *\t\t  <img src=\"...\" />\n *    </CardMedia>\n *    <CardBody>...</CardBody>\n *  </Card>\n * );\n * ```\n */\nexport const CardMedia = contextConnect( UnconnectedCardMedia, 'CardMedia' );\n\nexport default CardMedia;\n"], "mappings": ";;;;;;;AASA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAXA;AACA;AACA;;AAGA;AACA;AACA;;AAOA,SAASG,oBAAoBA,CAC5BC,KAAmD,EACnDC,YAAiC,EAChC;EACD,MAAMC,cAAc,GAAG,IAAAC,kBAAY,EAAEH,KAAM,CAAC;EAE5C,OAAO,IAAAI,MAAA,CAAAC,aAAA,EAACR,KAAA,CAAAS,IAAI;IAAA,GAAMJ,cAAc;IAAGK,GAAG,EAAGN;EAAc,CAAE,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,SAAS,GAAG,IAAAC,uBAAc,EAAEV,oBAAoB,EAAE,WAAY,CAAC;AAACW,OAAA,CAAAF,SAAA,GAAAA,SAAA;AAAA,IAAAG,QAAA,GAE9DH,SAAS;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}