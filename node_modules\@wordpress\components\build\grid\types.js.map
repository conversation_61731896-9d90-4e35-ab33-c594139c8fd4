{"version": 3, "names": [], "sources": ["@wordpress/components/src/grid/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties, ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { ResponsiveCSSValue } from '../utils/types';\n\ntype GridAlignment =\n\t| 'bottom'\n\t| 'bottomLeft'\n\t| 'bottomRight'\n\t| 'center'\n\t| 'spaced'\n\t| 'left'\n\t| 'right'\n\t| 'stretch'\n\t| 'top'\n\t| 'topLeft'\n\t| 'topRight';\n\nexport type GridProps = {\n\t/**\n\t * Adjusts the block alignment of children.\n\t */\n\talign?: CSSProperties[ 'alignItems' ];\n\t/**\n\t * Adjusts the horizontal and vertical alignment of children.\n\t */\n\talignment?: GridAlignment;\n\t/**\n\t * The children elements.\n\t */\n\tchildren: ReactNode;\n\t/**\n\t * Adjusts the number of columns of the `Grid`.\n\t *\n\t * @default 2\n\t */\n\tcolumns?: ResponsiveCSSValue< number >;\n\t/**\n\t * Adjusts the `grid-column-gap`.\n\t */\n\tcolumnGap?: CSSProperties[ 'gridColumnGap' ];\n\t/**\n\t * Gap between each child.\n\t *\n\t * @default 3\n\t */\n\tgap?: number;\n\t/**\n\t * Changes the CSS display from `grid` to `inline-grid`.\n\t */\n\tisInline?: boolean;\n\t/**\n\t * Adjusts the inline alignment of children.\n\t */\n\tjustify?: CSSProperties[ 'justifyContent' ];\n\t/**\n\t * Adjusts the `grid-row-gap`.\n\t */\n\trowGap?: CSSProperties[ 'gridRowGap' ];\n\t/**\n\t * Adjusts the number of rows of the `Grid`.\n\t */\n\trows?: ResponsiveCSSValue< number >;\n\t/**\n\t * Adjusts the CSS grid `template-columns`.\n\t */\n\ttemplateColumns?: CSSProperties[ 'gridTemplateColumns' ];\n\t/**\n\t * Adjusts the CSS grid `template-rows`.\n\t */\n\ttemplateRows?: CSSProperties[ 'gridTemplateRows' ];\n};\n"], "mappings": ""}