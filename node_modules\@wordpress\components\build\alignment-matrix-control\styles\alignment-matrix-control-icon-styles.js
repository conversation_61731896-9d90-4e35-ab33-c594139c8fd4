"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Wrapper = exports.Root = exports.Point = exports.Cell = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react = require("@emotion/react");
var _alignmentMatrixControlStyles = require("./alignment-matrix-control-styles");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const rootSize = () => {
  const padding = 1.5;
  const size = 24;
  return /*#__PURE__*/(0, _react.css)({
    gridTemplateRows: `repeat( 3, calc( ${size - padding * 2}px / 3))`,
    padding,
    maxHeight: size,
    maxWidth: size
  }, process.env.NODE_ENV === "production" ? "" : ";label:rootSize;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const rootPointerEvents = ({
  disablePointerEvents
}) => {
  return /*#__PURE__*/(0, _react.css)({
    pointerEvents: disablePointerEvents ? 'none' : undefined
  }, process.env.NODE_ENV === "production" ? "" : ";label:rootPointerEvents;", process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const Wrapper = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "erowt52"
} : {
  target: "erowt52",
  label: "Wrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "ogl07i",
  styles: "box-sizing:border-box;padding:2px"
} : {
  name: "ogl07i",
  styles: "box-sizing:border-box;padding:2px",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.Wrapper = Wrapper;
const Root = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "erowt51"
} : {
  target: "erowt51",
  label: "Root"
})("transform-origin:top left;height:100%;width:100%;", _alignmentMatrixControlStyles.rootBase, ";", rootSize, ";", rootPointerEvents, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.Root = Root;
const pointActive = ({
  isActive
}) => {
  const boxShadow = isActive ? `0 0 0 1px currentColor` : null;
  return /*#__PURE__*/(0, _react.css)("box-shadow:", boxShadow, ";color:currentColor;*:hover>&{color:currentColor;}" + (process.env.NODE_ENV === "production" ? "" : ";label:pointActive;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
};
const Point = (0, _base.default)("span", process.env.NODE_ENV === "production" ? {
  target: "erowt50"
} : {
  target: "erowt50",
  label: "Point"
})("height:2px;width:2px;", _alignmentMatrixControlStyles.pointBase, ";", pointActive, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"));
exports.Point = Point;
const Cell = _alignmentMatrixControlStyles.Cell;
exports.Cell = Cell;
//# sourceMappingURL=alignment-matrix-control-icon-styles.js.map