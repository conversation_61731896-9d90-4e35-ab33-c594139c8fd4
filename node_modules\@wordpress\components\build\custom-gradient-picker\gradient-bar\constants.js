"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MINIMUM_SIGNIFICANT_MOVE = exports.MINIMUM_DISTANCE_BETWEEN_POINTS = exports.MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT = exports.MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_MARKER = exports.KEYBOARD_CONTROL_POINT_VARIATION = exports.INSERT_POINT_WIDTH = exports.GRADIENT_MARKERS_WIDTH = void 0;
const GRADIENT_MARKERS_WIDTH = 16;
exports.GRADIENT_MARKERS_WIDTH = GRADIENT_MARKERS_WIDTH;
const INSERT_POINT_WIDTH = 16;
exports.INSERT_POINT_WIDTH = INSERT_POINT_WIDTH;
const MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT = 10;
exports.MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT = MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT;
const MINIMUM_DISTANCE_BETWEEN_POINTS = 0;
exports.MINIMUM_DISTANCE_BETWEEN_POINTS = MINIMUM_DISTANCE_BETWEEN_POINTS;
const MINIMUM_SIGNIFICANT_MOVE = 5;
exports.MINIMUM_SIGNIFICANT_MOVE = MINIMUM_SIGNIFICANT_MOVE;
const KEYBOARD_CONTROL_POINT_VARIATION = MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_POINT;
exports.KEYBOARD_CONTROL_POINT_VARIATION = KEYBOARD_CONTROL_POINT_VARIATION;
const MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_MARKER = (INSERT_POINT_WIDTH + GRADIENT_MARKERS_WIDTH) / 2;
exports.MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_MARKER = MINIMUM_DISTANCE_BETWEEN_INSERTER_AND_MARKER;
//# sourceMappingURL=constants.js.map