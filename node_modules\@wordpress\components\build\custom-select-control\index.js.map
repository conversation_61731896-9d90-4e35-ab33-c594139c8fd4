{"version": 3, "names": ["_downshift", "require", "_classnames", "_interopRequireDefault", "_icons", "_i18n", "_element", "_deprecated", "_visually<PERSON><PERSON>den", "_selectControlStyles", "_chevronDown", "_styles", "_baseControlStyles", "_useDeprecatedProps", "itemToString", "item", "name", "stateReducer", "selectedItem", "type", "changes", "props", "items", "useSelect", "stateChangeTypes", "ToggleButtonKeyDownArrowDown", "Math", "min", "indexOf", "length", "ToggleButtonKeyDownArrowUp", "max", "CustomSelectControl", "__next40pxDefaultSize", "__nextUnconstrainedWidth", "className", "hideLabelFromVision", "label", "describedBy", "options", "onChange", "onSelectedItemChange", "size", "value", "_selectedItem", "onMouseOver", "onMouseOut", "onFocus", "onBlur", "__experimentalShowSelectedHint", "useDeprecated36pxDefaultSizeProp", "getLabelProps", "getToggleButtonProps", "getMenuProps", "getItemProps", "isOpen", "highlightedIndex", "initialSelectedItem", "undefined", "isFocused", "setIsFocused", "useState", "handleOnFocus", "e", "handleOnBlur", "deprecated", "since", "version", "hint", "getDescribedBy", "__", "sprintf", "menuProps", "onKeyDownHandler", "useCallback", "stopPropagation", "onKeyDown", "startsWith", "_react", "createElement", "classnames", "VisuallyHidden", "as", "StyledLabel", "InputBaseWithBackCompatMinWidth", "__unstableInputWidth", "labelPosition", "suffix", "default", "Select", "selectSize", "__experimentalHint", "map", "index", "key", "style", "Icon", "icon", "check", "StableCustomSelectControl"], "sources": ["@wordpress/components/src/custom-select-control/index.js"], "sourcesContent": ["// @ts-nocheck\n/**\n * External dependencies\n */\nimport { useSelect } from 'downshift';\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport { Icon, check } from '@wordpress/icons';\nimport { __, sprintf } from '@wordpress/i18n';\nimport { useCallback, useState } from '@wordpress/element';\nimport deprecated from '@wordpress/deprecated';\n\n/**\n * Internal dependencies\n */\nimport { VisuallyHidden } from '../visually-hidden';\nimport { Select as SelectControlSelect } from '../select-control/styles/select-control-styles';\nimport SelectControlChevronDown from '../select-control/chevron-down';\nimport { InputBaseWithBackCompatMinWidth } from './styles';\nimport { StyledLabel } from '../base-control/styles/base-control-styles';\nimport { useDeprecated36pxDefaultSizeProp } from '../utils/use-deprecated-props';\n\nconst itemToString = ( item ) => item?.name;\n// This is needed so that in Windows, where\n// the menu does not necessarily open on\n// key up/down, you can still switch between\n// options with the menu closed.\nconst stateReducer = (\n\t{ selectedItem },\n\t{ type, changes, props: { items } }\n) => {\n\tswitch ( type ) {\n\t\tcase useSelect.stateChangeTypes.ToggleButtonKeyDownArrowDown:\n\t\t\t// If we already have a selected item, try to select the next one,\n\t\t\t// without circular navigation. Otherwise, select the first item.\n\t\t\treturn {\n\t\t\t\tselectedItem:\n\t\t\t\t\titems[\n\t\t\t\t\t\tselectedItem\n\t\t\t\t\t\t\t? Math.min(\n\t\t\t\t\t\t\t\t\titems.indexOf( selectedItem ) + 1,\n\t\t\t\t\t\t\t\t\titems.length - 1\n\t\t\t\t\t\t\t  )\n\t\t\t\t\t\t\t: 0\n\t\t\t\t\t],\n\t\t\t};\n\t\tcase useSelect.stateChangeTypes.ToggleButtonKeyDownArrowUp:\n\t\t\t// If we already have a selected item, try to select the previous one,\n\t\t\t// without circular navigation. Otherwise, select the last item.\n\t\t\treturn {\n\t\t\t\tselectedItem:\n\t\t\t\t\titems[\n\t\t\t\t\t\tselectedItem\n\t\t\t\t\t\t\t? Math.max( items.indexOf( selectedItem ) - 1, 0 )\n\t\t\t\t\t\t\t: items.length - 1\n\t\t\t\t\t],\n\t\t\t};\n\t\tdefault:\n\t\t\treturn changes;\n\t}\n};\n\nexport default function CustomSelectControl( props ) {\n\tconst {\n\t\t/** Start opting into the larger default height that will become the default size in a future version. */\n\t\t__next40pxDefaultSize = false,\n\t\t/** Start opting into the unconstrained width that will become the default in a future version. */\n\t\t__nextUnconstrainedWidth = false,\n\t\tclassName,\n\t\thideLabelFromVision,\n\t\tlabel,\n\t\tdescribedBy,\n\t\toptions: items,\n\t\tonChange: onSelectedItemChange,\n\t\t/** @type {import('../select-control/types').SelectControlProps.size} */\n\t\tsize = 'default',\n\t\tvalue: _selectedItem,\n\t\tonMouseOver,\n\t\tonMouseOut,\n\t\tonFocus,\n\t\tonBlur,\n\t\t__experimentalShowSelectedHint = false,\n\t} = useDeprecated36pxDefaultSizeProp(\n\t\tprops,\n\t\t'wp.components.CustomSelectControl',\n\t\t'6.4'\n\t);\n\n\tconst {\n\t\tgetLabelProps,\n\t\tgetToggleButtonProps,\n\t\tgetMenuProps,\n\t\tgetItemProps,\n\t\tisOpen,\n\t\thighlightedIndex,\n\t\tselectedItem,\n\t} = useSelect( {\n\t\tinitialSelectedItem: items[ 0 ],\n\t\titems,\n\t\titemToString,\n\t\tonSelectedItemChange,\n\t\t...( typeof _selectedItem !== 'undefined' && _selectedItem !== null\n\t\t\t? { selectedItem: _selectedItem }\n\t\t\t: undefined ),\n\t\tstateReducer,\n\t} );\n\n\tconst [ isFocused, setIsFocused ] = useState( false );\n\n\tfunction handleOnFocus( e ) {\n\t\tsetIsFocused( true );\n\t\tonFocus?.( e );\n\t}\n\n\tfunction handleOnBlur( e ) {\n\t\tsetIsFocused( false );\n\t\tonBlur?.( e );\n\t}\n\n\tif ( ! __nextUnconstrainedWidth ) {\n\t\tdeprecated(\n\t\t\t'Constrained width styles for wp.components.CustomSelectControl',\n\t\t\t{\n\t\t\t\tsince: '6.1',\n\t\t\t\tversion: '6.4',\n\t\t\t\thint: 'Set the `__nextUnconstrainedWidth` prop to true to start opting into the new styles, which will become the default in a future version',\n\t\t\t}\n\t\t);\n\t}\n\n\tfunction getDescribedBy() {\n\t\tif ( describedBy ) {\n\t\t\treturn describedBy;\n\t\t}\n\n\t\tif ( ! selectedItem ) {\n\t\t\treturn __( 'No selection' );\n\t\t}\n\n\t\t// translators: %s: The selected option.\n\t\treturn sprintf( __( 'Currently selected: %s' ), selectedItem.name );\n\t}\n\n\tconst menuProps = getMenuProps( {\n\t\tclassName: 'components-custom-select-control__menu',\n\t\t'aria-hidden': ! isOpen,\n\t} );\n\n\tconst onKeyDownHandler = useCallback(\n\t\t( e ) => {\n\t\t\te.stopPropagation();\n\t\t\tmenuProps?.onKeyDown?.( e );\n\t\t},\n\t\t[ menuProps ]\n\t);\n\n\t// We need this here, because the null active descendant is not fully ARIA compliant.\n\tif (\n\t\tmenuProps[ 'aria-activedescendant' ]?.startsWith( 'downshift-null' )\n\t) {\n\t\tdelete menuProps[ 'aria-activedescendant' ];\n\t}\n\treturn (\n\t\t<div\n\t\t\tclassName={ classnames(\n\t\t\t\t'components-custom-select-control',\n\t\t\t\tclassName\n\t\t\t) }\n\t\t>\n\t\t\t{ hideLabelFromVision ? (\n\t\t\t\t<VisuallyHidden as=\"label\" { ...getLabelProps() }>\n\t\t\t\t\t{ label }\n\t\t\t\t</VisuallyHidden>\n\t\t\t) : (\n\t\t\t\t/* eslint-disable-next-line jsx-a11y/label-has-associated-control, jsx-a11y/label-has-for */\n\t\t\t\t<StyledLabel\n\t\t\t\t\t{ ...getLabelProps( {\n\t\t\t\t\t\tclassName: 'components-custom-select-control__label',\n\t\t\t\t\t} ) }\n\t\t\t\t>\n\t\t\t\t\t{ label }\n\t\t\t\t</StyledLabel>\n\t\t\t) }\n\t\t\t<InputBaseWithBackCompatMinWidth\n\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t__nextUnconstrainedWidth={ __nextUnconstrainedWidth }\n\t\t\t\tisFocused={ isOpen || isFocused }\n\t\t\t\t__unstableInputWidth={\n\t\t\t\t\t__nextUnconstrainedWidth ? undefined : 'auto'\n\t\t\t\t}\n\t\t\t\tlabelPosition={ __nextUnconstrainedWidth ? undefined : 'top' }\n\t\t\t\tsize={ size }\n\t\t\t\tsuffix={ <SelectControlChevronDown /> }\n\t\t\t>\n\t\t\t\t<SelectControlSelect\n\t\t\t\t\tonMouseOver={ onMouseOver }\n\t\t\t\t\tonMouseOut={ onMouseOut }\n\t\t\t\t\tas=\"button\"\n\t\t\t\t\tonFocus={ handleOnFocus }\n\t\t\t\t\tonBlur={ handleOnBlur }\n\t\t\t\t\tselectSize={ size }\n\t\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\t\t{ ...getToggleButtonProps( {\n\t\t\t\t\t\t// This is needed because some speech recognition software don't support `aria-labelledby`.\n\t\t\t\t\t\t'aria-label': label,\n\t\t\t\t\t\t'aria-labelledby': undefined,\n\t\t\t\t\t\tclassName: 'components-custom-select-control__button',\n\t\t\t\t\t\tdescribedBy: getDescribedBy(),\n\t\t\t\t\t} ) }\n\t\t\t\t>\n\t\t\t\t\t{ itemToString( selectedItem ) }\n\t\t\t\t\t{ __experimentalShowSelectedHint &&\n\t\t\t\t\t\tselectedItem.__experimentalHint && (\n\t\t\t\t\t\t\t<span className=\"components-custom-select-control__hint\">\n\t\t\t\t\t\t\t\t{ selectedItem.__experimentalHint }\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t) }\n\t\t\t\t</SelectControlSelect>\n\t\t\t</InputBaseWithBackCompatMinWidth>\n\t\t\t{ /* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */ }\n\t\t\t<ul { ...menuProps } onKeyDown={ onKeyDownHandler }>\n\t\t\t\t{ isOpen &&\n\t\t\t\t\titems.map( ( item, index ) => (\n\t\t\t\t\t\t// eslint-disable-next-line react/jsx-key\n\t\t\t\t\t\t<li\n\t\t\t\t\t\t\t{ ...getItemProps( {\n\t\t\t\t\t\t\t\titem,\n\t\t\t\t\t\t\t\tindex,\n\t\t\t\t\t\t\t\tkey: item.key,\n\t\t\t\t\t\t\t\tclassName: classnames(\n\t\t\t\t\t\t\t\t\titem.className,\n\t\t\t\t\t\t\t\t\t'components-custom-select-control__item',\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t'is-highlighted':\n\t\t\t\t\t\t\t\t\t\t\tindex === highlightedIndex,\n\t\t\t\t\t\t\t\t\t\t'has-hint': !! item.__experimentalHint,\n\t\t\t\t\t\t\t\t\t\t'is-next-40px-default-size':\n\t\t\t\t\t\t\t\t\t\t\t__next40pxDefaultSize,\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\t\tstyle: item.style,\n\t\t\t\t\t\t\t} ) }\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{ item.name }\n\t\t\t\t\t\t\t{ item.__experimentalHint && (\n\t\t\t\t\t\t\t\t<span className=\"components-custom-select-control__item-hint\">\n\t\t\t\t\t\t\t\t\t{ item.__experimentalHint }\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t{ item === selectedItem && (\n\t\t\t\t\t\t\t\t<Icon\n\t\t\t\t\t\t\t\t\ticon={ check }\n\t\t\t\t\t\t\t\t\tclassName=\"components-custom-select-control__item-icon\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t</li>\n\t\t\t\t\t) ) }\n\t\t\t</ul>\n\t\t</div>\n\t);\n}\n\nexport function StableCustomSelectControl( props ) {\n\treturn (\n\t\t<CustomSelectControl\n\t\t\t{ ...props }\n\t\t\t__experimentalShowSelectedHint={ false }\n\t\t/>\n\t);\n}\n"], "mappings": ";;;;;;;;;AAIA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AAKA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AACA,IAAAM,WAAA,GAAAJ,sBAAA,CAAAF,OAAA;AAKA,IAAAO,eAAA,GAAAP,OAAA;AACA,IAAAQ,oBAAA,GAAAR,OAAA;AACA,IAAAS,YAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,kBAAA,GAAAX,OAAA;AACA,IAAAY,mBAAA,GAAAZ,OAAA;AAvBA;AACA;AACA;AACA;;AAIA;AACA;AACA;;AAMA;AACA;AACA;;AAQA,MAAMa,YAAY,GAAKC,IAAI,IAAMA,IAAI,EAAEC,IAAI;AAC3C;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGA,CACpB;EAAEC;AAAa,CAAC,EAChB;EAAEC,IAAI;EAAEC,OAAO;EAAEC,KAAK,EAAE;IAAEC;EAAM;AAAE,CAAC,KAC/B;EACJ,QAASH,IAAI;IACZ,KAAKI,oBAAS,CAACC,gBAAgB,CAACC,4BAA4B;MAC3D;MACA;MACA,OAAO;QACNP,YAAY,EACXI,KAAK,CACJJ,YAAY,GACTQ,IAAI,CAACC,GAAG,CACRL,KAAK,CAACM,OAAO,CAAEV,YAAa,CAAC,GAAG,CAAC,EACjCI,KAAK,CAACO,MAAM,GAAG,CACf,CAAC,GACD,CAAC;MAEP,CAAC;IACF,KAAKN,oBAAS,CAACC,gBAAgB,CAACM,0BAA0B;MACzD;MACA;MACA,OAAO;QACNZ,YAAY,EACXI,KAAK,CACJJ,YAAY,GACTQ,IAAI,CAACK,GAAG,CAAET,KAAK,CAACM,OAAO,CAAEV,YAAa,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,GAChDI,KAAK,CAACO,MAAM,GAAG,CAAC;MAEtB,CAAC;IACF;MACC,OAAOT,OAAO;EAChB;AACD,CAAC;AAEc,SAASY,mBAAmBA,CAAEX,KAAK,EAAG;EACpD,MAAM;IACL;IACAY,qBAAqB,GAAG,KAAK;IAC7B;IACAC,wBAAwB,GAAG,KAAK;IAChCC,SAAS;IACTC,mBAAmB;IACnBC,KAAK;IACLC,WAAW;IACXC,OAAO,EAAEjB,KAAK;IACdkB,QAAQ,EAAEC,oBAAoB;IAC9B;IACAC,IAAI,GAAG,SAAS;IAChBC,KAAK,EAAEC,aAAa;IACpBC,WAAW;IACXC,UAAU;IACVC,OAAO;IACPC,MAAM;IACNC,8BAA8B,GAAG;EAClC,CAAC,GAAG,IAAAC,oDAAgC,EACnC7B,KAAK,EACL,mCAAmC,EACnC,KACD,CAAC;EAED,MAAM;IACL8B,aAAa;IACbC,oBAAoB;IACpBC,YAAY;IACZC,YAAY;IACZC,MAAM;IACNC,gBAAgB;IAChBtC;EACD,CAAC,GAAG,IAAAK,oBAAS,EAAE;IACdkC,mBAAmB,EAAEnC,KAAK,CAAE,CAAC,CAAE;IAC/BA,KAAK;IACLR,YAAY;IACZ2B,oBAAoB;IACpB,IAAK,OAAOG,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,IAAI,GAChE;MAAE1B,YAAY,EAAE0B;IAAc,CAAC,GAC/Bc,SAAS,CAAE;IACdzC;EACD,CAAE,CAAC;EAEH,MAAM,CAAE0C,SAAS,EAAEC,YAAY,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAErD,SAASC,aAAaA,CAAEC,CAAC,EAAG;IAC3BH,YAAY,CAAE,IAAK,CAAC;IACpBb,OAAO,GAAIgB,CAAE,CAAC;EACf;EAEA,SAASC,YAAYA,CAAED,CAAC,EAAG;IAC1BH,YAAY,CAAE,KAAM,CAAC;IACrBZ,MAAM,GAAIe,CAAE,CAAC;EACd;EAEA,IAAK,CAAE7B,wBAAwB,EAAG;IACjC,IAAA+B,mBAAU,EACT,gEAAgE,EAChE;MACCC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE;IACP,CACD,CAAC;EACF;EAEA,SAASC,cAAcA,CAAA,EAAG;IACzB,IAAK/B,WAAW,EAAG;MAClB,OAAOA,WAAW;IACnB;IAEA,IAAK,CAAEpB,YAAY,EAAG;MACrB,OAAO,IAAAoD,QAAE,EAAE,cAAe,CAAC;IAC5B;;IAEA;IACA,OAAO,IAAAC,aAAO,EAAE,IAAAD,QAAE,EAAE,wBAAyB,CAAC,EAAEpD,YAAY,CAACF,IAAK,CAAC;EACpE;EAEA,MAAMwD,SAAS,GAAGnB,YAAY,CAAE;IAC/BlB,SAAS,EAAE,wCAAwC;IACnD,aAAa,EAAE,CAAEoB;EAClB,CAAE,CAAC;EAEH,MAAMkB,gBAAgB,GAAG,IAAAC,oBAAW,EACjCX,CAAC,IAAM;IACRA,CAAC,CAACY,eAAe,CAAC,CAAC;IACnBH,SAAS,EAAEI,SAAS,GAAIb,CAAE,CAAC;EAC5B,CAAC,EACD,CAAES,SAAS,CACZ,CAAC;;EAED;EACA,IACCA,SAAS,CAAE,uBAAuB,CAAE,EAAEK,UAAU,CAAE,gBAAiB,CAAC,EACnE;IACD,OAAOL,SAAS,CAAE,uBAAuB,CAAE;EAC5C;EACA,OACC,IAAAM,MAAA,CAAAC,aAAA;IACC5C,SAAS,EAAG,IAAA6C,mBAAU,EACrB,kCAAkC,EAClC7C,SACD;EAAG,GAEDC,mBAAmB,GACpB,IAAA0C,MAAA,CAAAC,aAAA,EAACvE,eAAA,CAAAyE,cAAc;IAACC,EAAE,EAAC,OAAO;IAAA,GAAM/B,aAAa,CAAC;EAAC,GAC5Cd,KACa,CAAC,GAEjB;EACA,IAAAyC,MAAA,CAAAC,aAAA,EAACnE,kBAAA,CAAAuE,WAAW;IAAA,GACNhC,aAAa,CAAE;MACnBhB,SAAS,EAAE;IACZ,CAAE;EAAC,GAEDE,KACU,CACb,EACD,IAAAyC,MAAA,CAAAC,aAAA,EAACpE,OAAA,CAAAyE,+BAA+B;IAC/BnD,qBAAqB,EAAGA,qBAAuB;IAC/CC,wBAAwB,EAAGA,wBAA0B;IACrDyB,SAAS,EAAGJ,MAAM,IAAII,SAAW;IACjC0B,oBAAoB,EACnBnD,wBAAwB,GAAGwB,SAAS,GAAG,MACvC;IACD4B,aAAa,EAAGpD,wBAAwB,GAAGwB,SAAS,GAAG,KAAO;IAC9DhB,IAAI,EAAGA,IAAM;IACb6C,MAAM,EAAG,IAAAT,MAAA,CAAAC,aAAA,EAACrE,YAAA,CAAA8E,OAAwB,MAAE;EAAG,GAEvC,IAAAV,MAAA,CAAAC,aAAA,EAACtE,oBAAA,CAAAgF,MAAmB;IACnB5C,WAAW,EAAGA,WAAa;IAC3BC,UAAU,EAAGA,UAAY;IACzBoC,EAAE,EAAC,QAAQ;IACXnC,OAAO,EAAGe,aAAe;IACzBd,MAAM,EAAGgB,YAAc;IACvB0B,UAAU,EAAGhD,IAAM;IACnBT,qBAAqB,EAAGA,qBAAuB;IAAA,GAC1CmB,oBAAoB,CAAE;MAC1B;MACA,YAAY,EAAEf,KAAK;MACnB,iBAAiB,EAAEqB,SAAS;MAC5BvB,SAAS,EAAE,0CAA0C;MACrDG,WAAW,EAAE+B,cAAc,CAAC;IAC7B,CAAE;EAAC,GAEDvD,YAAY,CAAEI,YAAa,CAAC,EAC5B+B,8BAA8B,IAC/B/B,YAAY,CAACyE,kBAAkB,IAC9B,IAAAb,MAAA,CAAAC,aAAA;IAAM5C,SAAS,EAAC;EAAwC,GACrDjB,YAAY,CAACyE,kBACV,CAEY,CACW,CAAC,EAElC,IAAAb,MAAA,CAAAC,aAAA;IAAA,GAASP,SAAS;IAAGI,SAAS,EAAGH;EAAkB,GAChDlB,MAAM,IACPjC,KAAK,CAACsE,GAAG,CAAE,CAAE7E,IAAI,EAAE8E,KAAK;EACvB;EACA,IAAAf,MAAA,CAAAC,aAAA;IAAA,GACMzB,YAAY,CAAE;MAClBvC,IAAI;MACJ8E,KAAK;MACLC,GAAG,EAAE/E,IAAI,CAAC+E,GAAG;MACb3D,SAAS,EAAE,IAAA6C,mBAAU,EACpBjE,IAAI,CAACoB,SAAS,EACd,wCAAwC,EACxC;QACC,gBAAgB,EACf0D,KAAK,KAAKrC,gBAAgB;QAC3B,UAAU,EAAE,CAAC,CAAEzC,IAAI,CAAC4E,kBAAkB;QACtC,2BAA2B,EAC1B1D;MACF,CACD,CAAC;MACD8D,KAAK,EAAEhF,IAAI,CAACgF;IACb,CAAE;EAAC,GAEDhF,IAAI,CAACC,IAAI,EACTD,IAAI,CAAC4E,kBAAkB,IACxB,IAAAb,MAAA,CAAAC,aAAA;IAAM5C,SAAS,EAAC;EAA6C,GAC1DpB,IAAI,CAAC4E,kBACF,CACN,EACC5E,IAAI,KAAKG,YAAY,IACtB,IAAA4D,MAAA,CAAAC,aAAA,EAAC3E,MAAA,CAAA4F,IAAI;IACJC,IAAI,EAAGC,YAAO;IACd/D,SAAS,EAAC;EAA6C,CACvD,CAEC,CACH,CACA,CACA,CAAC;AAER;AAEO,SAASgE,yBAAyBA,CAAE9E,KAAK,EAAG;EAClD,OACC,IAAAyD,MAAA,CAAAC,aAAA,EAAC/C,mBAAmB;IAAA,GACdX,KAAK;IACV4B,8BAA8B,EAAG;EAAO,CACxC,CAAC;AAEJ"}