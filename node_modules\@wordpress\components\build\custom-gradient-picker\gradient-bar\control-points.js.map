{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_colord", "_compose", "_element", "_i18n", "_icons", "_button", "_hStack", "_colorPicker", "_visually<PERSON><PERSON>den", "_colorPalette", "_utils", "_constants", "_dropdownContentWrapper", "ControlPointButton", "isOpen", "position", "color", "additionalProps", "instanceId", "useInstanceId", "descriptionId", "_react", "createElement", "Fragment", "default", "sprintf", "__", "className", "classnames", "VisuallyHidden", "id", "GradientColorPickerDropdown", "isRenderedInSidebar", "props", "popoverProps", "useMemo", "placement", "offset", "resize", "mergedClassName", "CustomColorPickerDropdown", "ControlPoints", "disable<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON>pha", "gradientPickerDomRef", "ignoreMarkerPosition", "value", "controlPoints", "onChange", "onStartControlPointChange", "onStopControlPointChange", "__experimentalIsRenderedInSidebar", "controlPointMoveState", "useRef", "onMouseMove", "event", "current", "undefined", "relativePosition", "getHorizontalRelativeGradientPosition", "clientX", "initialPosition", "index", "significantMoveHappened", "Math", "abs", "MINIMUM_SIGNIFICANT_MOVE", "updateControlPointPosition", "cleanEventListeners", "window", "removeEventListener", "listenersActivated", "cleanEventListenersRef", "useEffect", "map", "point", "key", "onClose", "renderToggle", "onToggle", "onClick", "onMouseDown", "addEventListener", "onKeyDown", "code", "stopPropagation", "clampPercent", "KEYBOARD_CONTROL_POINT_VARIATION", "renderContent", "paddingSize", "ColorPicker", "enableAlpha", "updateControlPointColor", "colord", "toRgbString", "length", "HStack", "alignment", "removeControlPoint", "variant", "style", "left", "transform", "InsertPoint", "onOpenInserter", "onCloseInserter", "insertPosition", "alreadyInsertedPoint", "setAlreadyInsertedPoint", "useState", "icon", "plus", "addControlPoint", "updateControlPointColorByPosition", "_default", "exports"], "sources": ["@wordpress/components/src/custom-gradient-picker/gradient-bar/control-points.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport { colord } from 'colord';\n\n/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { useEffect, useRef, useState, useMemo } from '@wordpress/element';\nimport { __, sprintf } from '@wordpress/i18n';\nimport { plus } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport Button from '../../button';\nimport { HStack } from '../../h-stack';\nimport { ColorPicker } from '../../color-picker';\nimport { VisuallyHidden } from '../../visually-hidden';\nimport { CustomColorPickerDropdown } from '../../color-palette';\n\nimport {\n\taddControlPoint,\n\tclampPercent,\n\tremoveControlPoint,\n\tupdateControlPointColor,\n\tupdateControlPointColorByPosition,\n\tupdateControlPointPosition,\n\tgetHorizontalRelativeGradientPosition,\n} from './utils';\nimport {\n\tMINIMUM_SIGNIFICANT_MOVE,\n\tKEYBOARD_CONTROL_POINT_VARIATION,\n} from './constants';\nimport type { WordPressComponentProps } from '../../context';\nimport type {\n\tControlPointButtonProps,\n\tControlPointMoveState,\n\tControlPointsProps,\n\tInsertPointProps,\n} from '../types';\nimport type { CustomColorPickerDropdownProps } from '../../color-palette/types';\nimport DropdownContentWrapper from '../../dropdown/dropdown-content-wrapper';\n\nfunction ControlPointButton( {\n\tisOpen,\n\tposition,\n\tcolor,\n\t...additionalProps\n}: WordPressComponentProps< ControlPointButtonProps, 'button', true > ) {\n\tconst instanceId = useInstanceId( ControlPointButton );\n\tconst descriptionId = `components-custom-gradient-picker__control-point-button-description-${ instanceId }`;\n\treturn (\n\t\t<>\n\t\t\t<Button\n\t\t\t\taria-label={ sprintf(\n\t\t\t\t\t// translators: %1$s: gradient position e.g: 70, %2$s: gradient color code e.g: rgb(52,121,151).\n\t\t\t\t\t__(\n\t\t\t\t\t\t'Gradient control point at position %1$s%% with color code %2$s.'\n\t\t\t\t\t),\n\t\t\t\t\tposition,\n\t\t\t\t\tcolor\n\t\t\t\t) }\n\t\t\t\taria-describedby={ descriptionId }\n\t\t\t\taria-haspopup=\"true\"\n\t\t\t\taria-expanded={ isOpen }\n\t\t\t\tclassName={ classnames(\n\t\t\t\t\t'components-custom-gradient-picker__control-point-button',\n\t\t\t\t\t{\n\t\t\t\t\t\t'is-active': isOpen,\n\t\t\t\t\t}\n\t\t\t\t) }\n\t\t\t\t{ ...additionalProps }\n\t\t\t/>\n\t\t\t<VisuallyHidden id={ descriptionId }>\n\t\t\t\t{ __(\n\t\t\t\t\t'Use your left or right arrow keys or drag and drop with the mouse to change the gradient position. Press the button to change the color or remove the control point.'\n\t\t\t\t) }\n\t\t\t</VisuallyHidden>\n\t\t</>\n\t);\n}\n\nfunction GradientColorPickerDropdown( {\n\tisRenderedInSidebar,\n\tclassName,\n\t...props\n}: CustomColorPickerDropdownProps ) {\n\t// Open the popover below the gradient control/insertion point\n\tconst popoverProps = useMemo(\n\t\t() =>\n\t\t\t( {\n\t\t\t\tplacement: 'bottom',\n\t\t\t\toffset: 8,\n\t\t\t\t// Disabling resize as it would otherwise cause the popover to show\n\t\t\t\t// scrollbars while dragging the color picker's handle close to the\n\t\t\t\t// popover edge.\n\t\t\t\tresize: false,\n\t\t\t} ) as const,\n\t\t[]\n\t);\n\n\tconst mergedClassName = classnames(\n\t\t'components-custom-gradient-picker__control-point-dropdown',\n\t\tclassName\n\t);\n\n\treturn (\n\t\t<CustomColorPickerDropdown\n\t\t\tisRenderedInSidebar={ isRenderedInSidebar }\n\t\t\tpopoverProps={ popoverProps }\n\t\t\tclassName={ mergedClassName }\n\t\t\t{ ...props }\n\t\t/>\n\t);\n}\n\nfunction ControlPoints( {\n\tdisableRemove,\n\tdisableAlpha,\n\tgradientPickerDomRef,\n\tignoreMarkerPosition,\n\tvalue: controlPoints,\n\tonChange,\n\tonStartControlPointChange,\n\tonStopControlPointChange,\n\t__experimentalIsRenderedInSidebar,\n}: ControlPointsProps ) {\n\tconst controlPointMoveState = useRef< ControlPointMoveState >();\n\n\tconst onMouseMove = ( event: MouseEvent ) => {\n\t\tif (\n\t\t\tcontrolPointMoveState.current === undefined ||\n\t\t\tgradientPickerDomRef.current === null\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst relativePosition = getHorizontalRelativeGradientPosition(\n\t\t\tevent.clientX,\n\t\t\tgradientPickerDomRef.current\n\t\t);\n\n\t\tconst { initialPosition, index, significantMoveHappened } =\n\t\t\tcontrolPointMoveState.current;\n\n\t\tif (\n\t\t\t! significantMoveHappened &&\n\t\t\tMath.abs( initialPosition - relativePosition ) >=\n\t\t\t\tMINIMUM_SIGNIFICANT_MOVE\n\t\t) {\n\t\t\tcontrolPointMoveState.current.significantMoveHappened = true;\n\t\t}\n\n\t\tonChange(\n\t\t\tupdateControlPointPosition( controlPoints, index, relativePosition )\n\t\t);\n\t};\n\n\tconst cleanEventListeners = () => {\n\t\tif (\n\t\t\twindow &&\n\t\t\twindow.removeEventListener &&\n\t\t\tcontrolPointMoveState.current &&\n\t\t\tcontrolPointMoveState.current.listenersActivated\n\t\t) {\n\t\t\twindow.removeEventListener( 'mousemove', onMouseMove );\n\t\t\twindow.removeEventListener( 'mouseup', cleanEventListeners );\n\t\t\tonStopControlPointChange();\n\t\t\tcontrolPointMoveState.current.listenersActivated = false;\n\t\t}\n\t};\n\n\t// Adding `cleanEventListeners` to the dependency array below requires the function itself to be wrapped in a `useCallback`\n\t// This memoization would prevent the event listeners from being properly cleaned.\n\t// Instead, we'll pass a ref to the function in our `useEffect` so `cleanEventListeners` itself is no longer a dependency.\n\tconst cleanEventListenersRef = useRef< () => void >();\n\tcleanEventListenersRef.current = cleanEventListeners;\n\n\tuseEffect( () => {\n\t\treturn () => {\n\t\t\tcleanEventListenersRef.current?.();\n\t\t};\n\t}, [] );\n\n\treturn (\n\t\t<>\n\t\t\t{ controlPoints.map( ( point, index ) => {\n\t\t\t\tconst initialPosition = point?.position;\n\t\t\t\treturn (\n\t\t\t\t\tignoreMarkerPosition !== initialPosition && (\n\t\t\t\t\t\t<GradientColorPickerDropdown\n\t\t\t\t\t\t\tisRenderedInSidebar={\n\t\t\t\t\t\t\t\t__experimentalIsRenderedInSidebar\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tkey={ index }\n\t\t\t\t\t\t\tonClose={ onStopControlPointChange }\n\t\t\t\t\t\t\trenderToggle={ ( { isOpen, onToggle } ) => (\n\t\t\t\t\t\t\t\t<ControlPointButton\n\t\t\t\t\t\t\t\t\tkey={ index }\n\t\t\t\t\t\t\t\t\tonClick={ () => {\n\t\t\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t\t\tcontrolPointMoveState.current &&\n\t\t\t\t\t\t\t\t\t\t\tcontrolPointMoveState.current\n\t\t\t\t\t\t\t\t\t\t\t\t.significantMoveHappened\n\t\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tif ( isOpen ) {\n\t\t\t\t\t\t\t\t\t\t\tonStopControlPointChange();\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\tonStartControlPointChange();\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tonToggle();\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\tonMouseDown={ () => {\n\t\t\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t\t\twindow &&\n\t\t\t\t\t\t\t\t\t\t\twindow.addEventListener\n\t\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\t\tcontrolPointMoveState.current = {\n\t\t\t\t\t\t\t\t\t\t\t\tinitialPosition,\n\t\t\t\t\t\t\t\t\t\t\t\tindex,\n\t\t\t\t\t\t\t\t\t\t\t\tsignificantMoveHappened: false,\n\t\t\t\t\t\t\t\t\t\t\t\tlistenersActivated: true,\n\t\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\t\tonStartControlPointChange();\n\t\t\t\t\t\t\t\t\t\t\twindow.addEventListener(\n\t\t\t\t\t\t\t\t\t\t\t\t'mousemove',\n\t\t\t\t\t\t\t\t\t\t\t\tonMouseMove\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t\twindow.addEventListener(\n\t\t\t\t\t\t\t\t\t\t\t\t'mouseup',\n\t\t\t\t\t\t\t\t\t\t\t\tcleanEventListeners\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\tonKeyDown={ ( event ) => {\n\t\t\t\t\t\t\t\t\t\tif ( event.code === 'ArrowLeft' ) {\n\t\t\t\t\t\t\t\t\t\t\t// Stop propagation of the key press event to avoid focus moving\n\t\t\t\t\t\t\t\t\t\t\t// to another editor area.\n\t\t\t\t\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t\t\t\t\t\tonChange(\n\t\t\t\t\t\t\t\t\t\t\t\tupdateControlPointPosition(\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontrolPoints,\n\t\t\t\t\t\t\t\t\t\t\t\t\tindex,\n\t\t\t\t\t\t\t\t\t\t\t\t\tclampPercent(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpoint.position -\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tKEYBOARD_CONTROL_POINT_VARIATION\n\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t\t\t\t\tevent.code === 'ArrowRight'\n\t\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\t\t// Stop propagation of the key press event to avoid focus moving\n\t\t\t\t\t\t\t\t\t\t\t// to another editor area.\n\t\t\t\t\t\t\t\t\t\t\tevent.stopPropagation();\n\t\t\t\t\t\t\t\t\t\t\tonChange(\n\t\t\t\t\t\t\t\t\t\t\t\tupdateControlPointPosition(\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontrolPoints,\n\t\t\t\t\t\t\t\t\t\t\t\t\tindex,\n\t\t\t\t\t\t\t\t\t\t\t\t\tclampPercent(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpoint.position +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tKEYBOARD_CONTROL_POINT_VARIATION\n\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\tisOpen={ isOpen }\n\t\t\t\t\t\t\t\t\tposition={ point.position }\n\t\t\t\t\t\t\t\t\tcolor={ point.color }\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\trenderContent={ ( { onClose } ) => (\n\t\t\t\t\t\t\t\t<DropdownContentWrapper paddingSize=\"none\">\n\t\t\t\t\t\t\t\t\t<ColorPicker\n\t\t\t\t\t\t\t\t\t\tenableAlpha={ ! disableAlpha }\n\t\t\t\t\t\t\t\t\t\tcolor={ point.color }\n\t\t\t\t\t\t\t\t\t\tonChange={ ( color ) => {\n\t\t\t\t\t\t\t\t\t\t\tonChange(\n\t\t\t\t\t\t\t\t\t\t\t\tupdateControlPointColor(\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontrolPoints,\n\t\t\t\t\t\t\t\t\t\t\t\t\tindex,\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolord(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor\n\t\t\t\t\t\t\t\t\t\t\t\t\t).toRgbString()\n\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t{ ! disableRemove &&\n\t\t\t\t\t\t\t\t\t\tcontrolPoints.length > 2 && (\n\t\t\t\t\t\t\t\t\t\t\t<HStack\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"components-custom-gradient-picker__remove-control-point-wrapper\"\n\t\t\t\t\t\t\t\t\t\t\t\talignment=\"center\"\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={ () => {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tremoveControlPoint(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcontrolPoints,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tindex\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClose();\n\t\t\t\t\t\t\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"link\"\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{ __(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t'Remove Control Point'\n\t\t\t\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t</HStack>\n\t\t\t\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\t\t</DropdownContentWrapper>\n\t\t\t\t\t\t\t) }\n\t\t\t\t\t\t\tstyle={ {\n\t\t\t\t\t\t\t\tleft: `${ point.position }%`,\n\t\t\t\t\t\t\t\ttransform: 'translateX( -50% )',\n\t\t\t\t\t\t\t} }\n\t\t\t\t\t\t/>\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\t} ) }\n\t\t</>\n\t);\n}\n\nfunction InsertPoint( {\n\tvalue: controlPoints,\n\tonChange,\n\tonOpenInserter,\n\tonCloseInserter,\n\tinsertPosition,\n\tdisableAlpha,\n\t__experimentalIsRenderedInSidebar,\n}: InsertPointProps ) {\n\tconst [ alreadyInsertedPoint, setAlreadyInsertedPoint ] = useState( false );\n\treturn (\n\t\t<GradientColorPickerDropdown\n\t\t\tisRenderedInSidebar={ __experimentalIsRenderedInSidebar }\n\t\t\tclassName=\"components-custom-gradient-picker__inserter\"\n\t\t\tonClose={ () => {\n\t\t\t\tonCloseInserter();\n\t\t\t} }\n\t\t\trenderToggle={ ( { isOpen, onToggle } ) => (\n\t\t\t\t<Button\n\t\t\t\t\taria-expanded={ isOpen }\n\t\t\t\t\taria-haspopup=\"true\"\n\t\t\t\t\tonClick={ () => {\n\t\t\t\t\t\tif ( isOpen ) {\n\t\t\t\t\t\t\tonCloseInserter();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tsetAlreadyInsertedPoint( false );\n\t\t\t\t\t\t\tonOpenInserter();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tonToggle();\n\t\t\t\t\t} }\n\t\t\t\t\tclassName=\"components-custom-gradient-picker__insert-point-dropdown\"\n\t\t\t\t\ticon={ plus }\n\t\t\t\t/>\n\t\t\t) }\n\t\t\trenderContent={ () => (\n\t\t\t\t<DropdownContentWrapper paddingSize=\"none\">\n\t\t\t\t\t<ColorPicker\n\t\t\t\t\t\tenableAlpha={ ! disableAlpha }\n\t\t\t\t\t\tonChange={ ( color ) => {\n\t\t\t\t\t\t\tif ( ! alreadyInsertedPoint ) {\n\t\t\t\t\t\t\t\tonChange(\n\t\t\t\t\t\t\t\t\taddControlPoint(\n\t\t\t\t\t\t\t\t\t\tcontrolPoints,\n\t\t\t\t\t\t\t\t\t\tinsertPosition,\n\t\t\t\t\t\t\t\t\t\tcolord( color ).toRgbString()\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\tsetAlreadyInsertedPoint( true );\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tonChange(\n\t\t\t\t\t\t\t\t\tupdateControlPointColorByPosition(\n\t\t\t\t\t\t\t\t\t\tcontrolPoints,\n\t\t\t\t\t\t\t\t\t\tinsertPosition,\n\t\t\t\t\t\t\t\t\t\tcolord( color ).toRgbString()\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} }\n\t\t\t\t\t/>\n\t\t\t\t</DropdownContentWrapper>\n\t\t\t) }\n\t\t\tstyle={\n\t\t\t\tinsertPosition !== null\n\t\t\t\t\t? {\n\t\t\t\t\t\t\tleft: `${ insertPosition }%`,\n\t\t\t\t\t\t\ttransform: 'translateX( -50% )',\n\t\t\t\t\t  }\n\t\t\t\t\t: undefined\n\t\t\t}\n\t\t/>\n\t);\n}\nControlPoints.InsertPoint = InsertPoint;\n\nexport default ControlPoints;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAKA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAKA,IAAAM,OAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAR,OAAA;AACA,IAAAS,eAAA,GAAAT,OAAA;AACA,IAAAU,aAAA,GAAAV,OAAA;AAEA,IAAAW,MAAA,GAAAX,OAAA;AASA,IAAAY,UAAA,GAAAZ,OAAA;AAYA,IAAAa,uBAAA,GAAAd,sBAAA,CAAAC,OAAA;AA5CA;AACA;AACA;;AAIA;AACA;AACA;;AAMA;AACA;AACA;;AA8BA,SAASc,kBAAkBA,CAAE;EAC5BC,MAAM;EACNC,QAAQ;EACRC,KAAK;EACL,GAAGC;AACgE,CAAC,EAAG;EACvE,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEN,kBAAmB,CAAC;EACtD,MAAMO,aAAa,GAAI,uEAAuEF,UAAY,EAAC;EAC3G,OACC,IAAAG,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACC,IAAAF,MAAA,CAAAC,aAAA,EAACjB,OAAA,CAAAmB,OAAM;IACN,cAAa,IAAAC,aAAO;IACnB;IACA,IAAAC,QAAE,EACD,iEACD,CAAC,EACDX,QAAQ,EACRC,KACD,CAAG;IACH,oBAAmBI,aAAe;IAClC,iBAAc,MAAM;IACpB,iBAAgBN,MAAQ;IACxBa,SAAS,EAAG,IAAAC,mBAAU,EACrB,yDAAyD,EACzD;MACC,WAAW,EAAEd;IACd,CACD,CAAG;IAAA,GACEG;EAAe,CACpB,CAAC,EACF,IAAAI,MAAA,CAAAC,aAAA,EAACd,eAAA,CAAAqB,cAAc;IAACC,EAAE,EAAGV;EAAe,GACjC,IAAAM,QAAE,EACH,sKACD,CACe,CACf,CAAC;AAEL;AAEA,SAASK,2BAA2BA,CAAE;EACrCC,mBAAmB;EACnBL,SAAS;EACT,GAAGM;AAC4B,CAAC,EAAG;EACnC;EACA,MAAMC,YAAY,GAAG,IAAAC,gBAAO,EAC3B,OACG;IACDC,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAE,CAAC;IACT;IACA;IACA;IACAC,MAAM,EAAE;EACT,CAAC,CAAW,EACb,EACD,CAAC;EAED,MAAMC,eAAe,GAAG,IAAAX,mBAAU,EACjC,2DAA2D,EAC3DD,SACD,CAAC;EAED,OACC,IAAAN,MAAA,CAAAC,aAAA,EAACb,aAAA,CAAA+B,yBAAyB;IACzBR,mBAAmB,EAAGA,mBAAqB;IAC3CE,YAAY,EAAGA,YAAc;IAC7BP,SAAS,EAAGY,eAAiB;IAAA,GACxBN;EAAK,CACV,CAAC;AAEJ;AAEA,SAASQ,aAAaA,CAAE;EACvBC,aAAa;EACbC,YAAY;EACZC,oBAAoB;EACpBC,oBAAoB;EACpBC,KAAK,EAAEC,aAAa;EACpBC,QAAQ;EACRC,yBAAyB;EACzBC,wBAAwB;EACxBC;AACmB,CAAC,EAAG;EACvB,MAAMC,qBAAqB,GAAG,IAAAC,eAAM,EAA0B,CAAC;EAE/D,MAAMC,WAAW,GAAKC,KAAiB,IAAM;IAC5C,IACCH,qBAAqB,CAACI,OAAO,KAAKC,SAAS,IAC3Cb,oBAAoB,CAACY,OAAO,KAAK,IAAI,EACpC;MACD;IACD;IAEA,MAAME,gBAAgB,GAAG,IAAAC,4CAAqC,EAC7DJ,KAAK,CAACK,OAAO,EACbhB,oBAAoB,CAACY,OACtB,CAAC;IAED,MAAM;MAAEK,eAAe;MAAEC,KAAK;MAAEC;IAAwB,CAAC,GACxDX,qBAAqB,CAACI,OAAO;IAE9B,IACC,CAAEO,uBAAuB,IACzBC,IAAI,CAACC,GAAG,CAAEJ,eAAe,GAAGH,gBAAiB,CAAC,IAC7CQ,mCAAwB,EACxB;MACDd,qBAAqB,CAACI,OAAO,CAACO,uBAAuB,GAAG,IAAI;IAC7D;IAEAf,QAAQ,CACP,IAAAmB,iCAA0B,EAAEpB,aAAa,EAAEe,KAAK,EAAEJ,gBAAiB,CACpE,CAAC;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IACjC,IACCC,MAAM,IACNA,MAAM,CAACC,mBAAmB,IAC1BlB,qBAAqB,CAACI,OAAO,IAC7BJ,qBAAqB,CAACI,OAAO,CAACe,kBAAkB,EAC/C;MACDF,MAAM,CAACC,mBAAmB,CAAE,WAAW,EAAEhB,WAAY,CAAC;MACtDe,MAAM,CAACC,mBAAmB,CAAE,SAAS,EAAEF,mBAAoB,CAAC;MAC5DlB,wBAAwB,CAAC,CAAC;MAC1BE,qBAAqB,CAACI,OAAO,CAACe,kBAAkB,GAAG,KAAK;IACzD;EACD,CAAC;;EAED;EACA;EACA;EACA,MAAMC,sBAAsB,GAAG,IAAAnB,eAAM,EAAe,CAAC;EACrDmB,sBAAsB,CAAChB,OAAO,GAAGY,mBAAmB;EAEpD,IAAAK,kBAAS,EAAE,MAAM;IAChB,OAAO,MAAM;MACZD,sBAAsB,CAAChB,OAAO,GAAG,CAAC;IACnC,CAAC;EACF,CAAC,EAAE,EAAG,CAAC;EAEP,OACC,IAAAnC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAE,QAAA,QACGwB,aAAa,CAAC2B,GAAG,CAAE,CAAEC,KAAK,EAAEb,KAAK,KAAM;IACxC,MAAMD,eAAe,GAAGc,KAAK,EAAE5D,QAAQ;IACvC,OACC8B,oBAAoB,KAAKgB,eAAe,IACvC,IAAAxC,MAAA,CAAAC,aAAA,EAACS,2BAA2B;MAC3BC,mBAAmB,EAClBmB,iCACA;MACDyB,GAAG,EAAGd,KAAO;MACbe,OAAO,EAAG3B,wBAA0B;MACpC4B,YAAY,EAAGA,CAAE;QAAEhE,MAAM;QAAEiE;MAAS,CAAC,KACpC,IAAA1D,MAAA,CAAAC,aAAA,EAACT,kBAAkB;QAClB+D,GAAG,EAAGd,KAAO;QACbkB,OAAO,EAAGA,CAAA,KAAM;UACf,IACC5B,qBAAqB,CAACI,OAAO,IAC7BJ,qBAAqB,CAACI,OAAO,CAC3BO,uBAAuB,EACxB;YACD;UACD;UACA,IAAKjD,MAAM,EAAG;YACboC,wBAAwB,CAAC,CAAC;UAC3B,CAAC,MAAM;YACND,yBAAyB,CAAC,CAAC;UAC5B;UACA8B,QAAQ,CAAC,CAAC;QACX,CAAG;QACHE,WAAW,EAAGA,CAAA,KAAM;UACnB,IACCZ,MAAM,IACNA,MAAM,CAACa,gBAAgB,EACtB;YACD9B,qBAAqB,CAACI,OAAO,GAAG;cAC/BK,eAAe;cACfC,KAAK;cACLC,uBAAuB,EAAE,KAAK;cAC9BQ,kBAAkB,EAAE;YACrB,CAAC;YACDtB,yBAAyB,CAAC,CAAC;YAC3BoB,MAAM,CAACa,gBAAgB,CACtB,WAAW,EACX5B,WACD,CAAC;YACDe,MAAM,CAACa,gBAAgB,CACtB,SAAS,EACTd,mBACD,CAAC;UACF;QACD,CAAG;QACHe,SAAS,EAAK5B,KAAK,IAAM;UACxB,IAAKA,KAAK,CAAC6B,IAAI,KAAK,WAAW,EAAG;YACjC;YACA;YACA7B,KAAK,CAAC8B,eAAe,CAAC,CAAC;YACvBrC,QAAQ,CACP,IAAAmB,iCAA0B,EACzBpB,aAAa,EACbe,KAAK,EACL,IAAAwB,mBAAY,EACXX,KAAK,CAAC5D,QAAQ,GACbwE,2CACF,CACD,CACD,CAAC;UACF,CAAC,MAAM,IACNhC,KAAK,CAAC6B,IAAI,KAAK,YAAY,EAC1B;YACD;YACA;YACA7B,KAAK,CAAC8B,eAAe,CAAC,CAAC;YACvBrC,QAAQ,CACP,IAAAmB,iCAA0B,EACzBpB,aAAa,EACbe,KAAK,EACL,IAAAwB,mBAAY,EACXX,KAAK,CAAC5D,QAAQ,GACbwE,2CACF,CACD,CACD,CAAC;UACF;QACD,CAAG;QACHzE,MAAM,EAAGA,MAAQ;QACjBC,QAAQ,EAAG4D,KAAK,CAAC5D,QAAU;QAC3BC,KAAK,EAAG2D,KAAK,CAAC3D;MAAO,CACrB,CACC;MACHwE,aAAa,EAAGA,CAAE;QAAEX;MAAQ,CAAC,KAC5B,IAAAxD,MAAA,CAAAC,aAAA,EAACV,uBAAA,CAAAY,OAAsB;QAACiE,WAAW,EAAC;MAAM,GACzC,IAAApE,MAAA,CAAAC,aAAA,EAACf,YAAA,CAAAmF,WAAW;QACXC,WAAW,EAAG,CAAEhD,YAAc;QAC9B3B,KAAK,EAAG2D,KAAK,CAAC3D,KAAO;QACrBgC,QAAQ,EAAKhC,KAAK,IAAM;UACvBgC,QAAQ,CACP,IAAA4C,8BAAuB,EACtB7C,aAAa,EACbe,KAAK,EACL,IAAA+B,cAAM,EACL7E,KACD,CAAC,CAAC8E,WAAW,CAAC,CACf,CACD,CAAC;QACF;MAAG,CACH,CAAC,EACA,CAAEpD,aAAa,IAChBK,aAAa,CAACgD,MAAM,GAAG,CAAC,IACvB,IAAA1E,MAAA,CAAAC,aAAA,EAAChB,OAAA,CAAA0F,MAAM;QACNrE,SAAS,EAAC,iEAAiE;QAC3EsE,SAAS,EAAC;MAAQ,GAElB,IAAA5E,MAAA,CAAAC,aAAA,EAACjB,OAAA,CAAAmB,OAAM;QACNwD,OAAO,EAAGA,CAAA,KAAM;UACfhC,QAAQ,CACP,IAAAkD,yBAAkB,EACjBnD,aAAa,EACbe,KACD,CACD,CAAC;UACDe,OAAO,CAAC,CAAC;QACV,CAAG;QACHsB,OAAO,EAAC;MAAM,GAEZ,IAAAzE,QAAE,EACH,sBACD,CACO,CACD,CAEa,CACtB;MACH0E,KAAK,EAAG;QACPC,IAAI,EAAG,GAAG1B,KAAK,CAAC5D,QAAU,GAAE;QAC5BuF,SAAS,EAAE;MACZ;IAAG,CACH,CACD;EAEH,CAAE,CACD,CAAC;AAEL;AAEA,SAASC,WAAWA,CAAE;EACrBzD,KAAK,EAAEC,aAAa;EACpBC,QAAQ;EACRwD,cAAc;EACdC,eAAe;EACfC,cAAc;EACd/D,YAAY;EACZQ;AACiB,CAAC,EAAG;EACrB,MAAM,CAAEwD,oBAAoB,EAAEC,uBAAuB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;EAC3E,OACC,IAAAxF,MAAA,CAAAC,aAAA,EAACS,2BAA2B;IAC3BC,mBAAmB,EAAGmB,iCAAmC;IACzDxB,SAAS,EAAC,6CAA6C;IACvDkD,OAAO,EAAGA,CAAA,KAAM;MACf4B,eAAe,CAAC,CAAC;IAClB,CAAG;IACH3B,YAAY,EAAGA,CAAE;MAAEhE,MAAM;MAAEiE;IAAS,CAAC,KACpC,IAAA1D,MAAA,CAAAC,aAAA,EAACjB,OAAA,CAAAmB,OAAM;MACN,iBAAgBV,MAAQ;MACxB,iBAAc,MAAM;MACpBkE,OAAO,EAAGA,CAAA,KAAM;QACf,IAAKlE,MAAM,EAAG;UACb2F,eAAe,CAAC,CAAC;QAClB,CAAC,MAAM;UACNG,uBAAuB,CAAE,KAAM,CAAC;UAChCJ,cAAc,CAAC,CAAC;QACjB;QACAzB,QAAQ,CAAC,CAAC;MACX,CAAG;MACHpD,SAAS,EAAC,0DAA0D;MACpEmF,IAAI,EAAGC;IAAM,CACb,CACC;IACHvB,aAAa,EAAGA,CAAA,KACf,IAAAnE,MAAA,CAAAC,aAAA,EAACV,uBAAA,CAAAY,OAAsB;MAACiE,WAAW,EAAC;IAAM,GACzC,IAAApE,MAAA,CAAAC,aAAA,EAACf,YAAA,CAAAmF,WAAW;MACXC,WAAW,EAAG,CAAEhD,YAAc;MAC9BK,QAAQ,EAAKhC,KAAK,IAAM;QACvB,IAAK,CAAE2F,oBAAoB,EAAG;UAC7B3D,QAAQ,CACP,IAAAgE,sBAAe,EACdjE,aAAa,EACb2D,cAAc,EACd,IAAAb,cAAM,EAAE7E,KAAM,CAAC,CAAC8E,WAAW,CAAC,CAC7B,CACD,CAAC;UACDc,uBAAuB,CAAE,IAAK,CAAC;QAChC,CAAC,MAAM;UACN5D,QAAQ,CACP,IAAAiE,wCAAiC,EAChClE,aAAa,EACb2D,cAAc,EACd,IAAAb,cAAM,EAAE7E,KAAM,CAAC,CAAC8E,WAAW,CAAC,CAC7B,CACD,CAAC;QACF;MACD;IAAG,CACH,CACsB,CACtB;IACHM,KAAK,EACJM,cAAc,KAAK,IAAI,GACpB;MACAL,IAAI,EAAG,GAAGK,cAAgB,GAAE;MAC5BJ,SAAS,EAAE;IACX,CAAC,GACD7C;EACH,CACD,CAAC;AAEJ;AACAhB,aAAa,CAAC8D,WAAW,GAAGA,WAAW;AAAC,IAAAW,QAAA,GAEzBzE,aAAa;AAAA0E,OAAA,CAAA3F,OAAA,GAAA0F,QAAA"}