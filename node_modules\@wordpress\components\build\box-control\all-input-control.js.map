{"version": 3, "names": ["_compose", "require", "_boxControlStyles", "_hStack", "_unitControl", "_utils", "noop", "AllInputControl", "__next40pxDefaultSize", "onChange", "onFocus", "values", "sides", "selected<PERSON><PERSON><PERSON>", "setSelectedUnits", "props", "_CUSTOM_VALUE_SETTING", "_CUSTOM_VALUE_SETTING2", "inputId", "useInstanceId", "allValue", "getAllValue", "<PERSON><PERSON><PERSON><PERSON>", "isValuesDefined", "isMixed", "isValuesMixed", "allPlaceholder", "LABELS", "mixed", "undefined", "parsedQuantity", "parsedUnit", "parseQuantityAndUnitFromRawValue", "handleOnFocus", "event", "side", "onValueChange", "next", "isNumeric", "isNaN", "parseFloat", "nextValue", "nextV<PERSON>ues", "applyValueToSides", "slider<PERSON>n<PERSON><PERSON>e", "join", "handleOnUnitChange", "unit", "newUnits", "_react", "createElement", "HStack", "StyledUnitControl", "className", "disableUnits", "id", "isPressEnterToChange", "value", "onUnitChange", "placeholder", "label", "all", "hideLabelFromVision", "FlexedRangeControl", "__nextHasNoMarginBottom", "min", "max", "CUSTOM_VALUE_SETTINGS", "step", "withInputField"], "sources": ["@wordpress/components/src/box-control/all-input-control.tsx"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\n/**\n * Internal dependencies\n */\nimport type { UnitControlProps } from '../unit-control/types';\nimport {\n\tFlexedRangeControl,\n\tStyledUnitControl,\n} from './styles/box-control-styles';\nimport { HStack } from '../h-stack';\nimport type { BoxControlInputControlProps } from './types';\nimport { parseQuantityAndUnitFromRawValue } from '../unit-control';\nimport {\n\tLABELS,\n\tapplyValueToSides,\n\tgetAllValue,\n\tisValuesMixed,\n\tisValuesDefined,\n\tCUSTOM_VALUE_SETTINGS,\n} from './utils';\n\nconst noop = () => {};\n\nexport default function AllInputControl( {\n\t__next40pxDefaultSize,\n\tonChange = noop,\n\tonFocus = noop,\n\tvalues,\n\tsides,\n\tselectedUnits,\n\tsetSelectedUnits,\n\t...props\n}: BoxControlInputControlProps ) {\n\tconst inputId = useInstanceId( AllInputControl, 'box-control-input-all' );\n\n\tconst allValue = getAllValue( values, selectedUnits, sides );\n\tconst hasValues = isValuesDefined( values );\n\tconst isMixed = hasValues && isValuesMixed( values, selectedUnits, sides );\n\tconst allPlaceholder = isMixed ? LABELS.mixed : undefined;\n\n\tconst [ parsedQuantity, parsedUnit ] =\n\t\tparseQuantityAndUnitFromRawValue( allValue );\n\n\tconst handleOnFocus: React.FocusEventHandler< HTMLInputElement > = (\n\t\tevent\n\t) => {\n\t\tonFocus( event, { side: 'all' } );\n\t};\n\n\tconst onValueChange = ( next?: string ) => {\n\t\tconst isNumeric = next !== undefined && ! isNaN( parseFloat( next ) );\n\t\tconst nextValue = isNumeric ? next : undefined;\n\t\tconst nextValues = applyValueToSides( values, nextValue, sides );\n\n\t\tonChange( nextValues );\n\t};\n\n\tconst sliderOnChange = ( next?: number ) => {\n\t\tonValueChange(\n\t\t\tnext !== undefined ? [ next, parsedUnit ].join( '' ) : undefined\n\t\t);\n\t};\n\n\t// Set selected unit so it can be used as fallback by unlinked controls\n\t// when individual sides do not have a value containing a unit.\n\tconst handleOnUnitChange: UnitControlProps[ 'onUnitChange' ] = ( unit ) => {\n\t\tconst newUnits = applyValueToSides( selectedUnits, unit, sides );\n\t\tsetSelectedUnits( newUnits );\n\t};\n\n\treturn (\n\t\t<HStack>\n\t\t\t<StyledUnitControl\n\t\t\t\t{ ...props }\n\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\tclassName=\"component-box-control__unit-control\"\n\t\t\t\tdisableUnits={ isMixed }\n\t\t\t\tid={ inputId }\n\t\t\t\tisPressEnterToChange\n\t\t\t\tvalue={ allValue }\n\t\t\t\tonChange={ onValueChange }\n\t\t\t\tonUnitChange={ handleOnUnitChange }\n\t\t\t\tonFocus={ handleOnFocus }\n\t\t\t\tplaceholder={ allPlaceholder }\n\t\t\t\tlabel={ LABELS.all }\n\t\t\t\thideLabelFromVision\n\t\t\t/>\n\n\t\t\t<FlexedRangeControl\n\t\t\t\t__nextHasNoMarginBottom\n\t\t\t\t__next40pxDefaultSize={ __next40pxDefaultSize }\n\t\t\t\taria-controls={ inputId }\n\t\t\t\tlabel={ LABELS.all }\n\t\t\t\thideLabelFromVision\n\t\t\t\tonChange={ sliderOnChange }\n\t\t\t\tmin={ 0 }\n\t\t\t\tmax={ CUSTOM_VALUE_SETTINGS[ parsedUnit ?? 'px' ]?.max ?? 10 }\n\t\t\t\tstep={\n\t\t\t\t\tCUSTOM_VALUE_SETTINGS[ parsedUnit ?? 'px' ]?.step ?? 0.1\n\t\t\t\t}\n\t\t\t\tvalue={ parsedQuantity ?? 0 }\n\t\t\t\twithInputField={ false }\n\t\t\t/>\n\t\t</HStack>\n\t);\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,iBAAA,GAAAD,OAAA;AAIA,IAAAE,OAAA,GAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAfA;AACA;AACA;;AAsBA,MAAMK,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AAEN,SAASC,eAAeA,CAAE;EACxCC,qBAAqB;EACrBC,QAAQ,GAAGH,IAAI;EACfI,OAAO,GAAGJ,IAAI;EACdK,MAAM;EACNC,KAAK;EACLC,aAAa;EACbC,gBAAgB;EAChB,GAAGC;AACyB,CAAC,EAAG;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAChC,MAAMC,OAAO,GAAG,IAAAC,sBAAa,EAAEZ,eAAe,EAAE,uBAAwB,CAAC;EAEzE,MAAMa,QAAQ,GAAG,IAAAC,kBAAW,EAAEV,MAAM,EAAEE,aAAa,EAAED,KAAM,CAAC;EAC5D,MAAMU,SAAS,GAAG,IAAAC,sBAAe,EAAEZ,MAAO,CAAC;EAC3C,MAAMa,OAAO,GAAGF,SAAS,IAAI,IAAAG,oBAAa,EAAEd,MAAM,EAAEE,aAAa,EAAED,KAAM,CAAC;EAC1E,MAAMc,cAAc,GAAGF,OAAO,GAAGG,aAAM,CAACC,KAAK,GAAGC,SAAS;EAEzD,MAAM,CAAEC,cAAc,EAAEC,UAAU,CAAE,GACnC,IAAAC,6CAAgC,EAAEZ,QAAS,CAAC;EAE7C,MAAMa,aAA0D,GAC/DC,KAAK,IACD;IACJxB,OAAO,CAAEwB,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAM,CAAE,CAAC;EAClC,CAAC;EAED,MAAMC,aAAa,GAAKC,IAAa,IAAM;IAC1C,MAAMC,SAAS,GAAGD,IAAI,KAAKR,SAAS,IAAI,CAAEU,KAAK,CAAEC,UAAU,CAAEH,IAAK,CAAE,CAAC;IACrE,MAAMI,SAAS,GAAGH,SAAS,GAAGD,IAAI,GAAGR,SAAS;IAC9C,MAAMa,UAAU,GAAG,IAAAC,wBAAiB,EAAEhC,MAAM,EAAE8B,SAAS,EAAE7B,KAAM,CAAC;IAEhEH,QAAQ,CAAEiC,UAAW,CAAC;EACvB,CAAC;EAED,MAAME,cAAc,GAAKP,IAAa,IAAM;IAC3CD,aAAa,CACZC,IAAI,KAAKR,SAAS,GAAG,CAAEQ,IAAI,EAAEN,UAAU,CAAE,CAACc,IAAI,CAAE,EAAG,CAAC,GAAGhB,SACxD,CAAC;EACF,CAAC;;EAED;EACA;EACA,MAAMiB,kBAAsD,GAAKC,IAAI,IAAM;IAC1E,MAAMC,QAAQ,GAAG,IAAAL,wBAAiB,EAAE9B,aAAa,EAAEkC,IAAI,EAAEnC,KAAM,CAAC;IAChEE,gBAAgB,CAAEkC,QAAS,CAAC;EAC7B,CAAC;EAED,OACC,IAAAC,MAAA,CAAAC,aAAA,EAAC/C,OAAA,CAAAgD,MAAM,QACN,IAAAF,MAAA,CAAAC,aAAA,EAAChD,iBAAA,CAAAkD,iBAAiB;IAAA,GACZrC,KAAK;IACVP,qBAAqB,EAAGA,qBAAuB;IAC/C6C,SAAS,EAAC,qCAAqC;IAC/CC,YAAY,EAAG9B,OAAS;IACxB+B,EAAE,EAAGrC,OAAS;IACdsC,oBAAoB;IACpBC,KAAK,EAAGrC,QAAU;IAClBX,QAAQ,EAAG2B,aAAe;IAC1BsB,YAAY,EAAGZ,kBAAoB;IACnCpC,OAAO,EAAGuB,aAAe;IACzB0B,WAAW,EAAGjC,cAAgB;IAC9BkC,KAAK,EAAGjC,aAAM,CAACkC,GAAK;IACpBC,mBAAmB;EAAA,CACnB,CAAC,EAEF,IAAAb,MAAA,CAAAC,aAAA,EAAChD,iBAAA,CAAA6D,kBAAkB;IAClBC,uBAAuB;IACvBxD,qBAAqB,EAAGA,qBAAuB;IAC/C,iBAAgBU,OAAS;IACzB0C,KAAK,EAAGjC,aAAM,CAACkC,GAAK;IACpBC,mBAAmB;IACnBrD,QAAQ,EAAGmC,cAAgB;IAC3BqB,GAAG,EAAG,CAAG;IACTC,GAAG,GAAAlD,qBAAA,GAAGmD,4BAAqB,CAAEpC,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,IAAI,CAAE,EAAEmC,GAAG,cAAAlD,qBAAA,cAAAA,qBAAA,GAAI,EAAI;IAC9DoD,IAAI,GAAAnD,sBAAA,GACHkD,4BAAqB,CAAEpC,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,IAAI,CAAE,EAAEqC,IAAI,cAAAnD,sBAAA,cAAAA,sBAAA,GAAI,GACrD;IACDwC,KAAK,EAAG3B,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAI,CAAG;IAC7BuC,cAAc,EAAG;EAAO,CACxB,CACM,CAAC;AAEX"}