"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _compose = require("@wordpress/compose");
var _a11y = require("@wordpress/a11y");
/**
 * WordPress dependencies
 */
/** @typedef {import('react').ComponentType} ComponentType */
/**
 * A Higher Order Component used to be provide speak and debounced speak
 * functions.
 *
 * @see https://developer.wordpress.org/block-editor/packages/packages-a11y/#speak
 *
 * @param {ComponentType} Component The component to be wrapped.
 *
 * @return {ComponentType} The wrapped component.
 */
var _default = (0, _compose.createHigherOrderComponent)(Component => props => (0, _react.createElement)(Component, {
  ...props,
  speak: _a11y.speak,
  debouncedSpeak: (0, _compose.useDebounce)(_a11y.speak, 500)
}), 'withSpokenMessages');
exports.default = _default;
//# sourceMappingURL=index.js.map