{"version": 3, "names": [], "sources": ["@wordpress/components/src/circular-option-picker/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport type { Icon } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport type { ButtonAsButtonProps } from '../button/types';\nimport type { DropdownProps } from '../dropdown/types';\nimport type { WordPressComponentProps } from '../context';\nimport type { CompositeStore } from '../composite/v2';\n\ntype CommonCircularOptionPickerProps = {\n\t/**\n\t * An ID to apply to the component.\n\t */\n\tid?: string;\n\t/**\n\t * A CSS class to apply to the wrapper element.\n\t */\n\tclassName?: string;\n\t/**\n\t * The action(s) to be rendered after the options,\n\t * such as a 'clear' button as seen in `ColorPalette`.\n\t * Usually a `CircularOptionPicker.ButtonAction` or\n\t * `CircularOptionPicker.DropdownLinkAction` component.\n\t */\n\tactions?: ReactNode;\n\t/**\n\t * The options to be rendered, such as color swatches.\n\t * Usually a `CircularOptionPicker.Option` component.\n\t */\n\toptions: ReactNode;\n\t/**\n\t * The child elements.\n\t */\n\tchildren?: ReactNode;\n};\n\ntype WithBaseId = {\n\tbaseId: string;\n};\n\ntype FullListboxCircularOptionPickerProps = CommonCircularOptionPickerProps & {\n\t/**\n\t * Whether the control should present as a set of buttons,\n\t * each with its own tab stop.\n\t */\n\tasButtons?: false;\n\t/**\n\t * Prevents keyboard interaction from wrapping around.\n\t * Only used when `asButtons` is not true.\n\t *\n\t * @default true\n\t */\n\tloop?: boolean;\n} & (\n\t\t| {\n\t\t\t\t'aria-label': string;\n\t\t\t\t'aria-labelledby'?: never;\n\t\t  }\n\t\t| {\n\t\t\t\t'aria-label'?: never;\n\t\t\t\t'aria-labelledby': string;\n\t\t  }\n\t);\n\nexport type ListboxCircularOptionPickerProps = WithBaseId &\n\tOmit< FullListboxCircularOptionPickerProps, 'asButtons' >;\n\ntype FullButtonsCircularOptionPickerProps = CommonCircularOptionPickerProps & {\n\t/**\n\t * Whether the control should present as a set of buttons,\n\t * each with its own tab stop.\n\t *\n\t * @default false\n\t */\n\tasButtons: true;\n};\n\nexport type ButtonsCircularOptionPickerProps = WithBaseId &\n\tOmit< FullButtonsCircularOptionPickerProps, 'asButtons' >;\n\nexport type CircularOptionPickerProps =\n\t| FullListboxCircularOptionPickerProps\n\t| FullButtonsCircularOptionPickerProps;\n\nexport type DropdownLinkActionProps = {\n\tbuttonProps?: Omit<\n\t\tWordPressComponentProps< ButtonAsButtonProps, 'button', false >,\n\t\t'children'\n\t>;\n\tlinkText: string;\n\tdropdownProps: Omit< DropdownProps, 'className' | 'renderToggle' >;\n\tclassName?: string;\n};\n\nexport type OptionGroupProps = {\n\tclassName?: string;\n\toptions: ReactNode;\n};\n\nexport type OptionProps = Omit<\n\tWordPressComponentProps< ButtonAsButtonProps, 'button', false >,\n\t'isPressed' | 'className'\n> & {\n\tclassName?: string;\n\ttooltipText?: string;\n\tisSelected?: boolean;\n\t// `icon` is explicitly defined as 'check' by CircleOptionPicker.Option\n\t// and is not intended to be overridden.\n\t// `size` relies on the `Icon` component's default size of `24` to fit\n\t// `CircularOptionPicker`'s design, and should not be explicitly set.\n\tselectedIconProps?: Omit<\n\t\tReact.ComponentProps< typeof Icon >,\n\t\t'icon' | 'size'\n\t>;\n};\n\nexport type CircularOptionPickerCompositeStore = CompositeStore;\nexport type CircularOptionPickerContextProps = {\n\tbaseId?: string;\n\tcompositeStore?: CircularOptionPickerCompositeStore;\n};\n"], "mappings": ""}