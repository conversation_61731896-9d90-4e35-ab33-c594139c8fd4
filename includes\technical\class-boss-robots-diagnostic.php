<?php
/**
 * Classe de diagnostic pour le module robots.txt.
 *
 * Cette classe fournit des outils pour diagnostiquer et résoudre
 * les problèmes avec le fichier robots.txt.
 *
 * @since      1.1.0
 * @package    Boss_SEO
 * @subpackage Boss_SEO/includes/technical
 * <AUTHOR> SEO Team
 */
class Boss_Robots_Diagnostic {

    /**
     * Diagnostique les problèmes avec le robots.txt.
     *
     * @since    1.1.0
     * @return   array    Rapport de diagnostic.
     */
    public static function diagnose() {
        $report = array(
            'status' => 'ok',
            'issues' => array(),
            'recommendations' => array(),
            'tests' => array()
        );

        // Test 1: Vérifier le fichier physique
        $physical_file = ABSPATH . 'robots.txt';
        $has_physical_file = file_exists( $physical_file );
        
        $report['tests']['physical_file'] = array(
            'name' => __( 'Fichier robots.txt physique', 'boss-seo' ),
            'status' => $has_physical_file ? 'warning' : 'ok',
            'message' => $has_physical_file 
                ? __( 'Un fichier robots.txt physique existe', 'boss-seo' )
                : __( 'Aucun fichier robots.txt physique', 'boss-seo' )
        );

        if ( $has_physical_file ) {
            $report['status'] = 'warning';
            $report['issues'][] = __( 'Un fichier robots.txt physique empêche Boss SEO de gérer le contenu', 'boss-seo' );
            $report['recommendations'][] = __( 'Supprimez ou renommez le fichier robots.txt à la racine du site', 'boss-seo' );
        }

        // Test 2: Vérifier l'option en base
        $stored_content = get_option( 'boss_seo_robots_content', '' );
        $has_stored_content = ! empty( $stored_content );
        
        $report['tests']['stored_content'] = array(
            'name' => __( 'Contenu en base de données', 'boss-seo' ),
            'status' => $has_stored_content ? 'ok' : 'warning',
            'message' => $has_stored_content 
                ? __( 'Contenu robots.txt trouvé en base', 'boss-seo' )
                : __( 'Aucun contenu robots.txt en base', 'boss-seo' )
        );

        if ( ! $has_stored_content ) {
            $report['issues'][] = __( 'Aucun contenu robots.txt personnalisé n\'est défini', 'boss-seo' );
            $report['recommendations'][] = __( 'Définissez un contenu robots.txt dans l\'interface d\'administration', 'boss-seo' );
        }

        // Test 3: Vérifier la visibilité du site
        $is_public = get_option( 'blog_public' );
        
        $report['tests']['site_visibility'] = array(
            'name' => __( 'Visibilité du site', 'boss-seo' ),
            'status' => $is_public ? 'ok' : 'error',
            'message' => $is_public 
                ? __( 'Le site est public', 'boss-seo' )
                : __( 'Le site n\'est pas public', 'boss-seo' )
        );

        if ( ! $is_public ) {
            $report['status'] = 'error';
            $report['issues'][] = __( 'Le site n\'est pas public, le robots.txt ne sera pas généré', 'boss-seo' );
            $report['recommendations'][] = __( 'Activez la visibilité du site dans Réglages > Lecture', 'boss-seo' );
        }

        // Test 4: Vérifier le hook robots_txt
        global $wp_filter;
        $hook_registered = isset( $wp_filter['robots_txt'] );
        
        $report['tests']['hook_registered'] = array(
            'name' => __( 'Hook robots_txt', 'boss-seo' ),
            'status' => $hook_registered ? 'ok' : 'error',
            'message' => $hook_registered 
                ? __( 'Le hook robots_txt est enregistré', 'boss-seo' )
                : __( 'Le hook robots_txt n\'est pas enregistré', 'boss-seo' )
        );

        if ( ! $hook_registered ) {
            $report['status'] = 'error';
            $report['issues'][] = __( 'Le hook robots_txt n\'est pas enregistré', 'boss-seo' );
            $report['recommendations'][] = __( 'Vérifiez que Boss SEO est correctement activé', 'boss-seo' );
        }

        // Test 5: Tester la génération du robots.txt
        if ( $hook_registered && $is_public ) {
            $generated_content = apply_filters( 'robots_txt', '', $is_public );
            $has_generated_content = ! empty( $generated_content );
            
            $report['tests']['content_generation'] = array(
                'name' => __( 'Génération du contenu', 'boss-seo' ),
                'status' => $has_generated_content ? 'ok' : 'warning',
                'message' => $has_generated_content 
                    ? __( 'Le contenu robots.txt est généré correctement', 'boss-seo' )
                    : __( 'Aucun contenu robots.txt généré', 'boss-seo' )
            );

            if ( ! $has_generated_content ) {
                $report['issues'][] = __( 'Le hook ne génère pas de contenu', 'boss-seo' );
                $report['recommendations'][] = __( 'Vérifiez la configuration du module robots.txt', 'boss-seo' );
            }
        }

        return $report;
    }

    /**
     * Tente de résoudre automatiquement les problèmes détectés.
     *
     * @since    1.1.0
     * @return   array    Résultat des corrections.
     */
    public static function auto_fix() {
        $results = array(
            'fixed' => array(),
            'failed' => array()
        );

        // Correction 1: Supprimer le fichier robots.txt physique
        $physical_file = ABSPATH . 'robots.txt';
        if ( file_exists( $physical_file ) ) {
            // Sauvegarder le contenu avant suppression
            $content = file_get_contents( $physical_file );
            update_option( 'boss_seo_robots_backup', $content );
            
            if ( is_writable( $physical_file ) ) {
                if ( unlink( $physical_file ) ) {
                    $results['fixed'][] = __( 'Fichier robots.txt physique supprimé', 'boss-seo' );
                } else {
                    $results['failed'][] = __( 'Impossible de supprimer le fichier robots.txt physique', 'boss-seo' );
                }
            } else {
                $results['failed'][] = __( 'Le fichier robots.txt physique n\'est pas accessible en écriture', 'boss-seo' );
            }
        }

        // Correction 2: Créer un contenu par défaut si aucun n'existe
        $stored_content = get_option( 'boss_seo_robots_content', '' );
        if ( empty( $stored_content ) ) {
            $default_content = self::get_default_robots_content();
            update_option( 'boss_seo_robots_content', $default_content );
            $results['fixed'][] = __( 'Contenu robots.txt par défaut créé', 'boss-seo' );
        }

        return $results;
    }

    /**
     * Génère un contenu robots.txt par défaut.
     *
     * @since    1.1.0
     * @return   string    Contenu par défaut.
     */
    private static function get_default_robots_content() {
        $site_url = get_site_url();
        
        $content = "# Robots.txt généré par Boss SEO\n\n";
        $content .= "User-agent: *\n";
        $content .= "Disallow: /wp-admin/\n";
        $content .= "Allow: /wp-admin/admin-ajax.php\n";
        $content .= "Disallow: /wp-includes/\n";
        $content .= "Disallow: /wp-content/plugins/\n";
        $content .= "Disallow: /wp-content/themes/\n";
        $content .= "Disallow: /wp-login.php\n";
        $content .= "Disallow: /xmlrpc.php\n\n";
        $content .= "# Sitemap\n";
        $content .= "Sitemap: {$site_url}/sitemap.xml\n";
        
        return $content;
    }

    /**
     * Génère un rapport HTML du diagnostic.
     *
     * @since    1.1.0
     * @param    array    $report    Rapport de diagnostic.
     * @return   string             HTML du rapport.
     */
    public static function generate_html_report( $report ) {
        $html = '<div class="boss-robots-diagnostic">';
        
        // Statut global
        $status_class = $report['status'] === 'ok' ? 'success' : ($report['status'] === 'warning' ? 'warning' : 'error');
        $html .= '<div class="notice notice-' . $status_class . '">';
        $html .= '<h3>' . __( 'Diagnostic robots.txt', 'boss-seo' ) . '</h3>';
        
        if ( $report['status'] === 'ok' ) {
            $html .= '<p>' . __( 'Aucun problème détecté avec le robots.txt', 'boss-seo' ) . '</p>';
        } else {
            $html .= '<p>' . __( 'Problèmes détectés:', 'boss-seo' ) . '</p>';
            $html .= '<ul>';
            foreach ( $report['issues'] as $issue ) {
                $html .= '<li>' . esc_html( $issue ) . '</li>';
            }
            $html .= '</ul>';
            
            if ( ! empty( $report['recommendations'] ) ) {
                $html .= '<p><strong>' . __( 'Recommandations:', 'boss-seo' ) . '</strong></p>';
                $html .= '<ul>';
                foreach ( $report['recommendations'] as $recommendation ) {
                    $html .= '<li>' . esc_html( $recommendation ) . '</li>';
                }
                $html .= '</ul>';
            }
        }
        
        $html .= '</div>';
        
        // Détails des tests
        $html .= '<h4>' . __( 'Détails des tests', 'boss-seo' ) . '</h4>';
        $html .= '<table class="widefat">';
        $html .= '<thead><tr><th>' . __( 'Test', 'boss-seo' ) . '</th><th>' . __( 'Statut', 'boss-seo' ) . '</th><th>' . __( 'Message', 'boss-seo' ) . '</th></tr></thead>';
        $html .= '<tbody>';
        
        foreach ( $report['tests'] as $test ) {
            $status_icon = $test['status'] === 'ok' ? '✅' : ($test['status'] === 'warning' ? '⚠️' : '❌');
            $html .= '<tr>';
            $html .= '<td>' . esc_html( $test['name'] ) . '</td>';
            $html .= '<td>' . $status_icon . ' ' . esc_html( ucfirst( $test['status'] ) ) . '</td>';
            $html .= '<td>' . esc_html( $test['message'] ) . '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody></table>';
        $html .= '</div>';
        
        return $html;
    }
}
