{"version": 3, "names": ["ALIGNMENTS", "bottom", "alignItems", "justifyContent", "bottomLeft", "bottomRight", "center", "spaced", "left", "right", "stretch", "top", "topLeft", "topRight", "getAlignmentProps", "alignment", "alignmentProps"], "sources": ["@wordpress/components/src/grid/utils.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { CSSProperties } from 'react';\n\nconst ALIGNMENTS = {\n\tbottom: { alignItems: 'flex-end', justifyContent: 'center' },\n\tbottomLeft: { alignItems: 'flex-start', justifyContent: 'flex-end' },\n\tbottomRight: { alignItems: 'flex-end', justifyContent: 'flex-end' },\n\tcenter: { alignItems: 'center', justifyContent: 'center' },\n\tspaced: { alignItems: 'center', justifyContent: 'space-between' },\n\tleft: { alignItems: 'center', justifyContent: 'flex-start' },\n\tright: { alignItems: 'center', justifyContent: 'flex-end' },\n\tstretch: { alignItems: 'stretch' },\n\ttop: { alignItems: 'flex-start', justifyContent: 'center' },\n\ttopLeft: { alignItems: 'flex-start', justifyContent: 'flex-start' },\n\ttopRight: { alignItems: 'flex-start', justifyContent: 'flex-end' },\n};\n\nexport function getAlignmentProps( alignment?: keyof typeof ALIGNMENTS ): {\n\talignItems?: CSSProperties[ 'alignItems' ];\n\tjustifyContent?: CSSProperties[ 'justifyContent' ];\n} {\n\tconst alignmentProps = alignment ? ALIGNMENTS[ alignment ] : {};\n\n\treturn alignmentProps;\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;;AAGA,MAAMA,UAAU,GAAG;EAClBC,MAAM,EAAE;IAAEC,UAAU,EAAE,UAAU;IAAEC,cAAc,EAAE;EAAS,CAAC;EAC5DC,UAAU,EAAE;IAAEF,UAAU,EAAE,YAAY;IAAEC,cAAc,EAAE;EAAW,CAAC;EACpEE,WAAW,EAAE;IAAEH,UAAU,EAAE,UAAU;IAAEC,cAAc,EAAE;EAAW,CAAC;EACnEG,MAAM,EAAE;IAAEJ,UAAU,EAAE,QAAQ;IAAEC,cAAc,EAAE;EAAS,CAAC;EAC1DI,MAAM,EAAE;IAAEL,UAAU,EAAE,QAAQ;IAAEC,cAAc,EAAE;EAAgB,CAAC;EACjEK,IAAI,EAAE;IAAEN,UAAU,EAAE,QAAQ;IAAEC,cAAc,EAAE;EAAa,CAAC;EAC5DM,KAAK,EAAE;IAAEP,UAAU,EAAE,QAAQ;IAAEC,cAAc,EAAE;EAAW,CAAC;EAC3DO,OAAO,EAAE;IAAER,UAAU,EAAE;EAAU,CAAC;EAClCS,GAAG,EAAE;IAAET,UAAU,EAAE,YAAY;IAAEC,cAAc,EAAE;EAAS,CAAC;EAC3DS,OAAO,EAAE;IAAEV,UAAU,EAAE,YAAY;IAAEC,cAAc,EAAE;EAAa,CAAC;EACnEU,QAAQ,EAAE;IAAEX,UAAU,EAAE,YAAY;IAAEC,cAAc,EAAE;EAAW;AAClE,CAAC;AAEM,SAASW,iBAAiBA,CAAEC,SAAmC,EAGpE;EACD,MAAMC,cAAc,GAAGD,SAAS,GAAGf,UAAU,CAAEe,SAAS,CAAE,GAAG,CAAC,CAAC;EAE/D,OAAOC,cAAc;AACtB"}