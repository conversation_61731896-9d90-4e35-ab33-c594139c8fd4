{"version": 3, "names": ["_focalPointPickerStyle", "require", "_utils", "Media", "alt", "autoPlay", "src", "onLoad", "mediaRef", "muted", "props", "_react", "createElement", "MediaPlaceholder", "className", "ref", "isVideo", "isVideoType", "loop", "onLoadedData"], "sources": ["@wordpress/components/src/focal-point-picker/media.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { Ref } from 'react';\n\n/**\n * Internal dependencies\n */\nimport { MediaPlaceholder } from './styles/focal-point-picker-style';\nimport { isVideoType } from './utils';\nimport type { FocalPointPickerMediaProps } from './types';\n\nexport default function Media( {\n\talt,\n\tautoPlay,\n\tsrc,\n\tonLoad,\n\tmediaRef,\n\t// Exposing muted prop for test rendering purposes\n\t// https://github.com/testing-library/react-testing-library/issues/470\n\tmuted = true,\n\t...props\n}: FocalPointPickerMediaProps ) {\n\tif ( ! src ) {\n\t\treturn (\n\t\t\t<MediaPlaceholder\n\t\t\t\tclassName=\"components-focal-point-picker__media components-focal-point-picker__media--placeholder\"\n\t\t\t\tref={ mediaRef as Ref< HTMLDivElement > }\n\t\t\t\t{ ...props }\n\t\t\t/>\n\t\t);\n\t}\n\n\tconst isVideo = isVideoType( src );\n\n\treturn isVideo ? (\n\t\t<video\n\t\t\t{ ...props }\n\t\t\tautoPlay={ autoPlay }\n\t\t\tclassName=\"components-focal-point-picker__media components-focal-point-picker__media--video\"\n\t\t\tloop\n\t\t\tmuted={ muted }\n\t\t\tonLoadedData={ onLoad }\n\t\t\tref={ mediaRef as Ref< HTMLVideoElement > }\n\t\t\tsrc={ src }\n\t\t/>\n\t) : (\n\t\t<img\n\t\t\t{ ...props }\n\t\t\talt={ alt }\n\t\t\tclassName=\"components-focal-point-picker__media components-focal-point-picker__media--image\"\n\t\t\tonLoad={ onLoad }\n\t\t\tref={ mediaRef as Ref< HTMLImageElement > }\n\t\t\tsrc={ src }\n\t\t/>\n\t);\n}\n"], "mappings": ";;;;;;;AAQA,IAAAA,sBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AATA;AACA;AACA;;AAGA;AACA;AACA;;AAKe,SAASE,KAAKA,CAAE;EAC9BC,GAAG;EACHC,QAAQ;EACRC,GAAG;EACHC,MAAM;EACNC,QAAQ;EACR;EACA;EACAC,KAAK,GAAG,IAAI;EACZ,GAAGC;AACwB,CAAC,EAAG;EAC/B,IAAK,CAAEJ,GAAG,EAAG;IACZ,OACC,IAAAK,MAAA,CAAAC,aAAA,EAACZ,sBAAA,CAAAa,gBAAgB;MAChBC,SAAS,EAAC,wFAAwF;MAClGC,GAAG,EAAGP,QAAmC;MAAA,GACpCE;IAAK,CACV,CAAC;EAEJ;EAEA,MAAMM,OAAO,GAAG,IAAAC,kBAAW,EAAEX,GAAI,CAAC;EAElC,OAAOU,OAAO,GACb,IAAAL,MAAA,CAAAC,aAAA;IAAA,GACMF,KAAK;IACVL,QAAQ,EAAGA,QAAU;IACrBS,SAAS,EAAC,kFAAkF;IAC5FI,IAAI;IACJT,KAAK,EAAGA,KAAO;IACfU,YAAY,EAAGZ,MAAQ;IACvBQ,GAAG,EAAGP,QAAqC;IAC3CF,GAAG,EAAGA;EAAK,CACX,CAAC,GAEF,IAAAK,MAAA,CAAAC,aAAA;IAAA,GACMF,KAAK;IACVN,GAAG,EAAGA,GAAK;IACXU,SAAS,EAAC,kFAAkF;IAC5FP,MAAM,EAAGA,MAAQ;IACjBQ,GAAG,EAAGP,QAAqC;IAC3CF,GAAG,EAAGA;EAAK,CACX,CACD;AACF"}