{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_element", "_richText", "_compose", "_a11y", "_i18n", "_getDefaultUseItems", "_button", "_popover", "_visually<PERSON><PERSON>den", "_reactDom", "getAutoCompleterUI", "autocompleter", "useItems", "getDefaultUseItems", "AutocompleterUI", "filterValue", "instanceId", "listBoxId", "className", "selectedIndex", "onChangeOptions", "onSelect", "onReset", "reset", "contentRef", "items", "popoverAnchor", "useAnchor", "editableContentElement", "current", "needsA11yCompat", "setNeedsA11yCompat", "useState", "popoverRef", "useRef", "popoverRefs", "useMergeRefs", "useRefEffect", "node", "ownerDocument", "useOnClickOutside", "debouncedSpeak", "useDebounce", "speak", "announce", "options", "length", "sprintf", "_n", "__", "useLayoutEffect", "ListBox", "Component", "_react", "createElement", "id", "role", "map", "option", "index", "default", "key", "disabled", "isDisabled", "classnames", "onClick", "label", "Fragment", "focusOnMount", "onClose", "placement", "anchor", "ref", "createPortal", "VisuallyHidden", "body", "handler", "useEffect", "listener", "event", "contains", "target", "document", "addEventListener", "removeEventListener"], "sources": ["@wordpress/components/src/autocomplete/autocompleter-ui.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tuseLayoutEffect,\n\tuseRef,\n\tuseEffect,\n\tuseState,\n} from '@wordpress/element';\nimport { useAnchor } from '@wordpress/rich-text';\nimport { useDebounce, useMergeRefs, useRefEffect } from '@wordpress/compose';\nimport { speak } from '@wordpress/a11y';\nimport { __, _n, sprintf } from '@wordpress/i18n';\n\n/**\n * Internal dependencies\n */\nimport getDefaultUseItems from './get-default-use-items';\nimport Button from '../button';\nimport Popover from '../popover';\nimport { VisuallyHidden } from '../visually-hidden';\nimport { createPortal } from 'react-dom';\nimport type { AutocompleterUIProps, KeyedOption, WPCompleter } from './types';\n\nexport function getAutoCompleterUI( autocompleter: WPCompleter ) {\n\tconst useItems = autocompleter.useItems\n\t\t? autocompleter.useItems\n\t\t: getDefaultUseItems( autocompleter );\n\n\tfunction AutocompleterUI( {\n\t\tfilterValue,\n\t\tinstanceId,\n\t\tlistBoxId,\n\t\tclassName,\n\t\tselectedIndex,\n\t\tonChangeOptions,\n\t\tonSelect,\n\t\tonReset,\n\t\treset,\n\t\tcontentRef,\n\t}: AutocompleterUIProps ) {\n\t\tconst [ items ] = useItems( filterValue );\n\t\tconst popoverAnchor = useAnchor( {\n\t\t\teditableContentElement: contentRef.current,\n\t\t} );\n\n\t\tconst [ needsA11yCompat, setNeedsA11yCompat ] = useState( false );\n\t\tconst popoverRef = useRef< HTMLElement >( null );\n\t\tconst popoverRefs = useMergeRefs( [\n\t\t\tpopoverRef,\n\t\t\tuseRefEffect(\n\t\t\t\t( node ) => {\n\t\t\t\t\tif ( ! contentRef.current ) return;\n\n\t\t\t\t\t// If the popover is rendered in a different document than\n\t\t\t\t\t// the content, we need to duplicate the options list in the\n\t\t\t\t\t// content document so that it's available to the screen\n\t\t\t\t\t// readers, which check the DOM ID based aira-* attributes.\n\t\t\t\t\tsetNeedsA11yCompat(\n\t\t\t\t\t\tnode.ownerDocument !== contentRef.current.ownerDocument\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\t[ contentRef ]\n\t\t\t),\n\t\t] );\n\n\t\tuseOnClickOutside( popoverRef, reset );\n\n\t\tconst debouncedSpeak = useDebounce( speak, 500 );\n\n\t\tfunction announce( options: Array< KeyedOption > ) {\n\t\t\tif ( ! debouncedSpeak ) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif ( !! options.length ) {\n\t\t\t\tif ( filterValue ) {\n\t\t\t\t\tdebouncedSpeak(\n\t\t\t\t\t\tsprintf(\n\t\t\t\t\t\t\t/* translators: %d: number of results. */\n\t\t\t\t\t\t\t_n(\n\t\t\t\t\t\t\t\t'%d result found, use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\t\t'%d results found, use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\t\toptions.length\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\toptions.length\n\t\t\t\t\t\t),\n\t\t\t\t\t\t'assertive'\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\tdebouncedSpeak(\n\t\t\t\t\t\tsprintf(\n\t\t\t\t\t\t\t/* translators: %d: number of results. */\n\t\t\t\t\t\t\t_n(\n\t\t\t\t\t\t\t\t'Initial %d result loaded. Type to filter all available results. Use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\t\t'Initial %d results loaded. Type to filter all available results. Use up and down arrow keys to navigate.',\n\t\t\t\t\t\t\t\toptions.length\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t\toptions.length\n\t\t\t\t\t\t),\n\t\t\t\t\t\t'assertive'\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdebouncedSpeak( __( 'No results.' ), 'assertive' );\n\t\t\t}\n\t\t}\n\n\t\tuseLayoutEffect( () => {\n\t\t\tonChangeOptions( items );\n\t\t\tannounce( items );\n\t\t\t// Temporarily disabling exhaustive-deps to avoid introducing unexpected side effecst.\n\t\t\t// See https://github.com/WordPress/gutenberg/pull/41820\n\t\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t\t}, [ items ] );\n\n\t\tif ( items.length === 0 ) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconst ListBox = ( {\n\t\t\tComponent = 'div',\n\t\t}: {\n\t\t\tComponent?: React.ElementType;\n\t\t} ) => (\n\t\t\t<Component\n\t\t\t\tid={ listBoxId }\n\t\t\t\trole=\"listbox\"\n\t\t\t\tclassName=\"components-autocomplete__results\"\n\t\t\t>\n\t\t\t\t{ items.map( ( option, index ) => (\n\t\t\t\t\t<Button\n\t\t\t\t\t\tkey={ option.key }\n\t\t\t\t\t\tid={ `components-autocomplete-item-${ instanceId }-${ option.key }` }\n\t\t\t\t\t\trole=\"option\"\n\t\t\t\t\t\taria-selected={ index === selectedIndex }\n\t\t\t\t\t\tdisabled={ option.isDisabled }\n\t\t\t\t\t\tclassName={ classnames(\n\t\t\t\t\t\t\t'components-autocomplete__result',\n\t\t\t\t\t\t\tclassName,\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t'is-selected': index === selectedIndex,\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t) }\n\t\t\t\t\t\tonClick={ () => onSelect( option ) }\n\t\t\t\t\t>\n\t\t\t\t\t\t{ option.label }\n\t\t\t\t\t</Button>\n\t\t\t\t) ) }\n\t\t\t</Component>\n\t\t);\n\n\t\treturn (\n\t\t\t<>\n\t\t\t\t<Popover\n\t\t\t\t\tfocusOnMount={ false }\n\t\t\t\t\tonClose={ onReset }\n\t\t\t\t\tplacement=\"top-start\"\n\t\t\t\t\tclassName=\"components-autocomplete__popover\"\n\t\t\t\t\tanchor={ popoverAnchor }\n\t\t\t\t\tref={ popoverRefs }\n\t\t\t\t>\n\t\t\t\t\t<ListBox />\n\t\t\t\t</Popover>\n\t\t\t\t{ contentRef.current &&\n\t\t\t\t\tneedsA11yCompat &&\n\t\t\t\t\tcreatePortal(\n\t\t\t\t\t\t<ListBox Component={ VisuallyHidden } />,\n\t\t\t\t\t\tcontentRef.current.ownerDocument.body\n\t\t\t\t\t) }\n\t\t\t</>\n\t\t);\n\t}\n\n\treturn AutocompleterUI;\n}\n\nfunction useOnClickOutside(\n\tref: React.RefObject< HTMLElement >,\n\thandler: AutocompleterUIProps[ 'reset' ]\n) {\n\tuseEffect( () => {\n\t\tconst listener = ( event: MouseEvent | TouchEvent ) => {\n\t\t\t// Do nothing if clicking ref's element or descendent elements, or if the ref is not referencing an element\n\t\t\tif (\n\t\t\t\t! ref.current ||\n\t\t\t\tref.current.contains( event.target as Node )\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\thandler( event );\n\t\t};\n\t\tdocument.addEventListener( 'mousedown', listener );\n\t\tdocument.addEventListener( 'touchstart', listener );\n\t\treturn () => {\n\t\t\tdocument.removeEventListener( 'mousedown', listener );\n\t\t\tdocument.removeEventListener( 'touchstart', listener );\n\t\t};\n\t\t// Disable reason: `ref` is a ref object and should not be included in a\n\t\t// hook's dependency list.\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [ handler ] );\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AAMA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AAKA,IAAAM,mBAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,OAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,QAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,eAAA,GAAAT,OAAA;AACA,IAAAU,SAAA,GAAAV,OAAA;AA1BA;AACA;AACA;;AAGA;AACA;AACA;;AAYA;AACA;AACA;;AAQO,SAASW,kBAAkBA,CAAEC,aAA0B,EAAG;EAChE,MAAMC,QAAQ,GAAGD,aAAa,CAACC,QAAQ,GACpCD,aAAa,CAACC,QAAQ,GACtB,IAAAC,2BAAkB,EAAEF,aAAc,CAAC;EAEtC,SAASG,eAAeA,CAAE;IACzBC,WAAW;IACXC,UAAU;IACVC,SAAS;IACTC,SAAS;IACTC,aAAa;IACbC,eAAe;IACfC,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC;EACqB,CAAC,EAAG;IACzB,MAAM,CAAEC,KAAK,CAAE,GAAGb,QAAQ,CAAEG,WAAY,CAAC;IACzC,MAAMW,aAAa,GAAG,IAAAC,mBAAS,EAAE;MAChCC,sBAAsB,EAAEJ,UAAU,CAACK;IACpC,CAAE,CAAC;IAEH,MAAM,CAAEC,eAAe,EAAEC,kBAAkB,CAAE,GAAG,IAAAC,iBAAQ,EAAE,KAAM,CAAC;IACjE,MAAMC,UAAU,GAAG,IAAAC,eAAM,EAAiB,IAAK,CAAC;IAChD,MAAMC,WAAW,GAAG,IAAAC,qBAAY,EAAE,CACjCH,UAAU,EACV,IAAAI,qBAAY,EACTC,IAAI,IAAM;MACX,IAAK,CAAEd,UAAU,CAACK,OAAO,EAAG;;MAE5B;MACA;MACA;MACA;MACAE,kBAAkB,CACjBO,IAAI,CAACC,aAAa,KAAKf,UAAU,CAACK,OAAO,CAACU,aAC3C,CAAC;IACF,CAAC,EACD,CAAEf,UAAU,CACb,CAAC,CACA,CAAC;IAEHgB,iBAAiB,CAAEP,UAAU,EAAEV,KAAM,CAAC;IAEtC,MAAMkB,cAAc,GAAG,IAAAC,oBAAW,EAAEC,WAAK,EAAE,GAAI,CAAC;IAEhD,SAASC,QAAQA,CAAEC,OAA6B,EAAG;MAClD,IAAK,CAAEJ,cAAc,EAAG;QACvB;MACD;MACA,IAAK,CAAC,CAAEI,OAAO,CAACC,MAAM,EAAG;QACxB,IAAK/B,WAAW,EAAG;UAClB0B,cAAc,CACb,IAAAM,aAAO,GACN;UACA,IAAAC,QAAE,EACD,0DAA0D,EAC1D,2DAA2D,EAC3DH,OAAO,CAACC,MACT,CAAC,EACDD,OAAO,CAACC,MACT,CAAC,EACD,WACD,CAAC;QACF,CAAC,MAAM;UACNL,cAAc,CACb,IAAAM,aAAO,GACN;UACA,IAAAC,QAAE,EACD,yGAAyG,EACzG,0GAA0G,EAC1GH,OAAO,CAACC,MACT,CAAC,EACDD,OAAO,CAACC,MACT,CAAC,EACD,WACD,CAAC;QACF;MACD,CAAC,MAAM;QACNL,cAAc,CAAE,IAAAQ,QAAE,EAAE,aAAc,CAAC,EAAE,WAAY,CAAC;MACnD;IACD;IAEA,IAAAC,wBAAe,EAAE,MAAM;MACtB9B,eAAe,CAAEK,KAAM,CAAC;MACxBmB,QAAQ,CAAEnB,KAAM,CAAC;MACjB;MACA;MACA;IACD,CAAC,EAAE,CAAEA,KAAK,CAAG,CAAC;IAEd,IAAKA,KAAK,CAACqB,MAAM,KAAK,CAAC,EAAG;MACzB,OAAO,IAAI;IACZ;IAEA,MAAMK,OAAO,GAAGA,CAAE;MACjBC,SAAS,GAAG;IAGb,CAAC,KACA,IAAAC,MAAA,CAAAC,aAAA,EAACF,SAAS;MACTG,EAAE,EAAGtC,SAAW;MAChBuC,IAAI,EAAC,SAAS;MACdtC,SAAS,EAAC;IAAkC,GAE1CO,KAAK,CAACgC,GAAG,CAAE,CAAEC,MAAM,EAAEC,KAAK,KAC3B,IAAAN,MAAA,CAAAC,aAAA,EAAChD,OAAA,CAAAsD,OAAM;MACNC,GAAG,EAAGH,MAAM,CAACG,GAAK;MAClBN,EAAE,EAAI,gCAAgCvC,UAAY,IAAI0C,MAAM,CAACG,GAAK,EAAG;MACrEL,IAAI,EAAC,QAAQ;MACb,iBAAgBG,KAAK,KAAKxC,aAAe;MACzC2C,QAAQ,EAAGJ,MAAM,CAACK,UAAY;MAC9B7C,SAAS,EAAG,IAAA8C,mBAAU,EACrB,iCAAiC,EACjC9C,SAAS,EACT;QACC,aAAa,EAAEyC,KAAK,KAAKxC;MAC1B,CACD,CAAG;MACH8C,OAAO,EAAGA,CAAA,KAAM5C,QAAQ,CAAEqC,MAAO;IAAG,GAElCA,MAAM,CAACQ,KACF,CACP,CACQ,CACX;IAED,OACC,IAAAb,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAc,QAAA,QACC,IAAAd,MAAA,CAAAC,aAAA,EAAC/C,QAAA,CAAAqD,OAAO;MACPQ,YAAY,EAAG,KAAO;MACtBC,OAAO,EAAG/C,OAAS;MACnBgD,SAAS,EAAC,WAAW;MACrBpD,SAAS,EAAC,kCAAkC;MAC5CqD,MAAM,EAAG7C,aAAe;MACxB8C,GAAG,EAAGrC;IAAa,GAEnB,IAAAkB,MAAA,CAAAC,aAAA,EAACH,OAAO,MAAE,CACF,CAAC,EACR3B,UAAU,CAACK,OAAO,IACnBC,eAAe,IACf,IAAA2C,sBAAY,EACX,IAAApB,MAAA,CAAAC,aAAA,EAACH,OAAO;MAACC,SAAS,EAAGsB;IAAgB,CAAE,CAAC,EACxClD,UAAU,CAACK,OAAO,CAACU,aAAa,CAACoC,IAClC,CACA,CAAC;EAEL;EAEA,OAAO7D,eAAe;AACvB;AAEA,SAAS0B,iBAAiBA,CACzBgC,GAAmC,EACnCI,OAAwC,EACvC;EACD,IAAAC,kBAAS,EAAE,MAAM;IAChB,MAAMC,QAAQ,GAAKC,KAA8B,IAAM;MACtD;MACA,IACC,CAAEP,GAAG,CAAC3C,OAAO,IACb2C,GAAG,CAAC3C,OAAO,CAACmD,QAAQ,CAAED,KAAK,CAACE,MAAe,CAAC,EAC3C;QACD;MACD;MACAL,OAAO,CAAEG,KAAM,CAAC;IACjB,CAAC;IACDG,QAAQ,CAACC,gBAAgB,CAAE,WAAW,EAAEL,QAAS,CAAC;IAClDI,QAAQ,CAACC,gBAAgB,CAAE,YAAY,EAAEL,QAAS,CAAC;IACnD,OAAO,MAAM;MACZI,QAAQ,CAACE,mBAAmB,CAAE,WAAW,EAAEN,QAAS,CAAC;MACrDI,QAAQ,CAACE,mBAAmB,CAAE,YAAY,EAAEN,QAAS,CAAC;IACvD,CAAC;IACD;IACA;IACA;EACD,CAAC,EAAE,CAAEF,OAAO,CAAG,CAAC;AACjB"}