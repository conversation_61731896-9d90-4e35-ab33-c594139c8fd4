import { __ } from '@wordpress/i18n';
import {
  <PERSON>,
  Card<PERSON><PERSON>,
  CardHeader,
  Button,
  Dashicon
} from '@wordpress/components';

const MediaOverview = ({ stats }) => {
  return (
    <Card className="boss-mb-6">
      <CardHeader className="boss-border-b boss-border-gray-200">
        <h2 className="boss-text-lg boss-font-semibold boss-text-boss-dark">
          {__('Vue d\'ensemble des médias', 'boss-seo')}
        </h2>
      </CardHeader>
      <CardBody>
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-4 boss-gap-6 boss-mb-6">
          <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-center">
            <div className="boss-text-3xl boss-font-bold boss-text-boss-dark boss-mb-1">
              {stats.totalMedia}
            </div>
            <div className="boss-text-sm boss-text-boss-gray">
              {__('Total des médias', 'boss-seo')}
            </div>
          </div>
          
          <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-center">
            <div className="boss-text-3xl boss-font-bold boss-text-green-600 boss-mb-1">
              {stats.optimizedMedia}
            </div>
            <div className="boss-text-sm boss-text-boss-gray">
              {__('Médias optimisés', 'boss-seo')}
            </div>
          </div>
          
          <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-center">
            <div className="boss-text-3xl boss-font-bold boss-text-yellow-600 boss-mb-1">
              {stats.unoptimizedMedia}
            </div>
            <div className="boss-text-sm boss-text-boss-gray">
              {__('Médias non optimisés', 'boss-seo')}
            </div>
          </div>
          
          <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg boss-text-center">
            <div className="boss-text-3xl boss-font-bold boss-text-boss-primary boss-mb-1">
              {stats.spaceSaved} MB
            </div>
            <div className="boss-text-sm boss-text-boss-gray">
              {__('Espace économisé', 'boss-seo')}
            </div>
          </div>
        </div>
        
        <div className="boss-grid boss-grid-cols-1 md:boss-grid-cols-2 boss-gap-6">
          <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
              {__('Répartition par type', 'boss-seo')}
            </h3>
            
            <div className="boss-space-y-3">
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                <span className="boss-text-sm boss-text-boss-gray">JPEG</span>
                <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.mediaTypes.jpeg}</span>
              </div>
              <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5">
                <div className="boss-bg-blue-600 boss-h-2.5 boss-rounded-full" style={{ width: `${(stats.mediaTypes.jpeg / stats.totalMedia) * 100}%` }}></div>
              </div>
              
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                <span className="boss-text-sm boss-text-boss-gray">PNG</span>
                <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.mediaTypes.png}</span>
              </div>
              <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5">
                <div className="boss-bg-green-600 boss-h-2.5 boss-rounded-full" style={{ width: `${(stats.mediaTypes.png / stats.totalMedia) * 100}%` }}></div>
              </div>
              
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                <span className="boss-text-sm boss-text-boss-gray">GIF</span>
                <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.mediaTypes.gif}</span>
              </div>
              <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5">
                <div className="boss-bg-yellow-600 boss-h-2.5 boss-rounded-full" style={{ width: `${(stats.mediaTypes.gif / stats.totalMedia) * 100}%` }}></div>
              </div>
              
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                <span className="boss-text-sm boss-text-boss-gray">WebP</span>
                <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.mediaTypes.webp}</span>
              </div>
              <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5">
                <div className="boss-bg-purple-600 boss-h-2.5 boss-rounded-full" style={{ width: `${(stats.mediaTypes.webp / stats.totalMedia) * 100}%` }}></div>
              </div>
              
              <div className="boss-flex boss-justify-between boss-items-center boss-mb-1">
                <span className="boss-text-sm boss-text-boss-gray">SVG</span>
                <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.mediaTypes.svg}</span>
              </div>
              <div className="boss-w-full boss-bg-gray-200 boss-rounded-full boss-h-2.5">
                <div className="boss-bg-red-600 boss-h-2.5 boss-rounded-full" style={{ width: `${(stats.mediaTypes.svg / stats.totalMedia) * 100}%` }}></div>
              </div>
            </div>
          </div>
          
          <div className="boss-bg-gray-50 boss-p-4 boss-rounded-lg">
            <h3 className="boss-text-md boss-font-semibold boss-text-boss-dark boss-mb-3">
              {__('Problèmes détectés', 'boss-seo')}
            </h3>
            
            <div className="boss-space-y-3">
              <div className="boss-flex boss-items-start">
                <div className="boss-flex-shrink-0 boss-mr-3">
                  <Dashicon icon="warning" className="boss-text-yellow-600" />
                </div>
                <div>
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <h4 className="boss-font-medium boss-text-boss-dark">{__('Images non compressées', 'boss-seo')}</h4>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.issues.uncompressed}</span>
                  </div>
                  <p className="boss-text-sm boss-text-boss-gray">{__('Images qui pourraient être compressées davantage.', 'boss-seo')}</p>
                </div>
              </div>
              
              <div className="boss-flex boss-items-start">
                <div className="boss-flex-shrink-0 boss-mr-3">
                  <Dashicon icon="warning" className="boss-text-yellow-600" />
                </div>
                <div>
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <h4 className="boss-font-medium boss-text-boss-dark">{__('Images sans texte alternatif', 'boss-seo')}</h4>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.issues.missingAlt}</span>
                  </div>
                  <p className="boss-text-sm boss-text-boss-gray">{__('Images sans attribut alt pour l\'accessibilité et le SEO.', 'boss-seo')}</p>
                </div>
              </div>
              
              <div className="boss-flex boss-items-start">
                <div className="boss-flex-shrink-0 boss-mr-3">
                  <Dashicon icon="warning" className="boss-text-yellow-600" />
                </div>
                <div>
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <h4 className="boss-font-medium boss-text-boss-dark">{__('Images trop grandes', 'boss-seo')}</h4>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.issues.oversized}</span>
                  </div>
                  <p className="boss-text-sm boss-text-boss-gray">{__('Images avec des dimensions excessives pour leur utilisation.', 'boss-seo')}</p>
                </div>
              </div>
              
              <div className="boss-flex boss-items-start">
                <div className="boss-flex-shrink-0 boss-mr-3">
                  <Dashicon icon="warning" className="boss-text-yellow-600" />
                </div>
                <div>
                  <div className="boss-flex boss-justify-between boss-items-center">
                    <h4 className="boss-font-medium boss-text-boss-dark">{__('Images sans lazy loading', 'boss-seo')}</h4>
                    <span className="boss-text-sm boss-font-medium boss-text-boss-dark">{stats.issues.noLazyLoading}</span>
                  </div>
                  <p className="boss-text-sm boss-text-boss-gray">{__('Images sans attribut de chargement différé.', 'boss-seo')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="boss-mt-6 boss-flex boss-justify-center">
          <Button
            isPrimary
            href="#media-list"
          >
            {__('Voir tous les médias', 'boss-seo')}
          </Button>
        </div>
      </CardBody>
    </Card>
  );
};

export default MediaOverview;
