{"version": 3, "names": ["_react", "require", "_flex", "_utils", "_space", "_text", "_configValues", "_interopRequireDefault", "_EMOTION_STRINGIFIED_CSS_ERROR__", "CIRCLE_SIZE", "INNER_CIRCLE_SIZE", "deprecatedBottomMargin", "__nextHasNoMarginBottom", "css", "space", "process", "env", "NODE_ENV", "Root", "_base", "default", "Flex", "target", "label", "exports", "CircleRoot", "CONFIG", "borderWidth", "COLORS", "ui", "border", "CircleIndicatorWrapper", "name", "styles", "map", "toString", "CircleIndicator", "theme", "accent", "UnitText", "Text"], "sources": ["@wordpress/components/src/angle-picker-control/styles/angle-picker-control-styles.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport { Flex } from '../../flex';\nimport { COLORS } from '../../utils';\nimport { space } from '../../utils/space';\nimport { Text } from '../../text';\nimport CONFIG from '../../utils/config-values';\n\nimport type { AnglePickerControlProps } from '../types';\n\nconst CIRCLE_SIZE = 32;\nconst INNER_CIRCLE_SIZE = 6;\n\nconst deprecatedBottomMargin = ( {\n\t__nextHasNoMarginBottom,\n}: Pick< AnglePickerControlProps, '__nextHasNoMarginBottom' > ) => {\n\treturn ! __nextHasNoMarginBottom\n\t\t? css`\n\t\t\t\tmargin-bottom: ${ space( 2 ) };\n\t\t  `\n\t\t: '';\n};\n\nexport const Root = styled( Flex )`\n\t${ deprecatedBottomMargin }\n`;\n\nexport const CircleRoot = styled.div`\n\tborder-radius: 50%;\n\tborder: ${ CONFIG.borderWidth } solid ${ COLORS.ui.border };\n\tbox-sizing: border-box;\n\tcursor: grab;\n\theight: ${ CIRCLE_SIZE }px;\n\toverflow: hidden;\n\twidth: ${ CIRCLE_SIZE }px;\n\n\t:active {\n\t\tcursor: grabbing;\n\t}\n`;\n\nexport const CircleIndicatorWrapper = styled.div`\n\tbox-sizing: border-box;\n\tposition: relative;\n\twidth: 100%;\n\theight: 100%;\n\n\t:focus-visible {\n\t\toutline: none;\n\t}\n`;\n\nexport const CircleIndicator = styled.div`\n\tbackground: ${ COLORS.theme.accent };\n\tborder-radius: 50%;\n\tbox-sizing: border-box;\n\tdisplay: block;\n\tleft: 50%;\n\ttop: 4px;\n\ttransform: translateX( -50% );\n\tposition: absolute;\n\twidth: ${ INNER_CIRCLE_SIZE }px;\n\theight: ${ INNER_CIRCLE_SIZE }px;\n`;\n\nexport const UnitText = styled( Text )`\n\tcolor: ${ COLORS.theme.accent };\n\tmargin-right: ${ space( 3 ) };\n`;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAC,sBAAA,CAAAN,OAAA;AAA+C,SAAAO,iCAAA;AAI/C,MAAMC,WAAW,GAAG,EAAE;AACtB,MAAMC,iBAAiB,GAAG,CAAC;AAE3B,MAAMC,sBAAsB,GAAGA,CAAE;EAChCC;AAC2D,CAAC,KAAM;EAClE,OAAO,CAAEA,uBAAuB,oBAC7BC,UAAG,oBACe,IAAAC,YAAK,EAAE,CAAE,CAAC,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,4DAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA,krFAE5B,EAAE;AACN,CAAC;AAEM,MAAMC,IAAI,GAAG,kBAAAC,KAAA,CAAAC,OAAA,EAAQC,UAAI,EAAAN,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,CAC9BZ,sBAAsB,SAAAI,OAAA,CAAAC,GAAA,CAAAC,QAAA,grFACzB;AAACO,OAAA,CAAAN,IAAA,GAAAA,IAAA;AAEK,MAAMO,UAAU,OAAAN,KAAA,CAAAC,OAAA,SAAAL,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,gCAEXG,qBAAM,CAACC,WAAW,aAAYC,aAAM,CAACC,EAAE,CAACC,MAAM,gDAG9CrB,WAAW,+BAEZA,WAAW,oCAAAM,OAAA,CAAAC,GAAA,CAAAC,QAAA,grFAKrB;AAACO,OAAA,CAAAC,UAAA,GAAAA,UAAA;AAEK,MAAMM,sBAAsB,OAAAZ,KAAA,CAAAC,OAAA,SAAAL,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,GAAAR,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAe,IAAA;EAAAC,MAAA;AAAA;EAAAD,IAAA;EAAAC,MAAA;EAAAC,GAAA;EAAAC,QAAA,EAAA3B;AAAA,EASlC;AAACgB,OAAA,CAAAO,sBAAA,GAAAA,sBAAA;AAEK,MAAMK,eAAe,OAAAjB,KAAA,CAAAC,OAAA,SAAAL,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,kBACZK,aAAM,CAACS,KAAK,CAACC,MAAM,mIAQxB5B,iBAAiB,gBAChBA,iBAAiB,WAAAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,grFAC5B;AAACO,OAAA,CAAAY,eAAA,GAAAA,eAAA;AAEK,MAAMG,QAAQ,GAAG,kBAAApB,KAAA,CAAAC,OAAA,EAAQoB,UAAI,EAAAzB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAK,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,CAAC,CAAC,WAC3BK,aAAM,CAACS,KAAK,CAACC,MAAM,oBACZ,IAAAxB,YAAK,EAAE,CAAE,CAAC,SAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,grFAC3B;AAACO,OAAA,CAAAe,QAAA,GAAAA,QAAA"}