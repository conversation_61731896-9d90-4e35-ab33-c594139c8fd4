/** @format */

// <PERSON>'s new WordPress shades of gray, from http://codepen.io/hugobaeta/pen/grJjVp.
$black: #000;
$white: #fff;

$light-gray-500: #e2e4e7; // Good for "grayed" items and borders.
$light-gray-400: #e8eaeb; // Good for "readonly" input fields and special text selection.

// Dark opacities, for use with light themes.
$dark-opacity-900: rgba(#000510, 0.9);
$dark-opacity-800: rgba(#00000a, 0.85);
$dark-opacity-600: rgba(#000913, 0.75);


// Light opacities, for use with dark themes.
$light-opacity-700: rgba($white, 0.85);
$light-opacity-200: rgba($white, 0.6);
$light-opacity-light-100: rgba($white, 0.1);
$light-opacity-light-700: rgba($white, 0.4);


// Alert colors.
$alert-yellow: #f0b849;
$alert-red: #d94f4f;
$alert-green: #4ab866;
$red-30: #f86368;
$red-40: #e65054;
$red-50: #d63638;

// Primary Accent (Blues)
$blue-wordpress: #0087be;
$blue-medium: #00aadc;
$blue-60: #055d9c;
$blue-500: #016087;
// Primary tint color (light)
$blue-50: #2271b1;
$blue-40: #1689db;
// Primary tint color (dark)
$blue-30: #5198d9;

// Grays
$gray: #87a6bc;
$gray-light: lighten($gray, 33%); //#f3f6f8
$gray-dark: darken($gray, 38%); //#2e4453
$gray-900: #1a1a1a;
$gray-700: #757575;

// $gray-text: ideal for standard, non placeholder text
// $gray-text-min: minimum contrast needed for WCAG 2.0 AA on white background
$gray-text: $gray-dark;
$gray-text-min: darken($gray, 18%); //#537994

// Shades of gray
$gray-lighten-10: lighten($gray, 10%); // #a8bece
$gray-lighten-20: lighten($gray, 20%); // #c8d7e1
$gray-lighten-30: lighten($gray, 30%); // #e9eff3
$gray-darken-10: darken($gray, 10%);
$gray-darken-20: darken($gray, 20%); // #4f748e
$gray-darken-30: darken($gray, 30%); // #3d596d

// Custom
$toolbar-button: #7b9ab1;
$toolbar-button-disabled: $gray-lighten-30;

$background-dark-elevated: #2e2e2e;
$background-dark-secondary: #1e2327;

$button-fallback-bg: #595959;

// color-studio
$gray-0: #f6f7f7;
$gray-5: #dcdcde;
$gray-10: #c3c4c7;
$gray-20: #a7aaad;
$gray-30: #8e9196;
$gray-40: #787c82;
$gray-50: #646970;
$gray-60: #50575e;
$gray-70: #3c434a;
$gray-80: #2c3338;
$gray-90: #1d2327;
$gray-100: #101517;

$yellow-30: #deb100;
$yellow-50: #9d6e00;

// neutral (light) - black is a base color in light mode
$light-primary: #000d; //rgba(0, 0, 0, 0.87);
$light-secondary: #0009; //rgba(0, 0, 0, 0.6);
$light-tertiary: #0000006d; //rgba(0, 0, 0, 0.43);
$light-quaternary: #0000003f; //rgba(0, 0, 0, 0.25);
$light-dim: #0000001e; //rgba(0, 0, 0, 0.12);
$light-ultra-dim: #0000000a; //rgba(0, 0, 0, 0.04);

// neutral (dark) - white is a base color in dark mode
$dark-primary: #fffd; //rgba(255, 255, 255, 0.87);
$dark-secondary: #fff9; //rgba(255, 255, 255, 0.6);
$dark-tertiary: #ffffff6d; //rgba(255, 255, 255, 0.43);
$dark-quaternary: #ffffff3f; //rgba(255, 255, 255, 0.25);
$dark-ultra-dim: #ffffff14; //rgba(255, 255, 255, 0.08);
$dark-dim: #ffffff1f; //rgba(255, 255, 255, 0.12)

// Design Token colors
$app-background: $white;
$app-background-dark: $black;
$app-background-dark-alt: $background-dark-elevated;
$app-safe-area-background-dark: #141414;

$modal-background: $white;
$modal-background-dark: $background-dark-elevated;

$sub-heading: $gray-text-min;
$sub-heading-dark: $white;
/**
 * Deprecated colors.
 * Please avoid using these.
 */

$dark-gray-500: #555d66;
