{"version": 3, "names": ["_compose", "require", "_element", "_backdrop", "_interopRequireDefault", "_label", "_inputControlStyles", "_context", "_useDeprecatedProps", "useUniqueId", "idProp", "instanceId", "useInstanceId", "InputBase", "id", "getUIFlexProps", "labelPosition", "props", "direction", "expanded", "gap", "justify", "ref", "__next40pxDefaultSize", "__unstableInputWidth", "children", "className", "disabled", "hideLabelFromVision", "isFocused", "label", "prefix", "size", "suffix", "restProps", "useDeprecated36pxDefaultSizeProp", "<PERSON><PERSON><PERSON><PERSON>", "paddingLeft", "paddingRight", "getSizeConfig", "inputSize", "prefixSuffixContextValue", "useMemo", "InputControlPrefixWrapper", "InputControlSuffixWrapper", "_react", "createElement", "Root", "default", "htmlFor", "Container", "ContextSystemProvider", "value", "Prefix", "Suffix", "_default", "forwardRef", "exports"], "sources": ["@wordpress/components/src/input-control/input-base.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\nimport { forwardRef, useMemo } from '@wordpress/element';\n\n/**\n * Internal dependencies\n */\nimport Backdrop from './backdrop';\nimport Label from './label';\nimport {\n\tContainer,\n\tRoot,\n\tPrefix,\n\tSuffix,\n\tgetSizeConfig,\n} from './styles/input-control-styles';\nimport type { InputBaseProps, LabelPosition } from './types';\nimport type { WordPressComponentProps } from '../context';\nimport { ContextSystemProvider } from '../context';\nimport { useDeprecated36pxDefaultSizeProp } from '../utils/use-deprecated-props';\n\nfunction useUniqueId( idProp?: string ) {\n\tconst instanceId = useInstanceId( InputBase );\n\tconst id = `input-base-control-${ instanceId }`;\n\n\treturn idProp || id;\n}\n\n// Adapter to map props for the new ui/flex component.\nfunction getUIFlexProps( labelPosition?: LabelPosition ) {\n\tconst props: {\n\t\tdirection?: string;\n\t\tgap?: number;\n\t\tjustify?: string;\n\t\texpanded?: boolean;\n\t} = {};\n\tswitch ( labelPosition ) {\n\t\tcase 'top':\n\t\t\tprops.direction = 'column';\n\t\t\tprops.expanded = false;\n\t\t\tprops.gap = 0;\n\t\t\tbreak;\n\t\tcase 'bottom':\n\t\t\tprops.direction = 'column-reverse';\n\t\t\tprops.expanded = false;\n\t\t\tprops.gap = 0;\n\t\t\tbreak;\n\t\tcase 'edge':\n\t\t\tprops.justify = 'space-between';\n\t\t\tbreak;\n\t}\n\n\treturn props;\n}\n\nexport function InputBase(\n\tprops: WordPressComponentProps< InputBaseProps, 'div' >,\n\tref: ForwardedRef< HTMLDivElement >\n) {\n\tconst {\n\t\t__next40pxDefaultSize,\n\t\t__unstableInputWidth,\n\t\tchildren,\n\t\tclassName,\n\t\tdisabled = false,\n\t\thideLabelFromVision = false,\n\t\tlabelPosition,\n\t\tid: idProp,\n\t\tisFocused = false,\n\t\tlabel,\n\t\tprefix,\n\t\tsize = 'default',\n\t\tsuffix,\n\t\t...restProps\n\t} = useDeprecated36pxDefaultSizeProp(\n\t\tprops,\n\t\t'wp.components.InputBase',\n\t\t'6.4'\n\t);\n\n\tconst id = useUniqueId( idProp );\n\tconst hideLabel = hideLabelFromVision || ! label;\n\n\tconst { paddingLeft, paddingRight } = getSizeConfig( {\n\t\tinputSize: size,\n\t\t__next40pxDefaultSize,\n\t} );\n\tconst prefixSuffixContextValue = useMemo( () => {\n\t\treturn {\n\t\t\tInputControlPrefixWrapper: { paddingLeft },\n\t\t\tInputControlSuffixWrapper: { paddingRight },\n\t\t};\n\t}, [ paddingLeft, paddingRight ] );\n\n\treturn (\n\t\t// @ts-expect-error The `direction` prop from Flex (FlexDirection) conflicts with legacy SVGAttributes `direction` (string) that come from React intrinsic prop definitions.\n\t\t<Root\n\t\t\t{ ...restProps }\n\t\t\t{ ...getUIFlexProps( labelPosition ) }\n\t\t\tclassName={ className }\n\t\t\tgap={ 2 }\n\t\t\tisFocused={ isFocused }\n\t\t\tlabelPosition={ labelPosition }\n\t\t\tref={ ref }\n\t\t>\n\t\t\t<Label\n\t\t\t\tclassName=\"components-input-control__label\"\n\t\t\t\thideLabelFromVision={ hideLabelFromVision }\n\t\t\t\tlabelPosition={ labelPosition }\n\t\t\t\thtmlFor={ id }\n\t\t\t>\n\t\t\t\t{ label }\n\t\t\t</Label>\n\t\t\t<Container\n\t\t\t\t__unstableInputWidth={ __unstableInputWidth }\n\t\t\t\tclassName=\"components-input-control__container\"\n\t\t\t\tdisabled={ disabled }\n\t\t\t\thideLabel={ hideLabel }\n\t\t\t\tlabelPosition={ labelPosition }\n\t\t\t>\n\t\t\t\t<ContextSystemProvider value={ prefixSuffixContextValue }>\n\t\t\t\t\t{ prefix && (\n\t\t\t\t\t\t<Prefix className=\"components-input-control__prefix\">\n\t\t\t\t\t\t\t{ prefix }\n\t\t\t\t\t\t</Prefix>\n\t\t\t\t\t) }\n\t\t\t\t\t{ children }\n\t\t\t\t\t{ suffix && (\n\t\t\t\t\t\t<Suffix className=\"components-input-control__suffix\">\n\t\t\t\t\t\t\t{ suffix }\n\t\t\t\t\t\t</Suffix>\n\t\t\t\t\t) }\n\t\t\t\t</ContextSystemProvider>\n\t\t\t\t<Backdrop disabled={ disabled } isFocused={ isFocused } />\n\t\t\t</Container>\n\t\t</Root>\n\t);\n}\n\nexport default forwardRef( InputBase );\n"], "mappings": ";;;;;;;;;AAQA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAKA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,mBAAA,GAAAL,OAAA;AASA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,mBAAA,GAAAP,OAAA;AA1BA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;;AAeA,SAASQ,WAAWA,CAAEC,MAAe,EAAG;EACvC,MAAMC,UAAU,GAAG,IAAAC,sBAAa,EAAEC,SAAU,CAAC;EAC7C,MAAMC,EAAE,GAAI,sBAAsBH,UAAY,EAAC;EAE/C,OAAOD,MAAM,IAAII,EAAE;AACpB;;AAEA;AACA,SAASC,cAAcA,CAAEC,aAA6B,EAAG;EACxD,MAAMC,KAKL,GAAG,CAAC,CAAC;EACN,QAASD,aAAa;IACrB,KAAK,KAAK;MACTC,KAAK,CAACC,SAAS,GAAG,QAAQ;MAC1BD,KAAK,CAACE,QAAQ,GAAG,KAAK;MACtBF,KAAK,CAACG,GAAG,GAAG,CAAC;MACb;IACD,KAAK,QAAQ;MACZH,KAAK,CAACC,SAAS,GAAG,gBAAgB;MAClCD,KAAK,CAACE,QAAQ,GAAG,KAAK;MACtBF,KAAK,CAACG,GAAG,GAAG,CAAC;MACb;IACD,KAAK,MAAM;MACVH,KAAK,CAACI,OAAO,GAAG,eAAe;MAC/B;EACF;EAEA,OAAOJ,KAAK;AACb;AAEO,SAASJ,SAASA,CACxBI,KAAuD,EACvDK,GAAmC,EAClC;EACD,MAAM;IACLC,qBAAqB;IACrBC,oBAAoB;IACpBC,QAAQ;IACRC,SAAS;IACTC,QAAQ,GAAG,KAAK;IAChBC,mBAAmB,GAAG,KAAK;IAC3BZ,aAAa;IACbF,EAAE,EAAEJ,MAAM;IACVmB,SAAS,GAAG,KAAK;IACjBC,KAAK;IACLC,MAAM;IACNC,IAAI,GAAG,SAAS;IAChBC,MAAM;IACN,GAAGC;EACJ,CAAC,GAAG,IAAAC,oDAAgC,EACnClB,KAAK,EACL,yBAAyB,EACzB,KACD,CAAC;EAED,MAAMH,EAAE,GAAGL,WAAW,CAAEC,MAAO,CAAC;EAChC,MAAM0B,SAAS,GAAGR,mBAAmB,IAAI,CAAEE,KAAK;EAEhD,MAAM;IAAEO,WAAW;IAAEC;EAAa,CAAC,GAAG,IAAAC,iCAAa,EAAE;IACpDC,SAAS,EAAER,IAAI;IACfT;EACD,CAAE,CAAC;EACH,MAAMkB,wBAAwB,GAAG,IAAAC,gBAAO,EAAE,MAAM;IAC/C,OAAO;MACNC,yBAAyB,EAAE;QAAEN;MAAY,CAAC;MAC1CO,yBAAyB,EAAE;QAAEN;MAAa;IAC3C,CAAC;EACF,CAAC,EAAE,CAAED,WAAW,EAAEC,YAAY,CAAG,CAAC;EAElC;IACC;IACA,IAAAO,MAAA,CAAAC,aAAA,EAACxC,mBAAA,CAAAyC,IAAI;MAAA,GACCb,SAAS;MAAA,GACTnB,cAAc,CAAEC,aAAc,CAAC;MACpCU,SAAS,EAAGA,SAAW;MACvBN,GAAG,EAAG,CAAG;MACTS,SAAS,EAAGA,SAAW;MACvBb,aAAa,EAAGA,aAAe;MAC/BM,GAAG,EAAGA;IAAK,GAEX,IAAAuB,MAAA,CAAAC,aAAA,EAACzC,MAAA,CAAA2C,OAAK;MACLtB,SAAS,EAAC,iCAAiC;MAC3CE,mBAAmB,EAAGA,mBAAqB;MAC3CZ,aAAa,EAAGA,aAAe;MAC/BiC,OAAO,EAAGnC;IAAI,GAEZgB,KACI,CAAC,EACR,IAAAe,MAAA,CAAAC,aAAA,EAACxC,mBAAA,CAAA4C,SAAS;MACT1B,oBAAoB,EAAGA,oBAAsB;MAC7CE,SAAS,EAAC,qCAAqC;MAC/CC,QAAQ,EAAGA,QAAU;MACrBS,SAAS,EAAGA,SAAW;MACvBpB,aAAa,EAAGA;IAAe,GAE/B,IAAA6B,MAAA,CAAAC,aAAA,EAACvC,QAAA,CAAA4C,qBAAqB;MAACC,KAAK,EAAGX;IAA0B,GACtDV,MAAM,IACP,IAAAc,MAAA,CAAAC,aAAA,EAACxC,mBAAA,CAAA+C,MAAM;MAAC3B,SAAS,EAAC;IAAkC,GACjDK,MACK,CACR,EACCN,QAAQ,EACRQ,MAAM,IACP,IAAAY,MAAA,CAAAC,aAAA,EAACxC,mBAAA,CAAAgD,MAAM;MAAC5B,SAAS,EAAC;IAAkC,GACjDO,MACK,CAEa,CAAC,EACxB,IAAAY,MAAA,CAAAC,aAAA,EAAC3C,SAAA,CAAA6C,OAAQ;MAACrB,QAAQ,EAAGA,QAAU;MAACE,SAAS,EAAGA;IAAW,CAAE,CAC/C,CACN;EAAC;AAET;AAAC,IAAA0B,QAAA,GAEc,IAAAC,mBAAU,EAAE3C,SAAU,CAAC;AAAA4C,OAAA,CAAAT,OAAA,GAAAO,QAAA"}