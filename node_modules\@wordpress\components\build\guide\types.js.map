{"version": 3, "names": [], "sources": ["@wordpress/components/src/guide/types.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport type { ReactNode } from 'react';\n\n/**\n * Internal dependencies\n */\nimport type { ModalProps } from '../modal/types';\n\nexport type Page = {\n\t/**\n\t * Content of the page.\n\t */\n\tcontent: ReactNode;\n\t/**\n\t * Image displayed above the page content.\n\t */\n\timage?: ReactNode;\n};\n\nexport type GuideProps = {\n\t/**\n\t * Deprecated. Use `pages` prop instead.\n\t *\n\t * @deprecated since 5.5\n\t */\n\tchildren?: ReactNode;\n\t/**\n\t * A custom class to add to the modal.\n\t */\n\tclassName?: string;\n\t/**\n\t * Used as the modal's accessibility label.\n\t */\n\tcontentLabel: ModalProps[ 'contentLabel' ];\n\t/**\n\t * Use this to customize the label of the _Finish_ button shown at the end of the guide.\n\t *\n\t * @default 'Finish'\n\t */\n\tfinishButtonText?: string;\n\t/**\n\t * A function which is called when the guide is finished.\n\t */\n\tonFinish: ModalProps[ 'onRequestClose' ];\n\t/**\n\t * A list of objects describing each page in the guide. Each object **must** contain a `'content'` property and may optionally contain a `'image'` property.\n\t *\n\t * @default []\n\t */\n\tpages?: Page[];\n};\n\nexport type PageControlProps = {\n\t/**\n\t * Current page index.\n\t */\n\tcurrentPage: number;\n\t/**\n\t * Total number of pages.\n\t */\n\tnumberOfPages: number;\n\t/**\n\t * Called when user clicks on a `PageControlIcon` button.\n\t */\n\tsetCurrentPage: ( page: number ) => void;\n};\n"], "mappings": ""}