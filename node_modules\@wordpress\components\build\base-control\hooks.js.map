{"version": 3, "names": ["_compose", "require", "_", "_interopRequireDefault", "useBaseControlProps", "props", "help", "id", "preferredId", "restProps", "uniqueId", "useInstanceId", "BaseControl", "helpPropName", "baseControlProps", "controlProps"], "sources": ["@wordpress/components/src/base-control/hooks.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport { useInstanceId } from '@wordpress/compose';\n\n/**\n * Internal dependencies\n */\nimport BaseControl from '.';\nimport type { BaseControlProps } from './types';\n\n/**\n * Generate props for the `BaseControl` and the inner control itself.\n *\n * Namely, it takes care of generating a unique `id`, properly associating it with the `label` and `help` elements.\n *\n * @param props\n */\nexport function useBaseControlProps(\n\tprops: Omit< BaseControlProps, 'children' >\n) {\n\tconst { help, id: preferredId, ...restProps } = props;\n\n\tconst uniqueId = useInstanceId(\n\t\tBaseControl,\n\t\t'wp-components-base-control',\n\t\tpreferredId\n\t);\n\n\t// ARIA descriptions can only contain plain text, so fall back to aria-details if not.\n\tconst helpPropName =\n\t\ttypeof help === 'string' ? 'aria-describedby' : 'aria-details';\n\n\treturn {\n\t\tbaseControlProps: {\n\t\t\tid: uniqueId,\n\t\t\thelp,\n\t\t\t...restProps,\n\t\t},\n\t\tcontrolProps: {\n\t\t\tid: uniqueId,\n\t\t\t...( !! help ? { [ helpPropName ]: `${ uniqueId }__help` } : {} ),\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,CAAA,GAAAC,sBAAA,CAAAF,OAAA;AARA;AACA;AACA;;AAGA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,mBAAmBA,CAClCC,KAA2C,EAC1C;EACD,MAAM;IAAEC,IAAI;IAAEC,EAAE,EAAEC,WAAW;IAAE,GAAGC;EAAU,CAAC,GAAGJ,KAAK;EAErD,MAAMK,QAAQ,GAAG,IAAAC,sBAAa,EAC7BC,SAAW,EACX,4BAA4B,EAC5BJ,WACD,CAAC;;EAED;EACA,MAAMK,YAAY,GACjB,OAAOP,IAAI,KAAK,QAAQ,GAAG,kBAAkB,GAAG,cAAc;EAE/D,OAAO;IACNQ,gBAAgB,EAAE;MACjBP,EAAE,EAAEG,QAAQ;MACZJ,IAAI;MACJ,GAAGG;IACJ,CAAC;IACDM,YAAY,EAAE;MACbR,EAAE,EAAEG,QAAQ;MACZ,IAAK,CAAC,CAAEJ,IAAI,GAAG;QAAE,CAAEO,YAAY,GAAK,GAAGH,QAAU;MAAQ,CAAC,GAAG,CAAC,CAAC;IAChE;EACD,CAAC;AACF"}