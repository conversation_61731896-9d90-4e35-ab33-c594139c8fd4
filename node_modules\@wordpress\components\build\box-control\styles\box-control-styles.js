"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.StyledUnitControl = exports.ResetButton = exports.LinkedButtonWrapper = exports.InputWrapper = exports.FlexedRangeControl = exports.FlexedBoxControlIcon = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _icon = _interopRequireDefault(require("../icon"));
var _button = _interopRequireDefault(require("../../button"));
var _hStack = require("../../h-stack");
var _rangeControl = _interopRequireDefault(require("../../range-control"));
var _unitControl = _interopRequireDefault(require("../../unit-control"));
var _space = require("../../utils/space");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const StyledUnitControl = ( /*#__PURE__*/0, _base.default)(_unitControl.default, process.env.NODE_ENV === "production" ? {
  target: "e1jovhle5"
} : {
  target: "e1jovhle5",
  label: "StyledUnitControl"
})(process.env.NODE_ENV === "production" ? {
  name: "1ejyr19",
  styles: "max-width:90px"
} : {
  name: "1ejyr19",
  styles: "max-width:90px",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFjc0QiLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9ib3gtY29udHJvbC9zdHlsZXMvYm94LWNvbnRyb2wtc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuLyoqXG4gKiBJbnRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IEJveENvbnRyb2xJY29uIGZyb20gJy4uL2ljb24nO1xuaW1wb3J0IEJ1dHRvbiBmcm9tICcuLi8uLi9idXR0b24nO1xuaW1wb3J0IHsgSFN0YWNrIH0gZnJvbSAnLi4vLi4vaC1zdGFjayc7XG5pbXBvcnQgUmFuZ2VDb250cm9sIGZyb20gJy4uLy4uL3JhbmdlLWNvbnRyb2wnO1xuaW1wb3J0IFVuaXRDb250cm9sIGZyb20gJy4uLy4uL3VuaXQtY29udHJvbCc7XG5pbXBvcnQgeyBzcGFjZSB9IGZyb20gJy4uLy4uL3V0aWxzL3NwYWNlJztcblxuZXhwb3J0IGNvbnN0IFN0eWxlZFVuaXRDb250cm9sID0gc3R5bGVkKCBVbml0Q29udHJvbCApYFxuXHRtYXgtd2lkdGg6IDkwcHg7XG5gO1xuXG5leHBvcnQgY29uc3QgSW5wdXRXcmFwcGVyID0gc3R5bGVkKCBIU3RhY2sgKWBcblx0Z3JpZC1jb2x1bW46IDEgLyBzcGFuIDM7XG5gO1xuXG5leHBvcnQgY29uc3QgUmVzZXRCdXR0b24gPSBzdHlsZWQoIEJ1dHRvbiApYFxuXHRncmlkLWFyZWE6IDEgLyAyO1xuXHRqdXN0aWZ5LXNlbGY6IGVuZDtcbmA7XG5cbmV4cG9ydCBjb25zdCBMaW5rZWRCdXR0b25XcmFwcGVyID0gc3R5bGVkLmRpdmBcblx0Z3JpZC1hcmVhOiAxIC8gMztcblx0anVzdGlmeS1zZWxmOiBlbmQ7XG5gO1xuXG5leHBvcnQgY29uc3QgRmxleGVkQm94Q29udHJvbEljb24gPSBzdHlsZWQoIEJveENvbnRyb2xJY29uIClgXG5cdGZsZXg6IDAgMCBhdXRvO1xuYDtcblxuZXhwb3J0IGNvbnN0IEZsZXhlZFJhbmdlQ29udHJvbCA9IHN0eWxlZCggUmFuZ2VDb250cm9sIClgXG5cdHdpZHRoOiAxMDAlO1xuXHRtYXJnaW4taW5saW5lLWVuZDogJHsgc3BhY2UoIDIgKSB9O1xuYDtcbiJdfQ== */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.StyledUnitControl = StyledUnitControl;
const InputWrapper = ( /*#__PURE__*/0, _base.default)(_hStack.HStack, process.env.NODE_ENV === "production" ? {
  target: "e1jovhle4"
} : {
  target: "e1jovhle4",
  label: "InputWrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "1j1lmoi",
  styles: "grid-column:1/span 3"
} : {
  name: "1j1lmoi",
  styles: "grid-column:1/span 3",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFrQjRDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBCb3hDb250cm9sSWNvbiBmcm9tICcuLi9pY29uJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uLy4uL2gtc3RhY2snO1xuaW1wb3J0IFJhbmdlQ29udHJvbCBmcm9tICcuLi8uLi9yYW5nZS1jb250cm9sJztcbmltcG9ydCBVbml0Q29udHJvbCBmcm9tICcuLi8uLi91bml0LWNvbnRyb2wnO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi8uLi91dGlscy9zcGFjZSc7XG5cbmV4cG9ydCBjb25zdCBTdHlsZWRVbml0Q29udHJvbCA9IHN0eWxlZCggVW5pdENvbnRyb2wgKWBcblx0bWF4LXdpZHRoOiA5MHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IElucHV0V3JhcHBlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGdyaWQtY29sdW1uOiAxIC8gc3BhbiAzO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJlc2V0QnV0dG9uID0gc3R5bGVkKCBCdXR0b24gKWBcblx0Z3JpZC1hcmVhOiAxIC8gMjtcblx0anVzdGlmeS1zZWxmOiBlbmQ7XG5gO1xuXG5leHBvcnQgY29uc3QgTGlua2VkQnV0dG9uV3JhcHBlciA9IHN0eWxlZC5kaXZgXG5cdGdyaWQtYXJlYTogMSAvIDM7XG5cdGp1c3RpZnktc2VsZjogZW5kO1xuYDtcblxuZXhwb3J0IGNvbnN0IEZsZXhlZEJveENvbnRyb2xJY29uID0gc3R5bGVkKCBCb3hDb250cm9sSWNvbiApYFxuXHRmbGV4OiAwIDAgYXV0bztcbmA7XG5cbmV4cG9ydCBjb25zdCBGbGV4ZWRSYW5nZUNvbnRyb2wgPSBzdHlsZWQoIFJhbmdlQ29udHJvbCApYFxuXHR3aWR0aDogMTAwJTtcblx0bWFyZ2luLWlubGluZS1lbmQ6ICR7IHNwYWNlKCAyICkgfTtcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.InputWrapper = InputWrapper;
const ResetButton = ( /*#__PURE__*/0, _base.default)(_button.default, process.env.NODE_ENV === "production" ? {
  target: "e1jovhle3"
} : {
  target: "e1jovhle3",
  label: "ResetButton"
})(process.env.NODE_ENV === "production" ? {
  name: "tkya7b",
  styles: "grid-area:1/2;justify-self:end"
} : {
  name: "tkya7b",
  styles: "grid-area:1/2;justify-self:end",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFzQjJDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBCb3hDb250cm9sSWNvbiBmcm9tICcuLi9pY29uJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uLy4uL2gtc3RhY2snO1xuaW1wb3J0IFJhbmdlQ29udHJvbCBmcm9tICcuLi8uLi9yYW5nZS1jb250cm9sJztcbmltcG9ydCBVbml0Q29udHJvbCBmcm9tICcuLi8uLi91bml0LWNvbnRyb2wnO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi8uLi91dGlscy9zcGFjZSc7XG5cbmV4cG9ydCBjb25zdCBTdHlsZWRVbml0Q29udHJvbCA9IHN0eWxlZCggVW5pdENvbnRyb2wgKWBcblx0bWF4LXdpZHRoOiA5MHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IElucHV0V3JhcHBlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGdyaWQtY29sdW1uOiAxIC8gc3BhbiAzO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJlc2V0QnV0dG9uID0gc3R5bGVkKCBCdXR0b24gKWBcblx0Z3JpZC1hcmVhOiAxIC8gMjtcblx0anVzdGlmeS1zZWxmOiBlbmQ7XG5gO1xuXG5leHBvcnQgY29uc3QgTGlua2VkQnV0dG9uV3JhcHBlciA9IHN0eWxlZC5kaXZgXG5cdGdyaWQtYXJlYTogMSAvIDM7XG5cdGp1c3RpZnktc2VsZjogZW5kO1xuYDtcblxuZXhwb3J0IGNvbnN0IEZsZXhlZEJveENvbnRyb2xJY29uID0gc3R5bGVkKCBCb3hDb250cm9sSWNvbiApYFxuXHRmbGV4OiAwIDAgYXV0bztcbmA7XG5cbmV4cG9ydCBjb25zdCBGbGV4ZWRSYW5nZUNvbnRyb2wgPSBzdHlsZWQoIFJhbmdlQ29udHJvbCApYFxuXHR3aWR0aDogMTAwJTtcblx0bWFyZ2luLWlubGluZS1lbmQ6ICR7IHNwYWNlKCAyICkgfTtcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.ResetButton = ResetButton;
const LinkedButtonWrapper = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "e1jovhle2"
} : {
  target: "e1jovhle2",
  label: "LinkedButtonWrapper"
})(process.env.NODE_ENV === "production" ? {
  name: "1dfa8al",
  styles: "grid-area:1/3;justify-self:end"
} : {
  name: "1dfa8al",
  styles: "grid-area:1/3;justify-self:end",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUEyQjZDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBCb3hDb250cm9sSWNvbiBmcm9tICcuLi9pY29uJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uLy4uL2gtc3RhY2snO1xuaW1wb3J0IFJhbmdlQ29udHJvbCBmcm9tICcuLi8uLi9yYW5nZS1jb250cm9sJztcbmltcG9ydCBVbml0Q29udHJvbCBmcm9tICcuLi8uLi91bml0LWNvbnRyb2wnO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi8uLi91dGlscy9zcGFjZSc7XG5cbmV4cG9ydCBjb25zdCBTdHlsZWRVbml0Q29udHJvbCA9IHN0eWxlZCggVW5pdENvbnRyb2wgKWBcblx0bWF4LXdpZHRoOiA5MHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IElucHV0V3JhcHBlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGdyaWQtY29sdW1uOiAxIC8gc3BhbiAzO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJlc2V0QnV0dG9uID0gc3R5bGVkKCBCdXR0b24gKWBcblx0Z3JpZC1hcmVhOiAxIC8gMjtcblx0anVzdGlmeS1zZWxmOiBlbmQ7XG5gO1xuXG5leHBvcnQgY29uc3QgTGlua2VkQnV0dG9uV3JhcHBlciA9IHN0eWxlZC5kaXZgXG5cdGdyaWQtYXJlYTogMSAvIDM7XG5cdGp1c3RpZnktc2VsZjogZW5kO1xuYDtcblxuZXhwb3J0IGNvbnN0IEZsZXhlZEJveENvbnRyb2xJY29uID0gc3R5bGVkKCBCb3hDb250cm9sSWNvbiApYFxuXHRmbGV4OiAwIDAgYXV0bztcbmA7XG5cbmV4cG9ydCBjb25zdCBGbGV4ZWRSYW5nZUNvbnRyb2wgPSBzdHlsZWQoIFJhbmdlQ29udHJvbCApYFxuXHR3aWR0aDogMTAwJTtcblx0bWFyZ2luLWlubGluZS1lbmQ6ICR7IHNwYWNlKCAyICkgfTtcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.LinkedButtonWrapper = LinkedButtonWrapper;
const FlexedBoxControlIcon = ( /*#__PURE__*/0, _base.default)(_icon.default, process.env.NODE_ENV === "production" ? {
  target: "e1jovhle1"
} : {
  target: "e1jovhle1",
  label: "FlexedBoxControlIcon"
})(process.env.NODE_ENV === "production" ? {
  name: "ou8xsw",
  styles: "flex:0 0 auto"
} : {
  name: "ou8xsw",
  styles: "flex:0 0 auto",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFnQzREIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBCb3hDb250cm9sSWNvbiBmcm9tICcuLi9pY29uJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uLy4uL2gtc3RhY2snO1xuaW1wb3J0IFJhbmdlQ29udHJvbCBmcm9tICcuLi8uLi9yYW5nZS1jb250cm9sJztcbmltcG9ydCBVbml0Q29udHJvbCBmcm9tICcuLi8uLi91bml0LWNvbnRyb2wnO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi8uLi91dGlscy9zcGFjZSc7XG5cbmV4cG9ydCBjb25zdCBTdHlsZWRVbml0Q29udHJvbCA9IHN0eWxlZCggVW5pdENvbnRyb2wgKWBcblx0bWF4LXdpZHRoOiA5MHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IElucHV0V3JhcHBlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGdyaWQtY29sdW1uOiAxIC8gc3BhbiAzO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJlc2V0QnV0dG9uID0gc3R5bGVkKCBCdXR0b24gKWBcblx0Z3JpZC1hcmVhOiAxIC8gMjtcblx0anVzdGlmeS1zZWxmOiBlbmQ7XG5gO1xuXG5leHBvcnQgY29uc3QgTGlua2VkQnV0dG9uV3JhcHBlciA9IHN0eWxlZC5kaXZgXG5cdGdyaWQtYXJlYTogMSAvIDM7XG5cdGp1c3RpZnktc2VsZjogZW5kO1xuYDtcblxuZXhwb3J0IGNvbnN0IEZsZXhlZEJveENvbnRyb2xJY29uID0gc3R5bGVkKCBCb3hDb250cm9sSWNvbiApYFxuXHRmbGV4OiAwIDAgYXV0bztcbmA7XG5cbmV4cG9ydCBjb25zdCBGbGV4ZWRSYW5nZUNvbnRyb2wgPSBzdHlsZWQoIFJhbmdlQ29udHJvbCApYFxuXHR3aWR0aDogMTAwJTtcblx0bWFyZ2luLWlubGluZS1lbmQ6ICR7IHNwYWNlKCAyICkgfTtcbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.FlexedBoxControlIcon = FlexedBoxControlIcon;
const FlexedRangeControl = ( /*#__PURE__*/0, _base.default)(_rangeControl.default, process.env.NODE_ENV === "production" ? {
  target: "e1jovhle0"
} : {
  target: "e1jovhle0",
  label: "FlexedRangeControl"
})("width:100%;margin-inline-end:", (0, _space.space)(2), ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFvQ3dEIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvYm94LWNvbnRyb2wvc3R5bGVzL2JveC1jb250cm9sLXN0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBCb3hDb250cm9sSWNvbiBmcm9tICcuLi9pY29uJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uLy4uL2gtc3RhY2snO1xuaW1wb3J0IFJhbmdlQ29udHJvbCBmcm9tICcuLi8uLi9yYW5nZS1jb250cm9sJztcbmltcG9ydCBVbml0Q29udHJvbCBmcm9tICcuLi8uLi91bml0LWNvbnRyb2wnO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi8uLi91dGlscy9zcGFjZSc7XG5cbmV4cG9ydCBjb25zdCBTdHlsZWRVbml0Q29udHJvbCA9IHN0eWxlZCggVW5pdENvbnRyb2wgKWBcblx0bWF4LXdpZHRoOiA5MHB4O1xuYDtcblxuZXhwb3J0IGNvbnN0IElucHV0V3JhcHBlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGdyaWQtY29sdW1uOiAxIC8gc3BhbiAzO1xuYDtcblxuZXhwb3J0IGNvbnN0IFJlc2V0QnV0dG9uID0gc3R5bGVkKCBCdXR0b24gKWBcblx0Z3JpZC1hcmVhOiAxIC8gMjtcblx0anVzdGlmeS1zZWxmOiBlbmQ7XG5gO1xuXG5leHBvcnQgY29uc3QgTGlua2VkQnV0dG9uV3JhcHBlciA9IHN0eWxlZC5kaXZgXG5cdGdyaWQtYXJlYTogMSAvIDM7XG5cdGp1c3RpZnktc2VsZjogZW5kO1xuYDtcblxuZXhwb3J0IGNvbnN0IEZsZXhlZEJveENvbnRyb2xJY29uID0gc3R5bGVkKCBCb3hDb250cm9sSWNvbiApYFxuXHRmbGV4OiAwIDAgYXV0bztcbmA7XG5cbmV4cG9ydCBjb25zdCBGbGV4ZWRSYW5nZUNvbnRyb2wgPSBzdHlsZWQoIFJhbmdlQ29udHJvbCApYFxuXHR3aWR0aDogMTAwJTtcblx0bWFyZ2luLWlubGluZS1lbmQ6ICR7IHNwYWNlKCAyICkgfTtcbmA7XG4iXX0= */"));
exports.FlexedRangeControl = FlexedRangeControl;
//# sourceMappingURL=box-control-styles.js.map