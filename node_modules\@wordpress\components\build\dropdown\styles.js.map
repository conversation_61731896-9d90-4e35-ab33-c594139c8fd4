{"version": 3, "names": ["_react", "require", "_space", "padding", "paddingSize", "paddingValues", "small", "space", "medium", "css", "process", "env", "NODE_ENV", "DropdownContentWrapperDiv", "_base", "default", "target", "label", "exports"], "sources": ["@wordpress/components/src/dropdown/styles.ts"], "sourcesContent": ["/**\n * External dependencies\n */\nimport { css } from '@emotion/react';\nimport styled from '@emotion/styled';\n\n/**\n * Internal dependencies\n */\nimport { space } from '../utils/space';\nimport type { DropdownContentWrapperProps } from './types';\n\nconst padding = ( { paddingSize = 'small' }: DropdownContentWrapperProps ) => {\n\tif ( paddingSize === 'none' ) return;\n\n\tconst paddingValues = {\n\t\tsmall: space( 2 ),\n\t\tmedium: space( 4 ),\n\t};\n\n\treturn css`\n\t\tpadding: ${ paddingValues[ paddingSize ] || paddingValues.small };\n\t`;\n};\n\nexport const DropdownContentWrapperDiv = styled.div< DropdownContentWrapperProps >`\n\t// Negative margin to reset (offset) the default padding on .components-popover__content\n\tmargin-left: ${ space( -2 ) };\n\tmargin-right: ${ space( -2 ) };\n\t&:first-of-type {\n\t\tmargin-top: ${ space( -2 ) };\n\t}\n\t&:last-of-type {\n\t\tmargin-bottom: ${ space( -2 ) };\n\t}\n\n\t${ padding };\n`;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAMA,IAAAC,MAAA,GAAAD,OAAA;AATA;AACA;AACA;;AAIA;AACA;AACA;;AAIA,MAAME,OAAO,GAAGA,CAAE;EAAEC,WAAW,GAAG;AAAqC,CAAC,KAAM;EAC7E,IAAKA,WAAW,KAAK,MAAM,EAAG;EAE9B,MAAMC,aAAa,GAAG;IACrBC,KAAK,EAAE,IAAAC,YAAK,EAAE,CAAE,CAAC;IACjBC,MAAM,EAAE,IAAAD,YAAK,EAAE,CAAE;EAClB,CAAC;EAED,wBAAOE,UAAG,cACGJ,aAAa,CAAED,WAAW,CAAE,IAAIC,aAAa,CAACC,KAAK,SAAAI,OAAA,CAAAC,GAAA,CAAAC,QAAA,6CAAAF,OAAA,CAAAC,GAAA,CAAAC,QAAA;AAEjE,CAAC;AAEM,MAAMC,yBAAyB,OAAAC,KAAA,CAAAC,OAAA,SAAAL,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAAAI,MAAA;AAAA;EAAAA,MAAA;EAAAC,KAAA;AAAA,mBAErB,IAAAV,YAAK,EAAE,CAAC,CAAE,CAAC,oBACV,IAAAA,YAAK,EAAE,CAAC,CAAE,CAAC,kCAEZ,IAAAA,YAAK,EAAE,CAAC,CAAE,CAAC,qCAGR,IAAAA,YAAK,EAAE,CAAC,CAAE,CAAC,QAG3BJ,OAAO,SAAAO,OAAA,CAAAC,GAAA,CAAAC,QAAA,4kDACV;AAACM,OAAA,CAAAL,yBAAA,GAAAA,yBAAA"}