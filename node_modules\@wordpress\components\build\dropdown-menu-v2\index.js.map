{"version": 3, "names": ["Ariakit", "_interopRequireWildcard", "require", "_element", "_i18n", "_icons", "_primitives", "_context", "_icon", "_interopRequireDefault", "Styled", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "DropdownMenuContext", "createContext", "undefined", "exports", "DropdownMenuItem", "forwardRef", "prefix", "suffix", "children", "hideOnClick", "props", "ref", "dropdownMenuContext", "useContext", "_react", "createElement", "accessibleWhenDisabled", "store", "ItemPrefixWrapper", "DropdownMenuItemContentWrapper", "DropdownMenuItemChildrenWrapper", "ItemSuffixWrapper", "DropdownMenuCheckboxItem", "MenuItemCheck", "render", "style", "width", "height", "icon", "check", "size", "radioCheck", "SVG", "xmlns", "viewBox", "Circle", "cx", "cy", "r", "DropdownMenuRadioItem", "DropdownMenuGroup", "UnconnectedDropdownMenu", "_props$placement", "open", "defaultOpen", "onOpenChange", "placement", "trigger", "gutter", "shift", "modal", "variant", "otherProps", "useContextSystem", "parentContext", "computedDirection", "isRTL", "computedPlacement", "test", "replace", "dropdownMenuStore", "useMenuStore", "parent", "focusLoop", "<PERSON><PERSON><PERSON>", "willBeOpen", "rtl", "contextValue", "useMemo", "appliedPlacementSide", "useState", "split", "isValidElement", "type", "console", "warn", "hideOnEscape", "useCallback", "event", "preventDefault", "wrapperProps", "dir", "direction", "Fragment", "MenuButton", "cloneElement", "SubmenuChevronIcon", "chevronRightSmall", "preserveAspectRatio", "DropdownMenu", "hideOnHoverOutside", "unmountOnHide", "Provider", "value", "contextConnect", "DropdownMenuSeparator", "DropdownMenuItemLabel", "numberOfLines", "DropdownMenuItemHelpText"], "sources": ["@wordpress/components/src/dropdown-menu-v2/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\n// eslint-disable-next-line no-restricted-imports\nimport * as Ariakit from '@ariakit/react';\n\n/**\n * WordPress dependencies\n */\nimport {\n\tforwardRef,\n\tcreateContext,\n\tuseContext,\n\tuseMemo,\n\tcloneElement,\n\tisValidElement,\n\tuseCallback,\n} from '@wordpress/element';\nimport { isRTL } from '@wordpress/i18n';\nimport { check, chevronRightSmall } from '@wordpress/icons';\nimport { SVG, Circle } from '@wordpress/primitives';\n\n/**\n * Internal dependencies\n */\nimport { useContextSystem, contextConnect } from '../context';\nimport type { WordPressComponentProps } from '../context';\nimport Icon from '../icon';\nimport type {\n\tDropdownMenuContext as DropdownMenuContextType,\n\tDropdownMenuProps,\n\tDropdownMenuGroupProps,\n\tDropdownMenuItemProps,\n\tDropdownMenuCheckboxItemProps,\n\tDropdownMenuRadioItemProps,\n\tDropdownMenuSeparatorProps,\n} from './types';\nimport * as Styled from './styles';\n\nexport const DropdownMenuContext = createContext<\n\tDropdownMenuContextType | undefined\n>( undefined );\n\nexport const DropdownMenuItem = forwardRef<\n\tHTMLDivElement,\n\tWordPressComponentProps< DropdownMenuItemProps, 'div', false >\n>( function DropdownMenuItem(\n\t{ prefix, suffix, children, hideOnClick = true, ...props },\n\tref\n) {\n\tconst dropdownMenuContext = useContext( DropdownMenuContext );\n\n\treturn (\n\t\t<Styled.DropdownMenuItem\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t\taccessibleWhenDisabled\n\t\t\thideOnClick={ hideOnClick }\n\t\t\tstore={ dropdownMenuContext?.store }\n\t\t>\n\t\t\t<Styled.ItemPrefixWrapper>{ prefix }</Styled.ItemPrefixWrapper>\n\n\t\t\t<Styled.DropdownMenuItemContentWrapper>\n\t\t\t\t<Styled.DropdownMenuItemChildrenWrapper>\n\t\t\t\t\t{ children }\n\t\t\t\t</Styled.DropdownMenuItemChildrenWrapper>\n\n\t\t\t\t{ suffix && (\n\t\t\t\t\t<Styled.ItemSuffixWrapper>\n\t\t\t\t\t\t{ suffix }\n\t\t\t\t\t</Styled.ItemSuffixWrapper>\n\t\t\t\t) }\n\t\t\t</Styled.DropdownMenuItemContentWrapper>\n\t\t</Styled.DropdownMenuItem>\n\t);\n} );\n\nexport const DropdownMenuCheckboxItem = forwardRef<\n\tHTMLDivElement,\n\tWordPressComponentProps< DropdownMenuCheckboxItemProps, 'div', false >\n>( function DropdownMenuCheckboxItem(\n\t{ suffix, children, hideOnClick = false, ...props },\n\tref\n) {\n\tconst dropdownMenuContext = useContext( DropdownMenuContext );\n\n\treturn (\n\t\t<Styled.DropdownMenuCheckboxItem\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t\taccessibleWhenDisabled\n\t\t\thideOnClick={ hideOnClick }\n\t\t\tstore={ dropdownMenuContext?.store }\n\t\t>\n\t\t\t<Ariakit.MenuItemCheck\n\t\t\t\tstore={ dropdownMenuContext?.store }\n\t\t\t\trender={ <Styled.ItemPrefixWrapper /> }\n\t\t\t\t// Override some ariakit inline styles\n\t\t\t\tstyle={ { width: 'auto', height: 'auto' } }\n\t\t\t>\n\t\t\t\t<Icon icon={ check } size={ 24 } />\n\t\t\t</Ariakit.MenuItemCheck>\n\n\t\t\t<Styled.DropdownMenuItemContentWrapper>\n\t\t\t\t<Styled.DropdownMenuItemChildrenWrapper>\n\t\t\t\t\t{ children }\n\t\t\t\t</Styled.DropdownMenuItemChildrenWrapper>\n\n\t\t\t\t{ suffix && (\n\t\t\t\t\t<Styled.ItemSuffixWrapper>\n\t\t\t\t\t\t{ suffix }\n\t\t\t\t\t</Styled.ItemSuffixWrapper>\n\t\t\t\t) }\n\t\t\t</Styled.DropdownMenuItemContentWrapper>\n\t\t</Styled.DropdownMenuCheckboxItem>\n\t);\n} );\n\nconst radioCheck = (\n\t<SVG xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\">\n\t\t<Circle cx={ 12 } cy={ 12 } r={ 3 }></Circle>\n\t</SVG>\n);\n\nexport const DropdownMenuRadioItem = forwardRef<\n\tHTMLDivElement,\n\tWordPressComponentProps< DropdownMenuRadioItemProps, 'div', false >\n>( function DropdownMenuRadioItem(\n\t{ suffix, children, hideOnClick = false, ...props },\n\tref\n) {\n\tconst dropdownMenuContext = useContext( DropdownMenuContext );\n\n\treturn (\n\t\t<Styled.DropdownMenuRadioItem\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t\taccessibleWhenDisabled\n\t\t\thideOnClick={ hideOnClick }\n\t\t\tstore={ dropdownMenuContext?.store }\n\t\t>\n\t\t\t<Ariakit.MenuItemCheck\n\t\t\t\tstore={ dropdownMenuContext?.store }\n\t\t\t\trender={ <Styled.ItemPrefixWrapper /> }\n\t\t\t\t// Override some ariakit inline styles\n\t\t\t\tstyle={ { width: 'auto', height: 'auto' } }\n\t\t\t>\n\t\t\t\t<Icon icon={ radioCheck } size={ 24 } />\n\t\t\t</Ariakit.MenuItemCheck>\n\n\t\t\t<Styled.DropdownMenuItemContentWrapper>\n\t\t\t\t<Styled.DropdownMenuItemChildrenWrapper>\n\t\t\t\t\t{ children }\n\t\t\t\t</Styled.DropdownMenuItemChildrenWrapper>\n\n\t\t\t\t{ suffix && (\n\t\t\t\t\t<Styled.ItemSuffixWrapper>\n\t\t\t\t\t\t{ suffix }\n\t\t\t\t\t</Styled.ItemSuffixWrapper>\n\t\t\t\t) }\n\t\t\t</Styled.DropdownMenuItemContentWrapper>\n\t\t</Styled.DropdownMenuRadioItem>\n\t);\n} );\n\nexport const DropdownMenuGroup = forwardRef<\n\tHTMLDivElement,\n\tWordPressComponentProps< DropdownMenuGroupProps, 'div', false >\n>( function DropdownMenuGroup( props, ref ) {\n\tconst dropdownMenuContext = useContext( DropdownMenuContext );\n\treturn (\n\t\t<Styled.DropdownMenuGroup\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t\tstore={ dropdownMenuContext?.store }\n\t\t/>\n\t);\n} );\n\nconst UnconnectedDropdownMenu = (\n\tprops: WordPressComponentProps< DropdownMenuProps, 'div', false >,\n\tref: React.ForwardedRef< HTMLDivElement >\n) => {\n\tconst {\n\t\t// Store props\n\t\topen,\n\t\tdefaultOpen = false,\n\t\tonOpenChange,\n\t\tplacement,\n\n\t\t// Menu trigger props\n\t\ttrigger,\n\n\t\t// Menu props\n\t\tgutter,\n\t\tchildren,\n\t\tshift,\n\t\tmodal = true,\n\n\t\t// From internal components context\n\t\tvariant,\n\n\t\t// Rest\n\t\t...otherProps\n\t} = useContextSystem<\n\t\ttypeof props & Pick< DropdownMenuContextType, 'variant' >\n\t>( props, 'DropdownMenu' );\n\n\tconst parentContext = useContext( DropdownMenuContext );\n\n\tconst computedDirection = isRTL() ? 'rtl' : 'ltr';\n\n\t// If an explicit value for the `placement` prop is not passed,\n\t// apply a default placement of `bottom-start` for the root dropdown,\n\t// and of `right-start` for nested dropdowns.\n\tlet computedPlacement =\n\t\tprops.placement ??\n\t\t( parentContext?.store ? 'right-start' : 'bottom-start' );\n\t// Swap left/right in case of RTL direction\n\tif ( computedDirection === 'rtl' ) {\n\t\tif ( /right/.test( computedPlacement ) ) {\n\t\t\tcomputedPlacement = computedPlacement.replace(\n\t\t\t\t'right',\n\t\t\t\t'left'\n\t\t\t) as typeof computedPlacement;\n\t\t} else if ( /left/.test( computedPlacement ) ) {\n\t\t\tcomputedPlacement = computedPlacement.replace(\n\t\t\t\t'left',\n\t\t\t\t'right'\n\t\t\t) as typeof computedPlacement;\n\t\t}\n\t}\n\n\tconst dropdownMenuStore = Ariakit.useMenuStore( {\n\t\tparent: parentContext?.store,\n\t\topen,\n\t\tdefaultOpen,\n\t\tplacement: computedPlacement,\n\t\tfocusLoop: true,\n\t\tsetOpen( willBeOpen ) {\n\t\t\tonOpenChange?.( willBeOpen );\n\t\t},\n\t\trtl: computedDirection === 'rtl',\n\t} );\n\n\tconst contextValue = useMemo(\n\t\t() => ( { store: dropdownMenuStore, variant } ),\n\t\t[ dropdownMenuStore, variant ]\n\t);\n\n\t// Extract the side from the applied placement — useful for animations.\n\tconst appliedPlacementSide = dropdownMenuStore\n\t\t.useState( 'placement' )\n\t\t.split( '-' )[ 0 ];\n\n\tif (\n\t\tdropdownMenuStore.parent &&\n\t\t! ( isValidElement( trigger ) && DropdownMenuItem === trigger.type )\n\t) {\n\t\t// eslint-disable-next-line no-console\n\t\tconsole.warn(\n\t\t\t'For nested DropdownMenus, the `trigger` should always be a `DropdownMenuItem`.'\n\t\t);\n\t}\n\n\tconst hideOnEscape = useCallback(\n\t\t( event: React.KeyboardEvent< Element > ) => {\n\t\t\t// Pressing Escape can cause unexpected consequences (ie. exiting\n\t\t\t// full screen mode on MacOs, close parent modals...).\n\t\t\tevent.preventDefault();\n\t\t\t// Returning `true` causes the menu to hide.\n\t\t\treturn true;\n\t\t},\n\t\t[]\n\t);\n\n\tconst wrapperProps = useMemo(\n\t\t() => ( {\n\t\t\tdir: computedDirection,\n\t\t\tstyle: {\n\t\t\t\tdirection:\n\t\t\t\t\tcomputedDirection as React.CSSProperties[ 'direction' ],\n\t\t\t},\n\t\t} ),\n\t\t[ computedDirection ]\n\t);\n\n\treturn (\n\t\t<>\n\t\t\t{ /* Menu trigger */ }\n\t\t\t<Ariakit.MenuButton\n\t\t\t\tref={ ref }\n\t\t\t\tstore={ dropdownMenuStore }\n\t\t\t\trender={\n\t\t\t\t\tdropdownMenuStore.parent\n\t\t\t\t\t\t? cloneElement( trigger, {\n\t\t\t\t\t\t\t\t// Add submenu arrow, unless a `suffix` is explicitly specified\n\t\t\t\t\t\t\t\tsuffix: (\n\t\t\t\t\t\t\t\t\t<>\n\t\t\t\t\t\t\t\t\t\t{ trigger.props.suffix }\n\t\t\t\t\t\t\t\t\t\t<Styled.SubmenuChevronIcon\n\t\t\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\t\t\ticon={ chevronRightSmall }\n\t\t\t\t\t\t\t\t\t\t\tsize={ 24 }\n\t\t\t\t\t\t\t\t\t\t\tpreserveAspectRatio=\"xMidYMid slice\"\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</>\n\t\t\t\t\t\t\t\t),\n\t\t\t\t\t\t  } )\n\t\t\t\t\t\t: trigger\n\t\t\t\t}\n\t\t\t/>\n\n\t\t\t{ /* Menu popover */ }\n\t\t\t<Styled.DropdownMenu\n\t\t\t\t{ ...otherProps }\n\t\t\t\tmodal={ modal }\n\t\t\t\tstore={ dropdownMenuStore }\n\t\t\t\t// Root menu has an 8px distance from its trigger,\n\t\t\t\t// otherwise 0 (which causes the submenu to slightly overlap)\n\t\t\t\tgutter={ gutter ?? ( dropdownMenuStore.parent ? 0 : 8 ) }\n\t\t\t\t// Align nested menu by the same (but opposite) amount\n\t\t\t\t// as the menu container's padding.\n\t\t\t\tshift={ shift ?? ( dropdownMenuStore.parent ? -4 : 0 ) }\n\t\t\t\thideOnHoverOutside={ false }\n\t\t\t\tdata-side={ appliedPlacementSide }\n\t\t\t\tvariant={ variant }\n\t\t\t\twrapperProps={ wrapperProps }\n\t\t\t\thideOnEscape={ hideOnEscape }\n\t\t\t\tunmountOnHide\n\t\t\t>\n\t\t\t\t<DropdownMenuContext.Provider value={ contextValue }>\n\t\t\t\t\t{ children }\n\t\t\t\t</DropdownMenuContext.Provider>\n\t\t\t</Styled.DropdownMenu>\n\t\t</>\n\t);\n};\nexport const DropdownMenu = contextConnect(\n\tUnconnectedDropdownMenu,\n\t'DropdownMenu'\n);\n\nexport const DropdownMenuSeparator = forwardRef<\n\tHTMLHRElement,\n\tWordPressComponentProps< DropdownMenuSeparatorProps, 'hr', false >\n>( function DropdownMenuSeparator( props, ref ) {\n\tconst dropdownMenuContext = useContext( DropdownMenuContext );\n\treturn (\n\t\t<Styled.DropdownMenuSeparator\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t\tstore={ dropdownMenuContext?.store }\n\t\t\tvariant={ dropdownMenuContext?.variant }\n\t\t/>\n\t);\n} );\n\nexport const DropdownMenuItemLabel = forwardRef<\n\tHTMLSpanElement,\n\tWordPressComponentProps< { children: React.ReactNode }, 'span', true >\n>( function DropdownMenuItemLabel( props, ref ) {\n\treturn (\n\t\t<Styled.DropdownMenuItemLabel\n\t\t\tnumberOfLines={ 1 }\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t/>\n\t);\n} );\n\nexport const DropdownMenuItemHelpText = forwardRef<\n\tHTMLSpanElement,\n\tWordPressComponentProps< { children: React.ReactNode }, 'span', true >\n>( function DropdownMenuItemHelpText( props, ref ) {\n\treturn (\n\t\t<Styled.DropdownMenuItemHelpText\n\t\t\tnumberOfLines={ 2 }\n\t\t\tref={ ref }\n\t\t\t{ ...props }\n\t\t/>\n\t);\n} );\n"], "mappings": ";;;;;;;;AAIA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAKA,IAAAC,QAAA,GAAAD,OAAA;AASA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAKA,IAAAK,QAAA,GAAAL,OAAA;AAEA,IAAAM,KAAA,GAAAC,sBAAA,CAAAP,OAAA;AAUA,IAAAQ,MAAA,GAAAT,uBAAA,CAAAC,OAAA;AAAmC,SAAAS,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAX,wBAAAe,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AArCnC;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAcA;AACA;AACA;;AAeO,MAAMW,mBAAmB,GAAG,IAAAC,sBAAa,EAE7CC,SAAU,CAAC;AAACC,OAAA,CAAAH,mBAAA,GAAAA,mBAAA;AAER,MAAMI,gBAAgB,GAAG,IAAAC,mBAAU,EAGvC,SAASD,gBAAgBA,CAC3B;EAAEE,MAAM;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,WAAW,GAAG,IAAI;EAAE,GAAGC;AAAM,CAAC,EAC1DC,GAAG,EACF;EACD,MAAMC,mBAAmB,GAAG,IAAAC,mBAAU,EAAEb,mBAAoB,CAAC;EAE7D,OACC,IAAAc,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC2B,gBAAgB;IACvBO,GAAG,EAAGA,GAAK;IAAA,GACND,KAAK;IACVM,sBAAsB;IACtBP,WAAW,EAAGA,WAAa;IAC3BQ,KAAK,EAAGL,mBAAmB,EAAEK;EAAO,GAEpC,IAAAH,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAACyC,iBAAiB,QAAGZ,MAAkC,CAAC,EAE/D,IAAAQ,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC0C,8BAA8B,QACrC,IAAAL,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC2C,+BAA+B,QACpCZ,QACqC,CAAC,EAEvCD,MAAM,IACP,IAAAO,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC4C,iBAAiB,QACtBd,MACuB,CAEW,CACf,CAAC;AAE5B,CAAE,CAAC;AAACJ,OAAA,CAAAC,gBAAA,GAAAA,gBAAA;AAEG,MAAMkB,wBAAwB,GAAG,IAAAjB,mBAAU,EAG/C,SAASiB,wBAAwBA,CACnC;EAAEf,MAAM;EAAEC,QAAQ;EAAEC,WAAW,GAAG,KAAK;EAAE,GAAGC;AAAM,CAAC,EACnDC,GAAG,EACF;EACD,MAAMC,mBAAmB,GAAG,IAAAC,mBAAU,EAAEb,mBAAoB,CAAC;EAE7D,OACC,IAAAc,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC6C,wBAAwB;IAC/BX,GAAG,EAAGA,GAAK;IAAA,GACND,KAAK;IACVM,sBAAsB;IACtBP,WAAW,EAAGA,WAAa;IAC3BQ,KAAK,EAAGL,mBAAmB,EAAEK;EAAO,GAEpC,IAAAH,MAAA,CAAAC,aAAA,EAAChD,OAAO,CAACwD,aAAa;IACrBN,KAAK,EAAGL,mBAAmB,EAAEK,KAAO;IACpCO,MAAM,EAAG,IAAAV,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAACyC,iBAAiB,MAAE;IACpC;IAAA;IACAO,KAAK,EAAG;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAG,GAE3C,IAAAb,MAAA,CAAAC,aAAA,EAACxC,KAAA,CAAAU,OAAI;IAAC2C,IAAI,EAAGC,YAAO;IAACC,IAAI,EAAG;EAAI,CAAE,CACZ,CAAC,EAExB,IAAAhB,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC0C,8BAA8B,QACrC,IAAAL,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC2C,+BAA+B,QACpCZ,QACqC,CAAC,EAEvCD,MAAM,IACP,IAAAO,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC4C,iBAAiB,QACtBd,MACuB,CAEW,CACP,CAAC;AAEpC,CAAE,CAAC;AAACJ,OAAA,CAAAmB,wBAAA,GAAAA,wBAAA;AAEJ,MAAMS,UAAU,GACf,IAAAjB,MAAA,CAAAC,aAAA,EAAC1C,WAAA,CAAA2D,GAAG;EAACC,KAAK,EAAC,4BAA4B;EAACC,OAAO,EAAC;AAAW,GAC1D,IAAApB,MAAA,CAAAC,aAAA,EAAC1C,WAAA,CAAA8D,MAAM;EAACC,EAAE,EAAG,EAAI;EAACC,EAAE,EAAG,EAAI;EAACC,CAAC,EAAG;AAAG,CAAS,CACxC,CACL;AAEM,MAAMC,qBAAqB,GAAG,IAAAlC,mBAAU,EAG5C,SAASkC,qBAAqBA,CAChC;EAAEhC,MAAM;EAAEC,QAAQ;EAAEC,WAAW,GAAG,KAAK;EAAE,GAAGC;AAAM,CAAC,EACnDC,GAAG,EACF;EACD,MAAMC,mBAAmB,GAAG,IAAAC,mBAAU,EAAEb,mBAAoB,CAAC;EAE7D,OACC,IAAAc,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC8D,qBAAqB;IAC5B5B,GAAG,EAAGA,GAAK;IAAA,GACND,KAAK;IACVM,sBAAsB;IACtBP,WAAW,EAAGA,WAAa;IAC3BQ,KAAK,EAAGL,mBAAmB,EAAEK;EAAO,GAEpC,IAAAH,MAAA,CAAAC,aAAA,EAAChD,OAAO,CAACwD,aAAa;IACrBN,KAAK,EAAGL,mBAAmB,EAAEK,KAAO;IACpCO,MAAM,EAAG,IAAAV,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAACyC,iBAAiB,MAAE;IACpC;IAAA;IACAO,KAAK,EAAG;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAG,GAE3C,IAAAb,MAAA,CAAAC,aAAA,EAACxC,KAAA,CAAAU,OAAI;IAAC2C,IAAI,EAAGG,UAAY;IAACD,IAAI,EAAG;EAAI,CAAE,CACjB,CAAC,EAExB,IAAAhB,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC0C,8BAA8B,QACrC,IAAAL,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC2C,+BAA+B,QACpCZ,QACqC,CAAC,EAEvCD,MAAM,IACP,IAAAO,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC4C,iBAAiB,QACtBd,MACuB,CAEW,CACV,CAAC;AAEjC,CAAE,CAAC;AAACJ,OAAA,CAAAoC,qBAAA,GAAAA,qBAAA;AAEG,MAAMC,iBAAiB,GAAG,IAAAnC,mBAAU,EAGxC,SAASmC,iBAAiBA,CAAE9B,KAAK,EAAEC,GAAG,EAAG;EAC3C,MAAMC,mBAAmB,GAAG,IAAAC,mBAAU,EAAEb,mBAAoB,CAAC;EAC7D,OACC,IAAAc,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC+D,iBAAiB;IACxB7B,GAAG,EAAGA,GAAK;IAAA,GACND,KAAK;IACVO,KAAK,EAAGL,mBAAmB,EAAEK;EAAO,CACpC,CAAC;AAEJ,CAAE,CAAC;AAACd,OAAA,CAAAqC,iBAAA,GAAAA,iBAAA;AAEJ,MAAMC,uBAAuB,GAAGA,CAC/B/B,KAAiE,EACjEC,GAAyC,KACrC;EAAA,IAAA+B,gBAAA;EACJ,MAAM;IACL;IACAC,IAAI;IACJC,WAAW,GAAG,KAAK;IACnBC,YAAY;IACZC,SAAS;IAET;IACAC,OAAO;IAEP;IACAC,MAAM;IACNxC,QAAQ;IACRyC,KAAK;IACLC,KAAK,GAAG,IAAI;IAEZ;IACAC,OAAO;IAEP;IACA,GAAGC;EACJ,CAAC,GAAG,IAAAC,yBAAgB,EAEjB3C,KAAK,EAAE,cAAe,CAAC;EAE1B,MAAM4C,aAAa,GAAG,IAAAzC,mBAAU,EAAEb,mBAAoB,CAAC;EAEvD,MAAMuD,iBAAiB,GAAG,IAAAC,WAAK,EAAC,CAAC,GAAG,KAAK,GAAG,KAAK;;EAEjD;EACA;EACA;EACA,IAAIC,iBAAiB,IAAAf,gBAAA,GACpBhC,KAAK,CAACoC,SAAS,cAAAJ,gBAAA,cAAAA,gBAAA,GACbY,aAAa,EAAErC,KAAK,GAAG,aAAa,GAAG,cAAgB;EAC1D;EACA,IAAKsC,iBAAiB,KAAK,KAAK,EAAG;IAClC,IAAK,OAAO,CAACG,IAAI,CAAED,iBAAkB,CAAC,EAAG;MACxCA,iBAAiB,GAAGA,iBAAiB,CAACE,OAAO,CAC5C,OAAO,EACP,MACD,CAA6B;IAC9B,CAAC,MAAM,IAAK,MAAM,CAACD,IAAI,CAAED,iBAAkB,CAAC,EAAG;MAC9CA,iBAAiB,GAAGA,iBAAiB,CAACE,OAAO,CAC5C,MAAM,EACN,OACD,CAA6B;IAC9B;EACD;EAEA,MAAMC,iBAAiB,GAAG7F,OAAO,CAAC8F,YAAY,CAAE;IAC/CC,MAAM,EAAER,aAAa,EAAErC,KAAK;IAC5B0B,IAAI;IACJC,WAAW;IACXE,SAAS,EAAEW,iBAAiB;IAC5BM,SAAS,EAAE,IAAI;IACfC,OAAOA,CAAEC,UAAU,EAAG;MACrBpB,YAAY,GAAIoB,UAAW,CAAC;IAC7B,CAAC;IACDC,GAAG,EAAEX,iBAAiB,KAAK;EAC5B,CAAE,CAAC;EAEH,MAAMY,YAAY,GAAG,IAAAC,gBAAO,EAC3B,OAAQ;IAAEnD,KAAK,EAAE2C,iBAAiB;IAAET;EAAQ,CAAC,CAAE,EAC/C,CAAES,iBAAiB,EAAET,OAAO,CAC7B,CAAC;;EAED;EACA,MAAMkB,oBAAoB,GAAGT,iBAAiB,CAC5CU,QAAQ,CAAE,WAAY,CAAC,CACvBC,KAAK,CAAE,GAAI,CAAC,CAAE,CAAC,CAAE;EAEnB,IACCX,iBAAiB,CAACE,MAAM,IACxB,EAAI,IAAAU,uBAAc,EAAEzB,OAAQ,CAAC,IAAI3C,gBAAgB,KAAK2C,OAAO,CAAC0B,IAAI,CAAE,EACnE;IACD;IACAC,OAAO,CAACC,IAAI,CACX,gFACD,CAAC;EACF;EAEA,MAAMC,YAAY,GAAG,IAAAC,oBAAW,EAC7BC,KAAqC,IAAM;IAC5C;IACA;IACAA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB;IACA,OAAO,IAAI;EACZ,CAAC,EACD,EACD,CAAC;EAED,MAAMC,YAAY,GAAG,IAAAZ,gBAAO,EAC3B,OAAQ;IACPa,GAAG,EAAE1B,iBAAiB;IACtB9B,KAAK,EAAE;MACNyD,SAAS,EACR3B;IACF;EACD,CAAC,CAAE,EACH,CAAEA,iBAAiB,CACpB,CAAC;EAED,OACC,IAAAzC,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAqE,QAAA,QAEC,IAAArE,MAAA,CAAAC,aAAA,EAAChD,OAAO,CAACqH,UAAU;IAClBzE,GAAG,EAAGA,GAAK;IACXM,KAAK,EAAG2C,iBAAmB;IAC3BpC,MAAM,EACLoC,iBAAiB,CAACE,MAAM,GACrB,IAAAuB,qBAAY,EAAEtC,OAAO,EAAE;MACvB;MACAxC,MAAM,EACL,IAAAO,MAAA,CAAAC,aAAA,EAAAD,MAAA,CAAAqE,QAAA,QACGpC,OAAO,CAACrC,KAAK,CAACH,MAAM,EACtB,IAAAO,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAAC6G,kBAAkB;QACzB,eAAY,MAAM;QAClB1D,IAAI,EAAG2D,wBAAmB;QAC1BzD,IAAI,EAAG,EAAI;QACX0D,mBAAmB,EAAC;MAAgB,CACpC,CACA;IAEH,CAAE,CAAC,GACHzC;EACH,CACD,CAAC,EAGF,IAAAjC,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAACgH,YAAY;IAAA,GACdrC,UAAU;IACfF,KAAK,EAAGA,KAAO;IACfjC,KAAK,EAAG2C;IACR;IACA;IAAA;IACAZ,MAAM,EAAGA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAMY,iBAAiB,CAACE,MAAM,GAAG,CAAC,GAAG;IACpD;IACA;IAAA;IACAb,KAAK,EAAGA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAMW,iBAAiB,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAK;IACxD4B,kBAAkB,EAAG,KAAO;IAC5B,aAAYrB,oBAAsB;IAClClB,OAAO,EAAGA,OAAS;IACnB6B,YAAY,EAAGA,YAAc;IAC7BJ,YAAY,EAAGA,YAAc;IAC7Be,aAAa;EAAA,GAEb,IAAA7E,MAAA,CAAAC,aAAA,EAACf,mBAAmB,CAAC4F,QAAQ;IAACC,KAAK,EAAG1B;EAAc,GACjD3D,QAC2B,CACV,CACpB,CAAC;AAEL,CAAC;AACM,MAAMiF,YAAY,GAAG,IAAAK,uBAAc,EACzCrD,uBAAuB,EACvB,cACD,CAAC;AAACtC,OAAA,CAAAsF,YAAA,GAAAA,YAAA;AAEK,MAAMM,qBAAqB,GAAG,IAAA1F,mBAAU,EAG5C,SAAS0F,qBAAqBA,CAAErF,KAAK,EAAEC,GAAG,EAAG;EAC/C,MAAMC,mBAAmB,GAAG,IAAAC,mBAAU,EAAEb,mBAAoB,CAAC;EAC7D,OACC,IAAAc,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAACsH,qBAAqB;IAC5BpF,GAAG,EAAGA,GAAK;IAAA,GACND,KAAK;IACVO,KAAK,EAAGL,mBAAmB,EAAEK,KAAO;IACpCkC,OAAO,EAAGvC,mBAAmB,EAAEuC;EAAS,CACxC,CAAC;AAEJ,CAAE,CAAC;AAAChD,OAAA,CAAA4F,qBAAA,GAAAA,qBAAA;AAEG,MAAMC,qBAAqB,GAAG,IAAA3F,mBAAU,EAG5C,SAAS2F,qBAAqBA,CAAEtF,KAAK,EAAEC,GAAG,EAAG;EAC/C,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAACuH,qBAAqB;IAC5BC,aAAa,EAAG,CAAG;IACnBtF,GAAG,EAAGA,GAAK;IAAA,GACND;EAAK,CACV,CAAC;AAEJ,CAAE,CAAC;AAACP,OAAA,CAAA6F,qBAAA,GAAAA,qBAAA;AAEG,MAAME,wBAAwB,GAAG,IAAA7F,mBAAU,EAG/C,SAAS6F,wBAAwBA,CAAExF,KAAK,EAAEC,GAAG,EAAG;EAClD,OACC,IAAAG,MAAA,CAAAC,aAAA,EAACtC,MAAM,CAACyH,wBAAwB;IAC/BD,aAAa,EAAG,CAAG;IACnBtF,GAAG,EAAGA,GAAK;IAAA,GACND;EAAK,CACV,CAAC;AAEJ,CAAE,CAAC;AAACP,OAAA,CAAA+F,wBAAA,GAAAA,wBAAA"}