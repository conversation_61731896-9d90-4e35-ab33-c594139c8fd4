{"version": 3, "names": [], "sources": ["@wordpress/components/src/font-size-picker/types.ts"], "sourcesContent": ["export type FontSizePickerProps = {\n\t/**\n\t * If `true`, it will not be possible to choose a custom fontSize. The user\n\t * will be forced to pick one of the pre-defined sizes passed in fontSizes.\n\t *\n\t * @default false\n\t */\n\tdisableCustomFontSizes?: boolean;\n\t/**\n\t * If no value exists, this prop defines the starting position for the font\n\t * size picker slider. Only relevant if `withSlider` is `true`.\n\t */\n\tfallbackFontSize?: number;\n\t/**\n\t * An array of font size objects. The object should contain properties size,\n\t * name, and slug.\n\t */\n\tfontSizes?: FontSize[];\n\t/**\n\t * A function that receives the new font size value.\n\t * If onChange is called without any parameter, it should reset the value,\n\t * attending to what reset means in that context, e.g., set the font size to\n\t * undefined or set the font size a starting value.\n\t */\n\tonChange?: (\n\t\tvalue: number | string | undefined,\n\t\tselectedItem?: FontSize\n\t) => void;\n\t/**\n\t * Available units for custom font size selection.\n\t */\n\tunits?: string[];\n\t/**\n\t * The current font size value.\n\t */\n\tvalue?: number | string;\n\t/**\n\t * If `true`, the UI will contain a slider, instead of a numeric text input\n\t * field. If `false`, no slider will be present.\n\t *\n\t * @default false\n\t */\n\twithSlider?: boolean;\n\t/**\n\t * If `true`, a reset button will be displayed alongside the input field\n\t * when a custom font size is active. Has no effect when\n\t * `disableCustomFontSizes` or `withSlider` is `true`.\n\t *\n\t * @default true\n\t */\n\twithReset?: boolean;\n\t/**\n\t * Start opting into the new margin-free styles that will become the default\n\t * in a future version, currently scheduled to be WordPress 6.4. (The prop\n\t * can be safely removed once this happens.)\n\t *\n\t * @default false\n\t */\n\t__nextHasNoMarginBottom?: boolean;\n\t/**\n\t * Start opting into the larger default height that will become the default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n\t/**\n\t * Size of the control.\n\t *\n\t * @default 'default'\n\t */\n\tsize?: 'default' | '__unstable-large';\n};\n\nexport type FontSize = {\n\t/**\n\t * The property `size` contains a number with the font size value, in `px` or\n\t * a string specifying the font size CSS property that should be used eg:\n\t * \"13px\", \"1em\", or \"clamp(12px, 5vw, 100px)\".\n\t */\n\tsize: number | string;\n\t/**\n\t * The `name` property includes a label for that font size e.g.: `Small`.\n\t */\n\tname?: string;\n\t/**\n\t * The `slug` property is a string with a unique identifier for the font\n\t * size. Used for the class generation process.\n\t */\n\tslug: string;\n};\n\nexport type FontSizePickerSelectProps = Pick<\n\tFontSizePickerProps,\n\t'value' | 'size'\n> & {\n\tfontSizes: NonNullable< FontSizePickerProps[ 'fontSizes' ] >;\n\tdisableCustomFontSizes: NonNullable<\n\t\tFontSizePickerProps[ 'disableCustomFontSizes' ]\n\t>;\n\tonChange: NonNullable< FontSizePickerProps[ 'onChange' ] >;\n\tonSelectCustom: () => void;\n\t__next40pxDefaultSize: boolean;\n};\n\nexport type FontSizePickerSelectOption = {\n\tkey: string;\n\tname: string;\n\tvalue?: FontSize[ 'size' ];\n\t__experimentalHint?: string;\n};\n\nexport type FontSizePickerToggleGroupProps = Pick<\n\tFontSizePickerProps,\n\t'value' | 'size' | '__nextHasNoMarginBottom' | '__next40pxDefaultSize'\n> & {\n\tfontSizes: NonNullable< FontSizePickerProps[ 'fontSizes' ] >;\n\tonChange: NonNullable< FontSizePickerProps[ 'onChange' ] >;\n};\n"], "mappings": ""}