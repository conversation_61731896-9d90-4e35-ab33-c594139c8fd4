"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.InputBaseWithBackCompatMinWidth = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _react = require("@emotion/react");
var _inputBase = _interopRequireDefault(require("../input-control/input-base"));
var _inputControlStyles = require("../input-control/styles/input-control-styles");
/**
 * External dependencies
 */

/**
 * Internal dependencies
 */

const backCompatMinWidth = props => !props.__nextUnconstrainedWidth ? /*#__PURE__*/(0, _react.css)(_inputControlStyles.Container, "{min-width:130px;}" + (process.env.NODE_ENV === "production" ? "" : ";label:backCompatMinWidth;"), process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY3VzdG9tLXNlbGVjdC1jb250cm9sL3N0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFrQk8iLCJmaWxlIjoiQHdvcmRwcmVzcy9jb21wb25lbnRzL3NyYy9jdXN0b20tc2VsZWN0LWNvbnRyb2wvc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHsgY3NzIH0gZnJvbSAnQGVtb3Rpb24vcmVhY3QnO1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgSW5wdXRCYXNlIGZyb20gJy4uL2lucHV0LWNvbnRyb2wvaW5wdXQtYmFzZSc7XG5pbXBvcnQgeyBDb250YWluZXIgYXMgSW5wdXRDb250cm9sQ29udGFpbmVyIH0gZnJvbSAnLi4vaW5wdXQtY29udHJvbC9zdHlsZXMvaW5wdXQtY29udHJvbC1zdHlsZXMnO1xuXG50eXBlIEJhY2tDb21wYXRNaW5XaWR0aFByb3BzID0ge1xuXHRfX25leHRVbmNvbnN0cmFpbmVkV2lkdGg6IGJvb2xlYW47XG59O1xuXG5jb25zdCBiYWNrQ29tcGF0TWluV2lkdGggPSAoIHByb3BzOiBCYWNrQ29tcGF0TWluV2lkdGhQcm9wcyApID0+XG5cdCEgcHJvcHMuX19uZXh0VW5jb25zdHJhaW5lZFdpZHRoXG5cdFx0PyBjc3NgXG5cdFx0XHRcdCR7IElucHV0Q29udHJvbENvbnRhaW5lciB9IHtcblx0XHRcdFx0XHRtaW4td2lkdGg6IDEzMHB4O1xuXHRcdFx0XHR9XG5cdFx0ICBgXG5cdFx0OiAnJztcblxuZXhwb3J0IGNvbnN0IElucHV0QmFzZVdpdGhCYWNrQ29tcGF0TWluV2lkdGggPSBzdHlsZWQoIElucHV0QmFzZSApYFxuXHQkeyBiYWNrQ29tcGF0TWluV2lkdGggfVxuYDtcbiJdfQ== */") : '';
const InputBaseWithBackCompatMinWidth = ( /*#__PURE__*/0, _base.default)(_inputBase.default, process.env.NODE_ENV === "production" ? {
  target: "eswuck60"
} : {
  target: "eswuck60",
  label: "InputBaseWithBackCompatMinWidth"
})(backCompatMinWidth, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY3VzdG9tLXNlbGVjdC1jb250cm9sL3N0eWxlcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUF5QmtFIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvY3VzdG9tLXNlbGVjdC1jb250cm9sL3N0eWxlcy50cyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCB7IGNzcyB9IGZyb20gJ0BlbW90aW9uL3JlYWN0JztcbmltcG9ydCBzdHlsZWQgZnJvbSAnQGVtb3Rpb24vc3R5bGVkJztcblxuLyoqXG4gKiBJbnRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IElucHV0QmFzZSBmcm9tICcuLi9pbnB1dC1jb250cm9sL2lucHV0LWJhc2UnO1xuaW1wb3J0IHsgQ29udGFpbmVyIGFzIElucHV0Q29udHJvbENvbnRhaW5lciB9IGZyb20gJy4uL2lucHV0LWNvbnRyb2wvc3R5bGVzL2lucHV0LWNvbnRyb2wtc3R5bGVzJztcblxudHlwZSBCYWNrQ29tcGF0TWluV2lkdGhQcm9wcyA9IHtcblx0X19uZXh0VW5jb25zdHJhaW5lZFdpZHRoOiBib29sZWFuO1xufTtcblxuY29uc3QgYmFja0NvbXBhdE1pbldpZHRoID0gKCBwcm9wczogQmFja0NvbXBhdE1pbldpZHRoUHJvcHMgKSA9PlxuXHQhIHByb3BzLl9fbmV4dFVuY29uc3RyYWluZWRXaWR0aFxuXHRcdD8gY3NzYFxuXHRcdFx0XHQkeyBJbnB1dENvbnRyb2xDb250YWluZXIgfSB7XG5cdFx0XHRcdFx0bWluLXdpZHRoOiAxMzBweDtcblx0XHRcdFx0fVxuXHRcdCAgYFxuXHRcdDogJyc7XG5cbmV4cG9ydCBjb25zdCBJbnB1dEJhc2VXaXRoQmFja0NvbXBhdE1pbldpZHRoID0gc3R5bGVkKCBJbnB1dEJhc2UgKWBcblx0JHsgYmFja0NvbXBhdE1pbldpZHRoIH1cbmA7XG4iXX0= */"));
exports.InputBaseWithBackCompatMinWidth = InputBaseWithBackCompatMinWidth;
//# sourceMappingURL=styles.js.map