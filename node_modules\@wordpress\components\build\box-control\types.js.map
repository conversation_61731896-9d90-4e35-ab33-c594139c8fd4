{"version": 3, "names": [], "sources": ["@wordpress/components/src/box-control/types.ts"], "sourcesContent": ["/**\n * Internal dependencies\n */\nimport type { UnitControlProps } from '../unit-control/types';\nimport type { LABELS } from './utils';\n\nexport type BoxControlValue = {\n\ttop?: string;\n\tright?: string;\n\tbottom?: string;\n\tleft?: string;\n};\n\nexport type CustomValueUnits = {\n\t[ key: string ]: { max: number; step: number };\n};\n\ntype UnitControlPassthroughProps = Omit<\n\tUnitControlProps,\n\t'label' | 'onChange' | 'onFocus' | 'onMouseOver' | 'onMouseOut' | 'units'\n>;\n\nexport type BoxControlProps = Pick<\n\tUnitControlProps,\n\t'onMouseOver' | 'onMouseOut' | 'units'\n> & {\n\t/**\n\t * If this property is true, a button to reset the box control is rendered.\n\t *\n\t * @default true\n\t */\n\tallowReset?: boolean;\n\t/**\n\t * The id to use as a base for the unique HTML id attribute of the control.\n\t */\n\tid?: string;\n\t/**\n\t * Props for the internal `UnitControl` components.\n\t *\n\t * @default `{ min: 0 }`\n\t */\n\tinputProps?: UnitControlPassthroughProps;\n\t/**\n\t * Heading label for the control.\n\t *\n\t * @default `__( 'Box Control' )`\n\t */\n\tlabel?: string;\n\t/**\n\t * A callback function when an input value changes.\n\t */\n\tonChange: ( next: BoxControlValue ) => void;\n\t/**\n\t * The `top`, `right`, `bottom`, and `left` box dimension values to use when the control is reset.\n\t *\n\t * @default `{ top: undefined, right: undefined, bottom: undefined, left: undefined }`\n\t */\n\tresetValues?: BoxControlValue;\n\t/**\n\t * Collection of sides to allow control of. If omitted or empty, all sides will be available.\n\t */\n\tsides?: readonly ( keyof BoxControlValue | 'horizontal' | 'vertical' )[];\n\t/**\n\t * If this property is true, when the box control is unlinked, vertical and horizontal controls\n\t * can be used instead of updating individual sides.\n\t *\n\t * @default false\n\t */\n\tsplitOnAxis?: boolean;\n\t/**\n\t * The current values of the control, expressed as an object of `top`, `right`, `bottom`, and `left` values.\n\t */\n\tvalues?: BoxControlValue;\n\t/**\n\t * Start opting into the larger default height that will become the default size in a future version.\n\t *\n\t * @default false\n\t */\n\t__next40pxDefaultSize?: boolean;\n};\n\nexport type BoxControlInputControlProps = UnitControlPassthroughProps & {\n\tonChange?: ( nextValues: BoxControlValue ) => void;\n\tonFocus?: (\n\t\t_event: React.FocusEvent< HTMLInputElement >,\n\t\t{ side }: { side: keyof typeof LABELS }\n\t) => void;\n\tonHoverOff?: (\n\t\tsides: Partial< Record< keyof BoxControlValue, boolean > >\n\t) => void;\n\tonHoverOn?: (\n\t\tsides: Partial< Record< keyof BoxControlValue, boolean > >\n\t) => void;\n\tselectedUnits: BoxControlValue;\n\tsetSelectedUnits: React.Dispatch< React.SetStateAction< BoxControlValue > >;\n\tsides: BoxControlProps[ 'sides' ];\n\tvalues: BoxControlValue;\n};\n\nexport type BoxControlIconProps = {\n\t/**\n\t * @default 24\n\t */\n\tsize?: number;\n\t/**\n\t * @default 'all'\n\t */\n\tside?: keyof typeof LABELS;\n\tsides?: BoxControlProps[ 'sides' ];\n};\n"], "mappings": ""}