{"version": 3, "names": [], "sources": ["@wordpress/components/src/animate/types.ts"], "sourcesContent": ["export type AppearOptions = {\n\ttype: 'appear';\n\torigin?:\n\t\t| 'top'\n\t\t| 'top left'\n\t\t| 'top right'\n\t\t| 'middle'\n\t\t| 'middle left'\n\t\t| 'middle right'\n\t\t| 'bottom'\n\t\t| 'bottom left'\n\t\t| 'bottom right';\n};\ntype SlideInOptions = { type: 'slide-in'; origin?: 'left' | 'right' };\ntype LoadingOptions = { type: 'loading'; origin?: never };\ntype NoAnimationOptions = { type?: never; origin?: never };\n\nexport type GetAnimateOptions =\n\t| AppearOptions\n\t| SlideInOptions\n\t| LoadingOptions\n\t| NoAnimationOptions;\n\n// Create a new type that and distributes the `Pick` operator separately to\n// every individual type of a union, thus preserving that same union.\ntype DistributiveTypeAndOptions< T extends { type?: any } > = T extends any\n\t? Pick< T, 'type' > & { options?: Omit< T, 'type' > }\n\t: never;\n\nexport type AnimateProps = DistributiveTypeAndOptions< GetAnimateOptions > & {\n\tchildren: ( props: { className?: string } ) => JSX.Element;\n};\n"], "mappings": ""}