"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = LinkedButton;
var _react = require("react");
var _icons = require("@wordpress/icons");
var _i18n = require("@wordpress/i18n");
var _button = _interopRequireDefault(require("../button"));
var _tooltip = _interopRequireDefault(require("../tooltip"));
/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

function LinkedButton({
  isLinked,
  ...props
}) {
  const label = isLinked ? (0, _i18n.__)('Unlink sides') : (0, _i18n.__)('Link sides');
  return (0, _react.createElement)(_tooltip.default, {
    text: label
  }, (0, _react.createElement)(_button.default, {
    ...props,
    className: "component-box-control__linked-button",
    size: "small",
    icon: isLinked ? _icons.link : _icons.linkOff,
    iconSize: 24,
    "aria-label": label
  }));
}
//# sourceMappingURL=linked-button.js.map