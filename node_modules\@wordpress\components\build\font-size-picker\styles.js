"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.HeaderToggle = exports.HeaderLabel = exports.HeaderHint = exports.Header = exports.Controls = exports.Container = void 0;
var _base = _interopRequireDefault(require("@emotion/styled/base"));
var _baseControl = _interopRequireDefault(require("../base-control"));
var _button = _interopRequireDefault(require("../button"));
var _hStack = require("../h-stack");
var _space = require("../utils/space");
var _utils = require("../utils");
function _EMOTION_STRINGIFIED_CSS_ERROR__() { return "You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."; }
const Container = (0, _base.default)("fieldset", process.env.NODE_ENV === "production" ? {
  target: "e8tqeku5"
} : {
  target: "e8tqeku5",
  label: "Container"
})(process.env.NODE_ENV === "production" ? {
  name: "1t1ytme",
  styles: "border:0;margin:0;padding:0"
} : {
  name: "1t1ytme",
  styles: "border:0;margin:0;padding:0",
  map: "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBY3dDIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBCYXNlQ29udHJvbCBmcm9tICcuLi9iYXNlLWNvbnRyb2wnO1xuaW1wb3J0IEJ1dHRvbiBmcm9tICcuLi9idXR0b24nO1xuaW1wb3J0IHsgSFN0YWNrIH0gZnJvbSAnLi4vaC1zdGFjayc7XG5pbXBvcnQgeyBzcGFjZSB9IGZyb20gJy4uL3V0aWxzL3NwYWNlJztcbmltcG9ydCB7IENPTE9SUyB9IGZyb20gJy4uL3V0aWxzJztcblxuZXhwb3J0IGNvbnN0IENvbnRhaW5lciA9IHN0eWxlZC5maWVsZHNldGBcblx0Ym9yZGVyOiAwO1xuXHRtYXJnaW46IDA7XG5cdHBhZGRpbmc6IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyID0gc3R5bGVkKCBIU3RhY2sgKWBcblx0aGVpZ2h0OiAkeyBzcGFjZSggNCApIH07XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyVG9nZ2xlID0gc3R5bGVkKCBCdXR0b24gKWBcblx0bWFyZ2luLXRvcDogJHsgc3BhY2UoIC0xICkgfTtcbmA7XG5cbmV4cG9ydCBjb25zdCBIZWFkZXJMYWJlbCA9IHN0eWxlZCggQmFzZUNvbnRyb2wuVmlzdWFsTGFiZWwgKWBcblx0ZGlzcGxheTogZmxleDtcblx0Z2FwOiAkeyBzcGFjZSggMSApIH07XG5cdGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcblx0bWFyZ2luLWJvdHRvbTogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBIZWFkZXJIaW50ID0gc3R5bGVkLnNwYW5gXG5cdGNvbG9yOiAkeyBDT0xPUlMuZ3JheVsgNzAwIF0gfTtcbmA7XG5cbmV4cG9ydCBjb25zdCBDb250cm9scyA9IHN0eWxlZC5kaXY8IHtcblx0X19uZXh0SGFzTm9NYXJnaW5Cb3R0b206IGJvb2xlYW47XG59ID5gXG5cdCR7ICggcHJvcHMgKSA9PlxuXHRcdCEgcHJvcHMuX19uZXh0SGFzTm9NYXJnaW5Cb3R0b20gJiYgYG1hcmdpbi1ib3R0b206ICR7IHNwYWNlKCA2ICkgfTtgIH1cbmA7XG4iXX0= */",
  toString: _EMOTION_STRINGIFIED_CSS_ERROR__
});
exports.Container = Container;
const Header = ( /*#__PURE__*/0, _base.default)(_hStack.HStack, process.env.NODE_ENV === "production" ? {
  target: "e8tqeku4"
} : {
  target: "e8tqeku4",
  label: "Header"
})("height:", (0, _space.space)(4), ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBb0JzQyIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2ZvbnQtc2l6ZS1waWNrZXIvc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgQmFzZUNvbnRyb2wgZnJvbSAnLi4vYmFzZS1jb250cm9sJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uL2gtc3RhY2snO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi91dGlscy9zcGFjZSc7XG5pbXBvcnQgeyBDT0xPUlMgfSBmcm9tICcuLi91dGlscyc7XG5cbmV4cG9ydCBjb25zdCBDb250YWluZXIgPSBzdHlsZWQuZmllbGRzZXRgXG5cdGJvcmRlcjogMDtcblx0bWFyZ2luOiAwO1xuXHRwYWRkaW5nOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGhlaWdodDogJHsgc3BhY2UoIDQgKSB9O1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlclRvZ2dsZSA9IHN0eWxlZCggQnV0dG9uIClgXG5cdG1hcmdpbi10b3A6ICR7IHNwYWNlKCAtMSApIH07XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyTGFiZWwgPSBzdHlsZWQoIEJhc2VDb250cm9sLlZpc3VhbExhYmVsIClgXG5cdGRpc3BsYXk6IGZsZXg7XG5cdGdhcDogJHsgc3BhY2UoIDEgKSB9O1xuXHRqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG5cdG1hcmdpbi1ib3R0b206IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVySGludCA9IHN0eWxlZC5zcGFuYFxuXHRjb2xvcjogJHsgQ09MT1JTLmdyYXlbIDcwMCBdIH07XG5gO1xuXG5leHBvcnQgY29uc3QgQ29udHJvbHMgPSBzdHlsZWQuZGl2PCB7XG5cdF9fbmV4dEhhc05vTWFyZ2luQm90dG9tOiBib29sZWFuO1xufSA+YFxuXHQkeyAoIHByb3BzICkgPT5cblx0XHQhIHByb3BzLl9fbmV4dEhhc05vTWFyZ2luQm90dG9tICYmIGBtYXJnaW4tYm90dG9tOiAkeyBzcGFjZSggNiApIH07YCB9XG5gO1xuIl19 */"));
exports.Header = Header;
const HeaderToggle = ( /*#__PURE__*/0, _base.default)(_button.default, process.env.NODE_ENV === "production" ? {
  target: "e8tqeku3"
} : {
  target: "e8tqeku3",
  label: "HeaderToggle"
})("margin-top:", (0, _space.space)(-1), ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBd0I0QyIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2ZvbnQtc2l6ZS1waWNrZXIvc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgQmFzZUNvbnRyb2wgZnJvbSAnLi4vYmFzZS1jb250cm9sJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uL2gtc3RhY2snO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi91dGlscy9zcGFjZSc7XG5pbXBvcnQgeyBDT0xPUlMgfSBmcm9tICcuLi91dGlscyc7XG5cbmV4cG9ydCBjb25zdCBDb250YWluZXIgPSBzdHlsZWQuZmllbGRzZXRgXG5cdGJvcmRlcjogMDtcblx0bWFyZ2luOiAwO1xuXHRwYWRkaW5nOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGhlaWdodDogJHsgc3BhY2UoIDQgKSB9O1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlclRvZ2dsZSA9IHN0eWxlZCggQnV0dG9uIClgXG5cdG1hcmdpbi10b3A6ICR7IHNwYWNlKCAtMSApIH07XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyTGFiZWwgPSBzdHlsZWQoIEJhc2VDb250cm9sLlZpc3VhbExhYmVsIClgXG5cdGRpc3BsYXk6IGZsZXg7XG5cdGdhcDogJHsgc3BhY2UoIDEgKSB9O1xuXHRqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG5cdG1hcmdpbi1ib3R0b206IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVySGludCA9IHN0eWxlZC5zcGFuYFxuXHRjb2xvcjogJHsgQ09MT1JTLmdyYXlbIDcwMCBdIH07XG5gO1xuXG5leHBvcnQgY29uc3QgQ29udHJvbHMgPSBzdHlsZWQuZGl2PCB7XG5cdF9fbmV4dEhhc05vTWFyZ2luQm90dG9tOiBib29sZWFuO1xufSA+YFxuXHQkeyAoIHByb3BzICkgPT5cblx0XHQhIHByb3BzLl9fbmV4dEhhc05vTWFyZ2luQm90dG9tICYmIGBtYXJnaW4tYm90dG9tOiAkeyBzcGFjZSggNiApIH07YCB9XG5gO1xuIl19 */"));
exports.HeaderToggle = HeaderToggle;
const HeaderLabel = ( /*#__PURE__*/0, _base.default)(_baseControl.default.VisualLabel, process.env.NODE_ENV === "production" ? {
  target: "e8tqeku2"
} : {
  target: "e8tqeku2",
  label: "HeaderLabel"
})("display:flex;gap:", (0, _space.space)(1), ";justify-content:flex-start;margin-bottom:0;" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBNEI0RCIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2ZvbnQtc2l6ZS1waWNrZXIvc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgQmFzZUNvbnRyb2wgZnJvbSAnLi4vYmFzZS1jb250cm9sJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uL2gtc3RhY2snO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi91dGlscy9zcGFjZSc7XG5pbXBvcnQgeyBDT0xPUlMgfSBmcm9tICcuLi91dGlscyc7XG5cbmV4cG9ydCBjb25zdCBDb250YWluZXIgPSBzdHlsZWQuZmllbGRzZXRgXG5cdGJvcmRlcjogMDtcblx0bWFyZ2luOiAwO1xuXHRwYWRkaW5nOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGhlaWdodDogJHsgc3BhY2UoIDQgKSB9O1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlclRvZ2dsZSA9IHN0eWxlZCggQnV0dG9uIClgXG5cdG1hcmdpbi10b3A6ICR7IHNwYWNlKCAtMSApIH07XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyTGFiZWwgPSBzdHlsZWQoIEJhc2VDb250cm9sLlZpc3VhbExhYmVsIClgXG5cdGRpc3BsYXk6IGZsZXg7XG5cdGdhcDogJHsgc3BhY2UoIDEgKSB9O1xuXHRqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG5cdG1hcmdpbi1ib3R0b206IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVySGludCA9IHN0eWxlZC5zcGFuYFxuXHRjb2xvcjogJHsgQ09MT1JTLmdyYXlbIDcwMCBdIH07XG5gO1xuXG5leHBvcnQgY29uc3QgQ29udHJvbHMgPSBzdHlsZWQuZGl2PCB7XG5cdF9fbmV4dEhhc05vTWFyZ2luQm90dG9tOiBib29sZWFuO1xufSA+YFxuXHQkeyAoIHByb3BzICkgPT5cblx0XHQhIHByb3BzLl9fbmV4dEhhc05vTWFyZ2luQm90dG9tICYmIGBtYXJnaW4tYm90dG9tOiAkeyBzcGFjZSggNiApIH07YCB9XG5gO1xuIl19 */"));
exports.HeaderLabel = HeaderLabel;
const HeaderHint = (0, _base.default)("span", process.env.NODE_ENV === "production" ? {
  target: "e8tqeku1"
} : {
  target: "e8tqeku1",
  label: "HeaderHint"
})("color:", _utils.COLORS.gray[700], ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBbUNxQyIsImZpbGUiOiJAd29yZHByZXNzL2NvbXBvbmVudHMvc3JjL2ZvbnQtc2l6ZS1waWNrZXIvc3R5bGVzLnRzIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFeHRlcm5hbCBkZXBlbmRlbmNpZXNcbiAqL1xuaW1wb3J0IHN0eWxlZCBmcm9tICdAZW1vdGlvbi9zdHlsZWQnO1xuXG4vKipcbiAqIEludGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgQmFzZUNvbnRyb2wgZnJvbSAnLi4vYmFzZS1jb250cm9sJztcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vYnV0dG9uJztcbmltcG9ydCB7IEhTdGFjayB9IGZyb20gJy4uL2gtc3RhY2snO1xuaW1wb3J0IHsgc3BhY2UgfSBmcm9tICcuLi91dGlscy9zcGFjZSc7XG5pbXBvcnQgeyBDT0xPUlMgfSBmcm9tICcuLi91dGlscyc7XG5cbmV4cG9ydCBjb25zdCBDb250YWluZXIgPSBzdHlsZWQuZmllbGRzZXRgXG5cdGJvcmRlcjogMDtcblx0bWFyZ2luOiAwO1xuXHRwYWRkaW5nOiAwO1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlciA9IHN0eWxlZCggSFN0YWNrIClgXG5cdGhlaWdodDogJHsgc3BhY2UoIDQgKSB9O1xuYDtcblxuZXhwb3J0IGNvbnN0IEhlYWRlclRvZ2dsZSA9IHN0eWxlZCggQnV0dG9uIClgXG5cdG1hcmdpbi10b3A6ICR7IHNwYWNlKCAtMSApIH07XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyTGFiZWwgPSBzdHlsZWQoIEJhc2VDb250cm9sLlZpc3VhbExhYmVsIClgXG5cdGRpc3BsYXk6IGZsZXg7XG5cdGdhcDogJHsgc3BhY2UoIDEgKSB9O1xuXHRqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG5cdG1hcmdpbi1ib3R0b206IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVySGludCA9IHN0eWxlZC5zcGFuYFxuXHRjb2xvcjogJHsgQ09MT1JTLmdyYXlbIDcwMCBdIH07XG5gO1xuXG5leHBvcnQgY29uc3QgQ29udHJvbHMgPSBzdHlsZWQuZGl2PCB7XG5cdF9fbmV4dEhhc05vTWFyZ2luQm90dG9tOiBib29sZWFuO1xufSA+YFxuXHQkeyAoIHByb3BzICkgPT5cblx0XHQhIHByb3BzLl9fbmV4dEhhc05vTWFyZ2luQm90dG9tICYmIGBtYXJnaW4tYm90dG9tOiAkeyBzcGFjZSggNiApIH07YCB9XG5gO1xuIl19 */"));
exports.HeaderHint = HeaderHint;
const Controls = (0, _base.default)("div", process.env.NODE_ENV === "production" ? {
  target: "e8tqeku0"
} : {
  target: "e8tqeku0",
  label: "Controls"
})(props => !props.__nextHasNoMarginBottom && `margin-bottom: ${(0, _space.space)(6)};`, ";" + (process.env.NODE_ENV === "production" ? "" : "/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBeUNHIiwiZmlsZSI6IkB3b3JkcHJlc3MvY29tcG9uZW50cy9zcmMvZm9udC1zaXplLXBpY2tlci9zdHlsZXMudHMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEV4dGVybmFsIGRlcGVuZGVuY2llc1xuICovXG5pbXBvcnQgc3R5bGVkIGZyb20gJ0BlbW90aW9uL3N0eWxlZCc7XG5cbi8qKlxuICogSW50ZXJuYWwgZGVwZW5kZW5jaWVzXG4gKi9cbmltcG9ydCBCYXNlQ29udHJvbCBmcm9tICcuLi9iYXNlLWNvbnRyb2wnO1xuaW1wb3J0IEJ1dHRvbiBmcm9tICcuLi9idXR0b24nO1xuaW1wb3J0IHsgSFN0YWNrIH0gZnJvbSAnLi4vaC1zdGFjayc7XG5pbXBvcnQgeyBzcGFjZSB9IGZyb20gJy4uL3V0aWxzL3NwYWNlJztcbmltcG9ydCB7IENPTE9SUyB9IGZyb20gJy4uL3V0aWxzJztcblxuZXhwb3J0IGNvbnN0IENvbnRhaW5lciA9IHN0eWxlZC5maWVsZHNldGBcblx0Ym9yZGVyOiAwO1xuXHRtYXJnaW46IDA7XG5cdHBhZGRpbmc6IDA7XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyID0gc3R5bGVkKCBIU3RhY2sgKWBcblx0aGVpZ2h0OiAkeyBzcGFjZSggNCApIH07XG5gO1xuXG5leHBvcnQgY29uc3QgSGVhZGVyVG9nZ2xlID0gc3R5bGVkKCBCdXR0b24gKWBcblx0bWFyZ2luLXRvcDogJHsgc3BhY2UoIC0xICkgfTtcbmA7XG5cbmV4cG9ydCBjb25zdCBIZWFkZXJMYWJlbCA9IHN0eWxlZCggQmFzZUNvbnRyb2wuVmlzdWFsTGFiZWwgKWBcblx0ZGlzcGxheTogZmxleDtcblx0Z2FwOiAkeyBzcGFjZSggMSApIH07XG5cdGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcblx0bWFyZ2luLWJvdHRvbTogMDtcbmA7XG5cbmV4cG9ydCBjb25zdCBIZWFkZXJIaW50ID0gc3R5bGVkLnNwYW5gXG5cdGNvbG9yOiAkeyBDT0xPUlMuZ3JheVsgNzAwIF0gfTtcbmA7XG5cbmV4cG9ydCBjb25zdCBDb250cm9scyA9IHN0eWxlZC5kaXY8IHtcblx0X19uZXh0SGFzTm9NYXJnaW5Cb3R0b206IGJvb2xlYW47XG59ID5gXG5cdCR7ICggcHJvcHMgKSA9PlxuXHRcdCEgcHJvcHMuX19uZXh0SGFzTm9NYXJnaW5Cb3R0b20gJiYgYG1hcmdpbi1ib3R0b206ICR7IHNwYWNlKCA2ICkgfTtgIH1cbmA7XG4iXX0= */"));
exports.Controls = Controls;
//# sourceMappingURL=styles.js.map