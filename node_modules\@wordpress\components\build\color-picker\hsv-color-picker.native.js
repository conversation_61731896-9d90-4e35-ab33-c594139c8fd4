"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var _reactNative = require("react-native");
var _element = require("@wordpress/element");
var _huePicker = _interopRequireDefault(require("./hue-picker"));
var _saturationPicker = _interopRequireDefault(require("./saturation-picker"));
var _styleNative = _interopRequireDefault(require("./style.native.scss"));
/**
 * External dependencies
 */

/**
 * WordPress dependencies
 */

/**
 * Internal dependencies
 */

const HsvColorPicker = props => {
  const maxWidth = _reactNative.Dimensions.get('window').width - 32;
  const satValPickerRef = (0, _element.useRef)(null);
  const {
    containerStyle = {},
    currentColor,
    huePickerContainerStyle = {},
    huePickerBorderRadius = 0,
    huePickerHue = 0,
    huePickerBarWidth = maxWidth,
    huePickerBarHeight = 12,
    huePickerSliderSize = 24,
    onHuePickerDragStart,
    onHuePickerDragMove,
    onHuePickerDragEnd,
    onHuePickerDragTerminate,
    onHuePickerPress,
    satValPickerContainerStyle = {},
    satValPickerBorderRadius = 0,
    satValPickerSize = {
      width: maxWidth,
      height: 200
    },
    satValPickerSliderSize = 24,
    satValPickerHue = 0,
    satValPickerSaturation = 1,
    satValPickerValue = 1,
    onSatValPickerDragStart,
    onSatValPickerDragMove,
    onSatValPickerDragEnd,
    onSatValPickerDragTerminate,
    onSatValPickerPress
  } = props;
  return (0, _react.createElement)(_reactNative.View, {
    style: [_styleNative.default['hsv-container'], containerStyle],
    testID: "hsv-color-picker"
  }, (0, _react.createElement)(_saturationPicker.default, {
    containerStyle: satValPickerContainerStyle,
    currentColor: currentColor,
    borderRadius: satValPickerBorderRadius,
    size: satValPickerSize,
    sliderSize: satValPickerSliderSize,
    hue: satValPickerHue,
    saturation: satValPickerSaturation,
    value: satValPickerValue,
    onDragStart: onSatValPickerDragStart,
    onDragMove: onSatValPickerDragMove,
    onDragEnd: onSatValPickerDragEnd,
    onDragTerminate: onSatValPickerDragTerminate,
    onPress: onSatValPickerPress,
    ref: satValPickerRef
  }), (0, _react.createElement)(_huePicker.default, {
    containerStyle: huePickerContainerStyle,
    borderRadius: huePickerBorderRadius,
    hue: huePickerHue,
    barWidth: huePickerBarWidth,
    barHeight: huePickerBarHeight,
    sliderSize: huePickerSliderSize,
    onDragStart: onHuePickerDragStart,
    onDragMove: onHuePickerDragMove,
    onDragEnd: onHuePickerDragEnd,
    onDragTerminate: onHuePickerDragTerminate,
    onPress: onHuePickerPress
  }));
};
var _default = HsvColorPicker;
exports.default = _default;
//# sourceMappingURL=hsv-color-picker.native.js.map