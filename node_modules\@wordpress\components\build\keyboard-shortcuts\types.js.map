{"version": 3, "names": [], "sources": ["@wordpress/components/src/keyboard-shortcuts/types.ts"], "sourcesContent": ["/**\n * WordPress dependencies\n */\nimport type { useKeyboardShortcut } from '@wordpress/compose';\n\n// TODO: We wouldn't have to do this if this type was exported from `@wordpress/compose`.\ntype WPKeyboardShortcutConfig = NonNullable<\n\tParameters< typeof useKeyboardShortcut >[ 2 ]\n>;\n\nexport type KeyboardShortcutProps = {\n\tshortcut: string | string[];\n\t/**\n\t * @see {@link https://craig.is/killing/mice Mousetrap documentation}\n\t */\n\tcallback: ( event: Mousetrap.ExtendedKeyboardEvent, combo: string ) => void;\n} & Pick< WPKeyboardShortcutConfig, 'bindGlobal' | 'eventName' | 'target' >;\n\nexport type KeyboardShortcutsProps = {\n\t/**\n\t * Elements to render, upon whom key events are to be monitored.\n\t */\n\tchildren?: React.ReactNode;\n\t/**\n\t * An object of shortcut bindings, where each key is a keyboard combination,\n\t * the value of which is the callback to be invoked when the key combination is pressed.\n\t *\n\t * The value of each shortcut should be a consistent function reference, not an anonymous function.\n\t * Otherwise, the callback will not be correctly unbound when the component unmounts.\n\t *\n\t * The `KeyboardShortcuts` component will not update to reflect a changed `shortcuts` prop.\n\t * If you need to change shortcuts, mount a separate `KeyboardShortcuts` element,\n\t * which can be achieved by assigning a unique `key` prop.\n\t *\n\t * @see {@link https://craig.is/killing/mice Mousetrap documentation}\n\t */\n\tshortcuts: Record< string, KeyboardShortcutProps[ 'callback' ] >;\n\t/**\n\t * By default, a callback will not be invoked if the key combination occurs in an editable field.\n\t * Pass `bindGlobal` as `true` if the key events should be observed globally, including within editable fields.\n\t *\n\t * Tip: If you need some but not all keyboard events to be observed globally,\n\t * simply render two distinct `KeyboardShortcuts` elements, one with and one without the `bindGlobal` prop.\n\t */\n\tbindGlobal?: KeyboardShortcutProps[ 'bindGlobal' ];\n\t/**\n\t * By default, a callback is invoked in response to the `keydown` event.\n\t * To override this, pass `eventName` with the name of a specific keyboard event.\n\t */\n\teventName?: KeyboardShortcutProps[ 'eventName' ];\n};\n"], "mappings": ""}