{"version": 3, "names": ["_classnames", "_interopRequireDefault", "require", "_i18n", "_element", "_icons", "_visually<PERSON><PERSON>den", "_externalLinkStyles", "UnforwardedExternalLink", "props", "ref", "href", "children", "className", "rel", "additionalProps", "optimizedRel", "Set", "split", "filter", "Boolean", "join", "classes", "classnames", "isInternalAnchor", "startsWith", "onClickHandler", "event", "preventDefault", "onClick", "_react", "createElement", "target", "VisuallyHidden", "as", "__", "StyledIcon", "icon", "external", "ExternalLink", "forwardRef", "exports", "_default", "default"], "sources": ["@wordpress/components/src/external-link/index.tsx"], "sourcesContent": ["/**\n * External dependencies\n */\nimport classnames from 'classnames';\nimport type { ForwardedRef } from 'react';\n\n/**\n * WordPress dependencies\n */\nimport { __ } from '@wordpress/i18n';\nimport { forwardRef } from '@wordpress/element';\nimport { external } from '@wordpress/icons';\n\n/**\n * Internal dependencies\n */\nimport { VisuallyHidden } from '../visually-hidden';\nimport { StyledIcon } from './styles/external-link-styles';\nimport type { ExternalLinkProps } from './types';\nimport type { WordPressComponentProps } from '../context';\n\nfunction UnforwardedExternalLink(\n\tprops: Omit<\n\t\tWordPressComponentProps< ExternalLinkProps, 'a', false >,\n\t\t'target'\n\t>,\n\tref: ForwardedRef< HTMLAnchorElement >\n) {\n\tconst { href, children, className, rel = '', ...additionalProps } = props;\n\tconst optimizedRel = [\n\t\t...new Set(\n\t\t\t[\n\t\t\t\t...rel.split( ' ' ),\n\t\t\t\t'external',\n\t\t\t\t'noreferrer',\n\t\t\t\t'noopener',\n\t\t\t].filter( Boolean )\n\t\t),\n\t].join( ' ' );\n\tconst classes = classnames( 'components-external-link', className );\n\t/* Anchor links are perceived as external links.\n\tThis constant helps check for on page anchor links,\n\tto prevent them from being opened in the editor. */\n\tconst isInternalAnchor = !! href?.startsWith( '#' );\n\n\tconst onClickHandler = (\n\t\tevent: React.MouseEvent< HTMLAnchorElement, MouseEvent >\n\t) => {\n\t\tif ( isInternalAnchor ) {\n\t\t\tevent.preventDefault();\n\t\t}\n\n\t\tif ( props.onClick ) {\n\t\t\tprops.onClick( event );\n\t\t}\n\t};\n\n\treturn (\n\t\t/* eslint-disable react/jsx-no-target-blank */\n\t\t<a\n\t\t\t{ ...additionalProps }\n\t\t\tclassName={ classes }\n\t\t\thref={ href }\n\t\t\tonClick={ onClickHandler }\n\t\t\ttarget=\"_blank\"\n\t\t\trel={ optimizedRel }\n\t\t\tref={ ref }\n\t\t>\n\t\t\t{ children }\n\t\t\t<VisuallyHidden as=\"span\">\n\t\t\t\t{\n\t\t\t\t\t/* translators: accessibility text */\n\t\t\t\t\t__( '(opens in a new tab)' )\n\t\t\t\t}\n\t\t\t</VisuallyHidden>\n\t\t\t<StyledIcon\n\t\t\t\ticon={ external }\n\t\t\t\tclassName=\"components-external-link__icon\"\n\t\t\t/>\n\t\t</a>\n\t\t/* eslint-enable react/jsx-no-target-blank */\n\t);\n}\n\n/**\n * Link to an external resource.\n *\n * ```jsx\n * import { ExternalLink } from '@wordpress/components';\n *\n * const MyExternalLink = () => (\n *   <ExternalLink href=\"https://wordpress.org\">WordPress.org</ExternalLink>\n * );\n * ```\n */\nexport const ExternalLink = forwardRef( UnforwardedExternalLink );\n\nexport default ExternalLink;\n"], "mappings": ";;;;;;;;AAGA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAKA,IAAAI,eAAA,GAAAJ,OAAA;AACA,IAAAK,mBAAA,GAAAL,OAAA;AAjBA;AACA;AACA;;AAIA;AACA;AACA;;AAKA;AACA;AACA;;AAMA,SAASM,uBAAuBA,CAC/BC,KAGC,EACDC,GAAsC,EACrC;EACD,MAAM;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,GAAG,GAAG,EAAE;IAAE,GAAGC;EAAgB,CAAC,GAAGN,KAAK;EACzE,MAAMO,YAAY,GAAG,CACpB,GAAG,IAAIC,GAAG,CACT,CACC,GAAGH,GAAG,CAACI,KAAK,CAAE,GAAI,CAAC,EACnB,UAAU,EACV,YAAY,EACZ,UAAU,CACV,CAACC,MAAM,CAAEC,OAAQ,CACnB,CAAC,CACD,CAACC,IAAI,CAAE,GAAI,CAAC;EACb,MAAMC,OAAO,GAAG,IAAAC,mBAAU,EAAE,0BAA0B,EAAEV,SAAU,CAAC;EACnE;AACD;AACA;EACC,MAAMW,gBAAgB,GAAG,CAAC,CAAEb,IAAI,EAAEc,UAAU,CAAE,GAAI,CAAC;EAEnD,MAAMC,cAAc,GACnBC,KAAwD,IACpD;IACJ,IAAKH,gBAAgB,EAAG;MACvBG,KAAK,CAACC,cAAc,CAAC,CAAC;IACvB;IAEA,IAAKnB,KAAK,CAACoB,OAAO,EAAG;MACpBpB,KAAK,CAACoB,OAAO,CAAEF,KAAM,CAAC;IACvB;EACD,CAAC;EAED,OACC;IACA,IAAAG,MAAA,CAAAC,aAAA;MAAA,GACMhB,eAAe;MACpBF,SAAS,EAAGS,OAAS;MACrBX,IAAI,EAAGA,IAAM;MACbkB,OAAO,EAAGH,cAAgB;MAC1BM,MAAM,EAAC,QAAQ;MACflB,GAAG,EAAGE,YAAc;MACpBN,GAAG,EAAGA;IAAK,GAETE,QAAQ,EACV,IAAAkB,MAAA,CAAAC,aAAA,EAACzB,eAAA,CAAA2B,cAAc;MAACC,EAAE,EAAC;IAAM,GAEvB;IACA,IAAAC,QAAE,EAAE,sBAAuB,CAEb,CAAC,EACjB,IAAAL,MAAA,CAAAC,aAAA,EAACxB,mBAAA,CAAA6B,UAAU;MACVC,IAAI,EAAGC,eAAU;MACjBzB,SAAS,EAAC;IAAgC,CAC1C,CACC;IACH;EAAA;AAEF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM0B,YAAY,GAAG,IAAAC,mBAAU,EAAEhC,uBAAwB,CAAC;AAACiC,OAAA,CAAAF,YAAA,GAAAA,YAAA;AAAA,IAAAG,QAAA,GAEnDH,YAAY;AAAAE,OAAA,CAAAE,OAAA,GAAAD,QAAA"}